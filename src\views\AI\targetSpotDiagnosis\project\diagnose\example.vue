<script setup>
defineOptions({ name: 'example' })
const activeStepIndex = ref(0)
const stepList = ref([
  {
    name: '阅读评估标准，做出有无判断',
    comp: shallowRef(defineAsyncComponent(() => import('./components/standard.vue')))
  },
  {
    name: '能力DNA详情诊断，做出有无判断',
    comp: shallowRef(defineAsyncComponent(() => import('./components/dna.vue')))
  },
  {
    name: '对要改善的能力进行排序</br>（能力组件）',
    comp: shallowRef(defineAsyncComponent(() => import('./components/sortComponent.vue')))
  },
  {
    name: '对要改善的能力进行排序</br> （能力模块）',
    comp: shallowRef(defineAsyncComponent(() => import('./components/sortModel.vue')))
  }
])
const router = useRouter()
const jump = () => {
  router.push({ path: '/AI/targetSpotDiagnosis/project/diagnose/model' })
}
const next = () => {
  if (activeStepIndex.value < stepList.value.length - 1) {
    activeStepIndex.value++
    return
  }
}
</script>
<template>
  <div class="example-content">
    <div class="example-title">测评步骤示例</div>
    <div class="step-wrap">
      <div class="step-main">
        <div
          class="step-item"
          :class="{ active: activeStepIndex >= index }"
          v-for="(item, index) in stepList"
          :key="index"
          @click="activeStepIndex = index"
        >
          <div class="step-index">{{ index + 1 }}</div>
          <div class="step-line"></div>
          <div class="step-name" v-html="item.name"></div>
        </div>
      </div>
    </div>
    <component :is="stepList[activeStepIndex].comp" />
    <div class="page-btn-wrap">
      <div class="btn border" v-show="activeStepIndex < 3" @click="jump">跳过，直接开始评估</div>
      <div class="btn" v-show="activeStepIndex < 3" @click="next">已了解，查看下一个提示</div>
      <div class="btn" v-show="activeStepIndex == 3" @click="jump">
        恭喜您，已完成所有能力评估的练习，点击开始正式评估
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.example-content {
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
  padding: 20px;
  .example-title {
    font-weight: 600;
    font-size: 16px;
    color: #53a9f9;
    line-height: 16px;
    margin-bottom: 20px;
  }
}
.step-wrap {
  position: relative;
  padding: 20px 100px;
  overflow: hidden;
  .step-main {
    display: flex;
    align-items: center;
    .step-item {
      position: relative;
      flex: 1;
      color: #888888;
      transform: translateX(50%);
      cursor: pointer;
      &.active {
        color: #40a0ff;
        .step-index {
          background: linear-gradient(180deg, #40a0ff 0%, #9fcfff 100%);
          box-shadow:
            0px 15px 24px 0px rgba(165, 186, 217, 0.3),
            inset 0px 1px 0px 0px #9fcfff;
          color: #ffffff;
        }
        .step-line {
          background: linear-gradient(63deg, #40a0ff 0%, rgba(64, 160, 255, 0.1) 97%);
        }
      }
      &:last-of-type {
        .step-line {
          display: none;
        }
      }
      .step-index {
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        font-family: Anton, Anton;
        font-weight: 600;
        font-size: 20px;
        color: #acacac;
        border-radius: 50%;
        margin-bottom: 26px;
        background: linear-gradient(180deg, #ebf0f4 0%, #ffffff 100%);
        box-shadow:
          0px 15px 24px 0px rgba(165, 186, 217, 0.3),
          inset 0px 1px 0px 0px #ffffff;
      }
      .step-line {
        position: absolute;
        top: 16px;
        left: 50px;
        width: calc(100% - 60px);
        height: 8px;
        background: #d8d8d8;
        border-radius: 37px 37px 37px 37px;
      }
      .step-name {
        display: inline-block;
        font-size: 16px;
        line-height: 19px;
        transform: translateX(-50%);
        text-align: center;
      }
    }
  }
}
</style>
