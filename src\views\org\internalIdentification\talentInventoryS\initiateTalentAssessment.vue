<script setup>
import Steps from "../../components/steps.vue";
const emits = defineEmits(["closeInventory"]);
const projectBMlist = ref([]);
const stepsData = ref([
  {
    name: "创建盘点项目",
    finshSign: false,
    code: 1,
  },
  {
    name: "选择盘点范围",
    finshSign: false,
    code: 2,
  },
  {
    name: "盘点范围校验",
    finshSign: false,
    code: 3,
  },
  {
    name: "评级模型确认",
    finshSign: false,
    code: 4,
  },
  {
    name: "评价关系设置",
    finshSign: false,
    code: 5,
  },
  {
    name: "评价关系确认",
    finshSign: false,
    code: 6,
  },
  {
    name: "启动盘点",
    finshSign: false,
    code: 7,
  },
]);
const stepsNum = ref(1);

// steps1
const nrdb = ref([
  {
    title: "",
    info: "个人：基本信息（岗位名称、本岗位工作时长）、工作协同内容（人力复用率）",
  },
  {
    title: "",
    info: "部门负责人&上级：人员招募需求（空缺人数、当前需求人数、紧急程度等），数据上传中的岗位编制与现有人数对比，人员招募需求",
  },
]);
const czqx = ref([
  {
    title: "",
    info: "个人：维护基本信息（岗位名称、本岗位工作时长）",
  },
  {
    title: "",
    info: "部门负责人&上级：确认招募需求紧急程度、评估人员可替代性、确认编制与空缺人数",
  },
]);

const dbzt = ref([
  {
    name: "人才数量盘点",
    code: 1,
    check: false,
  },
  {
    name: "人才结构盘点",
    code: 2,
    check: false,
  },
  {
    name: "人才质量盘点",
    code: 3,
    check: false,
  },
  {
    name: "人才效能盘点",
    code: 4,
    check: false,
  },

  {
    name: "人才发展盘点",
    code: 5,
    check: false,
  },
  {
    name: "人才风险盘点",
    code: 6,
    check: false,
  },
]);
const menuCheckSign = ref(1);
const step1CheckNum = ref(0);
const ruleFormRef = ref();
const ruleForm = ref({
  name: "",
  date: [],
});

const rules = ref({
  name: [
    { required: true, message: "请填写", trigger: "blur" },
    // { min: 0, max: 25, message: "Length should be 3 to 5", trigger: "blur" },
  ],
  date: [
    {
      required: true,
      message: "请选择",
      trigger: "change",
    },
  ],
});
const step1CheckMenu = (i) => {
  dbzt.value[i].check = !dbzt.value[i].check;
  if (dbzt.value[i].check) {
    step1CheckNum.value++;
  } else {
    step1CheckNum.value--;
  }
};

const operateBtn = (c) => {
  let maxArea = stepsData.value.length;
  if (c == "N" && stepsNum.value < maxArea) {
    stepsNum.value++;
  } else if (c == "P" && stepsNum.value > 1) {
    stepsNum.value--;
  }
};

const goBack = () => {
  emits("closeInventory", false);
};
</script>
<template>
  <div class="projectBM_add_wrap">
    <div class="title_main justify-between">
      <div class="page-title-line">发起盘点</div>
      <div class="t_btn" @click="goBack"><span class="icon"></span>返回</div>
    </div>

    <div class="projectBM_main_wrap section_box_wrap">
      <div class="s_wrap">
        <Steps
          :stepsData="stepsData"
          :stepsNum="stepsNum"
          :verticalSign="true"
        ></Steps>
      </div>
      <div class="m_steps_wrap steps_01_wrap" v-if="stepsNum == 1">
        <div class="form_wrap">
          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="rules"
            size="small"
            class="demo-ruleForm"
            status-icon
            label-width="120px"
            style="max-width: 460px"
          >
            <el-form-item label="盘点起止日期" prop="date">
              <el-date-picker
                v-model="ruleForm.date"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                range-separator="至"
                format="YYYY-MM-DD"
                date-format="YYYY/MM/DD ddd"
              />
            </el-form-item>

            <el-form-item label="盘点项目名称" prop="name">
              <el-input v-model="ruleForm.name" />
            </el-form-item>
          </el-form>
        </div>
        <div class="title_wrap justify-between">
          <div class="title"><span class="icon"></span>选择盘点维度</div>
          <div class="check_info">已选：{{ step1CheckNum }}维度</div>
        </div>
        <div class="steps_01_main justify-between">
          <div class="menu_wrap section_box_wrap">
            <div
              class="item_wrap"
              :class="{ act: it.check }"
              v-for="(it, index) in dbzt"
              @click="step1CheckMenu(index)"
            >
              <span class="text">{{ it.name }}</span>
              <span class="icon"></span>
            </div>
          </div>
          <div class="r_main_wrap">
            <div class="section_box_wrap">
              <div class="t">简介</div>
              评估组织当前人员数量与业务需求的匹配度，包括编制缺口、冗余分析、紧急需求等。
            </div>
            <div class="section_box_wrap dot_content_wrap">
              <div class="t">盘点要素</div>
              <div class="item_wrap" v-for="item in nrdb">
                <span class="icon"></span>
                <span class="title">{{ item.title }}</span>
                <span class="info">{{ item.info }}</span>
              </div>
            </div>
            <div class="section_box_wrap dot_content_wrap">
              <div class="t">操作权限</div>
              <div class="item_wrap" v-for="item in czqx">
                <span class="icon"></span>
                <span class="title">{{ item.title }}</span>
                <span class="info">{{ item.info }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="justify-end">
          <div class="btn">加入盘点维度</div>
        </div>
        <div class="btn_wrap justify-between">
          <div class="bnt next_btn" @click="operateBtn('N')">下一步</div>
        </div>
      </div>
      <div class="m_steps_wrap steps_03_wrap" v-if="stepsNum == 2">
        <div class="btn_wrap justify-between">
          <div class="bnt pre_btn" @click="operateBtn('P')">上一步</div>
          <div class="bnt next_btn" @click="operateBtn('N')">下一步</div>
        </div>
      </div>
      <div class="m_steps_wrap steps_03_wrap" v-if="stepsNum == 3">
        <div class="btn_wrap justify-between">
          <div class="bnt pre_btn" @click="operateBtn('P')">上一步</div>
          <div class="bnt next_btn" @click="operateBtn('N')">下一步</div>
        </div>
      </div>
      <div class="m_steps_wrap steps_04_wrap" v-if="stepsNum == 4"></div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-end {
  display: flex;
  justify-content: flex-end;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.projectBM_add_wrap {
  .top_t_wrap {
    margin-bottom: 10px;
    align-items: center;
    .page-title-line {
      margin: 3px 0 0 0;
    }
  }
  .title_main {
    .t_l {
    }
    .t_btn {
      width: 74px;
      height: 28px;
      line-height: 28px;
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #40a0ff;
      text-align: center;
      color: #40a0ff;
      cursor: pointer;
      .icon {
        display: inline-block;
        margin: 0px 6px -1px 0;
        width: 16px;
        height: 16px;
        background: url("@/assets/imgs/org/icon_06.png") no-repeat center;
        background-size: 100% 100%;
      }
    }
  }

  .projectBM_main_wrap {
    background: #fff;
    .s_wrap {
      width: 100%;
      margin: 50px auto 40px;
    }

    .m_steps_wrap {
      .title {
        margin-bottom: 16px;
        .icon {
          display: inline-block;
          margin-right: 10px;
          width: 16px;
          height: 12px;
          background: url("@/assets/imgs/org/icon_03.png") no-repeat center;
          background-size: 100% 100%;
        }
        .tips {
          padding: 4px 10px;
          font-size: 14px;
          color: #6ab5ff;
          background: #e2f0ff;
          border-radius: 4px 4px 4px 4px;
        }
      }
    }
    .steps_01_wrap {
      .title_wrap {
        color: #40a0ff;
      }
      .steps_01_main {
        .menu_wrap {
          padding: 20px 10px;
          margin-right: 20px;
          width: 320px;
          background: #fff;
          .item_wrap {
            padding: 0 10px;
            margin-bottom: 10px;
            height: 36px;
            line-height: 36px;
            background: #f3f3f3;
            border-radius: 5px 5px 5px 5px;
            font-size: 14px;
            cursor: pointer;
            span {
              display: inline-block;
            }
            .text {
              width: 90%;
              white-space: nowrap;
            }
            .icon {
              width: 14px;
              height: 14px;
              background: url("@/assets/imgs/org/icon_05.png") no-repeat center;
              background-size: 100% 100%;
            }
            &.act {
              background: #f0f9ff;
              color: #40a0ff;
              .icon {
                background: url("@/assets/imgs/org/icon_04.png") no-repeat
                  center;
                background-size: 100% 100%;
              }
            }
          }
        }
        .r_main_wrap {
          .section_box_wrap {
            background: #fff;
            &.dot_content_wrap {
              .info {
                display: inline-block;
                margin-left: -20px;
              }
            }
          }
        }
      }
    }
    .steps_02_wrap {
    }
    .steps_03_wrap {
    }
    .steps_04_wrap {
      margin: 0 auto;
    }

    .btn_wrap {
      margin: 40px auto 0;
      width: 260px;
      .bnt {
        width: 120px;
        height: 36px;
        border-radius: 6px 6px 6px 6px;
        line-height: 36px;
        color: #40a0ff;
        text-align: center;
        border: 1px solid #40a0ff;
        cursor: pointer;
      }
      .pre_btn {
      }
      .next_btn {
        background: #40a0ff;
        color: #fff;
      }
    }
  }
}
</style>
