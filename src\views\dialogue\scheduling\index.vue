<script setup>
import { UploadFilled } from '@element-plus/icons-vue'
import { CaretRight } from '@element-plus/icons-vue'
import { uploadFile, exportExample } from '@/api/modules/dialogue.js'
import { useDialogueStore } from '@/stores'

let fileData = ref(null)
let disabled = ref(true)
let user = '123'
const router = useRouter()

watch(fileData, (newVal, oldVal) => {
  console.log('newVal', newVal, 'oldVal', oldVal)
  disabled.value = !newVal
})

const beforeUpload = rawFile => {
  console.log('rawFile222', rawFile)
  return true
}

const handleRemove = () => {
  fileData.value = null
}

const customerUpload = option => {
  const formData = new FormData()
  formData.append('file', option.file)
  formData.append('user', user)
  uploadFile(formData).then(res => {
    if (res.code == 200) {
      option.onSuccess(res.data)
    } else {
      option.onError(res.data)
    }
  })
}

const handleSuccess = (response, file, fileList) => {
  console.log('handleSuccess', response, file, fileList)
  fileData.value = response.id
}

const handleError = (err, file, fileList) => {
  console.log('handleError', err, file, fileList)
  fileData.value = null
}

const goDetail = () => {
  useDialogueStore().setFirst(fileData.value)
  router.push(`/scheduling/${fileData.value}`)
}

const download = () => {
  exportExample().then(res => {
    downloadFile('模板.xlsx', res)
  })
}

function downloadFile(name, res) {
  const blob = new Blob([res])
  const elink = document.createElement('a')
  elink.href = window.URL.createObjectURL(blob)
  elink.download = name
  elink.style.display = 'none'
  document.body.appendChild(elink)
  elink.click()
  document.body.removeChild(elink)
  window.URL.revokeObjectURL(elink.href) // 释放URL 对象
}

const goPath = path => {
  router.push(path)
}
</script>

<template>
  <div class="app-scheduling">
    <!-- <div class="add-card" @click="goPath('/scheduling')">
      <el-icon><CirclePlus /></el-icon>
      <span class="text">新建排产</span>
    </div> -->
    <el-upload
      class="upload-demo"
      drag
      action="#"
      :http-request="customerUpload"
      :limit="1"
      :on-change="handleChange"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      accept=".xlsx, .xls"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">将文件拖放到此处或 <em>单击上传</em></div>
      <template #tip>
        <div class="el-upload__tip" @click="download">下载模板</div>
      </template>
    </el-upload>
    <div class="button-card">
      <el-button type="primary" :disabled="disabled" @click="goDetail">
        <el-icon class="el-icon--right"><CaretRight /></el-icon>
        运行
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
// @import "@/assets/styles/mixin.scss";
// @import "@/assets/styles/variables.module.scss";
.app-scheduling {
  @include flex-center(column, center, flex-start);
  padding-top: 150px;
  width: 800px;
  max-width: 90%;
  margin: 0 auto;
  .add-card {
    display: flex;
    align-items: center;
    gap: 4px;
    height: 34px;
    width: 104px;
    margin-bottom: 20px;
    padding: 0 10px;
    border: 1px solid #e7e4e4;
    border-radius: 12px;
    cursor: pointer;
    .el-icon {
      vertical-align: middle;
      color: #333;
    }
    .text {
      color: #333;
      font-size: 14px;
    }
    &:hover {
      .el-icon {
        color: #53a9f9;
      }
      .text {
        color: #53a9f9;
      }
    }
  }
  .upload-demo {
    width: 100%;
  }
  .button-card {
    width: 100%;
    padding-top: 20px;
    display: flex;
    justify-content: flex-end;
    .el-icon--right {
      margin-right: 4px;
      margin-left: 0;
      font-size: 20px;
    }
  }
  .el-upload__tip {
    font-size: 14px;
    cursor: pointer;
    color: #53acff;
  }
}
</style>
