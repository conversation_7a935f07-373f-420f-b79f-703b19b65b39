import Vue from 'vue'
import Vuex from 'vuex'
import { getUserBasicInfo, getCompanyInfo } from '../request/api'

//挂载Vuex
Vue.use(Vuex)

//创建VueX对象

const state = {
  pageTitle: '人才数字化管理平台',
  createStaffId: null,
  userInfo: {},
  companyInfo: {},
  // 部分页面筛选条件缓存
  // 员工管理
  staffMParams: {},
  // 岗位管理
  postMParams: {},
  // 职位管理
  jobMParams: {}
}
const mutations = {
  setPageTitle(state, str) {
    state.pageTitle = str
  },
  setStaffId(state, id) {
    state.createStaffId = id
  },
  setUserInfo(state, userInfo) {
    state.userInfo = userInfo
  },
  setCompanyInfo(state, companyInfo) {
    state.companyInfo = companyInfo
  },
  setParams(state, data) {
    let { stateKey, params } = { ...data }
    state[stateKey] = { ...state[stateKey], ...params }
  }
}

const actions = {
  async requestUserInfo(context) {
    //获取用户信息
    let userInfo = await getUserBasicInfo({})
    if (userInfo.code == 200) {
      context.commit('setUserInfo', userInfo.data)
      //获取企业信息
      let companyInfo = await getCompanyInfo({
        companyId: state.userInfo.companyId
      })
      context.commit('setCompanyInfo', companyInfo)
    }
  }
}

const store = new Vuex.Store({
  state,
  mutations,
  actions
})

export default store
