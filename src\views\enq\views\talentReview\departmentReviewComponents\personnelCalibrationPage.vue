<template>
	<div class="personnel_calibration_main">
        <div class="page_second_title">人员校准</div>
		<div class="department_main marginT_30">
			<div class="page_second_title">核心能力校准</div>
			<div class="department_content marginT_20">
				<div class="item" v-for="(item,index) in capabilityRank" :key="index">
					<div class="title">
						<div>{{item.name}}</div>
						<div v-show="item.data.length">
							<span>{{item.data.length}}</span>人
						</div>
					</div>
					<div class="content">
						<draggable class="drag_wrap" :id="index" @end="dragRemove($event,'capabilityRank')" v-model="item.data" :options="{group:'ability'}">
							<div class="personnel_item" @click="showPersonnelInfo(list)" :id="index" :index="listIndex" v-for="(list,listIndex) in item.data"
							 :key="list.userId+list.rcreateTime">{{list.userName}}</div>
						</draggable>
					</div>
				</div>
			</div>
			<div class="page_second_title">绩效表现校准</div>
			<div class="department_content marginT_20">
				<div class="item" v-for="(item,index) in kpiRank" :key="index">
					<div class="title">
						<div>{{item.name}}</div>
						<div v-show="item.data.length">
							<span>{{item.data.length}}</span>人
						</div>
					</div>
					<div class="content">
						<draggable class="drag_wrap" :id="index" @end="dragRemove($event,'kpiRank')" v-model="item.data" :options="{group:'performance'}">
							<div class="personnel_item" @click="showPersonnelInfo(list)" :id="index" :index="listIndex" v-for="(list,listIndex) in item.data"
							 :key="list.userId+list.rcreateTime">{{list.userName}}</div>
						</draggable>
					</div>
				</div>
			</div>
			<div class="page_second_title">发展潜力校准</div>
			<div class="department_content marginT_20">
				<div class="item" v-for="(item,index) in developmentPotential" :key="index">
					<div class="title">
						<div>{{item.name}}</div>
						<div v-show="item.data.length">
							<span>{{item.data.length}}</span>人
						</div>
					</div>
					<div class="content">
						<draggable class="drag_wrap" :id="index" @end="dragRemove($event,'developmentPotential')" v-model="item.data"
						 :options="{group:'develop'}">
							<div class="personnel_item" @click="showPersonnelInfo(list)" :id="index" :index="listIndex" v-for="(list,listIndex) in item.data"
							 :key="list.userId+list.rcreateTime">{{list.userName}}</div>
						</draggable>
					</div>
				</div>
			</div>
		</div>
		<div class="oper_btn_wrap align_center">
			<el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
			<el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{nextBtnText}}</el-button>
		</div>
		<!-- 人员信息弹窗 -->
		<div class="personnel_dialog_wrap" v-show="showPersonnelDialog">
			<div class="close_btn">
				<i class="el-icon-close" @click="showPersonnelDialog = false"></i>
			</div>
			<div class="personnel_dialog_title">
				<span class="name">{{personnelInfo.userName}}</span>
				<span class="dept">{{personnelInfo.orgName}} / </span>
				<span class="post_name">{{personnelInfo.postName}}</span>
			</div>
			<div class="personnel_dialog_content">
				<div class="content_item">
					<div class="item_label">绩效表现</div>
					<div class="item_content flex_row_between">
						<div class="list" :class="{'active': personnelInfo.kpiRank == item.dictCode}" v-for="item in optionsCfg.kpiRank"
						 :key="item.dictCode">{{item.codeName}}</div>
					</div>
				</div>
				<div class="content_item">
					<div class="item_label">核心能力</div>
					<div class="item_content flex_row_between">
						<div class="list" :class="{'active': personnelInfo.competenceRank == item.dictCode}" v-for="item in optionsCfg.competenceRank"
						 :key="item.dictCode">{{item.codeName}}</div>
					</div>
				</div>
				<div class="content_item">
					<div class="item_label">发展潜力</div>
					<div class="item_content flex_row_between">
						<div class="list" :class="{'active': personnelInfo.developmentPotential == item.dictCode}" v-for="item in optionsCfg.developmentPotential"
						 :key="item.dictCode">{{item.codeName}}</div>
					</div>
				</div>
				<div class="content_item">
					<div class="item_label">离职风险</div>
					<div class="item_content flex_row_between">
						<div class="list" :class="{'active': personnelInfo.retentionRisk == item.dictCode}" v-for="item in optionsCfg.retentionRisk"
						 :key="item.dictCode">{{item.codeName}}</div>
					</div>
				</div>
				<div class="content_item">
					<div class="item_label">离职影响</div>
					<div class="item_content flex_row_between">
						<div class="list" :class="{'active': personnelInfo.dimissionImpact == item.dictCode}" v-for="item in optionsCfg.dimissionImpact"
						 :key="item.dictCode">{{item.codeName}}</div>
					</div>
				</div>
				<div class="content_item">
					<div class="item_label">内部可替代</div>
					<div class="item_content flex_row_between">
						<div class="list" :class="{'active': personnelInfo.innerSubstitution == item.dictCode}" v-for="item in optionsCfg.innerSubstitution"
						 :key="item.dictCode">{{item.codeName}}</div>
					</div>
				</div>
				<div class="content_item">
					<div class="item_label">外部可替代</div>
					<div class="item_content flex_row_between">
						<div class="list" :class="{'active': personnelInfo.externalSubstitution == item.dictCode}" v-for="item in optionsCfg.externalSubstitution"
						 :key="item.dictCode">{{item.codeName}}</div>
					</div>
				</div>
				<div class="content_item">
					<div class="item_label">离职可能性</div>
					<div class="item_content flex_row_between">
						<div class="list" :class="{'active': personnelInfo.dimissionPossibility == item.dictCode}" v-for="item in optionsCfg.dimissionPossibility"
						 :key="item.dictCode">{{item.codeName}}</div>
					</div>
				</div>
				<div class="content_item">
					<div class="item_label">能否继任</div>
					<div class="item_content flex_row_between">
						<div class="list">
							<el-select class="list_select" disabled v-model="personnelInfo.successionPossibility" size="mini" placeholder>
								<el-option v-for="item in optionsCfg.successionPossibility" :label="item.codeName" :value="item.dictCode" :key="item.dictCode"></el-option>
							</el-select>
						</div>
					</div>
				</div>
				<!-- <div class="content_item">
					<div class="item_label">理想岗位</div>
					<div class="item_content flex_row_between">
						<div class="list">
							<el-input disabled v-model="personnelInfo.expectationPostName" size="mini"></el-input>
						</div>
					</div>
				</div> -->
			</div>
		</div>
	</div>
</template>

<script>
	import {
		queryPerCorrect,
		updateCapaKpiDevelopment,
		getUserEval
	} from "../../../request/api"
	import draggable from 'vuedraggable'
	export default {
		name: "personnelCalibration",
		props: ["nextBtnText","enqId"],
		components: {
			draggable
		},
		data() {
			return {
				capabilityRank: {},
				kpiRank: {},
				developmentPotential: {},
				personnelInfo: {},
				showPersonnelDialog: false,
				optionsCfg: {},
			};
		},
		computed: {
		},
		created() {
			this.queryPerCorrectFun();
			this.getDocListFun();

		},
		methods: {
			dragRemove(data, dataType) {
				let oldKey = data.from.id;
				let dataIndex = data.to.id;
				let index = data.newIndex;
				let listData = this[dataType][dataIndex].data[index];
				let paramsResult = this[dataType][dataIndex]['key'];
				let params = {
					enqId: this.enqId,
					postCode: listData.postCode,
					userId: listData.userId
				};
				params[dataType] = paramsResult;
				this.updateCapaKpiDevelopmentFun(params);
			},
			updateCapaKpiDevelopmentFun(params) {
				updateCapaKpiDevelopment(params).then(res => {
					if (res.code == "200") {
						this.$msg.success(res.msg);
						// this.queryPerCorrectFun();
					} else {
						this.$msg.error(res.msg);
					}
				})
			},
			queryPerCorrectFun() {
				let params = {
					enqId: this.enqId,
					// orgCode: this.orgCode
				};
				queryPerCorrect(params).then(res => {
					console.log(res);
					this.capabilityRank = res.capabilityRank;
					this.developmentPotential = res.developmentPotential;
					this.kpiRank = res.kpiRank;
				})
			},
			showPersonnelInfo(data) {
				let params = {
					enqId: this.enqId,
					userId: data.userId,
					postCode: data.postCode
				};
				getUserEval(params).then(res => {
					if (res.code == "200") {
						this.$msg.success(res.msg);
						let data = res.data;
						this.showPersonnelDialog = true;
						this.personnelInfo = data;
					} else {
						this.$msg.error(res.msg);
						this.showPersonnelDialog = false;
					}
				})
			},
			prevBtn() {
				this.$emit("prevStep");
			},
			nextBtn() {
				this.$emit("nextStep");
			},
			getDocListFun() {
				let docList = [
					"KPI_RANK",
					"COMPETENCE_RANK",
					"DEVELOPMENT_POTENTIAL",
					"TALENT_CLASS",
					"PROMOTION_POSSIBILITY",
					"RETENTION_RISK",
					"DIMISSION_IMPACT",
					"INNER_SUBSTITUTION",
					"EXTERNAL_SUBSTITUTION",
					"DEVELOPMENT_CYCLE",
					"DIMISSION_POSSIBILITY",
					"SUCCESSION_POSSIBILITY",
				];
				this.$getDocList(docList).then(res => {
					let optionsCfg = {
						kpiRank: res.KPI_RANK,
						competenceRank: res.COMPETENCE_RANK,
						developmentPotential: res.DEVELOPMENT_POTENTIAL,
						talentClass: res.TALENT_CLASS,
						promotionPossibility: res.PROMOTION_POSSIBILITY,
						retentionRisk: res.RETENTION_RISK,
						dimissionImpact: res.DIMISSION_IMPACT,
						innerSubstitution: res.INNER_SUBSTITUTION,
						externalSubstitution: res.EXTERNAL_SUBSTITUTION,
						developmentCycle: res.DEVELOPMENT_CYCLE,
						dimissionPossibility: res.DIMISSION_POSSIBILITY,
						successionPossibility: res.SUCCESSION_POSSIBILITY,
					};
					this.optionsCfg = optionsCfg;
				});
			},
		}
	};
</script>

<style scoped lang="scss">
	.department_content {
		display: flex;
		flex-flow: row nowrap;
		justify-content: flex-start;
		margin-bottom: 16px;

		.item {
            flex: 1;
			margin-right: 10px;
            height: 168px;

			.title {
				display: flex;
				flex-flow: row nowrap;
				justify-content: space-between;
				line-height: 35px;
				color: #fff;
				padding: 0 8px;
				background: #E5F0F9;

				span {
				}
			}

			.content {
                height: 130px;
				padding: 5px 12px;
				color: #fff;
				overflow-y: auto;
				.drag_wrap {
                    height: 100%;
                    overflow-y: auto;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: flex-start;
				}

				.personnel_item {
                    width: 60px;
                    height: 25px;
                    line-height: 23px;
                    color: #fff;
					display: inline-block;
					font-size: 12px;
					background: #0070C0;
                    text-align: center;
                    padding:2px 4px;
                    margin: 2px;
					cursor: pointer;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap
				}
			}
            &:nth-child(1){
                border: 1px solid #e28d80;
                .title{
                    background: #e28d80;
                }
                .content{
                    .personnel_item{
                        background: #e28d80;
                    }
                }
            }
            &:nth-child(2){
                border: 1px solid #719dd5;
                .title{
                    background: #719dd5;
                }
                .content{
                    .personnel_item{
                        background: #719dd5;
                    }
                }
            }
            &:nth-child(3){
                border: 1px solid #a3d0f3;
                .title{
                    background: #a3d0f3;
                }
                .content{
                    .personnel_item{
                        background: #a3d0f3;
                    }
                }
            }
            &:nth-child(4){
                border: 1px solid #BED269;
                .title{
                    background: #BED269;
                }
                .content{
                    .personnel_item{
                        background: #BED269;
                    }
                }
            }
            &:nth-child(5){
                border: 1px solid #ccc;
                .title{
                    background: #ccc;
                }
                .content{
                    .personnel_item{
                        background: #ccc;
                    }
                }
            }

		}
	}

	.personnel_dialog_wrap {
		position: fixed;
		top: 50%;
		left: 50%;
		width: 500px;
		// height: 400px;
		margin: -250px 0 0 -250px;
		background-color: #fff;
		border-radius: 8px;
		box-shadow: 0px 1px 5px #000;
		padding: 16px 32px 32px 16px;
		z-index: 999;

		.close_btn {
			cursor: pointer;
			position: absolute;
			right: 8px;
			top: 8px;
			color: #000000;
		}

		.personnel_dialog_title {
			font-size: 16px;
			padding-bottom: 16px;
			border-bottom: 1px solid #4573C4;
			margin-bottom: 16px;

			.name {
				margin-right: 30px;
                font-weight: bold;
			}
		}

		.personnel_dialog_content {
			padding-bottom: 16px;

			.content_item {
				line-height: 30px;
				margin-bottom: 8px;

				.item_label {
					float: left;
					width: 80px;
					line-height: 26px;
				}

				.item_content {
					overflow: hidden;
                    border: 1px solid #e5e5e5;
					.list {
						font-size: 12px;
						flex: 1;
						/*margin: 0 2px;*/
						text-align: center;
						line-height: 24px;
						/*background: #E8F0F7;*/
                        border-right: 1px solid #e5e5e5;
                        &:last-child{
                            border-right: none;
                        }

						&.active {
							background-color: #449CFF;
							color: #fff;
						}

						.list_select {
							width: 100%;
						}

						.el-input.is-disabled .el-input__inner {
							width: 100%;
							background-color: #fff;
                            border: none;
                            color: #212121;
						}
					}

				}
			}
		}
	}
</style>
