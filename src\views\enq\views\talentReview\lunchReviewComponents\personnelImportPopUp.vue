<template>
  <el-dialog title="开始导入" v-model="dialogVisible" @close="emit('update:show', false)" :width="'50%'" center>
    <div class="import_post_wrap">
      <div class="import_post_title">操作步骤:</div>
      <div class="oper_step">
        <p>1、下载《{{ tabsPaneInfo.length > 0 ? tabsPaneInfo[0].label : '' }}导入模板》</p>
        <p>2、打开下载表，将对应字段信息输入或粘贴进本表，为了保障粘贴信息被有效导入，请使用纯文本或者数字。</p>
        <p>3、信息输入完毕，点击"选择文件"按钮，选择excel文档。</p>
        <p>4、点击"开始导入",导入中如有任何疑问，请致电000000000。</p>
      </div>
      <div class="fs16 main_color download_file">
        <span @click="Export">
          立即下载《{{ tabsPaneInfo.length > 0 ? tabsPaneInfo[0].label : '' }}批量导入模板》
        </span>
      </div>
      <div class="upload_file_wrap">
        <el-input v-model="fileName" placeholder="请输入内容" readonly>
          <template #append>
            <label for="up" class="upload_label">
              选择文件
              <input
                id="up"
                style="display: none"
                ref="fileInput"
                type="file"
                class="form-control page_clear_btn"
                @change="fileChange"
              />
            </label>
          </template>
        </el-input>
      </div>
    </div>
    <template #footer>
      <el-button class="page_clear_btn" @click="cancel">取 消</el-button>
      <el-button class="page_add_btn" type="primary" @click="submitBtn">开始导入</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  exportperModuleDictionary,
  importPerModuleDictionary,
  exportPosModuleDictionary,
  importPorModuleDictionary
} from '../../../request/api'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  getEnqId: {
    type: Function,
    required: true
  },
  modelId: {
    type: [String, Number],
    required: true
  },
  tabsPaneInfo: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:show', 'importSign'])

const dialogVisible = ref(props.show)
const enqId = ref('')
const fileName = ref('')
const uploadFile = ref(null)
const fileInput = ref(null)

const downloadFile = async (res, label) => {
  const blob = new Blob([res])
  const elink = document.createElement('a')
  elink.download = `${label}列表.xlsx`
  elink.style.display = 'none'
  elink.href = URL.createObjectURL(blob)
  document.body.appendChild(elink)
  elink.click()
  URL.revokeObjectURL(elink.href)
  document.body.removeChild(elink)
}

const Export = async () => {
  try {
    const params = {
      enqId: enqId.value,
      modelId: props.modelId
    }

    if (props.tabsPaneInfo[0].moduleCode == 'XZW') {
      const res = await exportPosModuleDictionary(params)
      await downloadFile(res, props.tabsPaneInfo[0].label)
    } else {
      const res = await exportperModuleDictionary(params)
      await downloadFile(res, props.tabsPaneInfo[0].label)
    }
  } catch (error) {
    ElMessage.error('导出模板失败')
  }
}

const fileChange = e => {
  if (e.target.files.length == 0) return

  const file = e.target.files[0]
  const formData = new FormData()
  formData.append('file', file)
  formData.append('enqId', enqId.value)
  formData.append('modelId', props.modelId)

  fileName.value = file.name
  uploadFile.value = formData
}

const importFile = async importFn => {
  try {
    emit('importSign', false)
    const res = await importFn(uploadFile.value)
    if (res.code == 200) {
      ElMessage.success('上传成功')
      dialogVisible.value = false
      emit('importSign', true)
    } else {
      ElMessage.warning(res.msg)
    }
  } catch (error) {
    ElMessage.error('导入失败')
  }
}

const cancel = () => {
  dialogVisible.value = false
}

const submitBtn = async () => {
  if (!fileName.value) {
    ElMessage.warning('请选择导入文件!')
    return
  }

  if (props.tabsPaneInfo[0].moduleCode == 'XZW') {
    await importFile(importPorModuleDictionary)
  } else {
    await importFile(importPerModuleDictionary)
  }
}

watch(
  () => props.show,
  val => {
    dialogVisible.value = val
    if (!val) {
      fileName.value = ''
      if (fileInput.value) {
        fileInput.value.value = null
      }
    }
  }
)

onMounted(() => {
  enqId.value = props.getEnqId()
})
</script>

<style scoped lang="scss">
.import_post_wrap {
  .import_post_title {
    color: #515c71;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .oper_step {
    line-height: 28px;
    color: #515c71;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .download_file {
    margin-bottom: 16px;
    span {
      cursor: pointer;
    }
  }

  .upload_file_wrap {
    margin-bottom: 16px;

    .upload_label {
      display: block;
      height: 28px;
      line-height: 28px;
      width: 100%;
      cursor: pointer;
    }
  }
}

:deep(.el-dialog__header) {
  background-color: #ebf4ff;
  padding: 15px 20px;
}
</style>
