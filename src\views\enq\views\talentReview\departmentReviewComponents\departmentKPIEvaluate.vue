<template>
  <div class="department_KPI_evaluate_wrap performance_info_main">
    <div class="">
      <div class="page_second_title marginT_8">
        <span>KPI评价</span>
      </div>
      <div class="department_KPI_evaluate_mian flex_row_betweens marginT_8">
        <div class="department_KPI_evaluate_left">
          <ul class="left_staff_list_wrap">
            <li
              class="flex_row_between"
              v-for="(item, index) in evaluationSubList"
              :key="index"
              :class="{ check_sub: item.userId == defaultCheckSub }"
              @click="() => checkEvaluationSub(item.userId, index, item.relation)"
            >
              <span>{{ item.userName }}</span>
              <span
                :class="{
                  icon: true,
                  'el-icon-check': item.type == 'Y',
                  circle_icon: item.type != 'Y'
                }"
              ></span>
            </li>
          </ul>
        </div>
        <div class="department_KPI_evaluate_right">
          <div class="title_info">绩效指标</div>
          <div class="index_list_wrap">
            <div class="edu_info_center kpi_center">
              <div class="edu_info_header kpi_evaluate_header">
                <div class="item">指标名称</div>
                <div class="item bg_color">目标</div>
                <div class="item bg_color">实际表现</div>
                <div class="item">评价标准</div>
                <div class="item evaluation_number">权重(%)</div>
                <div class="item evaluation_number" v-if="curRelation != '2'">上级评价</div>
                <div class="item evaluation_number" v-if="curRelation != '1'">分管领导</div>
                <div class="item evaluation_number">综合得分</div>
              </div>
            </div>
            <div class="kpi_evaluate_main">
              <div class="edu_info_item" v-for="(item, index) in kpiList" :key="item.kpiIndex">
                <div class="item overflow_elps" :title="item.kpiName">
                  {{ item.kpiName }}
                </div>
                <div class="item bg_color">{{ item.kpiObjective }}</div>
                <div class="item bg_color">{{ item.kpiActual }}</div>
                <div class="item overflow_elps">
                  {{ item.evaluationStandard }}
                </div>
                <el-input
                  class="item evaluation_number"
                  type="number"
                  step="1"
                  :min="0"
                  :max="100"
                  v-model="item.weight"
                  @input="val => numberChange(index, val)"
                  @change="val => numberChange(index, val)"
                ></el-input>
                <el-input
                  class="item evaluation_number"
                  v-if="curRelation != '2'"
                  type="number"
                  step="1"
                  :min="0"
                  :max="100"
                  v-model="item.supScore"
                ></el-input>
                <el-input
                  class="item evaluation_number"
                  v-if="curRelation != '1'"
                  type="number"
                  step="1"
                  :min="0"
                  :max="100"
                  v-model="item.leaderScore"
                ></el-input>
                <el-input
                  class="item evaluation_number"
                  :disabled="true"
                  type="number"
                  step="1"
                  :min="0"
                  :max="100"
                  v-model="item.overallScore"
                ></el-input>
              </div>
              <div class="summarize_line_wrap flex_row_betweens" v-if="kpiList.length > 0">
                <div class="score">标得分: {{ kpiScore }}</div>
                <div class="sysMerit">系统评级: {{ kpiSysMeritName }}</div>
                <div class="expression">
                  实际表现：
                  <el-select class="item" clearable v-model.number="expression" placeholder="请选择">
                    <el-option
                      v-for="item in comprehensiveAssessmentOption"
                      :key="item.dictCode"
                      :label="item.codeName"
                      :value="item.dictCode"
                    ></el-option>
                  </el-select>
                </div>
              </div>
            </div>
            <div class="submit_btn_wrap" v-if="kpiList.length > 0">
              <el-button class="page_add_btn" type="primary" @click="affirm()">保存</el-button>
            </div>
            <div class="align_center">
              <el-button
                class="page_confirm_btn"
                type="primary"
                @click="prevBtn"
                v-show="currentIndex != currentFirstCode"
                >上一步</el-button
              >
              <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits } from 'vue'
import { getDirectSubordinates, getObjectiveKpi, saveObjectiveKpi } from '../../../request/api'
import { useUserStore } from '@/stores/modules/user.js'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const defaultCheckSub = ref('')
const evaluationSubList = ref([])
const kpiList = ref([])
const comprehensiveAssessmentOption = ref([])
const kpiScore = ref('')
const kpiSysMeritName = ref('')
const expression = ref('')
const curRelation = ref('')

const userStore = useUserStore()

const getDictData = async () => {
  const res = await userStore.getDocList(['ACTUAL_GRADE'])
  comprehensiveAssessmentOption.value = res.ACTUAL_GRADE
}

const getDirectSubordinatesFun = () => {
  getDirectSubordinates({
    enqId: props.enqId,
    type: 'kpi'
  }).then(res => {
    evaluationSubList.value = []
    if (res.code == 200 && res.data.length > 0) {
      evaluationSubList.value = res.data.map(item => ({
        userId: item.userId,
        userName: item.userName,
        type: item.type,
        relation: item.relation
      }))
      if (!defaultCheckSub.value) {
        defaultCheckSub.value = evaluationSubList.value[0].userId
        curRelation.value = evaluationSubList.value[0].relation
      }
      getObjectiveKpiFun()
    }
  })
}

const checkEvaluationSub = (val, index, rela) => {
  defaultCheckSub.value = val
  curRelation.value = rela
  getObjectiveKpiFun()
}

const getObjectiveKpiFun = () => {
  getObjectiveKpi({
    enqId: props.enqId,
    userId: defaultCheckSub.value
  }).then(res => {
    kpiList.value = []
    if (res.code == 200) {
      kpiList.value = res.data.enqObjectiveKpis.map(item => ({
        kpiName: item.kpiName,
        kpiObjective: item.kpiObjective,
        kpiActual: item.kpiActual,
        evaluationStandard: item.evaluationStandard,
        weight: item.weight,
        supScore: curRelation.value != '2' ? item.supScore : null,
        leaderScore: curRelation.value != '1' ? item.leaderScore : null,
        kpiCode: item.kpiCode,
        postCode: item.postCode,
        overallScore: item.overallScore
      }))
      expression.value = res.data.kpiOverallMerit
      kpiScore.value = res.data.kpiScore
      kpiSysMeritName.value = res.data.kpiSysMeritName
    }
  })
}

const numberChange = (index, curNumber) => {
  if (curNumber > 100) {
    kpiList.value[index].weight = 100
  }
  if (curNumber < 0) {
    kpiList.value[index].weight = 0
  }
}

const affirm = stepType => {
  saveObjectiveKpiFun(stepType)
}

const saveObjectiveKpiFun = stepType => {
  if (curRelation.value == 1) {
    for (let i = 0; i < kpiList.value.length; i++) {
      if (kpiList.value[i].weight === '' || kpiList.value[i].supScore === '') {
        ElMessage.warning('请完善评价信息！')
        return
      }
    }
  } else if (curRelation.value == 2) {
    for (let i = 0; i < kpiList.value.length; i++) {
      if (kpiList.value[i].weight === '' || kpiList.value[i].leaderScore === '') {
        ElMessage.warning('请完善评价信息！')
        return
      }
    }
  } else if (curRelation.value == 3) {
    for (let i = 0; i < kpiList.value.length; i++) {
      if (kpiList.value[i].weight === '' || kpiList.value[i].supScore === '' || kpiList.value[i].leaderScore === '') {
        ElMessage.warning('请完善评价信息！')
        return
      }
    }
  }
  if (kpiList.value.length > 0 && !expression.value) {
    ElMessage.warning('请完善评价信息！')
    return
  }
  let enqUserInfoRequest = {
    enqId: props.enqId,
    enqObjectiveKpiRequests: kpiList.value,
    kpiOverallMerit: expression.value,
    userId: defaultCheckSub.value,
    relationModule: props.currentIndex,
    relation: curRelation.value
  }
  saveObjectiveKpi(enqUserInfoRequest).then(res => {
    if (res.code == 200) {
      ElMessage.success(res.msg)
      if (stepType == 'prevStep') {
        emit('prevStep')
      } else if (stepType == 'nextStep') {
        emit('nextStep')
      } else if (!stepType) {
        getDirectSubordinatesFun()
      }
    } else {
      ElMessage.warning(res.msg)
    }
  })
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      affirm('prevStep')
    })
    .catch(action => {
      if (action == 'cancel') emit('prevStep')
    })
}

const nextBtn = () => {
  let isHasUnevaluated = evaluationSubList.value.filter(index => index.type != 'Y')
  if (isHasUnevaluated.length > 0) {
    ElMessage.warning('请评价所有人员！')
    return
  } else {
    if (kpiList.value.length > 0) {
      affirm('nextStep')
    } else {
      emit('nextStep')
    }
  }
}

onMounted(() => {
  getDictData()
  getDirectSubordinatesFun()
})
</script>

<style scoped lang="scss">
.department_KPI_evaluate_wrap {
  .department_KPI_evaluate_mian {
    .department_KPI_evaluate_left {
      margin: 0 30px 0 0;
      .left_staff_list_wrap {
        li {
          margin: 0 0 15px 0;
          padding: 0 20px;
          width: 190px;
          height: 40px;
          line-height: 40px;
          background: #e0e3ea;
          border-radius: 4px;
          color: #0091f9;
          cursor: pointer;
          .icon {
            font-size: 22px;
            font-weight: 600;
          }
          .circle_icon {
            width: 17px;
            height: 17px;
            border-radius: 50%;
            background: #0091f9;
          }
        }
        .check_sub {
          color: #fff;
          background: #0091f9;
          .icon {
            color: #fff;
          }
          .circle_icon {
            background: #fff;
          }
        }
        li:hover {
          color: #fff;
          background: #0091f9;
          .icon {
            color: #fff;
          }
          .circle_icon {
            background: #fff;
          }
        }
      }
    }
    .department_KPI_evaluate_right {
      border-left: 1px solid #d8d8d8;
      padding: 0 0 0 20px;
      flex: 1;
      .title_info {
        margin: 0 0 15px 0;
      }
      .index_list_wrap {
        border: 1px solid #d8d8d8;
        padding: 22px;
      }
    }
  }
  .kpi_center {
    .kpi_evaluate_header {
      .item {
        width: 13%;
      }
      .index {
        // width: 5%;
      }
      .evaluation_number {
        width: 8%;
      }
    }
  }
  .kpi_evaluate_main {
    .edu_info_item {
      border-bottom: 1px solid #d8d8d8;
      .item {
        width: 13%;
      }
      .index {
        // margin: 0 auto;
        // width: 5%;
      }
      .bg_color {
        background: #ededed;
        border-radius: 3px;
      }
      .evaluation_number {
        width: 8%;
      }
    }
    .summarize_line_wrap {
      padding: 0 20px 0 0;
      margin: 20px 0 0 0;
      .expression {
        .el-select {
          width: 137px;
        }
      }
    }
  }
  .tips_wrap {
    padding: 0 0 0 20px;
    height: 45px;
    line-height: 45px;
    span {
      color: #3b92ff;
    }
  }
  .submit_btn_wrap {
    margin: 30px 0 0 0;
  }
  .align_center {
    padding: 110px 0 0 0;
  }
}
</style>
