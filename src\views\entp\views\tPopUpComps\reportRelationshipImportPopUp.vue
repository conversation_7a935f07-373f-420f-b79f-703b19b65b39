<template>
  <el-dialog title="开始导入" v-model="dialogVisible" @close="$emit('update:show', false)" width="50%" center>
    <div class="import_post_wrap">
      <div class="import_post_title">操作步骤:</div>
      <div class="oper_step">
        <p>1、下载《汇报关系批量导入模板》</p>
        <p>2、打开下载表，将对应字段信息输入或粘贴进本表，为了保障粘贴信息被有效导入，请使用纯文本或者数字。</p>
        <p>3、信息输入完毕，点击"选择文件"按钮，选择excel文档。</p>
        <p>4、点击"开始导入",导入中如有任何疑问，请致电000000000。</p>
      </div>
      <a
        class="fs16 main_color download_file"
        href="/edp/api/entp/reportRelationshipTemplate.xlsx"
        download="汇报关系批量导入模板.xlsx"
        >立即下载《汇报关系批量导入模板》</a
      >
      <div class="upload_file_wrap">
        <el-input placeholder="请输入内容" v-model="fileName" readonly>
          <template #append>
            <label for="up" class="upload_label">
              选择文件
              <input
                id="up"
                style="display: none"
                ref="fileInput"
                type="file"
                class="form-control page_clear_btn"
                @change="fileChange"
              />
            </label>
          </template>
        </el-input>
      </div>
    </div>
    <template #footer>
      <el-button class="page_clear_btn" @click="cancel">取 消</el-button>
      <el-button class="page_add_btn" type="primary" @click="submitBtn">开始导入</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { readReportRelationshipExcelData } from '../../request/api'

// Props定义
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

// Emits定义
const emit = defineEmits(['update:show', 'importSign'])

// 路由实例
const router = useRouter()

// 响应式状态
const dialogVisible = computed({
  get: () => props.show,
  set: value => emit('update:show', value)
})
const fileName = ref('')
const uploadFile = ref(null)
const fileInput = ref(null)

// 方法定义
const fileChange = e => {
  if (e.target.files.length == 0) {
    return
  }
  const formData = new FormData()
  const file = e.target.files[0]
  formData.append('file', file)
  fileName.value = file.name
  uploadFile.value = formData
}

const readReportRelationshipExcelDataFun = async () => {
  emit('importSign', false)
  try {
    const res = await readReportRelationshipExcelData(uploadFile.value)
    if (res.code == 200) {
      uploadFile.value = null
      if (res.data.errorCount > 0) {
        router.push({
          path: '/basicSettingHome/staffManagement/reportRelationshipErrorData',
          query: {
            data: res.data.obj
          }
        })
      } else {
        ElMessage.success(res.msg)
        fileName.value = ''
        if (fileInput.value) {
          fileInput.value.value = null
        }
        dialogVisible.value = false
        emit('importSign', true)
      }
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请重试')
  }
}

const cancel = () => {
  dialogVisible.value = false
}

const submitBtn = () => {
  if (!fileName.value) {
    ElMessage.warning('请选择导入文件!')
    return
  }
  readReportRelationshipExcelDataFun()
}
</script>

<style scoped>
.import_post_wrap .import_post_title {
  color: #515c71;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.import_post_wrap .oper_step {
  line-height: 28px;
  color: #515c71;
  font-size: 14px;
  margin-bottom: 16px;
}

.import_post_wrap .download_file {
  display: block;
  margin-bottom: 16px;
}

.import_post_wrap .upload_file_wrap {
  margin-bottom: 16px;
}

.import_post_wrap .upload_file_wrap .upload_label {
  display: block;
  height: 28px;
  line-height: 28px;
  width: 100%;
  cursor: pointer;
}

:deep(.el-dialog__header) {
  background-color: var(--color-tagbg);
  padding: 15px 20px;
}
</style>
