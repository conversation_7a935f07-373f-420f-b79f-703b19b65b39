import Layout from '@/layout/index.vue'

const entpRoutes = [
  {
    path: '/personnelInfoHome',
    redirect: '/basicSettingHome/personnelInfo/personnelInfo',
    name: 'basicSettingHome',
    meta: {
      name: '基础设置'
    },
    // component: () => import('@/layout/index.vue'),
    component: () => import('@/views/entp/views/basicSettingHome.vue'),
    children: [
      // 个人信息
      {
        path: '/basicSettingHome/personnelInfoHome',
        name: 'personnelInfoHome',
        meta: {
          name: '个人信息',
          iconName: 'el-icon-message'
        },
        component: () => import('@/views/entp/views/personnelInfo/personnelInfoHome.vue'),
        children: [
          {
            path: '/basicSettingHome/personnelInfo/basicInfo',
            name: 'basicInfo',
            meta: {
              name: '基础信息'
            },
            component: () => import('@/views/entp/views/personnelInfo/basicInfo.vue')
          },

          {
            path: '/basicSettingHome/personnelInfo/eduInfo',
            name: 'eduInfo',
            meta: {
              name: '教育信息'
            },
            component: () => import('@/views/entp/views/personnelInfo/eduInfo.vue')
          },
          {
            path: '/basicSettingHome/personnelInfo/workExperience',
            name: 'workExperience',
            meta: {
              name: '工作履历'
            },
            component: () => import('@/views/entp/views/personnelInfo/workExperience.vue')
          },
          {
            path: '/basicSettingHome/personnelInfo/accountManagement',
            name: 'accountManagement',
            meta: {
              name: '账号密码'
            },
            component: () => import('@/views/entp/views/personnelInfo/accountManagement.vue')
          }
        ]
      },

      // 企业设置
      {
        path: '/basicSettingHome/companySeting',
        redirect: '/basicSettingHome/companySeting/companyInformation',
        name: 'companySetingHome',
        meta: {
          name: '企业设置',
          iconName: 'el-icon-tickets'
        },
        component: () => import('@/views/entp/views/companySeting/companySetingHome.vue'),
        children: [
          {
            path: '/basicSettingHome/companySeting/companyInformation',
            name: 'companyInformation',
            meta: {
              name: '企业信息设置'
            },
            component: () => import('@/views/entp/views/companySeting/companyInformation.vue')
          },
          {
            path: '/basicSettingHome/companySeting/businessArea',
            name: 'businessArea',
            meta: {
              name: '业务领域设置'
            },
            component: () => import('@/views/entp/views/companySeting/businessArea.vue')
          },
          {
            path: '/basicSettingHome/companySeting/businessProcess',
            name: 'businessProcess',
            meta: {
              name: '业务流程设置'
            },
            component: () => import('@/views/entp/views/companySeting/businessProcess.vue')
          }
        ]
      },
      // 组织管理
      {
        path: '/basicSettingHome/orgManagement',
        redirect: '/basicSettingHome/orgManagement/orgLevel',
        name: 'orgManagementHome',
        meta: {
          name: '组织管理',
          iconName: 'el-icon-bell'
        },
        component: () => import('@/views/entp/views/orgManagement/orgManagementHome.vue'),
        children: [
          {
            path: '/basicSettingHome/orgManagement/orgLevel',
            name: 'orgLevel',
            meta: {
              name: '组织层级'
            },
            component: () => import('@/views/entp/views/orgManagement/orgLevel.vue')
          },
          {
            path: '/basicSettingHome/orgManagement/orgDuty',
            name: 'orgDuty',
            meta: {
              name: '组织职责'
            },
            component: () => import('@/views/entp/views/orgManagement/orgDuty.vue')
          },
          {
            path: '/basicSettingHome/orgManagement/orgDutyImportErrorData',
            name: 'orgDutyImportErrorData',
            meta: {
              hidd: true,
              name: '组织职责导入错误一览'
            },
            component: () => import('@/views/entp/views/orgManagement/orgDutyImportErrorData.vue')
          },
          {
            path: '/basicSettingHome/orgManagement/orgManagement',
            name: 'orgManagement',
            meta: {
              name: '组织管理'
            },
            component: () => import('@/views/entp/views/orgManagement/orgManagement.vue')
          },
          {
            path: '/basicSettingHome/orgManagement/orgImportErrorData',
            name: 'orgImportErrorData',
            meta: {
              hidd: true,
              name: '组织信息导入错误一览'
            },
            component: () => import('@/views/entp/views/orgManagement/orgImportErrorData.vue')
          },
          {
            path: '/basicSettingHome/orgManagement/orgChart',
            name: 'orgChart',
            meta: {
              name: '组织结构图'
            },
            component: () => import('@/views/entp/views/orgManagement/orgChart.vue')
          }
        ]
      },

      // 岗位管理
      {
        path: '/basicSettingHome/postManagement',
        redirect: '/basicSettingHome/postManagement/postManagement',
        name: 'postManagementHome',
        meta: {
          name: '岗位管理'
        },
        component: () => import('@/views/entp/views/postManagement/postManagementHome.vue'),
        children: [
          {
            path: '/basicSettingHome/postManagement/postSequence',
            name: 'postSequence',
            meta: {
              name: '岗位序列'
            },
            component: () => import('@/views/entp/views/postManagement/postSequence.vue')
          },
          {
            path: '/basicSettingHome/postManagement/jobGradeManagement',
            name: 'jobGradeManagement',
            meta: {
              name: '职等管理'
            },
            component: () => import('@/views/entp/views/postManagement/jobGradeManagement.vue')
          },
          {
            path: '/basicSettingHome/postManagement/jobLevelManagement',
            name: 'jobLevelManagement',
            meta: {
              name: '职层管理'
            },
            component: () => import('@/views/entp/views/postManagement/jobLevelManagement.vue')
          },
          {
            path: '/basicSettingHome/postManagement/workActivity',
            name: 'workActivity',
            meta: {
              name: '工作活动'
            },
            component: () => import('@/views/entp/views/postManagement/workActivity.vue')
          },
          {
            path: '/basicSettingHome/postManagement/jobManagement',
            name: 'jobManagement',
            meta: {
              name: '职位管理'
            },
            component: () => import('@/views/entp/views/postManagement/jobManagement.vue')
          },
          // 修改导入职位数据
          {
            path: '/basicSettingHome/postManagement/jobImportErrorData',
            name: 'jobImportErrorData',
            meta: {
              hidd: true,
              name: '职位管理'
            },
            component: () => import('@/views/entp/views/postManagement/jobImportErrorData.vue')
          },
          {
            path: '/basicSettingHome/postManagement/createJob',
            name: 'createPost',
            meta: {
              hidd: true,
              name: '新增职位'
              // hidden: true
            },
            component: () => import('@/views/entp/views/postManagement/createJob.vue')
          },
          {
            path: '/basicSettingHome/postManagement/postManagement',
            name: 'postManagement',
            meta: {
              name: '岗位管理'
            },
            component: () => import('@/views/entp/views/postManagement/postManagement.vue')
          },
          {
            path: '/basicSettingHome/postManagement/postImportErrorData',
            name: 'postImportErrorData',
            meta: {
              hidd: true,
              name: '岗位导入错误数据'
            },
            component: () => import('@/views/entp/views/postManagement/postImportErrorData.vue')
          },
          {
            path: '/basicSettingHome/postManagement/createPost',
            name: 'createPost',
            meta: {
              hidd: true,
              name: '新增岗位'
              // hidden: true
            },
            component: () => import('@/views/entp/views/postManagement/createPost.vue')
          },
          {
            path: '/basicSettingHome/postManagement/postRelationshipMap',
            name: 'postRelationshipMap',
            meta: {
              name: '岗位关系图谱'
            },
            component: () => import('@/views/entp/views/postManagement/postRelationshipMap.vue')
          },
          {
            path: '/basicSettingHome/postManagement/postDescription',
            name: 'postDescription',
            meta: {
              name: '岗位说明书'
            },
            component: () => import('@/views/entp/views/postManagement/postDescription.vue')
          },
          {
            path: '/basicSettingHome/postManagement/postDescription/postDescriptionDetailed',
            name: 'postDescriptionDetailed',
            meta: {
              hidd: true,
              name: '岗位说明书-详细'
            },
            component: () => import('@/views/entp/views/postManagement/postDescriptionDetailed.vue')
          }
        ]
      },
      // 员工管理
      {
        path: '/basicSettingHome/staffManagement',
        redirect: '/basicSettingHome/staffManagement/staffManagement',
        name: 'staffManagementHome',
        meta: {
          name: '员工管理'
        },
        component: () => import('@/views/entp/views/staffManagement/staffManagementHome.vue'),
        children: [
          {
            path: '/basicSettingHome/staffManagement/staffManagement',
            name: 'staffManagement',
            meta: {
              name: '员工管理'
            },
            component: () => import('@/views/entp/views/staffManagement/staffManagement.vue')
          },
          {
            path: '/basicSettingHome/staffManagement/createStaff',
            name: 'createStaff',
            meta: {
              name: '新增员工'
            },
            component: () => import('@/views/entp/views/staffManagement/createStaff.vue')
          },
          {
            path: '/basicSettingHome/staffManagement/editStaff',
            name: 'editStaff',
            meta: {
              name: '编辑员工'
            },
            component: () => import('@/views/entp/views/staffManagement/createStaff.vue')
          },
          {
            path: '/basicSettingHome/staffManagement/staffImportView',
            name: 'staffImportView',
            meta: {
              name: '员工导入'
            },
            component: () => import('@/views/entp/views/staffManagement/staffImportView.vue')
          },
          // 汇报关系
          {
            path: '/basicSettingHome/reportRelationshipHome',
            redirect: '/basicSettingHome/reportRelationshipHome/reportRelationshipList',
            name: 'reportRelationshipHome',
            meta: {
              name: '汇报关系'
            },
            component: () => import('@/views/entp/views/staffManagement/reportRelationshipHome.vue'),
            children: [
              {
                path: '/basicSettingHome/reportRelationshipHome/reportRelationshipList',
                name: 'reportRelationshipList',
                meta: {
                  name: '汇报关系'
                },
                component: () => import('@/views/entp/views/staffManagement/reportRelationshipList.vue')
              },
              {
                path: '/basicSettingHome/reportRelationshipHome/reportRelationshipErrorData',
                name: 'reportRelationshipErrorData',
                meta: {
                  name: '汇报关系报错信息页面'
                },
                component: () => import('@/views/entp/views/staffManagement/reportRelationshipErrorData.vue')
              }
            ]
          }
        ]
      },
      // 指标管理
      {
        path: '/basicSettingHome/targetManagementHome',
        redirect: '/basicSettingHome/targetManagementHome/targetMaintenance',
        name: 'targetManagementHome',
        meta: {
          name: '指标管理'
        },
        component: () => import('@/views/entp/views/targetManagement/targetManagementHome.vue'),
        children: [
          {
            path: '/basicSettingHome/targetManagementHome/targetMaintenance',
            name: 'targetMaintenance',
            meta: {
              name: '指标维护'
            },
            component: () => import('@/views/entp/views/targetManagement/targetMaintenance.vue')
          },
          {
            path: '/basicSettingHome/targetManagementHome/relationshipGroups',
            name: 'relationshipGroups',
            meta: {
              name: '组织关联'
            },
            component: () => import('@/views/entp/views/targetManagement/relationshipGroups.vue')
          },
          {
            path: '/basicSettingHome/targetManagementHome/postAssociated',
            name: 'postAssociated',
            meta: {
              name: '岗位关联'
            },
            component: () => import('@/views/entp/views/targetManagement/postAssociated.vue')
          },
          {
            path: '/basicSettingHome/targetManagementHome/personnelAssociated',
            name: 'personnelAssociated',
            meta: {
              name: '人员关联'
            },
            component: () => import('@/views/entp/views/targetManagement/personnelAssociated.vue')
          }
        ]
      },
      // 系统管理
      {
        path: '/basicSettingHome/systemManagementHome',
        redirect: '/basicSettingHome/systemManagementHome/rolePermission',
        name: 'systemManagementHome',
        meta: {
          name: '系统管理'
        },
        component: () => import('@/views/entp/views/systemManagement/systemManagementHome.vue'),
        children: [
          {
            path: '/basicSettingHome/systemManagementHome/rolePermission',
            name: 'rolePermission',
            meta: {
              name: '角色权限'
            },
            component: () => import('@/views/entp/views/systemManagement/rolePermission.vue')
          },
          {
            path: '/basicSettingHome/systemManagementHome/operationLog',
            name: 'operationLog',
            meta: {
              name: '角色权限'
            },
            component: () => import('@/views/entp/views/systemManagement/operationLog.vue')
          }
        ]
      }
    ]
  }
]

export default entpRoutes
