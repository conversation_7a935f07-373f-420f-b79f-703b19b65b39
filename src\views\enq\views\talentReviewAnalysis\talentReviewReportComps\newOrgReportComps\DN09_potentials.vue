<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <div class="page_second_title">人员潜力</div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24">
                <div class="item_title">人员潜力详情</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentChange"
                    @handleSizeChange="handleSizeChange"
                    :tableData="tableData"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf && tableData.page.total >10">
                    更多数据请查看网页版报告
                </div>
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    // import {
    //     orgPersonalDeve,
    //     orgTalentDeve,
    //     performanceMatrix,
    //     potentialMatrix,
    // } from "../../../../request/api";
    import{getPersonnelPotential,getPotentialDetails} from "../../../../request/api.js"
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import talentClassifyMatrix from "@/components/talent/common/talentClassifyMatrix";
    import talentMatrix from "@/components/talent/common/talentMatrix";
    export default {
        name: "orgRPotentials",
        props: ["enqId", "orgCode", "isPdf"],
        components: { tableComps, talentClassifyMatrix, talentMatrix },
        data() {
            return {
                size: 10,
                current: 1,
                kpiRankOption: [],
                competenceRankOptions: [],
                developmentOptions: [],
                developmentCapability: [],
                kpiCapablity: {},
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "人员潜力分布",
                        elSpan: 8,
                        chartHeight: "200",
                        chartType: "YBar",
                        dataKey: "potentialEval",
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "整体潜力实际",
                        elSpan: 16,
                        chartType: "XBar",
                        dataKey: "potentialActual",
                    },
                ],
                tableData: {
                    columns: [
                       
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
            };
        },
        created() {
            // this.init();
            this.getPersonnelPotentialFun()
            this.getPotentialDetailsFun()
        },
        mounted() {
           
        },
        methods: {
            getPersonnelPotentialFun(){
                getPersonnelPotential({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then(res=>{
                    if(res.code == 200){
                        this.initChart(res.data)
                    }
                })
            },
            getPotentialDetailsFun(){
                getPotentialDetails({
                    size: this.tableData.page.size,
                    current: this.tableData.page.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then(res=>{
                    if(res.code == 200){
                        this.tableData.columns = [ 
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                        ]
                        let tableTitle = res.data.legend.map(item=>{
                            return{
                                label:item.name,
                                prop:item.code.replace(/\./g,'-')
                            }
                        })
                        let tableTitleEnd = [
                            {
                                label: "系统评级",
                                prop: "sysGrade",
                            },
                            {
                                label: "上级评价等级",
                                prop: "grade",
                            },
                        ]
                        this.tableData.columns = this.tableData.columns.concat(tableTitle)
                        this.tableData.columns = this.tableData.columns.concat(tableTitleEnd)
                        this.tableData.data = this.dotToline(res.data.dataList, "key");
                        this.tableData.page = res.page
                    }
                })
            },
            handleCurrentChange(current) {
                this.tableData.page.current = current;
                this.getPotentialDetailsFun();
            },
            handleSizeChange(size) {
                this.tableData.page.size = size;
                this.getPotentialDetailsFun();
            },
            dotToline(param, type, valueKey) {
                if (Array.isArray(param)) {
                    if (param.length == 0) {
                        return;
                    }
                    param.forEach((item) => {
                        if (typeof item == "object") {
                            for (const key in item) {
                                if (item.hasOwnProperty(key)) {
                                    if (type == "key") {
                                        let newKey = key.split(".").join("-");
                                        item[newKey] = item[key];
                                    }else if(type == "value"){
                                        let val = item[valueKey];
                                        item[valueKey] = val.split(".").join("-");
                                    }
                                    // delete item[key];
                                }
                            }
                        }
                    });
                    return param;
                }
            },
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },

            // ---------
            init() {
                let docList = [
                    "KPI_RANK",
                    "COMPETENCE_RANK",
                    "DEVELOPMENT_POTENTIAL",
                ];
                this.$getDocList(docList).then((res) => {
                    this.kpiRankOption = this.$util
                        .deepClone(res.KPI_RANK)
                        .reverse();
                    this.competenceRankOptions = res.COMPETENCE_RANK;
                    this.developmentOptions = res.DEVELOPMENT_POTENTIAL;
                });
                this.getData();
                this.orgRiskDetailsFn();
                this.performanceMatrixFn();
                this.potentialMatrixFn();
            },

            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTalentDeve(params).then((res) => {
                    if (res.code == "200") {
                        this.initChart(res.data);
                    }
                });
            },
            orgRiskDetailsFn() {
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgPersonalDeve(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
            // 能力绩效矩阵
            performanceMatrixFn() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                performanceMatrix(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.kpiCapablity = res.data;
                    }
                });
            },
            // 能力潜力矩阵
            potentialMatrixFn() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                potentialMatrix(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.developmentCapability = res.data;
                    }
                });
            },

        },
    };
</script>
 
<style scoped lang="scss">
</style>