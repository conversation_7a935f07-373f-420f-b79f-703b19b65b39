<template>
  <div>
    <div class="post_manage_basic_info_wrap from_wrap">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="100px" class="demo-ruleForm">
        <el-form-item label="所属组织" prop="orgCode">
          <el-cascader
            :options="OrgTreeData"
            v-model="ruleForm.orgCode"
            placeholder="请选择组织"
            :change-on-select="true"
            :props="{
              label: 'value',
              value: 'code',
              expandTrigger: 'hover'
            }"
            @change="val => handleItemChange(val, 'OrgTree')"
            disabled
          />
        </el-form-item>
        <el-form-item label="岗位编码" prop="postCode">
          <el-input v-model="ruleForm.postCode" class="bg_none" disabled />
        </el-form-item>
        <el-form-item label="上级岗位">
          <el-cascader
            :options="parentPostTreeData"
            v-model="ruleForm.parentPostCode"
            placeholder="请选择上级岗位"
            :change-on-select="true"
            :props="{
              label: 'value',
              value: 'code',
              expandTrigger: 'hover'
            }"
            @change="val => handleItemChange(val, 'parentPostTree')"
            clearable
          />
        </el-form-item>
        <el-form-item label="岗位名称" prop="postName" class="modify_tip_style">
          <el-input v-model="ruleForm.postName" />
        </el-form-item>
        <el-form-item label="对应职位" prop="jobCode" class="modify_tip_style">
          <el-select v-model="ruleForm.jobCode" placeholder="请选择" filterable @change="jobChange">
            <el-option
              v-for="item in jobOption"
              :key="item.jobCode"
              :label="`${item.jobName} ${item.jobGradeName}`"
              :value="item.jobCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="岗位族群" prop="jobClassCode">
          <el-cascader
            :options="jobClassTreeData"
            v-model="ruleForm.jobClassCode"
            placeholder="请选择族群"
            :change-on-select="true"
            :disabled="true"
            :props="{
              label: 'value',
              value: 'code',
              expandTrigger: 'hover'
            }"
            clearable
          />
        </el-form-item>
        <el-form-item label="岗位职层">
          <el-cascader
            :options="jobLevelTreeData"
            v-model="ruleForm.jobLevelCode"
            placeholder="请选择职层"
            :change-on-select="true"
            :disabled="true"
            :props="{
              label: 'value',
              value: 'code',
              expandTrigger: 'hover'
            }"
            clearable
          />
        </el-form-item>
        <el-form-item label="职等" prop="jobGradeCode">
          <el-select v-model="ruleForm.jobGradeCode" clearable placeholder="请选择职等" :disabled="true">
            <el-option
              v-for="item in jobGradeOption"
              :key="item.dictCode"
              :label="item.codeName"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人岗位" prop="jobGradeCode" class="leader_post_wrap">
          <el-select v-model="ruleForm.isLeader" placeholder="请选择" @change="handelChange">
            <el-option
              v-for="item in isLeaderOption"
              :key="item.dictCode"
              :label="item.codeName"
              :value="item.dictCode"
            />
          </el-select>
          <span class="leader_tips_wrap overflow_elps" :title="isLeaderTips">
            {{ isLeaderTips }}
          </span>
        </el-form-item>
        <el-form-item label="岗位外部编码" prop="postCodeExtn" class="leader_post_wrap">
          <el-input v-model="ruleForm.postCodeExtn" :disabled="!!curPostCodeCopy" />
        </el-form-item>
        <el-form-item v-if="curPostCodeCopy" label="是否启用" prop="rstatus" class="leader_post_wrap">
          <el-switch
            v-model="ruleForm.rstatus"
            :disabled="disabledRstatus"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
        <el-form-item label="岗位描述" class="textarea_wrap">
          <el-input type="textarea" v-model="ruleForm.postDesc" />
        </el-form-item>
        <el-form-item class="align_center">
          <el-button class="page_confirm_btn" type="primary" @click="submitForm(ruleFormRef)"> 保存 </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'
import {
  getJobClassTree,
  jobLevelTree,
  getJobGradeList,
  getOrgDeptTree,
  createPost,
  getPostCode,
  getPostTree,
  searchPost,
  updatePost,
  checkPostLeader,
  getDict,
  getJobInfoList,
  jobInfoByCode
} from '../../../../request/api'

// Props
const props = defineProps({
  curPostCodeCopy: String,
  copyOrgCode: String
})

// Emits
const emit = defineEmits(['curPostCode', 'curJobClassCode', 'submitSuccessTab'])

// Store
const userStore = useUserStore()

// Refs
const ruleFormRef = ref(null)

// Reactive State
const ruleForm = ref({
  postCode: '',
  postName: '',
  jobCode: '',
  parentPostCode: '',
  checkParentPostCode: '',
  jobClassCode: '',
  checkJobClassCode: '',
  jobLevelCode: '',
  checkJobLevelCode: '',
  jobGradeCode: '',
  orgCode: props.copyOrgCode,
  postCodeExtn: '',
  postDesc: '',
  isLeader: '',
  rstatus: true
})

const disabledRstatus = ref(false)
const isLeaderOption = ref([])
const isLeaderTips = ref('')
const jobLevelTreeData = ref([])
const jobClassTreeData = ref([])
const OrgTreeData = ref([])
const jobGradeOption = ref({})
const parentPostTreeData = ref([])
const jobOption = ref([])

// Validation Rules
const rules = {
  orgCode: [
    {
      required: true,
      message: '请选择组织',
      trigger: 'change'
    }
  ],
  postCode: [
    {
      required: true,
      message: '请填写岗位编码',
      trigger: 'change'
    }
  ],
  postName: [
    {
      required: true,
      message: '请输入岗位名称',
      trigger: 'blur'
    }
  ],
  jobCode: [
    {
      required: true,
      message: '请选择对应职位',
      trigger: 'change'
    }
  ],
  postCodeExtn: [
    {
      required: true,
      message: '请填写岗位外部编码',
      trigger: 'blur'
    }
  ]
}

// Computed
const companyId = computed(() => userStore.userInfo.companyId)

// Methods
const getOrgTreeFun = async () => {
  try {
    getOrgDeptTree({ companyId: companyId.value }).then(res => {
      if (res.code == 200) {
        OrgTreeData.value = res.data.length > 0 ? res.data : []
      } else {
        OrgTreeData.value = []
      }
    })
  } catch (error) {
    console.error('获取组织部门树失败:', error)
  }
}

const getPostTreeFun = async val => {
  try {
    const res = await getPostTree({ orgCode: props.copyOrgCode })
    parentPostTreeData.value = res.length > 0 ? res : []
  } catch (error) {
    console.error('获取岗位树失败:', error)
  }
}

const getJobClassTreeFun = async () => {
  try {
    const res = await getJobClassTree({})
    if (res.length > 0) {
      res.forEach(item => {
        item.disabled = true
      })
      jobClassTreeData.value = res
    } else {
      jobClassTreeData.value = []
    }
  } catch (error) {
    console.error('获取职族树失败:', error)
  }
}

const getJobLevelTree = async () => {
  try {
    const res = await jobLevelTree({})
    jobLevelTreeData.value = res.length > 0 ? res : []
  } catch (error) {
    console.error('获取职层树失败:', error)
  }
}

const getJobGradeListFun = async () => {
  try {
    const res = await getJobGradeList({})
    if (res.code == 200 && res.data.length > 0) {
      jobGradeOption.value = res.data.map(item => ({
        dictCode: item.jobGradeCode,
        codeName: item.jobGradeName
      }))
    }
  } catch (error) {
    console.error('获取职等失败:', error)
  }
}

const getJobInfoListFun = async () => {
  try {
    const res = await getJobInfoList()
    jobOption.value = res.data.map(item => ({
      jobCode: item.jobCode,
      jobName: item.jobName,
      jobGradeName: item.jobGradeName
    }))
  } catch (error) {
    console.error('获取职位列表失败:', error)
  }
}

const handleItemChange = (val, treeType) => {
  if (!val) return

  const lastValue = val.length > 0 ? val[val.length - 1] : ''

  switch (treeType) {
    case 'jobLevelTree':
      ruleForm.value.checkJobLevelCode = lastValue
      break
    case 'jobClassTree':
      ruleForm.value.checkJobClassCode = lastValue
      break
    case 'parentPostTree':
      ruleForm.value.checkParentPostCode = lastValue
      break
  }
}

const getDictFun = async () => {
  try {
    const res = await getDict({ dictId: 'YES_NO' })
    if (res.code == 200) {
      isLeaderOption.value = res.data
      ruleForm.value.isLeader = isLeaderOption.value[1].dictCode
    }
  } catch (error) {
    console.error('获取字典失败:', error)
  }
}

const handelChange = () => {
  checkPostLeaderFun()
}

const jobChange = val => {
  jobInfoByCodeFun(val)
}

const jobInfoByCodeFun = async val => {
  try {
    const res = await jobInfoByCode({ jobCode: val })
    if (res.code == 200) {
      const { jobClassCode, jobLevelCode, jobGradeCode } = res.data
      ruleForm.value.jobClassCode = jobClassCode
      ruleForm.value.jobLevelCode = jobLevelCode
      ruleForm.value.jobGradeCode = jobGradeCode
    }
  } catch (error) {
    console.error('获取职位信息失败:', error)
  }
}

const checkPostLeaderFun = async () => {
  try {
    const res = await checkPostLeader({
      isLeader: ruleForm.value.isLeader,
      orgCode: props.copyOrgCode,
      postCode: props.curPostCodeCopy
    })
    isLeaderTips.value = res.msg || ''
  } catch (error) {
    console.error('检查岗位负责人失败:', error)
  }
}

const getPostCodeFun = async val => {
  if (props.curPostCodeCopy) {
    ruleForm.value.postCode = props.curPostCodeCopy
    return
  }

  try {
    const res = await getPostCode({ orgCode: val })
    ruleForm.value.postCode = res
  } catch (error) {
    console.error('获取岗位编码失败:', error)
  }
}

const submitForm = async formEl => {
  if (!formEl) return

  try {
    await formEl.validate()
    if (props.curPostCodeCopy) {
      await updatePostFun()
    } else {
      await createPostFun()
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    return false
  }
}

const createPostFun = async () => {
  try {
    const res = await createPost({
      postCode: ruleForm.value.postCode,
      postName: ruleForm.value.postName,
      jobCode: ruleForm.value.jobCode,
      jobClassCode: ruleForm.value.jobClassCode,
      jobLevelCode: ruleForm.value.jobLevelCode,
      jobGradeCode: ruleForm.value.jobGradeCode,
      orgCode: ruleForm.value.orgCode,
      postDesc: ruleForm.value.postDesc,
      parentPostCode: ruleForm.value.checkParentPostCode,
      isLeader: ruleForm.value.isLeader,
      postCodeExtn: ruleForm.value.postCodeExtn || null,
      rstatus: ruleForm.value.rstatus ? 'Y' : 'N'
    })

    if (res.code == 200) {
      emit('curPostCode', res.data)
      emit('curJobClassCode', ruleForm.value.checkJobClassCode)
      ElMessage.success(res.msg)
      emit('submitSuccessTab', 'basicInfo')
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('创建岗位失败:', error)
    ElMessage.error('创建岗位失败')
  }
}

const updatePostFun = async () => {
  try {
    const res = await updatePost({
      postCode: ruleForm.value.postCode,
      postName: ruleForm.value.postName,
      jobCode: ruleForm.value.jobCode,
      jobClassCode: ruleForm.value.jobClassCode,
      jobLevelCode: ruleForm.value.jobLevelCode,
      jobGradeCode: ruleForm.value.jobGradeCode,
      orgCode: ruleForm.value.orgCode,
      postDesc: ruleForm.value.postDesc,
      parentPostCode: ruleForm.value.checkParentPostCode,
      isLeader: ruleForm.value.isLeader,
      postCodeExtn: ruleForm.value.postCodeExtn,
      rstatus: ruleForm.value.rstatus ? 'Y' : 'N'
    })

    if (res.code == 200) {
      ElMessage.success(res.msg)
      emit('submitSuccessTab', 'basicInfo')
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('更新岗位失败:', error)
    ElMessage.error('更新岗位失败')
  }
}

const searchPostFun = async () => {
  if (!props.curPostCodeCopy) return

  try {
    const res = await searchPost({ postCode: props.curPostCodeCopy })
    if (res.code == 200) {
      const data = res.data
      Object.assign(ruleForm.value, {
        parentPostCode: data.parentPostCode,
        checkParentPostCode: data.parentPostCode,
        postName: data.postName,
        jobCode: data.jobCode,
        jobClassCode: data.jobClassCode,
        checkJobClassCode: data.jobClassCode,
        jobLevelCode: data.jobLevelCode,
        checkJobLevelCode: data.jobLevelCode,
        jobGradeCode: data.jobGradeCode,
        postDesc: data.postDesc,
        isLeader: data.isLeader || isLeaderOption.value[1].dictCode,
        postCodeExtn: data.postCodeExtn,
        rstatus: data.rstatus == 'Y'
      })
      disabledRstatus.value = data.disable
      emit('curJobClassCode', ruleForm.value.checkJobClassCode)
    }
  } catch (error) {
    console.error('获取岗位信息失败:', error)
  }
}

// Watchers
watch(
  () => companyId.value,
  val => {
    if (val) {
      getOrgTreeFun()
    }
  },
  { immediate: true }
)

// Lifecycle Hooks
onMounted(() => {
  getJobClassTreeFun()
  getJobLevelTree()
  getJobGradeListFun()
  getPostCodeFun(props.copyOrgCode)
  getPostTreeFun(props.copyOrgCode)
  getDictFun()
  searchPostFun()
  getJobInfoListFun()
})
</script>

<style lang="scss" scoped>
:deep(.el-input__inner) {
  width: 280px;
}

:deep(.el-textarea) {
  .el-textarea__inner {
    resize: none !important;
    font-family:
      PingFang SC,
      Avenir,
      Helvetica,
      Arial,
      sans-serif !important;
  }
}

:deep(.modify_tip_style) {
  .el-form-item__error {
    margin: 0 0 0 20px;
  }
}

:deep(.leader_post_wrap) {
  position: relative;
  .el-select {
    margin: 0 10px 0 0;
  }
  .el-form-item__error {
    margin: 0 0 0 20px;
  }
}

:deep(.post_manage_basic_info_wrap) {
  .el-form-item {
    .el-form-item__label {
      width: 120px !important;
    }
  }
  .textarea_wrap {
    .el-form-item__content {
      margin-left: 120px !important;
    }
  }
}

.leader_tips_wrap {
  position: absolute;
  width: 520px;
  color: #f56c6c;
  font-size: 12px;
}
</style>
