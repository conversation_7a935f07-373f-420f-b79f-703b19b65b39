<template>
  <div class="business_area_wrap bg_write">
    <div class="page_main_title">组织层级</div>
    <div class="page_section">
      <div class="oper_btn_wrap">
        <el-button type="primary" class="page_add_btn" @click="addItem">新增</el-button>
      </div>
      <div class="business_area_center clearfix">
        <div class="edu_info_header">
          <div class="item">层级名称</div>
          <div class="item">类型</div>
          <div class="item">状态</div>
          <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
          <div class="edu_info_item" v-for="(item, index) in orgLevelData" :key="item.id">
            <el-input class="item" v-model="item.orgLevelName" placeholder />
            <el-select class="item" v-model="item.dataType" placeholder="请选择" disabled>
              <el-option v-for="opt in typeOptions" :key="opt.codeName" :label="opt.codeName" :value="opt.dictCode" />
            </el-select>
            <el-select class="item" v-model="item.status" placeholder="请选择">
              <el-option v-for="opt in stateOptions" :key="opt.codeName" :label="opt.codeName" :value="opt.dictCode" />
            </el-select>
            <div class="item item_icon_wrap">
              <el-icon class="item_icon" @click="deleteItem(item.companyId, item.orgLevelCode, index)">
                <Delete />
              </el-icon>
            </div>
          </div>
          <div class="no_data_tip" v-if="orgLevelData.length == 0">暂无数据</div>
          <div class="align_center paddT_30" v-if="orgLevelData.length > 0">
            <el-button class="page_confirm_btn" type="primary" @click="updateOrg"> 确认 </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { getOrgDeptTree, getOrgLevelList, createOrgLevel, deleteOrgLevel } from '../../request/api'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

// 响应式状态
const rStatus = ref('')
const OrgTreeData = ref([])
const orgLevelData = ref([])

// 选项数据
const typeOptions = [
  {
    codeName: '系统内置',
    dictCode: 'B'
  },
  {
    codeName: '企业定制',
    dictCode: 'C'
  }
]

const stateOptions = [
  {
    codeName: '启用',
    dictCode: 'Y'
  },
  {
    codeName: '关闭',
    dictCode: 'N'
  }
]

// 方法
const getOrgLevelListFun = async () => {
  try {
    const res = await getOrgLevelList({
      companyId: companyId.value,
      rStatus: rStatus.value
    })

    if (res.data.length > 0) {
      orgLevelData.value = res.data.map(item => ({
        companyId: item.companyId,
        orgLevelCode: item.orgLevelCode,
        orgLevelName: item.orgLevelName,
        dataType: item.dataType,
        status: item.rstatus,
        sortNbr: item.sortNbr
      }))
    } else {
      orgLevelData.value = []
    }
  } catch (error) {
    console.error('获取组织层级列表失败:', error)
    ElMessage.error('获取组织层级列表失败')
  }
}

const addItem = () => {
  const addObj = {
    companyId: companyId.value,
    orgLevelCode: '',
    orgLevelName: '',
    dataType: 'C',
    status: '',
    sortNbr: ''
  }

  if (orgLevelData.value.length > 0) {
    const lastItem = orgLevelData.value[orgLevelData.value.length - 1]
    if (!lastItem.orgLevelName || !lastItem.status) {
      ElMessage.warning('请完善当前信息后新增！')
      return
    }
  }

  orgLevelData.value.push(addObj)
}

const updateOrg = () => {
  if (orgLevelData.value.length == 0) {
    ElMessage.warning('请新增后提交！')
    return
  }

  const lastItem = orgLevelData.value[orgLevelData.value.length - 1]
  if (!lastItem.orgLevelName || !lastItem.status) {
    ElMessage.warning('请完善信息后提交！')
    return
  }

  createOrgLevelFun()
}

const createOrgLevelFun = async () => {
  try {
    const res = await createOrgLevel(orgLevelData.value)
    if (res.code == 200) {
      await getOrgLevelListFun()
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('创建组织层级失败:', error)
    ElMessage.error('创建组织层级失败')
  }
}

const deleteItem = async (companyId, code, index) => {
  try {
    await ElMessageBox.confirm('确认删除?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 有code 数据已被创建  没code数据为此次新增
    if (code) {
      await deleteOrgLevelFun(companyId, code)
    } else {
      orgLevelData.value.splice(index, 1)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除组织层级失败:', error)
      ElMessage.error('删除组织层级失败')
    }
  }
}

const deleteOrgLevelFun = async (companyId, code) => {
  try {
    const res = await deleteOrgLevel({
      companyId,
      orgLevelCode: code
    })

    if (res.code == 200) {
      await getOrgLevelListFun()
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('删除组织层级失败:', error)
    ElMessage.error('删除组织层级失败')
  }
}

// 监听公司ID变化
watch(
  () => companyId.value,
  val => {
    if (val) {
      getOrgLevelListFun()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.business_area_wrap .edu_info_item .item {
  width: 30%;
}

.business_area_wrap .edu_info_item .item_icon_wrap {
  text-align: center;
  width: 8%;
}

.business_area_wrap .edu_info_item .item_icon_wrap .item_icon {
  font-size: 20px;
  color: var(--el-color-danger);
  cursor: pointer;
}

.edu_info_header .item {
  text-align: center;
  width: 30%;
}

.edu_info_header .item_icon_wrap {
  text-align: center;
  width: 8%;
}
</style>
