<template>
  <div class="detail_main">
    <div class="detail_main_aside">
      <div class="page_third_title">分析主题</div>
      <tabsLink :tabsData="tabsLinkData" :isVertical="true"></tabsLink>
    </div>
    <div class="detail_main_content">
      <div class="page_third_title">分析视图</div>
      <router-view :filterData="filterData"></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { queryOrgWithSubChildren, queryJobClassByOrg } from '../../../../request/api'
import tabsLink from '@/components/talent/tabsComps/tabsLink'

const route = useRoute()

const tabsLinkData = ref([
  {
    id: 'asdfsdfqteqa',
    name: '离职风险',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentRisk/quitRisk'
  },
  // {
  //     id: "asdqrefafasdfa",
  //     name: "离职影响",
  //     path:
  //         "/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentRisk/quitInfluence"
  // },
  // {
  //     id: "asasdfdqrefasffa",
  //     name: "离职可能性",
  //     path:
  //         "/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentRisk/quitPossibility"
  // },
  {
    id: 'asasdfdqrefa',
    name: '离职风险矩阵',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentRisk/quitRiskMatrix'
  }
])

const filterData = reactive({
  orgData: [],
  jobClass: []
})

const queryOrgWithSubChildrenFun = async enqId => {
  try {
    const res = await queryOrgWithSubChildren({ enqId })
    filterData.orgData = res.data
  } catch (error) {
    console.error('获取组织数据失败:', error)
  }
}

const queryJobClassByOrgFun = async () => {
  try {
    const res = await queryJobClassByOrg()
    filterData.jobClass = res.data
  } catch (error) {
    console.error('获取职务类型数据失败:', error)
  }
}

onMounted(() => {
  const enqId = route.query.enqId
  tabsLinkData.value.forEach(item => {
    item.path = `${item.path}?enqId=${enqId}`
  })
  queryOrgWithSubChildrenFun(enqId)
  queryJobClassByOrgFun()
})
</script>

<style lang="scss"></style>
