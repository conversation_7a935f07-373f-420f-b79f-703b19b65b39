<template>
  <div class="main-detail">
    <div class="aside">
      <div class="title">流程赋能显差</div>
      <div class="bar_chart">
        <div class="bar_chart_item" v-for="item in barList">
          <div class="chart_item_label">{{ item.name }}</div>
          <div class="chart_item_bar">
            <div class="chart_item_bar_content" :style="{ width: `${item.value}%`, background: '#53B8FF' }"></div>
            <div class="chart_item_bar_text" :style="{ left: `calc(${item.value}%)` }">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="main-content">
      <div class="page-title-line">流程端到端差距对管理的影响</div>
      <div class="border mb-[28px] text">
        若未实现端到端闭环，各部门数据孤立，预测仅依赖单一维度（如历史数据），忽略市场动态和客户意向变化，导致预测与实际需求脱节。混合预测方法缺失，过度依赖定量分析或经验判断，预测结果偏离市场真实需求。动态更新机制缺失，无法及时响应促销活动、新品上市等变化，预测目标僵化。误差分析机制缺失，偏差原因不明，预测模型长期失准，导致生产计划与市场需求错配，库存积压或订单交付延误，增加供应链运营成本，降低客户满意度与市场竞争力。
      </div>
      <div class="page-title-line">流程端到端差距对管理的影响路径</div>
      <div class="border mb-[28px] text">
        <div class="path">
          <div class="list" v-for="item in pathList">
            <div class="name">{{ item.name }}</div>
            <SvgIcon class="icon" width="14px" height="12px" name="arrow-right-icon"></SvgIcon>
          </div>
        </div>
      </div>
      <div class="page-title-line">最佳实践描述</div>
      <div class="border mb-[28px] text">
        流程始于跨部门数据整合，销售部门汇总历史销售数据、客户订单意向，市场部门提供行业动态和竞品信息，营销部门同步企业促销排期，形成多维度数据输入。采用
        “定量模型 + 定性判断”
        的混合预测方法，定量模型分析历史趋势和关联因素，定性判断结合销售团队经验修正，针对战略客户订单单独建模。通过需求管理系统实现预测数据动态更新，定期滚动修正预测目标，确保覆盖不同产品类型和市场区域。建立预测误差分析机制，对比实际订单与预测值，组织跨部门追溯偏差原因，持续优化预测模型和数据输入维度，提升预测准确性和供应链响应速度。
      </div>
      <div class="page-title-line">管理要点与管理方法</div>
      <div class="table-dom mb-[28px]">
        <simplenessTable :columns="columns" :data="methodList"></simplenessTable>
      </div>
      <div class="page-title-line">建议的关键任务</div>
      <div class="simpleness-table round-border task">
        <el-table ref="simplenessTableRef" :data="taskData">
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column v-for="col in taskColumns" v-bind="{ ...col }" :key="col.prop || col.label">
            <!-- <template #default="scope">
              <div class="row-list" v-if="col.prop == 'action' || col.prop == 'result'">
                <div class="list" v-for="list in scope.row[col.prop]">{{ list }}</div>
              </div>
              <div class="" v-else>{{ scope.row[col.prop] }}</div>
            </template> -->
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import simplenessTable from '@/components/table/simplenessTable.vue'
const router = useRouter()
const emits = defineEmits(['changeType'])
emits('changeType', {
  name: '流程赋能',
  id: 2,
  value: 54.6,
  changeType: -1
})
const barList = ref([
  { name: '流程端到端', value: 72.0 },
  { name: '输入输出文档', value: 71.5 },
  { name: '业务规则', value: 70.8 },
  { name: '业务KPI', value: 70.2 }
])

const setColor = score => {
  const scoreRanges = [
    '#B2FDFE',
    '#83FDFE',
    '#34EAFF',
    '#3ED4FF',
    '#22B5FA',
    '#2589EC',
    '#0D69C1',
    '#08549B',
    '#03396E',
    '#032B4D)'
  ]
  const index = parseInt(score / 10) == 10 ? 9 : parseInt(score / 10)
  return scoreRanges[index]
}
const pathList = ref([
  { name: '数据孤立' },
  { name: '单一维度预测' },
  { name: '忽略市场动态' },
  { name: '预测与需求脱节' },
  { name: '混合预测方法缺失' },
  { name: '动态更新机制缺失' },
  { name: '预测目标僵化' },
  { name: '误差分析机制缺失' },
  { name: '预测模型失准' },
  { name: '生产计划错配' },
  { name: '库存积压 / 交付延误' },
  { name: '交付延误→运营成本增加、客户满意度下降' }
])

const columns = ref([
  { prop: 'name', label: '要点类别', width: '100px' },
  { prop: 'value', label: '要点核心内容' },
  { prop: 'method', label: '管理方法' }
])
const methodList = ref([
  {
    name: '完整性',
    value:
      '需求计划对象覆盖供应链全场景相关要素，包括产品、市场、客户等维度，无遗漏且边界清晰，确保计划范围完整涵盖业务涉及的所有需求主体和场景。',
    method:
      '建立标准化的《需求计划对象清单模板》，明确产品维度（如家电品类、型号、能效等级）、市场维度（区域市场、渠道类型）、客户维度（B 端经销商、C 端消费者）等具体分类及定义标准；每月组织销售、市场、生产等跨部门需求评审会，对照清单检查计划对象覆盖情况，重点验证新品类、新渠道、新客户群体的纳入完整性；利用 ERP 系统日志自动记录需求对象的新增、删除操作，生成《完整性检查报告》，对遗漏场景触发红色预警并指定责任部门 48 小时内补充。'
  },
  {
    name: '目标一致性',
    value:
      '需求计划对象定义与企业战略目标、市场定位、客户需求高度匹配，确保计划方向与企业整体经营目标一致，避免出现对象范围与战略脱节的情况。',
    method:
      '制定《需求计划对象 - 战略目标对照表》，明确智能家电产品线、高端市场拓展、下沉市场渗透等战略目标对应的需求对象（如将 “县域经销商” 纳入客户对象）；年度战略会议同步需求计划对象框架，由 CEO 牵头确认关键对象的战略匹配度；每季度通过平衡计分卡评估对象定义与当期战略的契合度，例如当企业启动 “全渠道融合” 战略时，新增 “O2O 渠道客户” 作为独立需求对象，并调整其需求优先级。'
  },
  {
    name: '闭环验证',
    value:
      '通过历史数据对比、实际需求反馈等方式，验证需求计划对象定义的合理性和有效性，形成 “制定 - 执行 - 反馈 - 优化” 的闭环管理，确保对象设置符合市场实际需求。',
    method:
      '开发《需求计划对象效果评估模型》，自动对比计划对象覆盖范围内的实际销量、库存周转率与计划数据，计算对象定义导致的偏差率（如某类产品计划覆盖但实际销量为零则标记为无效对象）；每月召开闭环验证会议，用帕累托图分析 Top3 偏差案例（如因未纳入 “直播电商客户” 导致促销订单漏判）；建立对象定义优化流程，验证发现的问题需在 2 周内完成调整，如新增 “直播渠道专属客户” 对象并更新预测模型参数，跟踪改进后 3 个月的订单满足率变化。'
  },
  {
    name: '可追溯性',
    value:
      '对需求计划对象的制定依据、决策过程、变更记录等进行全程留痕，确保每个对象的定义和调整都能追溯到具体的业务背景、数据来源和责任主体，便于问题复盘和责任界定。',
    method:
      '部署需求计划对象管理系统，自动记录每次定义 / 变更的时间、操作人、修改内容（如将 “空调柜机” 从 “常规产品” 调整为 “旺季重点产品” 的原因）、审批意见（需供应链总监电子签名）；建立电子档案库按时间轴存储历史版本的对象定义文档，支持关键词检索（如查询 “2024 年促销活动” 相关的对象调整记录）；在 OA 流程中设置追溯性检查节点，要求任何对象调整必须关联具体业务事件（如新品上市通知、市场调研报告）或数据依据（如连续两季某区域销量增长超 30%）。'
  },
  {
    name: '反馈改进',
    value:
      '建立常态化的需求计划对象反馈机制，收集市场一线、客户服务、生产制造等部门的实际操作反馈，针对对象定义中存在的模糊点、遗漏项或不合理之处，定期进行优化改进，持续提升计划对象的精准度。',
    method:
      '设立跨部门反馈收集小组（包含区域销售经理、客服主管、生产计划员），每月通过问卷星收集反馈，重点关注 “对象定义是否清晰指导执行”“是否存在未覆盖的特殊场景” 等问题；建立《需求对象优化优先级矩阵》，按影响程度（如导致交付延迟的对象遗漏为高优先级）排序处理，例如优先解决因 “工程客户” 对象定义模糊导致的批量订单漏排问题；每季度发布《需求对象改进报告》，公示改进成果（如新增 “商用空调工程客户” 对象后订单响应速度提升 40%）并明确下阶段优化方向。'
  }
])

const taskColumns = ref([
  { prop: 'name', label: '组织名称', width: '80px' },
  { prop: 'value', label: '举措', width: '80px' },
  { prop: 'strategy', label: '关联策略', width: '100px' },
  { prop: 'action', label: '关键行动' },
  { prop: 'person', label: '责任人', width: '100px' },
  { prop: 'result', label: '输出成果', width: '100px' },
  { prop: 'priority', label: '优先级', width: '50px' },
  { prop: 'status', label: '进度', width: '45px' }
])

const taskData = ref([
  {
    name: '供应链计划部',
    value: '建立需求计划对象清单',
    strategy: '全场景覆盖策略',
    action:
      '①制定《需求计划对象清单模板》，明确产品（如空调、冰箱等品类及型号）、市场（区域市场、渠道类型）、客户（B 端经销商、C 端消费者）等维度的具体分类及定义标准，确保无遗漏且边界清晰；②每月组织销售、市场、生产等跨部门需求评审会，对照清单检查计划对象完整性，重点验证新品类、新渠道、新客户群体的纳入情况，形成评审记录；③利用 ERP 系统日志自动记录需求对象的新增、删除操作，生成《完整性检查报告》，对遗漏场景触发红色预警，并指定责任部门在 48 小时内补充完善。',
    person: '张建国（供应链计划部）',
    result: '《需求计划对象完整性清单》',
    priority: '极高',
    status: '进行中'
  },
  {
    name: '供应链计划部',
    value: '战略匹配度提升',
    strategy: '战略对齐策略',
    action:
      '①制定《需求计划对象 - 战略目标对照表》，明确智能家电产品线、高端市场拓展等战略目标对应的需求对象，如将 “县域经销商” 纳入客户对象；②在年度战略会议上同步需求计划对象框架，由 CEO 牵头组织高层评审，确保对象定义与企业战略目标、市场定位高度匹配；③每季度通过平衡计分卡评估对象定义与当期战略的契合度，根据战略调整动态优化，如企业启动 “全渠道融合” 战略时，新增 “O2O 渠道客户” 作为独立需求对象。',
    person: '张建国（供应链计划部）',
    result: '《战略匹配度评估报告》',
    priority: '较高',
    status: '已完成'
  },
  {
    name: '供应链计划部',
    value: '闭环验证机制建设',
    strategy: '持续改进策略',
    action:
      '①开发《需求计划对象效果评估模型》，自动对比计划对象覆盖范围内的实际销量、库存周转率与计划数据，计算对象定义导致的偏差率，标记无效或低效对象；②每月召开闭环验证会议，运用帕累托图分析 Top3 偏差案例，如因未纳入 “直播电商客户” 导致促销订单漏判等问题，形成改进提案；③针对验证发现的问题，启动对象定义优化流程，在 2 周内完成调整并更新预测模型参数，跟踪改进后 3 个月的订单满足率、库存周转率等指标变化。',
    person: '张建国（供应链计划部）',
    result: '《闭环验证改进方案》',
    priority: '高',
    status: '未开始'
  },
  {
    name: '供应链计划部',
    value: '追溯体系建设',
    strategy: '过程管控策略',
    action:
      '①部署需求计划对象管理系统，自动记录每次定义 / 变更的时间、操作人、修改内容及审批意见，如将 “空调柜机” 调整为 “旺季重点产品” 的原因及供应链总监审批记录；②建立电子档案库存储历史版本的对象定义文档，支持按时间轴或关键词检索，便于复盘历史决策；③在 OA 流程中设置追溯性检查节点，要求任何对象调整必须关联具体业务事件（如新品上市通知）或数据依据（如连续两季某区域销量增长超 30%），否则无法提交审批。',
    person: '张建国（供应链计划部）',
    result: '《追溯体系操作手册》',
    priority: '中',
    status: '进行中'
  },
  {
    name: '供应链计划部',
    value: '反馈机制建设',
    strategy: '全员参与策略',
    action:
      '①设立跨部门反馈收集小组，包含区域销售经理、客服主管、生产计划员等，每月通过问卷星、座谈会等形式收集一线反馈，重点关注对象定义的模糊点、遗漏项；②建立《需求对象优化优先级矩阵》，按影响程度排序处理反馈问题，如优先解决因 “工程客户” 对象定义模糊导致的批量订单漏排问题；③每季度发布《需求对象改进报告》，公示改进成果并明确下阶段优化方向，如新增 “商用空调工程客户” 对象后订单响应速度提升 40%。',
    person: '张建国（供应链计划部）',
    result: '《反馈改进记录手册》',
    priority: '中',
    status: '进行中'
  }
])
</script>

<style lang="scss" scoped>
.main-detail {
  display: flex;
  align-items: flex-start;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
  padding: 20px;
  .aside {
    flex: 0 0 240px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    margin-right: 20px;
    padding: 10px;
    .title {
      line-height: 30px;
      background: #e3effa;
      border-radius: 8px 8px 8px 8px;
      text-align: center;
      margin-bottom: 20px;
      font-size: 12px;
      color: #333333;
    }
  }
}
.main-content {
  .border {
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 20px;
  }
  .text {
    font-size: 14px;
    color: #666666;
  }
  .path {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
    .list {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      &:last-of-type {
        .icon {
          display: none;
        }
      }
      .name {
        line-height: 34px;
        background: #e4f1ff;
        border-radius: 115px 115px 115px 115px;
        font-size: 14px;
        color: #333333;
        margin-right: 5px;
        margin-left: 5px;
        padding: 0 10px;
        font-size: 13px;
        color: #48a5ff;
      }
    }
  }
}

.bar_chart {
  .bar_chart_item {
    display: flex;
    align-items: stretch;
    min-height: 24px;
    transition: background-color 0.3s ease;
    &:hover {
      background-color: #efefef;
    }

    .chart_item_label {
      position: relative;
      flex: 0 0 80px;
      font-size: 12px;
      color: #3d3d3d;
      border-right: 1px solid #ddd;
      text-align: right;
      padding: 3px 3px 3px 0;
      z-index: 1;
      line-height: 32px;
    }

    .chart_item_bar {
      position: relative;
      width: calc(100% - 100px);

      .chart_item_bar_content {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 14px;
        // margin-left: -2px;
        z-index: 0;
        // background-color: #2589EC;
      }

      .chart_item_bar_text {
        position: absolute;
        top: -4px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        white-space: nowrap;
      }
    }
  }
}

.simpleness-table {
  // 设置table边框圆角
  &.task {
    :deep(.el-table) {
      td.el-table__cell {
        border-right: 1px solid #c6dbf3;
        &:last-of-type {
          border-right: none;
        }
      }
      th.el-table__cell {
        border-right: 1px solid #c6dbf3;
        &:last-of-type {
          border-right: none;
        }
      }
    }
  }
  :deep(.el-table) {
    --el-font-size-base: 14px;
    --el-table-header-text-color: #93abcb;
    --el-table-border-color: #c6dbf3;
    --el-table-tr-bg-color: transparent;
    --el-table-header-bg-color: transparent;
    background-color: transparent;
    .cell {
      padding: 0 4px;
    }
  }
  &.round-border {
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #c6dbf3;
    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        // 隐藏table底边框
        background-color: transparent;
      }
      tr:last-of-type {
        td {
          // 隐藏table底边框
          border-bottom: none;
        }
      }
    }
  }
}
.row-list {
  height: 210px;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  margin: 0 -4px;
  .list {
    width: 100%;
    display: flex;
    align-items: center;
    height: 70px;
    border-bottom: 1px solid #c6dbf3;
    padding: 0 4px;
    &:last-of-type {
      border-bottom: none;
    }
  }
}
</style>
