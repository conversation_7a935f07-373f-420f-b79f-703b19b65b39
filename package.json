{"name": "hx-agent", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite", "dev:test": "vite --mode test", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "clearsvgcolor": "node ./lib/clearsvgcolor.js", "preview": "vite preview", "lint": "eslint . --ext .js,.vue", "lint:fix": "eslint . --ext .js,.vue --fix", "prettier:check": "prettier --check .", "prettier:write": "prettier --write .", "prepare": "husky"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-vue-jsx": "^4.2.0", "axios": "^1.8.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-china-area-data": "^6.1.0", "markdown-it": "^14.1.0", "markdown-it-container": "^4.0.0", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.4.1", "qs": "^6.14.0", "sass": "^1.85.1", "sortablejs": "^1.15.6", "unplugin-icons": "^22.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuedraggable": "^2.24.3", "vuex": "^4.1.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@iconify-json/ep": "^1.2.2", "@vitejs/plugin-vue": "^5.2.1", "element-plus": "^2.9.6", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^10.0.0", "fast-glob": "^3.3.3", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.17", "terser": "^5.39.0", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.0", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^10.1.1"}, "lint-staged": {"*.{scss,less,css,html}": ["prettier --write"], "*.{js,vue,jsx,tsx,ts}": ["prettier --write", "eslint --cache --fix"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}