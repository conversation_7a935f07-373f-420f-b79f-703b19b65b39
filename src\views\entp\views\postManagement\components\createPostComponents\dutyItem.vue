<template>
    <div>
        <div class="edu_info_item" v-for="(item, index) in itemData" :key="item.id">
            <el-input class="item desc" v-model="item.desc" placeholder="请输入职责描述"></el-input>
            <el-select class="item" v-model="item.proportion" placeholder="请选择时间占比">
                <el-option label="0%" value="0"></el-option>
                <el-option label="10%" value="10"></el-option>
                <el-option label="20%" value="20"></el-option>
                <el-option label="30%" value="30"></el-option>
            </el-select>
            <div class="item item_icon_wrap">
                <i class="item_icon el-icon-delete" @click="deleteItem(item,index)"></i>
            </div>
        </div>
    </div>
</template>
 
<script>
export default {
    name: "dutyItem",
    props: {
        itemData: {
            type: Array,
            default: function() {
                return [
                    {
                        id: "1",
                        desc: "",
                        proportion: ""
                    }
                ];
            }
        }
    },
    data() {
        return {
            name: "",
            type: "",
            state: "",
            sortIndex: ""
        };
    },
    methods: {
        deleteItem(index) {
            this.$emit("deleteItem", index);
        }
    }
};
</script>
 
<style scoped lang="scss">
.edu_info_item {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 0 16px;
    .item {
        // flex: 1;
        width: 20%;
        padding: 0 4px;
        line-height: 40px;
        &.desc {
            width: 75%;
        }
    }
    .item_icon_wrap {
        text-align: center;
        width: 5%;
        .item_icon {
            font-size: 20px;
            color: #0099fd;
            cursor: pointer;
        }
    }
}
</style>