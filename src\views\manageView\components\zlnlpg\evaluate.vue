<script setup>
defineOptions({ name: 'evaluate' })
const route = useRoute()
const processId = ref(route.query.processId || 'example')
const processList = reactive(['example', 'model', 'eval', 'evalSummary'])
const activeProcessIndex = ref(0)
const processMap = ref({
  model: {
    comp: shallowRef(defineAsyncComponent(() => import('./evaluateComp/modelList.vue')))
  },
  example: {
    comp: shallowRef(defineAsyncComponent(() => import('./evaluateComp/example.vue')))
  },
  eval: {
    comp: shallowRef(defineAsyncComponent(() => import('./evaluateComp/eval.vue')))
  },
  evalSummary: {
    comp: shallowRef(defineAsyncComponent(() => import('./evaluateComp/evalSummary.vue')))
  },
  progress: {
    comp: shallowRef(defineAsyncComponent(() => import('./evaluateComp/progress.vue')))
  }
})
const next = () => {
  activeProcessIndex.value++
  processId.value = processList[activeProcessIndex.value]
}

const router = useRouter()

watch(
  () => route.query,
  val => {
    console.log('val', val)

    processId.value = val.processId || 'example'
    activeProcessIndex.value = processList.indexOf(val.processId || 'example')
  },
  { immediate: true }
)
const changeProcess = id => {
  router.push({ path: route.path, query: Object.assign({}, route.query, { processId: id }) })
  activeProcessIndex.value = processList.indexOf(id)
  processId.value = id
}
</script>
<template>
  <div class="eval-page">
    <div class="head-title">您的核心能力测评产品与服务如下：</div>
    <div class="eval-info">
      <div class="name">战略绩效闭环运营能力测评（20250415）</div>
      <div class="process">2人正在测评，进行中（158/251）</div>
      <div class="num">可用次数：<b>1</b>次</div>
      <div class="btn-wrap">
        <div class="btn" @click="changeProcess('eval')">开始测评</div>
        <div class="btn" @click="changeProcess('progress')">测评进度</div>
        <div class="btn disable">查看报告</div>
      </div>
    </div>
    <component :is="processMap[processId].comp" @next="next" />
  </div>
</template>
<style lang="scss" scoped>
.eval-page {
  .head-title {
    font-size: 16px;
    color: #888888;
    line-height: 20px;
    margin-bottom: 16px;
  }
  .eval-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 26px 20px;
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    margin-bottom: 20px;
    .name {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
    }
    .process {
      font-size: 14px;
      color: #898989;
    }
    .num {
      font-size: 14px;
      color: #898989;
      b {
        color: #40a0ff;
      }
    }
    .btn-wrap {
      display: flex;
      align-items: center;
      gap: 10px;
      .btn {
        width: 92px;
        line-height: 30px;
        background: #40a0ff;
        border-radius: 3px 3px 3px 3px;
        text-align: center;
        color: #fff;
        cursor: pointer;
        &.disable {
          background: #d8d8d8;
          color: #898989;
          cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
  :deep(.page-btn-wrap) {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
    .btn {
      min-width: 152px;
      line-height: 30px;
      text-align: center;
      color: #fff;
      background: #40a0ff;
      border-radius: 3px 3px 3px 3px;
      font-size: 12px;
      padding: 0 10px;
      cursor: pointer;
      &.border {
        background-color: transparent;
        border: 1px solid #40a0ff;
        color: #40a0ff;
      }
    }
  }
}
</style>
