<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'
import Sortable from 'sortablejs'

defineOptions({ name: 'eval' })

const activeModelIndex = ref(0)
const modelList = ref([
  {
    name: '目标与信息源体系构建',
    completed: 3,
    total: 8
  },
  {
    name: '‌数据采集与清洗校准',
    completed: 0,
    total: 2
  },
  {
    name: '多维分析框架搭建',
    completed: 0,
    total: 5
  },
  {
    name: '预测模型构建与验证',
    completed: 0,
    total: 5
  },
  {
    name: '战略建议制定与执行跟踪',
    completed: 0,
    total: 5
  },
  {
    name: '我的改善清单'
  }
])

const columns = ref([
  {
    label: '序号',
    type: 'index',
    width: 50
  },
  {
    label: '评估项目类型',
    prop: 'type',
    width: 120
  },
  {
    label: '参考标准',
    prop: 'reference'
  }
])
const tableData = ref([
  {
    type: '流程端到端闭环',
    reference: '制定了包含数据采集→规则制定→审批发布→执行监控全环节流程，每季度闭环验证标准适用性',
    flag: 'Y'
  },
  {
    type: '输出文档',
    reference: '输出了《大客户评分模型说明书》《分级白皮书》等文档，需符合分析报告类"数据可靠性"标准',
    flag: 'N'
  },
  {
    type: '业务规则',
    reference: '定义了触发条件：当TOP10客户流失率>15%时启动规则修订，决策逻辑包含收入贡献、战略协同等权重算法',
    flag: 'Y'
  },
  {
    type: '业务KPI',
    reference: '设定了"大客户识别准确率≥90%""标准更新及时率100%"等指标',
    flag: 'N'
  },
  {
    type: '组织设置',
    reference: '设立了跨部门战略客户委员会，明确例会频次（每月1次）和决策权限',
    flag: 'Y'
  },
  {
    type: '岗位角色职责',
    reference: '定义了数据分析师负责模型维护、销售总监负责规则审批等职责',
    flag: 'N'
  },
  {
    type: '岗位协同RACI',
    reference: '设立了RACI矩阵，R(执行)=区域销售；A(审批)=委员会；C(咨询)=财务；I(知会)=客服',
    flag: 'Y'
  },
  {
    type: '组织岗位KPI',
    reference: '委员会成员考核“标准优化贡献度"，权重占绩效考核20%',
    flag: 'N'
  },
  {
    type: '人员动力',
    reference: '将客户分级准确率与销售提成系数挂钩（误差率每降1%提成+0.5%）',
    flag: 'Y'
  },
  {
    type: '人员能力要求',
    reference: '要求客户经理掌握RFM模型应用能力（需通过L3级认证）',
    flag: 'N'
  },
  {
    type: '人员能力评估',
    reference: '每半年开展模型应用实战考核，得分<80分者需复训',
    flag: 'Y'
  },
  {
    type: '能力培训',
    reference: '开发《大客户识别工作坊》课程，包含沙盘模拟和案例库',
    flag: 'Y'
  },
  {
    type: '系统赋能',
    reference: 'CRM系统需支持自定义评分规则配置和自动分级',
    flag: 'N'
  },
  {
    type: '数据治理',
    reference: '主数据字段完整率≥95%，历史交易数据覆盖近3年',
    flag: 'Y'
  },
  {
    type: '系统集成',
    reference: 'ERP与CRM系统客户数据实时同步，延迟≤5分钟',
    flag: 'N'
  },
  {
    type: '系统改善及规划',
    reference: '每年度评估系统对新型客户分类分级与其他大客户管理的支持度'
  }
])

const changeFlag = (row, flag) => {
  // tableData.value[data.$index].flag = flag
  if (flag == row.flag) {
    return
  }
  row.flag = flag
  let { type } = row
  let index = dnaList.value.findIndex(item => item.name == type)
  if (index > -1) {
    dnaList.value.splice(index, 1)
  } else {
    dnaList.value.push({ name: type })
  }
}

const dnaColumns = ref([
  {
    label: '序号',
    type: 'index',
    width: 50
  },
  {
    label: '评估维度',
    prop: 'dim',
    width: 120
  },
  {
    label: '参考标准',
    prop: 'reference'
  }
])
const dnaTableData = ref([
  {
    dim: '闭环验证',
    reference: '每个环节设置输入/输出验收标准（如规则修订需委员会审批文件），且触发验证动作（季度复盘会议）',
    flag: 'Y'
  },
  {
    dim: '反馈改进',
    reference: '是否建立季度评审机制，根据执行结果优化流程（如客户投诉率>5%时触发流程重构）',
    flag: 'N'
  },
  {
    dim: '可追溯性',
    reference: '流程关键决策（如阈值调整）可追溯至原始数据（如市场分析报告），变更记录保留≥2年',
    flag: 'N'
  },
  {
    dim: '流程完整性',
    reference: '是否覆盖数据采集→规则制定→审批发布→执行监控全环节',
    flag: 'Y'
  },
  {
    dim: '目标一致性',
    reference: '流程设计目标与战略规划（如大客户营收占比提升）直接关联，每个环节设置目标对齐验证点',
    flag: 'N'
  },
  {
    dim: '完整性',
    reference: '流程覆盖数据采集→规则制定→审批发布→执行监控全环节，明确标注各环节输入输出标准，且无断点或盲区',
    flag: 'Y'
  }
])
const dnaIndex = ref(0)
const dnaList = ref([
  {
    name: '流程端到端闭环'
  }
])
const changeDnaFlag = (row, flag) => {
  if (flag == row.flag) {
    return
  }
  row.flag = flag
}
dnaList.value = tableData.value
  .filter(item => item.flag == 'Y')
  .map(item => {
    return {
      name: item.type
    }
  })

const emits = defineEmits(['next'])
const nextItem = () => {
  if (activeModelIndex.value == modelList.value.length - 1) {
    emits('next')
    return
  }
  activeModelIndex.value++
}

// 改善清单
const list = [
  {
    id: 'dkhgl',
    name: '建立标签体系'
  },
  {
    id: 'qdgl',
    name: '专项小组访谈'
  },
  {
    id: 'xsyy',
    name: '设立考核指标'
  },
  {
    id: 'sjgl',
    name: '建立标签体系'
  },
  {
    id: 'xstdgl',
    name: '设计问卷模板'
  }
]
const sortList = ref([
  {
    id: 'zycd',
    title: '重要程度排序',
    tip: '请基于项目价值、影响、必要性、长期意义、对管理提升的贡献等方面，在您选择的项目中进行排序（长按上下拖动可更改排序）',
    list: [
      {
        id: 'qdfj',
        name: '渠道分级与策略制定（5）'
      },
      {
        id: 'hytx',
        name: '会员体系与忠诚度计划（4）'
      },
      {
        id: 'szh',
        name: '数字化渠道赋能（3）'
      },
      {
        id: 'bdkh',
        name: 'B端客户（经销商）赋能（2）'
      },
      {
        id: 'ppdw',
        name: '品牌定位与策略制定（2）'
      }
    ]
  },
  {
    id: 'jjcd',
    title: '紧急程度排序',
    tip: '请重点从时间维度、时效性等方面，做出紧急度的排序，即：在有限时间内想做出改善的顺序 （长按上下拖动可更改排序）',
    list: JSON.parse(JSON.stringify(list))
  },
  {
    id: 'sswbd',
    title: '实施完备度排序',
    tip: '请从计划制定、资源准备、风险应对等方面，在您选择的项目中进行排序，即：开展事项基础比较成熟 （长按上下拖动可更改排序）',
    list: JSON.parse(JSON.stringify(list))
  },
  {
    id: 'ssgzl',
    title: '实施工作量排序',
    tip: '请从从人员投入、时间投入、成本投入、复杂程度等方面考量，在您选择的项目中进行排序 （长按上下拖动可更改排序）',
    list: JSON.parse(JSON.stringify(list))
  }
])

const initSortable = id => {
  const el = document.getElementById(id)
  // 创建拖拽实例
  Sortable.create(el, {
    animation: 150,
    handle: '.sort-list',
    dragClass: 'sort-active',
    chosenClass: 'sort-active',
    dataIdAttr: 'id',
    // 结束拖动事件
    onEnd: p => {
      let { newIndex, oldIndex } = p
      let index = p.target.id.split('-')[1]
      console.log(index)

      //拖动后的操作
      const curr = JSON.parse(JSON.stringify(sortList.value[index].list[oldIndex]))
      nextTick(() => {
        sortList.value[index].list.splice(oldIndex, 1)
        sortList.value[index].list.splice(newIndex, 0, curr)
      })
    }
  })
}
onMounted(() => {
  // 初始化拖拽实例
  for (let i = 0; i < sortList.value.length; i++) {
    initSortable(sortList.value[i].id + '-' + i)
  }
})
</script>
<template>
  <div class="eval-answer-content">
    <div class="module-name">
      <div class="back-icon">
        <SvgIcon name="icon-back"></SvgIcon>
        返回
      </div>
      <div class="name">组织协同与变革管理 （8/25 ）</div>
    </div>
    <div class="tip"></div>
    <div class="model-list-wrap">
      <div
        class="model-list"
        :class="{ active: activeModelIndex == index, noCount: !model.total }"
        v-for="(model, index) in modelList"
        :key="model.name"
        @click="activeModelIndex = index"
      >
        {{ model.name }}
        <div class="process" v-if="model.total">（{{ model.completed }}/{{ model.total }}）</div>
      </div>
    </div>
    <div class="eval-answer-main" v-show="activeModelIndex < 5">
      <div class="comp-wrap">
        <div class="comp-item active">
          <div class="comp-name">明确大客户评价标准 （3/3）</div>
          <div class="comp-status">已完成</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">构建多层级信息采集网络（0/2）</div>
          <div class="comp-status">未开始</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">制定数据质量管理规则（0/2）</div>
          <div class="comp-status">未开始</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">启动信息采集系统（0/1）</div>
          <div class="comp-status">未开始</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">我的直观判断</div>
          <div class="comp-status">未开始</div>
        </div>
      </div>
      <div class="eval-answer">
        <div class="title">能力描述（明确大客户评价标准）</div>
        <div class="desc">
          明确大客户评价标准需建立数据驱动的量化评估体系：首先通过ERP/CRM系统集成提取交易频次、订单金额、利润率等核心数据（信息赋能-数据治理），结合行业对标制定多维度评分卡（流程赋能-业务规则）。由战略客户委员会（组织赋能-组织设置）牵头，市场、销售、财务组成专项组（岗位协同RACI），按季度更新标准并生成《大客户分级白皮书》（流程赋能-输出文档）。系统自动计算客户等级并推送至相关岗位（信息赋能-系统集成），同时将分级准确率纳入销售团队KPI（流程赋能-业务KPI）。每年开展客户经理能力认证（人岗赋能-能力培训），确保规则执行一致性。
        </div>
        <div class="eval-amswer-box">
          <div class="eval-answer-title">1、针对上述能力，请开展详细评估</div>
          <div class="eval-answer-table">
            <SimplenessTable :roundBorder="false" :columns="columns" :data="tableData">
              <template v-slot:oper>
                <el-table-column prop="oper" label="有无判断" width="130">
                  <template v-slot="scope">
                    <div class="answer-oper">
                      <div class="btn" @click="changeFlag(scope.row, 'N')" :class="{ active: scope.row.flag == 'N' }">
                        无
                      </div>
                      <div class="btn" @click="changeFlag(scope.row, 'Y')" :class="{ active: scope.row.flag == 'Y' }">
                        有
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </template>
            </SimplenessTable>
          </div>
          <div class="dna-wrap">
            <div class="eval-amswer-box">
              <div class="page-title-line">DNA级诊断</div>
              <div class="eval-answer-item">
                <div
                  class="item"
                  :class="{ active: dnaIndex == index }"
                  v-for="(list, index) in dnaList"
                  :key="list.name"
                >
                  {{ list.name }}
                </div>
              </div>
              <div class="eval-answer-table">
                <SimplenessTable :roundBorder="false" :columns="dnaColumns" :data="dnaTableData">
                  <template v-slot:oper>
                    <el-table-column prop="oper" label="有无判断" width="130">
                      <template v-slot="scope">
                        <div class="answer-oper">
                          <div
                            class="btn"
                            @click="changeDnaFlag(scope.row, 'N')"
                            :class="{ active: scope.row.flag == 'N' }"
                          >
                            无
                          </div>
                          <div
                            class="btn"
                            @click="changeDnaFlag(scope.row, 'Y')"
                            :class="{ active: scope.row.flag == 'Y' }"
                          >
                            有
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                </SimplenessTable>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sort-box" v-show="activeModelIndex == 5">
      <div class="sort-item" v-for="(item, index) in sortList" :key="item.id">
        <div class="sort-title">{{ item.title }}</div>
        <div class="sort-tip">{{ item.tip }}</div>
        <div class="sort-list" :id="item.id + '-' + index">
          <div class="list sort-list" v-for="(list, index) in item.list" :key="list.id">
            <span class="index">{{ index + 1 }}</span>
            <span class="name">{{ list.name }}</span>
            <SvgIcon class="icon" name="drag-icon" />
          </div>
        </div>
      </div>
    </div>
    <div class="page-btn-wrap">
      <div class="btn" @click="nextItem">下一项</div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.eval-answer-content {
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
  padding: 22px 0;
  .module-name {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    color: #53a9f9;
    line-height: 16px;
    margin-bottom: 10px;
    padding-left: 20px;
    .back-icon {
      font-weight: 400;
      font-size: 14px;
      color: #888888;
      margin-right: 10px;
    }
  }

  .model-list-wrap {
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    gap: 40px;
    border-bottom: 1px solid #d8d8d8;
    padding-left: 20px;
    .model-list {
      padding: 19px 20px 5px;
      border-bottom: 3px solid transparent;
      margin-bottom: -1px;
      font-size: 14px;
      color: #3d3d3d;
      cursor: pointer;
      text-align: center;
      &.active {
        font-weight: 600;
        color: #40a0ff;
        border-bottom-color: #40a0ff;
      }
      &.noCount {
        line-height: 50px;
      }
      .process {
        margin-top: 8px;
      }
    }
  }
  .eval-answer-main {
    display: flex;
    align-items: flex-start;
    .comp-wrap {
      flex: 0 0 266px;
      border-right: 1px solid #d8d8d8;
      margin-right: 20px;
      .comp-item {
        border-bottom: 1px solid #d8d8d8;
        border-right: 3px solid transparent;
        padding: 10px 0 10px 20px;
        color: #333;
        cursor: pointer;
        &.active {
          color: #53a9f9;
          border-right-color: #40a0ff;
          background: linear-gradient(-90deg, rgba(64, 160, 255, 0.3) 0%, rgba(64, 160, 255, 0) 100%);
          .comp-status {
            color: inherit;
          }
        }
        .comp-name {
          font-size: 16px;
          margin-bottom: 10px;
        }
        .comp-status {
          color: #888;
          font-size: 16px;
        }
      }
    }
    .eval-answer {
      padding-top: 20px;
      padding-right: 20px;
      .title {
        font-weight: 600;
        font-size: 16px;
        color: #3d3d3d;
        line-height: 16px;
        margin-bottom: 12px;
      }
      .desc {
        font-size: 14px;
        color: #666666;
        line-height: 24px;
        padding-bottom: 20px;
        border-bottom: 1px dashed #e7e7e7;
        margin-bottom: 20px;
      }
      .eval-amswer-box {
        .eval-answer-title {
          font-weight: 600;
          font-size: 16px;
          color: #3d3d3d;
          line-height: 16px;
          margin-bottom: 20px;
        }
        .eval-answer-table {
          :deep(.el-table th.el-table__cell) {
            background: #e8eff7;
            color: #3d3d3d;
          }
        }
        .page-title-line {
          margin-top: 20px;
        }
        .eval-answer-item {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          line-height: 42px;
          background: #f0f0f0;
          font-size: 14px;
          margin-bottom: 10px;
          .item {
            padding: 0 10px;
            &.active {
              background: #40a0ff;
              color: #fff;
            }
          }
        }
      }
      .answer-oper {
        display: flex;
        gap: 10px;
        .btn {
          width: 50px;
          line-height: 24px;
          background: #d8d8d8;
          text-align: center;
          border-radius: 3px 3px 3px 3px;
          font-size: 12px;
          color: #898989;
          cursor: pointer;
          &.active {
            background: #40a0ff;
            color: #fff;
          }
        }
      }
    }
  }
}

.sort-box {
  display: flex;
  align-items: stretch;
  justify-content: flex-start;
  gap: 20px;
  padding: 20px;
  .sort-item {
    flex: 1;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 20px;
    .sort-title {
      font-weight: 600;
      font-size: 16px;
      color: #3d3d3d;
      line-height: 16px;
      margin-bottom: 10px;
    }
    .sort-tip {
      font-size: 14px;
      color: #666666;
      line-height: 21px;
      margin-bottom: 20px;
    }
    .sort-list {
      background: #ffffff;
      box-shadow: 0px 0px 6px 0px rgba(190, 201, 209, 0.3);
      border-radius: 4px 4px 4px 4px;
      font-size: 14px;
      color: #333333;
      .list {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        line-height: 40px;
        padding: 0 10px;
        border-bottom: 1px solid #eaeaea;
        font-size: 16px;
        cursor: move;
        &:last-of-type {
          border-bottom: none;
        }
        .index {
          font-weight: 600;
          font-size: 14px;
          color: #53b8ff;
          margin-right: 8px;
        }
        .name {
        }
        .icon {
          margin-left: auto;
        }
      }
    }
  }
}
</style>
