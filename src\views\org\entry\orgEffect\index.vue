<script setup>
import SectionTab from '../../components/sectionTab.vue'
import seeOrgEffect from './seeOrgEffect.vue'
import talentReview from './talentReview.vue'
import talentAssessment from './talentAssessment.vue'
const router = useRouter()
const route = useRoute()
const tabContentList = ref([seeOrgEffect, talentReview, talentAssessment])
const sectionTabCheckSign = ref(1)
const sectionTabList = ref([
  {
    name: '了解组织效能',
    code: 1
  },
  {
    name: '了解人才盘点',
    code: 2
  },
  {
    name: '了解人才测评',
    code: 3
  }
])

const checkSecTab = c => {
  sectionTabCheckSign.value = c
}
</script>
<template>
  <div class="orgEffect_wrap">
    <SectionTab
      :sectionTabList="sectionTabList"
      :sectionTabCheckSign="sectionTabCheckSign"
      @checkSecTab="checkSecTab"
    ></SectionTab>
    <div class="content-mian">
      <component :is="tabContentList[sectionTabCheckSign - 1]" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
</style>
