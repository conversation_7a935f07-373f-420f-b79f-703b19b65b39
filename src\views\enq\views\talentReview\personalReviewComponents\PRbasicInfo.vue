<template>
  <div>
    <div class="from_wrap clearfix">
      <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="130px" class="demo-ruleForm clearfix">
        <div class="basic_info marginT_16">
          <div class="page_second_title">基本信息</div>
          <div class="form_wrap marginT_8">
            <el-form-item label="姓名" prop="userName">
              <el-input v-model="ruleForm.userName" />
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-select v-model="ruleForm.gender" placeholder="请选择性别">
                <el-option
                  v-for="item in genderOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker
                v-model="ruleForm.birthday"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                type="date"
                placeholder="选择日期"
              />
            </el-form-item>
            <el-form-item label="籍贯" prop="nativePlace">
              <el-cascader
                :options="cityInfo"
                v-model="ruleForm.nativePlace"
                :props="{ expandTrigger: 'hover', checkStrictly: true }"
                :clearable="true"
                :filterable="true"
                @change="handleChange($event, 'nativePlace')"
              />
            </el-form-item>
            <el-form-item label="户籍所在地" prop="homePlace">
              <el-cascader
                :options="cityInfo"
                v-model="ruleForm.homePlace"
                :props="{ expandTrigger: 'hover', checkStrictly: true }"
                :clearable="true"
                :filterable="true"
                @change="handleChange($event, 'homePlace')"
              />
            </el-form-item>
            <el-form-item label="现居住地" prop="residencePlace">
              <el-cascader
                :options="cityInfo"
                v-model="ruleForm.residencePlace"
                :props="{ expandTrigger: 'hover', checkStrictly: true }"
                :clearable="true"
                :filterable="true"
                @change="handleChange($event, 'residencePlace')"
              />
            </el-form-item>
            <el-form-item label="民族" prop="nationalityCode">
              <el-select v-model="ruleForm.nationalityCode" placeholder="请选择民族">
                <el-option
                  v-for="item in nationalityOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="婚姻状况" prop="maritalStatus">
              <el-select v-model="ruleForm.maritalStatus" placeholder="请选择婚姻状况">
                <el-option
                  v-for="item in maritalOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="入司时间" prop="currentEmpDate">
              <el-date-picker
                v-model="ruleForm.currentEmpDate"
                value-format="YYYY-MM-DD"
                type="date"
                placeholder="选择日期"
              />
            </el-form-item>
            <el-form-item label="当前岗位" prop="postName">
              <el-input v-model="ruleForm.postName" />
            </el-form-item>
            <el-form-item label="当前岗位年限" prop="currentPostAge">
              <el-select v-model="ruleForm.currentPostAge" placeholder="请选择当前岗位年限">
                <el-option
                  v-for="item in currentPostAgeOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="最近晋升日期" prop="lastPromotionDate">
              <el-date-picker
                v-model="ruleForm.lastPromotionDate"
                value-format="YYYY-MM-DD"
                type="date"
                placeholder="选择日期"
              />
            </el-form-item>
            <el-form-item label="参加工作时间" prop="joinWorkDate">
              <el-date-picker
                v-model="ruleForm.joinWorkDate"
                value-format="YYYY-MM-DD"
                type="date"
                placeholder="选择日期"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
      <div class="text_center">
        <el-button class="page_confirm_btn" type="primary" @click="prevStep" v-show="currentIndex != currentFirstCode"
          >上一步</el-button
        >
        <el-button class="page_confirm_btn" type="primary" @click="submitForm(ruleFormRef)">{{
          nextBtnText
        }}</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'
import { regionData } from 'element-china-area-data'
import { updateEnqUserInfo, getDictList, getEnqUserInfo } from '../../../request/api'
import { compareDate } from '@/utils/utils'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  currentIndex: String,
  currentFirstCode: String
})

const emit = defineEmits(['nextStep', 'prevStep'])

const userStore = useUserStore()
const ruleFormRef = ref(null)

// 响应式状态
const cityInfo = ref(regionData)

const lastPromotionOptions = ref([])
const currentPostAgeOptions = ref([])
const maritalOptions = ref([])
const nationalityOptions = ref([])
const genderOptions = ref([])

const ruleForm = ref({
  userId: '',
  enqId: props.enqId,
  userName: '',
  gender: '',
  birthday: '',
  nativePlace: '',
  homePlace: '',
  residencePlace: '',
  nationalityCode: '',
  maritalStatus: '',
  currentEmpDate: '',
  postName: '',
  currentPostAge: '',
  lastPromotionDate: '',
  joinWorkDate: ''
})

const rules = {
  userName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  birthday: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
  nativePlace: [{ required: true, message: '请选择籍贯', trigger: 'change' }],
  homePlace: [{ required: true, message: '请选择户籍所在地', trigger: 'change' }],
  residencePlace: [{ required: true, message: '请选择现居住地', trigger: 'change' }],
  nationalityCode: [{ required: true, message: '请选择民族', trigger: 'change' }],
  maritalStatus: [{ required: true, message: '请选择婚姻状况', trigger: 'change' }],
  currentEmpDate: [{ required: true, message: '请选择入司时间', trigger: 'change' }],
  postName: [{ required: true, message: '请输入当前岗位', trigger: 'blur' }],
  currentPostAge: [{ required: true, message: '请选择当前岗位年限', trigger: 'change' }],
  lastPromotionDate: [{ required: true, message: '请选择最近晋升日期', trigger: 'change' }],
  joinWorkDate: [{ required: true, message: '请选择参加工作时间', trigger: 'change' }]
}

// 方法
const getEnqUserInfoFun = async () => {
  try {
    const res = await getEnqUserInfo({
      enqId: props.enqId
    })
    if (res.code == 200) {
      Object.assign(ruleForm.value, res.data)
    } else {
      ElMessage.error('获取数据失败!')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败!')
  }
}

const disabledDate = time => {
  return time.getTime() > Date.now()
}

const saveBasicInfo = async () => {
  try {
    const res = await updateEnqUserInfo(ruleForm.value)
    if (res.code == 200) {
      ElMessage.success(res.msg)
      emit('nextStep')
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('保存失败!')
  }
}

const submitForm = async formEl => {
  if (!formEl) return

  try {
    await formEl.validate()
    await saveBasicInfo()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleChange = (code, type) => {
  ruleForm.value[type] = code[code.length - 1]
}

const prevStep = async () => {
  try {
    await ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '保存',
      cancelButtonText: '放弃修改'
    })
    await submitForm(ruleFormRef.value)
  } catch (action) {
    ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
    if (action == 'cancel') {
      emit('prevStep')
    }
  }
}

// 监听器
watch(
  () => ruleForm.value.lastPromotionDate,
  val => {
    if (val && !compareDate(val, ruleForm.value.currentEmpDate)) {
      ElMessage.warning('最近晋升日期需大于入司时间')
      ruleForm.value.lastPromotionDate = ''
    }
  }
)

// 生命周期钩子
onMounted(async () => {
  try {
    const docList = await userStore.getDocList(['EXPERIENCE_AGE', 'NATIONALITY_CODE', 'MARITAL_STATUS', 'GENDER'])
    lastPromotionOptions.value = docList.EXPERIENCE_AGE
    currentPostAgeOptions.value = docList.EXPERIENCE_AGE
    maritalOptions.value = docList.MARITAL_STATUS
    nationalityOptions.value = docList.NATIONALITY_CODE
    genderOptions.value = docList.GENDER

    await getEnqUserInfoFun()
  } catch (error) {
    console.error(error)
    ElMessage.error('初始化数据失败!')
  }
})
</script>

<style scoped lang="scss">
.from_wrap {
  .basic_info {
    float: left;
    width: 50%;
  }
}
</style>
