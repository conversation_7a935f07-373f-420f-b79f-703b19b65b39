<template>
  <div>
    <div class="filter_item_wrap">
      <div class="filter_group">
        <div class="filter_group_title">{{ itemData.name }}</div>
        <div class="list_wrap">
          <div
            v-for="item in itemData.children"
            :key="item.code"
            :class="setClass(item.code)"
            @click="toggleCheckItem(item.code)"
          >
            <el-icon><CaretRight /></el-icon>
            {{ item.value }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { CaretRight } from '@element-plus/icons-vue'

const props = defineProps({
  itemData: {
    type: Object,
    default: () => ({})
  },
  defaultChecked: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['getCheckedId'])

const setClass = code => {
  const checked = props.defaultChecked.indexOf(code) !== -1
  return {
    filter_item: true,
    checked
  }
}

const toggleCheckItem = code => {
  const index = props.defaultChecked.indexOf(code)
  if (index == -1) {
    props.defaultChecked.push(code)
  } else {
    props.defaultChecked.splice(index, 1)
  }
  emit('getCheckedId', props.defaultChecked)
}
</script>

<style scoped lang="scss">
.filter_item_wrap {
  width: 100%;
  margin-bottom: 16px;
  overflow-y: auto;

  .list_wrap {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e5e5e5;
    border-top: none;
  }

  .filter_group_title {
    font-size: 14px;
    background: #e5f0f9;
    padding: 10px 16px;
    color: #525e6c;
  }

  .filter_item {
    height: 40px;
    line-height: 40px;
    padding: 0 40px;
    margin-bottom: 1px;
    cursor: pointer;

    .el-icon {
      color: transparent;
    }

    &.checked {
      color: #0099fd;

      .el-icon {
        display: inline-block;
        color: #0099fd;
      }
    }
  }
}
</style>
