/*
 * echarts转img
 *
 * 1.创建echarts容器
 * 2.获取echarts参数
 * 3.渲染echarts
 * 4.echarts转base64
 * 5.清空echarts容器
 *
 * */
import * as echarts from 'echarts'
import util from '../methods'
import getEchartsOptions from './echartsOption'

/*--------------------------------echarts转img-------------------------------------------------*/
//数据转echarts
function dataToEC(id, type, width, height, chartData) {
  return createEchartsWrap(id, type, width, height, chartData)
}
//创建容器
function createEchartsWrap(id, type, width, height, chartData) {
  let dom = document.createElement('div')
  dom.id = id
  dom.style.width = `${width}px`
  dom.style.height = `${height}px`
  dom.style.display = 'none'
  let body = document.getElementsByTagName('body')[0]
  body.appendChild(dom)
  //渲染
  let imgBase64 = renderEcharts(id, type, chartData)
  clearEchartsWrap(body, dom)
  return imgBase64
}

//清除容器
function clearEchartsWrap(body, dom) {
  body.removeChild(dom)
}

//渲染echarts
function renderEcharts(id, type, chartData) {
  let myChart = echarts.init(document.getElementById(id))
  if (chartData.data.length == 0) {
    myChart.clear()
    return
  }
  //获取echarts options
  let option = getEchartsOptions(type, chartData)
  // console.log(option)
  myChart.setOption(option)
  return myChart.getDataURL()
}

//canvas转img
function echartsToImg(id, type, size, chartData) {
  let sizeArr = size.split(':')
  let width = sizeArr[0]
  let height = sizeArr[1]

  //echarts转img
  //id: 图表id(后台定义)
  //type:图表类型
  //width,height:图表所占宽高
  //chartData:图表数据
  if (type == 'ReportYBar') {
    height = chartData.data.length * 101 + 25
  }
  if (type == 'ReportYLine') {
    height = chartData.data.length * 50.9 + 50
  }
  if (type == 'YBar') {
    // 报告中图表高度动态设置
    if (chartData.data.length <= 7) {
      height = chartData.data.length * 80 + 25
    } else if (chartData.data.length > 35) {
      height = chartData.data.length * 15 + 25
    }
  }

  return dataToEC(id, type, width, height, chartData)
}

/*--------------------------------页面渲染图表-------------------------------------------------*/

//创建容器
function createEchartsPageWrap(id, type, width, height, chartData, callback) {
  let wrap = document.getElementById(id)
  let w = wrap.offsetWidth
  let h = wrap.offsetHeight
  console.log('w :>> ', w)
  let dom = document.createElement('div')
  let domId = util.createRandomId()
  dom.id = domId
  // dom.style.width = `${width}px`;
  dom.style.width = width ? `${width}px` : w ? `${w}px` : '100%'
  // dom.style.height = `${height}px`;
  dom.style.height = height ? `${height}px` : `${h}px`
  dom.style.lineHeight = height ? `${height}px` : `${h}px`
  wrap.innerHTML = ''
  wrap.appendChild(dom)
  renderPageEcharts(domId, type, chartData, callback)
}

//渲染页面图表
function renderPageEcharts(domId, type, chartData, callback) {
  let myChart = echarts.init(document.getElementById(domId))
  if (!chartData.data || chartData.data.length == 0) {
    myChart.clear()
    let dom = document.getElementById(domId)
    dom.innerHTML = '暂无数据'
    // dom.style.lineHeight = height ? `${height}px` : `${h}px`;

    return
  }
  //获取echarts options
  let option = getEchartsOptions(type, chartData)
  if (!option) {
    return
  }
  myChart.setOption(option)
  myChart.off()
  myChart.on('click', params => {
    let option = getEchartsOptions(type, chartData, params.dataIndex)
    if (!option) {
      return
    }
    myChart.setOption(option)
    typeof callback == 'function' && callback(params)
  })
}

//echarts页面渲染
function echartsRenderPage(id, type, width, height, chartData, callback) {
  //echarts直接渲染到页面
  //id: 图表容器id
  //type:图表类型
  //width,height:图表所占宽高
  //chartData:图表数据
  createEchartsPageWrap(id, type, width, height, chartData, callback)
}

//设置图表数据

let chartTypeObj = {
  1: 'ProgressPie', //进度环形图
  2: 'YBar', //Y轴柱图（柱图+折线图（lineData:[]））（图表数据中添加属性：target:true,//柱图有目标值（边框图））
  3: 'XBar', //X轴柱图（柱图+折线图（lineData:[]））（图表数据中添加属性：target:true,//柱图有目标值（边框图））
  4: 'Radar', //雷达图
  5: 'Quadrant', //四象限图
  6: 'ReportYLine', //报告Y轴折线图
  7: 'ReportYBar', //报告Y轴柱图
  8: 'XTree', //树形图
  9: 'SolidPie', //实行饼图
  10: 'XStack', //X轴堆叠图
  11: 'XLadder', //X轴阶梯图 （图表数据中添加属性：type:"ladder"）
  12: 'Funnel', //漏斗图
  13: 'XLine', //折线图
  14: 'Scatter', //散点图
  15: 'Gauge' // 速度仪表盘
}

function setChartData(item, data) {
  let renderType = chartTypeObj[item.chartType]
  let renderData = {}
  switch (item.chartType) {
    case '1':
      renderData = {
        data: {
          label: data.name,
          value: (data.key + '').split('%')[0],
          company: (data.key + '').indexOf('%') > -1 ? '%' : ''
        }
      }
      break
    case '2':
    case '3':
      renderData.report = true
      renderData.target = data.target
      renderData.padding = data.padding ? data.padding : null
      renderData.needAverage = data.needAverage ? data.needAverage : false
      if (data.lineLegend) {
        renderData.lineLegend = data.lineLegend
        renderData.lineData = data.lineData.map(row => {
          return {
            name: row.name,
            value: row.key
          }
        })
      } else if (data.lineData) {
        renderData.lineData = data.lineData.map(row => {
          return {
            name: row.name,
            value: row.key
          }
        })
      }
      if (data.legend) {
        renderData.legend = data.legend
        renderData.data = data.chartData
      } else {
        renderData.data = data.chartData.map(row => {
          return {
            name: row.name,
            value: row.key
          }
        })
      }
      break
    case '4':
      renderData.legend = data.legend
      renderData.data = data.chartData
      break
    case '5':
      renderData = {
        legend: data.legend,
        data: data.chartData,
        report: true
      }
      break
    case '6':
      renderData = {
        legend: data.legend,
        data: data.chartData,
        padding: data.padding ? data.padding : null
      }
      break
    case '7':
      renderData = {
        padding: data.padding ? data.padding : null,
        data: data.map(item => {
          return {
            name: item.moduleName,
            value: item.deviation
          }
        })
      }
      break

    //------------------------------
    case '8':
      renderData = {
        data: data
      }
      break
    case '9':
      renderData = {
        data: data.map(item => {
          return {
            name: item.name,
            value: item.key
          }
        }),
        report: true
      }
      break
    case '10':
      renderData = {
        data: data.data,
        area: data.area,
        legend: data.legend,
        padding: data.padding ? data.padding : null
      }
      break
    case '11':
      renderData = {
        type: 'ladder',
        data: data,
        padding: data.padding ? data.padding : null
      }
      break
    case '12':
      renderData = {
        data: data.map(item => {
          return {
            name: item.name,
            value: item.key
          }
        })
      }
      break
    case '13':
      renderData = {
        data: data.chartData,
        legend: data.legend,
        report: true,
        padding: data.padding ? data.padding : null
      }
      break
    case '14':
      renderData = {
        data: data.chartData,
        legend: data.legend
      }
      break
    default:
      break
  }
  //返回图表类型和数据
  return {
    renderType,
    renderData
  }
}

export { echartsToImg, echartsRenderPage, setChartData }
