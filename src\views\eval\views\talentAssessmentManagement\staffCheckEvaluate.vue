<template>
  <div class="staff_check_evaluate_wrap bg_write">
    <div class="page_main_title">
        {{ pageTitle }}
      <div class="goback_geader" v-link="'/talentAssessment/talentAssessmentManagement/evaluationItemList'">
        <i class="el-icon-arrow-left"></i>返回
      </div>
    </div>
    <div class="page_main">
      <div class="tip_wrap">请对您的下属进行终极个人发展评价</div>
      <div class="title_wrap">请对您的下属进行最终的个人发展建议</div>
      <div class="staff_check_evaluate_main page_section flex_row_betweens">
        <div class="staff_check_evaluate_left_wrap">
          <ul class="left_staff_list_wrap">
            <li
              class="flex_row_between"
              v-for="(item, index) in evaluationSubList"
              :key="item.objectId"
              :class="{ check_sub: item.objectId == defaultCheckSub }"
              @click="checkEvaluationSub(item.objectId, index)"
            >
              <span>{{ item.objectName }}</span
              ><span
                :class="{
                  icon: true,
                  'el-icon-check': item.reviewStatus == 'Y',
                  circle_icon: item.reviewStatus != 'Y',
                }"
              ></span>
            </li>
          </ul>
        </div>
        <div class="staff_check_evaluate_rigth_wrap">
          <div class="right_title_wrap">
            {{ checkSubInfo.objectName }}
            <span>{{ checkSubInfo.postName }}</span>
            <span v-if="checkSubInfo.currentEmpAgeName"
              >（{{ checkSubInfo.currentEmpAgeName }}）</span
            >
          </div>
          <template v-if="checkSubInfo.developmentType">
            <div class="right_line_wrap flex_row_start">
              <div class="line_item_wrap">
                <div class="line_item_title"><span></span>个人期望发展类型</div>
                <div class="line_item_content line_item_info">
                  {{ checkSubInfo.developmentType }}
                </div>
              </div>
              <div class="line_item_wrap">
                <div class="line_item_title"><span></span>个人期望晋升职位</div>
                <div class="line_item_content line_iten_info">
                  {{ checkSubInfo.expectationJob }}
                </div>
              </div>
              <div class="line_item_wrap">
                <div class="line_item_title"><span></span>个人期望晋升周期</div>
                <div class="line_item_content line_item_info">
                  {{ checkSubInfo.expectationCycle }}
                </div>
              </div>
            </div>
            <div class="right_line_wrap flex_row_start">
              <div class="line_item_wrap two_thirds_wrap">
                <div class="line_item_title"><span></span>个人短板分析</div>
                <div class="line_item_content line_item_info">
                  {{ checkSubInfo.analyzeSummarize }}
                </div>
              </div>
              <div class="line_item_wrap one_third_wrap">
                <div class="line_item_title"><span></span>个人发展计划</div>
                <div class="line_item_content line_item_info">
                  {{ checkSubInfo.developmentPlan }}
                </div>
              </div>
            </div>
          </template>
          <div class="right_line_wrap flex_row_start">
            <div class="line_item_wrap">
              <div class="line_item_title"><span></span>工作绩效表现</div>
              <div class="line_item_content">
                <el-select
                  class="item"
                  clearable
                  v-model="checkSubInfo.kpiRank"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in jobPerformanceOptions"
                    :key="item.dictCode"
                    :label="item.codeName"
                    :value="item.dictCode"
                  ></el-option>
                </el-select>
              </div>
            </div>
            <div class="line_item_wrap">
              <div class="line_item_title"><span></span>潜力表现</div>
              <div class="line_item_content">
                <el-select
                  class="item"
                  clearable
                  v-model="checkSubInfo.developmentPotential"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in jobPerformanceOptions"
                    :key="item.dictCode"
                    :label="item.codeName"
                    :value="item.dictCode"
                  ></el-option>
                </el-select>
              </div>
            </div>
          </div>
          <div class="right_line_wrap flex_row_start">
            <div class="line_item_wrap">
              <div class="line_item_title"><span></span>建议发展类型</div>
              <div class="line_item_content">
                <el-radio-group
                  class="reset_radio_group"
                  v-model="checkSubInfo.superiorDevelopmentType"
                  
                  @change="devTypeChange"
                >
                  <el-radio
                    v-for="item in developmentTypeOptions"
                    :key="item.dictCode"
                    :label="item.dictCode"
                    border
                    >{{ item.codeName }}</el-radio
                  >
                </el-radio-group>
              </div>
            </div>
            <div class="line_item_wrap">
              <div class="line_item_title"><span></span>建议目标职位</div>
              <div class="line_item_content panel_center">
                <div
                  class=""
                  v-if="
                    checkSubInfo.superiorDevelopmentType == 1 ||
                    checkSubInfo.superiorDevelopmentType == 4 ||
                    checkSubInfo.superiorDevelopmentType == 5
                  "
                >
                  不需要选择晋升岗位
                </div>
                <div
                  class="goal_post"
                  v-show="checkSubInfo.superiorDevelopmentType == 2"
                >
                  <div
                    class="current_post_center marginT_16"
                    v-if="!jobTipSign"
                  >
                    <div class="item">
                      <div class="item_title">职位族群</div>
                      <div class="item_text">
                        {{ goalPostInfo.parentJobClassName }}
                      </div>
                    </div>
                    <div class="item">
                      <div class="item_title">职位序列</div>
                      <div class="item_text">
                        {{ goalPostInfo.jobClassName }}
                      </div>
                    </div>
                    <div class="item">
                      <div class="item_title">职位名称</div>
                      <div class="item_text">
                        {{ goalPostInfo.jobName }}
                      </div>
                    </div>
                    <div class="item">
                      <div class="item_title">职层</div>
                      <div class="item_text">
                        {{ goalPostInfo.jobLevelName }}
                      </div>
                    </div>
                    <div class="item">
                      <div class="item_title">职等</div>
                      <div class="item_text">
                        {{ goalPostInfo.jobGradeName }}
                      </div>
                    </div>
                  </div>
                  <div class="" v-if="jobTipSign">{{ jobTip }}</div>
                </div>

                <div
                  class=""
                  v-show="checkSubInfo.superiorDevelopmentType == 3"
                >
                  <div class="promotion_post clearfix">
                    <div class="post_type fl">
                      <div class="title border_r_1">职族</div>
                      <div class="post_type_center border_r_1">
                        <div class="post_type_main">
                          <tree-comp-radio
                            :treeData="treeData"
                            :needCheckedFirstNode="false"
                            :nodeKey="'code'"
                            :defaultCheckedKeys="defaultCheckedKeys"
                            @clickCallback="treeCallback"
                          ></tree-comp-radio>
                        </div>
                      </div>
                    </div>
                    <div class="post_type fl">
                      <div class="title">职位名称</div>
                      <div class="post_type_center">
                        <div class="post_type_main">
                          <el-radio-group v-model="checkGoalPostCode">
                            <el-radio
                              class="post_item"
                              v-for="item in postList"
                              :key="item.jobCode"
                              :label="item.jobCode"
                              >{{ item.jobName }}</el-radio
                            >
                          </el-radio-group>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div class="" v-if="checkSubInfo.superiorDevelopmentType == 5">
                        <div class="page_second_title">当前职位</div>
                        <div class="marginT_16">职位名称</div>
                        <div class="marginT_16">销售经理</div>
                    </div> -->
              </div>
            </div>
            <div class="line_item_wrap">
              <div>
                <div class="line_item_title"><span></span>预计周期</div>
                <div class="line_item_content">
                  <el-select
                    class="item"
                    clearable
                    v-model="checkSubInfo.superiorExpectationCycle"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in expectedCycleOptions"
                      :key="item.dictCode"
                      :label="item.codeName"
                      :value="item.dictCode"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div>
                <div class="line_item_title"><span></span>个人优势</div>
                <div class="line_item_content">
                  <el-input
                    type="textarea"
                    placeholder=""
                    v-model="checkSubInfo.superiorAnalyzeSummarize"
                  ></el-input>
                </div>
              </div>
              <div>
                <div class="line_item_title"><span></span>待提升的能力</div>
                <div class="line_item_content">
                  <el-input
                    type="textarea"
                    placeholder=""
                    v-model="checkSubInfo.capacityToImproved"
                  ></el-input>
                </div>
              </div>
            </div>
          </div>
          <div class="submit_btn_wrap">
            <div class="submit_btn" @click="affirm()">确认</div>
          </div>
          <div class="evaluation_complete_btn_wrap" v-if="isAllEvaluateSign">
            <div class="evaluation_complete_btn" @click="submit">
              已全部评价完，提交
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import treeCompRadio from "@/components/talent/treeComps/treeCompRadio";
import {
  getOrgDeptTree,
  getPostList,
  searchPost,
  getJobClassTree,
  getSearchJobList,
} from "@/views/entp/request/api";
import {
  getEvaluationSubList,
  getDevelopmentProposals,
  submitPerformance,
  getIdealJob,
  answerSubmit,
  getEvalInfo
} from "../../request/api";

export default {
  name: "staffCheckEvaluate",
  data() {
    return {
      pageTitle:'',
      evalId: this.$route.query.evalId,
      evaluationSubList: [],
      defaultCheckSub: "",
      defaultCheckIndex: "",
      checkSubInfo: {
        objectName: "",
        objectId: "",
        postName: "",
        postCode: "",
        currentEmpAgeName: "",
        developmentType: "",
        expectationJob: "",
        expectationCycle: "",
        analyzeSummarize: "",
        developmentPlan: "",
        superiorDevelopmentType: "1",
        superiorExpectationJob: "",
        superiorExpectationCycle: "",
        superiorAnalyzeSummarize: "",
        capacityToImproved: "",
        kpiRank: "",
        developmentPotential: "",
      },
      developmentTypeOptions: [],
      goalPostInfo: {},
      treeData: [],
      defaultCheckedKeys: [],
      checkGoalPostCode: "",
      postList: [],
      orgCode: null,
      jobTipSign: false,
      jobTip: "",
      jobPerformance: "",
      jobPerformanceOptions: [],
      expectedCycleOptions: [],
      isAllEvaluateSign: false,
    };
  },
  components: {
    treeCompRadio,
  },
  created() {
    this.$getDocList([
      "JOB_PERFORMANCE",
      "SUPERIOR_DEVELOPMENT_TYPE",
      "EXPECTATION_CYCLE",
    ]).then((res) => {
      this.jobPerformanceOptions = res.JOB_PERFORMANCE;
      this.expectedCycleOptions = res.EXPECTATION_CYCLE;
      this.developmentTypeOptions = res.SUPERIOR_DEVELOPMENT_TYPE;
      // console.log(this.jobPerformanceOptions)
    });
    this.getEvalInfoFun();
    // this.getIdealJobFun()
  },
  mounted() {
    this.getEvaluationSubListFun();
    this.getJobClassTreeFun();
  },
  methods: {
      //获取测评title
    getEvalInfoFun() {
      getEvalInfo({
        evalId: this.evalId,
      }).then((res) => {
        // console.log(res)
        this.pageTitle = res.evalName;
      });
    },
    // 选择理想下一晋升岗位
    getIdealJobFun() {
      getIdealJob({
        evalId: this.evalId,
        developmentType: "2",
        objectId: this.defaultCheckSub,
      }).then((res) => {
        this.jobTipSign = false;
        if (res.data) {
          this.hasParentPost = true;
          this.goalPostInfo = res.data;
        } else {
          this.hasParentPost = false;
          if (res.code == 500) {
            this.jobTipSign = true;
            this.jobTip = res.msg;
          }
        }
      });
    },
    // 发展类型切换
    devTypeChange(val) {
      this.checkSubInfo.superiorDevelopmentType = val;
      if (val == "1") {
      } else if (val == "2") {
      } else if (val == "3") {
      }
    },
    //族群序列树结构
    getJobClassTreeFun() {
      getJobClassTree({}).then((res) => {
        console.log(res);
        if (res.length > 0) {
          this.treeData = res;
        } else {
          this.treeData = [];
        }
      });
    },
    // 选择组织
    treeCallback(code) {
      console.log(code);
      this.orgCode = code;
      // console.log(this.superiorDevelopmentType);
      // if (this.superiorDevelopmentType == 3) {
      this.getSearchJobListFun();
      // }
    },
    getSearchJobListFun() {
      getSearchJobList({
        jobClassCode: this.orgCode,
      }).then((res) => {
        console.log(res);
        this.postList = [];
        if (res.code == 200) {
          this.postList = res.data;
        }
      });
    },
    //参评下级列表
    getEvaluationSubListFun() {
      getEvaluationSubList({
        evalId: this.evalId,
      }).then((res) => {
        // console.log(res)
        this.defaultCheckedKeys = [];
        this.checkGoalPostCode = "";
        if (res.length > 0) {
          this.evaluationSubList = res;
          this.defaultCheckIndex = 0;
          if (this.defaultCheckSub) {
          } else {
            this.defaultCheckSub = this.evaluationSubList[0].objectId;
          }
          this.checkSubInfo = this.evaluationSubList[0];
          if (!this.checkSubInfo.superiorDevelopmentType) {
            this.checkSubInfo.superiorDevelopmentType = "1";
          }
          if (this.checkSubInfo.superiorDevelopmentType == "3") {
            this.checkGoalPostCode = this.checkSubInfo.superiorExpectationJob;
            this.defaultCheckedKeys.push(
              this.checkSubInfo.superiorJobClassCode
            );
          }
          this.isAllEvaluateSign = res.every((item, index, array) => {
            return item.reviewStatus == "Y";
          });
          this.getIdealJobFun();
        }
      });
    },
    //选择参评人员
    checkEvaluationSub(val, index) {
      this.defaultCheckSub = val;
      this.checkSubInfo = this.evaluationSubList[index];
      this.getIdealJobFun();
    },
    // 确认
    affirm() {
      this.submitPerformanceFun();
    },
    //评价人员
    submitPerformanceFun() {
      console.log(this.checkSubInfo);
      if (
        this.checkSubInfo.superiorDevelopmentType == "3" &&
        !this.checkGoalPostCode
      ) {
        this.$msg.warning("请勾选建议目标职位！");
        return;
      }

      if (
        !this.checkSubInfo.kpiRank ||
        !this.checkSubInfo.developmentPotential ||
        !this.checkSubInfo.superiorDevelopmentType ||
        !this.checkSubInfo.superiorExpectationCycle ||
        this.checkSubInfo.superiorAnalyzeSummarize == null ||
        this.checkSubInfo.capacityToImproved == null
      ) {
        this.$msg.warning("请完善相关评价信息！");
        return;
      }

      let expectJobCode = null;
      if(this.checkSubInfo.superiorDevelopmentType == "3"){
        expectJobCode = this.checkGoalPostCode
      }

      if(this.checkSubInfo.superiorDevelopmentType == "2"){
        expectJobCode = this.goalPostInfo.jobCode;
      }

      let performanceRequest = {
        evalId: this.evalId,
        objectId: this.checkSubInfo.objectId,
        postCode: this.checkSubInfo.postCode,
        kpiRank: this.checkSubInfo.kpiRank,
        developmentPotential: this.checkSubInfo.developmentPotential,
        capacityToImproved: this.checkSubInfo.capacityToImproved,
        superiorAnalyzeSummarize: this.checkSubInfo.superiorAnalyzeSummarize,
        superiorDevelopmentType: this.checkSubInfo.superiorDevelopmentType,
        superiorExpectationCycle: this.checkSubInfo.superiorExpectationCycle,
        superiorExpectationJob: expectJobCode,
      };

      submitPerformance(performanceRequest).then((res) => {
        // console.log(res)
        if (res.code == 200) {
          this.$msg.success(res.msg);
          this.getEvaluationSubListFun();
        }
      });
    },
    submit() {
      answerSubmit({ evalId: this.evalId }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.$router.push('/talentAssessment/talentAssessmentManagement/evaluationItemList');
        } else {
          this.$msg.error(res.msg);
        }
      });
    },
    // //获取参评人员详情
    // getDevelopmentProposalsFun(){
    //     getDevelopmentProposals({
    //         evalId:this.evalId,
    //         objectId:this.defaultCheckSub
    //     }).then(res=>{

    //     })
    // },
    //
  },
};
</script>
<style lang="scss" scoped>
.staff_check_evaluate_wrap {
  .tip_wrap {
    margin: 0 5px 0;
    padding: 0 15px 0;
    width: 97.5%;
    height: 40px;
    line-height: 40px;
    background: #edf4fe;
    border-radius: 4px;
  }
  .title_wrap {
    margin: 0 6px 0;
    height: 70px;
    line-height: 70px;
    font-size: 18px;
  }
  .page_main {
    height: auto;
  }
  .staff_check_evaluate_main {
    .staff_check_evaluate_left_wrap {
      margin: 0 30px 0 0;
      width: 190px;
      .left_staff_list_wrap {
        li {
          margin: 0 0 15px 0;
          padding: 0 20px;
          width: 190px;
          height: 40px;
          line-height: 40px;
          background: #e0e3ea;
          border-radius: 4px;
          color: #0091f9;
          // font-weight: 600;
          cursor: pointer;
          .icon {
            font-size: 22px;
            font-weight: 600;
          }
          .circle_icon {
            width: 17px;
            height: 17px;
            border-radius: 50%;
            background: #0091f9;
          }
        }
        .check_sub {
          color: #fff;
          background: #0091f9;
          .icon {
            color: #fff;
          }
          .circle_icon {
            background: #fff;
          }
        }
        li:hover {
          color: #fff;
          background: #0091f9;
          .icon {
            color: #fff;
          }
          .circle_icon {
            background: #fff;
          }
        }
      }
    }
    .staff_check_evaluate_rigth_wrap {
      border-left: 2px solid #eaeaea;
      flex: 1;
      padding: 0 0 0 20px;
      .right_title_wrap {
        height: 35px;
        line-height: 35px;
        font-size: 18px;
        color: #0f222f;
        font-weight: 600;
        span {
          display: inline-block;
          margin: 0 0 0 12px;
          font-size: 14px;
          font-weight: 400;
          color: #2d2d2d;
        }
      }
      .right_line_wrap {
        margin: 18px 0 0 0;
        .line_item_wrap {
          width: 230px;
          .line_item_title {
            color: #0f222f;
            height: 27px;
            line-height: 27px;
            span {
              display: inline-block;
              margin-right: 5px;
              width: 10px;
              height: 10px;
              background: #0091f9;
            }
          }
          .line_item_content {
            line-height: 27px;
            color: #6f6f6f;
            .el-select {
              margin: 5px 0 0 0;
            }
            .el-textarea {
              margin: 5px 0 0 0;
              width: 320px;
              .el-textarea__inner {
                height: 99px;
                resize: none;
              }
            }
            .reset_radio_group {
              margin: 5px 0 0 0;
              width: 190px;
              height: 35px;
              .el-radio {
                position: relative;
                width: 100%;
                height: 100%;
                margin: 0 0 15px 0;
                line-height: 35px;
                padding: 0;
                border-color: #e5e5e5;
                &.is-checked {
                  border-color: #449CFF;
                }
                .el-radio__input {
                  position: absolute;
                  width: 20px;
                  height: 20px;
                  right: 5px;
                  top: 6px;
                  border: 1px solid #e5e5e5;
                  border-radius: 50%;
                  overflow: hidden;
                  .el-radio__inner {
                    display: none;

                    &::after {
                      width: 0;
                      height: 0;
                    }
                  }
                  &.is-checked {
                    background: #0099FF;
                    .el-radio__inner {
                      display: block;
                      position: absolute;
                      width: 13px;
                      height: 7px;
                      left: 3px;
                      top: 4px;
                      border: 2px solid #fff;
                      border-radius: 0;
                      transform: rotate(-45deg);
                      border-top: none;
                      border-right: none;
                    }
                  }
                }
              }
              .el-radio {
                position: relative;
                width: 100%;
                height: 100%;
                margin: 0 0 15px 0;
                line-height: 35px;
                padding: 0;
                border-color: #e5e5e5;

                &.is-checked {
                  border-color: #449CFF;
                }
                .el-radio__input {
                  position: absolute;
                  width: 20px;
                  height: 20px;
                  right: 5px;
                  top: 6px;
                  border: 1px solid #e5e5e5;
                  border-radius: 50%;
                  overflow: hidden;

                  .el-radio__inner {
                    display: none;

                    &::after {
                      width: 0;
                      height: 0;
                    }
                  }

                  &.is-checked {
                    background: #0099FF;

                    .el-radio__inner {
                      display: block;
                      position: absolute;
                      width: 13px;
                      height: 7px;
                      left: 3px;
                      top: 4px;
                      border: 2px solid #fff;
                      border-radius: 0;
                      transform: rotate(-45deg);
                      border-top: none;
                      border-right: none;
                    }
                  }
                }
              }
            }
            .el-date-editor {
              margin: 5px 0 0 0;
            }
          }
          .line_item_info {
            min-height: 30px;
            line-height: 30px;
          }
          .panel_center {
            .current_post_center {
              width: 300px;
              border: 1px solid #e5e5e5;
              border-bottom: none;

              .item {
                width: 100%;
                display: flex;
                height: 40px;
                line-height: 40px;
                font-size: 12px;

                .item_title {
                  width: 40%;
                  text-align: center;
                  background: #f4f4f4;
                  border-bottom: 1px solid #e5e5e5;
                }
                .item_text {
                  flex: 1;
                  padding-left: 12px;
                  border-bottom: 1px solid #e5e5e5;
                }
              }
            }
            .promotion_post {
              margin: 5px 0 0 0;
              width: 300px;
              border: 1px solid #e5e5e5;
              .post_type {
                width: 50%;
                .title {
                  background: #EBF4FF;
                  height: 45px;
                  line-height: 45px;
                  text-align: center;
                }

                .post_type_center {
                  padding: 10px;
                  height: 360px;
                  .post_type_main {
                    width: 100%;
                    height: 100%;
                    overflow: auto;
                    .post_item {
                      display: block;
                      margin-right: 0;
                    }
                    // .el-radio-group{
                    //     width: 100%;
                    //     .el-radio{
                    //         margin: 0;
                    //         font-size: 14px;
                    //         line-height: 27px;
                    //         height: 27px;
                    //         .el-radio__input{
                    //             right: auto;
                    //             width: 15px;
                    //             height: 15px;
                    //             .el-radio__inner{
                    //                 top: 2px;
                    //                 left: 2.5px;
                    //                 width: 10px;
                    //                 height: 5px;
                    //                 border-block-width: 1px;
                    //             }
                    //         }
                    //         .el-radio__label{
                    //             margin: 0 0 0 15px;
                    //         }
                    //     }
                    // }
                  }

                  .item {
                    line-height: 40px;
                    cursor: pointer;

                    .icon {
                      color: transparent;
                    }

                    &.active {
                      color: #0099FF;

                      .icon {
                        color: #0099FF;
                      }
                    }
                  }
                }
              }
            }
          }
          .text_area_wrap {
            height: 99px !important;
          }
        }
        .line_item_wrap:nth-child(2) {
          width: 320px;
        }
        .two_thirds_wrap {
          margin: 0 70px 0 0;
          width: 480px;
        }
        .one_third_wrap {
          // flex: 1;
          width: 320px;
        }
      }
      .submit_btn_wrap {
        margin: 30px 0 0 0;
        .submit_btn {
          width: 80px;
          height: 30px;
          line-height: 30px;
          color: #449cff;
          border: 1.5px solid #449cff;
          border-radius: 5px;
          text-align: center;
          cursor: pointer;
        }
        .submit_btn:hover {
          color: #fff;
          background: #0099ff;
        }
      }
      .evaluation_complete_btn_wrap {
        .evaluation_complete_btn {
          margin: 40px auto 0;
          width: 180px;
          height: 35px;
          line-height: 35px;
          color: #ffffff;
          text-align: center;
          border-radius: 8px;
          background: #449cff;
          cursor: pointer;
        }
        .evaluation_complete_btn:hover {
          background: #0099ff;
        }
      }
    }
  }
}
</style>