<template>
    <div class="model_manage_wrap bg_write">
        <div class="page_main_title clearfix">
            模型管理
        </div>
        <div class="page_section">
            <div class="oper_btn align_right">
                <el-button class="page_add_btn" type="primary" size="mini" v-link="'/talentAssessment/creatModel'">
                    新增模型
                </el-button>
            </div>
            <div class="model_list_wrap marginT_20">
                <table-component :tableData="tableData" :needIndex='needIndex' @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange">
                    <template v-slot:oper>
                        <el-table-column label="操作" width="100">
                            <template slot-scope="scope">
<!--                                <el-button-->
<!--                                        class="icon_detail"-->
<!--                                        @click.native.prevent="show(scope.$index, tableData.data)"-->
<!--                                        link-->
<!--                                        -->
<!--                                        icon="el-icon-document"-->
<!--                                ></el-button>-->
                                <el-button
                                        class="icon_edit"
                                        @click.native.prevent="edit(scope.$index, tableData.data)"
                                        link
                                        
                                        :title="scope.row.buildStatus == '4' ? '查看' : '编辑'"
                                        :icon="scope.row.buildStatus == '4' ? 'el-icon-view' : 'el-icon-edit'"
                                ></el-button>
                                <el-button
                                        v-if="scope.row.buildStatus != '4'"
                                        class="icon_del"
                                        @click.native.prevent="remove(scope.$index, tableData.data)"
                                        link
                                        
                                        icon="el-icon-delete"
                                ></el-button>
                            </template>
                        </el-table-column>
                    </template>
                </table-component>
            </div>
        </div>
    </div>
</template>

<script>
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import {getModelList,deleteModel} from "../../request/api"
    export default {
        name: "modelManage",
        components: {
            tableComponent
        },
        data() {
            return {
                needIndex:true,
                tableData: {
                    columns: [
                        {
                            label: "模型名称",
                            prop: "modelName",
                        },
                        {
                            label: "能力分类",
                            prop: "classCount",
                            formatterFun:(row, column, val)=>{
                                return val +"类"
                            }
                        },
                        {
                            label: "能力词典",
                            prop: "dictCount",
                            formatterFun:(row, column, val)=>{
                                return val +"项"
                            }
                        },
                        {
                            label: "最新更新时间",
                            prop: "updateTime",
                            width: 120,
                            formatterFun:(row, column, val)=>{
                                return val ? val.split(" ")[0] :""
                            }
                        },
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10
                    }
                },
                listLength:0
            };
        },
        created() {

        },
        mounted(){
            this.getModelListFun();
        },
        methods: {
            // show(index, rows) {
            //     console.log(rows[index]);
            //     this.$router.push({
            //         path:'/talentAssessment/creatModel',
            //         query:{modelId:rows[index].modelId}
            //     })
            // },
            edit(index, rows) {
                this.$router.push({
                    path:'/talentAssessment/creatModel',
                    query:{modelId:rows[index].modelId,buildStatus:rows[index].buildStatus}
                })
            },
            remove(index, rows) {
                this.$confirm('此操作将删除该模型, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.deleteModelFun(rows[index].modelId)
                }).catch(() => {

                });
            },

            /*api*/
            getModelListFun(){
                let params={
                    current:this.tableData.page.current,
                    size:this.tableData.page.size
                }
                getModelList(params).then(res=>{
                    console.log(res)
                    if(res.code ==200){
                        this.tableData.data=res.data;
                        this.listLength = res.data.length;
                        this.tableData.page=res.page
                    }else{
                        this.$msg.warning(res.message)
                    }
                })
            },
            //删除模型
            deleteModelFun(modelId){
                deleteModel({
                    modelId:modelId
                }).then(res=>{
                    console.log(res)
                    if(this.listLength <=1){
                        this.tableData.page.current > 1 &&  this.tableData.page.current--;
                    }
                    this.getModelListFun();
                    if(res.code== 200){
                        this.$msg.success(res.msg)
                    }
                })
            },

            //pageSize 改变时会触发
            handleSizeChange(size){
                console.log(size)
                this.tableData.page.current=1;
                this.tableData.page.size=size;
                this.getModelListFun();
            },

            //currpage 改变时会触发
            handleCurrentChange(page){
                this.tableData.page.current = page;
                this.getModelListFun();
            },

        }
    };
</script>

<style scoped lang="scss">
    .model_manage_wrap {

    }

</style>