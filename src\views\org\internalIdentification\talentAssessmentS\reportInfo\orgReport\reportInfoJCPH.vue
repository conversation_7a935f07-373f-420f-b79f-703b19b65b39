<script setup>
import Table from '@/components/table/simplenessTable.vue'
// 11
const columns7 = ref([
  {
    label: '二级能力',
    prop: 'a',
    width: 260
  },
  {
    label: '经验依赖性',
    prop: 'b'
  },
  {
    label: '数据驱动型',
    prop: 'c'
  },
  {
    label: '机会主义型',
    prop: 'd'
  },

  {
    label: '系统思考型',
    prop: 'e'
  }
])
const data7 = ref([
  {
    a: '市场分析与战略规划',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  },
  {
    a: '战略解码与目标分解',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  }
])

const columns8 = ref([
  {
    label: '决策模式',
    prop: 'a'
  },
  {
    label: '人数',
    prop: 'b'
  },
  {
    label: '能力得分',
    prop: 'c'
  },
  {
    label: '基本定义',
    prop: 'd',
    width: 260
  },

  {
    label: '核心优势',
    prop: 'e',
    width: 260
  },
  {
    label: '主要短板',
    prop: 'f',
    width: 260
  }
])
const data8 = ref([
  {
    a: '经验依赖型',
    b: '12',
    c: '36',
    d: '以过往成功或失败案例为决策核心依据，通过类比历史情境解决当下问题',
    e: '快速复用历史验证的成熟方案，在核心业务领域凭借经验积累精准判断关键成功因子，提升战略落地效率。',
    f: '面对新业务或复杂场景时，易因路径依赖导致资源错配与目标偏差，阻碍战略转型与创新突破。',
    g: '0',
    h: '2024-09-26 09:59:59',
    k: '100'
  },
  {
    a: '数据驱动型',
    b: '12',
    c: '36',
    d: '以过往成功或失败案例为决策核心依据，通过类比历史情境解决当下问题',
    e: '快速复用历史验证的成熟方案，在核心业务领域凭借经验积累精准判断关键成功因子，提升战略落地效率。',
    f: '面对新业务或复杂场景时，易因路径依赖导致资源错配与目标偏差，阻碍战略转型与创新突破。',
    g: '0',
    h: '2024-09-26 09:59:59',
    k: '100'
  },
  {
    a: '机会主义型 ',
    b: '12',
    c: '36',
    d: '以过往成功或失败案例为决策核心依据，通过类比历史情境解决当下问题',
    e: '灵活捕捉市场机会实现短期绩效突破，积累现金流反哺战略储备，提升资源利用效率。',
    f: '缺乏长期战略锚定，易因短期利益导向导致资源分散、目标漂移，放大系统性风险。',
    g: '0',
    h: '2024-09-26 09:59:59',
    k: '100'
  },
  {
    a: '系统思考型',
    b: '12',
    c: '36',
    d: '从整体视角出发，综合考虑各因素关联及长期影响，构建长效决策机制。​',
    e: '构建全局战略框架，统筹长期目标与资源分配，通过压力测试保障绩效稳定性与可持续竞争力。',
    f: '决策流程复杂、协同成本高，难以及时响应市场突变，可能导致关键动作滞后与执行效率损耗。',
    g: '0',
    h: '2024-09-26 09:59:59',
    k: '100'
  }
])

const columns9 = ref([
  {
    label: '决策模式',
    prop: 'a',
    width: 150
  },
  {
    label: '人员',
    prop: 'b'
  },
  {
    label: '改善建议',
    prop: 'c'
  }
])
const data9 = ref([
  {
    a: '经验依赖型 （12人）',
    b: '王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）',
    c: '1.划清经验边界：填写《经验适用边界清单》，标注差异点与潜在风险，避免盲目复制。 2.反经验训练：每月推演 “非惯性做法”，输出《反经验行动清单》，强制尝试新策略。3. 新场景探索：分配 10% 时间接触跨界案例，撰写《旧经验失效分析》，打破路径依赖。'
  }
])

const columns10 = ref([
  {
    label: '二级能力',
    prop: 'a',
    width: 260
  },
  {
    label: '保守型',
    prop: 'b'
  },
  {
    label: '冒险型',
    prop: 'c'
  },
  {
    label: '折中型',
    prop: 'd'
  },

  {
    label: '回避型',
    prop: 'e'
  }
])
const data10 = ref([
  {
    a: '市场分析与战略规划',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  },
  {
    a: '战略解码与目标分解',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  }
])

const columns11 = ref([
  {
    label: '决策模式',
    prop: 'a',
    width: 260
  },
  {
    label: '人数',
    prop: 'b'
  },
  {
    label: '能力得分',
    prop: 'c'
  },
  {
    label: '典型行为特征',
    prop: 'd'
  },

  {
    label: '优势能力领域',
    prop: 'e'
  },
  {
    label: '致命短板',
    prop: 'f'
  }
])
const data11 = ref([
  {
    a: '保守型',
    b: '15',
    c: '36',
    d: '反复验证风险，偏好成熟方案 ',
    e: '风险评估、流程合规',
    f: ' 决策缓慢，错失先机'
  },
  {
    a: '冒险型',
    b: '15',
    c: '15',
    d: '聚焦高收益，忽视潜在风险 ',
    e: '机会捕捉、创新突破',
    f: '资源浪费、风险失控'
  },
  {
    a: '折中型',
    b: '15',
    c: '15',
    d: '权衡利弊，追求 “安全与收益平衡” ',
    e: '资源匹配、策略微调',
    f: '缺乏主见，易陷入折中主义'
  },
  {
    a: '回避型',
    b: '15',
    c: '15',
    d: '拖延决策，依赖他人或集体决策',
    e: '无明显优势',
    f: '责任推诿，团队效率拖累'
  }
])

const columns12 = ref([
  {
    label: '决策模式',
    prop: 'a',
    width: 150
  },
  {
    label: '人员',
    prop: 'b'
  },
  {
    label: '改善建议',
    prop: 'c'
  }
])
const data12 = ref([
  {
    a: '保守型 （12人）',
    b: '王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）',
    c: '1.划清经验边界：填写《经验适用边界清单》，标注差异点与潜在风险，避免盲目复制。 2.反经验训练：每月推演 “非惯性做法”，输出《反经验行动清单》，强制尝试新策略。 3.新场景探索：分配 10% 时间接触跨界案例，撰写《旧经验失效分析》，打破路径依赖。'
  }
])

onMounted(() => {})
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">决策偏好分析</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        通过人才测评数据构建决策偏好分析模型，精准解析参评人员在不同业务场景下的决策倾向（如风险偏好、信息依赖、决策风格等）。基于分析结果，针对性制定决策能力提升方案，优化决策策略与协作机制，有效减少因决策偏好偏差导致的低效决策或策略失衡，助力团队决策与业务目标高效对齐。
      </div>
    </div>
    <div class="info_section_wrap three_seven_wrap justify-between">
      <div class="l_wrap">
        <div class="page-title-line">整体决策模式人数分布</div>
        <div class="chart_box"></div>
      </div>
      <div class="r_wrap">
        <div class="page-title-line">不同能力下的决策模式人数分布</div>
        <Table :roundBorder="false" :columns="columns7" :data="data7" headerColor showIndex> </Table>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">决策模式优劣分析（市场分析与战略规划）</div>
      <Table :roundBorder="false" :columns="columns8" :data="data8" headerColor> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">决策模式改善建议</div>
      <Table :roundBorder="false" :columns="columns9" :data="data9" headerColor> </Table>
    </div>
    <div class="info_section_wrap three_seven_wrap justify-between">
      <div class="l_wrap">
        <div class="page-title-line">整体决策风格人数分布</div>
        <div class="chart_box"></div>
      </div>
      <div class="r_wrap">
        <div class="page-title-line">不同能力下的决策模式人数分布</div>
        <Table :roundBorder="false" :columns="columns10" :data="data10" headerColor showIndex> </Table>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">决策风格优劣分析（市场分析与战略规划）</div>
      <Table :roundBorder="false" :columns="columns11" :data="data11" headerColor> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">决策风格改善建议</div>
      <Table :roundBorder="false" :columns="columns12" :data="data12" headerColor> </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../../../../style/common.scss';
@import './common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
