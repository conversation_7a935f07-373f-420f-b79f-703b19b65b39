<script setup>
defineOptions({ name: 'displayer' })
const route = useRoute()
console.log(route)

const stepList = ref([
  {
    id: 'scene',
    title: '能力显差概览',
    path: '/AI/displayer/overview'
  },
  {
    id: 'model',
    title: '流程赋能显差'
    // comp: defineAsyncComponent(() => import('./model.vue'))
  },
  {
    id: 'step',
    title: '了解评估步骤'
    // comp: defineAsyncComponent(() => import('./step.vue'))
  },
  {
    id: 'report',
    title: '了解评估报告'
    // comp: defineAsyncComponent(() => import('./report.vue'))
  }
])
</script>
<template>
  <div class="page-content">
    <!-- <div class="content-aside">
      <div class="aside-title">能力显差类型:</div>
      <div
        class="step-list"
        :class="{ active: route.path.indexOf(step.path) != -1 }"
        v-for="(step, index) in stepList"
        :key="step.id"
        @click="changeStep(step, index)"
      >
        {{ step.title }}
      </div>
    </div> -->
    <div class="content-mian">
      <router-view @setSecondName="setSecondName" @setThirdName="setThirdName" @setFourName="setFourName"></router-view>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.page-content {
  display: flex;
  align-items: flex-start;
  .content-aside {
    flex: 0 0 200px;
    padding: 24px 20px;
    background: linear-gradient(224deg, #d0e4f9 0%, rgba(195, 230, 255, 0.6) 100%);
    border-radius: 8px 8px 8px 8px;
    margin-right: 16px;
    .aside-title {
      font-size: 14px;
      color: #333333;
      line-height: 35px;
    }
    .step-list {
      width: 100%;
      line-height: 33px;
      background: #f0f9ff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #a5c1dc;
      text-align: center;
      cursor: pointer;
      margin-bottom: 10px;

      &.active {
        background: #83c1ff;
        border: 1px solid #83c1ff;
        color: #ffffff;
      }
    }
  }
  .content-mian {
    flex: 1;
  }
}
:deep(.next-btn) {
  width: 269px;
  line-height: 45px;
  font-size: 16px;
  color: #ffffff;
  text-align: center;
  background: #53a9f9;
  border-radius: 8px 8px 8px 8px;
  margin: 0 auto;
  cursor: pointer;
}
</style>
