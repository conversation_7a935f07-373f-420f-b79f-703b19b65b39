<template>
    <div class="talent_main">
        <div class="aside_filter_wrap">
            <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
        </div>
        <div class="talent_number_content page_section flex_row_wrap_start">
            <div class="content_item el-col-12">
                <div class="content_item_main">
                    <div class="content_item_title">岗位重要性</div>
                    <div class="content_item_content"></div>
                </div>
            </div>
            <div class="content_item el-col-12">
                <div class="content_item_main">
                    <div class="content_item_title">TOP 岗位</div>
                    <div class="content_item_content"></div>
                </div>
            </div>
            <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">各岗位平均晋升周期</div>
                    <div class="content_item_content"></div>
                </div>
            </div>
        </div>
    </div>
</template>
 
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js"
import { postImportance } from "../../../../../request/api.js"
import asideFilter from "@/components/talent/asideNav/asideFilter"

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref([])

const maritalData = reactive([
    { name: "非常重要", value: 40 },
    { name: "重要", value: 30 },
    { name: "一般", value: 20 },
    { name: "不重要", value: 10 }
])

const ageData = reactive([
    { name: "25以下", value: 6.7 },
    { name: "36~30", value: 14.5 },
    { name: "31~35", value: 15.0 },
    { name: "36~40", value: 13.1 },
    { name: "41~50", value: 33.2 },
    { name: "51以上", value: 17.4 }
])

const provinceData = reactive([
    { name: "吉林省", value: 14 },
    { name: "湖南省", value: 17 },
    { name: "西川省", value: 24 },
    { name: "河南省", value: 41 },
    { name: "贵州省", value: 91 },
    { name: "安徽省", value: 118 },
    { name: "云南省", value: 217 },
    { name: "浙江省", value: 441 }
])

const navData = reactive([
    {
        id: "1",
        name: "按组织架构",
        children: [
            { id: "1-1", name: "组织架构1" },
            { id: "1-2", name: "组织架构2" },
            { id: "1-3", name: "组织架构3" },
            { id: "1-4", name: "组织架构4" }
        ]
    },
    {
        id: "2",
        name: "按职务类型",
        children: [
            { id: "2-1", name: "全部" },
            { id: "2-2", name: "战略运营类" },
            { id: "2-3", name: "市场营销类" },
            { id: "2-4", name: "供应链类" },
            { id: "2-5", name: "业务流程设置" }
        ]
    },
    {
        id: "3",
        name: "研发技术类",
        children: [
            { id: "3-1", name: "研发技术1" },
            { id: "3-2", name: "研发技术2" }
        ]
    }
])

const jobClass = reactive({
    data: []
})

const jobLevel = reactive({
    data: []
})

const jobGrade = reactive({
    data: []
})

const initChart = () => {
    echartsRenderPage("job_class", "Ring", "350", "250", jobClass)
    echartsRenderPage("job_level", "YBar", "350", "250", jobLevel)
    echartsRenderPage("job_grade", "XBar", "770", "250", jobGrade)
}

const postImportanceFun = async () => {
    try {
        const params = {
            enqId: enqId.value,
            jobClassCode: jobClassCode.value,
            orgCode: orgCode.value
        }
        const res = await postImportance(params)
        if (res.code == "200") {
            const data = res.data
            jobLevel.data = data.jobLevel
            jobClass.data = data.jobClass
            jobGrade.data = data.jobGrade
            initChart()
        }
    } catch (error) {
        console.error(error)
    }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
    jobClassCode.value = jobClassCodeVal
    orgCode.value = orgCodeVal
    postImportanceFun()
}

onMounted(() => {
    enqId.value = route.query.enqId
    filterData.value = route.attrs.filterData
    postImportanceFun()
})
</script>
 
<style scoped lang="scss">
</style>