<template>
  <div class="bg_write personalReview-wrap">
    <div class="page_main_title">
      <div class="goback_geader" @click="goback()"><i class="el-icon-arrow-left"></i>返回</div>
      个人盘点--{{ enqName }}
    </div>
    <div class="page_section">
      <div class="talent_raview_main">
        <step-bar
          :needClick="enqUserStatus !== 'P'"
          @stepClick="stepClick"
          :labelKey="'enqModuleName'"
          :stepData="stepData"
          :currentIndex="currentModuleCode"
        ></step-bar>
        <component
          :is="moduleObj[currentModuleCode]"
          :nextBtnText="nextBtnText"
          :enqId="enqId"
          :currentIndex="currentModuleCode"
          :currentFirstCode="currentFirstCode"
          @nextStep="nextStep"
          @prevStep="prevStep"
          @nextStepClick="personalNextStepFun"
        ></component>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { personalNextStep, getPersonModule, submitPersonnel, getEnqUserInfo, getEnqInfo } from '../../request/api'
import stepBar from '@/components/talent/stepsComps/stepBar.vue'
import PRbasicInfo from './personalReviewComponents/PRbasicInfo' //基本信息
import PRworkActivities from './personalReviewComponents/PRworkActivities' //工作活动
import PReduInfo from './personalReviewComponents/PReduInfo' //教育信息
import PRworkExperience from './personalReviewComponents/PRworkExperience' //工作履历
import PRperformanceInfo from './personalReviewComponents/PRperformanceInfo' //指标信息
import PRtrainInfo from './personalReviewComponents/PRtrainInfo' //培训信息
import PRawardInformation from './personalReviewComponents/PRawardInformation' //获奖信息
import PRpersonalPlanning from './personalReviewComponents/PRpersonalPlanning' //个人规划
import PRpersonalWork from './personalReviewComponents/PRpersonalWork' //个人工作
import PRqualityEvaluate from './personalReviewComponents/PRqualityEvaluate' //素质评价
import PRperformanceEvaluate from './personalReviewComponents/PRperformanceEvaluate' //业绩评价
import TargetAndResult from './personalReviewComponents/TargetAndResult' //目标与结果
import PRworkFeel from './personalReviewComponents/PRworkFeel' //工作感受
import PRsurvey from './personalReviewComponents/PRsurvey' //满意度
import PRworkDrive from './personalReviewComponents/PRworkDrive' //工作驱动
import PRpersonalRecruitment from './personalReviewComponents/PRpersonalRecruitment' //人员招募
import PRKPIEvaluate from './personalReviewComponents/PRKPIEvaluate.vue' //kpi评价
import PRcoord from './personalReviewComponents/PRcoord.vue' //协同网络
import PRocclValues from './personalReviewComponents/PRocclValues.vue' //协同网络
import PRCoreQuality from './personalReviewComponents/PRCoreQuality.vue' //协同网络

const moduleObj = {
  PN01: PRbasicInfo,
  PN02: PReduInfo,
  PN03: PRworkExperience,
  PN04: PRtrainInfo,
  PN05: PRawardInformation,
  PN06: PRpersonalWork,
  PN07: PRpersonalPlanning,
  PN08: PRqualityEvaluate,
  PN09: PRperformanceEvaluate,
  PN10: TargetAndResult,
  PN11: PRKPIEvaluate,
  PN12: PRworkFeel,
  PN13: PRsurvey,
  PN14: PRworkDrive,
  PN15: PRpersonalRecruitment,
  PN16: PRcoord,
  PN17: PRocclValues,
  PN18: PRCoreQuality
}

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

// 响应式状态
const enqId = ref(null)
const enqName = ref('')
const enqUserStatus = ref('U')
const moduleArr = ref([]) //按顺序储存盘点模块的code
const currentModuleCode = ref('') //当前显示的模块code
const currentIndex = ref(0) //用来改变当前显示的模块code，从moduleArr中获取
const currentFirstCode = ref('')
const nextBtnText = ref('下一步')
const stepData = ref([])

// 计算属性
const userId = computed(() => userStore.userInfo.userId)

// 方法
const getEnqInfoFun = async () => {
  const res = await getEnqInfo({ id: enqId.value })
  if (res.code == '200') {
    enqName.value = res.data.enqName
  }
}

const getPersonModuleFun = async () => {
  const res = await getPersonModule({
    enqId: enqId.value
  })
  moduleArr.value = []
  if (res.code == '200') {
    res.data.forEach(item => {
      moduleArr.value.push(item.enqModuleCode)
      item['name'] = item.enqModuleName
      item['code'] = item.enqModuleCode
    })
    stepData.value = res.data
    currentModuleCode.value = moduleArr.value[currentIndex.value]
    //取第一个code
    if (moduleArr.value && moduleArr.value.length != 0) {
      currentFirstCode.value = moduleArr.value[0]
    }
    if (currentIndex.value == stepData.value.length - 1) {
      nextBtnText.value = '提交'
    } else {
      nextBtnText.value = '下一步'
    }
  }
}

const getEnqUserInfoFun = async () => {
  const res = await getEnqUserInfo({
    enqId: enqId.value
  })
  if (res.code == '200') {
    ElMessage.success('获取信息成功')
    const data = res.data
    enqUserStatus.value = data.enqUserStatus
  } else {
    ElMessage.error(res.msg)
  }
}

const personalNextStepFun = async params => {
  await personalNextStep(params)
}

const stepClick = (stepCode, index) => {
  currentIndex.value = index
}

const nextStep = async () => {
  const params = {
    enqId: enqId.value,
    module: currentModuleCode.value
  }
  await personalNextStepFun(params)

  if (currentIndex.value == stepData.value.length - 1) {
    // 最后一步提交
    await submitPersonnelEnq()
    return false
  }
  stepData.value[currentIndex.value].enqProgress = 'Y'
  currentIndex.value++
  // 设置按钮文本 "下一步" or "提交"
  if (currentIndex.value == stepData.value.length - 1) {
    nextBtnText.value = '提交'
  } else {
    nextBtnText.value = '下一步'
  }
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

const prevStep = () => {
  if (currentIndex.value == 0) {
    return false
  }
  currentIndex.value--
  if (currentIndex.value == stepData.value.length - 1) {
    nextBtnText.value = '提交'
  } else {
    nextBtnText.value = '下一步'
  }
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

const submitPersonnelEnq = async () => {
  const params = {
    enqId: enqId.value,
    userId: userId.value
  }
  const res = await submitPersonnel(params)
  if (res.code == '200') {
    ElMessage.success(res.msg)
    goback()
  } else {
    ElMessage.error(res.msg)
  }
}

const goback = () => {
  router.push('/talentReviewHome/talentReview')
}

// 监听器
watch(currentIndex, val => {
  currentModuleCode.value = moduleArr.value[currentIndex.value]
  if (currentIndex.value == stepData.value.length - 1) {
    nextBtnText.value = '提交'
  } else {
    nextBtnText.value = '下一步'
  }
})

// 生命周期钩子
onMounted(() => {
  enqId.value = route.query.enqId
  getEnqInfoFun()
  getEnqUserInfoFun()
  getPersonModuleFun()
})
</script>

<style scoped lang="scss">
.personalReview-wrap {
  .page_section {
    height: 720px;
    overflow: auto;
  }
}
.talent_raview_main {
  .talent_raview_btn_wrap {
    text-align: center;
  }
}

.from_wrap {
  .basic_info {
    float: left;
    width: 50%;
  }

  .post_info {
    overflow: hidden;
  }

  :deep(.el-input__inner) {
    width: 280px;
  }
}

:deep(.oper_btn_wrap) {
  padding-top: 32px;
}
// 去除input number类型 加减箭头
:deep(input::-webkit-outer-spin-button),
:deep(input::-webkit-inner-spin-button) {
  -webkit-appearance: none;
}
:deep(input) {
  -moz-appearance: textfield;
}
</style>
