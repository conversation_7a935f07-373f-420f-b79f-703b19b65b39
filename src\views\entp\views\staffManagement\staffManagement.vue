<template>
  <div class="staff_management_wrap bg_write">
    <div class="page_main_title">员工管理</div>
    <div class="page_section staff_management_center clearfix">
      <div class="page_section_aside">
        <div class="aside_tree_title flex_row_between">
          <div>部门分类</div>
        </div>
        <div class="aside_tree_list">
          <tree-comp-radio
            v-model="defaultCheckedKeys"
            :treeData="treeData"
            :defaultExpandAll="false"
            :expandedLevel="2"
            @clickCallback="clickCallback"
            :needCheckedFirstNode="true"
          ></tree-comp-radio>
        </div>
      </div>
      <div class="table_wrap page_section_main page_shadow">
        <div class="filter_bar_wrap">
          <div class="filter_item title">筛选</div>
          <!-- <div class="filter_item">
                        <el-cascader
                            clearable
                            :show-all-levels="false"
                            :props="{
                                value: 'code',
                                label: 'value',
                                checkStrictly: 'true',
                                expandTrigger: 'hover',
                            }"
                            v-model="postCode"
                            placeholder="选择岗位"
                            :options="postTreeOptions"
                            @change="sascaderChange($event, 'postCode')"
                        ></el-cascader>
                    </div> -->
          <div class="filter_item">
            <el-select v-model="postCode" placeholder="岗位" clearable>
              <el-option
                v-for="item in postOptions"
                :label="item.codeName"
                :value="item.dictCode"
                :key="item.dictCode"
              ></el-option>
            </el-select>
          </div>
          <div class="filter_item">
            <el-cascader
              clearable
              :show-all-levels="false"
              :props="{
                value: 'code',
                label: 'value',
                checkStrictly: 'true',
                expandTrigger: 'hover'
              }"
              v-model="jobLevelCode"
              placeholder="选择职层"
              :options="jobLevelTreeData"
              @change="sascaderChange($event, 'jobLevelCode')"
            ></el-cascader>
          </div>
          <div class="filter_item">
            <el-select v-model="orgLeader" placeholder="是否部门负责人" clearable>
              <el-option
                v-for="item in yesOrNo"
                :label="item.codeName"
                :value="item.dictCode"
                :key="item.dictCode"
              ></el-option>
            </el-select>
          </div>
          <div class="filter_item">
            <el-select v-model="employeeStatus" placeholder="人员状态" clearable>
              <el-option
                v-for="item in employeeOptions"
                :label="item.codeName"
                :value="item.dictCode"
                :key="item.dictCode"
              ></el-option>
            </el-select>
          </div>
          <div class="filter_item">
            <el-input v-model="likeName" placeholder="按姓名查询" suffix-icon="el-icon-search" clearable></el-input>
          </div>
        </div>
        <div class="control_btn">
          <div class="filter_item">
            <el-button type="primary" class="page_add_btn" @click="search()">查询</el-button>
          </div>
          <div class="filter_item">
            <el-button type="primary" class="page_add_btn" @click="newlyIncreasedStaff">新增</el-button>
          </div>
          <div class="filter_item">
            <el-button type="primary" class="page_add_btn" @click="importStaffDialog = true">导入</el-button>
          </div>
          <div class="filter_item">
            <el-button type="primary" class="page_add_btn" @click="exportData">导出</el-button>
          </div>
        </div>
        <div class="selected_res_wrap" v-show="resShow">
          <div class="filter_bar_wrap">
            <div>
              <span class="filter_item title">已选中:</span>
              <span class="filter_item">{{ resLength }} 项</span>
            </div>

            <div class="filter_item">
              <el-button type="primary" class="page_add_btn" @click="deleteStaff">删除</el-button>
              <el-button type="primary" class="page_add_btn" @click="editStaffFun">编辑</el-button>
              <el-button type="primary" class="page_add_btn" @click="assignRoles">分配角色</el-button>
              <el-button type="primary" class="page_add_btn" @click="setQuit">设置离职</el-button>
              <el-button type="primary" class="page_add_btn" @click="resetPassword">重置密码</el-button>
            </div>
          </div>
        </div>
        <TableComponent
          :selectionStatus="true"
          @selectionChange="getSelectedId"
          :needIndex="true"
          :tableData="tableData"
          :size="'mini'"
          :needPagination="true"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></TableComponent>
        <!-- <coustomPagination :total="total" @pageChange="pageChange"></coustomPagination> -->
      </div>
    </div>

    <!-- 分配角色 弹窗 -->
    <el-dialog title="角色分配" v-model:visible="assignRolesDialog">
      <div class="assign_roles_content">
        <div class="title">分配人员：</div>
        <div class="staff_list">
          <span v-if="selectedId.length > 6"
            >{{ selectedName[0] }}、{{ selectedName[1] }} 等 {{ selectedId.length }}人</span
          >
          <div v-else>
            <span v-for="item in selectedName" :key="item">{{ item }}、</span>
            <span>{{ selectedName.length }}人</span>
          </div>
        </div>
        <div class="title">选择角色</div>
        <div class="role_wrap">
          <div class="role_content">
            <el-select v-model="defaultRoleKey" multiple placeholder="选择角色" clearable>
              <el-option
                v-for="item in roleOptions"
                :label="item.roleName"
                :value="item.roleId"
                :key="item.roleId"
              ></el-option>
            </el-select>
          </div>
        </div>

        <div class="dialog_btn align_right">
          <el-button type="primary" @click="submitAssignRoles">保存</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 导入员工弹窗 -->
    <el-dialog
      title="开始导入"
      v-model:visible="importStaffDialog"
      width="width"
      :before-close="importStaffDialogBeforeClose"
    >
      <div class="import_staff_wrap">
        <div class="import_staff_title">操作步骤:</div>
        <div class="oper_step">
          <p>1、下载《人员批量导入模板》</p>
          <p>2、打开下载表，将对应字段信息输入或粘贴进本表，为了保障粘贴信息被有效导入，请使用纯文本或者数字。</p>
          <p>3、信息输入完毕，点击“选择文件”按钮，选择excel文档。</p>
          <p>4、点击"开始导入",导入中如有任何疑问，请致电000000000。</p>
        </div>
        <a class="fs16 main_color download_file" href="/edp/api/entp/userTemplate.xlsx" download="人员批量导入模板.xlsx"
          >立即下载《人员批量导入模板》</a
        >
        <div class="upload_file_wrap">
          <el-input placeholder="请输入内容" v-model="fileName" readonly>
            <template v-slot:append>
              <label for="up" class="upload_label">
                选择文件
                <!-- <el-button type="primary">选择文件</el-button> -->
                <input
                  id="up"
                  style="display: none"
                  ref="file"
                  type="file"
                  class="form-control page_clear_btn"
                  @change="fileChange"
                />
              </label>
            </template>
          </el-input>
        </div>
        <div class="import_staff_title"><span class="error_icon">∗</span> 当手机号重复时:</div>
        <div class="marginT_16">
          <el-radio-group v-model="phoneType" @change="checkImportType">
            <el-radio label="Y">覆盖更新</el-radio>
            <el-radio label="N">不导入</el-radio>
          </el-radio-group>
        </div>
      </div>
      <template v-slot:footer>
        <div>
          <el-button class="page_clear_btn" @click="importStaffDialog = false">取 消</el-button>
          <el-button type="primary" class="page_add_btn" @click="importStaff">开始导入</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 设置离职 -->
    <el-dialog title="设置离职" v-model:visible="quitDialog">
      <el-form>
        <el-form-item class="form_item" label="选择离职时间：">
          <el-date-picker
            v-model="currentQuitDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button class="page_clear_btn" @click="quitDialog = false">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="confirmQuitDate">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 重置密码 -->
    <el-dialog title="重置密码" v-model:visible="resetPasswordDialog">
      <el-form>
        <el-form-item class="form_item" label="新密码：">
          <el-input v-model="newPassword" placeholder="数字、字母、符号两种及以上的组合,6-20个字符"></el-input>
        </el-form-item>
        <el-form-item class="form_item" label="新密码确认：">
          <el-input v-model="newPasswordConfirm" placeholder="数字、字母、符号两种及以上的组合,6-20个字符"></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button class="page_clear_btn" @click="resetPasswordDialog = false">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="confirmResetPassword">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Setting, Edit, Delete } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/modules/user'
import {
  getStaff,
  editUserRole,
  editStaff,
  delStaff,
  readExcelData,
  getOrgDeptTree,
  jobLevelTree,
  getPostTree,
  getPostList,
  modifyCurrentQuitDate,
  editAccountInfo,
  getcompanyRoleList,
  getDictList,
  exportDataConfirm,
  exportDownload
} from '../../request/api'
import TreeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import TableComponent from '@/components/talent/tableComps/tableComponent'

const router = useRouter()
const route = useRoute()

// 响应式数据
const useCache = ref(route.params.useCache || false)
const yesOrNo = ref([])
const employeeOptions = ref([])
const defaultCheckedKeys = ref('')
const orgCode = ref('') // 组织树返回
const postCode = ref('')
const postOptions = ref('')
const postTreeOptions = ref([])
const jobLevelCode = ref('')
const jobLevelTreeData = ref([])
const orgLeader = ref('')
const employeeStatus = ref('')
const likeName = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 弹窗相关
const resShow = ref(true)
const resLength = ref(0)
const selectedId = ref([])
const selectedName = ref([])
const assignRolesDialog = ref(false)
const selectedRole = ref('')
const defaultRoleKey = ref('')
const roleOptions = ref([])

// 设置离职
const quitDialog = ref(false)
const currentQuitDate = ref('')

// 重置密码
const resetPasswordDialog = ref(false)
const newPassword = ref('')
const newPasswordConfirm = ref('')

// 导入员工弹窗
const importStaffDialog = ref(false)
const formData = ref(new FormData())
const fileName = ref('')
const phoneType = ref('')
const uploadFile = ref(null)
const treeData = ref([])

const employeeObj = reactive({})

// 表格数据
const tableData = reactive({
  columns: [
    {
      label: '员工姓名',
      prop: 'userName',
      width: 100
    },
    {
      label: '手机号码',
      prop: 'phoneNumber',
      width: 120
    },
    {
      label: '所属组织',
      prop: 'orgName'
    },
    {
      label: '任职岗位',
      prop: 'postName'
    },
    {
      label: '职层',
      prop: 'jobLevelName'
    },
    {
      label: '部门负责人',
      prop: 'orgLeader',
      width: 80,
      formatterFun: function (row, column, cellValue, index) {
        return cellValue == 'Y' ? '是' : '否'
      }
    },
    {
      label: '上级岗位',
      prop: 'parentPostName'
    },
    {
      label: '上级姓名',
      prop: 'parentName'
    },
    {
      label: '角色',
      prop: 'roleName'
    },
    {
      label: '最后登陆时间',
      prop: 'lastLoginTime',
      width: 120
    },
    {
      label: '人员状态',
      prop: 'employeeStatus',
      width: 80,
      formatterFun: function (row, column, cellValue, index) {
        return employeeObj[cellValue]
      }
    }
  ],
  page: {
    total: 0,
    current: 1,
    size: 10
  },
  data: []
})

// 计算属性
const userStore = useUserStore()
const userId = computed(() => userStore.userInfo.userId)
const companyId = computed(() => userStore.userInfo.companyId)

// 初始化数据
async function init() {
  console.log('init')
  let dicts = await getDictList({ dictIds: 'EMPLOYEE_STATUS' })
  let res = dicts.data['EMPLOYEE_STATUS']
  res.forEach(item => {
    employeeObj[item.dictCode] = item.codeName
  })

  let jobLevelTreeRes = await jobLevelTree()
  jobLevelTreeData.value = jobLevelTreeRes

  let orgTree = await getOrgDeptTree({ companyId: companyId.value })
  console.log(orgTree)
  if (orgTree.code == 200) {
    treeData.value = orgTree.data.length > 0 ? orgTree.data : []
  } else {
    treeData.value = []
  }
  if (useCache.value) {
    readCacheParams()
  }
  // 清除state.setStaffId
  userStore.createStaffId = null
}

function readCacheParams() {
  console.log('setParams')
  let cacheParams = userStore.getParams('staffMParams')
  currentPage.value = cacheParams.current
  pageSize.value = cacheParams.size
  orgCode.value = cacheParams.orgCode
  defaultCheckedKeys.value = cacheParams.orgCode
  jobLevelCode.value = cacheParams.jobLevelCode
  likeName.value = cacheParams.likeName
  orgLeader.value = cacheParams.orgLeader
  employeeStatus.value = cacheParams.employeeStatus
}

function handleCurrentChange(val) {
  currentPage.value = val
  getStaffData()
}

function handleSizeChange(val) {
  pageSize.value = val
  getStaffData()
}

// 树点击回调事件
function clickCallback(val, isLastNode) {
  orgCode.value = val
  if (!useCache.value) {
    tableData.page.current = 1
    currentPage.value = 1
  }
  getStaffData()
  getPostListFun()
}

function getJobLevelTreeFun() {
  jobLevelTree().then(res => {
    jobLevelTreeData.value = res
  })
}

// 岗位列表
function getPostListFun() {
  getPostList({
    orgCode: orgCode.value
  }).then(res => {
    console.log(res)
    if (res.code == 200) {
      postOptions.value = res.data.map(item => {
        return {
          codeName: item.postName,
          dictCode: item.postCode
        }
      })
    }
  })
}

function getPostTreeFun() {
  getPostTree().then(res => {
    postTreeOptions.value = res
  })
}

function sascaderChange(val, key) {
  if (key == 'postCode') {
    postCode.value = val[val.length - 1]
  } else if (key == 'jobLevelCode') {
    jobLevelCode.value = val[val.length - 1]
  }
}

function getOrgTreeFun() {
  getOrgDeptTree({
    companyId: companyId.value
  }).then(res => {
    if (res.length > 0) {
      treeData.value = res
    } else {
      treeData.value = []
    }
  })
}

function importStaff() {
  console.log(uploadFile.value)
  if (!phoneType.value) {
    ElMessage.warning('请选择手机号重复时导入方式!')
    return
  }
  readExcelData(formData.value).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      uploadFile.value = null
      if (res.data.obj.length > 0) {
        router.push({
          name: 'staffImportView',
          params: {
            data: res.data.obj
          }
        })
      } else {
        importStaffDialog.value = false
        getStaffData()
      }
    } else {
      ElMessage.error('模板文件格式不正确，请重新下载模板文件')
    }
  })
  importStaffDialog.value = true
}

function importStaffDialogBeforeClose(done) {
  fileName.value = ''
  document.getElementById('up').value = null
  done()
}

function fileChange(e) {
  let file = e.target.files[0]
  formData.value = new FormData()
  formData.value.append('file', file)
  fileName.value = file.name
  uploadFile.value = formData.value
}

function checkImportType(val) {
  if (formData.value.get('coverUpdate')) {
    formData.value.set('coverUpdate', val)
  } else {
    formData.value.append('coverUpdate', val)
  }
  uploadFile.value = formData.value
}

function newlyIncreasedStaff() {
  router.push({
    path: '/basicSettingHome/staffManagement/createStaff',
    query: {
      orgCode: orgCode.value
    }
  })
}

function search() {
  currentPage.value = 1
  getStaffData()
}

// 导出
function exportData() {
  exportDataConfirmFun()
}

function exportDataConfirmFun() {
  exportDataConfirm({
    companyId: companyId.value,
    orgCode: orgCode.value,
    jobLevelCode: jobLevelCode.value,
    likeName: likeName.value,
    orgLeader: orgLeader.value,
    employeeStatus: employeeStatus.value,
    postCode: postCode.value
  }).then(res => {
    if (res.code == 200) {
      exportDownloadFun(res.data)
    } else {
      ElMessage.warning(res.msg)
    }
  })
}

function exportDownloadFun(val) {
  exportDownload({
    fileName: val
  }).then(res => {
    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '人员列表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  })
}

function getStaffData(val, isPageChange = false) {
  console.log('getStaff')
  let data = {
    companyId: companyId.value,
    current: currentPage.value,
    size: pageSize.value,
    orgCode: orgCode.value,
    jobLevelCode: jobLevelCode.value,
    likeName: likeName.value,
    orgLeader: orgLeader.value,
    employeeStatus: employeeStatus.value,
    postCode: postCode.value
  }

  let cacheParams = userStore.getParams('staffMParams')

  let params = {}

  // 页面初始加载
  if (useCache.value) {
    params = { ...data, ...cacheParams }
  } else {
    params = { ...cacheParams, ...data }
  }
  let obj = {
    params: params,
    stateKey: 'staffMParams'
  }
  console.log(params)

  // 缓存筛选条件
  userStore.setParams(obj)
  getStaff(params).then(res => {
    if (res.code == '200') {
      if (res.data) {
        tableData.data = res.data
        tableData.page.total = res.total
        ElMessage.success('获取成功！')
      } else {
        tableData.data = []
        tableData.page.total = res.total
      }
      useCache.value = false
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function getSelectedId(select) {
  selectedId.value = []
  selectedName.value = []
  resLength.value = select.length
  if (select.length > 0) {
    select.forEach(item => {
      let id = item.userId
      let name = item.userName
      selectedId.value.push(id)
      selectedName.value.push(name)
    })
  }
}

function deleteStaff() {
  if (resLength.value == 0) {
    ElMessage.warning('当前未选中人员')
    return
  }
  let index = selectedId.value.indexOf(userId.value)
  if (index !== -1) {
    ElMessage.warning('不能删除当前账户员工！')
    return
  }
  ElMessageBox.confirm('确定删除选中人员?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      let params = {
        userIds: selectedId.value.join(',')
      }
      delStaff(params).then(res => {
        if (res.code == '200') {
          getStaffData()
          ElMessage.success(res.msg)
        } else {
          ElMessage.error(res.msg)
        }
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      })
    })
}

function editStaffFun() {
  let length = selectedId.value.length
  if (length == 0) {
    ElMessage.warning('请选择要编辑的员工')
    return
  }
  if (length > 1) {
    ElMessage.warning('一次只能编辑一名员工')
    return
  }
  router.push(`/basicSettingHome/staffManagement/editStaff?userId=${selectedId.value[0]}`)
}

function assignRoles() {
  let length = selectedId.value.length
  if (length == 0) {
    ElMessage.warning('请选择要修改的员工')
    return
  }
  getcompanyRoleList().then(res => {
    if (res.code == '200') {
      roleOptions.value = res.data
    }
  })
  assignRolesDialog.value = true
}

function selectRole(role) {
  defaultRoleKey.value = role.key
}

function submitAssignRoles() {
  let params = {
    roleIds: defaultRoleKey.value.join(','),
    userIds: selectedId.value.join(',')
  }
  editUserRole(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      getStaffData()
      assignRolesDialog.value = false
    } else {
      ElMessage.success(res.msg)
    }
  })
}

function setQuit() {
  let length = selectedId.value.length
  if (length == 0) {
    ElMessage.warning('请选择要设置的员工')
    return
  }
  if (length > 1) {
    ElMessage.warning('一次只能设置一名员工')
    return
  }
  quitDialog.value = true
}

function confirmQuitDate() {
  let params = {
    currentQuitDate: currentQuitDate.value,
    userId: selectedId.value[0]
  }
  modifyCurrentQuitDate(params).then(res => {
    if (res.code == '200') {
      currentQuitDate.value = ''
      ElMessage.success(res.msg)
      getStaffData()
      quitDialog.value = false
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function resetPassword() {
  let length = selectedId.value.length
  if (length == 0) {
    ElMessage.warning('请选择要修改的员工')
    return
  }
  if (length > 1) {
    ElMessage.warning('一次只能设置一名员工')
    return
  }
  resetPasswordDialog.value = true
}

function confirmResetPassword() {
  if (newPassword.value == '' || newPasswordConfirm.value == '') {
    ElMessage.warning('请输入密码！')
  }
  if (newPasswordConfirm.value !== newPassword.value) {
    ElMessage.warning('两次输入密码不一致！')
    return
  }
  let params = {
    type: 'resetPasswd',
    userPasswd: newPassword.value,
    userId: selectedId.value[0]
  }
  editAccountInfo(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      getStaffData()
      newPassword.value = ''
      newPasswordConfirm.value = ''
      resetPasswordDialog.value = false
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 监听 companyId
watch(
  () => companyId.value,
  val => {
    if (val) {
      init()
    }
  },
  { immediate: true }
)

// 生命周期钩子
onMounted(() => {
  // init()
})
</script>

<style scoped lang="scss">
.aside_wrap {
  float: left;
  width: 220px;
  border: 1px solid #e5e5e5;
}
.filter_item {
  margin-right: 10px;
}
.table_wrap {
  overflow-x: hidden;
  .control_btn {
    display: flex;
    justify-content: flex-end;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;
  }
}

.selected_res_wrap {
  margin-top: 20px;
  .filter_item.title {
    min-width: 50px;
  }
}

// 角色分配弹窗样式
.assign_roles_content {
  font-weight: bold;

  .title,
  .staff_list {
    margin-bottom: 8px;
  }

  .role_content {
    font-weight: normal;
    .el-select {
      width: 80%;
    }

    .item {
      width: 200px;
      line-height: 38px;
      margin-bottom: 8px;
      background: #f8f8f8;
      padding-left: 50px;
      cursor: pointer;

      &.selected {
        color: #0099fd;
        background: #eaf6fc;
      }
    }
  }
}

// 导入员工
.import_staff_wrap {
  .import_staff_title {
    color: #515c71;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  .error_icon {
    display: inline-block;
    color: #f56c6c;
    font-size: 18px;
  }
  .oper_step {
    line-height: 28px;
    color: #515c71;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .download_file {
    display: block;
    margin-bottom: 16px;
  }

  .upload_file_wrap {
    margin-bottom: 16px;

    .upload_label {
      display: block;
      height: 28px;
      line-height: 28px;
      width: 100%;
      cursor: pointer;
    }
  }
}
.el-dialog__header {
  background-color: #ebf4ff;
  padding: 15px 20px;
}
</style>
