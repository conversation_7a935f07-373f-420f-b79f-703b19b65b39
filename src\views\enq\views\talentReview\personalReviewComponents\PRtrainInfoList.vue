<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in trainData" :key="item.id">
      <el-input class="item" v-model="item.trainingName" placeholder="填写培训名称"></el-input>
      <!-- <el-date-picker class="item" v-model="item.date" type="date" placeholder="选择日期"></el-date-picker> -->
      <!-- <el-date-picker v-model="item.trainingDate" type="month" placeholder="选择月"></el-date-picker> -->
      <el-date-picker
        class="item"
        value-format="YYYY-MM-DD"
        v-model="item.trainingDate"
        type="month"
        placeholder="选择日期"
      ></el-date-picker>
      <el-select class="item" v-model="item.trainingCourse">
        <el-option
          v-for="item in trainingCourseOptions"
          :key="item.dictCode"
          :label="item.codeName"
          :value="item.dictCode"
        ></el-option>
      </el-select>
      <el-select class="item" v-model="item.trainingType">
        <el-option
          v-for="item in trainingTypeOptions"
          :key="item.dictCode"
          :label="item.codeName"
          :value="item.dictCode"
        ></el-option>
      </el-select>
      <div class="item item_icon_wrap">
        <el-icon class="item_icon" @click="deleteItem(item, index)"><Delete /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { Delete } from '@element-plus/icons-vue'
const userStore = useUserStore()
const props = defineProps({
  trainData: {
    type: Array,
    default: () => [
      {
        id: '1',
        schoolName: '',
        graduationDate: '',
        education: '',
        post: '',
        industry: ''
      }
    ]
  }
})
const emit = defineEmits(['deleteItem'])

const trainingCourseOptions = ref([])
const trainingTypeOptions = ref([])

onMounted(async () => {
  const res = await userStore.getDocList(['TRAINING_COURSE', 'TRAINING_TYPE'])
  trainingCourseOptions.value = res.TRAINING_COURSE
  trainingTypeOptions.value = res.TRAINING_TYPE
})

function deleteItem(item, index) {
  emit('deleteItem', item, index)
}
</script>

<style scoped lang="scss">
.edu_info_item {
  .item {
    // flex: 1;
    width: 25%;
  }
  .item_icon_wrap {
    text-align: center;
    width: 10%;
    padding-top: 2px;
    color: #0099fd;
    font-size: 20px;
  }
}
</style>
