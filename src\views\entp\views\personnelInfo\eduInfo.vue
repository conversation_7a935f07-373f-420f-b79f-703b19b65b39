<template>
  <div class="edu_info_wrap bg_write">
    <div class="page_main_title">教育信息</div>
    <div class="page_section clearfix">
      <div class="oper_btn_wrap">
        <el-button type="primary" class="page_add_btn" @click="addItem">新增</el-button>
      </div>
      <div class="edu_info_center">
        <div class="edu_info_header">
          <div class="item school_name">毕业院校</div>
          <div class="item">毕业日期</div>
          <div class="item">学历</div>
          <div class="item">最高学历</div>
          <div class="item">与当前岗位相关</div>
          <div class="item">与当前行业相关</div>
          <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
          <div class="table_wrap">
            <edu-info-item :eduInfoData="eduInfoData" v-on:deleteItem="deleteItem"></edu-info-item>
          </div>
          <div class="pagination_wrap">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="page.current"
              :page-sizes="[10, 20, 50, 100, 200]"
              :page-size="page.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="page.total"
            ></el-pagination>
          </div>
          <div class="align_center paddT_12" v-if="eduInfoData.length > 0">
            <el-button type="primary" class="page_confirm_btn" @click="submit">确认</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import eduInfoItem from './components/eduInfoItem.vue'
import { addEducation, getEducation, delEducation } from '../../request/api'
// 假设有 util 工具函数
import { objHasEmpty } from '@/utils/utils'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const userId = computed(() => userStore.userInfo.userId)

const submitFlag = ref(true)
const eduInfoData = ref([])
const page = reactive({
  current: 1,
  size: 10,
  total: 0
})

function deleteItem(item, index) {
  if (!Object.prototype.hasOwnProperty.call(item, 'educationId')) {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        eduInfoData.value.splice(index, 1)
        ElMessage({ type: 'success', message: '删除成功!' })
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消删除' })
      })
  } else {
    const educationId = item.educationId
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        delEducation({ educationId, userId: userId.value }).then(res => {
          if (res.code == '200') {
            page.current = 1
            getEducationData()
            ElMessage.success(res.msg)
          } else {
            ElMessage.error(res.msg)
          }
        })
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消删除' })
      })
  }
}

function addItem() {
  const obj = eduInfoData.value[eduInfoData.value.length - 1]
  const addObj = {
    graduateSchool: '',
    graduateDate: '',
    qualification: '',
    highestQualification: '',
    postRelated: '',
    industryRelated: '',
    userId: userId.value
  }
  if (!obj) {
    eduInfoData.value.push(addObj)
    return
  }
  if (checkData(eduInfoData.value)) {
    ElMessage({ message: '请完善当前信息后新增！', type: 'warning' })
    return
  }
  eduInfoData.value.push(addObj)
}

function submit() {
  if (!submitFlag.value) return
  if (checkData(eduInfoData.value)) {
    ElMessage.warning('请完善信息后提交！')
    return
  }
  if (eduInfoData.value.length == 0) {
    ElMessage.warning('请新增后提交！')
    return
  }
  submitFlag.value = false
  addEducation(eduInfoData.value).then(res => {
    if (res.code == 200) {
      ElMessage.success(res.msg)
      getEducationData()
      submitFlag.value = true
    } else {
      submitFlag.value = true
      ElMessage.error(res.msg)
    }
  })
}

function getEducationData() {
  getEducation({
    current: page.current,
    size: page.size,
    userId: userId.value
  }).then(res => {
    eduInfoData.value = []
    if (res.code == 200) {
      if (res.data.length > 0) {
        eduInfoData.value = res.data.map(item => ({
          graduateSchool: item.graduateSchool,
          graduateDate: item.graduateDate,
          qualification: item.qualification,
          highestQualification: item.highestQualification,
          postRelated: item.postRelated,
          industryRelated: item.industryRelated,
          userId: userId.value,
          educationId: item.educationId
        }))
      }
      Object.assign(page, res.page)
    } else {
      page.current = 1
      page.size = 10
      page.total = 0
      ElMessage.error('获取数据失败!')
    }
  })
}

function checkData(data) {
  for (let i = 0; i < data.length; i++) {
    if (objHasEmpty(data[i])) {
      return true
    }
  }
  return false
}

function handleCurrentChange(curPage) {
  page.current = curPage
  getEducationData()
}
function handleSizeChange(curSize) {
  page.size = curSize
  getEducationData()
}

watch(
  userId,
  val => {
    if (val) {
      getEducationData()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.edu_info_header {
  .item {
    width: 23%;
    padding: 0 4px;
    // text-align: center;
    &.school_name {
      width: 27%;
    }
  }
  .item_icon_wrap {
    text-align: center;
    width: 5%;
  }
}
</style>
