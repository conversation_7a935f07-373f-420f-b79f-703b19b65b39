<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section">
      <div class="content_item_title marginB_16">能力业绩矩阵</div>
      <div class>
        <div class="matrix_chart">
          <div class="matrix_head">
            <div class="title">业绩</div>
            <div class="flex_row_start border">
              <div class="item" v-for="item in kpiRankOptions" :key="item.dictCode">
                {{ item.codeName }}
              </div>
            </div>
          </div>
          <div class="clearfix">
            <div class="matrix_aside">
              <div class="matrix_aside_head flex_row_start">
                <div class="title">核心能力</div>
                <div class="flex_col_start border">
                  <div class="item" v-for="item in competenceRankOptions" :key="item.dictCode">
                    {{ item.codeName }}
                  </div>
                </div>
              </div>
            </div>
            <div class="matrix_main flex_col_start">
              <div class="item flex_row_start" v-for="(item, index) in matrixData" :key="item.code + index">
                <div
                  class="list"
                  :class="'level_' + item.code + list.code"
                  v-for="(list, listIndex) in item.maps"
                  :key="listIndex + list.code"
                >
                  {{ list.value }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content_item el-col-24">
          <div class="content_item_main">
            <div class="content_item_title">
              详情列表
              <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
            </div>
            <div class="content_item_content">
              <tableComponet
                @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange"
                :tableData="tableData"
                :needIndex="true"
              ></tableComponet>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { qualityAllOrgDist, queryCompetenceList, competence, exportData } from '../../../../../request/api.js'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import asideFilterBar from '../../asideFilterBar.vue'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref({})
const competenceRankOptions = ref([])
const kpiRankOptions = ref([])
const matrixData = ref([])
const page = ref(1)
const size = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '员工编码',
      prop: 'employee_code'
    },
    {
      label: '员工姓名',
      prop: 'user_name'
    },
    {
      label: '所属组织',
      prop: 'org_name'
    },
    {
      label: '任职岗位',
      prop: 'post_name'
    },
    {
      label: '职层',
      prop: 'job_level_name'
    },
    {
      label: '核心能力',
      prop: 'competence'
    },
    {
      label: '业绩表现',
      prop: 'kpi'
    },
    {
      label: '人才分类',
      prop: 'talent'
    },
    {
      label: '评价人',
      prop: 'superior'
    },
    {
      label: '评价日期',
      prop: 'evaluationTime'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const competenceFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await competence(params)
    if (res.code == 200) {
      matrixData.value = res.data
    }
  } catch (error) {
    console.error('获取能力业绩矩阵数据失败:', error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  page.value = 1
  competenceFun()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryCompetenceList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error('获取表格数据失败:', error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'n'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '能力业绩详细列表')
  } catch (error) {
    console.error('导出数据失败:', error)
  }
}

onMounted(async () => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  competenceFun()
  getTableData()
  const res = await window.$getDocList(['COMPETENCE_RANK', 'KPI_RANK'])
  competenceRankOptions.value = res.COMPETENCE_RANK
  kpiRankOptions.value = window.$util.deepClone(res.KPI_RANK).reverse()
})
</script>

<style scoped lang="scss">
.matrix_chart {
  width: 700px;
  float: left;
  margin-right: 6px;
  margin-bottom: 16px;
  .matrix_head {
    width: 100%;
    text-align: center;
    line-height: 30px;
    .title {
      height: 30px;
      background: #fbfbfb;
      padding-left: 90px;
    }
    .flex_row_start {
      height: 30px;
      margin-left: 90px;
      &.border {
        border-bottom: 1px solid #f6f6f6;
      }
    }
    .item {
      flex: 1;
    }
  }
}
</style>
