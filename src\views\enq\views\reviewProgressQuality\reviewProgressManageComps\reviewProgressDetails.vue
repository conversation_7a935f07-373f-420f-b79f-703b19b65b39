<template>
  <div class="review_progress_details_wrap">
    <div class="page_main_title flex_row_betweens">
      <div class="title flex_row_start">
        <p>个人进度</p>
        <div class="check_title" v-if="enqName"><span>/</span>{{ enqName }}</div>
      </div>
      <div class="go_back_btn" @click="goback()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section">
      <div class="page_section job_grade_center clearfix">
        <div class="filter_bar_wrap">
          <div class="flex_row_start">
            <div class="filter_item title">状态:</div>
            <div class="filter_item">
              <el-select v-model="layerNo" clearable placeholder="请选择">
                <el-option
                  v-for="item in layerNoOption"
                  :key="item.codeName"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </div>
            <!-- <div class="filter_item">
              <el-button
                class="page_add_btn"
                type="primary"
                
                @click="getTargetDataFun"
                >查询</el-button
              >
            </div> -->
          </div>
          <div class="filter_item">
            <el-button class="page_add_btn" type="primary" @click="exportData">导出</el-button>
          </div>
        </div>
        <table-component
          :needIndex="true"
          :tableData="tableData"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        >
        </table-component>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getOrgSchedule, getOrgLayNo, getUserSchedule, getUnLoginSchedule, orgExportData } from '../../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const route = useRoute()
const enqId = route.query.enqId
const enqName = route.query.enqName
const layerNo = ref('')
const layerNoOption = ref([
  {
    codeName: '全部',
    dictCode: ''
  },
  {
    codeName: '已完成',
    dictCode: 'P'
  },
  {
    codeName: '未完成',
    dictCode: 'E'
  },
  {
    codeName: '未开始',
    dictCode: 'N'
  }
])

const tableData = reactive({
  columns: [
    {
      label: '一级部门',
      prop: 'oneLevelName'
    },
    {
      label: '二级部门',
      prop: 'twoLevelName'
    },
    {
      label: '三级部门',
      prop: 'threeLevelName'
    },
    {
      label: '四级部门',
      prop: 'fourLevelName'
    },
    {
      label: '姓名',
      prop: 'userName'
    },
    {
      label: '岗位',
      prop: 'postName'
    },
    {
      label: '盘点进度',
      prop: 'status'
    },
    {
      label: '完成时间',
      prop: 'finishTime'
    }
  ],
  data: [],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})

const getUserScheduleFun = async () => {
  const res = await getUserSchedule({
    enqId: enqId,
    enqUserStatus: layerNo.value,
    current: tableData.page.current,
    size: tableData.page.size
  })
  tableData.data = []
  if (res.code == 200) {
    tableData.data = res.data
    tableData.page.total = res.total
  }
}

const handleSizeChange = size => {
  tableData.page.current = 1
  tableData.page.size = size
  getUserScheduleFun()
}

const handleCurrentChange = current => {
  tableData.page.current = current
  getUserScheduleFun()
}

const exportData = async () => {
  const params = {
    enqId: enqId,
    type: 'a',
    enqStatus: layerNo.value
  }
  let fileName
  if (layerNo.value == 'P') {
    fileName = enqName + '--个人进度已完成'
  } else if (layerNo.value == 'E') {
    fileName = enqName + '--个人进度未完成'
  } else if (layerNo.value == 'N') {
    fileName = enqName + '--个人进度未开始'
  } else {
    fileName = enqName + '--个人进度'
  }
  const res = await orgExportData(params)
  const blob = new Blob([res])
  const elink = document.createElement('a')
  elink.download = fileName + '.xlsx'
  elink.style.display = 'none'
  elink.href = URL.createObjectURL(blob)
  document.body.appendChild(elink)
  elink.click()
  URL.revokeObjectURL(elink.href)
  document.body.removeChild(elink)
}

watch(layerNo, () => {
  tableData.page.current = 1
  getUserScheduleFun()
})

onMounted(() => {
  getUserScheduleFun()
})

const router = useRouter()
const goback = () => {
  router.back()
}
</script>

<style scoped lang="scss">
.review_progress_details_wrap {
  .page_main_title {
    .title {
      p {
      }
      .check_title {
        span {
          display: inline-block;
          margin: 0 6px;
        }
      }
    }
    .go_back_btn {
      padding-right: 20px;
      color: #0099ff;
      cursor: pointer;
      font-weight: normal;
      font-size: 14px;
    }
  }
  .review_progress_details_center {
    position: relative;
    .export_btn_wrap {
      position: absolute;
      right: 16px;
      top: 5px;
    }
  }
}
</style>
