<template>
    <div class="report_view_list">
        <div class="page_section_aside">
            <div class="page_second_title">报告检索</div>
            <div class="report_filter_group">
                <div class="page_third_title">报告类型</div>
                <div class>
                    <div
                        class="report_filter_item"
                        :class="{ active: activeReportType == 'org' }"
                        @click="changeReportType('org')"
                    >
                        部门盘点报告
                    </div>
                    <div
                        class="report_filter_item"
                        :class="{ active: activeReportType == 'user' }"
                        @click="changeReportType('user')"
                    >
                        个人盘点报告
                    </div>
                </div>
            </div>
            <div class="report_filter_group">
                <div class="page_third_title">模糊检索</div>
                <div class>
                    <el-input
                        class="marginB_16"
                        v-model="searchValue"
                        placeholder
                    ></el-input>
                    <el-button
                        class="page_add_btn"
                        type="primary"
                        @click="searchReport"
                        
                        >查询</el-button
                    >
                </div>
            </div>
        </div>
        <div class="page_section_main">
            <table-component
                :tableData="tableData"
                :needIndex="true"
                @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange"
            >
                <template v-slot:oper>
                    <el-table-column
                        label="人数"
                        width="100"
                        v-if="activeReportType == 'org'"
                    >
                        <template slot-scope="scope">
                            <div class="partakeNum">
                                <span class="num">{{
                                    scope.row.enqUserCount
                                }}</span>
                                人
                            </div>
                        </template>
                    </el-table-column>
                </template>
                <template v-slot:oper2>
                    <el-table-column label width="100">
                        <template slot-scope="scope">
                            <!-- @click.native.prevent="showReport(scope.$index, tableData.data)" -->
                            <!-- v-link="'/talentAssessment/assessmentReport/assessmentReportOrg?id='+scope.row.id" -->
                            <el-button
                                class="page_add_btn"
                                @click="showReport(scope.row)"
                                type="primary"
                                size="mini"
                                >查看报告</el-button
                            >
                        </template>
                    </el-table-column>
                </template>
            </table-component>
        </div>
    </div>
</template>
 
<script>
    import { queryOrgReportList, queryUserReportList } from "../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "reportListView",
        components: {
            tableComponent,
        },
        data() {
            return {
                useCache: this.$route.query.useCache || false,
                pageSize: 10,
                currentPage: 1,
                activeReportType: "org",
                searchValue: "",
                tableData: {
                    columns: [],
                    data: [],
                    page: {},
                },
                orgReportColumns: [
                    {
                        label: "公司名称",
                        prop: "companyName",
                    },
                    {
                        label: "项目名称",
                        prop: "enqName",
                    },
                    {
                        label: "部门名称",
                        prop: "orgName",
                    },
                    {
                        label: "报告日期",
                        prop: "reportDate",
                    },
                ],
                userReportColumns: [
                    {
                        label: "公司名称",
                        prop: "companyName",
                    },
                    {
                        label: "项目名称",
                        prop: "enqName",
                    },
                    {
                        label: "名称",
                        prop: "userName",
                        width:'80'
                    },
                    {
                        label: "部门名称",
                        prop: "orgName",
                    },
                    {
                        label: "岗位",
                        prop: "postName",
                    },
                    {
                        label: "报告日期",
                        prop: "reportDate",
                    },
                ],
            };
        },
        created() {
            this.init();
        },
        watch: {
            pageSize: function (val) {
                this.getReportData();
            },
            currentPage: function (val) {
                this.getReportData();
            },
            activeReportType: function (val) {
                this.getReportData();
            },
        },
        methods: {
            init(){
                this.enqId = this.$route.query.enqId;
                this.activeReportType = this.$route.query.type
                    ? this.$route.query.type
                    : "org";

                if(this.useCache){
                    this.readCacheParams();
                }
                this.getReportData();
            },
            readCacheParams() {
                console.log("setParams");
                let cacheParams = this.$store.state.reportViewListParams;

                this.activeReportType = cacheParams.activeReportType;
                this.currentPage = cacheParams.current;
                this.pageSize = cacheParams.size;
                this.enqId = cacheParams.enqId;
                if(this.activeReportType == 'org'){
                    this.searchValue = cacheParams.orgName
                }else if(this.activeReportType == 'user'){
                    this.searchValue = cacheParams.userName
                }

            },
            queryOrgReportListFun() {

                let data = {
                    current: this.currentPage,
                    size: this.pageSize,
                    orgName: this.searchValue,
                    enqId: this.enqId,
                };

                let cacheParams = this.$store.state.reportViewListParams;
                delete cacheParams.userName;

                let params = {};

                // 页面初始加载
                if (this.useCache) {
                    params = { ...data, ...cacheParams };
                } else {
                    params = { ...cacheParams, ...data };
                }
                params.activeReportType = this.activeReportType;
                // 缓存筛选条件
                this.$store.commit("setParams", params);


                queryOrgReportList(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        // this.$msg.success(res.msg)
                        if (res.data) {
                            this.tableData.columns = this.orgReportColumns;
                            this.tableData.data = res.data;
                            this.tableData.page = res.page;
                        } else {
                            this.tableData.columns = this.orgReportColumns;
                            this.tableData.data = [];
                            this.tableData.page = {
                                current:1,
                                size:10,
                                total:0
                            };
                        }
                    } else {
                        this.$msg.error(res.msg);
                    }
                    this.useCache = false;
                });
            },
            queryUserReportList() {
                let data = {
                    current: this.currentPage,
                    size: this.pageSize,
                    userName: this.searchValue,
                    enqId: this.enqId,
                };

                let cacheParams = this.$store.state.reportViewListParams;

                delete cacheParams.orgName;
                let params = {};

                // 页面初始加载
                if (this.useCache) {
                    params = { ...data, ...cacheParams };
                } else {
                    params = { ...cacheParams, ...data };
                }
                params.activeReportType = this.activeReportType;
                // 缓存筛选条件
                this.$store.commit("setParams", params);

                queryUserReportList(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        // this.$msg.success(res.msg)
                        if (res.data) {
                            this.tableData.columns = this.userReportColumns;
                            this.tableData.data = res.data;
                            this.tableData.page = res.page;
                        } else {
                            this.tableData.columns = this.userReportColumns;
                            this.tableData.data = [];
                            this.tableData.page = {
                                current:1,
                                size:10,
                                total:0
                            };
                        }
                    } else {
                        this.$msg.error(res.msg);
                    }
                    this.useCache = false;
                });
            },
            searchReport(){
                this.currentPage = 1;
                this.getReportData();
            },
            getReportData() {
                if (this.activeReportType == "org") {
                    this.queryOrgReportListFun();
                } else if (this.activeReportType == "user") {
                    this.queryUserReportList();
                }
            },
            changeReportType(type, $event) {
                this.currentPage = 1;
                this.searchValue = "";
                // this.enqId = null;
                if (type == "org") {
                    this.activeReportType = "org";
                } else if (type == "user") {
                    this.activeReportType = "user";
                }
            },
            handleSizeChange(size) {
                this.pageSize = size;
            },
            handleCurrentChange(current) {
                this.currentPage = current;
            },
            showReport(row) {
                let enqId = row.enqId;
                let orgCode = row.orgCode;
                let userId = row.userId;
                let postCode = row.postCode;
                console.log(row);
                if (this.activeReportType == "user") {
                    this.$router.push({
                        path: "/talentReviewHome/talentReviewAnalysis/userReport",
                        query: { enqId: enqId, userId: userId, postCode: postCode },
                    });
                } else {
                    this.$router.push({
                        path: "/talentReviewHome/talentReviewAnalysis/orgReport",
                        query: { enqId: enqId, orgCode: orgCode },
                    });
                }
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .page_section_aside {
        padding-top: 16px;
        margin-right: 36px !important;
        border-right: 1px solid #e4e7ed;
        .page_second_title {
            margin-left: 12px;
            margin-bottom: 16px;
        }
    }
    .report_view_list {
        padding-top: 32px;
        .left {
            float: left;
            width: 280px;
        }
    }
    .report_filter_group {
        padding: 0 32px 10px 16px;
        .report_filter_item {
            text-align: center;
            color: #525e6c;
            line-height: 30px;
            margin-bottom: 5px;
            background: #EBF4FF;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            &.active {
                color: #fff;
                background: #0099FF;
            }
        }
    }
    .partakeNum {
        font-size: 14px;
        .num {
            font-size: 16px;
            color: #0070c0;
            font-weight: bold;
        }
    }
</style>