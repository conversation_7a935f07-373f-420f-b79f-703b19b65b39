<script setup>
defineOptions({ name: 'tree' })
const props = defineProps({
  treeData: {
    type: Object
  },
  formatProps: {
    type: Object,
    default: {}
  },
  defaultChecked: {
    type: Array,
    default: []
  },
  defaultExpand: {
    type: <PERSON>olean,
    default: true
  },
  checkStrictly: {
    type: <PERSON><PERSON>an,
    default: false
  }
})
const showL = ref(true)
const mouseoverL = e => {
  console.log(e)
  let t = e.target
  let tl = t.clientWidth
  let cl = t.scrollWidth
  console.log(tl)
  console.log(cl)
  if (tl < cl) {
    showL.value = false
  } else {
    showL.value = true
  }
}
const treeProps = computed(() => {
  return {
    label: props.formatProps.label || 'label', // 默认使用 'label'
    children: props.formatProps.children || 'children', // 默认使用 'children'
    disabled: props.formatProps.disabled || undefined // 可选字段
  }
})
</script>
<template>
  <div class="tree_wrap">
    <el-tree
      :class="{ act_tree: checkStrictly }"
      :data="treeData"
      show-checkbox
      node-key="id"
      :default-expand-all="defaultExpand"
      :default-checked-keys="defaultChecked"
      :props="formatProps"
      :check-strictly="checkStrictly"
    >
      <template #default="{ data }">
        <el-tooltip :disabled="showL" class="item" effect="dark" :content="data[treeProps.label]" placement="top">
          <span class="" @mouseover="mouseoverL">{{ data[treeProps.label] }}</span>
        </el-tooltip>
      </template>
    </el-tree>
  </div>
</template>
<style lang="scss" scoped>
.tree_wrap {
  :deep .el-tree {
    .el-tree-node__label {
      font-size: 14px;
    }
    .el-tooltip__trigger {
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  :deep .act_tree {
    .el-checkbox__input {
      .el-checkbox__inner {
        border-radius: 50%;
      }
    }
  }
}
</style>
