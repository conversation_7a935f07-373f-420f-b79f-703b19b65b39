<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in trainData" :key="item.id">
      <el-input class="item" v-model="item.workingContent" placeholder="填写工作内容"></el-input>
      <el-radio-group class="item" v-model="item.workingFreq">
        <el-radio :label="'D'">{{ '' }}</el-radio>
        <el-radio :label="'W'">{{ '' }}</el-radio>
        <el-radio :label="'Y'">{{ '' }}</el-radio>
      </el-radio-group>
      <el-input class="item" v-model="item.workingCount" placeholder="填写工作次数"></el-input>
      <el-input class="item" v-model="item.workingDuration" placeholder="填写单次工作时长"></el-input>
      <div class="item item_icon_wrap">
        <el-icon class="item_icon" @click="deleteItem(item, index)"><Delete /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { Delete } from '@element-plus/icons-vue'
const userStore = useUserStore()
const props = defineProps({
  trainData: {
    type: Array,
    default: () => [
      {
        id: '1',
        workingContent: '',
        workingFreq: '',
        workingCount: '',
        workingDuration: ''
      }
    ]
  }
})
const emit = defineEmits(['deleteItem'])

function deleteItem(item, index) {
  emit('deleteItem', item, index)
}
</script>

<style scoped lang="scss">
.edu_info_item {
  .item {
    // flex: 1;
    width: 25%;
  }
  .el-radio-group {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
  .el-radio {
    margin: 0;
  }
  .el-radio__label {
    padding: 0;
  }
  .item_icon_wrap {
    text-align: center;
    width: 10%;
    padding-top: 2px;
    color: #0099fd;
    font-size: 20px;
  }
}
</style>
