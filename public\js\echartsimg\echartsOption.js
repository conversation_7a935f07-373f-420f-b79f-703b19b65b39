import * as echarts from 'echarts'

//循环取给定的颜色
function getColor(colors, index) {
  // return colors[index] ? colors[index] : getColor(colors, index - colors.length);
  return colors[index] ? colors[index] : colors[index - colors.length]
}

//hex转rgb
function hexToRgba(hex, opacity) {
  let rgbaColor = ''
  let reg = /^#[\da-f]{6}$/i
  if (reg.test(hex)) {
    rgbaColor = `rgba(${parseInt('0x' + hex.slice(1, 3))},${parseInt(
      '0x' + hex.slice(3, 5)
    )},${parseInt('0x' + hex.slice(5, 7))},${opacity})`
  }
  return rgbaColor
}

//RGB转RGBA
function rgbToRgba(color, alp) {
  let rgbaAttr = color.match(/[\d.]+/g)
  if (rgbaAttr.length >= 3) {
    let r, g, b
    r = rgbaAttr[0]
    g = rgbaAttr[1]
    b = rgbaAttr[2]
    return 'rgba(' + r + ',' + g + ',' + b + ',' + alp + ')'
  }
}

//创建随机色
function createRGB() {
  //rgb颜色随机
  let r = Math.floor(Math.random() * 256)
  let g = Math.floor(Math.random() * 256)
  let b = Math.floor(Math.random() * 256)
  let rgb = 'rgb(' + r + ',' + g + ',' + b + ')'
  return rgb
}

//图表类型
/*
 * XBar:X轴柱形图
 * YBar:Y轴柱形图
 * XLine:X轴折线图
 * YLine:Y轴折线图
 * Rose:玫瑰图
 * Ring:环形图
 * SolidPie:饼图
 * ProgressPie:环形进度图
 * XStack:堆叠图
 * YStack:堆叠图
 * Scatter:散点图
 * Graph:点关系图
 * Quadrant:四象限图
 * Fan:扇形图
 * ReportYLine:报告竖形折线图
 * Radar:雷达图
 * Tree:树形图
 * Funnel:漏斗部
 * Gauge: 速度仪表盘
 * */

//生成echarts图片时需返回 animation: false; 属性
//如果图表添加事件 在图表数据中添加属性 addEvent:true;
let ecOptions = {
  //柱图
  // chartData: {
  //     report:true,//生成报告
  //     target:true,//柱图有目标值（边框图）
  //     legend: [ //多柱图与边框图需要传
  //         {
  //             legendName:"个性特质",
  //             legendKey:"trait",
  //             targetKey:"targetTrait"
  //         },
  //         {
  //             legendName:"团队管理",
  //             legendKey:"mgt",
  //             targetKey:"targetMgt"
  //         }],
  //     data: [
  //         {
  //             name: "总部",
  //             trait: 10,
  //             mgt: 20,
  //             targetTrait:20,
  //             targetMgt:30
  //         },
  //         {
  //             name: "质量部",
  //             trait: 25,
  //             mgt: 50,
  //             targetTrait:40,
  //             targetMgt:60
  //         },
  //         {
  //             name: "生产部",
  //             trait: 74,
  //             mgt: 40,
  //             targetTrait:80,
  //             targetMgt:50
  //         },
  //         {
  //             name: "审计部",
  //             trait: 60,
  //             mgt: 78,
  //             targetTrait:80,
  //             targetMgt:95
  //         },
  //         {
  //             name: "人力资源部",
  //             trait: 45,
  //             mgt: 30,
  //             targetTrait:60,
  //             targetMgt:80
  //         },
  //         {
  //             name: "技术部",
  //             trait: 38,
  //             mgt: 50,
  //             targetTrait:70,
  //             targetMgt:60
  //         },
  //         {
  //             name: "工程设备部",
  //             trait: 50,
  //             mgt: 40,
  //             targetTrait:80,
  //             targetMgt:70
  //         },
  //
  //     ],
  //     lineData:[
  //         {
  //             name: "总部",
  //             value: 15
  //         },
  //         {
  //             name: "质量部",
  //             value: 11
  //         },
  //         {
  //             name: "生产部",
  //             value: 8
  //         },
  //         {
  //             name: "审计部",
  //             value: 25
  //         },
  //         {
  //             name: "人力资源部",
  //             value: 38
  //         },
  //         {
  //             name: "技术部",
  //             value: 17
  //         },
  //         {
  //             name: "工程设备部",
  //             value: 9,
  //         },
  //     ]
  // },
  setBarOption(type, chartData, index) {
    let checkName = ''
    if (index == undefined && chartData.defaultChecked) {
      index = 0
    }
    if (chartData.addEvent) {
      //如果图表添加事件 则默认选择第一项
      checkName = index != undefined ? chartData.data[index].name : ''
    }
    let colors = [
      '#0099FF',
      '#89C997',
      '#7BCAFF',
      '#9fa8ff',
      '#ff8d38',
      '#0099FF',
      '#89C997',
      '#7BCAFF',
      '#9fa8ff',
      '#ff8d38',
      '#0099FF'
    ]
    let direction = [0, 0, 0, 0] //渐变方向[右，下，左，上]
    let legend = {
      show: false
    }
    let grid = {
      left: 50,
      top: 20,
      right: 80,
      bottom: 20
    }
    let tooltip = {}
    // if (typeof chartData.data[0].value == "string") {
    //     tooltip = {
    //         trigger: 'axis',
    //         formatter: (chartData.data[0].value + '').indexOf("%") > -1 ? '{b} : {c}%' : '{b} : {c}',
    //         axisPointer: {
    //             type: 'shadow'
    //         }
    //     };
    // } else {
    //     tooltip = {
    //         trigger: 'axis',
    //         formatter: '{b}:{c}',
    //         axisPointer: {
    //             type: 'shadow'
    //         }
    //     };
    // }
    tooltip = {
      trigger: 'axis',
      // formatter: (chartData.data[0].value + '').indexOf("%") > -1 ? '{b} : {c}%' : '{b} : {c}',
      // formatter: function (data) {
      //     console.log(data);
      // },
      axisPointer: {
        type: 'shadow'
      }
    }
    let xAxis = {
      show: false
    }
    let yAxis = {
      show: false
    }
    let series = []
    let axisOption = {
      type: 'category', //坐标轴类型
      data: chartData.data.map(function (item) {
        // X轴类别
        return item.name
      }),
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true,
        color: '#555',
        // interval: chartData.data.length > 10 ? 2 : 0,
        interval: 0,
        fontSize: 16,
        rotate: 75
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      }
    }
    let textPosition = 'top'
    switch (type) {
      case 'XBar':
        if (chartData.target) {
          xAxis = []
          xAxis.push(axisOption)
          xAxis.push({
            type: 'category',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitArea: {
              show: false
            },
            splitLine: {
              show: false
            },
            data: []
          })
        } else {
          xAxis = axisOption
        }
        grid.bottom = 80
        if (!(chartData.padding == null)) {
          grid.bottom = chartData.padding
        }
        direction = [0, 1, 0, 0]
        break
      case 'YBar':
        axisOption.axisLabel.rotate = 0
        axisOption.zlevel = 2
        yAxis = axisOption
        grid.bottom = 20
        grid.right = 30
        grid.left = 80
        if (!(chartData.padding == null)) {
          grid.left = chartData.padding
        }
        direction = [0, 0, 1, 0]
        textPosition = 'right'
        break
    }
    //多柱图
    if (chartData.legend) {
      legend = {
        show: true,
        itemWidth: 14,
        itemGap: 10,
        left: 50
      }
      if (typeof chartData.data[0][chartData.legend[0].legendKey] == 'string') {
        tooltip.formatter =
          (chartData.data[0][chartData.legend[0].legendKey] + '').indexOf('%') > -1 ? '{a} : {c}%' : '{a} : {c}'
      } else {
        // tooltip.formatter = '{a}:{c}'
      }
      grid.top = 50
      switch (type) {
        case 'XBar':
          if (!(chartData.padding == null)) {
            grid.bottom = chartData.padding
          }
          grid.right = 30
          break
        case 'YBar':
          if (!(chartData.padding == null)) {
            grid.left = chartData.padding
          }
          grid.bottom = 50
          break
      }

      if (chartData.needAverage) {
        grid.right = 100
      }
      series = chartData.legend.map((item, index) => {
        return {
          name: item.legendName,
          type: 'bar',
          barMaxWidth: 30,
          xAxisIndex: 0,
          label: {
            normal: {
              show: true,
              position: textPosition,
              fontSize: 16,
              color: '#333',
              formatter: (chartData.data[0][chartData.legend[0].legendKey] + '').indexOf('%') > -1 ? '{c}%' : '{c}'
            }
          },
          // barGap:'60%',
          // barCategoryGap:'40%',
          markLine: chartData.needAverage
            ? {
                label: {
                  show: true,
                  // position:'insideEndTop',
                  formatter: function (data) {
                    let str = ''
                    return (str += item.legendName + '平均' + data.value)
                  }
                },
                data: [
                  {
                    name: '平均线',
                    // 支持 'average', 'min', 'max'
                    type: 'average'
                  }
                ]
              }
            : null,
          itemStyle: {
            color: chartData.report
              ? getColor(colors, index)
              : new echarts.graphic.LinearGradient(...direction, [
                  {
                    offset: 0,
                    color: '#fff'
                  },
                  {
                    offset: 1,
                    color: getColor(colors, index)
                  }
                ])
          },
          data: chartData.data.map(function (data) {
            let value
            if (data[item.legendKey]) {
              value =
                typeof data[item.legendKey] == 'number' ? data[item.legendKey] : data[item.legendKey].split('%')[0]
            } else {
              value = null
            }
            return value
            // return data[item.legendKey];
          })
        }
      })
      if (chartData.target) {
        chartData.legend.forEach(item => {
          series.push({
            name: item.legendName, // bar图的外边框
            type: 'bar',
            barMaxWidth: 30,
            xAxisIndex: 1,
            itemStyle: {
              normal: {
                color: 'rgba(0,0,0,0)',
                borderColor: '#d6621e',
                barBorderRadius: 1,
                borderWidth: 1,
                shadowColor: '#d6621e',
                shadowBlur: 5,
                shadowOffsetX: 0,
                shadowOffsetY: 0
              }
            },

            data: chartData.data.map(data => {
              let value =
                typeof data[item.targetKey] == 'number' ? data[item.targetKey] : data[item.targetKey].split('%')[0]
              return value
            })
          })
        })
      }
    } else {
      //单柱图
      if (typeof chartData.data[0].value == 'string') {
        tooltip.formatter = (chartData.data[0].value + '').indexOf('%') > -1 ? '{b} : {c}%' : '{a} : {c}'
      } else {
        // tooltip.formatter = '{a}:{c}'
      }

      if (chartData.needAverage) {
        grid.right = 100
      }
      series = [
        {
          type: 'bar',
          barMaxWidth: 30,
          itemStyle: {
            color: function (params) {
              if (checkName && checkName === params.name) {
                return new echarts.graphic.LinearGradient(...direction, [
                  {
                    //点击后的颜色
                    offset: 0,
                    color: '#fff'
                  },
                  {
                    offset: 1,
                    color: colors[4]
                  }
                ])
              } else {
                return chartData.report
                  ? colors[0]
                  : new echarts.graphic.LinearGradient(...direction, [
                      {
                        offset: 0,
                        color: '#fff'
                      },
                      {
                        offset: 1,
                        color: colors[0]
                      }
                    ])
              }
            }
          },
          label: {
            normal: {
              show: true,
              position: textPosition,
              fontSize: 16,
              color: '#333',
              formatter: (chartData.data[0].value + '').indexOf('%') > -1 ? '{c}%' : '{c}'
            }
          },
          markLine: chartData.needAverage
            ? {
                label: {
                  show: true,
                  // position:'insideEndTop',
                  formatter: function (data) {
                    let str = ''
                    return (str += '平均' + data.value)
                  }
                },
                data: [
                  {
                    name: '平均线',
                    // 支持 'average', 'min', 'max'
                    type: 'average'
                  }
                ]
              }
            : null,
          data: chartData.data.map(data => {
            // return data.value;
            try {
              let val = typeof data.value == 'number' ? data.value : data.value.split('%')[0]
              return {
                value: val,
                label: {
                  show: true,
                  position: textPosition == 'top' ? (val > 0 ? 'top' : 'bottom') : val > 0 ? 'right' : 'left'
                }
              }
              // return (typeof data.value == "number" || !data.value) ? data.value : data.value.split('%')[0]
            } catch (err) {
              throw err
            }
          })
        }
      ]
    }
    if (chartData.lineData && chartData.lineData.length > 0) {
      function setSeriesObj(color, data, name) {
        return {
          name: name,
          type: 'line',
          smooth: true,
          symbolSize: 8,
          zlevel: 3,
          lineStyle: {
            normal: {
              color: color,
              opacity: 0.6
            }
          },
          itemStyle: {
            normal: {
              color: color
            }
          },
          label: {
            normal: {
              show: true,
              position: 'top',
              formatter: function (data) {
                let symbol = chartData.lineData[0][chartData.lineLegend[0].legendKey].indexOf('%') > -1 ? '%' : ''
                return data.value + symbol
              },
              textStyle: {
                color: '#333',
                fontSize: 16
              }
            }
          },
          symbol: 'circle', //数据交叉点样式

          areaStyle: !chartData.report
            ? {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: hexToRgba(color, 0.2)
                      },
                      {
                        offset: 1,
                        color: hexToRgba(color, 0)
                      }
                    ],
                    false
                  ),
                  shadowBlur: 10
                }
              }
            : null,
          data: data
        }
      }
      yAxis = [
        {
          type: 'value',
          name: '得分',
          nameLocation: 'center',
          nameGap: 30,
          nameTextStyle: {
            color: '#666'
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#555',
            // interval: chartData.data.length > 10 ? 2 : 0,
            interval: 0,
            fontSize: 16
          },
          axisLine: {
            lineStyle: {
              color: '#ddd'
            }
          },
          splitLine: {
            show: false
          }
        }
      ]

      //多线图
      if (chartData.lineLegend) {
        // legend = {
        //     show: false,
        //     itemWidth: 14,
        //     itemGap: 10,
        //     left: 50
        // };
        if (typeof chartData.lineData[0][chartData.lineLegend[0].legendKey] == 'string') {
          let symbol = chartData.lineData[0][chartData.lineLegend[0].legendKey].indexOf('%') > -1 ? '%' : ''
          tooltip.formatter = function (data) {
            let str = ''
            data.forEach(item => {
              str += item.seriesName + ':' + item.value + symbol + '<br />'
            })
            return str
          }
        } else {
          // tooltip.formatter = '{a}:{c}'
        }
        grid.top = 50

        legend.show = true
        series = series.concat(
          chartData.lineLegend.map((item, index) => {
            let getCol = getColor(colors, index)
            let data = chartData.lineData.map(json => {
              let value =
                typeof json[item.legendKey] == 'number' ? json[item.legendKey] : json[item.legendKey].split('%')[0]
              return value
            })
            return setSeriesObj(getCol, data, item.legendName)
          })
        )
      } else {
        yAxis.push({
          type: 'value',
          name: '偏离度',
          nameLocation: 'center',
          nameGap: 30,
          nameTextStyle: {
            color: '#666'
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#555',
            interval: 0,
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: '#ddd'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#eee'
            }
          }
        })

        series.push({
          name: '偏离度',
          type: 'line',
          yAxisIndex: 1,
          z: 22,
          symbol: 'emptyCircle',
          symbolSize: 10,
          itemStyle: {
            normal: {
              color: '#D68627'
            }
          },
          label: {
            normal: {
              show: false,
              position: 'top',
              formatter: 'C',
              textStyle: {
                color: '#fff',
                fontSize: 16
              }
            }
          },
          lineStyle: {
            width: 2,
            color: '#FFCE59'
          },
          data: chartData.lineData.map(item => item.value)
        })
      }
    }
    return {
      legend,
      grid,
      animation: false,
      tooltip,
      xAxis,
      yAxis,
      series
    }
  },

  //堆叠图数据
  // stackData:{
  //     area: ['新荣区', '平城区', '云冈区', '云州区', '阳高县', '天镇县', '广灵县', '浑源县', '左云县'],
  //     legend: ['因病', '因残', '因学', '因灾', '缺水', '缺技术'],
  //     data:[
  //         {
  //             name:"因病",
  //             value:[1320, 1302, 901, 634, 1390, 1330, 1320,680,556]
  //         },
  //         {
  //             name:"因残",
  //             value:[320, 402, 301, 334, 390, 330, 320, 100, 50]
  //         },
  //         {
  //             name:"因学",
  //             value:[420, 302, 301, 334, 890, 330, 320, 200, 150]
  //         },
  //         {
  //             name:"因灾",
  //             value:[720, 602, 301, 734, 390, 330, 320, 100, 50]
  //         },
  //         {
  //             name:"缺水",
  //             value:[620, 302, 301, 334, 890, 330, 320, 100, 150]
  //         },
  //         {
  //             name:"缺技术",
  //             value:[520, 302, 301, 334, 390, 330, 420, 100, 50]
  //         },
  //     ]
  // },

  //阶梯图数据
  // chartData: {
  //     type:"ladder", //表示阶梯图
  //     needPercent:true,
  //     data:[
  //         {
  //             name:"起始级",
  //             value:33.7
  //         },
  //         {
  //             name:"基础级",
  //             value:28.7
  //         },
  //         {
  //             name:"规范级",
  //             value:26.6
  //         },
  //         {
  //             name:"优秀级",
  //             value:9.3
  //         },
  //         {
  //             name:"卓越级",
  //             value:1.7
  //         },
  //
  //     ]
  // },
  setStackBarOption(type, chartData) {
    // type:"ladder":阶梯图表
    // needPercent:添加 '%'
    let ladderFlag = !!(chartData.type && chartData.type == 'ladder') //表示阶梯图
    if (ladderFlag) {
      //阶梯图
      chartData.area = chartData.data.map(item => item.name)
      let dataArr = []
      let emptyVal = 0
      dataArr[0] = {
        name: '',
        value: chartData.data.map(item => {
          let newVal = emptyVal
          emptyVal += Number(item.value)
          return newVal
        })
      }
      dataArr[1] = {
        name: '',
        value: chartData.data.map(item => {
          return item.value
        })
      }
      chartData.data = dataArr
    }

    let colors = [
      '#9fa8ff',
      '#43d1ff',
      '#87f9f4',
      '#a9e68a',
      '#ffc30d',
      '#ff8d38',
      '#9fa8ff',
      '#43d1ff',
      '#87f9f4',
      '#a9e68a',
      '#ffc30d',
      '#ff8d38'
    ]
    let legend = {
      show: !ladderFlag,
      itemWidth: 14
    }
    let grid = {
      left: 50,
      top: 30,
      right: 10,
      bottom: 20
    }
    let tooltip = {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      // formatter: chartData.needPercent ? '{a0} : {c}%' : '{a} : {c}'
      formatter: function (data) {
        let str = data[0].name + '<br />'
        data.forEach(item => {
          str += item.seriesName + ':' + item.value + (chartData.needPercent ? '%' : '') + '<br />'
        })
        return str
      }
    }
    let xAxis = {
      show: false
    }
    let yAxis = {
      show: ladderFlag,
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true,
        color: '#555',
        interval: 0,
        fontSize: 16,
        rotate: 75
      },
      splitLine: {
        lineStyle: {
          color: '#f2f2f2'
        }
      },
      axisLine: {
        lineStyle: {
          color: '#f2f2f2'
        }
      }
    }
    let axisOption = {
      type: 'category', //坐标轴类型
      data: chartData.area,
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true,
        color: '#555',
        interval: 0,
        fontSize: 16,
        rotate: 45
      },
      axisLine: {
        lineStyle: {
          color: '#EEEEEE'
        }
      }
    }

    if (ladderFlag) {
      if (!(chartData.padding == null)) {
        grid.bottom = chartData.padding
      }
      xAxis = axisOption
      tooltip.formatter = function (data) {
        let str = data[1].name + ':' + data[1].value + (chartData.needPercent ? '%' : '')
        // str += item.name + ':' + item.value + (chartData.needPercent ? '%' : '') + '<br />';
        return str
      }
    } else {
      if (type == 'XStack') {
        grid.bottom = 100
        if (!(chartData.padding == null)) {
          grid.bottom = chartData.padding
        }
        xAxis = axisOption
      } else {
        grid.left = 100
        if (!(chartData.padding == null)) {
          grid.left = chartData.padding
        }
        yAxis = axisOption
      }
    }
    let series = []

    series = chartData.data.map((item, index) => {
      return {
        name: item.name,
        type: 'bar',
        stack: '总量',
        barMaxWidth: ladderFlag ? 60 : 30,
        label: {
          normal: {
            show: ladderFlag && index == 0 ? false : true,
            position: ladderFlag ? 'top' : 'inside',
            fontSize: 16,
            color: '#333',
            formatter: function (data) {
              return data.value > 0 ? data.value + (chartData.needPercent ? '%' : '') : ''
            }
          }
        },
        itemStyle: {
          color: ladderFlag && index == 0 ? hexToRgba(getColor(colors, 1), 0.1) : getColor(colors, index)
        },
        data: item.value
      }
    })
    return {
      legend,
      grid,
      animation: false,
      tooltip,
      xAxis,
      yAxis,
      series
    }
  },
  //普通环形图
  // pieData:{
  //     data:[{
  //         name:"销售",
  //         value:"50%"
  //     },
  //         {
  //             name:"客户服务",
  //             value:"35%"
  //         },
  //         {
  //             name:"行政工作及其他",
  //             value:"15%"
  //         }
  //     ]
  // },
  setPieOption(type, chartData) {
    let colors = ['#FBB62D', '#70DA88', '#7BCAFF', '#9fa8ff', '#ff8d38', '#0099FF']
    let legend = {
      itemWidth: 10,
      itemHeight: 10,
      orient: 'vertical',
      top: 'center',
      right: '1%',
      data: chartData.data.map(item => {
        return item.name
      }),
      textStyle: {
        fontSize: 14,
        color: '#555'
      }
    }
    let tooltip = {}
    if (typeof chartData.data[0].value == 'string') {
      tooltip = {
        trigger: 'item',
        formatter: (chartData.data[0].value + '').indexOf('%') > -1 ? '{b} : {c}%' : '{b} : {c}'
      }
    } else {
      tooltip = {
        trigger: 'item',
        formatter: '{b} : {c}'
      }
    }

    let series = [
      {
        name: '',
        type: 'pie',
        radius: ['30%', '80%'],
        center: ['35%', '50%'],
        roseType: type == 'Rose' ? 'area' : false,
        label: {
          normal: {
            show: true,
            position: 'inside',
            fontSize: 16,
            color: '#333',
            formatter: (chartData.data[0].value + '').indexOf('%') > -1 ? '{c}%' : '{c}'
          }
        },
        labelLine: false,
        data: chartData.data.map((item, index) => {
          return {
            name: item.name,
            value: typeof item.value == 'number' ? item.value : item.value.split('%')[0],
            itemStyle: {
              color: colors[index]
            }
          }
        })
      }
    ]

    return {
      legend,
      tooltip,
      series,
      animation: false
    }
  },
  setSolidPieOption(type, chartData) {
    let colors = [
      '#FBB62D',
      '#70DA88',
      '#7BCAFF',
      '#9fa8ff',
      '#ff8d38',
      '#0099FF',
      '#FBB62D',
      '#70DA88',
      '#7BCAFF',
      '#9fa8ff',
      '#ff8d38',
      '#0099FF'
    ]
    let legend = {
      show: false
    }
    let tooltip = {}
    if (typeof chartData.data[0].value == 'string') {
      tooltip = {
        trigger: 'item',
        formatter: (chartData.data[0].value + '').indexOf('%') > -1 ? '{b} : {c}%' : '{b} : {c}'
      }
    } else {
      tooltip = {
        trigger: 'item',
        formatter: '{b} : {c}'
      }
    }

    let series = [
      {
        name: '',
        type: 'pie',
        radius: [0, '60%'],
        label: {
          position: chartData.report ? 'inside' : 'outside',
          alignTo: 'edge',
          margin: 5,
          formatter: '{b} : {c}%'
        },
        data: chartData.data.map((item, index) => {
          return {
            name: item.name,
            value: typeof item.value == 'number' ? item.value : item.value.split('%')[0],
            itemStyle: {
              color: colors[index]
            }
          }
        })
      }
    ]

    return {
      legend,
      tooltip,
      series,
      animation: false
    }
  },
  //进度环形图
  // ProgressPieData:{
  //     colorIndex:0, //两种颜色
  //     data:{
  //         label:"目标",
  //         value:66,
  //         company:"%"
  //     }
  // },
  setProgressPieOption(type, chartData) {
    let colors = ['#3fa692', '#92d050', '#FBB62D', '#70DA88', '#7BCAFF', '#9fa8ff', '#ff8d38', '#0099FF']
    let color = getColor(colors, chartData.colorIndex)
    let legend = {
      show: false
    }
    let title = [
      {
        text: chartData.data.label + '\n' + Number(chartData.data.value).toFixed(1) + (chartData.data.company || ''),
        x: 'center',
        y: 'center',
        textStyle: {
          fontWeight: 'normal',
          color: color,
          fontSize: 26,
          lineHeight: 34
        }
      }
    ]

    let tooltip = {
      show: false
    }
    let series = [
      {
        name: '',
        type: 'pie',
        clockWise: true,
        radius: ['70%', '95%'],
        itemStyle: {
          normal: {
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          }
        },
        hoverAnimation: false,
        data: [
          {
            value: Math.abs(chartData.data.value),
            name: '',
            itemStyle: {
              normal: {
                // color: colors[chartData.colorIndex],
                color,
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            }
          },
          {
            name: '',
            value: 100 - Math.abs(chartData.data.value)
          }
        ]
      }
    ]
    return {
      title,
      color: hexToRgba(color, 0.1),
      legend,
      tooltip,
      series,
      animation: false
    }
  },

  //普通页面折线图
  // lineData:{
  //     legend:[
  //         {
  //             legendName:"目标",
  //             legendKey:"target"
  //         },
  //         {
  //             legendName:"实际",
  //             legendKey:"actual"
  //         },
  //     ],
  //     data:[
  //
  //         {
  //             name:"2020/01",
  //             target:100,
  //             actual:90,
  //             value:70,
  //         },
  //         {
  //             name:"2020/02",
  //             target:120,
  //             actual:70,
  //             value:80,
  //         },
  //         {
  //             name:"2020/03",
  //             target:100,
  //             actual:80,
  //             value:90,
  //         },
  //         {
  //             name:"2020/04",
  //             target:60,
  //             actual:40,
  //             value:100,
  //         },
  //
  //     ]
  // },
  setLineOption(type, chartData) {
    let colors = ['#0099FF', '#70DA88', '#89C997', '#7BCAFF', '#9fa8ff']
    let legend = {
      show: false
    }

    let tooltip = {
      show: true,
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#e5e5e5'
        }
      }
    }
    let grid = {
      top: 30,
      left: 20,
      right: 20,
      bottom: 20,
      containLabel: true
    }
    let xAxis = {
      type: 'value',
      axisLabel: {
        rotate: 75,
        textStyle: {
          color: '#333'
        }
      },
      axisLine: {
        lineStyle: {
          color: '#e5e5e5'
        }
      }
    }
    let yAxis = {
      type: 'value',
      axisLabel: {
        rotate: 45,
        textStyle: {
          color: '#333'
        }
      },
      axisLine: {
        lineStyle: {
          color: '#e5e5e5'
        }
      }
    }
    let axisOption = {
      type: 'category',
      boundaryGap: false,
      data: chartData.data.map(item => {
        return item.name
      })
    }
    switch (type) {
      case 'XLine':
        Object.assign(xAxis, axisOption)
        break
      case 'YLine':
        Object.assign(yAxis, axisOption)
        break
    }

    function setSeriesObj(color, data, name) {
      return {
        name: name,
        type: 'line',
        smooth: true,
        symbolSize: 8,
        zlevel: 3,
        lineStyle: {
          normal: {
            color: color,
            opacity: 0.6
          }
        },
        itemStyle: {
          normal: {
            color: color
          }
        },
        symbol: 'circle', //数据交叉点样式

        areaStyle: !chartData.report
          ? {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: hexToRgba(color, 0.2)
                    },
                    {
                      offset: 1,
                      color: hexToRgba(color, 0)
                    }
                  ],
                  false
                ),
                shadowBlur: 10
              }
            }
          : null,
        data: data
      }
    }

    let series = []
    if (chartData.legend) {
      legend.show = true
      series = chartData.legend.map((item, index) => {
        let getCol = getColor(colors, index)
        let data = chartData.data.map(json => {
          return json[item.legendKey]
        })
        return setSeriesObj(getCol, data, item.legendName)
      })
    } else {
      let data = chartData.data.map(json => {
        return json.value
      })

      series = setSeriesObj(colors[0], data, '')
    }
    let animationStatus = chartData.report ? false : true

    return {
      legend,
      tooltip,
      grid,
      xAxis,
      yAxis,
      series,
      animation: animationStatus
    }
  },
  //报告竖形折线图
  // lineData:{
  //     legend:[
  //         {
  //             legendName:"目标",
  //             legendKey:"target"
  //         },
  //         {
  //             legendName:"实际",
  //             legendKey:"actual"
  //         },
  //         {
  //             legendName:"基础",
  //             legendKey:"value"
  //         },
  //
  //     ],
  //     data:[
  //
  //         {
  //             name:"2020/01",
  //             target:100,
  //             actual:90,
  //             value:70,
  //         },
  //         {
  //             name:"2020/02",
  //             target:120,
  //             actual:70,
  //             value:80,
  //         },
  //         {
  //             name:"2020/03",
  //             target:100,
  //             actual:80,
  //             value:90,
  //         },
  //         {
  //             name:"2020/04",
  //             target:60,
  //             actual:40,
  //             value:100,
  //         },
  //         {
  //             name:"2020/01",
  //             target:100,
  //             actual:90,
  //             value:70,
  //         },
  //         {
  //             name:"2020/02",
  //             target:120,
  //             actual:70,
  //             value:80,
  //         },
  //         {
  //             name:"2020/03",
  //             target:100,
  //             actual:80,
  //             value:90,
  //         },
  //         {
  //             name:"2020/04",
  //             target:60,
  //             actual:40,
  //             value:100,
  //         },
  //         {
  //             name:"2020/01",
  //             target:100,
  //             actual:90,
  //             value:70,
  //         },
  //         {
  //             name:"2020/02",
  //             target:120,
  //             actual:70,
  //             value:80,
  //         },
  //         {
  //             name:"2020/03",
  //             target:100,
  //             actual:80,
  //             value:90,
  //         },
  //         {
  //             name:"2020/04",
  //             target:60,
  //             actual:40,
  //             value:100,
  //         },
  //
  //     ]
  // };
  setReportLineOption(type, chartData) {
    let colorGroup = [
      '#0099FF',
      '#33ff00',
      '#DB7093',
      '#8B1A1A',
      '#FA8072',
      '#F4A460',
      '#68228B',
      '#33ff00',
      '#00FFFF',
      '#FF34B3',
      '#FF8247',
      '#000',
      '#CCC',
      '#F00',
      '#FFFF00'
    ]
    // let colors = ["#0099FF", "#70DA88", "#89C997", "#7BCAFF", "#9fa8ff"];
    let legend = {
      show: true,
      top: 0
    }
    let tooltip = {
      show: false
    }
    let grid = {
      top: 30,
      left: 10,
      right: 10,
      bottom: 0,
      containLabel: true
    }
    let xAxis = {
      type: 'value',
      position: 'top',
      splitLine: {
        show: false
      }
    }
    let yAxis = {
      type: 'category',
      axisLabel: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisTick: false,
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#ddd'
        }
      },
      data: chartData.data.map(item => {
        return item.name
      })
    }

    function setSeriesObj(color, data, name) {
      return {
        name: name,
        type: 'line',
        smooth: true,
        symbolSize: 8,
        zlevel: 3,
        lineStyle: {
          type: 'dashed',
          color: color
        },
        itemStyle: {
          normal: {
            color: color
          }
        },
        symbol: 'circle', //数据交叉点样式
        data: data.reverse()
      }
    }

    let series = []
    if (chartData.legend) {
      legend.show = true
      series = chartData.legend.map((item, index) => {
        let getCol = getColor(colorGroup, index)
        let data = chartData.data.map(json => {
          return json[item.legendKey]
        })
        return setSeriesObj(getCol, data, item.legendName)
      })
    } else {
      //单条数据
      let data = chartData.data.map(json => {
        return json.value
      })

      series = setSeriesObj(colorGroup[0], data, '')
    }

    return {
      legend,
      tooltip,
      grid,
      xAxis,
      yAxis,
      series,
      animation: false
    }
  },

  //散点图
  // scatterData:{
  //     legend:[
  //         {
  //             legendName:"重要度",
  //             legendKey:"important",
  //         },
  //         {
  //             legendName:"紧急度",
  //             legendKey:"emergency",
  //         },
  //     ],
  //     data:[
  //         [
  //             {
  //                 name:"名称2",
  //                 important:20,
  //                 emergency:80
  //             },
  //             {
  //                 name:"名称1",
  //                 important:40,
  //                 emergency:50
  //             },
  //             {
  //                 name:"名称3",
  //                 important:70,
  //                 emergency:50
  //             },
  //         ],
  //
  //     ]
  // }
  setScatterOption(type, chartData) {
    let colors = [
      '#186faf',
      '#FBB62D',
      '#7BCAFF',
      '#9fa8ff',
      '#ff8d38',
      '#0099FF',
      '#186faf',
      '#FBB62D',
      '#7BCAFF',
      '#9fa8ff',
      '#ff8d38',
      '#0099FF'
    ]
    let tooltip = {
      formatter: function (obj) {
        let value = obj.value
        return (
          chartData.legend[0].legendName + ':' + value[0] + '<br/>' + chartData.legend[1].legendName + ':' + value[1]
        )
      }
    }
    let xAxis = {
      name: chartData.legend[0].legendName,
      nameLocation: 'center',
      nameTextStyle: {
        color: '#212121',
        fontSize: 16
      },
      nameGap: 30,
      axisLine: {
        //  改变x轴颜色
        lineStyle: {
          color: '#e5e5e5'
        }
      },
      axisLabel: {
        //  改变x轴字体颜色和大小
        textStyle: {
          color: '#212121',
          fontSize: 14
        }
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#e5e5e5'
        }
      }
    }
    let yAxis = {
      name: chartData.legend[1].legendName,
      nameLocation: 'center',
      nameGap: 30,
      nameTextStyle: {
        color: '#212121',
        fontSize: 16
      },
      axisLine: {
        //  改变y轴颜色
        lineStyle: {
          color: '#e5e5e5'
        }
      },
      axisLabel: {
        //  改变y轴字体颜色和大小
        //formatter: '{value} m³ ', //  给y轴添加单位
        textStyle: {
          color: '#212121',
          fontSize: 14
        }
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#e5e5e5'
        }
      }
    }
    let series = chartData.data.map((item, index) => {
      return {
        itemStyle: {
          color: getColor(colors, index),
          shadowColor: getColor(colors, index),
          shadowBlur: 10
        },
        type: 'scatter',
        label: {
          show: true,
          color: '#333',
          position: 'bottom',
          formatter: function (data) {
            return item[data.dataIndex].name
          }
        },
        symbolSize: 15,
        data: item.map(data => {
          return [data[chartData.legend[0].legendKey], data[chartData.legend[1].legendKey], 20]
        })
      }
    })
    return {
      animation: false,
      tooltip,
      xAxis,
      yAxis,
      series
    }
  },

  //点关系图
  // graphChartData: {
  //     data: [
  //         {
  //             name: '销售与市场管理能力测评',
  //             children:[
  //                 {
  //                     name: '专业知识',
  //                 }, {
  //                     name: '管理能力',
  //                 }, {
  //                     name: '营销策划',
  //                 }, {
  //                     name: '市场洞察',
  //                 }, {
  //                     name: '渠道合作',
  //                     children:[
  //                         {
  //                             name: '渠道规划',
  //                         }, {
  //                             name: '渠道开发',
  //                         }
  //                     ]
  //                 }, {
  //                     name: '业务管理',
  //                     children:[
  //                         {
  //                             name: '客户关系',
  //                         }, {
  //                             name: '人机互动',
  //                         }
  //                     ]
  //                 },
  //             ]
  //         },
  //
  //     ],
  //
  // }
  setGraphOption(type, chartData) {
    //关系图数据转换
    function graphDataConver(data) {
      let dataArr = []
      let linkArr = []
      let dataTraverse = function (arr, index) {
        for (let i = 0; i < arr.length; i++) {
          dataArr.push({
            name: arr[i].name
          })
          if (typeof index == 'number') {
            linkArr.push({
              source: index,
              target: dataArr.length - 1
            })
          }
          if (arr[i].children && arr[i].children.length > 0) {
            let pIndex = dataArr.length - 1
            dataTraverse(arr[i].children, pIndex)
          }
        }
      }
      dataTraverse(data)
      return {
        data: dataArr,
        links: linkArr
      }
    }

    let data = graphDataConver(chartData.data)
    let tooltip = {
      show: false
    }
    let animationDurationUpdate = 1000
    let grid = {
      top: 20,
      left: 40,
      right: 40,
      bottom: 20
    }
    console.log(chartData)
    let series = [
      {
        type: 'graph',
        layout: 'force',
        symbolSize: 20,
        focusNodeAdjacency: true,
        autoCurveness: true,
        roam: true,
        itemStyle: {
          // color: "#0099ff"
          color: params => {
            // console.log(params);
            return '#0099ff'
          }
        },
        edgeSymbolSize: [10, 30],
        data: data.data,
        links: data.links,
        label: {
          normal: {
            show: true,
            position: 'bottom',
            textStyle: {
              fontSize: 14,
              color: '#212121'
            },
            formatter: params => {
              return params.name.split('$$')[0]
            }
          }
        },
        force: {
          repulsion: chartData.data[0].children.length > 6 ? 100 : 200,
          initLayout: 'circular',
          edgeLength: [10, 80],
          layoutAnimation: true
        },

        lineStyle: {
          normal: {
            opacity: 0.9,
            width: 1,
            curveness: 0,
            color: '#bbb'
          }
        }
      }
    ]
    return {
      // grid,
      tooltip,
      animationDurationUpdate,
      series
    }
  },

  //扇形图
  // fanData:{
  //     data:[
  //         {
  //             name: '危险区域',
  //             min:'0%',
  //             max:'30%'
  //         },
  //         {
  //             name: '问题区域',
  //             min:'30%',
  //             max:'45%'
  //
  //         },
  //         {
  //             name: '稳定区域',
  //             min:'45%',
  //             max:'65%'
  //         },
  //         {
  //             name: '高绩效区域',
  //             min:'65%',
  //             max:'100%'
  //         }
  //
  //     ],
  //     actual:'66%'
  // },
  setFanOption(type, chartData) {
    let colors = [
      '#FBB62D',
      '#70DA88',
      '#7BCAFF',
      '#9fa8ff',
      '#ff8d38',
      '#0099FF',
      '#FBB62D',
      '#70DA88',
      '#7BCAFF',
      '#9fa8ff',
      '#ff8d38',
      '#0099FF'
    ]
    let targetIndex = 0
    let renderData = chartData.data.map((item, index) => {
      // let actual = chartData.actual.split("%")[0];
      // let min = item.min.split("%")[0];
      // let max = item.max.split("%")[0];
      let actual = chartData.actual.split('%')[0]
      let min = item.min
      let max = item.max
      if (parseInt(actual) >= parseInt(min) && parseInt(actual) < parseInt(max)) {
        targetIndex = index
      }
      return {
        name: item.name,
        value: parseInt(max) - parseInt(min),
        itemStyle: {
          color: getColor(colors, index)
        }
      }
    })
    let a = 0
    for (let i = 0; i < renderData.length; i++) {
      a += renderData[i].value
    }
    renderData.push({
      value: a,
      name: '',
      min: 0,
      max: 0,
      itemStyle: {
        normal: {
          color: 'rgba(0,0,0,0)'
        }
      }
    })
    let series = [
      {
        name: '',
        type: 'pie',
        legendHoverLink: false,
        animation: false,
        radius: ['0', '20%'],
        center: ['50%', '50%'],
        label: {
          normal: {
            position: 'center',
            formatter: chartData.actual.split('%')[0],
            fontSize: 16,
            color: '#fff'
          }
        },
        data: [
          {
            name: '实际值',
            value: (chartData.actual + '').indexOf('%') > -1 ? chartData.actual.split('%')[0] : chartData.actual,
            itemStyle: {
              color: getColor(colors, targetIndex)
            }
          }
        ]
      },
      {
        name: '',
        type: 'pie',
        startAngle: -180,
        legendHoverLink: false,
        animation: false,
        radius: ['20%', '100%'],
        center: ['50%', '50%'],
        label: {
          position: 'inner',
          formatter: function (params) {
            let i = params.dataIndex
            if (i < chartData.data.length) {
              return (
                params.name + '：\n' + chartData.data[params.dataIndex].min + '~' + chartData.data[params.dataIndex].max
              )
            } else {
              return ''
            }
          },
          lineHeight: 20
        },
        data: renderData
      }
    ]
    return {
      series,
      animation: false
    }
  },

  //雷达图
  // data:{
  //     legend:[
  //         {
  //             legendName:"目标得分",
  //             legendKey:"target"
  //         },
  //         {
  //             legendName:"综合得分",
  //             legendKey:"actual"
  //         },
  //
  //     ],
  //     chartData:[
  //         {
  //             name:"个性特质",
  //             target:80,
  //             actual:40,
  //
  //         },
  //         {
  //             name:"通用知识",
  //             target:80,
  //             actual:60,
  //
  //         },
  //         {
  //             name:"专业知识",
  //             target:80,
  //             actual:40,
  //
  //         },
  //         {
  //             name:"团队管理",
  //             target:70,
  //             actual:40,
  //             average:60
  //         },
  //     ]
  // }
  setRadarOption(type, chartData) {
    let colors = [
      '#FBB62D',
      '#70DA88',
      '#7BCAFF',
      '#9fa8ff',
      '#ff8d38',
      '#0099FF',
      '#FBB62D',
      '#70DA88',
      '#7BCAFF',
      '#9fa8ff',
      '#ff8d38',
      '#0099FF'
    ]
    let tooltip = {
      show: true,
      trigger: 'item'
    }
    let grid = {
      top: 20,
      left: 40,
      right: 40,
      bottom: 20,
      containLabel: true
    }
    let legend = {
      show: true,
      icon: 'circle',
      bottom: 5,
      textStyle: {
        fontSize: 14
      },
      data: chartData.legend.map(item => {
        return item.legendName
      })
    }
    let maxValue = 0
    let series = chartData.legend.map((item, index) => {
      let data = chartData.data.map(json => {
        return json[item.legendKey]
      })
      maxValue = Math.max(...data) > maxValue ? Math.max(...data) : maxValue
      return {
        name: chartData.legend[index].legendName,
        type: 'radar',
        symbol: 'circle',
        symbolSize: 6,
        areaStyle: {
          normal: {
            color: getColor(colors, index),
            opacity: 0.2
          }
        },
        itemStyle: {
          color: getColor(colors, index),
          borderColor: hexToRgba(getColor(colors, index), 0.1),
          borderWidth: 10
        },
        lineStyle: {
          normal: {
            type: 'dashed',
            color: getColor(colors, index),
            width: 1
          }
        },
        data: [data]
      }
    })
    maxValue = maxValue % 10 > 0 ? maxValue - (maxValue % 10) + 10 : maxValue
    let radar = {
      center: ['50%', '50%'],
      radius: '58%',
      startAngle: 90,
      splitNumber: 5,
      shape: 'circle',
      splitArea: {
        areaStyle: {
          color: ['transparent']
        }
      },
      name: {
        textStyle: {
          color: '#333',
          fontSize: 12
        }
      },
      axisLabel: {
        show: false,
        margin: 4,
        color: '#bbb'
      },
      axisLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#e5e5e5'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e5e5e5'
        }
      },
      lineStyle: {
        type: 'dashed',
        color: '#e5e5e5'
      },
      axisTick: {
        show: false,
        lineStyle: {
          type: 'dashed',
          color: '#e5e5e5'
        }
      },
      indicator: chartData.data.map((item, index) => {
        if (index == 0) {
          return {
            name: item.name || item.moduleName,
            max: maxValue,
            min: 0,
            axisLabel: {
              show: true
            }
          }
        }
        return {
          name: item.name || item.moduleName,
          max: maxValue,
          min: 0
        }
      })
    }
    return {
      grid,
      tooltip,
      legend,
      radar,
      series,
      animation: false
    }
  },
  // 树图
  setTreeOption(type, chartData) {
    let tooltip = {
      trigger: 'item',
      triggerOn: 'mousemove'
      // formatter:function(data){
      //     console.log(data);
      //     return `${data.data.name}下级部门:${data.data.countOrg}个，${data.data.countPostUser}人`
      // }
    }

    function setNodeStyle(obj) {
      let inEval = obj.inEval
      if (inEval == 'Y') {
        obj['label'] = {
          backgroundColor: '#ff0',
          padding: [4, 6],
          position: 'right',
          verticalAlign: 'middle'
        }
      }
      if (obj.children && obj.children.length > 0) {
        obj.children.forEach(item => {
          setNodeStyle(item)
        })
      } else {
      }
    }
    setNodeStyle(chartData.data)

    let seriesOption = {
      type: 'tree',
      data: [chartData.data],
      left: 20,
      right: 20,
      top: 20,
      bottom: 20,
      symbol: 'emptyCircle',
      symbolSize: 10,
      orient: 'TB',
      label: {
        position: 'top',
        rotate: 0,
        verticalAlign: 'middle',
        align: 'center',
        fontSize: 12
      },
      leaves: {
        label: {
          position: 'bottom',
          rotate: -90,
          verticalAlign: 'middle',
          align: 'left'
        }
      },
      expandAndCollapse: false,
      animationDurationUpdate: 750
    }
    if (type == 'XTree') {
      seriesOption.orient = 'LR'
      seriesOption.left = 100
      seriesOption.right = 100
      seriesOption.leaves.label = {
        position: 'right',
        rotate: 0,
        verticalAlign: 'middle',
        align: 'left'
      }
    }

    let series = [seriesOption]

    return {
      tooltip,
      series,
      animation: false
    }
  },
  //四象限图
  // scatterData: {
  //     legend: [
  //         {
  //             legendName: "综合得分",
  //             legendKey: "score"
  //         },
  //         {
  //             legendName: "偏离度",
  //             legendKey: "diverge"
  //         },
  //
  //     ],
  //     data: [
  //         {
  //             moduleName: "影响力",
  //             score: 80,
  //             devDegree: 10
  //         },
  //         {
  //             moduleName: "系统知识",
  //             score: 85,
  //             devDegree: 15
  //         },
  //         {
  //             moduleName: "结果导向",
  //             score: 90,
  //             diverge: 18
  //         },
  //         {
  //             moduleName: "市场洞察",
  //             score: 92,
  //             devDegree: 25
  //         },
  //         {
  //             moduleName: "敬业投入",
  //             score: 95,
  //             devDegree: 28
  //         },
  //
  //     ]
  // },
  //四象限图
  setQuadrantData(type, chartData) {
    let symbol = ['circle', 'rect', 'triangle', 'diamond']
    let tooltip = {
      show: true
    }
    let length = 0
    chartData.data.forEach(item => {
      let name = item.name || item.moduleName
      length += name.split('').length
    })
    length += chartData.data.length * 3
    let top = Math.ceil(length / 48) * 28 + 15
    let grid = {
      top: chartData.report ? 40 : top,
      // top: 40,
      left: 20,
      right: 20,
      bottom: 40,
      containLabel: true
    }
    let legendIndex = 1
    let legend = {
      show: chartData.report ? false : true,
      icon: 'circle',
      itemWidth: 12,
      itemHeight: 12,
      top: 10,
      left: 40,
      textStyle: {
        fontSize: 12
      },
      selectedMode: false,
      formatter: function (name) {
        return `${legendIndex++}.${name}`
      }
    }
    let xAxis = {
      name: chartData.legend[0].legendName,
      nameLocation: 'center',
      nameTextStyle: {
        color: '#999',
        fontSize: 14
      },
      min: 0,
      max: 100,
      nameGap: 30,
      interval: 20,
      axisLine: {
        //  改变x轴颜色
        lineStyle: {
          color: '#ddd'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        //  改变x轴字体颜色和大小
        textStyle: {
          color: '#666',
          fontSize: 14
        }
      },
      splitLine: {
        show: false
      }
    }
    let yAxis = {
      name: chartData.legend[1].legendName,
      nameLocation: 'center',
      nameGap: 30,
      min: -100,
      max: 100,
      interval: 20,
      nameTextStyle: {
        color: '#999',
        fontSize: 14
      },
      axisLine: {
        //  改变y轴颜色
        lineStyle: {
          color: '#e5e5e5'
        }
      },
      axisLabel: {
        //  改变y轴字体颜色和大小
        //formatter: '{value} m³ ', //  给y轴添加单位
        textStyle: {
          color: '#666',
          fontSize: 14
        }
      },
      splitLine: {
        lineStyle: {
          color: '#e5e5e5',
          type: 'dashed'
        }
      }
    }
    let series = chartData.data.map((item, index) => {
      return {
        type: 'scatter',
        // name: item.moduleName,
        name: item.name || item.moduleName,
        label: {
          normal: {
            show: true,
            position: 'inside',
            formatter: function () {
              return index + 1
            },
            textStyle: {
              fontSize: 12,
              color: '#fff'
            }
          }
        },
        itemStyle: {
          color: createRGB()
        },
        symbol: symbol[Math.floor(Math.random() * symbol.length)],
        symbolSize: 24,
        markLine: {
          show: true,
          markLine: false,
          lineStyle: {
            color: '#9BF25D'
          },
          data: [
            {
              name: '50',
              xAxis: 50
            },
            {
              name: '0',
              yAxis: 0
            }
          ]
        },
        data: [
          [
            (item[chartData.legend[0].legendKey] + '').split('%')[0],
            (item[chartData.legend[1].legendKey] + '').split('%')[0]
          ]
        ]
      }
    })
    return {
      tooltip,
      grid,
      legend,
      xAxis,
      yAxis,
      series,
      animation: false
    }
  },
  //报告自我认知偏差柱图
  setReportYBarOption(type, chartData) {
    //获取最大值
    function getMax(chartData) {
      let maxValue = 20 //默认最小值
      chartData.data.forEach(item => {
        let currVal = Math.abs(item.value)
        currVal > maxValue && (maxValue = currVal)
      })
      return maxValue
    }
    //向上取整10
    function ceilNumber(number) {
      let bite = 0
      if (number < 10) {
        return 10
      }
      while (number >= 10) {
        number /= 10
        bite += 1
      }
      return Math.ceil(number) * Math.pow(10, bite)
    }

    let maxValue = ceilNumber(getMax(chartData))
    let legend = {
      show: false
    }
    let grid = {
      left: 10,
      top: 20,
      right: 10,
      bottom: 0
    }
    let xAxis = {
      show: true,
      type: 'value',
      position: 'top',
      min: -1 * maxValue,
      max: maxValue,
      interval: 5,
      splitLine: {
        lineStyle: {
          color: '#ddd',
          type: 'dashed'
        }
      }
    }
    let yAxis = {
      show: false,
      type: 'category'
    }
    let series = [
      {
        type: 'bar',
        barMaxWidth: 20,
        itemStyle: {
          color: function (params) {
            if (params.value < 0) {
              return 'rgb(113,157,213)'
            }

            return 'rgb(199,78,141)'
          }
        },
        label: {
          normal: {
            show: false
          }
        },
        data: chartData.data.reverse()
      }
    ]

    return {
      legend,
      grid,
      xAxis,
      yAxis,
      series,
      animation: false
    }
  },
  //漏斗图
  // chartData: {
  //     data:[
  //         {
  //             name:"员工",
  //             value:100
  //         },
  //         {
  //             name:"主管",
  //             value:80
  //         },
  //         {
  //             name:"经理",
  //             value:50
  //         },
  //         {
  //             name:"高级经理",
  //             value:20
  //         },
  //         {
  //             name:"总监",
  //             value:5
  //         },
  //         {
  //             name:"副总",
  //             value:2
  //         },
  //     ]
  // },
  setFunnelOption(type, chartData) {
    let color = ['#9fa8ff', '#43d1ff', '#87f9f4', '#a9e68a', '#ffc30d', '#ff8d38', '#ee99ff']
    let tooltip = {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}({d}%)'
    }
    let legend = {
      orient: 'vertical',
      left: 20,
      top: 50,
      data: chartData.data.map(item => item.name)
    }
    let series = [
      {
        name: '',
        type: 'funnel',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        left: 40,
        top: 20,
        right: 20,
        bottom: 20,
        sort: 'ascending',
        gap: 0,
        label: {
          show: true,
          position: 'inside',
          fontSize: 14,
          color: '#333'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        data: chartData.data
      }
    ]
    return {
      color,
      tooltip,
      legend,
      series,
      animation: false
    }
  },
  setGaugeOption(type, chartData) {
    let series = [
      {
        type: 'gauge',
        axisLine: {
          lineStyle: {
            width: 20,
            color: [
              [0.3, '#B5CAF7'],
              [0.7, '#85A8F2'],
              [1, '#4A82FE']
            ]
          }
        },
        pointer: {
          itemStyle: {
            color: 'auto'
          }
        },
        axisTick: {
          distance: -30,
          length: 8,
          lineStyle: {
            color: '#fff',
            width: 2
          }
        },
        splitLine: {
          distance: -30,
          length: 30,
          lineStyle: {
            color: '#fff',
            width: 4
          }
        },
        axisLabel: {
          color: '#85A8F2',
          distance: -5,
          fontSize: 16
        },
        detail: {
          // valueAnimation: true,
          // formatter: '{value} km/h',
          // color: 'inherit'
          fontWeight: 'bold',
          offsetCenter: [0, '50%']
        },
        data: [
          {
            value: chartData.data
          }
        ]
      }
    ]
    return {
      series
    }
  }
}

let chartTypeObj = {
  XBar: ecOptions.setBarOption,
  YBar: ecOptions.setBarOption,
  Rose: ecOptions.setPieOption,
  Ring: ecOptions.setPieOption,
  ProgressPie: ecOptions.setProgressPieOption,
  SolidPie: ecOptions.setSolidPieOption,
  XLine: ecOptions.setLineOption,
  YLine: ecOptions.setLineOption,
  ReportYLine: ecOptions.setReportLineOption,
  XStack: ecOptions.setStackBarOption,
  XLadder: ecOptions.setStackBarOption,
  YStack: ecOptions.setStackBarOption,
  Scatter: ecOptions.setScatterOption,
  Graph: ecOptions.setGraphOption,
  Fan: ecOptions.setFanOption,
  Radar: ecOptions.setRadarOption,
  Quadrant: ecOptions.setQuadrantData,
  XTree: ecOptions.setTreeOption,
  YTree: ecOptions.setTreeOption,
  ReportYBar: ecOptions.setReportYBarOption,
  Funnel: ecOptions.setFunnelOption,
  Gauge: ecOptions.setGaugeOption
}
export default function getEchartsOptions(type, chartData, index) {
  return chartTypeObj[type](type, chartData, index)
}
