<template>
    <div class="comment_lower_wrap bg_write">
        <div class="page_main_title">
            点评下级
            <div
                class="goback_geader"
                v-link="
                    '/talentAssessment/talentAssessmentManagement/evaluationItemList'
                "
            >
                <i class="el-icon-arrow-left"></i>返回
            </div>
        </div>
        <div class="page_section">
            <div class="title_bar clearfix">
                <div
                    class="bar_item"
                    v-for="(item, index) in barList"
                    :class="{ active: currIndex == index }"
                >
                    <div class="bar_content">{{ item.name }}</div>
                </div>
            </div>
            <div class="performance_wrap" v-show="currIndex == 0">
                <commentLowerkpi @nextStep="nextStep"></commentLowerkpi>
            </div>
            <div class="capacity_wrap" v-show="currIndex == 1">
                <div class="flex_row_betweens">
                    <div class="capacity_left">
                        <div class="page_second_title marginT_30">选择下属</div>
                        <div class="employee_list marginT_16">
                            <ul>
                                <li
                                    v-for="(item, index) in personList"
                                    :class="{
                                        active: employeeIndex == index,
                                        tick: item.improvementReviewStatus == 'Y',
                                    }"
                                    @click="checkEmplyee(item, index)"
                                >
                                    {{ item.objectName }}
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="capacity_right">
                        <div class="page_second_title marginT_30">
                            设置能力改善优先级
                        </div>
                        <div class="capacity_table marginT_16">
                            <table>
                                <thead>
                                    <tr>
                                        <th rowspan="2" width="50">序号</th>
                                        <th rowspan="2">能力分类</th>
                                        <th rowspan="2">目标</th>
                                        <th rowspan="2">实际</th>
                                        <th rowspan="2">差距</th>
                                        <th rowspan="2">偏离度</th>
                                        <th colspan="3">重要度</th>
                                        <th colspan="3">紧急度</th>
                                    </tr>
                                    <tr>
                                        <th class="flag_td">重要</th>
                                        <th class="flag_td">一般</th>
                                        <th class="flag_td">不重要</th>
                                        <th class="flag_td">紧急</th>
                                        <th class="flag_td">一般</th>
                                        <th class="flag_td">不紧急</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <tr v-for="(item, index) in tableData">
                                        <td>{{ index + 1 }}</td>
                                        <td>{{ item.moduleName }}</td>
                                        <td>{{ item.expectedScore }}</td>
                                        <td>{{ item.overallScore }}</td>
                                        <td>{{ item.diffScore }}</td>
                                        <td>{{ item.devDegree }}</td>
                                        <td
                                            class="bg_td"
                                            :class="{
                                                level1: item.ipvImportance == 3,
                                            }"
                                            @click="
                                                setImportant(item, index, 3)
                                            "
                                        >
                                            <span>重要</span>
                                        </td>
                                        <td
                                            class="bg_td"
                                            :class="{
                                                level2: item.ipvImportance == 2,
                                            }"
                                            @click="
                                                setImportant(item, index, 2)
                                            "
                                        >
                                            <span>一般</span>
                                        </td>
                                        <td
                                            class="bg_td"
                                            :class="{
                                                level3: item.ipvImportance == 1,
                                            }"
                                            @click="
                                                setImportant(item, index, 1)
                                            "
                                        >
                                            <span>不重要</span>
                                        </td>
                                        <td
                                            class="bg_td"
                                            :class="{
                                                level1: item.ipvEmergency == 3,
                                            }"
                                            @click="
                                                setEmergency(item, index, 3)
                                            "
                                        >
                                            <span>紧急</span>
                                        </td>
                                        <td
                                            class="bg_td"
                                            :class="{
                                                level2: item.ipvEmergency == 2,
                                            }"
                                            @click="
                                                setEmergency(item, index, 2)
                                            "
                                        >
                                            <span>一般</span>
                                        </td>
                                        <td
                                            class="bg_td"
                                            :class="{
                                                level3: item.ipvEmergency == 1,
                                            }"
                                            @click="
                                                setEmergency(item, index, 1)
                                            "
                                        >
                                            <span>不紧急</span>
                                        </td>
                                    </tr>
                                </tbody>

                                <!--                                <tfoot>-->
                                <!--                                    <tr class="text_center">-->
                                <!--                                        <td colspan="2">小计</td>-->
                                <!--                                        <td colspan="4"></td>-->
                                <!--                                        <td>3</td>-->
                                <!--                                        <td>2</td>-->
                                <!--                                        <td>1</td>-->
                                <!--                                        <td>2</td>-->
                                <!--                                        <td>3</td>-->
                                <!--                                        <td>4</td>-->
                                <!--                                    </tr>-->
                                <!--                                </tfoot>-->
                            </table>
                        </div>

                        <div class="page_second_title marginT_30">
                            能力改善建议
                        </div>
                        <div class="area_wrap marginT_16">
                            <el-input
                                v-model="reviewProposal"
                                type="textarea"
                                rows="4"
                                resize="none"
                                placeholder=""
                            ></el-input>
                        </div>
                        <div class="save_btn marginT_16 align_center">
                            <el-button
                                class="page_add_btn"
                                type="primary"
                                @click="saveItem()"
                                >保存</el-button
                            >
                        </div>
                    </div>
                </div>
            </div>
            <div class="align_center marginT_30">
                <el-button
                    class="page_confirm_btn"
                    type="primary"
                    v-show="currIndex == 1"
                    @click="prev()"
                    >上一步</el-button
                >
                <el-button
                    class="page_confirm_btn"
                    type="primary"
                    v-show="currIndex == 1"
                    @click="submit()"
                    >提交</el-button
                >
            </div>
        </div>
    </div>
</template>

<script>
    import {
        getReviewSub,
        submitPerformance,
        getReviewDetail,
        submitReviewDetail,
        submitReviewCheck,
    } from "../../request/api";
    import commentLowerkpi from "./commentLowerKpi";
    export default {
        name: "commentLower",
        components: { commentLowerkpi },
        data() {
            return {
                evalId: this.$route.query.evalId,
                personVal: 0,
                step: 1,
                currIndex: 0,
                barList: [
                    {
                        name: "业绩区分",
                    },
                    {
                        name: "能力改善优先级",
                    },
                ],
                personList: [],
                employeeList: [],
                employeeIndex: 0,
                tableData: [],
                objectId: "",
                postCode: "",
                reviewProposal: "",
            };
        },
        created() {
            this.getReviewSubFun();
        },
        mounted() {},
        methods: {
            checkEmplyee(item, index) {
                this.employeeIndex = index;
                this.getReviewDetailFun(item);
            },
            prev() {
                this.currIndex = 0;
            },
            next() {
                this.submitPerformanceFun();
            },
            nextStep(){
                this.currIndex = 1;
                this.checkEmplyee(this.personList[0],0)
            },
            setImportant(row, index, type) {
                this.tableData[index].ipvImportance = type;
            },
            setEmergency(row, index, type) {
                this.tableData[index].ipvEmergency = type;
            },

            getReviewSubFun() {
                getReviewSub({
                    evalId: this.evalId,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.personList = res.data.map((item) => {
                            return {
                                evalId: item.evalId,
                                objectName: item.objectName,
                                objectId: item.objectId,
                                orgName: item.orgName,
                                postCode: item.postCode,
                                postName: item.postName,
                                improvementReviewStatus: item.improvementReviewStatus,
                                kpiPercentile: this.kpiReverse(item.kpiPercentile),
                            };
                        });

                    }
                });
            },
            submitPerformanceFun() {
                let params = this.personList.map((item) => {
                    return {
                        evalId: item.evalId,
                        objectId: item.objectId,
                        postCode: item.postCode,
                        kpiPercentile: this.kpiConversion(item.kpiPercentile),
                    };
                });
                console.log(params);
                submitPerformance(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.currIndex = 1;
                        if (this.tableData.length > 0) {
                            return;
                        }
                        this.getReviewDetailFun(this.personList[0]);
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            getReviewDetailFun(item) {
                if (!item) {
                    return;
                }
                getReviewDetail({
                    evalId: item.evalId,
                    objectId: item.objectId,
                    postCode: item.postCode,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.tableData = res.data.evalModuleResultList;
                        this.reviewProposal =
                            res.data.evalPostObject.reviewProposal;
                        this.objectId = res.data.evalPostObject.objectId;
                        this.postCode = res.data.evalPostObject.postCode;
                    }
                });
            },

            kpiReverse(val) {
                switch (val) {
                    case 100:
                        return 0;
                        break;
                    case 95:
                        return 1;
                    case 80:
                        return 2;
                        break;
                    case 35:
                        return 3;
                        break;
                    case 5:
                        return 4;
                        break;
                    default:
                        return 0;
                        break;
                }
            },
            kpiConversion(val) {
                switch (val) {
                    case 0:
                        return "100";
                        break;
                    case 1:
                        return "95";
                    case 2:
                        return "80";
                        break;
                    case 3:
                        return "35";
                        break;
                    case 4:
                        return "5";
                        break;
                    default:
                        return "100";
                        break;
                }
            },

            saveItem() {
                let len = this.tableData.length;
                for (let index = 0; index < len; index++) {
                    const item = this.tableData[index];
                    if(item.ipvImportance == null){
                        this.$msg.warning(`请设置${item.moduleName}的重要度！`)
                        return
                    }
                    if(item.ipvEmergency == null){
                        this.$msg.warning(`请设置${item.moduleName}的紧急度！`)
                        return
                    }
                }
                let params = {
                    evalId: this.evalId,
                    objectId: this.objectId,
                    postCode: this.postCode,
                    reviewProposal: this.reviewProposal,
                    detailList: this.tableData.map((item) => {
                        return {
                            moduleCode: item.moduleCode,
                            ipvEmergency: item.ipvEmergency,
                            ipvImportance: item.ipvImportance,
                        };
                    }),
                };
                console.log(params);
                submitReviewDetail(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$msg.success("保存成功");
                        this.getReviewSubFun();
                    }
                });
            },
            submitReviewCheckFun() {
                submitReviewCheck({
                    evalId: this.evalId,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$util.goback();
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            submit() {
                for (let index = 0; index < this.personList.length; index++) {
                    const item = this.personList[index];
                    if(item.improvementReviewStatus !='Y'){
                        this.$msg.warning(`请为${item.objectName}设置改善优先级`)
                        return
                    }
                }
                this.submitReviewCheckFun();
            },
        },
    };
</script>

<style scoped lang="scss">
    .comment_lower_wrap {
        .title_bar {
            .bar_item {
                position: relative;
                float: left;
                width: 530px;
                height: 60px;
                background: #EBF4FF;
                text-align: center;
                line-height: 60px;
                font-size: 20px;
                font-weight: bold;
                border: 1px solid #0099FF;
                border-right: none;
                color: #0099FF;
                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    width: 41px;
                    height: 41px;
                    right: -20px;
                    top: 8px;
                    background: #EBF4FF;
                    border-top: 1px solid #0099FF;
                    border-right: 1px solid #0099FF;
                    transform: rotate(45deg) scale(1.02);
                }
                &:first-child {
                    z-index: 99999;
                }
                &:last-child {
                    z-index: 1;
                }
                &.active {
                    background: #0099FF;
                    color: #fff;
                    &::after {
                        background: #0099FF;
                    }
                }
            }
        }
        .performance_item {
            flex: 1;
            margin: 0 10px;
            height: 140px;
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid #e5e5e5;
            .perfor_title {
                height: 30px;
                line-height: 30px;
                background: #0099FF;
                text-align: center;
                color: #fff;
            }
            .perfor_text {
                padding: 5px 10px;
                line-height: 24px;
                font-size: 12px;
            }
        }
        .performance_setting {
            .performance_set_wrap {
                .label_item {
                    flex: 1;
                    height: 50px;
                    text-align: center;
                    background: #f4f4f4;
                    line-height: 20px;
                    padding: 5px;
                    &.row {
                        line-height: 40px;
                    }
                }
            }
            .person_list {
                width: 100%;
                height: 225px;
                overflow-y: auto;
                ul {
                    width: 100%;
                    li {
                        width: 100%;
                        height: 45px;
                        line-height: 45px;
                        &:nth-child(even) {
                            background: #f4f4f4;
                        }
                        .person_info {
                            width: 37.5%;
                            span {
                                flex: 1;
                                text-align: center;
                            }
                        }
                        .person_progress {
                            flex: 1;
                            margin: 0 70px;
                        }
                    }
                }
            }
        }
        .capacity_wrap {
            .capacity_left {
                .employee_list {
                    width: 150px;
                    padding: 5px;
                    height: 480px;
                    overflow-y: auto;
                    border: 1px solid #e5e5e5;
                    ul {
                        width: 100%;
                        li {
                            position: relative;
                            width: 100%;
                            height: 30px;
                            background: #EBF4FF;
                            line-height: 30px;
                            padding-left: 20px;
                            color: #0099FF;
                            margin-bottom: 5px;
                            cursor: pointer;
                            &.active {
                                background: #0099FF;
                                color: #fff;

                                &.tick {
                                    &::after {
                                        border-color: #fff;
                                    }
                                }
                            }
                            &.tick {
                                &::after {
                                    content: "";
                                    position: absolute;
                                    display: block;
                                    width: 14px;
                                    height: 7px;
                                    top: 7px;
                                    right: 10px;
                                    border-left: 2px solid;
                                    border-bottom: 2px solid;
                                    border-color: #0099FF;
                                    transform: rotate(-45deg);
                                }
                            }
                        }
                    }
                }
            }
            .capacity_right {
                flex: 1;
                margin-left: 20px;
                .capacity_table {
                    max-height: 310px;
                    overflow-y: auto;
                    table {
                        width: 100%;
                        // border-collapse:0;
                        border-spacing: 1px;
                        thead {
                            tr {
                                background: #f4f4f4;

                                th {
                                    height: 40px;
                                    color: #212121;
                                    font-weight: normal;
                                    &.flag_td {
                                        width: 80px;
                                    }
                                }
                            }
                        }
                        .tbody_wrap {
                        }
                        tbody {
                            font-size: 12px;
                            tr {
                                background: #EBF4FF;
                                td {
                                    height: 40px;
                                    text-align: center;
                                    padding: 5px;
                                    cursor: pointer;
                                    &.bg_td {
                                        &.level1 {
                                            span {
                                                background: #f9926f;
                                                color: #fff;
                                            }
                                        }
                                        &.level2 {
                                            span {
                                                background: #449cff;
                                                color: #fff;
                                            }
                                        }
                                        &.level3 {
                                            span {
                                                background: #fbb72c;
                                                color: #fff;
                                            }
                                        }
                                        span {
                                            display: block;
                                            height: 100%;
                                            line-height: 30px;
                                            background: #d4e5fa;
                                        }
                                    }
                                }
                            }
                        }
                        tfoot {
                            tr {
                                background: #f4f4f4;
                                td {
                                    height: 40px;
                                    color: #0099FF;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
</style>