<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in eduInfoData" :key="item.id">
      <div class="item item_icon_wrap">{{ index + 1 }}</div>
      <div class="item item_icon_wrap">{{ item.userName }}</div>
      <div class="item">{{ item.YJPBMM }}</div>
      <div class="item">{{ item.YJPGRM }}</div>
      <div class="item item_icon_wrap">{{ item.YJPWCZ }}</div>
      <div class="item">{{ item.YJPWCJ }}</div>
      <div class="item">{{ item.YJPJXC }}</div>
      <div class="item item_icon_wrap">{{ item.YJPCBK }}</div>
      <div class="item item_icon_wrap">{{ item.performOverallScore }}</div>
      <div class="item item_icon_wrap">{{ item.performLevelSys }}</div>
      <el-select class="item" v-model="item.actualPerformGrade" placeholder="请选择">
        <el-option
          v-for="(option, idx) in qualificationOptions"
          :label="option.codeName"
          :value="option.dictCode"
          :key="idx"
        ></el-option>
      </el-select>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user.js'

const props = defineProps({
  eduInfoData: {
    type: Array,
    default: () => [{}]
  }
})

const qualificationOptions = ref([])
const userStore = useUserStore()

onMounted(async () => {
  const res = await userStore.getDocList(['ACTUAL_GRADE'])
  qualificationOptions.value = res.ACTUAL_GRADE
})
</script>

<style scoped lang="scss">
.edu_info_item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  padding: 8px 16px;

  .item {
    width: 9%;
  }

  .item_icon_wrap {
    text-align: center;
    width: 6%;

    .item_icon {
      font-size: 20px;
      color: #0099fd;
      cursor: pointer;
    }
  }
}
</style>
