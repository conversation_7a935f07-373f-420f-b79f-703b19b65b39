<script setup>
defineOptions({ name: 'viewReport' })
const reportList = ref([
  { id: 1, title: '20250201 销售预测核心能力测评报告（单人测评）', expand: false },
  { id: 2, title: '20250201 销售竞争力测评报告（多人测评）', expand: true }
])

const toggleViewReport = item => {
  item.expand = !item.expand
}

const asideList = ref([
  {
    title: '整体能力表现',
    list: [
      {
        id: 'ztbx',
        name: '整体表现'
      },
      {
        id: 'nlcdb',
        name: '能力长短板'
      },
      {
        id: 'nltp',
        name: '能力图谱'
      }
    ]
  },
  {
    title: '能力短板分析',
    list: [
      {
        id: 'nlcy',
        name: '能力差因'
      },
      {
        id: 'nljm',
        name: '能力解码'
      },
      {
        id: 'dbxj',
        name: '短板详解'
      }
    ]
  },
  {
    title: '各模块能力详细分析',
    list: [
      {
        id: 'mkztbx',
        name: '模块整体表现'
      },
      {
        id: 'mkcyfx',
        name: '模块差因分析'
      },
      {
        id: 'mknlgs',
        name: '模块能力改善'
      }
    ]
  },
  {
    title: '能力改善总结',
    list: [
      {
        id: 'nldbfx',
        name: '能力短板风险'
      },
      {
        id: 'nltsjz',
        name: '能力提升举措'
      },
      {
        id: 'dqxdhj',
        name: '短期行动计划'
      }
    ]
  }
])
const activeAside = ref('ztbx')
const activeAsideName = ref('整体表现')
const change = list => {
  activeAside.value = list.id
  activeAsideName.value = list.name
}
</script>
<template>
  <div class="report-page">
    <div class="head-title">您的可查看的能力测评报告如下：</div>
    <div class="report-content">
      <div class="report-item" v-for="item in reportList" :key="item.id">
        <div class="item-head">
          <div class="title">{{ item.title }}</div>
          <div class="view-btn" @click="toggleViewReport(item)">{{ item.expand ? '收起' : '查看报告' }}</div>
        </div>
        <div class="report-main" v-if="item.expand">
          <div class="aside-wrap">
            <div class="aside-title">测评报告内容</div>
            <div class="aside-item" v-for="item in asideList" :key="item.title">
              <div class="item-title">{{ item.title }}</div>
              <div
                class="item-list"
                :class="{ active: activeAside == list.id }"
                v-for="list in item.list"
                :key="list.id"
                @click="change(list)"
              >
                {{ list.name }}
              </div>
            </div>
          </div>
          <div class="report-page-content">
            <div class="title">{{ activeAsideName }}</div>
            <div class="page-title-line">
              <span>整体得分与所处阶段</span>
              <div class="line"></div>
              <div class="ai-btn">AI解读</div>
            </div>
            <div class="flex-box">
              <div class="box-item">
                <div class="content">
                  <div class="item">
                    <div class="title">能力指数</div>
                    <img src="@/assets/imgs/manageView/report-1.png" alt="" srcset="" />
                  </div>
                  <div class="item">
                    <div class="title">能力阶段</div>
                    <img style="margin-top: 40px" src="@/assets/imgs/manageView/report-2.png" alt="" srcset="" />
                  </div>
                </div>
              </div>
              <div class="box-item">
                <div class="content jiedu">
                  <div class="jiedu">整体得分与级别详情</div>
                  <div class="jiedu-text">
                    本次测评整体得分 61.8 分，处于 规范级 阶段，距离上一层级 优秀级 ，还有 8.2
                    分的差距；在所有参与测评的企业中，排名 4238 名，位列全部企业的 64.9% 位置；同行业排名 182
                    名，位列行业企业的 71.9% 位置；
                  </div>
                </div>
              </div>
            </div>
            <div class="page-title-line">
              <span>各项能力对标</span>
              <div class="line"></div>
              <div class="ai-btn">AI解读</div>
            </div>
            <div class="flex-box">
              <div class="box-item">
                <div class="content">
                  <div class="item">
                    <img style="margin-top: 9px" src="@/assets/imgs/manageView/report-3.png" alt="" srcset="" />
                  </div>
                </div>
              </div>
              <div class="box-item">
                <div class="content jiedu">
                  <div class="jiedu">各项能力对比详情</div>
                  <div class="jiedu-text">
                    ‌营销与销售‌：27.0分，相对较低，需加强市场洞察、品牌建设和渠道管理能力。
                    ‌产品研发‌：23.1分，同样较低，需提升产品规划、创新机制与敏捷开发能力。
                    ‌采购管理‌：30.1分，处于中等水平，需进一步优化供应商管理、成本控制和风险管理。
                    ‌生产管理‌：38.7分，表现相对较好，但仍需持续优化设备维护、工艺标准化和生产成本。
                    ‌供应链产销协同‌：32.5分，需加强需求预测、库存优化和物流网络规划。
                    ‌组织人才发展‌：21.9分，最低，需重点提升人才梯队建设、绩效与激励机制和培训发展体系。
                    ‌产业分析与跟踪‌：24.8分，需加强行业趋势分析、政策法规跟踪和市场需求洞察。
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.report-page {
  .head-title {
    font-size: 16px;
    color: #888888;
    line-height: 20px;
    margin-bottom: 16px;
  }
}
.report-content {
  .report-item {
    margin-bottom: 15px;

    .item-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 26px 28px;
      background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      margin-bottom: 15px;
      .title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
      }
      .view-btn {
        font-size: 14px;
        color: #fff;
        cursor: pointer;
        line-height: 30px;
        background: #40a0ff;
        border-radius: 3px 3px 3px 3px;
        text-align: center;
        padding: 0 22px;
      }
    }
    .report-main {
      display: flex;
      flex-flow: row nowrap;
      gap: 18px;
      .aside-wrap {
        flex: 0 0 198px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 8px 8px 8px 8px;
        padding: 14px 9px;
        .aside-title {
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          line-height: 20px;
          margin-bottom: 26px;
        }
        .aside-item {
          text-align: center;
          .item-title {
            font-weight: 600;
            font-size: 16px;
            color: #333333;
            line-height: 50px;
          }
          .item-list {
            line-height: 42px;
            border-radius: 5px 5px 5px 5px;
            border: 1px solid transparent;
            color: #333;
            font-size: 14px;
            cursor: pointer;
            &.active {
              border-color: #53a9f9;
              color: #53a9f9;
            }
          }
        }
      }
      .report-page-content {
        flex: 1;
        background: #ffffff;
        box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
        border-radius: 8px 8px 8px 8px;
        padding: 20px 30px;
        .title {
          font-weight: 500;
          font-size: 16px;
          color: #53a9f9;
          margin-bottom: 20px;
        }
        .page-title-line {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 20px;
          .line {
            flex: 1;
            height: 1px;
            background: #e5e5e5;
          }
          .ai-btn {
            width: 73px;
            text-align: center;
            line-height: 30px;
            background: #e1f3ff;
            border-radius: 30px;
            font-weight: 500;
            font-size: 16px;
            color: #40a0ff;
            cursor: pointer;
          }
        }
        .value-stage {
          margin-bottom: 25px;
        }
        .flex-box {
          display: flex;
          align-items: stretch;
          gap: 28px;
          margin-bottom: 30px;
          .box-item {
            flex: 1;
            .content {
              display: flex;
              gap: 22px;
              &.jiedu {
                flex-flow: column;
                height: 100%;
                padding: 18px 21px;
                background: #e3efff;
                border-radius: 6px 6px 6px 6px;
              }
              .item {
                flex: 1;
                .title {
                  width: 100%;
                  line-height: 30px;
                  background: #eaf4ff;
                  border-radius: 51px;
                  font-weight: 600;
                  text-align: center;
                  font-size: 16px;
                  color: #40a0ff;
                }
              }
              img {
                width: 100%;
                height: auto;
              }
              .jiedu-title {
                font-size: 16px;
                color: #40a0ff;
                line-height: 35px;
              }
              .jiedu-text {
                font-size: 14px;
                color: #3d3d3d;
                line-height: 31px;
              }
            }
          }
        }
      }
      .border {
        padding: 16px 25px;
        background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #c6dbf3;
      }
    }
  }
}
</style>
