<script setup>
defineOptions({ name: "simplenessTable" });
const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  columns: {
    type: Array,
    required: true,
  },
  // 是否设置table边框圆角
  roundBorder: {
    type: Boolean,
    default: true,
  },
  showSelect: {
    type: Boolean,
    default: false,
  },
  showIndex: {
    type: Boolean,
    default: false,
  },
  showIndexAlgin: {
    type: String,
    default: "center",
  },
  headerColor: {
    type: Boolean,
    default: false,
  },
});

const simplenessTableRef = ref(null);

defineExpose({
  simplenessTableRef: simplenessTableRef,
});
</script>
<template>
  <div
    class="simpleness-table"
    :class="{ 'round-border': roundBorder, 'header-color': headerColor }"
  >
    <el-table ref="simplenessTableRef" :data="data" v-bind="$attrs">
      <el-table-column v-if="showSelect" type="selection" width="55" />
      <el-table-column
        v-if="showIndex"
        label="序号"
        :align="showIndexAlgin"
        type="index"
        width="55"
      />
      <el-table-column
        v-for="col in columns"
        v-bind="{ ...col }"
        :key="col.prop || col.label"
      >
        <template v-if="col.children">
          <el-table-column
            v-for="colChildren in col.children"
            v-bind="{ ...colChildren }"
            :key="colChildren.prop || colChildren.label"
          >
            <template v-if="colChildren.children">
              <el-table-column
                v-for="colSon in colChildren.children"
                v-bind="{ ...colSon }"
                :key="colSon.prop || colSon.label"
              >
                <template v-if="colSon.slot" #default="scope">
                  <slot :name="colSon.slot" :row="scope.row"></slot>
                </template>
              </el-table-column>
            </template>
            <template v-if="colChildren.slot" #default="scope">
              <slot :name="colChildren.slot" :row="scope.row"></slot>
            </template>
          </el-table-column>
        </template>
        <template v-if="col.slot" #default="scope">
          <slot :name="col.slot" :row="scope.row"></slot>
        </template>
      </el-table-column>

      <slot></slot>
      <slot name="oper"></slot>
    </el-table>
  </div>
</template>
<style lang="scss" scoped>
.simpleness-table {
  // 设置table边框圆角
  width: 100%;
  :deep(.el-table) {
    width: 100%;
    --el-font-size-base: 14px;
    --el-table-header-text-color: #93abcb;
    --el-table-border-color: #c6dbf3;
    --el-table-tr-bg-color: transparent;
    --el-table-header-bg-color: transparent;
    background-color: transparent;
    .el-table__header .el-table__cell {
      // 隐藏table底边框
      background-color: transparent;
    }
  }
  &.round-border {
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #c6dbf3;
    padding: 0 15px;
    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        // 隐藏table底边框
        background-color: transparent;
      }
      tr:last-of-type {
        td {
          // 隐藏table底边框
          border-bottom: none;
        }
      }
    }
  }
  &.header-color {
    :deep(.el-table) {
      .el-table__header {
        // 隐藏table底边框
        background-color: #eff3f6;
      }
    }
  }
}
</style>
