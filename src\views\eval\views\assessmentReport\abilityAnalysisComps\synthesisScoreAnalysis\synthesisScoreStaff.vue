<template>
    <div class="">
        <div class="page_third_title">能力词典得分
            <div class="fr">
                <el-button type="primary" @click="exportExcel" size="mini">导出</el-button>
            </div>
        </div>
        <table-component
            :tableData="tableData"
            :needIndex="true"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
        ></table-component>
    </div>
</template>
 
<script>
    import { competenceDictionaryScore,allExportData } from "../../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "synthesisScoreStaff",
        props: ["orgCode","evalId"],
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading:false,
                tableData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "orgName",
                        },
                        {
                            label: "岗位",
                            prop: "postName",
                        },
                        {
                            label: "姓名",
                            prop: "objectName",
                            width: 80,
                        },
                        {
                            label: "能力模块",
                            prop: "moduleNames",
                        },
                        {
                            label: "能力词典",
                            prop: "moduleName",
                        },
                        {
                            label: "能力目标",
                            prop: "expectedScore",
                        },
                        {
                            label: "实际",
                            prop: "overallScore",
                        },
                        {
                            label: "差距",
                            prop: "disparity",
                        },
                        {
                            label: "匹配度",
                            prop: "matching",
                            formatterFun: function (data) {
                                return data["matching"] + "%";
                            },
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        components: {
            tableComponent,
        },
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
        },

        methods: {
            getData() {
                this.loading = true;
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                competenceDictionaryScore(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                        this.loading = false;
                    }else{
                        this.loading = false;
                    }
                });
            },
            handleSizeChange(size) {
                this.pageSize = size;
                this.getData();
            },
            handleCurrentChange(current) {
                this.currPage = current;
                this.getData();
            },
            exportExcel(){
                let params = {
                    evalId:this.evalId,
                    orgCode:this.orgCode,
                    type:'item'
                }
                allExportData(params).then(res => {
                    console.log(res);
                    this.$exportDownload(res.data,'能力词典得分');
                })
            }
        },
    };
</script>
 
<style scoped lang="scss">
</style>