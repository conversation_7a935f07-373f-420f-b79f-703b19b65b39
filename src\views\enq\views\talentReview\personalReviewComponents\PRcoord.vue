<template>
  <div class="coord_wrap">
    <div class="page_second_title marginT_16">
      <span>协同网络</span>
    </div>
    <div class="coord_content">
      <div class="coord_title">
        1、请挑选出日常工作中打交道人，最少3个，最多10个人，包括：在工作中有直接联系的人，或经常寻求建议、帮助或向其学习请教的人，可以跨部门
      </div>
      <div class="staff_item_wrap">
        <div class="staff_item" v-for="item in staffList" :key="item.coordinationId">
          <el-icon class="close_icon" size="16" name="close" @click="() => deleteStaff([item])"></el-icon>
          <div class="name">{{ item.userName }}</div>
          <div class="dept">{{ item.orgName }}</div>
          <div class="post">{{ item.postName }}</div>
        </div>
      </div>
      <div class="marginB_16" v-if="staffList.length < 10">
        <el-button type="primary" @click="showDialogFn">新增人员</el-button>
      </div>
      <div class="coord_title">2、请选择与他们的协同方式，可以多选</div>
      <div class="coord_type">
        <div class="coord_type_content">
          <div class="coord_type_head">
            <div class="item flex_row_between">
              <b>人员</b>
            </div>
            <div class="item" v-for="item in coordTypeList" :key="item.dictCode">
              <div class="dict_name">{{ item.codeName }}</div>
              <div>{{ item.codeDesc }}</div>
            </div>
          </div>
          <div class="coord_type_main">
            <div class="row" v-for="(item, index) in coordTypeStaffList" :key="item.coordinationId">
              <div class="item user_info">
                <span class="index">{{ index + 1 }}</span>
                <span class="info">
                  {{ item.userName }}
                  {{ item.orgName }}
                  {{ item.postName }}
                </span>
              </div>
              <div class="item" v-for="list in coordTypeList" :key="list.dictCode">
                <div
                  class="border"
                  :class="{
                    checked: calcStatus(item, list.dictCode)
                  }"
                  @click="() => toggleCheck(item, list.dictCode)"
                >
                  <el-icon class="el-icon-check" v-if="calcStatus(item, list.dictCode)"><Check /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="confirm_btn">
          <el-button type="primary" size="mini" @click="saveCoordType">保存</el-button>
        </div>
      </div>
    </div>
    <div class="text_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevStep" v-show="currentIndex !== currentFirstCode"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="nextStep">{{ nextBtnText }}</el-button>
    </div>
    <el-dialog title="提示" v-model="showDialog" width="80%" :before-close="closeDialog">
      <div class="page_section staff_management_center clearfix">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div>部门分类</div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio
              :treeData="treeData"
              :defaultExpandAll="false"
              :expandedLevel="2"
              @clickCallback="deptChange"
            ></tree-comp-radio>
          </div>
        </div>
        <div class="table_wrap page_section_main page_shadow">
          <div class="filter_bar_wrap">
            <div class="flex_row_start">
              <div class="filter_item">
                <el-select v-model="flag" placeholder="是否已选" clearable>
                  <el-option
                    v-for="item in yesOrNo"
                    :label="item.codeName"
                    :value="item.dictCode"
                    :key="item.dictCode"
                  ></el-option>
                </el-select>
              </div>
              <div class="filter_item">
                <el-input v-model="userName" placeholder="按姓名查询" suffix-icon="el-icon-search" clearable></el-input>
              </div>
            </div>
            <div class="control_btn flex_row_start">
              <div class="filter_item">
                <el-button type="primary" class="page_add_btn" @click="getTableData">查询</el-button>
              </div>
              <div class="filter_item">
                <el-button type="primary" class="page_add_btn" @click="closeDialog">确认</el-button>
              </div>
            </div>
          </div>
          <tableComponent
            :selectionStatus="true"
            :needIndex="true"
            :tableData="tableData"
            :size="'mini'"
            :needPagination="true"
            :checkSelection="checkSelection"
            @curSelectInfo="curSelectInfo"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          ></tableComponent>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio.vue'
import tableComponent from '@/components/talent/tableComps/tableComponent.vue'
import { getOrgDeptTree } from '@/views/entp/request/api'
import { Check } from '@element-plus/icons-vue'
import {
  getCoordinationUser,
  getCoordinationModeList,
  addUserCoordination,
  deleteCoordinationUser,
  coordinationType,
  addCoordType
} from '../../../request/api'
import { deepClone } from '@/utils/utils' // 你需要根据实际路径调整

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const useUser = useUserStore()
const staffList = ref([])
const coordTypeStaffList = ref([])
const coordTypeList = ref([])
const saveFlag = ref(false)
const size = ref(10)
const current = ref(1)
const submitFlag = ref(true)
const eduInfoData = ref([])
const showDialog = ref(false)
const userName = ref(null)
const flag = ref(null)
const yesOrNo = ref([])
const orgCode = ref(null)
const treeData = ref([])
const defaultCheckedKeys = ref([])
const tableData = reactive({
  columns: [
    { label: '员工姓名', prop: 'userName', width: 100 },
    { label: '一级组织', prop: 'oneLevelName' },
    { label: '二级组织', prop: 'twoLevelName' },
    { label: '三级组织', prop: 'threeLevelName' },
    { label: '四级组织', prop: 'fourLevelName' },
    { label: '五级组织', prop: 'fiveLevelName' },
    { label: '任职岗位', prop: 'postName' },
    { label: '职层', prop: 'jobLevelName' }
  ],
  page: {
    total: 0,
    current: 1,
    size: 10
  },
  data: []
})

const userId = computed(() => useUser.userInfo.userId)
const postCode = computed(() => useUser.userInfo.postCode)
const companyId = computed(() => useUser.userInfo.companyId)
const checkSelection = computed(() => tableData.data.filter(item => item.flag == 'Y'))

watch(staffList, newValue => {
  saveFlag.value = false
  coordTypeStaffList.value = deepClone(newValue)
})

onMounted(() => {
  init()
})

async function init() {
  let dicts = await useUser.getDocList(['YES_NO', 'EMPLOYEE_STATUS'])
  yesOrNo.value = dicts.YES_NO

  let orgTree = await getOrgDeptTree({
    companyId: companyId.value,
    status: 'all'
  })
  if (orgTree.code == 200) {
    treeData.value = orgTree.data.length > 0 ? orgTree.data : []
  }
  getStaffList()
  getCoordinationType()
}

async function getStaffList() {
  let res = await getCoordinationModeList({ enqId: props.enqId })
  if (res.code == 200) {
    staffList.value = res.data
  } else {
    ElMessage.error(res.msg)
  }
}

function deptChange(code) {
  orgCode.value = code
  getTableData()
}

function showDialogFn() {
  showDialog.value = true
  getTableData()
}

function getTableData() {
  let params = {
    enqId: props.enqId,
    size: size.value,
    current: current.value,
    orgCode: orgCode.value,
    flag: flag.value,
    userName: userName.value
  }
  getCoordinationUser(params).then(res => {
    tableData.data = res.data
    tableData.page.total = res.total
  })
}

function addstaff(list) {
  addUserCoordination(list).then(() => {
    getTableData()
    getStaffList()
  })
}

function deleteStaff(userList, isFetchTableData = false) {
  let params = userList.map(item => ({
    coordinationId: item.coordinationId,
    userId: userId.value,
    postCode: postCode.value,
    enqId: props.enqId
  }))
  deleteCoordinationUser(params).then(() => {
    if (isFetchTableData) {
      getTableData()
    } else {
      getStaffList()
    }
  })
}

function curSelectInfo(selectList, row) {
  let { flag: rowFlag, userId: coordinationId } = row
  let selectArr = [
    {
      coordinationId,
      userId: userId.value,
      postCode: postCode.value,
      enqId: props.enqId
    }
  ]
  if (rowFlag == 'Y') {
    row.coordinationId = row.userId
    deleteStaff([row], true)
  } else {
    if (staffList.value.length < 10) {
      addstaff(selectArr)
    } else {
      ElMessage.warning('最多只能选10人')
      getTableData()
    }
  }
}

function closeDialog(done) {
  showDialog.value = false
  getStaffList()
  if (typeof done == 'function') done()
}

function handleSizeChange(val) {
  size.value = val
  current.value = 1
  getTableData()
}

function handleCurrentChange(val) {
  current.value = val
  getTableData()
}

function getCoordinationType() {
  coordinationType().then(res => {
    if (res.code == 200) {
      coordTypeList.value = res.data
    }
  })
}

function toggleCheck(item, code) {
  if (!item.coordinationTypeList) item.coordinationTypeList = []
  const idx = item.coordinationTypeList.indexOf(code)
  if (idx !== -1) {
    item.coordinationTypeList.splice(idx, 1)
  } else {
    item.coordinationTypeList.push(code)
  }
}

function calcStatus(item, code) {
  return item.coordinationTypeList && item.coordinationTypeList.includes(code)
}

function saveCoordType(stepType) {
  if (!hasEmptyData(coordTypeStaffList.value)) {
    addCoordType(coordTypeStaffList.value).then(res => {
      if (res.code == 200) {
        ElMessage.success(res.msg)
        getStaffList()
        saveFlag.value = true
        if (stepType) emit(stepType)
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}

function hasEmptyData(data) {
  for (const obj of data) {
    if (!obj.coordinationTypeList || obj.coordinationTypeList.length == 0) {
      ElMessage.warning(`${obj.userName} 未分配协同方式`)
      return true
    }
  }
  return false
}

function prevStep() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      saveCoordType('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}

function nextStep() {
  if (staffList.value.length == 0) {
    ElMessage.warning('请添加协同网络人员!')
    return
  }
  if (hasEmptyData(staffList.value)) {
    return
  }
  emit('nextStep')
}
</script>

<style scoped lang="scss">
.coord_wrap {
  .coord_content {
    padding-left: 6px;
  }
  .coord_title {
    color: #449cff;
    margin-bottom: 16px;
  }
  .staff_item_wrap {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
    .staff_item {
      position: relative;
      border: 1px solid #35c1f4;
      border-radius: 4px;
      padding: 10px 15px;
      margin-right: 12px;
      margin-bottom: 10px;
      background-color: #e5f7fd;
      font-size: 12px;
      color: #00b0f0;
      .close_icon {
        position: absolute;
        top: 5px;
        right: 5px;
        cursor: pointer;
      }
      .name {
        font-size: 14px;
      }
      .dept {
      }
      .post {
      }
    }
  }
}
.coord_type_content {
  text-align: center;
  margin-bottom: 10px;
  .item {
    flex: 1;
  }
  .coord_type_head {
    display: flex;
    align-items: stretch;
    line-height: 24px;
    color: #00b0f0;
    margin-bottom: 12px;
    .item {
      background-color: #deebf7;
      margin: 0 5px;
      padding: 5px 10px;
      .dict_name {
        font-weight: bold;
      }
    }
  }
  .coord_type_main {
    .row {
      display: flex;
      align-items: center;
      line-height: 36px;
      margin-bottom: 5px;
      .user_info {
        display: flex;
        justify-content: flex-start;
        width: 100%;
        text-align: left;
        span {
          flex: 1;
          background: #f2f2f2;
          margin-right: 5px;
          &.index {
            text-align: center;
            flex: 0 0 50px;
          }
        }
        .info {
          padding: 0 10px;
          margin-right: 0;
        }
      }
      .item {
        padding: 0 5px;
      }
      .border {
        height: 38px;
        border: 1px solid #d9d9d9;
        cursor: pointer;
        &.checked {
          border-color: #00b0f0;
          background-color: #e5f7fd;
          color: #00b0f0;
          font-weight: bold;
          font-size: 18px;
        }
      }
    }
  }
}
.el-table__fixed-header-wrapper .el-table_1_column_1 .el-checkbox {
  display: none;
}
</style>
