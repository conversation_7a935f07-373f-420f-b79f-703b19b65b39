<template>
    <div class="ability_classify_wrap">
        <div class="ability_classify_main flex_row_betweens">
            <div class="main_left page_section">
                <div class="ability_classify_title flex_row_betweens">
                    <div class="page_second_title">能力分类</div>
                    <div class="align_right" v-if="buildStatus !== '4'">
                        <el-button
                            class="icon_plus"
                            type="primary"
                            size="mini"
                            @click="toggleModel"
                            >选择</el-button
                        >
                        <el-button
                            class="icon_plus"
                            icon="el-icon-plus"
                            link
                            size="mini"
                            @click="creatDegree"
                        ></el-button>
                    </div>
                </div>
                <ul class="degree_list_wrap">
                    <li
                        class="flex_row_betweens"
                        :class="{ active: index == currIndex }"
                        v-for="(item, index) in degreeList"
                        @click="selectItem(index)"
                    >
                        <div class="left">{{ item.moduleName }}</div>
                        <div class="icon_group" v-if="buildStatus !== '4'">
                            <!--                            <span class="el-icon-edit icon_edit" @click="editorDegree"></span>-->
                            <span
                                class="el-icon-delete icon_del"
                                @click="removeDegree(item.moduleCode, index)"
                            ></span>
                        </div>
                    </li>
                    <li class="no_data_tip" v-if="degreeList.length == 0">
                        暂无数据
                    </li>
                </ul>
            </div>
            <div class="main_right" :class="{ event_none: buildStatus == '4' }">
                <div class="degree_info_wrap" v-show="degreeList.length">
                    <!-- <div class="line_item_wrap">
                        <div class="descript">
                            分类名称<span class="tips"></span>
                        </div>
                        <div class="input_wrap">
                            <el-input
                                v-model="classifyInfo.moduleName"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line_item_wrap">
                        <div class="descript">
                            分类属性<span class="tips"></span>
                        </div>
                        <div class="input_wrap">
                            <el-select
                                v-model="classifyInfo.competenceClass"
                                clearable
                                placeholder="请选择"
                                :disabled="buildStatus == '4'"
                            >
                                <el-option
                                    v-for="item in moduleTypeList"
                                    :key="item.value"
                                    :label="item.codeName"
                                    :value="item.dictCode"
                                >
                                </el-option>
                            </el-select>
                        </div>
                    </div>

                    <div class="line_item_wrap">
                        <div class="descript">分类说明</div>
                        <div class="input_wrap">
                            <el-input
                                type="textarea"
                                v-model="classifyInfo.moduleDesc"
                                rows="5"
                                resize="none"
                            ></el-input>
                        </div>
                    </div>
                    <div class="align_center" v-if="buildStatus !== '4'">
                        <el-button
                            class="page_add_btn"
                            type="primary"
                            @click="submitItem"
                            >确认</el-button
                        >
                    </div> -->
                    <el-form
                        :model="ruleForm"
                        :rules="rules"
                        ref="ruleForm"
                        label-position="top"
                        class="demo-ruleForm"
                    >
                        <el-form-item label="分类名称" prop="moduleName">
                            <el-input v-model="ruleForm.moduleName"></el-input>
                        </el-form-item>
                        <el-form-item label="分类属性" prop="competenceClass">
                            <el-select
                                v-model="ruleForm.competenceClass"
                                clearable
                                placeholder="请选择"
                                :disabled="buildStatus == '4'"
                            >
                                <el-option
                                    v-for="item in moduleTypeList"
                                    :key="item.value"
                                    :label="item.codeName"
                                    :value="item.dictCode"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="分类说明" prop="moduleDesc">
                            <el-input
                                type="textarea"
                                v-model="ruleForm.moduleDesc"
                                rows="5"
                                resize="none"
                                placeholder="最多256个字符"
                            ></el-input>
                        </el-form-item>
                        <div class="align_center" v-if="buildStatus !== '4'">
                            <el-button
                                class="page_add_btn"
                                type="primary"
                                @click="submitItem"
                                >确认</el-button
                            >
                        </div>
                    </el-form>
                </div>
            </div>
        </div>
        <div class="align_center" v-if="buildStatus !== '4'">
            <el-button class="page_confirm_btn" type="primary" @click="prev()"
                >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="next()"
                >下一步</el-button
            >
        </div>
        <div class="popup_wrap">
            <el-dialog :title="popupName" :visible.sync="dialogFormVisible">
                <div class="line_item_wrap">
                    <div class="descript">分类名称</div>
                    <div class="input_wrap">
                        <el-input
                            ref="classifyName"
                            v-model="classifyName"
                            @keyup.enter.native="addClassify"
                        ></el-input>
                    </div>
                </div>
                <div slot="footer" class="dialog-footer">
                    <el-button
                        class="page_clear_btn"
                        @click="dialogFormVisible = false"
                        >取 消</el-button
                    >
                    <el-button
                        class="page_add_btn"
                        type="primary"
                        @click="addClassify"
                        >确 定</el-button
                    >
                </div>
            </el-dialog>
        </div>
        <!-- 选择能力模块弹窗 -->
        <el-dialog
            title="选择模块"
            :width="'80%'"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :visible.sync="dialogTableVisible"
        >
            <div class="filter_bar_wrap">
                <div class="flex_row_start">
                    <div class="filter_item title">筛选:</div>
                    <div class="filter_item">
                        <el-select
                            v-model="dialogFilter.modelId"
                            clearable
                            placeholder="所属模型"
                            
                        >
                            <el-option
                                v-for="item in modelOption"
                                :key="item.modelId"
                                :label="item.modelName"
                                :value="item.modelId"
                            ></el-option>
                        </el-select>
                    </div>
                    <!-- <div class="filter_item">
                        <el-select
                            v-model="dialogFilter.moduleCode"
                            clearable
                            placeholder="所属模块"
                            
                        >
                            <el-option
                                v-for="item in moduleTypeList"
                                :key="item.codeName"
                                :label="item.codeName"
                                :value="item.dictCode"
                            ></el-option>
                        </el-select>
                    </div> -->
                    <div class="filter_item">
                        <el-input
                            v-model="dialogFilter.moduleName"
                            placeholder="按模块名称检索"
                            clearable
                        />
                    </div>
                    <div class="filter_item">
                        <el-input
                            v-model="dialogFilter.moduleDesc"
                            placeholder="按模块描述检索"
                            clearable
                        />
                    </div>

                    <div class="filter_item">
                        <el-button
                            class="page_add_btn"
                            type="primary"
                            
                            @click="getModelList"
                            >查询</el-button
                        >
                    </div>
                </div>
            </div>
            <tableComponent
                :needIndex="true"
                :tableData="gridData"
                @page="pageChange"
            >
                <template v-slot:oper>
                    <el-table-column label="操作" width="100">
                        <template slot-scope="scope">
                            <el-button
                                @click.native.prevent="
                                    rowChoose(
                                        scope.$index,
                                        gridData.data[scope.$index]
                                    )
                                "
                                :type="scope.row.status ? '' : 'primary'"
                                size="mini"
                                >{{
                                    scope.row.status ? "取消" : "确定"
                                }}</el-button
                            >
                            <!-- <el-button
                                class="color_danger"
                                @click.native.prevent="
                                    tableDeleteRow(scope.$index, tableData.data)
                                "
                                link
                                
                                >删除</el-button
                            > -->
                        </template>
                    </el-table-column>
                </template>
            </tableComponent>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogTableVisible = false">取 消</el-button>
                <!-- <el-button type="primary" @click="dialogComfirm"
                    >确 定</el-button
                > -->
            </span>
        </el-dialog>
    </div>
</template>

<script>
    import {
        getModelModule,
        createModelModule,
        deleteModelModule,
        getDictItem,
        getCapabilityCategory,
        getModelCheckList
    } from "../../../request/api";

    import tableComponent from "@/components/talent/tableComps/tableComponent";

    export default {
        name: "abilityClassify",
        props: ["modelId", "buildStatus"],
        components: { tableComponent },
        data() {
            return {
                currIndex: 0,
                degreeList: [],
                alias: "",
                dialogFormVisible: false,
                classifyName: "",
                popupName: "",
                classificationAttribute: "",
                moduleTypeList: [],
                ruleForm: {
                    moduleName: "",
                    competenceClass: "",
                    moduleDesc: "",
                },
                rules: {
                    moduleName: [
                        {
                            required: true,
                            message: "请输入分类名称",
                            trigger: "blur",
                        },
                    ],
                    competenceClass: [
                        {
                            required: true,
                            message: "请选择分类属性",
                            trigger: "change",
                        },
                    ],
                    moduleDesc: [
                        {
                            required: true,
                            message: "请填写分类说明",
                            trigger: "blur",
                        },
                        {
                            min: 0,
                            max: 256,
                            message: "最多256个字符",
                            trigger: "blur",
                        },
                    ],
                },
                classifyInfo: {},
                moduleInfo: {}, // 表格选中的模块信息
                dialogTableVisible: false,
                modelOption:[],
                dialogFilter: {
                    modelId: null,
                    moduleCode: null,
                    moduleName: null,
                    moduleDesc: null,
                    current: 1,
                    size: 10,
                },
                gridData: {
                    columns: [
                        {
                            label: "模块编码",
                            prop: "moduleCode",
                        },
                        {
                            label: "模块名称",
                            prop: "moduleName",
                        },
                        {
                            label: "所属模型",
                            prop: "modelName",
                        },
                        {
                            label: "模块描述",
                            prop: "moduleDesc",
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
            };
        },
        created() {
            this.getDictItemFun("COMPETENCE_CLASS").then((res) => {
                this.moduleTypeList = res;
            });
            this.getModelCheckListFn();
            if (this.modelId) {
                this.getModelModuleFun();
            }
        },
        methods: {
            // 获取模型列表
            getModelCheckListFn(){
                getModelCheckList().then(res => {
                    console.log(res);
                    this.modelOption = res;
                })
            },
            //获取分类属性
            async getDictItemFun(dictCode) {
                return await getDictItem({
                    dictId: dictCode,
                }).then((res) => {
                    if (res.code == 200) {
                        return res.data;
                    }
                });
            },
            creatDegree() {
                this.dialogFormVisible = true;
                this.popupName = "新增";
                this.$nextTick(() => {
                    this.$refs.classifyName.focus();
                });
            },
            // 获取模块列表
            getModelList() {
                getCapabilityCategory(this.dialogFilter).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.gridData.data = res.data;
                        this.gridData.page = res.page;
                    }
                });
            },
            // 弹窗显隐
            toggleModel() {
                this.dialogTableVisible = !this.dialogTableVisible;
                if (this.dialogTableVisible) {
                    this.getModelList();
                }
            },
            // 选择能力分类
            chooseDegree() {},
            // 表格行确定按钮
            rowChoose(index, row) {
                console.log(index, row);
                this.moduleInfo = row.status ? {} : row;
                row.status = !row.status;
                console.log(this.moduleInfo);
                this.degreeList.push(this.$util.deepClone(this.moduleInfo));
                this.toggleModel();
                this.currIndex = this.degreeList.length - 1;
                this.ruleForm = this.degreeList[this.currIndex];
                this.ruleForm.moduleCode = null;
            },
            // 弹窗确定按钮
            dialogComfirm() {
                if (this.$util.isEmptyObj(this.moduleInfo)) {
                    this.$msg.warning("请选择模型");
                    return;
                }
                this.degreeList.push(this.$util.deepClone(this.moduleInfo));
                this.toggleModel();
                this.currIndex = this.degreeList.length - 1;
                this.ruleForm = this.degreeList[this.currIndex];
            },
            editorDegree() {
                this.dialogFormVisible = true;
                this.popupName = "编辑";
            },
            //
            pageChange(size, page) {
                this.dialogFilter.size = size;
                this.dialogFilter.current = page;
                this.getModelList();
            },
            //删除按钮
            removeDegree(moduleCode, index) {
                this.$confirm("此操作将删除该分类, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        if (!moduleCode) {
                            this.degreeList.splice(index, 1);
                            this.$message({
                                type: "success",
                                message: "删除成功!",
                            });
                            this.currIndex = 0;
                            this.getModelModuleFun();
                        } else {
                            this.deleteModelModuleFun(moduleCode);
                        }
                    })
                    .catch(() => {});
            },
            //添加按钮
            addClassify() {
                if (!this.classifyName) {
                    this.$msg.warning("请输入分类名称");
                    return;
                }
                this.degreeList.push({
                    moduleCode: "",
                    moduleName: this.classifyName,
                    competenceClass: "",
                    moduleDesc: "",
                });
                this.dialogFormVisible = false;
                this.classifyName = "";
                this.currIndex = this.degreeList.length - 1;
                this.ruleForm = this.degreeList[this.currIndex];
            },
            selectItem(index) {
                this.currIndex = index;
                this.ruleForm = this.degreeList[index];
            },
            submitItem() {
                this.submitForm("ruleForm");
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.createModelModuleFun();
                    } else {
                        console.log("error submit!!");
                        return false;
                    }
                });
            },
            prev() {
                this.$emit("prevStep");
            },
            next() {
                if (!this.degreeList.length > 0) {
                    this.$msg.warning("请添加能力分类");
                    return;
                } else {
                    for (let index = 0; index < this.degreeList.length; index++) {
                        const item = this.degreeList[index];
                        if (item.moduleCode == "") {
                            this.$msg.warning(
                                `能力分类:${item.moduleName} 信息未确认`
                            );
                            return;
                        }
                    }
                }
                this.$emit("nextStep");
            },
            //获取能力分类
            getModelModuleFun() {
                getModelModule({
                    modelId: this.modelId,
                }).then((res) => {
                    console.log(res);
                    this.degreeList = res;
                    if (this.degreeList.length > 0) {
                        this.ruleForm = this.degreeList[this.currIndex];
                    } else {
                        this.ruleForm = {
                            moduleName: "",
                            competenceClass: "",
                            moduleDesc: "",
                        };
                    }
                });
            },
            //创建、修改能力分类
            createModelModuleFun() {
                let params = {
                    modelId: this.modelId,
                    layerNo: 1,
                    moduleCode: this.ruleForm.moduleCode,
                    moduleName: this.ruleForm.moduleName,
                    moduleDesc: this.ruleForm.moduleDesc,
                    competenceClass: this.ruleForm.competenceClass,
                };
                console.log(params);
                createModelModule(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$msg.success(res.msg);
                        this.currIndex = 0;
                        this.getModelModuleFun();
                    }
                });
            },
            //删除能力分类
            deleteModelModuleFun(moduleCode) {
                let params = {
                    modelId: this.modelId,
                    moduleCode: moduleCode,
                };
                deleteModelModule(params).then((res) => {
                    if (res.code == 200) {
                        this.$msg.success("删除成功！");
                        this.getModelModuleFun();
                        this.currIndex = 0;
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .ability_classify_wrap {
        .ability_classify_main {
            margin: 20px 0;

            .ability_classify_title {
                font-size: 14px;
                line-height: 30px;
                height: 30px;
                margin-bottom: 16px;
                border-radius: 3px;
            }

            .main_left {
                width: 40%;

                .ability_classify_title {
                    span {
                        margin: 0 0 0 10px;

                        &.creat {
                            font-weight: 700;
                            cursor: pointer;
                        }

                        &.creat:hover {
                            color: #4ab6fd;
                        }
                    }
                }

                .degree_list_wrap {
                    height: 400px;
                    overflow-y: auto;
                    border: 1px solid #e5e5e5;
                    /*margin-top: 12px;*/

                    li {
                        font-size: 12px;
                        padding: 0 8px;
                        line-height: 32px;
                        cursor: pointer;

                        .icon_group {
                            span {
                                margin: 0 0 0 10px;
                                cursor: pointer;
                                line-height: 32px;
                            }
                        }
                    }

                    .active {
                        cursor: pointer;
                        .left {
                            color: #fff;
                        }
                        .icon_del {
                            color: #fff !important;
                        }
                        background: #0099FF;
                    }
                }
            }

            .main_right {
                width: 60%;
                height: 400px;
                border: 1px solid #e5e5e5;
                margin-top: 50px;
                overflow-y: auto;

                .degree_info_wrap {
                    .line_item_wrap {
                        padding: 0 8px;
                        margin-bottom: 20px;

                        .descript {
                            height: 32px;
                            line-height: 32px;
                            font-size: 14px;
                            position: relative;
                            padding-left: 15px;
                            margin-top: 10px;

                            &::after {
                                content: "";
                                position: absolute;
                                width: 10px;
                                height: 10px;
                                left: 0;
                                top: 11px;
                                background: #0099FF;
                                border-radius: 50%;
                            }

                            .tips {
                                display: inline-block;
                                color: #f33333;
                                font-size: 12px;
                            }
                        }

                        .input_wrap {
                             .el-select {
                                width: 100%;
                            }
                        }
                    }

                    .btn {
                        margin: 10px 8px 0 10px;
                        text-align: right;
                    }
                }
            }
        }
    }

    .popup_wrap {
        .line_item_wrap {
            display: flex;

            .descript {
                width: 100px;
                height: 32px;
                line-height: 32px;
            }

            .input_wrap {
                width: 85%;
            }
        }
    }
    .demo-ruleForm {
        padding-left: 8px;
        padding-right: 8px;
    }
    .el-form--label-top .el-form-item__label {
        position: relative;
        padding-left: 15px;
    }
    .el-form-item.is-required:not(.is-no-asterisk)
        > .el-form-item__label:before {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        left: 0;
        top: 11px;
        background: #0099FF;
        border-radius: 50%;
    }
</style>