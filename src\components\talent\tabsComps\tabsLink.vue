<template>
  <div class="tabs_link_main" :class="{ vertical: isVertical }">
    <div class="tabs_link_item" v-for="item in tabsData" :key="item.id">
      <router-link class="tabs_link" :to="item.path" replace>{{ item.name }}</router-link>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
const props = defineProps({
  tabsData: {
    type: Array,
    default: () => []
  },
  isVertical: {
    type: Boolean,
    default: false
  }
})
</script>

<style scoped lang="scss">
.tabs_link_main {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  border-bottom: 2px solid #e4e7ed;
  &.vertical {
    float: left;
    width: auto;
    width: 100%;
    flex-flow: column;
    border: none;
    border-right: 2px solid #e4e7ed;
    .tabs_link_item {
      width: 100%;
      padding: 0 20px 0 10px;
      .tabs_link {
        width: 100%;
        line-height: 30px;
      }
      .router-link-active {
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: calc(100% + 20px);
          width: 2px;
          height: 100%;
          background: #409dff;
        }
      }
    }
  }
  .tabs_link_item {
    padding: 0 20px 0 0;
    .tabs_link {
      position: relative;
      display: inline-block;
      line-height: 40px;
      color: #303133;
    }
    .router-link-active {
      color: #0099ff;
      &::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        height: 2px;
        background: #409dff;
      }
    }
  }
}
</style>
