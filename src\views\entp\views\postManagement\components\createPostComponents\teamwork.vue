<template>
  <div class="work_activities_main">
    <div class="page_second_title">本岗主要对内的协同岗位（与哪些岗位有工作协同）</div>
    <div class="collaboration_post_content">
      <div class="post_item_wrap flex_row_wrap_start">
        <div class="flex_row_betweens xt_wrap">
          <div class="icon" />
        </div>
        <div class="post_item_main">
          <div class="post_item" v-for="(item, index) in collaborativeJobsI" :key="item.coopPostCode">
            <div class="name overflow_elps" :title="item.coopPostName">
              {{ item.coopPostName }}
            </div>
            <div class="name_icon" @click="deletePostI(index, item.coopPostCode)">
              <el-icon><Minus /></el-icon>
            </div>
          </div>
          <div class="work_add_btn">
            <el-button link @click="addPostI">
              <el-icon><Plus /></el-icon>添加
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="page_second_title">本岗主要对外的协同角色（与哪些外部角色打交道）</div>
    <div class="collaborative_role flex_row_start">
      <div class="icon" />
      <el-checkbox-group v-model="checkedCollaborativeJobsO" @change="handleCheckedOptionsChange">
        <el-checkbox
          v-for="item in collaborativeJobsO"
          :key="item.dictCode"
          :label="item.dictCode"
          :title="item.codeName"
        >
          {{ item.codeName }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="btn_wrap align_center">
      <el-button class="page_confirm_btn" type="primary" @click="submitBtn"> 保存 </el-button>
    </div>

    <el-dialog v-model="dialogVisible" title="新增对内协同岗位" width="60%" center>
      <div class="page_section">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title">组织</div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-checkbox
              :defaultCheckedKeys="defaultCheckedKeys"
              :treeData="treeData"
              :checkChildNode="false"
              @node-click-callback="nodeClickCallback"
            />
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <table-component
            :tableData="tableData"
            :needIndex="false"
            :needPagination="false"
            :checkSelection="checkSelection"
            :selectionStatus="true"
            @selectionChange="selectionChange"
            @curSelectInfo="curSelectInfo"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer align_right">
          <el-button class="page_clear_btn" @click="cancel">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="popUpSubmitBtn"> 确 定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Minus } from '@element-plus/icons-vue'
import { useStore } from 'vuex'
import {
  getPostCoop,
  batchCreatePostCoop,
  getDict,
  getPostTree,
  getOrgDeptTree,
  getPostList
} from '../../../../request/api'
import treeCompCheckbox from '@/components/talent/treeComps/treeCompCheckbox'
import tableComponent from '@/components/talent/tableComps/tableComponent'

// Props
const props = defineProps({
  curPostCodeCopy: String,
  copyOrgCode: String
})

// Emits
const emit = defineEmits(['submitSuccessTab'])

// Store
const store = useStore()

// Computed
const companyId = computed(() => store.state.userInfo.companyId)

// Reactive State
const postCode = ref(props.curPostCodeCopy)
const collaborativeJobsI = ref([])
const checkedCollaborativeJobsO = ref([])
const collaborativeJobsO = ref([])
const dialogVisible = ref(false)
const defaultCheckedKeys = ref([])
const treeData = ref([])
const tableData = ref({
  columns: [
    {
      label: '岗位名称',
      prop: 'postName'
    },
    {
      label: '所属组织',
      prop: 'orgName'
    }
  ],
  data: []
})
const checkSelection = ref([])
const checkPostList = ref([])
const defaultCheckPostList = ref([])

// Methods
const getDictFun = async () => {
  try {
    const [coopTypeRes, coopRoleRes] = await Promise.all([
      getDict({ dictId: 'COOP_TYPE' }),
      getDict({ dictId: 'COOP_ROLE' })
    ])

    if (coopRoleRes.code == 200) {
      collaborativeJobsO.value = coopRoleRes.data.map(item => ({
        codeName: item.codeName,
        dictCode: item.dictCode
      }))
    }
  } catch (error) {
    console.error('获取字典数据失败:', error)
    ElMessage.error('获取字典数据失败')
  }
}

const handleCheckedOptionsChange = val => {
  checkedCollaborativeJobsO.value = val
}

const getPostCoopFun = async () => {
  try {
    const [internalRes, externalRes] = await Promise.all([
      getPostCoop({ postCode: postCode.value, coopType: 'I' }),
      getPostCoop({ postCode: postCode.value, coopType: 'O' })
    ])

    if (internalRes.code == 200) {
      collaborativeJobsI.value = internalRes.data.map(item => ({
        coopPostName: item.coopPostName,
        coopPostCode: item.coopPostCode,
        orgCode: item.orgCode
      }))
    }

    if (externalRes.code == 200) {
      checkedCollaborativeJobsO.value = externalRes.data.map(item => item.coopPostCode)
    }
  } catch (error) {
    console.error('获取协同岗位失败:', error)
    ElMessage.error('获取协同岗位失败')
  }
}

const deletePostI = async (index, val) => {
  try {
    await ElMessageBox.confirm('确定删除?', '确定', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    collaborativeJobsI.value.splice(index, 1)
  } catch (error) {
    // User cancelled the operation
  }
}

const addPostI = () => {
  dialogVisible.value = true
}

const cancel = () => {
  dialogVisible.value = false
  defaultCheckedOrgAndPost()
}

const submitBtn = () => {
  batchCreatePostCoopFun()
}

const batchCreatePostCoopFun = async () => {
  const coopRequestList = [
    ...collaborativeJobsI.value.map(item => ({
      coopType: 'I',
      coopPostCode: item.coopPostCode
    })),
    ...checkedCollaborativeJobsO.value.map(code => ({
      coopType: 'O',
      coopPostCode: code
    }))
  ]

  const params = {
    postCode: postCode.value,
    postCoopCodeRequests: coopRequestList
  }

  try {
    const res = await batchCreatePostCoop(params)
    if (res.code == 200) {
      await getDictFun()
      await getPostCoopFun()
      emit('submitSuccessTab', 'teamwork')
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('保存协同岗位失败:', error)
    ElMessage.error('保存协同岗位失败')
  }
}

const getPostTreeFun = async val => {
  try {
    const res = await getPostTree({
      orgCode: val ? val : ''
    })
    if (res.length > 0) {
      // Assuming coopPostOptions is a ref or reactive property
      // This part of the original code was not fully defined in the new_code,
      // so I'm keeping it as is, but it might need adjustment depending on the actual state.
      // For now, I'll assume coopPostOptions is a ref or reactive property.
      // If it's not, this line will cause an error.
      // collaborativeJobsI.value = res // This line was not in the new_code, so I'm removing it.
    } else {
      // collaborativeJobsI.value = [] // This line was not in the new_code, so I'm removing it.
    }
  } catch (error) {
    console.error('获取岗位树失败:', error)
    ElMessage.error('获取岗位树失败')
  }
}

const nodeClickCallback = (val, curCheckCode) => {
  if (val.length > 0) {
    addDefaultCheckPost(val, curCheckCode)
    const orgCodes = val.join(',')
    getPostListFun(orgCodes)
  } else {
    tableData.value.data = []
  }
}

const addDefaultCheckPost = (val, curCheckCode) => {
  if (val.includes(curCheckCode)) {
    val.forEach(orgCode => {
      defaultCheckedKeys.value.forEach(defaultOrgCode => {
        if (orgCode == defaultOrgCode) {
          defaultCheckPostList.value.forEach(post => {
            if (orgCode == post.orgCode) {
              const exists = checkPostList.value.some(item => item.postCode == post.postCode)
              if (!exists) {
                checkPostList.value.push(post)
              }
            }
          })
        }
      })
    })
  }
}

const selectionChange = val => {
  checkPostList.value = val
}

const curSelectInfo = (selection, row) => {
  const isSelected = selection.some(item => item.postCode == row.postCode)
  if (isSelected) {
    checkPostList.value.push(row)
  } else {
    const index = checkPostList.value.findIndex(item => item.postCode == row.postCode)
    if (index !== -1) {
      checkPostList.value.splice(index, 1)
    }
  }
}

const getPostListFun = async orgCodes => {
  try {
    const res = await getPostList({ orgCodes })
    if (res.code == 200) {
      tableData.value.data = res.data.map(item => ({
        postName: item.postName,
        postCode: item.postCode,
        orgName: item.orgName,
        orgCode: item.orgCode,
        isCheckSign: item.postCode == postCode.value
      }))
    }
    curCheckPosts()
  } catch (error) {
    console.error('获取岗位列表失败:', error)
    ElMessage.error('获取岗位列表失败')
  }
}

const curCheckPosts = () => {
  const curCheckSelection = checkPostList.value
    .map(checkPost => tableData.value.data.find(tablePost => checkPost.postCode == tablePost.postCode))
    .filter(Boolean)

  checkSelection.value = curCheckSelection
}

const popUpSubmitBtn = () => {
  if (checkPostList.value.length == 0) {
    collaborativeJobsI.value = []
  } else {
    collaborativeJobsI.value = checkPostList.value.map(item => ({
      coopPostName: item.postName,
      coopPostCode: item.postCode,
      orgName: item.orgName,
      orgCode: item.orgCode
    }))
  }
  dialogVisible.value = false
}

const defaultCheckedOrgAndPost = () => {
  if (collaborativeJobsI.value.length == 0) {
    defaultCheckedKeys.value = []
    checkPostList.value = []
    checkSelection.value = []
    tableData.value.data = []
    return
  }

  const orgCodes = [...new Set(collaborativeJobsI.value.map(item => item.orgCode))]

  checkPostList.value = collaborativeJobsI.value.map(item => ({
    postName: item.coopPostName,
    postCode: item.coopPostCode,
    orgCode: item.orgCode
  }))

  defaultCheckPostList.value = checkPostList.value
  defaultCheckedKeys.value = orgCodes
}

// Watchers
watch(
  () => dialogVisible.value,
  val => {
    if (val) {
      defaultCheckedOrgAndPost()
    }
  }
)

watch(
  () => companyId.value,
  val => {
    if (val) {
      getOrgTreeFun()
    }
  },
  { immediate: true }
)

// Initial Setup
getDictFun()
getPostCoopFun()
getPostTreeFun()
</script>

<style lang="scss" scoped>
.work_activities_content {
  margin-bottom: 16px;
}

.collaboration_post_content {
  margin-bottom: 16px;

  .select_post {
    width: 80px;
    height: 80px;
    padding: 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    background: #5cb87a;
    margin-right: 5px;
  }

  .post_item_wrap {
    color: #525e6c;
    margin: 20px 0;

    .post_item_main {
      flex: 1;
      margin-left: 10px;
      display: flex;
      flex-wrap: wrap;

      .post_item {
        width: 18%;
        height: 35px;
        display: flex;
        padding: 0 10px;
        justify-content: space-between;
        line-height: 35px;
        margin: 0 5px;
        border: 1px solid #e4e7ed;

        .name {
          width: 80%;
        }

        .name_icon {
          width: 20px;
          height: 20px;
          background: #d2d2d2;
          border-radius: 50%;
          margin-top: 6px;
          text-align: center;
          line-height: 22px;
          font-size: 16px;
          color: #fff;
          cursor: pointer;

          &:hover {
            opacity: 0.6;
          }
        }
      }

      .work_add_btn {
        width: 18%;
        height: 35px;
        margin: 0 5px;

        button {
          width: 100%;
          height: 100%;
          border: 1px dashed var(--el-color-primary);
          line-height: 35px;
          padding: 0;
        }
      }
    }

    .xt_wrap {
      .icon {
        width: 85px;
        height: 85px;
        background: url('../../../../../../../public/icons/icon_xt.png') no-repeat center;
      }
    }
  }
}

.collaborative_role {
  margin: 20px 0;

  .icon {
    width: 85px;
    height: 35px;
    background: url('../../../../../../../public/icons/icon_xt2.png') no-repeat center;
  }

  .el-checkbox-group {
    flex: 1;
    margin-left: 10px;

    :deep(.el-checkbox) {
      width: 18%;
      height: 35px;
      border: 1px solid #e5e5e5;
      line-height: 35px;
      font-size: 12px;
      margin: 0 5px;

      .el-checkbox__label {
        width: 80%;
        height: 35px;
        line-height: 35px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &.is-checked {
        border-color: var(--el-color-primary);
      }

      .el-checkbox__input {
        float: right;
        width: 33px;
        height: 33px;

        &.is-checked {
          .el-checkbox__inner {
            background: none;
          }

          & + .el-checkbox__label {
            color: var(--el-color-primary);
          }
        }

        .el-checkbox__inner {
          width: 100%;
          height: 100%;
          border: none;

          &::after {
            top: 4px;
            left: 12px;
            width: 10px;
            height: 16px;
            font-size: 30px;
            border-width: 2px;
            border-color: var(--el-color-primary);
          }
        }

        .el-checkbox__original {
          width: 33px;
          height: 33px;
        }
      }
    }
  }

  .select_post {
    width: 80px;
    padding: 5px 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    background: #66b1ff;
    margin-right: 5px;
  }
}

.line_wrap {
  margin: 0 0 10px 0;
  line-height: 40px;

  span {
    padding: 0 10px 0 0;
    width: 110px;
    text-align: right;
  }

  div {
    flex: 1;
  }

  :deep(.el-textarea) {
    .el-textarea__inner {
      resize: none;
      font-family:
        PingFang SC,
        Avenir,
        Helvetica,
        Arial,
        sans-serif !important;
    }
  }

  :deep(.el-cascader) {
    width: 100%;

    .el-input--suffix {
      width: 100%;
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__body {
    padding: 0 5px;

    .page_section {
      .page_section_aside {
        width: 30%;

        .aside_tree_title {
          .tree_title {
            width: 100%;
          }
        }

        .aside_tree_list {
          height: 350px !important;
          padding: 10px 5px;
        }
      }

      .page_section_main {
        height: 380px;
        min-height: 386px;
        flex: 1;
      }
    }
  }
}
</style>
