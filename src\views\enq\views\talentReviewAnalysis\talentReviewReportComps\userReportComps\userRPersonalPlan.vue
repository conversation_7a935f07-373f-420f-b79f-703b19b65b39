<template>
    <div class="report_section personal_plan_main">
        <div class="page_second_title marginB_16 marginT_16">当前岗位</div>
        <div class="personal_plan_centent clearfix">
            <div class="item">
                <div class="title">岗位名称</div>
                <div class="text">{{perInfo.postCode}}</div>
            </div>
            <div class="item">
                <div class="title">所属部门</div>
                <div class="text">{{perInfo.orgName}}</div>
            </div>
            <div class="item">
                <div class="title">岗位族群</div>
                <div class="text">{{perInfo.parentJobClassName}}</div>
            </div>
            <div class="item">
                <div class="title">岗位序列</div>
                <div class="text">{{perInfo.jobClassName}}</div>
            </div>
            <div class="item">
                <div class="title">发展类型</div>
                <div class="text">{{perInfo.developmentType}}</div>
            </div>
            <div class="item">
                <div class="title">下一晋升岗位</div>
                <div class="text">{{perInfo.expectationPost}}</div>
            </div>
            <div class="item">
                <div class="title">预计晋升周期</div>
                <div class="text">{{perInfo.expectationCycle}}</div>
            </div>
            <div class="item plan">
                <div class="title">个人发展计划</div>
                <div class="text">{{perInfo.developmentPlan}}</div>
            </div>
        </div>
    </div>
</template>
 
<script>
    import { getPersonalPlanning } from "../../../../request/api";
    export default {
        name: "userRPersonalPlan",
        props: ["enqId", "userId"],
        components: {},
        data() {
            return {
                perInfo: {},
            };
        },
        created() {
            this.getPersonalPlanningFun();
        },
        mounted() {},
        methods: {
            getPersonalPlanningFun() {
                let params = {
                    enqId: this.enqId,
                    userId: this.userId,
                };
                getPersonalPlanning(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.perInfo = res.data;
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .personal_plan_centent {
        width: 80%;
        .item {
            text-align: left;
            margin-bottom: 25px;
            padding-top: 10px;
            float: left;
            width: 25%;
            line-height: 20px;
            &.plan{
                width: 100%;
            }
            .title {
                color: #0099ff;
                margin-bottom: 10px;
            }
            .text {
                font-size: 0.0625rem;
                color: #777;
            }
        }
        
    }
</style>