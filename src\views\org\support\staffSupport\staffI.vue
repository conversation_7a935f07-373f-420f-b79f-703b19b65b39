<script setup>
import Table from "../../components/table.vue";

const columns = ref([
  {
    label: "姓名",
    prop: "a",
  },
  {
    label: "岗位",
    prop: "b",
  },
  {
    label: "指标数量",
    prop: "c",
  },
  {
    label: "本期达成率",
    prop: "d",
  },
  {
    label: "上期达成率",
    prop: "e",
  },
  {
    label: "变化",
    prop: "f",
  },
  {
    label: "结果绩效",
    prop: "g",
    slot: "gSlot",
    align: "center",
    width: 220,
  },
]);
const data = ref([
  {
    a: "王伟",
    b: "销售",
    c: "3",
    d: "95%",
    e: "95%",
    f: "+5%",
  },
]);

const columns2 = ref([
  {
    label: "姓名",
    prop: "a",
  },
  {
    label: "岗位",
    prop: "b",
  },
  {
    label: "指标名称",
    prop: "c",
  },
  {
    label: "目标类别",
    prop: "d",
  },
  {
    label: "当期",
    prop: "e",
  },
  {
    label: "上期",
    prop: "f",
  },
  {
    label: "当前目标",
    prop: "g",
  },
  {
    label: "上期目标",
    prop: "h",
  },
  {
    label: "目标变化",
    prop: "i",
  },
  {
    label: "当前实际",
    prop: "j",
  },
  {
    label: "上期实际",
    prop: "k",
  },
  {
    label: "实际变化",
    prop: "l",
  },
  {
    label: "操作",
    prop: "m",
    slot: "mSlot",
    align: "center",
    width: 220,
  },
]);
const data2 = ref([
  {
    a: "王伟",
    b: "销售",
    c: "供应商交付准时率",
    d: "年度目标",
    e: "2025年",
    f: "2024年",
    g: "98%",
    h: "98%",
    i: "+8%",
    j: "98%",
    k: "98%",
    l: "+8%",
  },
]);

const columns3 = ref([
  {
    label: "维度",
    prop: "a",
  },
  {
    label: "数据",
    prop: "b",
  },
  {
    label: "行业参考",
    prop: "c",
  },
  {
    label: "解读结论",
    prop: "d",
    width: 400,
  },
]);
const data3 = ref([
  {
    a: "当期实际值",
    b: "82天",
    c: "头部企业（如海尔）平均65-70天",
    d: "存在显著差距：高于行业标杆15%以上，反映库存效率优化空间巨大 ",
    e: "王伟（负责人） HRBP",
    f: "≥75",
  },
]);

const columns4 = ref([
  {
    label: "优先级",
    prop: "a",
  },
  {
    label: "行动方向",
    prop: "b",
  },
  {
    label: "具体举措",
    prop: "c",
    width: 400,
  },
  {
    label: "预期效果",
    prop: "d",
  },
]);
const data4 = ref([
  {
    a: "紧急",
    b: "压缩原材料库存",
    c: "推行JIT供料模式，对压缩机等长交期物料实施「周供货+安全库存动态重置」（参考历史波动率±5%）",
    d: "缩短周转5-7天",
    e: "王伟（负责人） HRBP",
    f: "≥75",
  },
]);
</script>
<template>
  <div class="content-wrap">
    <div class="justify-between">
      <div class="page-title-line">人员指标整体达成率</div>
      <div class=""><span class="pearl_blue">已选指标：</span>库存周转天数</div>
    </div>
    <Table
      :roundBorder="false"
      :columns="columns"
      :data="data"
      headerColor
      showIndex
    >
      <template v-slot:gSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>详情</el-button>
        <el-button class="ai_btn" type="primary" plain round>对比</el-button>
      </template>
    </Table>
    <div class="page-title-line marginT20">人员指标详情</div>
    <Table
      :roundBorder="false"
      :columns="columns2"
      :data="data2"
      headerColor
      showIndex
    >
      <template v-slot:mSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>Ai解读</el-button>
        <el-button class="ai_btn" type="primary" plain round
          >同指标对比</el-button
        >
      </template>
    </Table>

    <div class="page-title-line marginT20">人员指标AI解读</div>
    <div class="marginB20">一、指标全景分析</div>
    <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor>
    </Table>

    <div class="marginT20 marginB20">二、根因诊断与关联指标推演</div>
    <div class="marginB20">1、原材料囤积问题</div>
    <div>
      <span class="text_b">线索：</span>目标降幅（-5天）＞实际降幅（-4天）
    </div>
    <div>
      <span class="text_b">推测：</span
      >关键元器件（如空调压缩机）可能因供应风险超额备货，需核查：
    </div>

    <div class="dot_content_wrap">
      <div class="item_wrap">
        <span class="icon"></span>
        <span class="title"></span>
        <span class="info">原材料周转天数（若＞60天则为异常） </span>
      </div>
      <div class="item_wrap">
        <span class="icon"></span>
        <span class="title"></span>
        <span class="info">供应商交货准时率（若＜90%则倒逼囤货）</span>
      </div>
    </div>

    <div class="marginT20 marginB20">2、成品滞销风险</div>
    <div>
      <span class="text_b">行业特性：</span
      >大家电（冰箱/空调）占库存70%以上，周转天然偏慢
    </div>
    <div>
      <span class="text_b">预警信号：</span
      >实际值（82天）接近行业警戒线（85天），需联动分析：
    </div>

    <div class="dot_content_wrap">
      <div class="item_wrap">
        <span class="icon"></span>
        <span class="title"></span>
        <span class="info">滞销SKU占比（健康值＜10%）</span>
      </div>
      <div class="item_wrap">
        <span class="icon"></span>
        <span class="title"></span>
        <span class="info">渠道库存饱和度（＞80%需启动促销）</span>
      </div>
    </div>

    <div class="marginT20 marginB20">3、计划与执行割裂</div>
    <div><span class="text_b">典型证据：</span>目标连续两年未达成</div>
    <div><span class="text_b">机制缺陷：</span></div>

    <div class="dot_content_wrap">
      <div class="item_wrap">
        <span class="icon"></span>
        <span class="title"></span>
        <span class="info"
          >需求预测偏差率（家电业容忍度±15%，若＞20%则计划失效）</span
        >
      </div>
      <div class="item_wrap">
        <span class="icon"></span>
        <span class="title"></span>
        <span class="info"
          >补货决策延迟（标杆企业≤24h，若＞48h则拖累周转）</span
        >
      </div>
    </div>

    <div class="marginT20 marginB20">三、改善路径与行动清单</div>
    <Table :roundBorder="false" :columns="columns4" :data="data4" headerColor>
    </Table>

    <div class="marginT20 marginB20">四、高管汇报关键点</div>
    <div>
      结论：刘威团队库存效率改善趋势符合预期（-4.7%），但绝对值与行业标杆差距扩大。核心矛盾在于供应链响应速度滞后于市场变化，建议聚焦原材料JIT改革及滞销品清理，同步升级计划协同机制。若2025Q3前未突破78天关口，需启动组织架构重组。
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.content-wrap {
  .pearl_blue {
    color: #94a1af;
  }
  :deep .el-table {
    .ai_btn {
      padding: 0 15px;
      height: 24px;
      font-size: 14px;
    }
  }
  .dot_content_wrap {
    // margin: 0 0 40px 0;
    .item_wrap {
      margin-bottom: 0;
      .icon {
        margin: -2px 0px 0 10px;
      }
    }
  }
}
</style>
