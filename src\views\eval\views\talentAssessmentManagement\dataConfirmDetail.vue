<template>
    <div class="assess_progress_manage_info_wrap bg_write" v-if="showPage">
        <div class="page_main_title">
            数据确认--{{ evalName }}
            <div class="goback_geader" @click="$util.goback()">
                <i class="el-icon-arrow-left"></i>返回
            </div>
        </div>
        <div class="page_section">
            <div class="department_level_wrap flex_row_start">
                <div class="filter_item">
                    <span class="select_title">被评人员：</span>
                    <el-input
                        class="filter_ipt"
                        v-model="objectName"
                        
                        placeholder="请输入人员名称"
                    ></el-input>
                </div>
                <div class="filter_item">
                    <span class="select_title">评价人：</span>
                    <el-input
                        class="filter_ipt"
                        
                        v-model="userName"
                        placeholder="请输入人员名称"
                    ></el-input>
                </div>

                <div class="filter_item">
                    <span class="select_title">分值区间：</span>
                    <el-input
                        class="filter_ipt_mini"
                        
                        type="number"
                        clearable
                        v-model="minScore"
                        placeholder=""
                    ></el-input>
                    <span class="sign">~</span>
                    <el-input
                        class="filter_ipt_mini"
                        
                        type="number"
                        clearable
                        v-model="maxScore"
                        placeholder=""
                    ></el-input>
                </div>
                <div class="filter_item">
                    <span class="select_title">差异区间：</span>
                    <el-select
                        class="filter_item marginR_16"
                        v-model="difference"
                        clearable
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in differenceOption"
                            :key="item.dictCode"
                            :label="item.codeName"
                            :value="item.dictCode"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div class="filter_item">
                    <span class="select_title">评价时间：</span>
                    <el-select
                        class="filter_item marginR_16"
                        v-model="elapsedTime"
                        clearable
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in timeOption"
                            :key="item.dictCode"
                            :label="item.codeName"
                            :value="item.dictCode"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div class="filter_item">
                    <el-button
                        @click="searchFun"
                        type="primary"
                        
                        >查询</el-button
                    >
                </div>
            </div>
            <tableComponent
                v-if="tableData.data.length > 0"
                :tableData="tableData"
                :needIndex="true"
                @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange"
            >
                <template v-slot:oper>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <div class="btn_group" v-if="scope.row.rstatus== 'Y'">
                                <!-- <el-button
                                    type="primary"
                                    class=""
                                    size="mini"
                                    @click="backEval(scope.row)"
                                    >退回</el-button> -->
                                <el-button
                                    type="primary"
                                    class=""
                                    size="mini"
                                    @click="abolishEval(scope.row)"
                                    >作废</el-button
                                >
                            </div>
                        </template>
                    </el-table-column>
                </template>
            </tableComponent>
        </div>
    </div>
</template>

<script>
    import tabsChangeData from "@/components/talent/tabsComps/tabsChangeData";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import {
        dataValidation,
        userReturn,
        toVoid,
        getEvalInfo,
    } from "../../request/api";

    export default {
        name: "dataConfirmDetail",
        components: {
            tabsChangeData,
            tableComponent,
        },
        data() {
            return {
                showPage: true,
                evalId: this.$route.query.evalId,
                evalName: "",
                orgName: this.orgName, //被评部门
                objectName: this.objectName, //被评人员
                userName: this.userName, //评价人
                postName: this.postName, //被评岗位
                maxScore: "",
                minScore: "",
                differenceOption: [],
                difference: null,
                timeOption: [], //评价时间
                elapsedTime: null,
                current: this.pageCurrent,
                size: this.pageSize,
                orgCode: null,
                userName: "",
                status: "",
                statusOption: [],
                pageCurrent: 1,
                pageSize: 10,
                tableData: {
                    columns: [
                        {
                            label: "被评部门",
                            prop: "orgName",
                        },
                        {
                            label: "被评岗位",
                            prop: "postName",
                            width: 100,
                            className: "align_center",
                        },
                        {
                            label: "被评人员",
                            prop: "objectName",
                            // width: 120,
                            className: "align_center",
                        },
                        {
                            label: "评价关系",
                            prop: "relationType",
                            className: "align_center",
                        },
                        {
                            label: "评价人",
                            prop: "userName",
                            className: "align_center",
                        },
                        {
                            label: "评价分值",
                            prop: "score",
                            className: "align_center",
                        },
                        {
                            label: "综合差异",
                            prop: "difference",
                            className: "align_center",
                        },
                        {
                            label: "总评价时间",
                            prop: "elapsedTime",
                            // width: 120,
                            className: "align_center",
                        },
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        created() {
            this.$getDocList(["DIFFERENCE", "ELAPSED_TIME"]).then((res) => {
                console.log(res);
                this.differenceOption = res.DIFFERENCE;
                this.timeOption = res.ELAPSED_TIME;
            });

            //获取测评状态
            getEvalInfo({
                evalId: this.evalId,
            }).then((res) => {
                console.log(res);
                this.evalStatus = res.evalStatus;
                this.evalName = res.evalName;
            });
            this.getUserScheduleFun();
        },
        methods: {
            //切换页容量
            handleSizeChange(val) {
                this.pageSize = val;
                this.getUserScheduleFun();
            },
            //分页
            handleCurrentChange(val) {
                this.pageCurrent = val;
                this.getUserScheduleFun();
            },
            searchFun(){
                this.pageCurrent = 1;
                this.getUserScheduleFun();
            },
            getUserScheduleFun() {
                dataValidation({
                    evalId: this.evalId,
                    orgName: this.orgName,
                    objectName: this.objectName,
                    userName: this.userName,
                    postName: this.postName,
                    elapsedTime: this.elapsedTime,
                    current: this.pageCurrent,
                    size: this.pageSize,
                    maxScore: this.maxScore,
                    minScore: this.minScore,
                    difference: this.difference,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        // this.tableData = res.data;
                        if (res.data != null) {
                            this.$set(this.tableData, "data", res.data);
                            this.$set(this.tableData, "page", res.page);
                        } else {
                            this.$set(this.tableData, "data", []);
                            this.$set(this.tableData, "page", {
                                total: 0,
                                current: 1,
                                size: 10,
                            });
                        }
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            backEval(row) {
                this.$confirm("确认退回吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.$confirm("是否清除答题？", "提示", {
                            confirmButtonText: "是",
                            cancelButtonText: "否",
                            type: "warning",
                        })
                            .then(() => {
                                userReturn({
                                    evalId: this.evalId,
                                    userId: row.userId,
                                    isClear: "true",
                                }).then((res) => {
                                    console.log(res);
                                    if (res.code == 200) {
                                        this.getUserScheduleFun(this.tabIndex);
                                    }
                                });
                            })
                            .catch(() => {
                                userReturn({
                                    evalId: this.evalId,
                                    userId: row.userId,
                                    isClear: "false",
                                }).then((res) => {
                                    console.log(res);
                                    if (res.code == 200) {
                                        this.getUserScheduleFun(this.tabIndex);
                                    }
                                });
                            });
                    })
                    .catch(() => {});
            },
            abolishEval(row) {
                this.$confirm("确认作废吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        toVoid({
                            evalId: this.evalId,
                            userId: row.userId,
                            objectId:row.objectId
                        }).then((res) => {
                            console.log(res);
                            if (res.code == 200) {
                                this.$msg.success(res.msg)
                                this.getUserScheduleFun(this.tabIndex);
                            }else{
                                this.$msg.success(res.msg)
                            }
                        });
                    })
                    .catch(() => {});
            },
        },
    };
</script>

<style scoped lang="scss">
    .assess_progress_manage_info_wrap {
        .department_level_wrap {
            margin: 10px 0 20px 0;

            .select_title {
                display: inline-block;
                // width: 80px;
                // text-align: center;
            }
            .filter_item {
                margin-right: 8px;
                // width: 300px;
                // flex: 1;
                .sign {
                    margin: 0 5px;
                }
                .filter_ipt {
                    display: inline-block;
                    width: 110px;
                }
                .filter_ipt_mini {
                    width: 50px;
                }
            }

             .el-input__inner {
                // height: 35px;
                // line-height: 35px;
                padding: 0 5px;
            }

            //  .el-input__suffix {
            //     display: flex;
            //     align-items: center;
            // }
        }

         .el-table {
            margin: 10px 0 0 0;

            .has-gutter {
                tr th {
                    background: #f2f8ff;
                }
            }

            .align_center {
                text-align: center;
                /*font-weight: bold;*/

                &.completed {
                    color: #00b050;
                }

                &.incomplete {
                    color: #ffc000;
                }

                &.not_login {
                    color: #00b0f0;
                }

                &.status {
                }
            }
        }

         .el-table__body .last_date {
            font-size: 12px;
        }

        .completion_rate {
            .bar_wrap {
                width: calc(100% - 40px);
                height: 18px;
                background: #EBF4FF;
                position: relative;
                padding-top: 5px;

                .bar_progress {
                    background: #00b050;
                    height: 8px;
                    width: 50%;

                    &.bg_high {
                        background: #00b050;
                    }

                    &.bg_middle {
                        background: #00b0f0;
                    }

                    &.bg_normal {
                        background: #ffc000;
                    }

                    &.bg_low {
                        background: #ff8181;
                    }
                }
            }

            .completion_rate_num {
                font-weight: bold;

                &.not_login {
                    color: #ff6d6d;
                }

                &.color_high {
                    color: #00b050;
                }

                &.color_middle {
                    color: #00b0f0;
                }

                &.color_normal {
                    color: #ffc000;
                }

                &.color_low {
                    color: #ff8181;
                }
            }
        }
    }
    // 去除input number类型 加减箭头
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
    input {
        -moz-appearance: textfield;
    }
</style>