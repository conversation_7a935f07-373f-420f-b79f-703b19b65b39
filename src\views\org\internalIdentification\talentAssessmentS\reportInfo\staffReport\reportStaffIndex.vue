<script setup>
const emits = defineEmits(["sign"]);
import ReportInfoZTNL from "./reportInfoZTNL.vue";
import ReportInfoNLYS from "./reportInfoNLYS.vue";
import ReportInfoGRNL from "./reportInfoGRNL.vue";
import ReportInfoJCPH from "./reportInfoJCPH.vue";

import ReportInfoFXPH from "./reportInfoFXPH.vue";
import ReportInfoTDPH from "./reportInfoTDPH.vue";
import ReportInfoCBPH from "./reportInfoCBPH.vue";

import ReportInfoDBTS from "./reportInfoDBTS.vue";
import ReportInfoYSFZ from "./reportInfoYSFZ.vue";
import ReportInfoTJXX from "./reportInfoTJXX.vue";
import ReportInfoRGPP from "./reportInfoRGPP.vue";
import ReportInfoNLTS from "./reportInfoNLTS.vue";

const route = useRoute();
const router = useRouter();
const comList = ref([
  {
    sign: 1,
    com: ReportInfoZTNL,
  },
  {
    sign: 2,
    com: ReportInfoNLYS,
  },
  {
    sign: 3,
    com: ReportInfoGRNL,
  },
  {
    sign: 11,
    com: ReportInfoJCPH,
  },
  {
    sign: 12,
    com: ReportInfoFXPH,
  },
  {
    sign: 13,
    com: ReportInfoTDPH,
  },
  {
    sign: 14,
    com: ReportInfoCBPH,
  },
  {
    sign: 21,
    com: ReportInfoDBTS,
  },
  {
    sign: 22,
    com: ReportInfoYSFZ,
  },
  {
    sign: 23,
    com: ReportInfoTJXX,
  },
  {
    sign: 31,
    com: ReportInfoRGPP,
  },
  {
    sign: 32,
    com: ReportInfoNLTS,
  },
]);
const curCom = ref({
  sign: "",
  com: "",
});

const comInit = () => {
  curCom.value = comList.value[0];
};
// const curSign = (p) => {
//   comInit(p);
// };
const closeInfo = (i) => {
  emits("sign", "list");
};

const leftMenu2CheckSign = ref(1);
const orgLeftMenu = ref([
  {
    name: "个人能力画像",
    code: "A",
    children: [
      {
        name: "整体能力表现",
        code: 1,
      },
      {
        name: "能力优势与短板",
        code: 2,
      },
      {
        name: "个人能力图谱",
        code: 3,
      },
    ],
  },
  {
    name: "管理风格特征分析",
    code: "B",
    children: [
      {
        name: "决策偏好分析",
        code: 11,
      },
      {
        name: "风险偏好分析",
        code: 12,
      },
      {
        name: "团队协作偏好",
        code: 13,
      },
      {
        name: "成本收益偏好",
        code: 14,
      },
    ],
  },
  {
    name: "能力提升地图",
    code: "C",
    children: [
      {
        name: "短板提升项",
        code: 21,
      },
      {
        name: "优势发展项",
        code: 22,
      },
      {
        name: "推荐学习资源",
        code: 23,
      },
    ],
  },
  //
  {
    name: "人才发展建议",
    code: "d",
    children: [
      {
        name: "人岗匹配建议",
        code: 31,
      },
      {
        name: "能力提升计划",
        code: 32,
      },
    ],
  },
]);

const leftMenu2Check = (c) => {
  comList.value.forEach((e, index) => {
    if (c == e.sign) {
      curCom.value = comList.value[index];
    }
  });
  leftMenu2CheckSign.value = c;
};
onMounted(() => {
  comInit();
});
</script>
<template>
  <div class="index_wrap reportInfo_wrap">
    <div class="t_btn" @click="closeInfo"><span class="icon"></span>返回</div>
    <div class="title_main">20240201 战略管理人才测评报告（采购部）</div>
    <div class="reportInfo_main justify-between">
      <div class="left_menu_wrap">
        <div class="title">测评报告内容</div>
        <div class="item_wrap" v-for="it in orgLeftMenu">
          <div class="name">{{ it.name }}</div>
          <div
            class="item_c_wrap"
            :class="{ item_c_act: leftMenu2CheckSign == item.code }"
            v-for="item in it.children"
            @click="leftMenu2Check(item.code)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="right_wrap">
        <component :is="curCom.com" />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.reportInfo_wrap {
  .t_btn {
    margin: 0 0 20px 0;
    width: 74px;
    height: 28px;
    line-height: 28px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #40a0ff;
    text-align: center;
    color: #40a0ff;
    cursor: pointer;
    .icon {
      display: inline-block;
      margin: 0px 6px -1px 0;
      width: 16px;
      height: 16px;
      background: url("@/assets/imgs/org/icon_06.png") no-repeat center;
      background-size: 100% 100%;
    }
  }
  .title_main {
    margin-bottom: 20px;
    padding: 0 22px;
    height: 82px;
    line-height: 82px;
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
  }
  .reportInfo_main {
    padding: 20px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
    border-radius: 8px 8px 8px 8px;
  }
  .left_menu_wrap {
    margin-right: 20px;
    padding: 12px 10px;
    width: 198px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #a5c1dc;
    .item_wrap {
      text-align: center;
      .name {
        height: 35px;
        line-height: 35px;
        color: #333333;
      }
      .item_c_wrap {
        margin: 0 auto;
        width: 178px;
        height: 35px;
        line-height: 35px;
        font-size: 14px;
        border-radius: 5px 5px 5px 5px;
        cursor: pointer;
        &.item_c_act {
          border: 1px solid #53a9f9;
          color: #53a9f9;
        }
      }
    }
  }
  .right_wrap {
    flex: 1;
  }
}
</style>
