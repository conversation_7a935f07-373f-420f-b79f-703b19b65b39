<script setup>
import Table from "@/components/table/simplenessTable.vue";
// 11
const columns = ref([
  {
    label: "场景类型",
    prop: "a",
  },
  {
    label: "个人得分<40",
    prop: "b",
  },
  {
    label: "个人得分40~60分",
    prop: "c",
  },
  {
    label: "个人得分40~80分",
    prop: "d",
  },

  {
    label: "个人得分80~90分",
    prop: "e",
  },
  {
    label: "个人得分90~100分",
    prop: "f",
  },
]);
const data = ref([
  {
    a: "战略场景",
    b: "15人",
    c: "15人",
    d: "15人",
    e: "15人",
  },
  {
    a: "组织场景",
    b: "15人",
    c: "15人",
    d: "15人",
    e: "15人",
  },
]);

const columns2 = ref([
  {
    label: "姓名",
    prop: "a",
  },
  {
    label: "岗位",
    prop: "b",
  },
  {
    label: "数字化场景得分",
    prop: "c",
  },
]);
const data2 = ref([
  {
    a: "王伟",
    b: "采购经理",
    c: "36",
  },
]);

const columns3 = ref([
  {
    label: "姓名",
    prop: "a",
    width: 80,
  },

  {
    label: "岗位",
    prop: "b",
  },
  {
    label: "二级能力",
    prop: "c",
  },

  {
    label: "二级能力数字化场景得分",
    prop: "d",
    width: 160,
  },
]);
const data3 = ref([
  {
    a: "王伟",
    b: "采购经理",
    c: "市场分析与战略规划",
    e: "92",
  },
]);

const columns4 = ref([
  {
    label: "二级能力",
    prop: "a",
    width: 260,
  },
  {
    label: "数字化场景得分",
    prop: "b",
    width: 260,
  },
  {
    label: "管理建议",
    prop: "c",
  },
]);
const data4 = ref([
  {
    a: "市场分析与战略规划",
    b: "15",
    c: "",
  },
]);

onMounted(() => {});
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">业务场景匹配</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        基于能力测评数据，精准识别团队成员能力与目标岗位胜任力要求的匹配程度，通过岗位调整实现
        “人岗适配”，最大化释放人才效能。
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">业务场景匹配</div>
      <Table
        :roundBorder="false"
        :columns="columns"
        :data="data"
        headerColor
        showIndex
      >
      </Table>
    </div>
    <div class="info_section_wrap half_section_wrap justify-between">
      <div class="l_wrap">
        <div class="page-title-line">
          业务场景匹配（数字化场景-个人得分90~100）
        </div>
        <Table
          :roundBorder="false"
          :columns="columns2"
          :data="data2"
          headerColor
          showIndex
        >
        </Table>
      </div>
      <div class="r_wrap">
        <div class="page-title-line">业务场景二级展开（王伟-数字化场景）</div>
        <Table
          :roundBorder="false"
          :columns="columns3"
          :data="data3"
          headerColor
        >
        </Table>
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">
        业务场景匹配详情（数字化场景-个人得分90~100-王伟-采购经理）
      </div>
      <Table
        :roundBorder="false"
        :columns="columns4"
        :data="data4"
        headerColor
        showIndex
      >
      </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
