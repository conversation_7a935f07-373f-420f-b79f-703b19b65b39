<template>
  <div class="report_page" id="page">
    <!-- <div class="report_section">
            <div class="page_second_title">敬业度驱动因素“影响力分析”</div>
            <div class="page_third_title">按部门分析</div>
            <div class="report_section_content clearfix">
                <div class="explain_text">
                    <p class="marginB_16">
                        敬业度影响力模型，是通过各个因素对敬业度的相关分析得出。通过分析，可以帮助我们明确是哪些因素驱动员工敬业度，并将这些因素的优先关注次序排列出来。通过这些信息，可以明确对员工和雇主而言，哪些因素是重要的领域、哪些因素应值得关注，从而聚焦在提升员工敬业度的关键领域。
                        在模型中，有两个领域的定义
                    </p>
                    <p>
                        <span>机会领域：</span>
                        指对公司敬业度正向影响力较大的驱动因素，一般该因素得分相对较低，是公司做得不够好的地方。如果公司针对该驱动因素有所改善，其会使总体满意度提升较多。
                    </p>
                    <p>
                        <span>威胁领域：</span>
                        指对公司敬业度负向影响力较大的驱动因素，一般该因素得分相对较高，是公司做得比较好的地方，是需要保持的。如果公司该驱动因素得分下降，其会使总体满意度大幅下降。
                    </p>
                </div>
            </div>
        </div> -->
    <!-- <div class="report_section">
            <div class="page_second_title">敬业度驱动因素</div>
            <div class="report_section_content clearfix">
                <div class="chart_box">
                    <div id></div>
                </div>
                <div class="explain_text">
                    <div class="explain_title">形态一：微笑曲线，在某司龄段出现敬业度拐点。</div>
                    <ul>
                        <li class="line">1-2 年员工敬业度最低：</li>
                        <li class="dot">该由于HR招聘时给予应聘者较高的承诺，员工入职1年后，发现现实工作和理想存在偏差，这导致员工敬业度水平下滑。</li>
                        <li>3-5 年员工敬业度最低：</li>
                    </ul>
                </div>
            </div>
        </div> -->
    <div class="report_section">
      <div class="page_second_title">各岗位族群敬业度与满意度</div>
      <div class="report_section_content clearfix">
        <el-row :gutter="16">
          <el-col :span="10">
            <div
              class="chart_box engagement_class_chart"
              id="engagementByClassChart"
            ></div>
          </el-col>
          <el-col :span="14">
            <tableComp
              :tableData="tableDataPost"
              :needPagination="false"
              :border="true"
              :overflowTooltip="false"
            ></tableComp>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="report_section">
      <div class="report_section_content clearfix">
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="page_second_title">敬业度满意度人数区间分布</div>
            <div class="jyd_matrix_data_wrap">
              <talentMatrix
                :matrixData="jydMatrixData"
                :matrixHeight="300"
                :headTitle="'敬业度'"
                :asideTitle="'满意度'"
              ></talentMatrix>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="page_second_title">敬业度满意度 &lt;80 的人才分类</div>
            <div class="chart_box" id="eightyChart"></div>
          </el-col>
          <el-col :span="8">
            <div class="page_second_title">敬业度满意度 &lt;60 的人才分类</div>
            <div class="chart_box" id="sixtyChart"></div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">各人才分类下敬业驱动因素</div>
      <div class="report_section_content clearfix">
        <tableComp
          :tableData="professionalDrivers"
          :spanMethod="objectSpanMethod"
          :needPagination="false"
          :border="true"
          :overflowTooltip="false"
        ></tableComp>
      </div>
    </div>

    <div class="report_section">
      <div class="page_second_title">各岗位族群下敬业驱动因素</div>
      <div class="report_section_content clearfix">
        <tableComp
          :tableData="jobClassDrivers"
          :spanMethod="objectSpanMethod"
          :needPagination="false"
          :border="true"
          :overflowTooltip="false"
        ></tableComp>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">下级组织的敬业驱动因素</div>
      <div class="report_section_content clearfix">
        <tableComp
          :tableData="orgDrivers"
          :spanMethod="objectSpanMethod"
          :needPagination="false"
          :border="true"
          :overflowTooltip="false"
        ></tableComp>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">各职层的敬业驱动因素</div>
      <div class="report_section_content clearfix">
        <tableComp
          :tableData="jobLevelDrivers"
          :spanMethod="objectSpanMethod"
          :needPagination="false"
          :border="true"
          :overflowTooltip="false"
        ></tableComp>
      </div>
    </div>

    <div class="report_section" style="margin-bottom: 100px">
      <div class="page_second_title">敬业度提升改善建议</div>
      <div class="report_section_content clearfix">
        <tableComp
          :tableData="tableData"
          :spanMethod="objectSpanMethod"
          :needPagination="false"
          :border="true"
          :overflowTooltip="false"
        ></tableComp>
      </div>
    </div>
  </div>
</template>
 
<script>
import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js";
import {
  jfAnalysisPage,
  engagementAnalysis,
  getOrgEngagement,
} from "../../../../../request/api.js";
import tableComp from "@/components/talent/tableComps/tableComponent";
import talentMatrix from "@/components/talent/common/talentMatrix";
export default {
  name: "",
  props: ["enqId", "orgCode", "isPdf", "userId"],
  components: { tableComp, talentMatrix },
  data() {
    return {
      pageData: "",
      tableDataPost: {
        columns: [
          {
            label: "岗位族群",
            prop: "parentJobClassName",
          },
          {
            label: "岗位序列",
            prop: "jobClassName",
          },
          {
            label: "敬业度",
            prop: "jyd",
          },
          {
            label: "满意度",
            prop: "myd",
          },
        ],
        data: [],
      },
      tableData: {
        columns: [
          {
            label: "机会领域",
            prop: "moduleName",
          },
          // {
          //     label: "维度指向",
          //     prop: "moduleName",
          //     width:150
          // },
          {
            label: "典型表现",
            prop: "typicalIssue",
          },
          {
            label: "具体评估项",
            prop: "itemName",
          },
          {
            label: "得分",
            prop: "actualScore",
            width: 100,
          },
          {
            label: "改善方向",
            prop: "improvementSuggestion",
          },
        ],
        data: [],
      },
      engagementByClassChart: {
        chartData: [],
        legend: [],
      },
      eightyChart: {
        data: [],
      },
      sixtyChart: {
        data: [],
      },
      jydMatrixData: [],
      professionalDrivers: {
        columns: [],
        data: [],
      },
      jobClassDrivers: {
        columns: [],
        data: [],
      },
      orgDrivers: {
        columns: [],
        data: [],
      },
      jobLevelDrivers: {
        columns: [],
        data: [],
      },
    };
  },
  created() {
    // this.jfAnalysisPageFun();
    // this.engagementAnalysisFn();
    this.getOrgEngagementFun();
  },
  mounted() {},
  methods: {
    getOrgEngagementFun() {
      getOrgEngagement({
        enqId: this.enqId,
        orgCode: this.orgCode,
        userId: this.userId,
      }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.pageData = res.data;
          // console.log(this.pageData)
          this.initData();
        }
      });
    },
    initData() {
      if (this.pageData) {
        // 各岗位族群敬业度与满意度
        this.engagementByClassChart.data =
          this.pageData.engagementByClassChart.chartData;
        this.engagementByClassChart.legend =
          this.pageData.engagementByClassChart.legend;
        echartsRenderPage(
          "engagementByClassChart",
          "Scatter",
          450,
          450,
          this.engagementByClassChart
        );
        this.tableDataPost.data = this.pageData.engagementByClass;
        // 敬业度满意度人数区间分布
        this.jydMatrixData = this.pageData.matrix;
        this.eightyChart.data = this.pageData.eighty.map((item) => {
          return {
            name: item.code_name,
            value: item.value,
          };
        });
        echartsRenderPage("eightyChart", "YBar", null, "198", this.eightyChart);
        this.sixtyChart.data = this.pageData.sixty.map((item) => {
          return {
            name: item.code_name,
            value: item.value,
          };
        });
        echartsRenderPage("sixtyChart", "YBar", null, "198", this.sixtyChart);
        // 各人才分类下敬业驱动因素
        let columns = [
          {
            label: "人才类别",
            prop: "name",
          },
        ];
        this.pageData.talentDrivers.header.map((item) => {
          columns.push({
            label: item.moduleName,
            prop: item.moduleCode.replace(/\./g, "_"),
          });
        });
        this.professionalDrivers.columns = columns;
        let professionalDriversData = {};
        this.pageData.talentDrivers.data.map((item) => {
          for (const key in item) {
            let itemName = key.replace(/\./g, "_");
            professionalDriversData[itemName] = item[key];
          }
          this.professionalDrivers.data.push(professionalDriversData);
        });
        // 各岗位族群下敬业驱动因素
        let jobClassDriversColums = [
          {
            label: " ",
            prop: "name",
          },
        ];
        this.pageData.jobClassDrivers.header.map((item) => {
          jobClassDriversColums.push({
            label: item.moduleName,
            prop: item.moduleCode.replace(/\./g, "_"),
          });
        });
        this.jobClassDrivers.columns = jobClassDriversColums;
        let jobClassDriversData = {};
        this.pageData.jobClassDrivers.data.map((item) => {
          for (const key in item) {
            let itemName = key.replace(/\./g, "_");
            jobClassDriversData[itemName] = item[key];
          }
          this.jobClassDrivers.data.push(jobClassDriversData);
        });
        // 下级组织的敬业驱动因素
        let orgDriversColumns = [
          {
            label: " ",
            prop: "name",
          },
        ];
        this.pageData.orgDrivers.header.map((item) => {
          orgDriversColumns.push({
            label: item.moduleName,
            prop: item.moduleCode.replace(/\./g, "_"),
          });
        });
        this.orgDrivers.columns = orgDriversColumns;
        let orgDriversData = {};
        this.pageData.orgDrivers.data.map((item) => {
          for (const key in item) {
            let itemName = key.replace(/\./g, "_");
            orgDriversData[itemName] = item[key];
          }
          this.orgDrivers.data.push(orgDriversData);
        });
        // 各职层的敬业驱动因素
        let jobLevelDriversColumns = [
          {
            label: " ",
            prop: "name",
          },
        ];
        this.pageData.jobLevelDrivers.header.map((item) => {
          jobLevelDriversColumns.push({
            label: item.moduleName,
            prop: item.moduleCode.replace(/\./g, "_"),
          });
        });
        this.jobLevelDrivers.columns = jobLevelDriversColumns;
        let jobLevelDriversData = {};
        this.pageData.jobLevelDrivers.data.map((item) => {
          for (const key in item) {
            let itemName = key.replace(/\./g, "_");
            jobLevelDriversData[itemName] = item[key];
          }
          this.jobLevelDrivers.data.push(jobLevelDriversData);
        });
        // 敬业度提升改善建议
        this.tableData.data = this.pageData.orgSuggestionsImprovement;
      }
    },
    // ----before-----
    initChart() {
      echartsRenderPage(
        "engagementByClassChart",
        "Scatter",
        null,
        280,
        this.engagementByClassChart
      );
      echartsRenderPage("eightyChart", "YBar", null, "198", this.eightyChart);
      echartsRenderPage("sixtyChart", "YBar", null, "198", this.sixtyChart);
      // echartsRenderPage(
      //     "jyDriversChart",
      //     "XBar",
      //     null,
      //     null,
      //     this.jyDriversChart
      // );
      // echartsRenderPage(
      //     "engagementData",
      //     "XBar",
      //     null,
      //     null,
      //     this.engagementData
      // );
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // if (columnIndex == 0) {
      //     if (rowIndex % row.size == 0 && row.size) {
      //         return {
      //             rowspan: row.size,
      //             colspan: 1,
      //         };
      //     } else {
      //         return {
      //             rowspan: 0,
      //             colspan: 0,
      //         };
      //     }
      // }
    },
    engagementAnalysisFn() {
      let params = {
        enqId: this.enqId,
        orgCode: this.orgCode,
      };
      engagementAnalysis(params).then((res) => {
        if (res.code == 200) {
          let tableDataPostdata = res.data.engagementByClass;
          let engagementByClassChart = res.data.engagementByClassChart;
          this.$set(this.tableDataPost, "data", tableDataPostdata);
          this.$set(
            this.engagementByClassChart,
            "data",
            engagementByClassChart.chartData
          );
          this.$set(
            this.engagementByClassChart,
            "legend",
            engagementByClassChart.legend
          );
          this.jydMatrixData = res.data.matrix;
          let columns = [
            {
              label: "人才类别",
              prop: "name",
            },
          ];
          res.data.professionalDrivers.header.map((item) => {
            let key = item.moduleCode.replace(/\./g, "_");
            columns.push({
              label: item.moduleName,
              prop: key,
            });
          });
          res.data.professionalDrivers.data.map((item) => {
            for (const key in item) {
              if (Object.hasOwnProperty.call(item, key)) {
                const val = item[key];
                let code = key.replace(/\./g, "_");
                item[code] = val;
                delete item.key;
              }
            }
          });
          console.log(columns);
          this.$set(this.professionalDrivers, "columns", columns);
          this.$set(
            this.professionalDrivers,
            "data",
            res.data.professionalDrivers.data
          );
          res.data.eighty.map((item) => {
            item["name"] = item["code_name"];
          });
          this.$set(this.eightyChart, "data", res.data.eighty);
          res.data.sixty.map((item) => {
            item["name"] = item["code_name"];
          });
          this.$set(this.sixtyChart, "data", res.data.sixty);

          this.initChart();
        }
      });
    },
    jfAnalysisPageFun() {
      let params = {
        enqId: this.enqId,
        orgCode: this.orgCode,
        number: "11",
      };
      jfAnalysisPage(params).then((res) => {
        if (res.code == "200") {
          let data = res.data.jfAnalysisPage11;
          this.$set(this.tableData, "data", data.actionPlanProposal);
        }
      });
    },
  },
  watch: {
    // pageData(val) {
    //   this.initData();
    // },
  },
};
</script>
 
<style scoped lang="scss">
.engagement_class_chart {
  padding: 0 0 0 10px;
}
.dedicated_main {
  height: 420px;
  overflow-y: auto;
}
.report_section {
  margin-bottom: 32px;
}
.chart_box {
  // float: left;
  // width: 230px;
  // height: 140px;
  // /*background: darkkhaki;*/
  width: 100%;
  height: 100%;
}
.explain_text {
  overflow: hidden;
  color: #212121;
  line-height: 24px;
}
</style>