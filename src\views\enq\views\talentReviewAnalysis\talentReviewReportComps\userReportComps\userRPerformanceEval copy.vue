<template>
    <div class="report_section userR_quality_eval_wrap">
        <div class="page_second_title">业绩评价表现</div>
        <div class="flex_row_around marginB_32">
            <li class="annulus_item">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveAchievementScore.qualityOverallScore"
                />
                <p>综合得分</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveAchievementScore.S != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveAchievementScore.S"
                />
                <p>自评</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveAchievementScore.P != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveAchievementScore.P"
                />
                <p>同级</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveAchievementScore.U != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveAchievementScore.U"
                />
                <p>上级</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveAchievementScore.B != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveAchievementScore.B"
                />
                <p>下级</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveAchievementScore.O != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveAchievementScore.O"
                />
                <p>协同</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveAchievementScore.L != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveAchievementScore.L"
                />
                <p>分管领导</p>
            </li>
            <li class="last_annulus_item">
                <span>
                    {{ comprehensiveAchievementScore.actualQualityGrade }}
                </span>
            </li>
        </div>
        <div class="flex_row_start marginB_32">
            <div class="ranking_wrap">
                <div class="page_third_title">
                    <span>业绩评价排名</span>
                </div>
                <ul class="ranking_main">
                    <li class="item_wrap">
                        <p class="title">整体排名</p>
                        <p class="number">
                            <span class="weight">{{
                                comprehensiveAchievementRanking.wholeRanking
                            }}</span
                            >/{{ comprehensiveAchievementRanking.whole }}
                        </p>
                    </li>
                    <li class="item_wrap">
                        <p class="title">本部门排名</p>
                        <p class="number">
                            <span class="weight">{{
                                comprehensiveAchievementRanking.orgRanking
                            }}</span
                            >/{{ comprehensiveAchievementRanking.org }}
                        </p>
                    </li>
                    <li class="item_wrap">
                        <p class="title">本职位排名</p>
                        <p class="number">
                            <span class="weight">{{
                                comprehensiveAchievementRanking.jobRanking
                            }}</span
                            >/{{ comprehensiveAchievementRanking.job }}
                        </p>
                    </li>
                </ul>
            </div>
            <div class="job_score">
                <div class="page_third_title">
                    <span>同职位360°评分对比</span>
                </div>
                <div class="chart_box factor" :id="jobScoreComparisonId"></div>
            </div>
        </div>
        <div class="marginB_32">
            <div class="page_third_title">
                <span>综合素质得分对比</span>
            </div>
            <div class="report_section_content">
                <div
                    class="chart_box factor"
                    :id="qualityScoreComparisonId"
                ></div>
            </div>
        </div>
        <div class="bottom_wrap">
            <div class="page_third_title">
                <span>综合素质具体表现</span>
            </div>
            <div>
                <tableComponent
                    :tableData="tableData"
                    :border="true"
                    :needPagination="false"
                    :needIndex="false"
                ></tableComponent>
            </div>
        </div>
    </div>
</template>

<script>
    // 素质评价
    import customProcess from "@/components/talent/common/customProcess.vue";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import { getUserAchievementEval } from "../../../../request/api.js";
    export default {
        name: "userRPerformanceEval",
        props: ["nextBtnText", "enqId", "userId", "postCode", "orgCode"],
        components: {
            tableComponent,
            customProcess,
        },
        data() {
            return {
               comprehensiveAchievementScore:[],
				comprehensiveAchievementRanking:'',
				achievementScoreComparison:{
					data:[],
					legend:[],
				},
				tableData:{
					columns: [
                        {
                            label: "评价维度",
                            prop: "name",
                            width: "200",
                        },
						{
							label: "评估内容",
                            prop: "content",
                            width: "200",
						},
						{
							label: "表现",
                            prop: "performance",
                            width: "",
						},
					],
					data: [],
				},
				achievementComments:''
            };
        },
        created() {},
        computed: {},
        mounted() {
            this.getUserAchievementEvalFun();
        },
        methods: {
            setItemText(value) {
                return () => {
                    return value;
                };
            },
            getUserAchievementEvalFun(){
				getUserAchievementEval({
					enqId:this.enqId,
					userId:this.userId
				}).then(res=>{
					if(res.code == 200){
						this.comprehensiveAchievementScore = res.data.comprehensiveAchievementScore
						this.comprehensiveAchievementRanking = res.data.comprehensiveAchievementRanking
						this.achievementScoreComparison.data = res.data.achievementScoreComparison.chartData
						this.achievementScoreComparison.legend = res.data.achievementScoreComparison.legend
						echartsRenderPage(
							'scoreChart',      //id
							"XBar",				   // 图表类型
							'1100',                  //宽
							'300',                  //高
							this.achievementScoreComparison    //图表数据
						)
						this.tableData.data = res.data.achievementSpecificPerformance
						this.achievementComments = res.data.achievementComments
					}
				})
			}
        },
    };
</script>

<style scoped lang="scss">
    .score_classfiy {
        margin-bottom: 32px;
    }
    .annulus_item {
        text-align: center;
        p {
            color: #0099ff;
            font-weight: 600;
        }
    }
    .last_annulus_item {
		display: flex;
		align-items: center;
        height: 100px;
        width: 100px;
        text-align: center;
        color: #008fff;
        background: #dae8fd;
        border-radius: 50%;
        padding: 0 10px;
    }
    .ranking_wrap {
        width: 400px;
        .ranking_main {
            padding-left: 16px;
            .item_wrap {
                padding: 0 20px 0 10px;
                line-height: 32px;
                background: #dae8fd;
                text-align: center;
                color: #008fff;
                margin-bottom: 10px;
                display: flex;
                justify-content: space-between;
                .title {
                    font-weight: 600;
                }
                .number {
                    font-size: 12px;
                    .weight {
                        font-size: 14px;
                        font-weight: 600;
                    }
                }
            }
        }
    }
    .job_score {
        width: 1100px;
        margin-left: 50px;
    }
    .superior_comments {
        line-height: 25px;
        padding-left: 16px;
    }
    .remark_info {
        ul {
            li {
                line-height: 25px;
                margin: 0 0 15px 0;
                .name {
                    width: 80px;
                    height: 25px;
                    text-align: center;
                    border: 1px solid #0099ff;
                    color: #0099ff;
                    font-weight: 600;
                    margin: 0 15px 0 0;
                }
                .info {
                    flex: 1;
                    max-width: 1040px;
                }
            }
        }
    }
</style>
<!-- 
<template>
	<div class="report_section userR_performance_eval_wrap">
		<div class="userR_performance_eval_main">
			<div class="top_wrap marginT_30 flex_row_betweens">
				<div class="top_left_wrap">
					<div>
						<div class="page_second_title ">
							<span>业绩评价得分</span>
						</div>
						<div class="top_left_main flex_row_betweens">
							<li class="annulus_item">
								<el-progress type="circle" stroke-linecap='square'  :format='setItemText(comprehensiveAchievementScore.qualityOverallScore)'  :percentage="comprehensiveAchievementScore.qualityOverallScore" :width='60' :height='60'>
									<span>10</span>
								</el-progress>
								<p>综合得分</p>
							</li>
							<li class="annulus_item" v-if="comprehensiveAchievementScore.S != null">
								<el-progress type="circle" stroke-linecap='square' :format='setItemText(comprehensiveAchievementScore.S)' :percentage="comprehensiveAchievementScore.S" :width='60' :height='60'></el-progress>
								<p>自评</p>
							</li>
							<li class="annulus_item" v-if="comprehensiveAchievementScore.P != null">
								<el-progress type="circle" stroke-linecap='square' :format='setItemText(comprehensiveAchievementScore.P)' :percentage="comprehensiveAchievementScore.P" :width='60' :height='60'></el-progress>
								<p>同级</p>
							</li>
							<li class="annulus_item" v-if="comprehensiveAchievementScore.U != null">
								<el-progress type="circle" stroke-linecap='square' :format='setItemText(comprehensiveAchievementScore.U)' :percentage="comprehensiveAchievementScore.U" :width='60' :height='60'></el-progress>
								<p>上级</p>
							</li>
							<li class="annulus_item" v-if="comprehensiveAchievementScore.B != null">
								<el-progress type="circle" stroke-linecap='square' :format='setItemText(comprehensiveAchievementScore.B)' :percentage="comprehensiveAchievementScore.B" :width='60' :height='60'></el-progress>
								<p>下级</p>
							</li>
							<li class="annulus_item" v-if="comprehensiveAchievementScore.O != null">
								<el-progress type="circle" stroke-linecap='square' :format='setItemText(comprehensiveAchievementScore.O)' :percentage="comprehensiveAchievementScore.O" :width='60' :height='60'></el-progress>
								<p>协同</p>
							</li>
							<li class="annulus_item" v-if="comprehensiveAchievementScore.L != null">
								<el-progress type="circle" stroke-linecap='square' :format='setItemText(comprehensiveAchievementScore.L)' :percentage="comprehensiveAchievementScore.L" :width='60' :height='60'></el-progress>
								<p>分管领导</p>
							</li>
							<li class=" last_annulus_item">
								{{comprehensiveAchievementScore.actualQualityGrade}}
							</li>
						</div>
					</div>
				</div>
				<div class="top_right_wrap">
					<div class="ranking_wrap">
						<div class="page_second_title ">
							<span>业绩评价排名</span>
						</div>
						<ul class="ranking_main  flex_row_betweens">
							<li class="item_wrap">
								<p class="title">全司排名</p>
								<p class="number"><span class="weight">{{comprehensiveAchievementRanking.wholeRanking}}</span>/{{comprehensiveAchievementRanking.whole}} </p>
							</li>
							<li class="item_wrap">
								<p class="title">本部门排名</p>
								<p class="number"><span class="weight">{{comprehensiveAchievementRanking.orgRanking}}</span>/{{comprehensiveAchievementRanking.org}} </p>
							</li>
							<li class="item_wrap">
								<p class="title">本职位排名</p>   
								<p class="number"><span class="weight">{{comprehensiveAchievementRanking.jobRanking}}</span>/{{comprehensiveAchievementRanking.job}} </p>
							</li>
						</ul>
					</div>
				</div>
			</div>
			<div class="score_chart_wrap">
				<div class="page_second_title ">
					<span>业绩评价得分对比</span>
				</div>
				<div class="report_section_content">
					<div
						class="chart_box factor"
						id="scoreChart"
					></div>
				</div>
			</div>
			<div class="bottom_wrap">
				<div class="page_second_title ">
					<span>业绩评价具体表现</span>
				</div>
				<div>
	        		<tableComponent :tableData="tableData" :border="true" :needPagination="false" :needIndex="false"></tableComponent>
				</div>
			</div>
			<div class="bottom_wrap">
				<div class="page_second_title ">
					<span>上级综合评语</span>
				</div>
				<p class="remark_info" v-for="(item,index) in achievementComments.u" :key='index'>
					{{item}}
				</p>
			</div>
		</div>
	</div>
</template>

<script>
	// 业绩评价
	import {getUserAchievementEval} from "../../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
	import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
	export default {
		name: "userRPerformanceEval",
		props: ["nextBtnText", "enqId", "userId", "postCode"],
		components: {
			tableComponent
		},
		data() {
			return {
				comprehensiveAchievementScore:[],
				comprehensiveAchievementRanking:'',
				achievementScoreComparison:{
					data:[],
					legend:[],
				},
				tableData:{
					columns: [
                        {
                            label: "评价维度",
                            prop: "name",
                            width: "200",
                        },
						{
							label: "评估内容",
                            prop: "content",
                            width: "200",
						},
						{
							label: "表现",
                            prop: "performance",
                            width: "",
						},
					],
					data: [],
				},
				achievementComments:''

			
			};
		},
		created() {
			
		},
		computed:{
			
		},
		mounted(){
			this.getUserAchievementEvalFun()
		},
		methods: {
			setItemText(value){
				return ()=>{
					return value
				}
			},
			getUserAchievementEvalFun(){
				getUserAchievementEval({
					enqId:this.enqId,
					userId:this.userId
				}).then(res=>{
					if(res.code == 200){
						this.comprehensiveAchievementScore = res.data.comprehensiveAchievementScore
						this.comprehensiveAchievementRanking = res.data.comprehensiveAchievementRanking
						this.achievementScoreComparison.data = res.data.achievementScoreComparison.chartData
						this.achievementScoreComparison.legend = res.data.achievementScoreComparison.legend
						echartsRenderPage(
							'scoreChart',      //id
							"XBar",				   // 图表类型
							'1100',                  //宽
							'300',                  //高
							this.achievementScoreComparison    //图表数据
						)
						this.tableData.data = res.data.achievementSpecificPerformance
						this.achievementComments = res.data.achievementComments
					}
				})
			}
		}
	};
</script>

<style scoped lang="scss">
.userR_performance_eval_wrap {
	padding: 0 10px;
	height: 480px;
	overflow:auto ;	
	pointer-events: auto;
	.userR_performance_eval_main{
		.top_wrap{
			.top_left_wrap{
				width: 590px;
				.top_left_main{
					padding: 25px 20px 0 0px;
					.annulus_item{
						text-align: center;
						p{
							margin: 10px 0 0 0;
						}
						.el-progress{
							.el-progress__text{
								margin: 0 0 0 15%;
								width: 42px;
								height: 42px;
								line-height: 45px;
								background: #dae8fd;
								border-radius: 50%;
							}
						}
					}
					.last_annulus_item{
						width: 100px;
						height: 80px;
						line-height: 80px;
						text-align: center;
						color: #008fff;
						background: #dae8fd;
						border-radius: 8%;
					}
				}
				
			}
			.top_right_wrap{
				flex: 1;
				.ranking_wrap{
					width: 590px;
					.ranking_main{
						padding: 25px 20px 0 0px;
						.item_wrap{
							padding: 20px 0;
							width: 160px;
							height: 90px;
							background: #dae8fd;
							text-align: center;
							color: #008fff;
							.title{
								font-weight: 600;
							}
							.number{
								margin: 20px 0 0 0;
								font-size: 12px;
								.weight{
									font-size: 14px;
									font-weight: 600;
								}
							}

						}
					}
				}
				
			}
		}
		.score_chart_wrap{
			.page_second_title{
				margin: 15px 0;
			}
			.report_section_content{
				border: 1px solid #dcdfe6;
				width: 100%;
				height: 300px;
			}
		}
		.bottom_wrap{
			.page_second_title{
				margin: 15px 0;
			}
			.remark_info{
				line-height: 25px;
			}
		}
		
	}
}
</style> -->
