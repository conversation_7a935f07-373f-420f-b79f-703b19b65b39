<script setup>
import Table from "../../components/table.vue";
const numberArea = ref([
  {
    num: "0~59",
  },
  {
    num: "60~69",
  },
  {
    num: "70~79",
  },
  {
    num: "80~89",
  },
  {
    num: "90~100",
  },
]);

const columns = ref([
  {
    label: "姓名",
    prop: "a",
  },
  {
    label: "岗位",
    prop: "b",
  },
  {
    label: "成就",
    prop: "c",
    slot: "cSlot",
    width: 80,
  },
  {
    label: "地位",
    prop: "d",
    slot: "dSlot",
    width: 80,
  },
  {
    label: "发展前景",
    prop: "e",
    slot: "eSlot",
    width: 80,
  },
  {
    label: "工作氛围",
    prop: "f",
    slot: "fSlot",
    width: 80,
  },
  {
    label: "互动",
    prop: "g",
    slot: "gSlot",
    width: 80,
  },
  {
    label: "积极性",
    prop: "h",
    slot: "hSlot",
    width: 80,
  },
  {
    label: "竞争",
    prop: "i",
    slot: "iSlot",
    width: 80,
  },
  {
    label: "灵活性",
    prop: "j",
    slot: "jSlot",
    width: 80,
  },
  {
    label: "求胜心",
    prop: "k",
    slot: "kSlot",
    width: 80,
  },
  {
    label: "趣味性",
    prop: "l",
    slot: "lSlot",
    width: 80,
  },
  {
    label: "权力",
    prop: "m",
    slot: "mSlot",
    width: 80,
  },
  {
    label: "认可",
    prop: "n",
    slot: "nSlot",
    width: 80,
  },
  {
    label: "商业意识",
    prop: "o",
    slot: "oSlot",
    width: 80,
  },
  {
    label: "事业心",
    prop: "p",
    slot: "pSlot",
    width: 80,
  },
  {
    label: "物质奖励",
    prop: "q",
    slot: "qSlot",
    width: 80,
  },
  {
    label: "自我成长",
    prop: "r",
    slot: "rSlot",
    width: 80,
  },
  {
    label: "自我价值观",
    prop: "s",
    slot: "sSlot",
    width: 80,
  },
  {
    label: "自主",
    prop: "t",
    slot: "tSlot",
    width: 80,
  },
  {
    label: "操作",
    prop: "u",
    slot: "uSlot",
    align: "center",
    width: 180,
  },
]);
const data = ref([
  {
    a: "王伟",
    b: "供应链总监",
    c: 94,
    d: 83,
    e: 14,
    f: 83,
    g: 93,
    h: 43,
    i: 54,
    j: 63,
    k: 74,
    l: 83,
    m: 93,
    n: 93,
    o: 93,
    p: 93,
    q: 93,
    r: 33,
    s: 94,
    t: 94,
  },
]);

const columns2 = ref([
  {
    label: "激励因素类型",
    prop: "a",
    width: 150,
  },
  {
    label: "维度",
    prop: "b",
    width: 150,
  },
  {
    label: "维度说明",
    prop: "c",
  },
  {
    label: "管理建议",
    prop: "d",
  },
]);
const data2 = ref([
  {
    a: "强激励因素",
    b: "事业心",
    c: "追求职业成功和长期成就的意愿",
    d: "提供挑战性项目、职业规划指导，认可贡献以激发抱负。",
  },
]);

const columns3 = ref([
  {
    label: "维度",
    prop: "a",
    width: 150,
  },
  {
    label: "得分",
    prop: "b",
    width: 150,
  },
  {
    label: "风险预警",
    prop: "c",
  },
]);
const data3 = ref([
  {
    a: "工作氛围",
    b: 32,
    c: "对组织文化高度敏感，官僚主义或负面人际环境会引发强烈抵触",
    d: "",
    e: "",
    f: "",
  },
]);

const columns4 = ref([
  {
    label: "动力维度",
    prop: "a",
    width: 150,
  },
  {
    label: "管理策略",
    prop: "b",
    width: 150,
  },
  {
    label: "执行要点",
    prop: "c",
  },
]);
const data4 = ref([
  {
    a: "事业心",
    b: "绑定企业战略里程碑",
    c: "让其负责“3年库存周转降至30天”公司级KPI",
    d: "",
    e: "",
    f: "",
  },
]);
const incentiveFactor = ref([
  {
    title: "强激励因素",
    info: ["成就", "认可", "自我成长", "发展前景"],
  },
  {
    title: "一般激励因素",
    info: ["竞争", "事业心"],
  },
  {
    title: "一般负激励因素",
    info: ["权力", "工作氛围", "趣味性", "自主"],
  },
  {
    title: "强负激励因素",
    info: ["求胜心", "灵活性", "物质奖励", "地位"],
  },
]);

const circleColor = (v) => {
  if (v < 59) {
    return "bg1_b";
  } else if (v > 59 && v < 69) {
    return "bg2_b";
  } else if (v > 69 && v < 79) {
    return "bg3_b";
  } else if (v > 79 && v < 89) {
    return "bg4_b";
  } else if (v > 89 && v <= 100) {
    return "bg5_b";
  }
};

const aiData = ref([
  {
    title: "",
    info: "强激励因素：最重视的驱动力，直接关联工作热情和绩效。高分表明员工渴望在这些方面得到满足，应主动提供机会和资源，以最大化激励效果。",
  },
  {
    title: "",
    info: "一般激励因素：这些因素有中等激励作用，但员工依赖度不高。满足这些因素可补充动力，但优先级较低。宜通过常规措施（如培训或反馈）维持，避免过度投入。",
  },
  {
    title: "",
    info: "一般负激励因素：缺失时易引起不满，但满足后不会大幅提升动力。低分表示员工不强烈追求这些，但基本需求需保障。管理者应监控基本水平，防止问题积累。",
  },
  {
    title: "",
    info: "强负激励因素：潜在“不满源”，缺失会引发强烈负面情绪（如离职倾向），极低分表明员工对这些因素敏感。须优先保障，通过改善环境或支持机制来化解风险。",
  },
]);
const ai2Data = ref({
  t: "",
  info: [
    {
      title: "核心特征：使命驱动的专业主义者",
    },
    {
      title: "核心驱动力：职业价值实现（强激励因素集群）",
      info: [
        {
          title: "",
          info: "事业心（90）：接近满分，表明他将职业成就视为人生核心价值，工作不仅是谋生手段更是自我实现的途径。",
        },
        {
          title: "",
          info: "发展前景（88）与积极性（87）：形成“价值-行动”闭环，高成长预期驱动持续主动投入。",
        },
        {
          title: "",
          info: "物质奖励（83）、认可（83）、地位（83）：三者同分构成成就反馈三角，反映其对外部价值标尺的依赖——需要薪酬、头衔、他人评价等具象化证据确认自身价值。",
        },
      ],
    },
    {
      title: "矛盾点：成就动机的结构性失衡 ",
      info: [
        {
          title: "",
          info: "高事业心（90） vs 低成就需求（33）：看似矛盾，实则揭示其独特动机模式；",
        },
        {
          title: "",
          info: "事业心指向对职业身份的长期承诺（如专业权威、行业地位）；",
        },
        {
          title: "",
          info: "成就需求关联短期目标达成（如KPI突破、竞赛获胜）。",
        },
      ],
    },
    {
      title: "关键风险：强负激励因素的“隐性危机”",
    },
  ],
});
const ai3Data = ref({
  t: "",
  info: [
    {
      title: "被忽视的杠杆：一般负激励因素",
    },
    {
      title: "自我价值观（45）与自主（42）：虽属负激励范畴，但揭示潜在机会：",
      info: [
        {
          title: "",
          info: "低自主需求：适合结构化强、流程清晰的角色（如研发专家、合规顾问）；",
        },
        {
          title: "",
          info: "低价值观整合：更关注专业价值而非企业使命，可用技术挑战替代文化灌输。",
        },
      ],
    },
    {
      title: "管理行动建议：",
    },
  ],
});
const ai4Data = ref({
  t: "",
  info: [
    {
      title: "人员动力总结",
      info: [
        {
          title: "",
          info: "适用于TA的激励公式：可持续动力 = (事业心×发展前景) / (成就缺口²) + 物质奖励×地位",
        },
        {
          title: "",
          info: "刘威是典型的“专业价值实现者”，需通过行业级挑战强化职业使命感，同时严格隔离组织负向环境干扰。其独特动机结构要求跳出传统激励框架，在技术权威体系而非管理体系中构建成长路径，可将其定位为供应链架构师而非执行者，通过参与行业论坛激活事业心，同时屏蔽具体业务指标压力。",
        },
      ],
    },
  ],
});
</script>
<template>
  <div class="content-wrap">
    <div class="page-title-line">人员动力概要</div>
    <div class="number_area_list justify-start">
      分值：
      <div class="item_wrap" v-for="(item, index) in numberArea">
        <span
          class="icon"
          :class="{
            act: index == 0,
            act1: index == 1,
            act2: index == 2,
            act3: index == 3,
            act4: index == 4,
          }"
        ></span
        >{{ item.num }}
      </div>
    </div>
    <Table
      :roundBorder="false"
      :columns="columns"
      :data="data"
      headerColor
      showIndex
    >
      <template v-slot:cSlot="scope">
        <span class="circle" :class="circleColor(scope.row.c)">{{
          scope.row.c
        }}</span>
      </template>
      <template v-slot:dSlot="scope">
        <span class="circle" :class="circleColor(scope.row.d)">{{
          scope.row.d
        }}</span>
      </template>
      <template v-slot:eSlot="scope">
        <span class="circle" :class="circleColor(scope.row.e)">{{
          scope.row.e
        }}</span>
      </template>
      <template v-slot:fSlot="scope">
        <span class="circle" :class="circleColor(scope.row.f)">{{
          scope.row.f
        }}</span>
      </template>
      <template v-slot:gSlot="scope">
        <span class="circle" :class="circleColor(scope.row.g)">{{
          scope.row.g
        }}</span>
      </template>
      <template v-slot:hSlot="scope">
        <span class="circle" :class="circleColor(scope.row.h)">{{
          scope.row.h
        }}</span>
      </template>
      <template v-slot:iSlot="scope">
        <span class="circle" :class="circleColor(scope.row.i)">{{
          scope.row.i
        }}</span>
      </template>
      <template v-slot:jSlot="scope">
        <span class="circle" :class="circleColor(scope.row.j)">{{
          scope.row.j
        }}</span>
      </template>
      <template v-slot:kSlot="scope">
        <span class="circle" :class="circleColor(scope.row.k)">{{
          scope.row.k
        }}</span>
      </template>
      <template v-slot:lSlot="scope">
        <span class="circle" :class="circleColor(scope.row.l)">{{
          scope.row.l
        }}</span>
      </template>

      <template v-slot:mSlot="scope">
        <span class="circle" :class="circleColor(scope.row.m)">{{
          scope.row.m
        }}</span>
      </template>
      <template v-slot:nSlot="scope">
        <span class="circle" :class="circleColor(scope.row.n)">{{
          scope.row.n
        }}</span>
      </template>
      <template v-slot:oSlot="scope">
        <span class="circle" :class="circleColor(scope.row.o)">{{
          scope.row.o
        }}</span>
      </template>
      <template v-slot:pSlot="scope">
        <span class="circle" :class="circleColor(scope.row.p)">{{
          scope.row.p
        }}</span>
      </template>
      <template v-slot:qSlot="scope">
        <span class="circle" :class="circleColor(scope.row.q)">{{
          scope.row.q
        }}</span>
      </template>
      <template v-slot:rSlot="scope">
        <span class="circle" :class="circleColor(scope.row.r)">{{
          scope.row.r
        }}</span>
      </template>
      <template v-slot:sSlot="scope">
        <span class="circle" :class="circleColor(scope.row.s)">{{
          scope.row.s
        }}</span>
      </template>
      <template v-slot:tSlot="scope">
        <span class="circle" :class="circleColor(scope.row.t)">{{
          scope.row.t
        }}</span>
      </template>

      <template v-slot:uSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>详情</el-button>
        <el-button class="ai_btn" type="primary" plain round>对比</el-button>
      </template>
    </Table>
    <div class="tips">已选人员：<span>王伟</span></div>
    <div class="page-title-line">人员动力详情（刘威-供应链总监）</div>
    <div class="tip_blue_bg marginB20">
      工作驱动（ 哪些因素能够有效的驱动个人更加的投入工作 ）
    </div>
    <div class="chart_box marginB20"></div>
    <div class="page-title-line">激励因素（对其产生激励效果的因素）</div>
    <div class="chart_list_wrap justify-between">
      <div class="item_wrap" v-for="item in incentiveFactor">
        <div class="chart_t">{{ item.title }}</div>
        <div class="type_box justify-between">
          <div class="item_factor" v-for="item1 in item.info">
            <span> {{ item1 }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="section_title blue_section_wrap justify-between">
      <div class="t">激励因素说明</div>
      <div class="line"></div>
      <div class="ai">AI解读</div>
    </div>

    <div class="">
      <div class="item_wrap marginT10" v-for="item in aiData">
        <span class="icon"></span>
        <span class="title">{{ item.title }}</span>
        <span class="info">{{ item.info }}</span>
      </div>
    </div>
    <div class="table2_wrap">
      <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor>
      </Table>
    </div>
    <div class="page-title-line marginB20">
      人员动力 AI解读（刘威-供应链总监）
    </div>
    <div class="">
      <div class="t marginB20">{{ ai2Data.t }}</div>
      <div class="dot_content_wrap" v-for="item in ai2Data.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          <span class="icon"></span>
          <span class="title">{{ item1.title }}</span>
          <span class="info">{{ item1.info }}</span>
        </div>
      </div>
    </div>
    <div class="table3_wrap">
      <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor>
      </Table>
    </div>
    <div class="">
      <div class="t marginB20">{{ ai3Data.t }}</div>
      <div class="dot_content_wrap" v-for="item in ai3Data.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          <span class="icon"></span>
          <span class="title">{{ item1.title }}</span>
          <span class="info">{{ item1.info }}</span>
        </div>
      </div>
    </div>
    <div class="table4_wrap">
      <Table :roundBorder="false" :columns="columns4" :data="data4" headerColor>
      </Table>
    </div>
    <div class="">
      <div class="t marginB20">{{ ai4Data.t }}</div>
      <div class="dot_content_wrap" v-for="item in ai4Data.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          <span class="icon"></span>
          <span class="title">{{ item1.title }}</span>
          <span class="info">{{ item1.info }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}

.content-wrap {
  :deep .el-table {
    .ai_btn {
      padding: 0 15px;
      height: 24px;
      font-size: 14px;
    }
    .circle {
      display: inline-block;
      width: 65px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      color: #fff;
      border-radius: 10px;
    }
    .bg1_b {
      background: #deeff9;
      color: #3d3d3d;
    }
    .bg2_b {
      background: #95d9f0;
    }
    .bg3_b {
      background: #65bbea;
    }
    .bg4_b {
      background: #2c89cd;
    }
    .bg5_b {
      background: #00659b;
    }
  }

  .tips {
    margin: 16px 0 40px;
    color: #94a1af;
    span {
      color: #3d3d3d;
    }
  }
  .tip_blue_bg {
    padding: 0 12px;
    height: 36px;
    line-height: 36px;
    background: #eff4f9;
    border-radius: 5px 5px 5px 5px;
    font-size: 14px;
    color: #40a0ff;
  }
  .chart_box {
    height: 300px;
  }
  .number_area_list {
    font-size: 12px;
    .item_wrap {
      margin: 0 25px 24px 0;
      .icon {
        display: inline-block;
        margin: 0 5px 0 15px;
        width: 14px;
        height: 8px;
        &.act {
          background: #deeff9;
        }
        &.act1 {
          background: #95d9f0;
        }
        &.act2 {
          background: #65bbea;
        }
        &.act3 {
          background: #2c89cd;
        }
        &.act4 {
          background: #00659b;
        }
      }
    }
  }
  .chart_list_wrap {
    margin: 0 -6px;
    .item_wrap {
      margin: 0 5px;
      padding: 7px 9px;
      flex: 1;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      .chart_t {
        height: 29px;
        line-height: 29px;
        text-align: center;
        background: #e4eef6;
        border-radius: 5px 5px 5px 5px;
        color: #7a94ad;
      }
      .type_box {
        padding: 5px 0 10px 0;
        width: 100%;
        height: 150px;
        flex-wrap: wrap;
        align-items: center;
        .item_factor {
          margin-top: 10px;
          padding: 7px;
          width: 48%;
          height: 56px;
          border-radius: 5px 5px 5px 5px;
          border: 2px solid #cee7ff;
          span {
            display: block;
            width: 100%;
            height: 40px;
            line-height: 40px;
            text-align: center;
            border-radius: 5px 5px 5px 5px;
            background: #cee7ff;
            color: #40a0ff;
            font-size: 16px;
          }
        }
      }
    }
  }
  .section_title {
    margin: 36px 0 15px;
    .t {
      color: #40a0ff;
    }
    .line {
      flex: 1;
      margin: 10px 7px 0;
      height: 1px;
      background: #d8d8d8;
    }
    .ai {
      margin-top: -5px;
      width: 73px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #40a0ff;
      border: 1px solid #40a0ff;
      border-radius: 18px;
    }
    &.blue_section_wrap {
    }
  }
  .table2_wrap {
    margin: 38px 0 63px;
  }
  .table3_wrap {
    margin: 27px 0;
  }
  .table4_wrap {
    margin: 13px 0 27px 0;
  }

  .section_box_wrap {
    background: #fff;
    .item_wrap {
      .item_title {
        margin: 10px 0 10px 0;
      }
      .info_item {
      }
    }
  }
  :deep .demo-form-inline {
    .el-form-item {
      width: 25%;
      margin-right: 0;
      .el-form-item__label {
        width: 130px;
        text-align: right;
      }
    }
  }
  .dot_content_wrap {
    // margin: 0 0 40px 0;
    .t {
      line-height: 34px;
    }
    .item_wrap {
      margin-bottom: 0;
      .icon {
        margin: -2px 0px 0 15px;
      }
      .info {
        line-height: 34px;
      }
    }
  }
}
</style>
