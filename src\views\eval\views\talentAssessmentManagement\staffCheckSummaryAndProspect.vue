<template>
  <div class="bg_write">
    <div class="staffCheck_summaryAnd_prospect_wrap">
      <div class="page_main_title">
        {{ pageTitle }}
        <div class="goback_geader" v-link="'/talentAssessment/talentAssessmentManagement/evaluationItemList'">
          <i class="el-icon-arrow-left"></i>返回
        </div>
      </div>
      <div class="page_main">
        <div class="tip_wrap">个人发展总结与发展期望</div>
        <div class="personal_planning_main clearfix">
          <div class="marginT_30 clearfix">
            <div class="panel_left fl">
              <div class="current_post">
                <div class="page_second_title">当前职位</div>
                <div class="current_post_center marginT_16">
                  <div class="item">
                    <div class="item_title">职位名称</div>
                    <div class="item_text">{{ currPostName }}</div>
                  </div>
                  <div class="item">
                    <div class="item_title">所属部门</div>
                    <div class="item_text">{{ currOrgName }}</div>
                  </div>
                  <div class="item">
                    <div class="item_title">职位族群</div>
                    <div class="item_text">
                      {{ parentJobClassName }}
                    </div>
                  </div>
                  <div class="item">
                    <div class="item_title">职位序列</div>
                    <div class="item_text">{{ jobClassName }}</div>
                  </div>
                </div>
              </div>
              <div class="current_post marginT_30">
                <div class="page_second_title">发展类型</div>
                <div class="type_wrap marginT_20">
                  <el-radio-group v-model="developmentType" @change="devTypeChange">
                    <el-radio
                      v-for="item in developmentTypeOptions"
                      :key="item.dictCode"
                      :label="item.dictCode"
                      border
                      >{{ item.codeName }}</el-radio
                    >
                  </el-radio-group>
                </div>
              </div>
            </div>
            <div class="panel_center fl">
              <div class="current_post">
                <div class="" v-if="developmentType == 1">
                  <div class="page_second_title">下一晋升职位</div>
                  不需要选择晋升职位
                </div>
                <!-- <div class="goal_post" v-show="developmentType == 2">
                  <div class="page_second_title">下一晋升职位</div>
                  <div
                    class="current_post_center marginT_16"
                    v-if="!jobTipSign"
                  >
                    <div class="item">
                      <div class="item_title">职位族群</div>
                      <div class="item_text">
                        {{ goalPostInfo.parentJobClassName }}
                      </div>
                    </div>
                    <div class="item">
                      <div class="item_title">职位序列</div>
                      <div class="item_text">
                        {{ goalPostInfo.jobClassName }}
                      </div>
                    </div>
                    <div class="item">
                      <div class="item_title">职位名称</div>
                      <div class="item_text">
                        {{ goalPostInfo.jobName }}
                      </div>
                    </div>
                    <div class="item">
                      <div class="item_title">职层</div>
                      <div class="item_text">
                        {{ goalPostInfo.jobLevelName }}
                      </div>
                    </div>
                    <div class="item">
                      <div class="item_title">职等</div>
                      <div class="item_text">
                        {{ goalPostInfo.jobGradeName }}
                      </div>
                    </div>
                  </div>
                  <div v-if="jobTipSign">{{ jobTip }}</div>
                </div> -->

                <div class="" v-show="developmentType == 3 || developmentType == 2">
                  <div class="page_second_title">选择理想的下一晋升职位</div>
                  <div class="promotion_post clearfix marginT_16">
                    <div class="post_type fl">
                      <div class="title border_r_1">职族</div>
                      <div class="post_type_center border_r_1">
                        <div class="post_type_main">
                          <tree-comp-radio
                            :treeData="treeData"
                            :needCheckedFirstNode="false"
                            :nodeKey="'code'"
                            :defaultCheckedKeys="defaultCheckedKeys"
                            @clickCallback="treeCallback"
                          ></tree-comp-radio>
                        </div>
                      </div>
                    </div>
                    <div class="post_type fl">
                      <div class="title">职位名称</div>
                      <div class="post_type_center">
                        <div class="post_type_main">
                          <el-radio-group v-model="checkGoalPostCode">
                            <el-radio
                              class="post_item"
                              v-for="item in postList"
                              :key="item.jobCode"
                              :label="item.jobCode"
                              >{{ item.jobName }}</el-radio
                            >
                          </el-radio-group>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div class="" v-if="developmentType == 5">
                                <div class="page_second_title">当前职位</div>
                                <div class="marginT_16">职位名称</div>
                                <div class="marginT_16">销售经理</div>
                            </div> -->
              </div>
            </div>
            <div class="panel_right fl">
              <div class="current_post">
                <div class="page_second_title">发展目标</div>
                <div class="development_goals marginT_16">
                  <div class="item">
                    <div class="title">预计周期</div>
                    <div class="center">
                      <el-select v-model="expectationCycle" placeholder="请选择">
                        <el-option
                          v-for="item in expectationCycleOptions"
                          :key="item.dictCode"
                          :label="item.codeName"
                          :value="item.dictCode"
                        ></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div class="item">
                    <div class="title">个人短板分析与总结</div>
                    <div class="center">
                      <el-input
                        type="textarea"
                        v-model="analyzeSummarize"
                        rows="5"
                        resize="none"
                        placeholder
                      ></el-input>
                    </div>
                  </div>
                  <div class="item">
                    <div class="title">个人发展计划</div>
                    <div class="center">
                      <el-input type="textarea" v-model="developmentPlan" rows="5" resize="none" placeholder></el-input>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="marginT_30 align_center">
          <el-button class="page_confirm_btn" type="primary" @click="submit">确认</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import {
  getEvalUserDevelopment,
  getIdealJob,
  saveEvalUserDevelopment,
  getEvalInfo,
  getDevelopmentFlag,
  answerSubmit
} from '../../request/api'
import { getOrgDeptTree, getPostList, searchPost } from '@/views/entp/request/api'
import { getPostData, getJobList } from '@/views/enq/request/api'
import { getJobClassTree, getSearchJobList } from '@/views/entp/request/api'
export default {
  name: 'staffCheckSummaryAndProspect',
  // props: ["evalId"],
  components: {
    treeCompRadio,
    tableComponent
  },
  data() {
    return {
      pageTitle: '',
      evalId: this.$route.query.evalId,
      currPostName: '',
      currOrgName: '',
      parentJobClassName: '',
      jobClassName: '',
      expectationCycle: '',
      expectationCycleOptions: [],
      analyzeSummarize: '',
      developmentPlan: '',
      developmentType: '1',
      developmentTypeOptions: [],
      expectationJob: '',
      defaultCheckedKeys: [],

      // 向上级别发展 理想的下一级晋升岗位
      goalPostInfo: {},
      hasParentPost: true,
      // 跨通道发展
      treeData: [],
      postList: [],
      orgCode: null,
      checkGoalPostCode: '',

      jobTipSign: false,
      jobTip: '',
      jumpStatus: ''
    }
  },
  created() {
    this.$getDocList(['DEVELOPMENT_TYPE', 'EXPECTATION_CYCLE']).then(res => {
      console.log(res)
      this.developmentTypeOptions = res.DEVELOPMENT_TYPE
      this.expectationCycleOptions = res.EXPECTATION_CYCLE
    })
    this.getDevelopmentFlagFn()
    this.getEvalUserDevelopmentFun()
    this.getIdealJobFun()
    this.getJobClassTreeFun()
    this.getEvalInfoFun()
  },
  computed: {
    userId() {
      return this.$store.state.userInfo.userId
    }
  },
  watch: {
    developmentType: function (val) {
      console.log(val)
      if (val == 3) {
        this.getJobClassTreeFun()
      }
    }
  },
  methods: {
    // 基本信息
    getEvalUserDevelopmentFun() {
      getEvalUserDevelopment({
        evalId: this.evalId
      }).then(res => {
        console.log(res)
        this.defaultCheckedKeys = []
        // 当前岗位
        this.currPostName = res.jobName
        this.currOrgName = res.orgName
        this.parentJobClassName = res.parentJobClassName
        this.jobClassName = res.jobClassName
        // 下一晋升岗位
        // if (res.developmentType == "2") {
        //   this.expectationJob = res.expectationJob;
        // } else if (res.developmentType == "3") {
        //   this.checkGoalPostCode = res.expectationJob;
        //   this.defaultCheckedKeys.push(res.jobClassCode);
        // }
        if (res.developmentType != '1') {
          this.checkGoalPostCode = res.expectationJob
          this.defaultCheckedKeys.push(res.jobClassCode)
        }
        // 预计周期
        this.expectationCycle = res.expectationCycle
        // 个人短板分析与总结
        this.analyzeSummarize = res.analyzeSummarize
        // 个人发展计划
        this.developmentPlan = res.developmentPlan
        // 发展类型
        this.developmentType = res.developmentType ? res.developmentType : '1'
      })
    },
    // 发展类型切换
    devTypeChange(val) {
      this.developmentType = val
      if (val == '1') {
      } else if (val == '2') {
      } else if (val == '3') {
      }
    },
    // 选择理想下一晋升岗位
    getIdealJobFun() {
      getIdealJob({
        evalId: this.evalId,
        developmentType: '2'
      }).then(res => {
        this.jobTipSign = false
        if (res.data) {
          this.hasParentPost = true
          this.goalPostInfo = res.data
        } else {
          this.hasParentPost = false
          if (res.code == 500) {
            this.jobTipSign = true
            this.jobTip = res.msg
          }
          // this.$msg.warning("当前岗位暂无上级岗位");
        }
      })
    },
    //族群序列树结构
    getJobClassTreeFun() {
      getJobClassTree({}).then(res => {
        console.log(res)
        if (res.length > 0) {
          this.treeData = res
        } else {
          this.treeData = []
        }
      })
    },
    // 选择组织
    treeCallback(code) {
      console.log(code)
      this.orgCode = code
      console.log(this.developmentType)
      // if (this.developmentType == 3) {
      this.getSearchJobListFun()
      // }
    },
    getSearchJobListFun() {
      getSearchJobList({
        jobClassCode: this.orgCode
      }).then(res => {
        console.log(res)
        this.postList = []
        if (res.code == 200) {
          this.postList = res.data
        }
      })
    },
    // 保存个人发展期望
    saveEvalUserDevelopmentFun() {
      // if (this.developmentType == "1") {
      //   this.expectationJob = "";
      // } else if (this.developmentType == "2") {
      //   this.expectationJob = this.goalPostInfo.jobCode;
      // } else if (this.developmentType == "3") {
      //     if(!this.checkGoalPostCode){
      //         this.$msg.warning("请选择下一晋升职位")
      //         return
      //     }
      //   this.expectationJob = this.checkGoalPostCode;
      // }
      if (this.developmentType == '1') {
        this.expectationJob = ''
      } else {
        if (!this.checkGoalPostCode) {
          this.$msg.warning('请选择下一晋升职位')
          return
        }
        this.expectationJob = this.checkGoalPostCode
      }
      let params = {
        analyzeSummarize: this.analyzeSummarize,
        developmentPlan: this.developmentPlan,
        developmentType: this.developmentType,
        evalId: this.evalId,
        expectationCycle: this.expectationCycle,
        expectationJob: this.expectationJob
      }
      saveEvalUserDevelopment(params).then(res => {
        this.$msg.success(res.msg)
        getDevelopmentFlag({ evalId: this.evalId }).then(res => {
          console.log(res)
          if (res == -1) {
            answerSubmit({ evalId: this.evalId }).then(res => {
              console.log(res)
              if (res.code == 200) {
                this.$router.push('/talentAssessment/talentAssessmentManagement/evaluationItemList')
              } else {
                this.$msg.error(res.msg)
              }
            })
          } else if (res == 2) {
            // 改变答题状态
            this.$router.push(`/talentAssessment/staffCheckEvaluate?evalId=${this.evalId}`)
          }
        })
      })
    },
    // 提交
    submit() {
      if (!this.expectationCycle) {
        this.$msg.warning('请选择预计周期')
        return
      }
      if (!this.analyzeSummarize) {
        this.$msg.warning('请填写个人短板分析与总结')
        return
      }
      if (!this.developmentPlan) {
        this.$msg.warning('请填写个人发展计划')
        return
      }
      this.saveEvalUserDevelopmentFun()
    },
    //获取测评title
    getEvalInfoFun() {
      getEvalInfo({
        evalId: this.evalId
      }).then(res => {
        // console.log(res)
        this.pageTitle = res.evalName
      })
    },
    // 是否跳转至下级评价
    getDevelopmentFlagFn() {
      getDevelopmentFlag({ evalId: this.evalId }).then(res => {
        console.log(res)
        this.jumpStatus = res
      })
    }
  }
}
</script>

<style scoped lang="scss">
.staffCheck_summaryAnd_prospect_wrap {
  .tip_wrap {
    margin: 0 5px 0;
    padding: 0 15px 0;
    width: 97.5%;
    height: 40px;
    line-height: 40px;
    background: #edf4fe;
    border-radius: 4px;
  }
}
.panel_left {
  width: 220px;

  .current_post {
    width: 100%;

    .current_post_center {
      width: 100%;
      border: 1px solid #e5e5e5;
      border-bottom: none;

      .item {
        width: 100%;
        display: flex;
        height: 40px;
        line-height: 40px;
        font-size: 12px;

        .item_title {
          width: 30%;
          text-align: center;
          background: #f4f4f4;
          border-bottom: 1px solid #e5e5e5;
        }

        .item_text {
          flex: 1;
          padding-left: 12px;
          border-bottom: 1px solid #e5e5e5;
          // white-space: nowrap;
        }
      }
    }

    .type_wrap {
      width: 168px;
      .el-radio-group {
        width: 100%;
        height: 35px;
        .el-radio {
          position: relative;
          width: 100%;
          height: 100%;
          margin: 0 0 15px 0;
          line-height: 35px;
          padding: 0;
          border-color: #e5e5e5;
          &.is-checked {
            border-color: #449cff;
          }
          .el-radio__input {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 5px;
            top: 6px;
            border: 1px solid #e5e5e5;
            border-radius: 50%;
            overflow: hidden;
            .el-radio__inner {
              display: none;

              &::after {
                width: 0;
                height: 0;
              }
            }
            &.is-checked {
              background: #0099ff;
              .el-radio__inner {
                display: block;
                position: absolute;
                width: 13px;
                height: 7px;
                left: 3px;
                top: 4px;
                border: 2px solid #fff;
                border-radius: 0;
                transform: rotate(-45deg);
                border-top: none;
                border-right: none;
              }
            }
          }
        }
        .el-radio {
          position: relative;
          width: 100%;
          height: 100%;
          margin: 0 0 15px 0;
          line-height: 35px;
          padding: 0;
          border-color: #e5e5e5;

          &.is-checked {
            border-color: #449cff;
          }
          .el-radio__input {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 5px;
            top: 6px;
            border: 1px solid #e5e5e5;
            border-radius: 50%;
            overflow: hidden;

            .el-radio__inner {
              display: none;

              &::after {
                width: 0;
                height: 0;
              }
            }

            &.is-checked {
              background: #0099ff;

              .el-radio__inner {
                display: block;
                position: absolute;
                width: 13px;
                height: 7px;
                left: 3px;
                top: 4px;
                border: 2px solid #fff;
                border-radius: 0;
                transform: rotate(-45deg);
                border-top: none;
                border-right: none;
              }
            }
          }
        }
      }
    }
  }
}

.panel_center {
  min-width: 350px;
  margin-left: 70px;
  .current_post_center {
    width: 100%;
    border: 1px solid #e5e5e5;
    border-bottom: none;

    .item {
      width: 100%;
      display: flex;
      height: 40px;
      line-height: 40px;
      font-size: 12px;

      .item_title {
        width: 40%;
        text-align: center;
        background: #f4f4f4;
        border-bottom: 1px solid #e5e5e5;
      }

      .item_text {
        flex: 1;
        padding-left: 12px;
        border-bottom: 1px solid #e5e5e5;
      }
    }
  }

  .promotion_post {
    width: 350px;
    border: 1px solid #e5e5e5;

    .post_type {
      width: 50%;

      .title {
        background: #ebf4ff;
        height: 45px;
        line-height: 45px;
        text-align: center;
      }

      .post_type_center {
        padding: 10px;
        height: 360px;

        .post_type_main {
          width: 100%;
          height: 100%;
          overflow-y: auto;
          .post_item {
            line-height: 26px;
            display: block;
            margin-right: 0;
          }
        }

        .item {
          line-height: 40px;
          cursor: pointer;

          .icon {
            color: transparent;
          }

          &.active {
            color: #0099ff;

            .icon {
              color: #0099ff;
            }
          }
        }
      }
    }
  }
}

.panel_right {
  margin-left: 70px;
  width: 280px;

  .development_goals {
    .item {
      margin-bottom: 20px;
    }

    .title {
      padding-bottom: 10px;
      font-size: 14px;
    }
  }
}
.post_info {
  border: 1px solid #e5e5e5;
  padding: 16px;
  line-height: 24px;
  .post_info_item {
    .label {
      width: 80px;
      margin-right: 8px;
      color: #212121;
    }
  }
}
.tree_wrap {
  width: 300px;
}
</style>
