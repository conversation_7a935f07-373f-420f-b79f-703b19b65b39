<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserKpiComment, saveUserKpiComment } from '../../../request/api'
import { useUserStore } from '@/stores/modules/user'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const kpiList = ref([])
const checkCount = ref(0)
const isNextStepsign = ref(false)
const number = ref('')

const userStore = useUserStore()
// 这里请用 pinia 获取 userId，或根据你的项目实际情况调整
const userId = computed(() => {
  return userStore.userInfo.userId // 这里假设 window.$store.state.userInfo.userId
})

function getUserKpiCommentFun() {
  getUserKpiComment({
    enqId: props.enqId,
    userId: userId.value
  }).then(res => {
    kpiList.value = []
    if (res.code == 200 && res.data.length > 0) {
      kpiList.value = res.data.map(item => ({
        type: item.type == 'Y',
        kpiName: item.kpiName,
        kpiObjective: item.kpiObjective,
        kpiCode: item.kpiCode,
        postCode: item.postCode,
        enqId: props.enqId,
        userId: userId.value,
        userName: item.userName
      }))
      curCheckCount()
    }
  })
}

function curCheckCount() {
  checkCount.value = 0
  for (let i = 0; i < kpiList.value.length; i++) {
    if (kpiList.value[i].type === true) {
      checkCount.value++
    }
  }
}

function checkKpi(val) {
  if (val) {
    checkCount.value++
  } else {
    checkCount.value--
  }
}

function judgeIsEmpty(stepType) {
  for (let i = 0; i < kpiList.value.length; i++) {
    if (kpiList.value[i].type && !kpiList.value[i].kpiObjective) {
      ElMessage.warning('启用的指标需要填写相应目标！')
      return
    }
  }
  for (let i = 0; i < kpiList.value.length; i++) {
    if (kpiList.value[i].type && kpiList.value[i].kpiObjective) {
      saveUserKpiCommentFun(stepType)
      return
    }
  }
  number.value = 0
  for (let i = 0; i < kpiList.value.length; i++) {
    if (!kpiList.value[i].type) {
      number.value = number.value + 1
      if (number.value == kpiList.value.length) {
        ElMessage.warning('请至少勾选一个指标！')
      }
    }
  }
}

function affirm(stepType = '') {
  judgeIsEmpty(stepType)
}

function saveUserKpiCommentFun(stepType) {
  let enqObjectiveKpiList = kpiList.value.filter(item => item.type)
  enqObjectiveKpiList = enqObjectiveKpiList.map(item => ({
    type: item.type === true ? 'Y' : 'N',
    kpiName: item.kpiName,
    kpiObjective: item.kpiObjective,
    kpiCode: item.kpiCode,
    postCode: item.postCode,
    enqId: item.enqId,
    userId: item.userId,
    userName: item.userName
  }))
  saveUserKpiComment(enqObjectiveKpiList).then(res => {
    if (res.code == 200) {
      ElMessage.success(res.msg)
      if (stepType === 'prevStep') {
        emit('prevStep')
      } else if (stepType === 'nextStep') {
        emit('nextStep')
      } else if (stepType === '') {
        getUserKpiCommentFun()
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function prevBtn() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      affirm('prevStep')
    })
    .catch(action => {
      if (action === 'cancel') {
        emit('prevStep')
      }
    })
}

function nextBtn() {
  if (kpiList.value.length > 0) {
    judgeIsEmpty('nextStep')
  } else {
    emit('nextStep')
  }
}

onMounted(() => {
  getUserKpiCommentFun()
})
</script>

<template>
  <div class="KPI_evaluate_wrap performance_info_main">
    <div class="">
      <div class="page_second_title marginT_16">
        <span>绩效指标</span>
      </div>
      <div class="edu_info_center kpi_center marginT_8">
        <div class="edu_info_header kpi_evaluate_header">
          <div class="item index">启用</div>
          <div class="item">指标名称</div>
          <div class="item">目标</div>
        </div>
      </div>
      <div class="kpi_evaluate_main">
        <div class="edu_info_item" v-for="(item, index) in kpiList" :key="index">
          <el-checkbox
            class="item index"
            label=""
            name="type"
            v-model="item.type"
            @change="checkKpi(item.type)"
          ></el-checkbox>
          <div class="item">{{ item.kpiName }}</div>
          <el-input class="item" v-model="item.kpiObjective" placeholder="请填写目标"></el-input>
        </div>
      </div>
      <div class="tips_wrap">
        已选择 <span>{{ checkCount }}</span> 项指标
      </div>
      <div class="save_btn_wrap" v-if="kpiList.length > 0">
        <el-button class="page_add_btn" type="primary" @click="affirm()">保存</el-button>
      </div>
    </div>
    <div class="align_center marginT_30">
      <el-button
        class="page_confirm_btn"
        type="primary"
        @click="prevBtn"
        v-show="props.currentIndex != props.currentFirstCode"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ props.nextBtnText }}</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.performance_info_main {
}

.KPI_evaluate_wrap {
  .kpi_center {
    .kpi_evaluate_header {
      .item {
        width: 47%;
      }
      .index {
        width: 5%;
      }
    }
  }
  .kpi_evaluate_main {
    .edu_info_item {
      .item {
        width: 47%;
      }
      .index {
        margin: 0 auto;
        width: 5%;
      }
    }
  }
  .tips_wrap {
    padding: 0 0 0 20px;
    height: 45px;
    line-height: 45px;
    span {
      color: #3b92ff;
    }
  }
  .save_btn_wrap {
    width: 80px;
    height: 20px;
    margin: 0 auto;
    .page_add_btn {
    }
  }
}
</style>
