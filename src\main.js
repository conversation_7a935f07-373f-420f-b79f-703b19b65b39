import { createApp } from 'vue'
import 'virtual:svg-icons-register'
import './styles/tailwind.css'
import './styles/index.scss'
import App from './App.vue'
import pinia from './stores'
import router from './router'
import directives from './directives'
import { exportDownload } from '@/views/entp/request/api'
const app = createApp(App)
app.config.globalProperties.$router = router

app.config.globalProperties.$exportDownloadFile = function (id, fileName) {
  let name = fileName ? fileName : '导出文件'
  let params = {
    fileName: id
  }
  exportDownload(params).then(res => {
    console.log(res)
    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = `${name}.xlsx`
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href) // 释放URL 对象
    document.body.removeChild(elink)
  })
}

app.use(pinia).use(router).use(directives)
app.mount('#app')
