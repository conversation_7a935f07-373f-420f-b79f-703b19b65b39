<template>
  <div class="choose_testee_wrap" :class="{ event_none: !isEdit }">
    <div class="choose_testee_main flex_row_betweens">
      <div class="main_left">
        <p class="page_second_title">选择人员</p>
        <div class="flex_row_betweens marginT_20">
          <div class="main_left_tree page_area">
            <p class="choose_testee_title">部门</p>
            <div class="second_level_post_wrap">
              <treeCompCheckbox :treeData="treeDataDict" @node-click-callback="clickCallback"></treeCompCheckbox>
            </div>
          </div>
          <div class="main_left_choose page_area">
            <p class="choose_testee_title flex_row_between">
              <span>人员</span>
              <span>{{ userList.length }}人员</span>
              <span class="relative pointer" @click="chooseUserAll">
                <span>全选</span>
                <i :class="{ active: chooseUserAllStatus }" class="el-icon-check icon_check_all"></i>
              </span>
            </p>
            <ul class="second_level_post_wrap">
              <li
                v-for="(item, index) in userList"
                @click="chooseThirdLevelPost(item, index)"
                :class="{
                  flex_row_betweens: true,
                  active: item.isChoose
                }"
              >
                <span class="overflow_elps">{{ item.postName }} - {{ item.userName }}</span>
                <span v-if="item.isChoose" class="el-icon-check"></span>
                <span v-else class="icon_check"></span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="main_right">
        <p class="page_second_title">已选人员</p>
        <div class="flex_row_betweens marginT_20">
          <div class="selected_post page_area">
            <div class="choose_testee_title flex_row_betweens">
              <span>{{ checkedUserList.length }}人</span>
              <div @click="removeAll">
                <span class="remove_icon el-icon-delete"></span>
                <span class="remove_all">清除全部</span>
              </div>
            </div>
            <ul class="second_level_post_wrap">
              <li v-for="(item, index) in checkedUserList" :class="{ flex_row_betweens: true }">
                <span>{{ item.postName }} - {{ item.userName }}</span>
                <span class="el_del_bg el-icon-minus" @click="removePost(item.userId, index)"></span>
              </li>
            </ul>
          </div>
          <ul class="from_wrap page_area">
            <li>
              <p>平均对多少词典进行评价(项)</p>
              <div>
                <el-input type="number" min="0" v-model="avgItemCount" size="mini"></el-input>
              </div>
            </li>
            <li>
              <p>单人维护时间预估(分钟)</p>
              <div>
                <el-input type="number" min="0" v-model="avgEvalTime" size="mini"></el-input>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="talent_raview_btn_wrap align_center marginT_30" v-if="isEdit">
      <el-button class="page_confirm_btn" type="primary" @click="prev()">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="next()">下一步</el-button>
    </div>
  </div>
</template>

<script>
import { getOrgDeptTree } from '@/views/entp/request/api'
import { getEvalPost, setUpEvalUserObject } from '../../../request/api'
import treeCompCheckbox from '@/components/talent/treeComps/treeCompCheckbox'
import tabsChangeData from '@/components/talent/tabsComps/tabsChangeData'

export default {
  name: 'chooseStaff',
  components: {
    treeCompCheckbox,
    tabsChangeData
  },
  props: ['evalId', 'isEdit'],
  data() {
    return {
      validation: false, //验证人员修改
      chooseUserAllStatus: false,
      curTabIndex: '1',
      treeDataDict: [],
      userList: [],
      checkedUserList: [],
      checkedCodeList: [],
      avgItemCount: '',
      avgEvalTime: '',
      checkOrgCode: ''
    }
  },
  created() {
    this.getEvalPostFun(true)
    this.getOrgDeptTreeFun()
  },
  computed: {
    companyId() {
      return this.$store.state.userInfo.companyId
    }
  },
  methods: {
    getOrgDeptTreeFun() {
      getOrgDeptTree({ companyId: this.companyId }).then(res => {
        console.log(res)
        this.treeDataDict = res
      })
    },
    getEvalPostFun(isFirst) {
      // @params isFirst 只在第一次时刷新已选人员列表
      getEvalPost({
        evalId: this.evalId,
        orgCodes: this.checkOrgCode
      }).then(res => {
        console.log(res)
        this.avgItemCount = res.avgItemCount
        this.avgEvalTime = res.avgEvalTime
        if (res.selectedUser.length > 0) {
          this.validation = true
        }
        if (isFirst) {
          this.checkedUserList = res.selectedUser.map(item => {
            this.checkedCodeList.push(item.objPostCode + ',' + item.userId)
            return {
              postName: item.objPostName,
              postCode: item.objPostCode,
              userId: item.userId,
              userName: item.userName
            }
          })
        }
        this.userList = res.postObj.map(item => {
          return {
            postName: item.objPostName,
            postCode: item.objPostCode,
            userId: item.userId,
            userName: item.userName
          }
        })
        for (let i = 0; i < this.checkedUserList.length; i++) {
          this.userList.some((item, index) => {
            if (item.postCode == this.checkedUserList[i].postCode && item.userId == this.checkedUserList[i].userId) {
              this.userList[index].isChoose = true
            }
          })
        }
      })
    },
    clickCallback(val) {
      this.userList = []
      console.log(val)

      this.checkOrgCode = val.join(',')
      this.getEvalPostFun()
      // val.forEach(item=>{
      //     this.treeDataDict.some(obj=>{
      //         if(obj.code ==item){
      //             this.userList=[...this.userList,...obj.userInfo]
      //         }
      //     })
      // })

      // console.log(this.userList)
      // console.log(this.checkedUserList)
      //数据回选
    },
    chooseUserAll() {
      let list = this.userList
      if (list.length == 0) {
        return
      }
      let chooseStatusArr = []
      for (let i = 0, len = list.length; i < len; i++) {
        let chooseStatus = list[i].isChoose
        chooseStatusArr.push(chooseStatus)
      }
      console.log(chooseStatusArr)

      // 列表中是否有选中的项
      // 有：全部取消选中
      // 无：全部选中

      // 保存判断的列表是否全选的状态
      let hasChecked = chooseStatusArr.includes(true)
      let cancelChecked = () => {
        this.userList.forEach((item, index) => {
          this.$set(this.userList[index], 'isChoose', !hasChecked)
        })
        this.validation = false
        // 取消选中
        this.chooseUserAllStatus = false
        // this.removeAll();
        this.userList.map(item => {
          item.chooseStatus = false
          let code = item.postCode + ',' + item.userId
          this.checkedUserList.map((user, index) => {
            let key = user.postCode + ',' + user.userId
            if (key == code) {
              this.checkedCodeList.splice(index, 1)
              this.checkedUserList.splice(index, 1)
            }
          })
        })
      }

      if (hasChecked) {
        if (this.validation) {
          this.$confirm('修改已选人员将会清空评价关系数据！', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            cancelChecked()
          })
        } else {
          cancelChecked()
        }
      } else {
        // 全选
        this.chooseUserAllStatus = true
        let checkedCode = []
        this.userList.forEach((item, index) => {
          this.$set(this.userList[index], 'isChoose', !hasChecked)
          this.checkedUserList.push(item)
          checkedCode.push(item.postCode + ',' + item.userId)
        })
      }

      // // 取消选中
      // this.userList.forEach((item, index) => {
      //     if (this.chooseUserAllStatus) {
      //         let clearCode = item.job_code;
      //         this.checkedJobList.some((list, index) => {
      //             if (list.job_code == clearCode) {
      //                 this.checkedJobList.splice(index, 1);
      //                 this.checkedJobCodeList.splice(index, 1);
      //             }
      //         });
      //     } else {
      //         this.$set(this.userList[index], "isChoose", true);
      //         this.checkedJobList.push(item);
      //         this.checkedJobCodeList.push(item.job_code);
      //     }
      // });
    },
    chooseThirdLevelPost(row, index) {
      if (!this.userList[index].isChoose) {
        let addUser = () => {
          this.$set(this.userList[index], 'isChoose', true)
          this.checkedUserList.push(row)
        }
        if (this.validation) {
          this.validationFun(addUser)
        } else {
          addUser()
        }
      } else {
        let removeFun = () => {
          this.$set(this.userList[index], 'isChoose', false)
          let clearId = row.userId
          this.checkedUserList.some((item, index) => {
            if (item.userId == clearId) {
              this.checkedUserList.splice(index, 1)
            }
          })
        }
        if (this.validation) {
          this.validationFun(removeFun)
        } else {
          removeFun()
        }
      }
    },
    //修改已选人员验证
    validationFun(cb) {
      this.$confirm('修改已选人员将会清空评价关系数据！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.validation = false
          cb()
        })
        .catch(() => {
          return
        })
    },
    removePost(userId, index) {
      let removeFun = () => {
        this.checkedUserList.splice(index, 1)
        this.userList.some((item, i) => {
          if (item.userId == userId) {
            this.$set(this.userList[i], 'isChoose', false)
          }
        })
      }

      if (this.validation) {
        this.validationFun(removeFun)
      } else {
        removeFun()
      }
    },
    removeAll() {
      if (this.checkedUserList.length == 0) {
        return
      }
      let removeFun = () => {
        this.$confirm('此操作将删除所有已选人员, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.checkedUserList = []
            this.userList.forEach(item => {
              item.isChoose = false
            })
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      }

      if (this.validation) {
        this.validationFun(removeFun)
      } else {
        removeFun()
      }
    },
    prev: function () {
      this.$emit('prevStep')
    },
    next: function () {
      let paramsData = this.dataGrouping(this.checkedUserList)
      this.setUpEvalUserObjectFun(paramsData)
    },
    setUpEvalUserObjectFun(params) {
      setUpEvalUserObject(params).then(res => {
        // console.log(res);
        if (res.code == 200) {
          this.$emit('nextStep')
        } else {
          this.$msg.warning(res.msg)
        }
      })
    },
    //数据分组
    dataGrouping(data) {
      if (data.length == 0) {
        this.$msg.warning('请选择人员！')
        return
      }
      if (!this.avgEvalTime || !this.avgItemCount) {
        this.$msg.warning('请填写评价数量和预估时间！')
        return
      }
      let postCodeList = []
      let postList = []
      for (let i = 0; i < data.length; i++) {
        let postCode = data[i].postCode
        if (postCodeList.indexOf(postCode) < 0) {
          postCodeList.push(postCode)
          let obj = {
            avgEvalTime: this.avgEvalTime,
            avgItemCount: this.avgItemCount,
            evalId: this.evalId,
            objPostCode: postCode,
            objPostName: data[i].postName,
            objectIdList: [data[i].userId]
          }
          postList.push(obj)
        } else {
          postList.some((item, index) => {
            if (item.objPostCode == postCode) {
              postList[index].objectIdList.push(data[i].userId)
            }
          })
        }
      }
      return postList
    }
  }
}
</script>

<style scoped lang="scss">
.choose_testee_wrap {
  .choose_testee_title {
    padding: 0 8px;
    font-size: 16px;
    height: 34px;
    line-height: 34px;
    background: #ebf4ff;
    .remove_icon {
      font-size: 14px;
      color: #f00;
    }
    .remove_all {
      cursor: pointer;
      line-height: 34px;
      color: #f00;
      font-size: 14px;
    }
    .icon_check_all {
      display: inline-block;
      border: 2px solid #666;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      font-size: 18px;
      font-weight: bold;
      color: #e5f0f9;
      margin-left: 5px;
      line-height: 22px;
      text-align: center;
      margin-right: -5px;
      &.active {
        color: #0099ff;
        border-color: #0099ff;
      }
    }
  }

  .page_area {
    height: 400px;
    overflow: auto;
    border: 1px solid #e5e5e5;
  }

  .main_left_title {
    padding: 3px 8px;
    font-size: 16px;
    line-height: 40px;
    background: #ebf4ff;
  }

  .choose_testee_main {
    .second_level_post_wrap {
      height: 362px;
      padding: 10px;
      overflow-y: auto;

      li {
        position: relative;
        margin: 5px 0 0 0;
        padding: 0 5px;
        height: 36px;
        line-height: 36px;
        cursor: pointer;
        border: 1px solid #e4e4e4;

        .icon_check {
          position: absolute;
          border: 1px solid #ddd;
          border-radius: 50%;
          right: 5px;
          top: 5px;
          width: 24px;
          height: 24px;
        }

        .el-icon-check {
          height: 35px;
          font-size: 24px;
          line-height: 35px;
          font-weight: bold;
        }

        .el_del_bg {
          position: absolute;
          right: 5px;
          top: 5px;
          width: 24px;
          height: 24px;
          background: #ddd;
          border-radius: 50%;
          line-height: 24px;
          font-size: 20px;
          color: #fff;
          text-align: center;
        }

        .el-icon-remove-outline {
          height: 35px;
          font-size: 24px;
          line-height: 35px;
          color: #ccc;
        }
      }

      .active {
        border: 1px solid #0099ff;
        color: #0099ff;
      }

      .hover_style:hover {
        background: #ebf4ff;
      }
    }

    .main_left {
      // width:512px;
      width: 50%;
      margin-right: 20px;
      .flex_row_betweens {
        .main_left_tree {
          // width:164px;
          width: 40%;
          margin-right: 20px;
        }

        .main_left_choose {
          // width: 164px;
          width: 60%;
        }
      }
    }

    .main_right {
      // width: 356px;
      width: 50%;

      .selected_post {
        width: 60%;
      }

      .from_wrap {
        // width: 182px;
        padding: 10px;
        li {
          p {
            height: 35px;
            line-height: 35px;
          }

          div {
            .el-input__inner {
              width: 100%;
              height: 35px;
            }
          }
        }
      }
    }
  }
}
</style>
