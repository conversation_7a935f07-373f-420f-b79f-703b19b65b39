<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in eduInfoData" :key="item.id">
      <div class="item index">{{ index + 1 }}</div>
      <el-tooltip class="item" effect="dark" :content="item.orgName" placement="top">
        <span class="item item_icon_wrap">{{ item.orgName }}</span>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" :content="item.jobName" placement="top">
        <span class="item item_icon_wrap">{{ item.jobName }}</span>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" :content="item.jobLevelName" placement="top">
        <span class="item item_icon_wrap">{{ item.jobLevelName }}</span>
      </el-tooltip>
      <el-select class="item" v-model="item.recruitmentReason" placeholder="选择招募原因">
        <el-option
          v-for="item in REASONS_RECRUITMENT"
          :key="item.dictCode"
          :label="item.codeName"
          :value="item.dictCode"
        ></el-option>
      </el-select>
      <div class="item item_icon_wrap">{{ item.staffCount }}</div>
      <el-input
        class="item"
        v-model="item.budgetedCount"
        @input="value => (item.budgetedCount = value.replace(/^(0-)|[^\d]+/g, ''))"
        placeholder="填写建议编制"
      ></el-input>
      <!-- <div class="item item_icon_wrap">{{item.shortageCount}}</div> -->
      <!-- 空缺人数=编制-现有人员 -->
      <div class="item item_icon_wrap">
        {{ item.budgetedCount && item.staffCount ? item.budgetedCount - item.staffCount : item.shortageCount }}
      </div>
      <!-- oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" -->
      <el-input
        class="item"
        v-model="item.recruitCount"
        @input="value => (item.recruitCount = value.replace(/^(0-)|[^\d]+/g, ''))"
        placeholder="填写当前需求人数"
      ></el-input>
      <el-select class="item" v-model="item.recruitEmergency" placeholder="选择紧急程度">
        <el-option
          v-for="item in EMERGENCY"
          :key="item.dictCode"
          :label="item.codeName"
          :value="item.dictCode"
        ></el-option>
      </el-select>
      <el-date-picker
        class="item"
        value-format="YYYY-MM-DD"
        v-model="item.expectArrivalDate"
        type="date"
        placeholder="选择日期"
      ></el-date-picker>
      <!-- <el-select class="item" v-model="item.recruitExpectDeadline" placeholder="选择期望到岗周期">
        <el-option
          v-for="item in RECRUIT_EXPECT_DEADLINE"
          :label="item.codeName"
          :value="item.dictCode"
        ></el-option>
      </el-select>-->
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { getDictList } from '@/views/entp/request/api'
const props = defineProps({
  eduInfoData: {
    type: Array,
    default: () => [{}]
  }
})

const userStore = useUserStore()

// 响应式状态
const EMERGENCY = ref([])
const REASONS_RECRUITMENT = ref([])
const RECRUIT_EXPECT_DEADLINE = ref([])

// 生命周期钩子
onMounted(async () => {
  try {
    const docList = await userStore.getDocList(['REASONS_RECRUITMENT', 'EMERGENCY', 'RECRUIT_EXPECT_DEADLINE'])
    console.log(docList)

    REASONS_RECRUITMENT.value = docList.REASONS_RECRUITMENT
    EMERGENCY.value = docList.EMERGENCY
    RECRUIT_EXPECT_DEADLINE.value = docList.RECRUIT_EXPECT_DEADLINE
  } catch (error) {
    console.error(error)
  }
})
</script>

<style scoped lang="scss">
.edu_info_item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  padding: 8px 16px;

  .item {
    width: 11%;
    &.index {
      width: 4%;
      text-align: center;
    }
  }

  .item_icon_wrap {
    text-align: center;
    width: 8%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;

    .item_icon {
      font-size: 20px;
      color: #0099fd;
      cursor: pointer;
    }
  }
  :deep(.el-input__prefix),
  :deep(.el-input__suffix) {
    height: 23px !important;
  }
  :deep(.el-date-editor.el-input) {
    width: 11%;
    // width: auto;
  }
}
</style>
