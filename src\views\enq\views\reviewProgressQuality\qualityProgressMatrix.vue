<template>
  <div class="quality_progress_matrix_wrap">
    <div class="page_main_title">质量进度矩阵</div>
    <div class="page_section review_progress_manage_center clearfix">
      <div class="training_activities_center clearfix">
        <div class="training_activities_item flex_row_between" v-for="(item, index) in listData" :key="item.id">
          <!-- <div class="progress_state">进行中</div> -->
          <div class="item_index" v-if="(index < 8 || index == 8) && page.current == 1">0{{ index + 1 }}</div>
          <div class="item_index" v-if="index > 8 && page.current == 1">{{ index + 1 }}</div>
          <div class="item_index" v-if="page.current > 1">{{ index + 1 + (page.current - 1) * page.size }}</div>
          <div class="item_content_wrap">
            <div class="item_content flex_row_between">
              <div class="item_content_list item_info">
                <div class="list_title">项目名称</div>
                <div class="list_text">
                  <p class="item_pro_name overflow_elps" :title="item.projectName">{{ item.enqName }}</p>
                </div>
              </div>
              <div class="item_content_list item_date_wrap">
                <div class="list_title">起止日期</div>
                <div class="list_text">
                  <p class="list_num">{{ item.beginDate }}</p>
                  <p class="list_num">{{ item.endDate }}</p>
                </div>
              </div>
              <!-- <div class="item_content_list check_progress_wrap">
                                <div class="list_title">盘点进度</div>
                                <div class="list_text">
                                    <div class="item_pro_name flex_row_between">
                                        <div class="check_pro flex_row_between">
                                            <span class="overflow_elps" :title="item.completed">{{item.completed}}</span>
                                            <p>提交</p>   
                                        </div>
                                        <div class="check_pro">/</div>
                                        <div class="check_pro flex_row_between">
                                            <span class="overflow_elps" :title="item.total">{{item.total}}</span>
                                            <p>总数</p>   
                                        </div>
                                    </div>
                                </div>
                            </div> -->
            </div>
          </div>
          <!-- <div class="item_content_wrap progress_details">
                        <div class="item_content  flex_row_around">
                            <div class="item_content_list">
                                <div class="list_title">完成度</div>
                                <div class="list_text progress_details_item flex_row_start">
                                    <span class="overflow_elps" :title="item.completionRate">{{item.completionRate}}</span>
                                    <p >%</p>
                                </div>
                            </div>
                            <div class="item_content_list">
                                <div class="list_title">完善中</div>
                                <div class="list_text progress_details_item flex_row_start">
                                    <span class="overflow_elps" :title="item.perfect">{{item.perfect}}</span>
                                    <p>人</p>
                                </div>
                            </div>
                            <div class="item_content_list">
                                <div class="list_title">未开始</div>
                                <div class="list_text progress_details_item flex_row_start">
                                    <span class="overflow_elps" :title="item.notStart">{{item.notStart}}</span>
                                    <p>人</p>
                                </div>
                            </div>
                            <div class="item_content_list">
                                <div class="list_title">未登录</div>
                                <div class="list_text progress_details_item flex_row_start">
                                    <span class="overflow_elps" :title="item.unLogin">{{item.unLogin}}</span>
                                    <p>人</p>
                                </div>
                            </div>
                        </div>
                    </div> -->
          <div class="item_oper flex_row_start">
            <div class="item_oper_list" @click="progressInfo(item.enqId, item.enqName)">
              <i class="icon"></i>
              <div class="text">质量进度</div>
            </div>
          </div>
        </div>
        <div class="pagination_wrap">
          <el-pagination
            :page-sizes="[10, 20, 50, 100]"
            @size-change="handleSizeChange"
            :current-page="page.current"
            :page-size="page.size"
            @current-change="handleCurrentChange"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { getScheduleList } from '../../request/api'

const router = useRouter()
const userStore = useUserStore()

const listData = ref([])
const page = reactive({
  current: 1,
  size: 10,
  total: 0
})

const companyId = computed(() => userStore.userInfo.companyId)

const progressInfo = (val, name) => {
  router.push({
    path: '/talentReviewHome/reviewProgressQuality/qualityMatrix/qualityMatrixDetails',
    query: {
      enqId: val,
      enqName: name
    }
  })
}

const handleSizeChange = val => {
  page.size = val
  getScheduleListFun()
}

const handleCurrentChange = val => {
  page.current = val
  getScheduleListFun()
}

const getScheduleListFun = async () => {
  const res = await getScheduleList({
    companyId: companyId.value,
    current: page.current,
    size: page.size
  })
  page.total = res.total
  listData.value = res.data.map(item => ({
    enqName: item.enqName,
    beginDate: item.beginDate ? item.beginDate.split(' ')[0] : '',
    endDate: item.endDate ? item.endDate.split(' ')[0] : '',
    completed: item.completed,
    total: item.total,
    completionRate: item.completionRate > 0 ? item.completionRate * 100 : item.completionRate,
    perfect: item.perfect,
    notStart: item.notStart,
    unLogin: item.unLogin,
    enqId: item.enqId
  }))
}

watch(
  companyId,
  val => {
    if (val) {
      getScheduleListFun()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.quality_progress_matrix_wrap {
  .review_progress_manage_center {
    .training_activities_center {
      .training_activities_item {
        position: relative;
        height: 120px;
        padding: 12px 30px 12px 30px;
        margin-bottom: 8px;
        border: 1px solid #e5e5e5;
        overflow: hidden;
        .progress_state {
          width: 100px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          position: absolute;
          background: #90d99e;
          transform: rotate(-45deg);
          transform-origin: center;
          font-size: 10px;
          color: #fff;
          left: 0;
          left: -28px;
          top: 7px;
        }
        .item_index {
          width: 100px;
          text-align: center;
          font-weight: bold;
          font-size: 20px;
          color: #0099ff;
          // background: pink;
        }
        .item_content_wrap {
          // background: yellow;
          width: 65%;
          padding: 0 8px;

          .item_content {
            padding-right: 10px;
            .item_content_list {
              color: #212121;
              .list_title {
                font-weight: bolder;
                margin-bottom: 10px;
              }
              .list_text {
                height: 38px;
                .item_pro_name {
                  line-height: 38px;
                  .check_pro {
                    height: 38px;
                    line-height: 38px;
                    margin: 0 5px 0 0;
                    span {
                      display: inline-block;
                      font-weight: bold;
                      color: #0099ff;
                      font-size: 16px;
                      padding-right: 5px;
                    }
                  }
                }
              }
              .progress_details_item {
                text-align: center;
                // height: 38px;
                line-height: 38px;
                span {
                  width: 23px;
                  text-align: right;
                  font-weight: bold;
                  color: #0099ff;
                  font-size: 16px;
                }
                p {
                }
              }
              .list_num {
                font-style: normal;
              }
            }
            .check_progress_wrap {
            }
            .item_info {
              width: 580px;
            }
            .item_date_wrap {
              text-align: center;
              .list_title {
                width: 200px;
              }
            }
          }
        }
        .progress_details {
          padding: 14px 0 0 0;
          width: 30%;
          background: #ebf4ff;
          height: 96px;
          border-radius: 5px;
        }
        .item_oper {
          width: 100px;
          // padding: 0 8px;
          text-align: center;
          .icon {
            display: inline-block;
            width: 30px;
            height: 33px;
            background: url('../../../../../public/images/pro_mang_icon.png') no-repeat center;
            background-size: 100% 100%;
          }
          &_list {
            width: 100%;
            cursor: pointer;
          }
        }
      }
      .training_activities_item:hover {
        // background: #EBF4FF;
        // cursor: pointer;
      }
    }
  }
}
</style>
