<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">人员工作年限</div>
          <div class="content_item_content" id="job_avatar"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">各族群平均工作年限</div>
          <div class="content_item_content" id="job_class"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title">各职层的工作年限</div>
          <div class="content_item_content" id="job_level"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            岗位职层明细
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { workAgeStructure, workingYearsList, exportData } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')

const jobClass = reactive({
  data: []
})

const jobLevel = reactive({
  data: []
})

const average = reactive({
  data: []
})

const filterData = ref([])
const page = ref(1)
const size = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '一级组织',
      prop: 'onLevelName'
    },
    {
      label: '二级组织',
      prop: 'twoLevelName'
    },
    {
      label: '三级组织',
      prop: 'threeLevelName'
    },
    {
      label: '四级组织',
      prop: 'fourLevelName'
    },
    {
      label: '姓名',
      prop: 'user_name'
    },
    {
      label: '岗位族群',
      prop: 'parent_job_class_name'
    },
    {
      label: '岗位序列',
      prop: 'job_class_name'
    },
    {
      label: '岗位名称',
      prop: 'post_name'
    },
    {
      label: '职层',
      prop: 'job_level_name'
    },
    {
      label: '职等',
      prop: 'job_grade_name',
      width: 60
    },
    {
      label: '工作年限',
      prop: 'value',
      width: 120
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('job_avatar', 'YBar', '350', '250', average)
  echartsRenderPage('job_class', 'YBar', '350', '250', jobClass)
  echartsRenderPage('job_level', 'XBar', '700', '250', jobLevel)
}

const workAgeStructureFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await workAgeStructure(params)
    if (res.code == 200) {
      const data = res.data
      jobLevel.data = data.jobLevel
      jobClass.data = data.jobClass
      average.data = data.average
      initChart()
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  page.value = 1
  workAgeStructureFun()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await workingYearsList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error(error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'j'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '岗位职层分布明细')
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  workAgeStructureFun()
  getTableData()
})
</script>

<style scoped lang="scss"></style>
