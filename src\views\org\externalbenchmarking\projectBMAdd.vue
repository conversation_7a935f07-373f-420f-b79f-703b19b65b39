<script setup>
import Steps from '../components/steps.vue'
const projectBMlist = ref([])
const stepsData = ref([
  {
    name: '选择对标主题',
    finshSign: false,
    code: 1
  },
  {
    name: '确认对标行业',
    finshSign: false,
    code: 2
  },
  {
    name: '完善对标数据',
    finshSign: false,
    startSign: false,
    code: 3
  },
  {
    name: '查看对标报告',
    finshSign: false,
    startSign: false,
    code: 4
  }
])
const stepsNum = ref(1)
const nrdb = ref([
  {
    title: '人力资本回报率（HCROI）对标：',
    info: '通过量化单位人力成本创造的利润或营收，直观反映薪酬投入与价值产出的匹配程度。企业可据此识别高成本低效能岗位，针对性优化薪酬结构、提升核心岗位效能，在控制人力成本的同时挖掘人效增长潜力，实现 “降本” 与 “增效” 的双向平衡。 '
  },
  {
    title: '人均产能增长对标：',
    info: '聚焦人均营收 / 利润的年均增速，衡量组织扩张中的效率提升能力。若增速滞后于行业，企业可据此排查流程冗余、技能错配等问题，精准制定人员结构优化、自动化升级或产能规划策略，确保业务规模扩张与人力资源效率提升同步，避免 “规模不经济”。 '
  },
  {
    title: '人才结构健康度对标：',
    info: '从年龄、学历、职级等维度评估人才梯队合理性，防范关键岗位断层或结构性冗余。企业可针对性制定校园招聘、内部培养或跨界引进计划，填补技术研发、管理储备等领域的人才缺口，确保人才结构与战略目标动态匹配，避免因 “人力断层” 导致的发展瓶颈。 '
  },
  {
    title: '人力成本对标：',
    info: '通过薪酬费用占营收的比例，评估人力成本投入的合理性。若成本占比过高，企业可通过自动化改造、工时优化等手段控本；若显著低于行业均值，则需警惕人才投入不足对长期效能的影响，平衡短期成本控制与长期人才储备，保障组织持续发展能力。'
  },
  {
    title: '员工人均薪酬对标：',
    info: '通过对比员工平均薪酬与行业水平，评估企业薪酬竞争力。薪酬显著低于市场时，可及时调整策略以吸引和保留人才；若薪酬高于行业却伴随人效低下，则需审视薪酬与绩效挂钩机制，推动薪酬资源向高价值岗位倾斜，在保障市场吸引力的同时避免成本浪费。 '
  },
  {
    title: '薪酬与业绩匹配度对标：',
    info: '聚焦核心岗位薪酬与其业绩贡献的偏离度，避免 “高薪低效” 或激励不足。企业可据此优化绩效权重、实施差异化激励，使薪酬与关键指标（如利润、产能、技术突破）强关联，激发核心人才创造力，确保薪酬投入切实转化为业务增长动力。'
  }
])
const dbzt = ref([
  {
    name: '人力资源效能',
    code: 1
  },
  {
    name: '财务健康度',
    code: 2
  },
  {
    name: '运营效率',
    code: 3
  },
  {
    name: '研创新发投入与产出',
    code: 4
  },

  {
    name: '行业增长与市场竞争',
    code: 5
  },
  {
    name: '供应链管理对标',
    code: 6
  }
])
const menuCheckSign = ref(1)
// step2
const topTabList = ref([
  {
    name: '按行业名称检索',
    code: 1
  },
  {
    name: '按典型公司名称检索',
    code: 2
  }
])

const topTabChekcSign = ref(2)
const keyWord = ref()
const keyWord2 = ref()
const options = ref([
  {
    value: 'guide',
    label: 'Guide',
    children: [
      {
        value: 'disciplines',
        label: 'Disciplines',
        children: [
          {
            value: 'consistency',
            label: 'Consistency'
          },
          {
            value: 'feedback',
            label: 'Feedback'
          },
          {
            value: 'efficiency',
            label: 'Efficiency'
          },
          {
            value: 'controllability',
            label: 'Controllability'
          }
        ]
      },
      {
        value: 'navigation',
        label: 'Navigation',
        children: [
          {
            value: 'side nav',
            label: 'Side Navigation'
          },
          {
            value: 'top nav',
            label: 'Top Navigation'
          }
        ]
      }
    ]
  }
])

const checkCompanyI = ref('')
const checkIndustryCode = ref()
const checkIndustry = ref()
const radio1 = ref()
const steps2DataList = ref([
  {
    companyName: '1',
    companyId: 1,
    industryName: 1,
    industryCode: 'A',
    industryName2: 2,
    industryCode2: 'B'
  },
  {
    companyName: '1',
    companyId: 1,
    industryName: 1,
    industryCode: 'A',
    industryName2: 2,
    industryCode2: 'B'
  },
  {
    companyName: '1',
    companyId: 1,
    industryName: 1,
    industryCode: 'A',
    industryName2: 2,
    industryCode2: 'B'
  },
  {
    companyName: '1',
    companyId: 1,
    industryName: 1,
    industryCode: 'A',
    industryName2: 2,
    industryCode2: 'B'
  },
  {
    companyName: '1',
    companyId: 1,
    industryName: 1,
    industryCode: 'A',
    industryName2: 2,
    industryCode2: 'B'
  },
  {
    companyName: '1',
    companyId: 1,
    industryName: 1,
    industryCode: 'A',
    industryName2: 2,
    industryCode2: 'B'
  }
])

// step3
const topTab2List = ref([
  {
    name: '经营业绩类信息',
    code: 1
  },
  {
    name: '成本类信息',
    code: 2
  },
  {
    name: '人员类信息',
    code: 3
  }
])

const topTab2ChekcSign = ref(1)
const form = reactive({
  n: '',
  n1: '',
  n2: '',
  n3: '',
  n4: '',
  n5: ''
})

// step4
const operateBtn = c => {
  let maxArea = stepsData.value.length
  if (c == 'N' && stepsNum.value < maxArea) {
    stepsNum.value++
  } else if (c == 'P' && stepsNum.value > 1) {
    stepsNum.value--
  }
}
const topTabChekc = c => {
  topTabChekcSign.value = c
}
const topTab2Chekc = c => {
  topTab2ChekcSign.value = c
}
const handelChange = c => {
  keyWord.value = c
}
const checkCI = (e, o) => {
  checkCompanyI.value = e
  if (o == 'F') {
    checkIndustry.value = e.industryName
    checkIndustryCode.value = e.industryCode
  } else {
    checkIndustry.value = e.industryName + '/' + e.industryName2
    checkIndustryCode.value = e.industryCode2
  }
}
</script>
<template>
  <div class="projectBM_add_wrap">
    <div class="page-title-line">新增对标项目</div>
    <div class="projectBM_main_wrap section_box_wrap">
      <div class="s_wrap">
        <Steps :stepsData="stepsData" :stepsNum="stepsNum" :verticalSign="true"></Steps>
      </div>
      <div class="m_steps_wrap steps_01_wrap" v-if="stepsNum == 1">
        <div class="title"><span class="icon"></span>对标主题</div>
        <div class="steps_01_main justify-between">
          <div class="menu_wrap section_box_wrap">
            <div class="item_wrap" :class="{ act: menuCheckSign == it.code }" v-for="it in dbzt">
              <span class="text">{{ it.name }}</span>
              <span class="icon"></span>
            </div>
          </div>
          <div class="r_main_wrap">
            <div class="section_box_wrap">
              <div class="t">基本概念</div>
              人力资源效能对标是指企业将自身的人力资源效率相关指标与同行业其他企业、行业标杆企业或不同行业但具有类似业务模式的优秀企业进行对比，以明确自身在人力资源利用效率方面的水平、优势与不足，进而采取针对性措施加以改进和优化，提高人力资源价值创造能力的管理活动。
            </div>
            <div class="section_box_wrap dot_content_wrap">
              <div class="t">对标内容</div>
              <div class="item_wrap" v-for="item in nrdb">
                <span class="icon"></span>
                <span class="title">{{ item.title }}</span>
                <span class="info">{{ item.info }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="justify-end">
          <div class="btn">加入对标模块</div>
        </div>
        <div class="btn_wrap justify-between">
          <div class="bnt next_btn" @click="operateBtn('N')">下一步</div>
        </div>
      </div>
      <div class="m_steps_wrap steps_02_wrap" v-if="stepsNum == 2">
        <div class="title">
          <span class="icon"></span>对标行业
          <span class="tips">您可以用两种方式确认行业</span>
        </div>

        <div class="top_tab justify-start">
          <div
            class="item_wrap"
            :class="{ top_tab_act: topTabChekcSign == it.code }"
            v-for="it in topTabList"
            @click="topTabChekc(it.code)"
          >
            <span class="bot_line"></span>
            {{ it.name }}
          </div>
        </div>
        <div class="" v-if="topTabChekcSign == 1">
          <div class="search_input_wrap justify-start">
            <el-cascader placeholder="请输入行业名称" :options="options" @change="handelChange" clearable filterable>
            </el-cascader>
            <div class="btn">查询</div>
          </div>
        </div>
        <div class="" v-if="topTabChekcSign == 2">
          <div class="search_input_wrap">
            <el-input v-model="keyWord2" placeholder="请输入公司名称">
              <template #append>查询</template>
            </el-input>
          </div>
          <div class="item_list_wrap">
            <el-radio-group v-model="radio1">
              <div class="f_center">
                <div class="item_list_main justify-start">
                  <div class="company_item" v-for="item in steps2DataList" :key="item.companyId">
                    <div class="company_info">
                      <div class="company_name">1{{ item.companyName }}</div>
                      <div class="industry_involved justify-between">
                        <div class="type">所属行业：</div>
                        <div class="option_list_wrap">
                          <div class="option_item">
                            <span class="industry_name">
                              {{ item.industryName }}
                            </span>
                            <el-radio :value="item.industryCode" @click="checkCI(item, 'F')" />
                            <!-- <el-radio
                            @click="checkCI(item, 'F')"
                            :value="item.industryCode"
                            :checked="
                              checkCompanyI.companyId == item.companyId &&
                              item.industryCode == checkIndustryCode
                            "
                            activeBackgroundColor="#53B8FF"
                          /> -->
                          </div>
                          <div class="option_item second_option_item">
                            <span class="industry_name">{{ item.industryName2 }}</span>
                            <el-radio :value="item.industryCode" @click="checkCI(item, 'S')" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-radio-group>
          </div>
        </div>

        <div class="btn_wrap justify-between">
          <div class="bnt pre_btn" @click="operateBtn('P')">上一步</div>
          <div class="bnt next_btn" @click="operateBtn('N')">下一步</div>
        </div>
      </div>
      <div class="m_steps_wrap steps_03_wrap" v-if="stepsNum == 3">
        <div class="title"><span class="icon"></span>完善对标数据</div>
        <div class="tips">
          请如实维护如下信息，若您仅想查看目标行业的效能分析，不做企业对标，下列信息也可直接跳过，报告中将不再显示先关对标差距与企业效能日和提升改善的内容
        </div>
        <div class="tab_steps_03_wrap tab_bottom_long_line_wrap">
          <div
            class="item_wrap"
            :class="{ top_tab_act: topTab2ChekcSign == it.code }"
            v-for="it in topTab2List"
            @click="topTab2Chekc(it.code)"
          >
            <span class="bot_line"></span>
            {{ it.name }}
          </div>
        </div>
        <el-form :model="form">
          <div class="form_item_wrap">
            <div class="ti">营业收入</div>
            <div class="i_tips">
              指企业在一定时期内，通过从事销售商品、提供劳务、让渡资产使用权等各种经营活动所取得的收入总和，一般企业：营业总收入
              = 主营业务收入 + 其他业务收入，金融企业：营业总收入 = 营业收入 + 利息收入 + 已赚保费 + 手续费及佣金收入
            </div>
            <div class="f_line_w justify-between">
              <div class="i_l_w">
                <el-form-item label="2023年（单位：万元）">
                  <el-input v-model="form.n" />
                </el-form-item>
              </div>
              <div class="i_l_w">
                <el-form-item label="2024年（单位：万元）">
                  <el-input v-model="form.n1" />
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="form_item_wrap">
            <div class="ti">营业利润</div>
            <div class="i_tips">
              营业利润是指企业从事生产经营活动中取得的利润，是企业利润的主要来源，营业利润 = 营业收入 - 营业成本 -
              税金及附加 - 期间费用（销售/管理/研发/财务费用） + 其他收益。
            </div>
            <div class="f_line_w justify-between">
              <div class="i_l_w">
                <el-form-item label="2023年（单位：万元）">
                  <el-input v-model="form.n2" />
                </el-form-item>
              </div>
              <div class="i_l_w">
                <el-form-item label="2024年（单位：万元）">
                  <el-input v-model="form.n3" />
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="form_item_wrap">
            <div class="ti">利润总额</div>
            <div class="i_tips">
              利润总额是企业在一定时期内通过生产经营活动所实现的最终财务成果，是衡量企业经营业绩的重要经济指标；利润总额
              = 营业利润 + 营业外收入 - 营业外支出。
            </div>
            <div class="f_line_w justify-between">
              <div class="i_l_w">
                <el-form-item label="2023年（单位：万元）">
                  <el-input v-model="form.n4" />
                </el-form-item>
              </div>
              <div class="i_l_w">
                <el-form-item label="2024年（单位：万元）">
                  <el-input v-model="form.n5" />
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>

        <div class="btn_wrap justify-between">
          <div class="bnt pre_btn" @click="operateBtn('P')">上一步</div>
          <div class="bnt next_btn" @click="operateBtn('N')">下一步</div>
        </div>
      </div>
      <div class="m_steps_wrap steps_04_wrap" v-if="stepsNum == 4">
        <div class="img_02"></div>
        <div class="tips">
          <span class="">恭喜您完成 </span>
          <span class="blue">人效对标（20250225）</span>
        </div>
        <div class="step4_btn justify-between">
          <div class="bnt">查看概要报告</div>
          <div class="bnt blue">下载全量报告</div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../style/common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-end {
  display: flex;
  justify-content: flex-end;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.projectBM_add_wrap {
  .top_t_wrap {
    margin-bottom: 10px;
    align-items: center;
    .page-title-line {
      margin: 3px 0 0 0;
    }
  }
  .projectBM_main_wrap {
    background: #fff;
    .s_wrap {
      width: 60%;
      margin: 50px auto 40px;
    }
    .m_steps_wrap {
      .title {
        margin-bottom: 16px;
        .icon {
          display: inline-block;
          margin-right: 10px;
          width: 16px;
          height: 12px;
          background: url('@/assets/imgs/org/icon_03.png') no-repeat center;
          background-size: 100% 100%;
        }
        .tips {
          padding: 4px 10px;
          font-size: 14px;
          color: #6ab5ff;
          background: #e2f0ff;
          border-radius: 4px 4px 4px 4px;
        }
      }
    }
    .steps_01_wrap {
      .steps_01_main {
        .menu_wrap {
          padding: 20px 10px;
          margin-right: 20px;
          width: 320px;
          background: #fff;
          .item_wrap {
            padding: 0 10px;
            margin-bottom: 10px;
            height: 36px;
            line-height: 36px;
            background: #f3f3f3;
            border-radius: 5px 5px 5px 5px;
            font-size: 14px;
            span {
              display: inline-block;
            }
            .text {
              width: 90%;
              white-space: nowrap;
            }
            .icon {
              width: 14px;
              height: 14px;
              background: url('@/assets/imgs/org/icon_05.png') no-repeat center;
              background-size: 100% 100%;
            }
            &.act {
              background: #f0f9ff;
              .icon {
                background: url('@/assets/imgs/org/icon_04.png') no-repeat center;
                background-size: 100% 100%;
              }
            }
          }
        }
        .r_main_wrap {
          .section_box_wrap {
            background: #fff;
          }
        }
      }
    }
    .steps_02_wrap {
      .top_tab {
        justify-content: center;
        .item_wrap {
          padding: 0 12px;
          position: relative;
          color: #666666;
          cursor: pointer;
          border-right: 1px solid #d8d8d8;
          .bot_line {
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: 30px;
            height: 4px;
            background: transparent;
            border-radius: 2px 2px 2px 2px;
          }
        }
        .item_wrap:last-child {
          border-right: 1px solid transparent;
        }
        .top_tab_act {
          color: #333;
          font-weight: 600;
          .bot_line {
            background: #40a0ff;
          }
        }
      }
      .search_input_wrap {
        width: 460px;
        margin: 30px auto;
        :deep .el-cascader {
          width: 372px;
          .el-input__wrapper {
            height: 36px;
            border-right-color: transparent;
            border-radius: 4px 0px 0px 4px;
          }
        }
        :deep .el-input {
          .el-input-group__append {
            border-color: #40a0ff;
            background: #40a0ff;
            color: #fff;
            cursor: pointer;
          }
        }
        .btn {
          width: 88px;
          height: 36px;
          background: #40a0ff;
          border-radius: 0px 4px 4px 0px;
        }
      }
      .item_list_wrap {
        // width: 100%;

        :deep .el-radio-group {
          color: #333;
          display: block;
        }
        .f_center {
        }
        .item_list_main {
          margin: 0 50px;
          flex-wrap: wrap;
        }
        .company_item {
          padding: 15px;
          margin: 0 10px 20px;
          width: 23.5%;
          background: #ffffff;
          border-radius: 6px 6px 6px 6px;
          border: 1px solid #c6dbf3;
          .company_info {
            .company_name {
              margin-bottom: 5px;
              font-size: 16px;
            }
            .industry_involved {
              .type {
                width: 70px;
                font-size: 14px;
              }
              .option_list_wrap {
                flex: 1;
                .option_item {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 0 10px 0 20px;
                  margin-bottom: 14px;
                  height: 32px;
                  line-height: 32px;
                  background: #e6eff6;
                  border-radius: 8px 8px 8px 8px;
                  .industry_name {
                    display: inline-block;
                    color: #53b8ff;
                    font-size: 14px;
                  }
                }
                .second_option_item {
                  margin-left: 20px;
                  .industry_name {
                  }
                }
              }
            }
          }
        }
      }
    }
    .steps_03_wrap {
      .tips {
        padding: 0 15px;
        width: 1360px;
        height: 35px;
        line-height: 35px;
        color: #40a0ff;
        background: #eff4f9;
        border-radius: 5px 5px 5px 5px;
      }
      .tab_steps_03_wrap {
        margin: 30px 0 0;
      }
      .form_item_wrap {
        padding: 30px 0 0;
        border-bottom: 1px solid #d8d8d8;
        .ti {
        }
        .i_tips {
          margin: 15px 0 25px;
          font-size: 14px;
          color: #40a0ff;
        }
        .f_line_w {
          .i_l_w {
            width: 50%;
            :deep .el-form-item__label {
              font-size: 16px;
              color: #3d3d3d;
            }
            :deep .el-input {
              width: 400px;
            }
          }
        }
      }
    }
    .steps_04_wrap {
      margin: 0 auto;
      .img_02 {
        margin: 130px auto 0;
        width: 200px;
        height: 200px;
        background: url('@/assets/imgs/org/img_02.png') no-repeat center;
        background-size: 100% 100%;
      }
      .tips {
        margin: 50px auto 40px;
        text-align: center;
        font-size: 24px;
        .blue {
          color: #40a0ff;
        }
      }
      .step4_btn {
        margin: 0 auto;
        width: 744px;
        .bnt {
          width: 350px;
          height: 50px;
          line-height: 50px;
          text-align: center;
          background: #ffffff;
          border-radius: 93px 93px 93px 93px;
          border: 1px solid #40a0ff;
          cursor: pointer;
        }
        .blue {
          background: #40a0ff;
          color: #fff;
        }
      }
    }

    .btn_wrap {
      margin: 40px auto 0;
      width: 260px;
      .bnt {
        width: 120px;
        height: 36px;
        border-radius: 6px 6px 6px 6px;
        line-height: 36px;
        color: #40a0ff;
        text-align: center;
        border: 1px solid #40a0ff;
        cursor: pointer;
      }
      .pre_btn {
      }
      .next_btn {
        background: #40a0ff;
        color: #fff;
      }
    }
  }
}
</style>
