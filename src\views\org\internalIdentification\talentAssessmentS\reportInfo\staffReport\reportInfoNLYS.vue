<script setup>
import Table from "@/components/table/simplenessTable.vue";
const emits = defineEmits(["closeReportInfo"]);
// 2
const columns = ref([
  {
    label: "一级能力",
    prop: "a",
  },
  {
    label: "二级能力",
    prop: "b",
  },
  {
    label: "综合得分",
    prop: "d",
    slot: "dSlot",
  },

  {
    label: "级别",
    prop: "e",
  },
]);
const data = ref([
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "61.1",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "58.6",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "41.1",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);

const columns2 = ref([
  {
    label: "能力组件群",
    prop: "a",
  },
  {
    label: "能力组件",
    prop: "b",
  },
  {
    label: "综合得分",
    prop: "d",
    slot: "dSlot",
  },

  {
    label: "级别",
    prop: "e",
  },
]);
const data2 = ref([
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "61.1",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "58.6",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "41.1",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);

const value = ref(40);

const marksStyle = ref({
  fontSize: "12px",
  color: "#40A0FF",
  width: "38px",
  height: "18px",
  lineHeight: "18px",
  background: "#D1E5FA",
  borderRadius: "93px 93px 93px 93px",
  textAlign: "center",
});
const marks = ref({
  0: {
    style: marksStyle.value,
    label: "基本",
  },
  50: {
    style: marksStyle.value,
    label: "平均",
  },
  100: {
    style: marksStyle.value,
    label: "领先",
  },
});

const closeInfo = (i) => {
  emits("closeReportInfo", true);
};

const circleColor = (v, o) => {
  if (o == "g") {
    if (v < 50) {
      return "green_bg2";
    } else if (v >= 50) {
      return "green_bg1";
    }
  } else if (o == "r") {
    if (v < 50) {
      return "red_bg2";
    } else if (v >= 50) {
      return "red_bg1";
    }
  }
};
onMounted(() => {});
</script>
<template>
  <div class="com_right_wrap right2_wrap">
    <div class="t">能力优势与短板</div>
    <div class="info_section_wrap">
      <div class="section_title justify-between">
        <div class="page-title-line">相对较好的能力</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>

      <Table
        :roundBorder="false"
        :columns="columns"
        :data="data"
        headerColor
        showIndex
      >
        <template v-slot:dSlot="scope">
          <span class="circle" :class="circleColor(scope.row.d, 'g')">{{
            scope.row.d
          }}</span>
        </template>
        <template #default="scope">
          <el-table-column
            class=""
            width="420px"
            align="center"
            label="所有参评人员中的位置"
          >
            <el-slider v-model="value" :marks="marks" disabled />
          </el-table-column>
        </template>
      </Table>
      <div class="desc_title">可进一步发挥优势能力（采购招投标）</div>
      <div class="section_box_wrap">
        1、担任内部讲师：面向团队培训招投标相关的法规政策、流程规范与实操技巧，剖析典型案例，提升团队整体专业素养与风险防范意识；​<br />
        2、主导流程优化：梳理现有招投标管理流程，识别冗余环节与潜在漏洞，引入数字化管理工具，制定标准化操作手册，提升招标效率与管理规范性；​<br />
        3、建立风控体系：牵头搭建招投标风险预警机制，制定风险评估指标，定期开展合规审查与风险排查，降低违规操作与法律纠纷风险；​<br />
        4、统筹资源协调：协调采购、法务、技术等多部门协作，统一需求标准，整合评审资源，推动跨部门高效沟通，保障招标项目顺利推进；​<br />
        5、优化供应商管理：制定供应商分级管理制度，建立动态评估与淘汰机制，主导供应商谈判与合同条款审核，强化供应链稳定性与合作价值。<br />
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="section_title justify-between">
        <div class="page-title-line">相对较差的能力</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <Table
        :roundBorder="false"
        :columns="columns2"
        :data="data2"
        headerColor
        showIndex
      >
        <template v-slot:dSlot="scope">
          <span class="circle" :class="circleColor(scope.row.d, 'r')">{{
            scope.row.d
          }}</span>
        </template>
        <template #default="scope">
          <el-table-column
            class=""
            width="420px"
            align="center"
            label="在全部参评人员中的位置"
          >
            <el-slider
              class="red_el-slider"
              v-model="value"
              :marks="marks"
              disabled
            />
          </el-table-column>
        </template>
      </Table>
      <div class="desc_title">需要采取的措施（供应商培育）</div>
      <div class="section_box_wrap">
        1、开展专项知识学习：制定个人学习计划，系统学习供应商管理、供应链优化等专业课程，研读行业法规政策与标杆企业案例，补足基础理论知识短板；​​<br />
        2、使用辅助工具提效：掌握供应商关系管理系统及数据分析软件，通过工具自动生成供应商评估报告、风险预警提示，减少因经验不足导致管理疏漏；​​<br />
        3、建立经验复盘机制：每次供应商培育工作结束后，梳理流程中的问题与亮点，形成个人工作笔记，定期与团队分享并请教资深同事，加速经验积累；​​<br />
        4、申请导师帮扶指导：主动向公司内供应商管理经验丰富的同事申请一对一指导，在项目执行中跟随导师学习谈判技巧、合同审核要点，针对性提升实操能力；​​<br />
        5、参与行业交流活动：积极参加供应商管理研讨会、行业论坛等活动，与同行交流前沿理念与实践方法，拓宽视野，借鉴外部优秀经验优化自身工作。​​<br />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
