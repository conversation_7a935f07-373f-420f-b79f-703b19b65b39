import axios from 'axios'
import errorCode from '@/utils/errorCode'
import { useUserStore } from '@/stores/modules/user'
import { isArray } from '@/utils'
const service = axios.create({
  baseURL: import.meta.env.VITE_API_PREFIX,
  timeout: 1000000,
  withCredentials: true
})

// 创建请求取消控制器映射表
const pendingMap = new Map()

// 在请求拦截器中添加取消逻辑
service.interceptors.request.use(
  config => {
    const userStore = useUserStore()

    // 为每个请求创建独立控制器
    const controller = new AbortController()
    config.signal = controller.signal
    config.headers.Authorization = `Bearer ${userStore.token}`
    // 生成请求唯一标识
    const key = `${config.method}${config.url}${JSON.stringify(config.data)}`

    // 如果存在相同请求则取消
    // if (pendingMap.has(key)) {
    //   pendingMap.get(key).abort()
    // }

    // 存储当前控制器
    pendingMap.set(key, controller)
    config.pendingKey = key
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 在响应拦截器中清除已完成请求
service.interceptors.response.use(
  res => {
    const { config } = res
    if (config?.pendingKey) {
      pendingMap.delete(config.pendingKey)
    }
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode['default']
    // 二进制数据则直接返回
    if (res.request.responseType == 'blob' || res.request.responseType == 'arraybuffer') {
      return res.data
    }
    if (isArray(res)) {
      return Promise.resolve(res)
    }
    if (code == 500) {
      ElMessage({ message: msg, type: 'error' })
      return Promise.reject(new Error(msg))
    } else if (code == 601) {
      ElMessage({ message: msg, type: 'warning' })
      return Promise.reject(new Error(msg))
    } else if (code !== 200) {
      ElNotification.error({ title: msg })
      return Promise.reject('error')
    } else {
      return Promise.resolve(res.data)
    }
  },
  error => {
    if (error.config?.pendingKey) {
      pendingMap.delete(error.config.pendingKey)
    }
    console.log('err', error)
    let { message } = error
    if (message == 'Network Error') {
      message = '后端接口连接异常'
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时'
    } else if (message.includes('Request failed with status code')) {
      message = '系统接口' + message.substr(message.length - 3) + '异常'
    } else if (message.includes('canceled')) {
      message = '请求已取消'
    }
    // 对话接口不报错
    // if (error?.config.baseURL != '/ce-agent/api') {
    //   ElMessage({ message: message, type: 'error', duration: 5 * 1000 })
    // }
    return Promise.reject(error)
  }
)

// 导出取消方法
export const cancelRequest = key => {
  console.log(key)
  console.log(pendingMap)
  console.log(pendingMap.get(key))
  if (pendingMap.has(key)) {
    console.log(pendingMap)
    console.log(pendingMap.get(key))

    pendingMap.get(key).abort()
    pendingMap.delete(key)
  }
}

// 取消特定请求（需知道请求唯一标识）   例如：
// const key = `POST/agent/converse${JSON.stringify(payload)}`
// cancelRequest(key)
// 取消所有请求
export const cancelAllRequests = () => {
  pendingMap.forEach((controller, key) => {
    controller.abort()
    pendingMap.delete(key)
  })
}
// 取消所有请求   例如：
// cancelAllRequests()

export default service
