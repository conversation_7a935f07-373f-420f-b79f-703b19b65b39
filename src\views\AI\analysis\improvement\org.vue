<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import SimplenessTable from '@/components/table/simplenessTable.vue'

defineOptions({ name: 'org' })
const activeType = ref('整体')
const typeList = ref(['整体', '详情'])
const changeType = item => {
  activeType.value = item
}
const orgTable = ref(null)
const overviewColumns = ref([
  { prop: 'coreIndImpact', label: '核心指标影响', width: 70 },
  { prop: 'procExecImpact', label: '流程执行影响', width: 70 },
  { prop: 'decEffImpact', label: '决策效率影响', width: 70 },
  { prop: 'sysConImpact', label: '制度约束影响', width: 70 },
  { prop: 'datQuaImpact', label: '数据质量影响', width: 70 },
  { prop: 'sysSupImpact', label: '系统支撑影响', width: 70 },
  { prop: 'posDutImpact', label: '岗位履职影响', width: 70 },
  { prop: 'colEffImpact', label: '协同效率影响', width: 70 },
  { prop: 'perEffImpact', label: '人员效能影响', width: 70 },
  { prop: 'tarDraImpact', label: '目标驱动影响', width: 70 }
])
const overviewData = ref([
  {
    id: 1,
    orgName: '采购部',
    coreIndImpact: '49',
    procExecImpact: '70',
    decEffImpact: '67',
    sysConImpact: '58',
    datQuaImpact: '58',
    sysSupImpact: '65',
    posDutImpact: '46',
    colEffImpact: '65',
    perEffImpact: '45',
    tarDraImpact: '49'
  },
  {
    id: 2,
    orgName: '重庆冰箱工厂',
    coreIndImpact: '63',
    procExecImpact: '67',
    decEffImpact: '47',
    sysConImpact: '50',
    datQuaImpact: '68',
    sysSupImpact: '58',
    posDutImpact: '61',
    colEffImpact: '49',
    perEffImpact: '56',
    tarDraImpact: '65'
  },
  {
    id: 3,
    orgName: '电子商务部',
    coreIndImpact: '66',
    procExecImpact: '48',
    decEffImpact: '69',
    sysConImpact: '61',
    datQuaImpact: '69',
    sysSupImpact: '64',
    posDutImpact: '61',
    colEffImpact: '61',
    perEffImpact: '66',
    tarDraImpact: '54'
  },
  {
    id: 4,
    orgName: '欧盟区产品部',
    coreIndImpact: '50',
    procExecImpact: '45',
    decEffImpact: '63',
    sysConImpact: '53',
    datQuaImpact: '65',
    sysSupImpact: '52',
    posDutImpact: '65',
    colEffImpact: '49',
    perEffImpact: '58',
    tarDraImpact: '47'
  },
  {
    id: 5,
    orgName: '工艺部',
    coreIndImpact: '52',
    procExecImpact: '59',
    decEffImpact: '60',
    sysConImpact: '67',
    datQuaImpact: '70',
    sysSupImpact: '46',
    posDutImpact: '53',
    colEffImpact: '67',
    perEffImpact: '64',
    tarDraImpact: '62'
  },
  {
    id: 6,
    orgName: '供应链计划管理部',
    coreIndImpact: '60',
    procExecImpact: '58',
    decEffImpact: '48',
    sysConImpact: '63',
    datQuaImpact: '57',
    sysSupImpact: '52',
    posDutImpact: '68',
    colEffImpact: '67',
    perEffImpact: '50',
    tarDraImpact: '68'
  },
  {
    id: 7,
    orgName: 'GTM 部',
    coreIndImpact: '67',
    procExecImpact: '67',
    decEffImpact: '52',
    sysConImpact: '47',
    datQuaImpact: '54',
    sysSupImpact: '57',
    posDutImpact: '54',
    colEffImpact: '58',
    perEffImpact: '61',
    tarDraImpact: '69'
  },
  {
    id: 8,
    orgName: '结构研发部',
    coreIndImpact: '70',
    procExecImpact: '52',
    decEffImpact: '68',
    sysConImpact: '53',
    datQuaImpact: '54',
    sysSupImpact: '68',
    posDutImpact: '61',
    colEffImpact: '45',
    perEffImpact: '53',
    tarDraImpact: '65'
  },
  {
    id: 9,
    orgName: '经营与财务管理部',
    coreIndImpact: '66',
    procExecImpact: '45',
    decEffImpact: '49',
    sysConImpact: '57',
    datQuaImpact: '57',
    sysSupImpact: '48',
    posDutImpact: '64',
    colEffImpact: '58',
    perEffImpact: '61',
    tarDraImpact: '58'
  },
  {
    id: 10,
    orgName: '冷柜研发部',
    coreIndImpact: '54',
    procExecImpact: '56',
    decEffImpact: '65',
    sysConImpact: '67',
    datQuaImpact: '49',
    sysSupImpact: '52',
    posDutImpact: '66',
    colEffImpact: '57',
    perEffImpact: '69',
    tarDraImpact: '62'
  },
  {
    id: 11,
    orgName: '零售与用户运营部',
    coreIndImpact: '67',
    procExecImpact: '66',
    decEffImpact: '54',
    sysConImpact: '50',
    datQuaImpact: '62',
    sysSupImpact: '46',
    posDutImpact: '50',
    colEffImpact: '47',
    perEffImpact: '50',
    tarDraImpact: '57'
  },
  {
    id: 12,
    orgName: '品牌与产品营销部',
    coreIndImpact: '70',
    procExecImpact: '58',
    decEffImpact: '47',
    sysConImpact: '46',
    datQuaImpact: '69',
    sysSupImpact: '64',
    posDutImpact: '64',
    colEffImpact: '64',
    perEffImpact: '62',
    tarDraImpact: '46'
  },
  {
    id: 13,
    orgName: '胶州冰冷工厂',
    coreIndImpact: '58',
    procExecImpact: '64',
    decEffImpact: '62',
    sysConImpact: '61',
    datQuaImpact: '68',
    sysSupImpact: '51',
    posDutImpact: '64',
    colEffImpact: '50',
    perEffImpact: '51',
    tarDraImpact: '70'
  },
  {
    id: 14,
    orgName: '渠道运营部',
    coreIndImpact: '51',
    procExecImpact: '45',
    decEffImpact: '69',
    sysConImpact: '58',
    datQuaImpact: '61',
    sysSupImpact: '58',
    posDutImpact: '46',
    colEffImpact: '56',
    perEffImpact: '67',
    tarDraImpact: '67'
  },
  {
    id: 15,
    orgName: '全球产品经理部',
    coreIndImpact: '49',
    procExecImpact: '69',
    decEffImpact: '56',
    sysConImpact: '56',
    datQuaImpact: '69',
    sysSupImpact: '62',
    posDutImpact: '45',
    colEffImpact: '57',
    perEffImpact: '65',
    tarDraImpact: '68'
  },
  {
    id: 16,
    orgName: '格米冰冷 GTM 部',
    coreIndImpact: '53',
    procExecImpact: '45',
    decEffImpact: '66',
    sysConImpact: '65',
    datQuaImpact: '50',
    sysSupImpact: '57',
    posDutImpact: '46',
    colEffImpact: '57',
    perEffImpact: '49',
    tarDraImpact: '47'
  },
  {
    id: 17,
    orgName: '东莞冰冷工厂',
    coreIndImpact: '49',
    procExecImpact: '61',
    decEffImpact: '54',
    sysConImpact: '52',
    datQuaImpact: '62',
    sysSupImpact: '46',
    posDutImpact: '45',
    colEffImpact: '60',
    perEffImpact: '66',
    tarDraImpact: '70'
  },
  {
    id: 18,
    orgName: '东莞研发部',
    coreIndImpact: '60',
    procExecImpact: '46',
    decEffImpact: '57',
    sysConImpact: '51',
    datQuaImpact: '70',
    sysSupImpact: '46',
    posDutImpact: '68',
    colEffImpact: '57',
    perEffImpact: '56',
    tarDraImpact: '56'
  },
  {
    id: 19,
    orgName: '苏州冰箱工厂',
    coreIndImpact: '59',
    procExecImpact: '56',
    decEffImpact: '50',
    sysConImpact: '67',
    datQuaImpact: '67',
    sysSupImpact: '59',
    posDutImpact: '58',
    colEffImpact: '56',
    perEffImpact: '66',
    tarDraImpact: '65'
  },
  {
    id: 20,
    orgName: '营销管理部',
    coreIndImpact: '46',
    procExecImpact: '53',
    decEffImpact: '56',
    sysConImpact: '65',
    datQuaImpact: '68',
    sysSupImpact: '67',
    posDutImpact: '47',
    colEffImpact: '59',
    perEffImpact: '54',
    tarDraImpact: '52'
  },
  {
    id: 21,
    orgName: '用户服务部',
    coreIndImpact: '48',
    procExecImpact: '67',
    decEffImpact: '65',
    sysConImpact: '49',
    datQuaImpact: '47',
    sysSupImpact: '53',
    posDutImpact: '46',
    colEffImpact: '58',
    perEffImpact: '58',
    tarDraImpact: '64'
  },
  {
    id: 22,
    orgName: '战略与变革管理部',
    coreIndImpact: '52',
    procExecImpact: '52',
    decEffImpact: '51',
    sysConImpact: '66',
    datQuaImpact: '70',
    sysSupImpact: '53',
    posDutImpact: '47',
    colEffImpact: '54',
    perEffImpact: '51',
    tarDraImpact: '45'
  },
  {
    id: 23,
    orgName: '制造中心领导',
    coreIndImpact: '62',
    procExecImpact: '46',
    decEffImpact: '63',
    sysConImpact: '53',
    datQuaImpact: '57',
    sysSupImpact: '56',
    posDutImpact: '54',
    colEffImpact: '51',
    perEffImpact: '49',
    tarDraImpact: '69'
  },
  {
    id: 24,
    orgName: '质量部',
    coreIndImpact: '62',
    procExecImpact: '60',
    decEffImpact: '68',
    sysConImpact: '59',
    datQuaImpact: '57',
    sysSupImpact: '51',
    posDutImpact: '49',
    colEffImpact: '52',
    perEffImpact: '53',
    tarDraImpact: '45'
  },
  {
    id: 25,
    orgName: '智能制造推进部',
    coreIndImpact: '66',
    procExecImpact: '48',
    decEffImpact: '45',
    sysConImpact: '48',
    datQuaImpact: '54',
    sysSupImpact: '62',
    posDutImpact: '47',
    colEffImpact: '64',
    perEffImpact: '49',
    tarDraImpact: '61'
  }
])
onMounted(() => {
  orgTable.value.setCurrentRow(overviewData.value[5])
})

const setColor = value => {
  if (value <= 40) {
    return '#83ECF7'
  } else if (value <= 50) {
    return '#A0F0F6'
  } else {
    return '#6ACEFC'
  }
}

const taskCol = ref([
  {
    label: '序号',
    type: 'index'
  },
  {
    label: '组织名称',
    prop: 'orgName',
    width: '90px'
  },
  {
    label: '举措',
    prop: 'name',
    width: '110px'
  },
  {
    label: '关键行动',
    prop: 'action'
  },
  {
    label: '建议责任人',
    prop: 'personal',
    width: '110px'
  },
  {
    label: '输出成果',
    prop: 'result',
    width: '150px'
  },
  {
    label: '优先级',
    prop: 'status',
    width: '60px'
  }
])
const taskData = ref([
  {
    orgName: '组织名称 1',
    name: '建立商机过程监控体系',
    action:
      '1. 定义 5 项核心过程指标（战略客户商机占比、高价值商机推进及时率、需求匹配度达标率、阶段转化率波动系数、客单价预测准确率）； 2. 开发实时监控看板，自动抓取 CRM 系统数据，设置指标阈值预警（如战略客户商机占比周环比下降超 5% 触发橙色预警）； 3. 每月召开指标分析会，输出《高价值商机流失归因报告》，针对 Top3 流失原因制定专项改进计划。',
    personal: '销售运营总监',
    result: '《商机过程指标手册》《监控看板操作指南》',
    status: '非常高'
  },
  {
    orgName: '组织名称 1',
    name: '制定标准化 SOP 及阶段管控',
    action:
      '1. 编制《商机推进标准操作流程（SOP）》，明确 8 大核心阶段（线索获取→初步筛选→需求调研→方案设计→商务谈判→合同签署→交付跟进→售后复盘）的输入输出标准（如需求调研阶段需输出《客户需求确认单》含 10 项核心字段）； 2. 在 CRM 系统中固化阶段跳转规则（如未完成需求确认单禁止进入方案设计阶段），设置阶段滞留预警（单个阶段超过标准时长 1.5 倍自动触发邮件提醒）； 3. 每季度开展 SOP 执行审计，抽取 20% 商机档案检查阶段完整性，违规案例纳入团队 KPI 考核。',
    personal: '流程管理总监',
    result: '《商机推进 SOP 手册》《系统配置说明书》',
    status: '非常高'
  },
  {
    orgName: '组织名称 1',
    name: '构建智能决策支持系统',
    action:
      '1. 引入 AI 商机评估模型，基于历史成单数据训练算法，自动输出商机成交概率预测（准确率≥85%）及最优响应策略（如 48 小时内高管拜访建议）； 2. 建立三级决策机制：A 类商机（成交概率 > 70%）触发 "高管 + 技术专家" 快速响应通道，B 类商机（40%-70%）启动标准评审流程（3 个工作日内完成），C 类商机（<40%）进入观察池并设置 30 天自动清退规则； 3. 优化 OA 审批流程，500 万以上商机自动关联风险评估报告模板，财务 / 法务关键条款系统自动高亮提示。',
    personal: '数字化转型总监',
    result: '《智能决策系统操作手册》《风险评估模板》',
    status: '非常高'
  },
  {
    orgName: '组织名称 1',
    name: '明确跨部门权责与协作规范',
    action:
      '1. 采用 RACI 矩阵重新定义市场、销售、产品、交付四部门在商机推进中的角色（如需求调研：销售执行 R、市场审核 A、产品支持 C、交付知情 I）； 2. 制定《跨部门协作管理办法》，规定需求评审会 48 小时内需输出《需求对接备忘录》（含优先级排序、责任人和时间节点），方案输出超期每日扣减责任部门协作分 10 分（纳入季度 KPI）； 3. 建立跨部门协作申诉机制，通过企业微信 "协作仲裁" 小程序实时提交争议事项，3 个工作日内由运营委员会裁定责任归属。',
    personal: '运营管理总监',
    result: '《跨部门权责手册》《协作管理办法》',
    status: '高'
  },
  {
    orgName: '组织名称 1',
    name: '完善客户数据治理体系',
    action:
      '1. 梳理客户画像核心字段（新增 "决策人偏好"" 竞品接触史 ""战略匹配度"3 项关键字段），在 CRM 系统设置强制录入校验（未填写禁止提交）； 2. 开发数据清洗工具，每周自动扫描重复客户（匹配度≥80%）、异常数据（如预算为负数），生成《数据质量日报》推送给数据责任人； 3. 建立数据质量追溯机制，每月对字段完整率 < 90% 的业务单元启动专项整改，连续两月不达标部门负责人扣减 20% 绩效。',
    personal: '数据管理总监',
    result: '《客户数据规范》《清洗工具操作指南》',
    status: '中'
  },
  {
    orgName: '组织名称 1',
    name: '打通线上线下业务闭环',
    action:
      '1. 启动 "系统集成攻坚计划"，3 个月内实现 CRM（商机）-ERP（交付）-OA（审批）- 财务系统（开票）的数据实时同步（如合同签署后自动生成交付工单和开票申请）； 2. 开发移动端商机管理 APP，支持现场拜访实时录入客户反馈、拍照上传资料，自动同步至 PC 端系统并触发后续流程（如需求变更即时通知方案团队）； 3. 建立系统异常处理小组，7×24 小时响应数据不同步、流程卡顿等问题，重大故障 30 分钟内响应，2 小时内恢复。',
    personal: 'IT 总监',
    result: '《系统集成方案》《移动端操作手册》',
    status: '非常高'
  },
  {
    orgName: '组织名称 1',
    name: '明确岗位责任与质量管控',
    action:
      '1. 修订《岗位说明书》，在销售、售前、交付等关键岗位增加 "流程质量管控" 职责（如售前工程师需对方案合规性负直接责任，交付经理需监控阶段滞留超期风险）； 2. 设计《关键环节责任清单》，明确 12 个核心节点的第一责任人（如合同预审由法务专员 A 负责，需求确认由客户经理 B 负责），节点质量与个人绩效挂钩（单个节点失误扣减 5% 绩效）； 3. 每半年开展岗位履职审计，通过流程日志追溯关键节点执行情况，出具《岗位责任落实报告》并公示整改要求。',
    personal: '人力资源总监',
    result: '《岗位说明书（修订版）》《责任清单》',
    status: '中'
  },
  {
    orgName: '组织名称 1',
    name: '建立售前交付衔接机制',
    action:
      '1. 制定《售前 - 交付衔接管理办法》，方案设计阶段必须包含《交付可行性评估表》（含交付周期、资源需求、风险预案 3 大模块 15 项评估指标），未通过交付部门会签的方案禁止提交客户； 2. 搭建跨部门信息共享平台，实时同步商机动态（如需求变更、客户特殊要求），设置关键信息自动推送规则（如交付周期压缩超 20% 时同步提醒交付总监）； 3. 每月召开售前交付协同复盘会，统计方案修订率、交付延期率等指标，TOP3 问题纳入次月专项改进计划。',
    personal: '项目管理总监',
    result: '《衔接管理办法》《共享平台操作指南》',
    status: '高'
  },
  {
    orgName: '组织名称 1',
    name: '构建经验沉淀与能力复制体系',
    action:
      '1. 建立《销售最佳实践知识库》，按商机阶段分类沉淀成功案例（如需求调研阶段《5 类客户提问应答模板》、谈判阶段《3 种价格博弈策略》），新员工入职必修且考核通过率≥80% 方可独立跟进商机； 2. 推行 "师徒制 2.0"，为新人匹配 Top20% 资深销售作为导师，制定 3 个月带教计划（如第 1 月掌握需求调研技巧，第 2 月独立完成方案讲解，第 3 月参与合同谈判），徒弟首单周期缩短 30% 以上给予导师专项奖励； 3. 开发智能培训系统，根据员工能力测评结果推送个性化课程（如商机分级准确率 < 70% 自动推送《质量评分模型解析》），每月生成能力提升报告。',
    personal: '销售培训总监',
    result: '《最佳实践知识库》《师徒带教手册》',
    status: '中'
  },
  {
    orgName: '组织名称 1',
    name: '优化考核体系与目标导向',
    action:
      '1. 调整销售考核指标结构：战略客户商机转化率（30%）+ 高毛利产品订单占比（25%）+ 成单质量系数（20%）+ 传统业绩指标（25%），其中战略客户定义为年采购额≥1000 万或行业 TOP10 企业； 2. 建立 "客户结构健康度" 监控看板，实时显示战略客户占比、小客户流失率等指标，季度战略目标达成率 < 80% 的区域启动高管约谈机制； 3. 设立 "战略攻坚专项奖"，对成功突破重点行业头部客户的团队给予合同金额 1% 的额外奖励，对过度追逐小客户导致结构失衡的团队扣减 10% 季度奖金。',
    personal: '销售总监',
    result: '《考核方案（修订版）》《监控看板》',
    status: '低'
  }
])

const affectCol = ref([
  {
    type: 'index',
    label: '序号'
  },
  {
    label: '组织名称',
    prop: 'name',
    width: 90
  },
  {
    label: '短板类型',
    prop: 'type',
    width: 90
  },
  {
    label: '得分',
    prop: 'score',
    width: 50
  },
  {
    label: '主要管理影响',
    prop: 'manage'
  },
  {
    label: '主要指标影响',
    prop: 'indicator'
  },
  {
    label: '影响程度',
    prop: 'extent',
    width: 50
  },
  {
    label: '传导路径',
    prop: 'path'
  },
  {
    label: '关键任务',
    prop: 'task',
    width: 50,
    align: 'center'
  }
])

const affectData = ref([
  {
    name: '组织名称 1',
    type: '核心指标影响',
    score: '59',
    manage: '商机转化率目标难以达成，战略客户渗透不足',
    indicator: '商机转化率下降，商机客单价下降',
    extent: '高',
    path: '缺乏关键过程指标监控→销售行为偏离战略导向→高价值商机流失',
    task: '3'
  },
  {
    name: '组织名称 1',
    type: '流程执行影响',
    score: '62',
    manage: '商机推进随意性大，阶段跳跃频繁',
    indicator: '平均推进周期上升，阶段滞留率上升',
    extent: '极高',
    path: '无标准化SOP→各团队执行差异大→漏斗预测失真',
    task: '3'
  },
  {
    name: '组织名称 1',
    type: '决策效率影响',
    score: '71',
    manage: '重大商机响应延迟，错失市场窗口期',
    indicator: '首触时效>48h占比提升，竞品截单率上升',
    extent: '极高',
    path: '人工评估耗时→关键决策滞后→客户体验下降',
    task: '3'
  },
  {
    name: '组织名称 1',
    type: '制度约束影响',
    score: '65',
    manage: '跨部门协作推诿扯皮，资源整合困难',
    indicator: '方案输出周期>5天比例上升，内部投诉率上升',
    extent: '高',
    path: '权责界定不清→需求评审低效→客户耐心耗尽',
    task: '3'
  },
  {
    name: '组织名称 1',
    type: '数据质量影响',
    score: '43',
    manage: '客户画像不完整，资源投放精准度低',
    indicator: '无效拜访率上升，需求匹配度下降',
    extent: '中',
    path: '关键字段缺失→客户洞察失真→解决方案偏离真实需求',
    task: '3'
  },
  {
    name: '组织名称 1',
    type: '系统支撑影响',
    score: '37',
    manage: '线上线下流程割裂，信息孤岛严重',
    indicator: '重复录入耗时上升，状态更新延迟率上升',
    extent: '极高',
    path: '系统未闭环→人工补录频发→数据时效性差',
    task: '3'
  },
  {
    name: '组织名称 1',
    type: '岗位履职影响',
    score: '54',
    manage: '关键环节无人负责，质量把控缺失',
    indicator: '方案错误率上升，客户投诉率上升',
    extent: '中',
    path: '职责边界模糊→重要节点漏检→交付风险累积',
    task: '3'
  },
  {
    name: '组织名称 1',
    type: '协同效率影响',
    score: '57',
    manage: '售前-交付衔接不畅，客户承诺难兑现',
    indicator: '方案修订次数上升，交付延期率上升',
    extent: '高',
    path: '信息传递失真→方案可行性不足→反复修改',
    task: '3'
  },
  {
    name: '组织名称 1',
    type: '人员效能影响',
    score: '56',
    manage: '新人成单周期长，团队能力方差大',
    indicator: '新人首单周期>45天上升，Top30%人员负载上升',
    extent: '中',
    path: '经验未沉淀→重复试错→资源利用率低',
    task: '3'
  },
  {
    name: '组织名称 1',
    type: '目标驱动影响',
    score: '47',
    manage: '销售追逐易成单小客户，战略目标达成困难',
    indicator: '战略客户占比下降，订单集中度上升',
    extent: '低',
    path: '考核导向偏差→短期利益优先→客户结构失衡',
    task: '3'
  }
])
</script>
<template>
  <div class="org-detail">
    <div class="page-title-line">能力短板影响分析组织分布</div>
    <el-table ref="orgTable" class="distribute" highlight-current-row stripe :data="overviewData">
      <el-table-column label="序号" type="index" width="55" align="center"></el-table-column>
      <el-table-column label="组织" prop="orgName" align="center"></el-table-column>
      <el-table-column
        v-for="col in overviewColumns"
        :key="col.prop"
        align="center"
        :label="col.label"
        :prop="col.prop"
        :width="col.width"
      >
        <template #default="{ row }">
          <div class="table-score">
            <div class="score" :style="{ background: setColor(row[col.prop]) }">{{ row[col.prop] }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="80" fixed="right">
        <template #default="{}">
          <el-button size="small" type="primary" plain>详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-title-line mt-7">能力短板影响分析(供应链计划管理部)</div>
    <SimplenessTable :columns="affectCol" :data="affectData"></SimplenessTable>
    <div class="page-title-line mt-7">
      <div class="page-title">建议改善关键任务(供应链计划管理部)</div>
    </div>
    <SimplenessTable highlight-current-row :columns="taskCol" :data="taskData"></SimplenessTable>
  </div>
</template>
<style lang="scss" scoped>
.page-title-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .choose-dna {
    font-size: 16px;
    color: #40a0ff;
  }
}

.table-score {
  display: flex;
  align-items: center;
  justify-content: center;
  .score {
    width: 65px;
    line-height: 24px;
    background: #6acefc;
    border-radius: 20px;
    color: #fff;
    font-size: 14px;
  }
}
:deep(.el-table.distribute .cell) {
  padding: 0 2px;
}
</style>
