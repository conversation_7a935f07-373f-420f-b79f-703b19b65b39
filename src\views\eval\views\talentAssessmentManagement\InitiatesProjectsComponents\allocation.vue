<template>
  <div class="allocation_wrap page_section">
    <div class="pro_estimate_wrap">
      <p class="page_second_title">项目预估</p>
      <ul class="pro_estimate__main flex_row_between text_center marginT_30">
        <li class="line_item">
          <span class="descript">开始时间</span>
          <p>{{ info.beginTime.split(" ")[0] }}</p>
        </li>
        <li class="line_item">
          <span class="descript">结束时间</span>
          <p>{{ info.endTime.split(" ")[0] }}</p>
        </li>
        <li class="line_item">
          <span class="descript">参评人员(人)</span>
          <p>{{ info.evalUserCount }}</p>
        </li>
        <li class="line_item">
          <span class="descript">预计个人报告(份)</span>
          <p>{{ info.evalObjCount }}</p>
        </li>
        <li class="line_item">
          <span class="descript">预计组织报告(份)</span>
          <p>{{ info.evalOrgCount }}</p>
        </li>
      </ul>
    </div>
    <div class="talent_raview_btn_wrap align_center marginT_30" v-if="isEdit">
      <el-button class="page_confirm_btn" type="primary" @click="prev()"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="start"
        >确认启动</el-button
      >
    </div>
  </div>
</template>

<script>
import { getEvalConfigInfo, startUpEval } from "../../../request/api";
export default {
  name: "allocation",
  components: {},
  props: ["evalId", "isEdit"],
  data() {
    return {
      info: {
        beginTime: "",
        endTime: "",
        evalUserCount: 0,
        evalObjCount: 0,
        evalOrgCount: 0,
      },
    };
  },
  created() {
    this.getEvalConfigInfoFun();
  },
  methods: {
    prev: function () {
      this.$emit("prevStep");
    },
    start: function () {
      this.startUpEvalFun();
    },
    getEvalConfigInfoFun() {
      getEvalConfigInfo({
        evalId: this.evalId,
      }).then((res) => {
        // console.log(res)
        this.info = res;
      });
    },
    startUpEvalFun() {
      startUpEval({
        evalId: this.evalId,
      }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.$router.push(
            "/talentAssessment/talentAssessmentManagement/evaluationItemList"
          );
        } else {
          ElMessage.error(res.msg);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.allocation_wrap {
  .allocation_title {
    padding: 3px 8px;
    font-size: 16px;
    line-height: 28px;
    color: #0099fd;
    font-weight: bold;
    background-color: #f2f8ff;
    border-radius: 3px;
  }

  .evaluation_wrap {
    ul {
      margin: 15px 0;

      li {
        margin: 0 20px 0 0;
        width: 120px;

        p {
          margin: 0 0 10px 0;
          height: 30px;
          line-height: 30px;
          text-align: center;
          background: #f2f8ff;
          color: #0099fd;
          font-weight: 700;
        }

        .el-input__inner {
          width: 60px;
        }

        .el-input-group__append {
          width: 60px;
        }
      }
    }
  }

  .pro_estimate__main {
    // margin: 0 auto;
    // width: 450px;
  }

  .line_item {
    flex: 1;
    line-height: 40px;
    .el-date-editor .el-range-separator {
      width: 8%;
    }
    span {
      display: block;
      height: 40px;
      width: 100%;
      background: #f4f4f4;
    }
  }
}
</style>