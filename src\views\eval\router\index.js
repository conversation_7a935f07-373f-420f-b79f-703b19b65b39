const evalRoutes = [
  {
    path: '/talentAssessment',
    redirect: '/talentAssessment/talentAssessmentManagement/evaluationItemList',
    name: 'talentAssessment',
    meta: {
      name: ''
    },
    component: () => import('../views/talentAssessmentHome'),
    children: [
      // 能力模型管理
      // {
      //   path: '/talentAssessment/capabilityModelHome',
      //   name: 'capabilityModelHome',
      //   meta: {
      //     hidd: true,
      //     name: '能力模型'
      //   },
      //   component: () => import('../views/capabilityModel/capabilityModelHome'),
      //   children: [
      //     {
      //       path: '/talentAssessment/modelCenter',
      //       name: 'modelCenter',
      //       meta: {
      //         name: '模型中心'
      //       },
      //       component: () => import('../views/capabilityModel/modelCenter')
      //     },
      //     {
      //       path: '/talentAssessment/viewModel',
      //       name: 'viewModel',
      //       meta: {
      //         name: '查看模型'
      //       },
      //       component: () => import('../views/capabilityModel/viewModel')
      //     },
      //     {
      //       path: '/talentAssessment/modelManage',
      //       name: 'modelManage',
      //       meta: {
      //         name: '模型管理'
      //       },
      //       component: () => import('../views/capabilityModel/modelManage')
      //     },
      //     {
      //       path: '/talentAssessment/creatModel',
      //       name: 'creatModel',
      //       meta: {
      //         name: '新增模型'
      //       },
      //       component: () => import('../views/capabilityModel/creatModel')
      //     },
      //     {
      //       path: '/talentAssessment/modelMate',
      //       name: 'modelMate',
      //       meta: {
      //         name: '岗能建模'
      //       },
      //       component: () => import('../views/capabilityModel/modelMate')
      //     },
      //     {
      //       path: '/talentAssessment/creatModelMate',
      //       name: 'creatModelMate',
      //       meta: {
      //         name: '新增岗能建模'
      //       },
      //       component: () => import('../views/capabilityModel/creatModelMate')
      //     },
      //     {
      //       path: '/talentAssessment/modelMateInfo',
      //       name: 'modelMateInfo',
      //       meta: {
      //         name: '岗能建模进度'
      //       },
      //       component: () => import('../views/capabilityModel/modelMateInfo')
      //     },
      //     {
      //       path: '/talentAssessment/modelPart',
      //       name: 'modelMate',
      //       meta: {
      //         name: '参与建模'
      //       },
      //       component: () => import('../views/capabilityModel/modelPart')
      //     },
          {
            path: '/talentAssessment/modelAnswer',
            name: 'modelAnswer',
            meta: {
              hidd:true,
              name: '参与建模'
            },
            component: () => import('../views/capabilityModel/modelAnswer2')
          },
      //     // 测评答题个人最后一步
      //     {
      //       path: '/talentAssessment/personalSummary',
      //       name: 'personalSummary',
      //       meta: {
      //         name: '个人发展总结与发展期望'
      //       },
      //       component: () => import('../views/talentAssessmentManagement/staffCheckSummaryAndProspect')
      //     },
      //     // 测评答题上级最后一步
      //     {
      //       path: '/talentAssessment/staffCheckEvaluate',
      //       name: 'staffCheckEvaluate',
      //       meta: {
      //         name: '对下属发展建议'
      //       },
      //       component: () => import('../views/talentAssessmentManagement/staffCheckEvaluate')
      //     },
      //     {
      //       path: '/talentAssessment/modelStart',
      //       name: 'modelMate',
      //       meta: {
      //         name: '开始建模'
      //       },
      //       component: () => import('../views/capabilityModel/modelStart')
      //     },
      //     {
      //       path: '/talentAssessment/modelAffirm',
      //       name: 'modelAffirm',
      //       meta: {
      //         name: '模型确认列表'
      //       },
      //       component: () => import('../views/capabilityModel/modelAffirm')
      //     },
      //     {
      //       path: '/talentAssessment/modelValidation',
      //       name: 'modelValidation',
      //       meta: {
      //         name: '模型确认'
      //       },
      //       component: () => import('../views/capabilityModel/modelValidation')
      //     }
      //   ]
      // },
      // 人才评估
      {
        path: '/talentAssessment/talentAssessmentManagement',
        name: 'talentAssessmentManagementHome',
        meta: {
          name: '人才评估'
        },
        component: () => import('../views/talentAssessmentManagement/talentAssessmentManagementHome'),
        children: [
          {
            path: '/talentAssessment/talentAssessmentManagement/evaluationItemList',
            name: 'evaluationItemList',
            meta: {
              name: '评估项目管理'
            },
            component: () => import('../views/talentAssessmentManagement/evaluationItemList')
          },
          {
            path: '/talentAssessment/talentAssessmentManagement/InitiatesProjects',
            name: 'InitiatesProjects',
            meta: {
              hidd: true,
              name: '发起项目'
            },
            component: () => import('../views/talentAssessmentManagement/InitiatesProjects')
          },
          {
            path: '/talentAssessment/talentAssessmentManagement/editProjects',
            name: 'InitiatesProjectsConfig',
            meta: {
              // 修改测评配置
              hidd: true,
              name: '测评配置'
            },
            component: () => import('../views/talentAssessmentManagement/InitiatesProjects')
          },
          // {
          //     path: "/talentAssessment/talentAssessmentManagement/evaluationProject",
          //     name: "evaluationProject",
          //     meta: {
          //         name: '评估项目管理详情'
          //     },
          //     component: r => require.ensure([], () => r(require('../views/talentAssessmentManagement/evaluationProject')), 'talentAssessmentManagement'),
          // },
          {
            path: '/talentAssessment/talentAssessmentManagement/startPrepare',
            name: 'startPrepare',
            meta: {
              hidd: true,
              name: '测评准备'
            },
            component: () => import('../views/talentAssessmentManagement/startPrepare')
          },
          {
            path: '/talentAssessment/talentAssessmentManagement/confirmEvaluation',
            name: 'confirmEvaluation',
            meta: {
              hidd: true,
              name: '评价关系确认'
            },
            component: () => import('../views/talentAssessmentManagement/confirmEvaluation')
          },
          // {
          //   path: '/talentAssessment/talentAssessmentManagement/evaluationAnswerPage/answerPage',
          //   name: 'answerPage',
          //   meta: {
          //     hidd: true,
          //     name: '答题'
          //   },
          //   component: () => import('../views/talentAssessmentManagement/evaluationAnswerPage/answerPage')
          // },
          // {
          //   path: '/talentAssessment/talentAssessmentManagement/commentLower',
          //   name: 'commentLower',
          //   meta: {
          //     hidd: true,
          //     name: '点评下级'
          //   },
          //   component: () => import('../views/talentAssessmentManagement/commentLower')
          // },
          // {
          //   path: '/talentAssessment/talentAssessmentManagement/assessStaffManage',
          //   name: 'assessStaffManage',
          //   meta: {
          //     hidd: true,
          //     name: '参与人员管理'
          //   },
          //   component: () => import('../views/talentAssessmentManagement/assessStaffManage')
          // },
          // {
          //   path: '/talentAssessment/talentAssessmentManagement/assessProgressManage',
          //   name: 'assessProgressManage',
          //   meta: {
          //     hidd: true,
          //     name: '评估进度管理'
          //   },
          //   component: () => import('../views/talentAssessmentManagement/assessProgressManage')
          // },
          // {
          //   path: '/talentAssessment/talentAssessmentManagement/assessProgressManageInfo',
          //   name: 'assessProgressManageInfo',
          //   meta: {
          //     hidd: true,
          //     name: '评估进度管理详情'
          //   },
          //   component: () => import('../views/talentAssessmentManagement/assessProgressManageInfo')
          // },
          // {
          //   path: '/talentAssessment/talentAssessmentManagement/assessCommentManageInfo',
          //   name: 'assessCommentManageInfo',
          //   meta: {
          //     hidd: true,
          //     name: '点评进度管理'
          //   },
          //   component: () => import('../views/talentAssessmentManagement/assessCommentManageInfo')
          // },
          // {
          //   path: '/talentAssessment/talentAssessmentManagement/evaluateProgressManage',
          //   name: 'evaluateProgressManage',
          //   meta: {
          //     hidd: true,
          //     name: '人员评价进度管理'
          //   },
          //   component: () => import('../views/talentAssessmentManagement/evaluateProgressManage')
          // },
          // {
          //   path: '/talentAssessment/talentAssessmentManagement/assessQualityManageInfo',
          //   name: 'assessQualityManageInfo',
          //   meta: {
          //     hidd: true,
          //     name: '评估质量管理'
          //   },
          //   component: () => import('../views/talentAssessmentManagement/assessQualityManageInfo')
          // },
          // {
          //   path: '/talentAssessment/talentAssessmentManagement/dataConfirmDetail',
          //   name: 'dataConfirmDetail',
          //   meta: {
          //     hidd: true,
          //     name: '数据确认'
          //   },
          //   component: () => import('../views/talentAssessmentManagement/dataConfirmDetail')
          // }
        ]
      }
      // 评估报告
      // {
      //   path: '/talentAssessment/assessmentReport',
      //   name: 'assessmentReportHome',
      //   meta: {
      //     name: '评估报告'
      //   },
      //   component: () => import('../views/assessmentReport/assessmentReportHome'),
      //   children: [
      //     {
      //       path: '/talentAssessment/assessmentReport/assessmentReport',
      //       redirect: '/talentAssessment/assessmentReport/report/projectView',
      //       name: 'assessmentReport',
      //       meta: {
      //         hidd: true,
      //         name: '评估报告'
      //       },
      //       component: () => import('../views/assessmentReport/assessmentReport'),
      //       children: [
      //         {
      //           path: '/talentAssessment/assessmentReport/report/projectView',
      //           name: 'projectView',
      //           meta: {
      //             hidd: true,
      //             name: '项目视图'
      //           },
      //           component: () => import('../views/assessmentReport/assessmentReportComps/projectView')
      //         },
      //         {
      //           path: '/talentAssessment/assessmentReport/report/listView',
      //           name: 'listView',
      //           meta: {
      //             hidd: true,
      //             name: '列表视图'
      //           },
      //           component: () => import('../views/assessmentReport/assessmentReportComps/reportViewList')
      //         },
      //         {
      //           path: '/talentAssessment/assessmentReport/report/projectView/showReport',
      //           name: 'projectViewContent',
      //           meta: {
      //             hidd: true,
      //             name: '查看报告'
      //           },
      //           component: () => import('../views/assessmentReport/assessmentReportComps/projectViewContent')
      //         }
      //       ]
      //     },
      //     {
      //       path: '/talentAssessment/assessmentReport/reportOrg',
      //       name: 'assessmentReportOrg',
      //       meta: {
      //         hidd: true,
      //         name: '评估报告-组织报告'
      //       },
      //       component: () => import('../views/assessmentReport/assessmentReportOrg')
      //     },
      //     {
      //       path: '/talentAssessment/assessmentReport/reportUser',
      //       name: 'assessmentReportOrg',
      //       meta: {
      //         hidd: true,
      //         name: '评估报告-个人报告'
      //       },
      //       component: () => import('../views/assessmentReport/assessmentReportUser')
      //     },
      //     {
      //       path: '/talentAssessment/assessmentReport/abilityAnalysis',
      //       name: 'abilityAnalysis',
      //       meta: {
      //         hidd: true,
      //         name: '能力分析'
      //       },
      //       component: () => import('../views/assessmentReport/abilityAnalysis')
      //     },
      //     {
      //       path: '/talentAssessment/assessmentReport/synthesisScoreAnalysis',
      //       name: 'synthesisScoreAnalysis',
      //       meta: {
      //         hidd: false,
      //         name: '综合得分分析'
      //       },
      //       component: () =>
      //         import('../views/assessmentReport/abilityAnalysisComps/synthesisScoreAnalysis/synthesisScoreAnalysisHome')
      //     },
      //     {
      //       path: '/talentAssessment/assessmentReport/abilityMatchingAnalysis',
      //       name: 'abilityMatchingAnalysis',
      //       meta: {
      //         hidd: true,
      //         name: '能力匹配度分析'
      //       },
      //       component: () =>
      //         import(
      //           '../views/assessmentReport/abilityAnalysisComps/abilityMatchingAnalysis/abilityMatchingAnalysisHome'
      //         )
      //     },
      //     {
      //       path: '/talentAssessment/assessmentReport/abilityRankAnalysis',
      //       name: 'abilityRankAnalysis',
      //       meta: {
      //         hidd: true,
      //         name: '能力排名分析'
      //       },
      //       component: () =>
      //         import('../views/assessmentReport/abilityAnalysisComps/abilityRankAnalysis/abilityRankAnalysisHome')
      //     },
      //     {
      //       path: '/talentAssessment/assessmentReport/evalRelationshipAnalysis',
      //       name: 'evalRelationshipAnalysis',
      //       meta: {
      //         hidd: true,
      //         name: '评价关系分析'
      //       },
      //       component: () =>
      //         import(
      //           '../views/assessmentReport/abilityAnalysisComps/evalRelationshipAnalysis/evalRelationshipAnalysisHome'
      //         )
      //     },
      //     {
      //       path: '/talentAssessment/assessmentReport/talentDifferentiationAnalysis',
      //       name: 'talentDifferentiationAnalysis',
      //       meta: {
      //         hidd: true,
      //         name: '人才区分分析'
      //       },
      //       component: () =>
      //         import(
      //           '../views/assessmentReport/abilityAnalysisComps/talentDifferentiationAnalysis/talentDifferentiationAnalysisHome'
      //         )
      //     },
      //     {
      //       path: '/talentAssessment/assessmentReport/capabilityLengthPlateAnalysis',
      //       name: 'capabilityLengthPlateAnalysis',
      //       meta: {
      //         hidd: true,
      //         name: '能力长短板分析'
      //       },
      //       component: () =>
      //         import(
      //           '../views/assessmentReport/abilityAnalysisComps/capabilityLengthPlateAnalysis/capabilityLengthPlateAnalysisHome'
      //         )
      //     },
      //     {
      //       path: '/talentAssessment/assessmentReport/trainingAdvice',
      //       name: 'trainingAdvice',
      //       meta: {
      //         hidd: true,
      //         name: '能力培训建议'
      //       },
      //       component: () => import('../views/assessmentReport/trainingAdvice')
      //     }
      //   ]
      // }
    ]
  }
]

export default evalRoutes
