<template>
    <div class="model_center_wrap bg_write">
        <div class="page_main_title">模型中心</div>
        <div class="page_section">
            <tabsChangeData :tabsData="tabsData" :activeName="'1'" :handleClick="changeTabs"></tabsChangeData>
            <div class="model_list_wrap">
                <table-component  :tableData="tableData" :needIndex='needIndex' @handleSizeChange='handleSizeChange'  @handleCurrentChange='handleCurrentChange'>
                    <template v-slot:oper>
                        <el-table-column label="操作" width="100" align="center">
                            <template slot-scope="scope">
                                <el-button
                                    class="page_add_btn"
                                    @click.native.prevent="tableSearchRow(scope.$index, tableData.data)"
                                >查看</el-button>
                            </template>
                        </el-table-column>
                    </template>
                </table-component>
            </div>
        </div>
    </div>
</template>

<script>
import {getPlatformModelList,getPlatformOpenModelList} from "../../request/api"
import tableComponent from "@/components/talent/tableComps/tableComponent";
import tabsChangeData from "@/components/talent/tabsComps/tabsChangeData";
export default {
    name: "modelCenter",
    components: {
        tableComponent,
        tabsChangeData
    },
    data() {
        return {
            needIndex:true,
            tabsData: [
                {
                    label: "平台模型",
                    name: "1"
                },
                {
                    label: "已开通模型",
                    name: "2"
                }
            ],
            activeName:'1',
            tableData: {},
            tableDataPM: {
                columns: [
                    {
                        label:"模型类型",
                        prop: "modelTypeName"
                    },
                    {
                        label:"模型名称",
                        prop: "modelName"
                    },
                    {
                        label: "能力分类",
                        prop: "classCount"
                    },
                    {
                        label: "能力词典",
                        prop: "dictCount"
                    },
                    {
                        label: "最新更新时间",
                        prop: "updateTime"
                    },
                    {
                        label: "状态",
                        prop: "status"
                    }
                ],
                data: [],
                page:{
                    total:0,
                    current:1,
                    size:10
                }
            },
            tableDataPOM: {
                columns: [
                    {
                        label:"模型类型",
                        prop: "modelTypeName"
                    },
                    {
                        label: "模型名称",
                        prop: "modelName"
                    },
                    {
                        label: "能力分类",
                        prop: "classCount"
                    },
                    {
                        label: "能力词典",
                        prop: "dictCount"
                    },
                    {
                        label: "状态",
                        prop: "status"
                    },
                    {
                        label: "开通日期",
                        prop: "updateTime"
                    }
                ],
                data: [],
                page:{
                    total:0,
                    current:1,
                    size:10
                }
            }
        };
    },
    created() {
        this.getPlatformModelListFun()
        this.getPlatformOpenModelListFun()
        this.tableData = this.tableDataPM;
    },
    mounted(){
    },
    methods: {
        changeTabs(tab, event) {
            // console.log(tab.name);
            this.tableData = tab.name == "1" ? this.tableDataPM : this.tableDataPOM;
            this.activeName = tab.name
            // console.log(this.activeName)
        },
        // 获取平台模型列表
        getPlatformModelListFun(){
            getPlatformModelList({
                current:this.tableDataPM.page.current,
                size:this.tableDataPM.page.size
            }).then(res=>{
                console.log(res)
                this.tableDataPM.data = []
                this.tableDataPM.page = {
                                            total:0,
                                            current:1,
                                            size:10
                                        }
                if(res.code == 200){
                    this.tableDataPM.data = res.data.map(item=>{
                        return{
                            classCount:item.classCount + '类',
                            dictCount:item.dictCount + '项',
                            modelId:item.modelId,
                            modelName:item.modelName,
                            modelTypeName:item.modelTypeName,
                            status:item.status,
                            updateTime:item.updateTime
                        }
                    })
                    this.tableDataPM.page = res.page
                }
            })
        },
        // 获取已开通模型列表
        getPlatformOpenModelListFun(){
            getPlatformOpenModelList({
                current:this.tableDataPOM.page.current,
                size:this.tableDataPOM.page.size
            }).then(res=>{
                console.log(res)
                this.tableDataPOM.data = []
                this.tableDataPOM.page = {
                                            total:0,
                                            current:1,
                                            size:10
                                        }
                if(res.code == 200){
                    this.tableDataPOM.data = res.data.map(item=>{
                        return{
                            classCount:item.classCount + '类',
                            dictCount:item.dictCount + '项',
                            modelId:item.modelId,
                            modelName:item.modelName,
                            modelTypeName:item.modelTypeName,
                            status:item.status,
                            updateTime:item.updateTime
                        }
                    })
                    this.tableDataPOM.page = res.page
                }
            })
        },
        // 翻页
        handleSizeChange(size){
            // console.log(this.activeName)
            if(this.activeName == '1'){
                // 平台模型
                this.tableDataPM.page.current=1;
                this.tableDataPM.page.size = size
                this.getPlatformModelListFun()
            }else if(this.activeName == '2'){
                // 已开通模型
                this.tableDataPOM.page.current=1;
                this.tableDataPOM.page.size = size;
                this.getPlatformOpenModelListFun()
            }
        },
        handleCurrentChange(current){
            if(this.activeName == '1'){
                // 平台模型
                this.tableDataPM.page.current = current
                this.getPlatformModelListFun()
            }else if(this.activeName == '2'){
                // 已开通模型
                this.tableDataPOM.page.current = current
                this.getPlatformOpenModelListFun()
            }
        },
        tableSearchRow(index,rows){
            this.$router.push({
                path: "/talentAssessment/viewModel",
                query: {modelId:rows[index].modelId}
            });
        },
    },
    watch:{
        
    },
};
</script>

<style scoped lang="scss">
.section_tab_wrap {
    border-bottom: 1px solid #333;
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    .section_tab_item {
        margin-right: 20px;
        border-bottom: 3px solid transparent;
        padding: 0 10px;
        line-height: 30px;
        cursor: pointer;
        //  color: #4471c4;
        &.active {
            color: #4471c4;
            border-color: #4471c4;
        }
    }
}
</style>
