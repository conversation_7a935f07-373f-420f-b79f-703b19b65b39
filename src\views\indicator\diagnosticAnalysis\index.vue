<template>
  <div class="diagnosticAnalysis">
    <div class="classify">
      <div class="title">指标类别</div>
      <div
        class="item"
        :class="{ active: item.id == activeStepId }"
        v-for="item in classify"
        :key="item.id"
        @click="changeStep(item)"
      >
        {{ item.name }}
      </div>
    </div>
    <component :is="activeStep.comp" />
  </div>
</template>
<script setup>
defineOptions({ name: 'diagnosticAnalysis' })
const classify = ref([
  { id: 1, name: '组织指标', comp: defineAsyncComponent(() => import('./organization.vue')) },
  { id: 2, name: '人员指标', comp: defineAsyncComponent(() => import('./person.vue')) },
  { id: 3, name: '项目指标', comp: defineAsyncComponent(() => import('./project.vue')) }
])
const router = useRouter()
const route = useRoute()
const activeStepId = ref(route.query.stepId || classify.value[0].id)
const activeStep = computed(() => {
  return classify.value.find(item => item.id == activeStepId.value)
})

router.push({ path: route.path, query: { ...route.query, stepId: activeStepId.value } })
const changeStep = item => {
  activeStepId.value = item.id
  router.push({ path: route.path, query: { ...route.query, stepId: item.id } })
}
</script>
<style lang="scss" scoped>
.diagnosticAnalysis {
  .classify {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      color: #333333;
      font-weight: 600;
      margin-right: 8px;
    }

    .item {
      width: 311px;
      height: 35px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #c6dbf3;
      font-size: 14px;
      color: #333333;
      text-align: center;
      line-height: 35px;
      margin-right: 9px;
      background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
      cursor: pointer;

      &.active {
        color: #40a0ff;
        box-shadow: 0px 0px 20px -4px rgba(64, 160, 255, 0.48);
      }
    }
  }
}
</style>
