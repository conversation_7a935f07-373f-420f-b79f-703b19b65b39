<template>
    <div class="dedicated_main org_report_main" :class="{ height_auto: isPdf }">
        <slot></slot>
        <div class="page_second_title">敬业度与满意度</div>
        <div class="report_section">
            <div class="page_third_title">整体敬业度</div>
            <div class="report_section_content clearfix">
                <div class="chart_fan_box overflow_y_hidden" :id="fanId"></div>
                <div class="explain_text">
                    员工敬业度是员工对于企业在情感和智力上的参与和承诺，通过大量的数据分析与跟踪调查，绩效表现较好的企业中有超过65%的员工敬业，并且拥有更优的经营业绩，而最佳表现的企业敬业度甚至能达到85%，而员工敬业度得分低于45%的企业，其经营业绩开始出现下滑。整体上，本次评估整体敬业度为
                    {{
                        wholeAvg
                    }}%企业高敬业推动企业快速发展，企业需要保持员工的高敬业度，从而促进企业的跨越式发展
                </div>
            </div>
        </div>
        <div class="report_section">
            <div class="page_third_title">具体敬业度表现</div>
            <div class="report_section_content clearfix">
                <tableComp
                    :tableData="tableData"
                    :needPagination="false"
                    :overflowTooltip="false"
                    :border="true"
                ></tableComp>
            </div>
        </div>
        <div class="second_section_wrap marginB_32">
            <div class="page_third_title">满意度表现</div>
            <div class="myd_wrap flex_row_start">
                <div class="myd_score">
                    <customProcess
                        :size="150"
                        :strokeWidth="20"
                        :num="wholeAvgMYD"
                    />
                </div>
                <div class="chart_myd_box" :id="driveChartId"></div>
            </div>
        </div>

        <el-row :gutter="16">
            <el-col :span="8">
                <div class="page_third_title">敬业度满意度人数区间分布</div>
                <div class="matrix_wrap">
                    <div class="matrix_content">
                        <div class="title align_left">满意度</div>
                        <div
                            class="m_row flex_row_start"
                            v-for="row in matrixData"
                        >
                            <div class="row_title">{{ row.name }}</div>
                            <div
                                :class="'m_col ' + row.code + col.code"
                                v-for="col in row.maps"
                            >
                                {{ col.value || null }}
                            </div>
                        </div>
                        <div class="m_row flex_row_start">
                            <div class="row_title"></div>
                            <div
                                class="col_title"
                                v-for="col in matrixData[0].maps"
                            >
                                {{ col.name }}
                            </div>
                        </div>
                        <div class="title align_right">敬业度</div>
                    </div>
                    <div class="matrix_desc flex_row_start">
                        <div class="item">
                            <div class="item_sign aa"></div>
                            <div class="item_text">士气激励</div>
                        </div>
                        <div class="item">
                            <div class="item_sign sign2"></div>
                            <div class="item_text">保持现状</div>
                        </div>
                        <div class="item">
                            <div class="item_sign sign3"></div>
                            <div class="item_text">提升满意度</div>
                        </div>
                        <div class="item">
                            <div class="item_sign sign4"></div>
                            <div class="item_text">防止流失</div>
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="page_third_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        getOrgEngagement,
        getDeptEngagement,
        getOrgTalentDrivers,
        getTalentDrivers,
        getSuggestionsImprovement,
        getPersonnelTalentDrivers,
    } from "../../../../request/api.js"; //敬业度新接口
    import listComp from "./components/listComp.vue";
    import tableComp from "@/components/talent/tableComps/tableComponent";
    import customProcess from "@/components/talent/common/customProcess.vue";

    export default {
        name: "orgRDedicatedAnalysis",
        props: ["enqId", "orgCode", "isPdf"],
        components: {
            listComp,
            tableComp,
            customProcess,
        },
        data() {
            return {
                fanId: this.$util.createRandomId(),
                pageData: "",
                fanChart: {
                    data: [],
                    actual: "",
                },
                wholeAvg: "",
                wholeAvgMYD: null,
                driveChartId: this.$util.createRandomId(),
                jyDistributeChart: {
                    data: [],
                },
                matrixData: [
                    {
                        maps: [],
                    },
                ],

                jyDistributeOverall: "",
                engagementData: {
                    data: [],
                    legend: [],
                },
                tableData: {
                    columns: [
                        {
                            label: "词典分类",
                            prop: "parentModuleName",
                            width: "100",
                        },
                        {
                            label: "对应意愿",
                            prop: "moduleName",
                            width: "100",
                        },
                        {
                            label: "行为名称",
                            prop: "itemName",
                        },
                        {
                            label: "得分",
                            prop: "score",
                            width: "80",
                        },
                        {
                            label: "行为描述",
                            prop: "itemDesc",
                        },
                    ],
                    data: [],
                },
                jyDriversChart: {
                    data: [],
                    padding: 100,
                },
                pageData: {},
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "敬业度满意度<80 人才分类",
                        elSpan: 8,
                        chartHeight: "200",
                        chartType: "YBar",
                        dataKey: "eighty",
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "敬业度满意度<60 人才分类",
                        elSpan: 8,
                        chartType: "YBar",
                        dataKey: "sixty",
                    },
                ],
                listArr: [
                    {
                        title: "各类人才满意度",
                        ajaxUrl: getOrgTalentDrivers,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "组织",
                                prop: "name",
                            },
                        ],
                    },
                    {
                        title: "各人才类别的满意度详情",
                        ajaxUrl: getTalentDrivers,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "",
                                prop: "name",
                            },
                        ],
                    },
                    {
                        title: "敬业度提升建议",
                        ajaxUrl: getSuggestionsImprovement,
                        isAsyncColumns: false,
                        columns: [
                            {
                                label: "机会领域",
                                prop: "moduleName",
                            },
                            {
                                label: "典型表现",
                                prop: "itemName",
                            },
                            {
                                label: "具体评估项",
                                prop: "typicalIssue",
                            },
                            {
                                label: "得分",
                                prop: "actualScore",
                                width: 100
                            },
                            {
                                label: "改善方向",
                                prop: "improvementSuggestion",
                            },
                        ],
                    },
                    {
                        title: "人才满意度详情",
                        ajaxUrl: getPersonnelTalentDrivers,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                        ],
                    },
                ],
            };
        },
        beforeCreate() {},
        created() {
            this.getOrgEngagementFun();
        },
        mounted() {},
        methods: {
            getOrgEngagementFun() {
                getDeptEngagement({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.pageData = res.data;
                        this.initData();
                    }
                });
            },
            initData() {
                if (this.pageData) {
                    // 整体敬业度
                    this.fanChart.data = this.pageData.modelGrades.map((item) => {
                        return {
                            name: item.gradeName + "%",
                            min: item.beginScore + "%",
                            max: item.endScore + "%",
                        };
                    });
                    this.fanChart.actual = this.pageData.wholeAvg + "%";
                    echartsRenderPage(
                        this.fanId,
                        "Fan",
                        "280",
                        "280",
                        this.fanChart
                    );
                    this.wholeAvg = this.pageData.wholeAvg;

                    // 具体敬业度表现
                    this.tableData.data = this.pageData.jydOrgList;
                    this.wholeAvgMYD = this.pageData.wholeAvgMYD;
                    // 敬业驱动因素
                    this.jyDriversChart.data = this.pageData.jyDriversChart.map(
                        (item) => {
                            return {
                                name: item.moduleName,
                                value: item.score,
                            };
                        }
                    );
                    echartsRenderPage(
                        this.driveChartId,
                        "XBar",
                        null,
                        "260",
                        this.jyDriversChart
                    );
                    this.matrixData = this.pageData.matrix;
                    this.initChart(this.pageData);

                    return;
                    // 敬业度分布
                    this.jyDistributeOverall =
                        this.pageData.jyDistributeChart[
                            this.pageData.jyDistributeChart.length - 1
                        ].value;
                    this.pageData.jyDistributeChart.pop();
                    this.jyDistributeChart.data = this.pageData.jyDistributeChart;
                    echartsRenderPage(
                        "jyDistributeChart",
                        "YBar",
                        "300",
                        "220",
                        this.jyDistributeChart
                    );
                    // 下级部门敬业度情况
                    this.engagementData.data = this.pageData.engagement.chartData;
                    this.engagementData.legend = this.pageData.engagement.legend;
                    let id = this.isPdf ? "subOrgjydChartPdf" : "subOrgjydChart";
                    echartsRenderPage(
                        id,
                        "XBar",
                        "1100",
                        "260",
                        this.engagementData
                    );
                }
            },
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                        padding: 115
                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .dedicated_main {
        height: 420px;
        padding-right: 16px;
        overflow-y: auto;
        &.height_auto {
            height: auto;
        }
    }
    .report_section {
        margin-bottom: 32px;
        .report_section_content {
            margin-bottom: 16px;
        }
    }
    .report_section {
        margin-bottom: 32px;
        .chart_box_wrap {
            width: 300px;
            .item_title {
                span {
                    font-weight: 600;
                }
            }
        }
    }
    .chart_fan_box {
        float: left;
        width: 300px;
        height: 170px;
        margin-right: 32px;
        border: none;
    }
    .chart_myd_box {
        width: 100%;
        height: 300px;
        margin-left: 16px;
    }
    .myd_score {
        margin-top: 30px;
    }

    .matrix_wrap {
        .matrix_content {
            width: 300px;
            text-align: center;
            .title {
            }
            .m_row {
                .row_title {
                    width: 50px;
                    line-height: 48px;
                }
                .m_col {
                    width: 50px;
                    height: 50px;
                    line-height: 50px;
                    border: 1px solid #bfbfbf;
                    margin-right: -1px;
                    margin-bottom: -1px;
                    background: #fff9e5;
                    color: #ffc000;
                }
                .col_title {
                    width: 49px;
                    height: 30px;
                    line-height: 30px;
                }
                .aa,
                .ab,
                .ac,
                .ba,
                .bb,
                .bc,
                .ca,
                .cb,
                .cc {
                    background: #ffdddd;
                    color: #ff0000;
                }
                .ad,
                .ae,
                .bd,
                .be,
                .cd,
                .ce {
                    background: #e5f7fd;
                }
                .dd,
                .de,
                .ed {
                    background: #c8e7a7;
                    color: #fff;
                }
                .ee {
                    background: #92d050;
                    color: #fff;
                }
            }
        }
        .matrix_desc {
            margin-top: 10px;
            .item {
                display: flex;
                align-items: center;
                margin-right: 10px;
            }
            .item_sign {
                width: 20px;
                height: 8px;
                background: #fff9e5;
                margin-right: 5px;
                &.sign2 {
                    background: #c8e7a7;
                }
                &.sign3 {
                    background: #ffdddd;
                }
                &.sign4 {
                    background: #92d050;
                }
            }
        }
    }

    // .chart_box {
    //     float: left;
    //     width: 300px;
    //     height: 170px;
    //     margin-right: 32px;
    //     border: none;
    //     &.factor {
    //         width: 100%;
    //         height: 260px;
    //         margin-bottom: 16px;
    //     }
    // }
    .table_wrap {
        width: 100%;
        align-items: flex-start;
        .top_table,
        .last_table {
            flex: 1;
        }
    }
    .explain_text {
        overflow: hidden;
        color: #212121;
        line-height: 24px;
        .explain_title {
            font-size: 16px;
            /*color: #92D050;*/
            font-weight: bold;
            margin-bottom: 16px;
        }
        .line {
            line-height: 28px;
        }
        .dot {
            list-style: inside disc;
        }
    }
</style>