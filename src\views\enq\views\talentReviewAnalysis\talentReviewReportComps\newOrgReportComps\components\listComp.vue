<template>
    <div class="">
        <div class="item_title">{{ resOptions.title }}</div>
        <tableComps
            :needIndex="true"
            @handleCurrentChange="handleCurrentChange"
            @handleSizeChange="handleSizeChange"
            :tableData="tableData"
            :needPagination="isPdf ? false : resOptions.paging"
            :maxHeight="isPdf ? 'auto' : '300'"
            :overflowTooltip="!isPdf"
            :loading="loading"
        ></tableComps>
        <div
            class="table_tip"
            v-if="
                isPdf &&
                resOptions.paging &&
                (tableData.page.total > size || tableData.data.length > size)
            "
        >
            更多数据请查看网页版报告
        </div>
    </div>
</template>

<script>
    import tableComps from "@/components/talent/tableComps/tableComponent";

    export default {
        name: "list",
        components: {
            tableComps,
        },
        props: {
            options: {
                type: Object,
                default: function () {
                    return {
                        enqId: null,
                        orgCode: null,
                        title: null,
                        ajaxUrl: function () {},
                        columns: [],
                        isPdf: false,
                        isAsyncColumns: false,
                        paging: true,
                    };
                },
            },
            enqId: String,
            orgCode: String,
            isPdf: Boolean,
        },

        data() {
            return {
                loading:true,
                size: 50,
                current: 1,
                tableData: {
                    data: [],
                    page: {
                        total: 0,
                        size: 50,
                        current: 1,
                    },
                },
            };
        },
        created() {
            this.getList();
        },
        computed: {
            resOptions() {
                let defaultOptions = {
                    enqId: null,
                    orgCode: null,
                    title: null,
                    ajaxUrl: function () {},
                    columns: [],
                    afterColumns: [],
                    isPdf: false,
                    isAsyncColumns: false,
                    paging: true,
                };
                return Object.assign(defaultOptions, this.options, {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                    isPdf: this.isPdf,
                });
            },
        },
        methods: {
            getList() {
                this.loading = true;
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                params = Object.assign(params,this.resOptions.otherParams);
                this.resOptions.ajaxUrl(params).then((res) => {
                    if (res.code == 200) {
                        let data = res.data;
                        if (this.resOptions.isAsyncColumns) {
                            // 异步表头处理
                            data.legend.map((item) => {
                                item.prop = item.code.replace(/\./g, "_");
                                item.label = item.name;
                                delete item.code;
                                delete item.name;
                            });
                            let columns = [
                                ...this.resOptions.columns,
                                ...data.legend,
                                ...this.resOptions.afterColumns,
                            ];
                            this.$set(this.tableData, "columns", columns);
                        } else {
                            this.$set(
                                this.tableData,
                                "columns",
                                this.resOptions.columns
                            );
                        }
                        if (Array.isArray(data)) {
                            // 用于处理旧的接口，列表数据返回在data字段中
                            data.map((item) => {
                                for (const key in item) {
                                    if (Object.hasOwnProperty.call(item, key)) {
                                        const value = item[key];
                                        let k = key.replace(/\./g, "_");
                                        item[k] = value;
                                    }
                                    delete item.key;
                                }
                            });
                            this.$set(this.tableData, "data", data);
                        } else {
                            data.dataList.map((item) => {
                                for (const key in item) {
                                    if (Object.hasOwnProperty.call(item, key)) {
                                        const value = item[key];
                                        let k = key.replace(/\./g, "_");
                                        item[k] = value;
                                    }
                                    delete item.key;
                                }
                            });
                            this.$set(this.tableData, "data", data.dataList);
                        }

                        this.$set(this.tableData, "page", res.page);
                    }
                    this.loading = false;
                });
            },
            handleCurrentChange(current) {
                this.current = current;
                this.getList();
            },
            handleSizeChange(size) {
                this.size = size;
                this.getList();
            },
        },
    };
</script>

<style lang="scss" scoped>
</style>