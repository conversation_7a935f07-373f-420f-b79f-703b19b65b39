<template>
  <div class="task_confirmation_main PRquality_Evaluate_wrap marginT_16">
    <div class="page_second_title">核心素质</div>
    <div class="department_main marginT_8">
      <div class="personnel_item_wrap_left">
        <el-menu
          :default-active="defaultActive"
          class="left_menu_wrap el-menu-vertical-demo"
          :unique-opened="true"
          @open="handleOpen"
          @close="handleClose"
        >
          <el-sub-menu v-for="(item, index) in personnelData" :key="index" :index="item.parent.moduleCode">
            <template #title>
              <span>{{ item.parent.moduleName }}</span>
            </template>
            <el-menu-item
              v-for="(item1, index1) in item.children"
              :index="item1.moduleCode"
              :key="index1"
              @click="selectPersonnel(item1, index1, index)"
            >
              {{ item1.moduleName }}
              <i
                class="icon disc"
                :class="{
                  completed: item1.statusName == 'Y'
                }"
              ></i>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </div>
      <div class="personnel_item_wrap_right">
        <div class="project_item" v-if="personnelData.length > 0 && oneLevelIndex != personnelData.length - 1">
          <span class="moduleName">
            {{
              personnelData
                ? personnelData[oneLevelIndex].parent.moduleName +
                  '/' +
                  personnelData[oneLevelIndex].children[currIndex].moduleName
                : ''
            }}：
          </span>
          <span class="item">{{ itemInfo.module ? itemInfo.module.moduleDesc : '' }}</span>
        </div>
        <div class="flex_row_start" v-if="oneLevelIndex != personnelData.length - 1">
          <div class="project_item" v-if="itemInfo.module">
            <span class="moduleName">正向行为：</span>
            <p class="item">{{ itemInfo.module.positiveBehavior }}</p>
          </div>
          <div class="project_item" v-if="itemInfo.module">
            <span class="moduleName">负向行为：</span>
            <p class="item">{{ itemInfo.module.negativeBehavior }}</p>
          </div>
        </div>
        <div class="project_item" v-if="oneLevelIndex != personnelData.length - 1">
          <span class="moduleName">评分规则</span>
          <div class="item">
            <p>0-3分: 很差，能够观察到的行为项极少或观察不到这些行为的表现，或者大部分行为表现为负面行为；</p>
            <p>4-5分: 较差，能够观察到的行为项很少或者这种行为时有时无，或有时个别行为表现为负面行为；</p>
            <p>6-7分: 合格，约超过一半以上行为能够观察到，并且持续表现为正面行为；</p>
            <p>8分: 良好，大部分行为能够观察到，并且这种行为出现的频率比较高，表现比较稳定；</p>
            <p>9-10分: 优秀，所有行为项都能够观察到，并且这种行为出现的频率非常高，表现很稳定</p>
          </div>
        </div>

        <div v-if="personnelData.length - 1 != oneLevelIndex">
          <evaluateTableSelect :types="'quality'" :dataFrom="itemInfo" @getChildData="getChildDataFn">
          </evaluateTableSelect>
        </div>
        <div v-else>
          <qualityEvaluationList :enqId="enqId" :type="'coreQuality'" @getPotentialData="getPotentialDataFn">
          </qualityEvaluationList>
        </div>

        <div class="btn_wrap align_center">
          <el-button
            class="page_new_confirm_btn"
            type="primary"
            @click="prevBtn"
            v-show="currentIndex != currentFirstCode"
            >上一步</el-button
          >
          <el-button class="page_new_confirm_btn" type="primary" @click="submitForm">{{ nextBtnText }}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import {
  getCoreQualityEvaluation,
  getQualityEvaluationItem,
  saveSurveySubmit,
  saveDurveyUserResult,
  saveEvaluationComment
} from '../../../request/api'
import evaluateTableSelect from './evaluateTableSelect.vue'
import qualityEvaluationList from './qualityEvaluationList.vue'

// Props
const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: Number,
  currentFirstCode: String
})

// Emits
const emit = defineEmits(['prevStep', 'nextStep'])

// Reactive data
const savaFlag = ref(true)
const currIndex = ref(0)
const oneLevelIndex = ref(0)
const personnelData = ref([])
const itemInfo = ref({})
const defaultActive = ref('')
const defaultOpeneds = ref('')
const type = ref('quality')

// Methods
const handleOpen = (key, keyPath) => {
  // console.log(key, keyPath);
}

const handleClose = (key, keyPath) => {}

// 点评保存题目
const getPotentialDataFn = data => {
  console.log('data :>> ', data)
  console.log('点击保存题目')
  if (data) {
    let arr = []
    data.forEach(item => {
      arr.push({
        comment: item.comment,
        relationType: item.relationType,
        userId: item.userId,
        enqId: props.enqId,
        type: 'coreQuality'
      })
    })
    saveEvaluationComment(arr).then(res => {
      if (res.code == '200') {
        ElMessage({
          type: 'success',
          message: '保存成功!'
        })
        getDeptUserPostFun()
      } else {
        ElMessage.error('保存失败!')
      }
    })
  }
}

// 除点评保存题目
const getChildDataFn = data => {
  console.log(data)
  if (data) {
    data.map(item => {
      item['coreQualityOptionNbr'] = item.optionNbr.join(',')
      delete item.optionNbr
    })
    let params = {
      enqId: props.enqId,
      enqItemOptionRequests: data,
      modelId: personnelData.value[oneLevelIndex.value].children[currIndex.value].modelId,
      moduleCode: personnelData.value[oneLevelIndex.value].children[currIndex.value].moduleCode
    }
    saveSurveySubmit(params).then(res => {
      if (res.code == '200') {
        ElMessage.success(res.msg)

        if (currIndex.value < personnelData.value[oneLevelIndex.value].children.length - 1) {
          currIndex.value++
        } else {
          if (oneLevelIndex.value < personnelData.value.length - 1) {
            currIndex.value = 0
            oneLevelIndex.value++
          } else if (oneLevelIndex.value == personnelData.value.length - 1) {
            currIndex.value++
          }
        }
        if (oneLevelIndex.value < personnelData.value.length - 1) {
          getDeptUserPostFun()
        } else {
          defaultActive.value = personnelData.value[oneLevelIndex.value].children[currIndex.value].moduleCode
        }
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}

// 侧边栏
const getDeptUserPostFun = () => {
  let params = {
    enqId: props.enqId
  }
  getCoreQualityEvaluation(params).then(res => {
    if (res.code == '200') {
      if (res.data && res.data.length != 0) {
        personnelData.value = res.data
        defaultActive.value = personnelData.value[oneLevelIndex.value].children[currIndex.value].moduleCode
        // 需要模块题目（除最后一个）
        if (oneLevelIndex.value < personnelData.value.length - 1) {
          getQualityEvaluationItemFun(personnelData.value[oneLevelIndex.value].children[currIndex.value])
        }
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const checkData = data => {
  let arr = data
  let len = arr.length

  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    let children = obj.children
    for (let j = 0; j < children.length; j++) {
      const row = children[j]
      if (row['statusName'] == 'N') {
        console.log('statusName :>> ', obj['statusName'])
        return true
      }
    }
  }
}

// 点击侧边栏
const selectPersonnel = (item, index, indexOne) => {
  console.log('item :>> ', item)
  currIndex.value = index
  oneLevelIndex.value = indexOne
  defaultActive.value = item.moduleCode

  if (oneLevelIndex.value < personnelData.value.length - 1) {
    getQualityEvaluationItemFun(item)
  }
}

const getQualityEvaluationItemFun = item => {
  let params = {
    enqId: props.enqId,
    moduleCode: item.moduleCode
  }
  getQualityEvaluationItem(params).then(res => {
    if (res.code == '200') {
      itemInfo.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const prevBtn = () => {
  emit('prevStep')
}

const submitForm = () => {
  emit('nextStep')
}

// Watchers
watch(
  itemInfo,
  val => {
    console.log(val)
  },
  { deep: true }
)

// Lifecycle
onMounted(() => {
  getDeptUserPostFun()
})
</script>

<style scoped lang="scss"></style>
