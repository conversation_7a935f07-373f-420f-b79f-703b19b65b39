<script setup>
import { Search } from "@element-plus/icons-vue";
import SectionTab from "../components/sectionTab.vue";
import Tree from "@/components/tree/index.vue";
import Table from "../components/table.vue";
const key = ref("");

const searchData = ref({
  num: "",
  result: "",
  ability: "",
  kpi: "",
  keyTask: "",
  flow: "",
  orgAbility: "",
  staffAbility: "",
  staffPower: "",
  engagement: "",
  digitization: "",
  AIAbility: "",
});

const columns = ref([
  {
    label: "组织编码",
    prop: "a",
  },
  {
    label: "组织名称",
    prop: "b",
  },
  {
    label: "业务领域",
    prop: "c",
  },
  {
    label: "组织负责人",
    prop: "d",
  },
  {
    label: "组织人数",
    prop: "e",
  },
  {
    label: "组织效能",
    prop: "f",
    slot: "fSlot",
    width: 80,
  },
  {
    label: "结果绩效",
    prop: "g",
    slot: "gSlot",
    width: 80,
  },
  {
    label: "能力建设",
    prop: "h",
    slot: "hSlot",
    width: 80,
  },
  {
    label: "KPI达成率",
    prop: "i",
    slot: "iSlot",
    width: 80,
  },
  {
    label: "关键任务",
    prop: "j",
    slot: "jSlot",
    width: 80,
  },
  {
    label: "流程能力",
    prop: "k",
    slot: "kSlot",
    width: 80,
  },
  {
    label: "组织能力",
    prop: "l",
    slot: "lSlot",
    width: 80,
  },
  {
    label: "人员能力",
    prop: "m",
    slot: "mSlot",
    width: 80,
  },
  {
    label: "人员动力",
    prop: "n",
    slot: "nSlot",
    width: 80,
  },
  {
    label: "人员敬业度",
    prop: "o",
    slot: "oSlot",
    width: 80,
  },
  {
    label: "数字化",
    prop: "p",
    slot: "pSlot",
    width: 80,
  },
  {
    label: "AI能力",
    prop: "q",
    slot: "qSlot",
    width: 80,
  },
  {
    label: "操作",
    prop: "r",
    slot: "rSlot",
    align: "center",
    width: 220,
  },
]);
const data = ref([
  {
    a: "Z003",
    b: "二级组织名称3",
    c: "销售",
    d: "王伟",
    e: 14,
    f: 83,
    g: 93,
    h: 43,
    i: 54,
    j: 63,
    k: 74,
    l: 83,
    m: 93,
    n: 93,
    o: 93,
    p: 93,
    q: 93,
  },
]);

const numberArea = ref([
  {
    num: "0~59",
  },
  {
    num: "60~69",
  },
  {
    num: "70~79",
  },
  {
    num: "80~89",
  },
  {
    num: "90~100",
  },
]);

const columns2 = ref([
  {
    label: "维度",
    prop: "a",
    width: 150,
  },
  {
    label: "数据表现",
    prop: "b",
    width: 150,
  },
  {
    label: "根因假设",
    prop: "c",
  },
]);
const data2 = ref([
  {
    a: "流程",
    b: "55分（最低项）",
    c: "销售SOP未标准化，跨部门协作存在断点 ",
  },
]);

const columns3 = ref([
  {
    label: "阶段",
    prop: "a",
  },
  {
    label: "举措",
    prop: "b",
  },
  {
    label: "关键行动",
    prop: "c",
  },
  {
    label: "关键行动举措",
    prop: "d",
    width: 300,
  },
  {
    label: "责任主体",
    prop: "e",
  },
  {
    label: "效能目标",
    prop: "f",
  },
]);
const data3 = ref([
  {
    a: "急救期",
    b: "第1-3月",
    c: "人员动力提升",
    d: "1. 上线阶梯式佣金+非物质激励方案 2. 部署自动化工具释放20%事务性工作时间",
    e: "王伟（负责人） HRBP",
    f: "≥75",
  },
  {
    a: "急救期",
    b: "第1-3月",
    c: "流程止血",
    d: "1. 选择报价审批流程进行AI改造试点 2. 建立销售SOP基础框架",
    e: "运营部 数字化团队",
    f: "≥75",
  },
]);

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0 || columnIndex === 1 || columnIndex === 5) {
    if (rowIndex % 2 === 0) {
      return {
        rowspan: 2,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};

const circleColor = (v) => {
  if (v < 59) {
    return "bg1_b";
  } else if (v > 59 && v < 69) {
    return "bg2_b";
  } else if (v > 69 && v < 79) {
    return "bg3_b";
  } else if (v > 79 && v < 89) {
    return "bg4_b";
  } else if (v > 89 && v <= 100) {
    return "bg5_b";
  }
};

const aiData = ref({
  t: "一、整体效能画像",
  info: [
    {
      title: "",
      info: "综合评分：72.4（中等偏上，但存在显著短板）",
    },
    {
      title: "",
      info: "优势领域：结果绩效（78）、关键任务达成（85%）、数字化能力（75）",
    },
    {
      title: "",
      info: "致命短板：流程能力（55）、人员动力（56）、敬业度（57）",
    },
    {
      title: "",
      info: "结论：团队具备执行力但缺乏可持续性，人员状态拖累业务潜力。",
    },
  ],
});
const ai2Data = ref({
  t: "二、关键问题诊断",
  info: [
    {
      title: "1、人员维度危机（核心矛盾）",
      info: [
        {
          title: "",
          info: "动力与敬业度双低（56/57分）；",
        },
        {
          title: "",
          info: "可能原因：激励机制失效、工作重复性高、成长路径模糊或领导管理风格问题。",
        },
        {
          title: "",
          info: "销售团队特性：低动力直接导致客户挖掘深度不足、商机转化率下降。",
        },
        {
          title: "",
          info: "能力建设评分偏低（68.7）：反映培训体系或知识复用机制缺失，影响长期竞争力。",
        },
      ],
    },
    {
      title: "2、流程能力崩塌（55分，重大风险）",
      info: [
        {
          title: "",
          info: "典型表现：销售流程（如客户管理、报价审批）依赖人工经验，效率低下且易出错；",
        },
        {
          title: "",
          info: "关联影响：KPI达成率仅71%（低于关键任务85%），说明过程管理混乱导致结果不稳定；",
        },
      ],
    },
    {
      title: "3、AI能力未赋能业务（63分）",
      info: [
        {
          title: "",
          info: "数字化能力（75）与AI能力（63）分差显著；",
        },
        {
          title: "",
          info: "工具停留在数据记录层（如CRM基础功能），未实现智能销售预测、客户画像分析等价值场景。",
        },
        {
          title: "",
          info: "人工重复劳动挤占销售核心时间（如数据录入、报表整理）。",
        },
      ],
    },
  ],
});
const ai3Data = ref([
  {
    title: "",
    info: "  ",
  },
]);
// const ai4Data = ref([
//   {
//     title: "",
//     info: "  ",
//   },
// ]);
const ai5Data = ref({
  t: "五、领导层行动清单",
  info: [
    {
      title: "",
      info: "立即启动：90天人员激励实验方案（覆盖70%团队）。",
    },
    {
      title: "",
      info: "关键决策：选择1个高痛点流程（如报价审批）进行AI改造试点。",
    },
    {
      title: "",
      info: "文化干预：负责人王伟需每周50%时间投入前线协同作战。",
    },
    {
      title: "",
      info: "警示：若流程能力与人员动力3个月内无改善，组织效能将滑坡至及格线以下，并引发人才流失风险。",
    },
  ],
});
</script>
<template>
  <div class="support_wrap">
    <div class="justify-start">
      <div class="left_menu_wrap marginR20">
        <div class="z_z">组织</div>
        <el-input
          class="marginB20"
          v-model="key"
          style="width: 100%; height: 30px"
          placeholder="按组织名称检索"
          :suffix-icon="Search"
        >
        </el-input>
        <Tree></Tree>
      </div>
      <div class="right_main">
        <div class="indicator_main">
          <div class="search_wrap">
            <div class="title marginB20">筛选</div>
            <div class="search_main">
              <el-form
                :inline="true"
                :model="searchData"
                class="demo-form-inline"
              >
                <el-form-item label="组织效能评分">
                  <el-select
                    v-model="searchData.num"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>
                <el-form-item label="结果绩效">
                  <el-select
                    v-model="searchData.result"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>
                <el-form-item label="能力建设">
                  <el-select
                    v-model="searchData.ability"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>

                <el-form-item label="KPI达成">
                  <el-select
                    v-model="searchData.kpi"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>

                <el-form-item label="关键任务">
                  <el-select
                    v-model="searchData.keyTask"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>

                <el-form-item label="流程能力">
                  <el-select
                    v-model="searchData.flow"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>

                <el-form-item label="组织能力">
                  <el-select
                    v-model="searchData.orgAbility"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>
                <el-form-item label="人员能力">
                  <el-select
                    v-model="searchData.staffAbility"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>
                <el-form-item label="人员动力">
                  <el-select
                    v-model="searchData.staffAbility"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>
                <el-form-item label="敬业度">
                  <el-select
                    v-model="searchData.engagement"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>
                <el-form-item label="数字化">
                  <el-select
                    v-model="searchData.digitization"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>
                <el-form-item label="AI能力">
                  <el-select
                    v-model="searchData.AIAbility"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="onSubmit">查询</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <div class="number_area_list justify-start">
            分值：
            <div class="item_wrap" v-for="(item, index) in numberArea">
              <span
                class="icon"
                :class="{
                  act: index == 0,
                  act1: index == 1,
                  act2: index == 2,
                  act3: index == 3,
                  act4: index == 4,
                }"
              ></span
              >{{ item.num }}
            </div>
          </div>
          <Table
            :roundBorder="false"
            :columns="columns"
            :data="data"
            headerColor
            showIndex
          >
            <template v-slot:fSlot="scope">
              <span class="circle" :class="circleColor(scope.row.f)">{{
                scope.row.f
              }}</span>
            </template>
            <template v-slot:gSlot="scope">
              <span class="circle" :class="circleColor(scope.row.g)">{{
                scope.row.g
              }}</span>
            </template>
            <template v-slot:hSlot="scope">
              <span class="circle" :class="circleColor(scope.row.h)">{{
                scope.row.h
              }}</span>
            </template>
            <template v-slot:iSlot="scope">
              <span class="circle" :class="circleColor(scope.row.i)">{{
                scope.row.i
              }}</span>
            </template>
            <template v-slot:jSlot="scope">
              <span class="circle" :class="circleColor(scope.row.j)">{{
                scope.row.j
              }}</span>
            </template>
            <template v-slot:kSlot="scope">
              <span class="circle" :class="circleColor(scope.row.k)">{{
                scope.row.k
              }}</span>
            </template>
            <template v-slot:lSlot="scope">
              <span class="circle" :class="circleColor(scope.row.l)">{{
                scope.row.l
              }}</span>
            </template>

            <template v-slot:mSlot="scope">
              <span class="circle" :class="circleColor(scope.row.m)">{{
                scope.row.m
              }}</span>
            </template>
            <template v-slot:nSlot="scope">
              <span class="circle" :class="circleColor(scope.row.n)">{{
                scope.row.n
              }}</span>
            </template>
            <template v-slot:oSlot="scope">
              <span class="circle" :class="circleColor(scope.row.o)">{{
                scope.row.o
              }}</span>
            </template>
            <template v-slot:pSlot="scope">
              <span class="circle" :class="circleColor(scope.row.p)">{{
                scope.row.p
              }}</span>
            </template>
            <template v-slot:qSlot="scope">
              <span class="circle" :class="circleColor(scope.row.q)">{{
                scope.row.q
              }}</span>
            </template>

            <template v-slot:rSlot="scope">
              <el-button class="ai_btn" type="primary" plain round
                >详情</el-button
              >
              <el-button class="ai_btn" type="primary" plain round
                >对比</el-button
              >
              <el-button class="ai_btn" type="primary" plain round
                >AI解读</el-button
              >
            </template>
          </Table>
          <el-divider />
          <div class="page-title-line">组织效能AI解读（二级组织名称3）</div>
          <div class="dot_content_wrap">
            <div class="t marginB20">{{ aiData.t }}</div>
            <div class="item_wrap" v-for="item in aiData.info">
              <span class="icon"></span>
              <span class="title">{{ item.title }}</span>
              <span class="info">{{ item.info }}</span>
            </div>
          </div>

          <div class="">
            <div class="t marginB20">{{ ai2Data.t }}</div>
            <div class="dot_content_wrap" v-for="item in ai2Data.info">
              <div class="t marginB20">{{ item.title }}</div>
              <div class="item_wrap" v-for="item1 in item.info">
                <span class="icon"></span>
                <span class="title">{{ item1.title }}</span>
                <span class="info">{{ item1.info }}</span>
              </div>
            </div>
          </div>

          <div class="marginB20">三、根因推导</div>
          <Table
            :roundBorder="false"
            :columns="columns2"
            :data="data2"
            headerColor
          >
          </Table>
          <div class="marginT20 marginB20">四、改进策略建议</div>
          <div class="">1、人员活力提升（紧急项）</div>
          <div class=""></div>
          <div class="dot_content_wrap">
            <div class="t marginT20 marginB20 text_b">重构激励体系：</div>
            <div class="item_wrap">
              <span class="icon"></span>
              <span class="title"></span>
              <span class="info"
                >引入阶梯式佣金+非金钱激励（如快速晋升通道、高潜力客户分配权）。</span
              >
            </div>
            <div class="item_wrap">
              <span class="icon"></span>
              <span class="title"></span>
              <span class="info"
                >建立每日微认可机制（如企业微信点赞即时兑换福利）。</span
              >
            </div>
          </div>
          <div class="dot_content_wrap">
            <div class="t marginT20 marginB20 text_b">减轻事务性负担：</div>
            <div class="item_wrap">
              <span class="icon"></span>
              <span class="title"></span>
              <span class="info"
                >通过自动化工具释放20%以上手工操作时间（见流程优化部分）。</span
              >
            </div>
          </div>

          <div class="marginB20">2、流程再造</div>
          <div class="dot_content_wrap">
            <div class="item_wrap">
              <span class="icon"></span>
              <span class="title"></span>
              <span class="info"
                >关键动作：将5个核心环节线上化并植入AI审核节点，缩短成交周期30%。</span
              >
            </div>
          </div>

          <div class="">3、AI能力场景落地</div>
          <div class=""></div>
          <div class="dot_content_wrap">
            <div class="t marginT20 marginB20 text_b">优先部署：</div>
            <div class="item_wrap">
              <span class="icon"></span>
              <span class="title"></span>
              <span class="info"
                >智能销售助手：自动抓取客户舆情生成攻单策略。</span
              >
            </div>
            <div class="item_wrap">
              <span class="icon"></span>
              <span class="title"></span>
              <span class="info"
                >预测性配额管理：基于历史数据动态调整区域任务。</span
              >
            </div>
          </div>
          <div class="dot_content_wrap">
            <div class="t marginT20 marginB20 text_b">效果预期：</div>
            <div class="item_wrap">
              <span class="icon"></span>
              <span class="title"></span>
              <span class="info"
                >销售人均产能提升15%-20%，动力分提升至65+。</span
              >
            </div>
          </div>

          <div class="dot_content_wrap">
            <div class="t marginB20">{{ ai5Data.t }}</div>
            <div class="item_wrap" v-for="item in ai5Data.info">
              <span class="icon"></span>
              <span class="title">{{ item.title }}</span>
              <span class="info">{{ item.info }}</span>
            </div>
          </div>

          <div class="marginB20">六、效能提升路径图</div>
          <el-table
            :data="data3"
            :span-method="objectSpanMethod"
            border
            class="self_table"
          >
            <el-table-column
              v-for="item in columns3"
              :key="item.prop"
              :prop="item.prop"
              :label="item.label"
              :align="item.align ? item.align : 'center'"
            />
          </el-table>

          <div class="marginT20 marginB20">七、结语</div>
          <div>
            该组织正处于效率瓶颈期，需通过“流程AI化+激励重塑”打破现状。建议优先投入资源解决人员动力与流程漏洞问题，避免优势领域（结果绩效）被短板侵蚀。数据证明：提升流程能力10分，可带动KPI达成率增长8%-12%。
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.support_wrap {
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
}
.left_menu_wrap {
  width: 240px;
  padding: 8px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #c6dbf3;
  .z_z {
    margin-bottom: 10px;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    text-align: center;
    background: #e3effa;
    border-radius: 8px 8px 8px 8px;
  }
}
.right_main {
  flex: 1;
  overflow: hidden;
}

.indicator_main {
  :deep .el-table {
    .ai_btn {
      padding: 0 15px;
      height: 24px;
      font-size: 14px;
    }
    .circle {
      display: inline-block;
      width: 65px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      color: #fff;
      border-radius: 10px;
    }
    .bg1_b {
      background: #deeff9;
      color: #3d3d3d;
    }
    .bg2_b {
      background: #95d9f0;
    }
    .bg3_b {
      background: #65bbea;
    }
    .bg4_b {
      background: #2c89cd;
    }
    .bg5_b {
      background: #00659b;
    }
  }
  :deep .self_table {
    width: 100%;
    --el-font-size-base: 14px;
    --el-table-header-text-color: #93abcb;
    --el-table-border-color: #c6dbf3;
    --el-table-tr-bg-color: transparent;
    --el-table-header-bg-color: transparent;
    background-color: transparent;

    .el-table__header .el-table__cell {
      // 隐藏table底边框
      background: #eaf4ff;
    }
  }
  .tips {
    margin: 16px 0 40px;
    color: #94a1af;
    span {
      color: #3d3d3d;
    }
  }
  .number_area_list {
    font-size: 12px;
    .item_wrap {
      margin: 0 25px 24px 0;
      .icon {
        display: inline-block;
        margin: 0 5px 0 15px;
        width: 14px;
        height: 8px;
        &.act {
          background: #deeff9;
        }
        &.act1 {
          background: #95d9f0;
        }
        &.act2 {
          background: #65bbea;
        }
        &.act3 {
          background: #2c89cd;
        }
        &.act4 {
          background: #00659b;
        }
      }
    }
  }
  .chart_list_wrap {
    margin: 0 -6px;
    .item_wrap {
      margin: 0 5px;
      padding: 7px 9px;
      flex: 1;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      .chart_t {
        height: 29px;
        line-height: 29px;
        text-align: center;
        background: #e4eef6;
        border-radius: 5px 5px 5px 5px;
        color: #7a94ad;
      }
      .chart_box {
        width: 100%;
        height: 200px;
      }
    }
  }
  .section_box_wrap {
    background: #fff;
    .item_wrap {
      .item_title {
        margin: 10px 0 10px 0;
      }
      .info_item {
      }
    }
  }
  :deep .demo-form-inline {
    .el-form-item {
      width: 25%;
      margin-right: 0;
      .el-form-item__label {
        width: 130px;
        text-align: right;
      }
    }
  }
  .dot_content_wrap {
    margin: 0 0 40px 0;
    .item_wrap {
      .icon {
        margin: -2px 0px 0 15px;
      }
    }
  }
  .search_wrap {
    .title {
      color: #53a9f9;
    }
  }
}
</style>
