<template>
  <div class="edu_info_wrap">
    <div class="clearfix">
      <div class="page_second_title marginT_16">
        <span>职位招募需求</span>
      </div>
      <div class="edu_info_center marginT_8">
        <div class="edu_info_header">
          <div class="item index">序号</div>
          <div class="item item_icon_wrap">部门</div>
          <div class="item item_icon_wrap">职位</div>
          <div class="item item_icon_wrap">职层</div>
          <div class="item">招募原因</div>
          <div class="item item_icon_wrap">现有人员</div>
          <div class="item">建议编制</div>
          <div class="item item_icon_wrap">空缺人数</div>
          <div class="item">当前需求人数</div>
          <div class="item">紧急程度</div>
          <div class="item">期望到岗日期</div>
          <!-- <div class="item">期望到岗周期</div> -->
        </div>
        <div class="edu_info_mmain">
          <person-info-item :eduInfoData="eduInfoData"></person-info-item>
          <div class="align_center marginT_30">
            <el-button
              class="page_confirm_btn"
              type="primary"
              @click="prevStep"
              v-show="currentIndex != currentFirstCode"
              >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="submit('nextStep')">{{ nextBtnText }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { saveJobInfo, getJobInfoList } from '../../../request/api'
import personInfoItem from './PRpersonInfoItem.vue'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  currentIndex: Number,
  currentFirstCode: Number
})

const emit = defineEmits(['nextStep', 'prevStep'])

const userStore = useUserStore()

// 响应式状态
const submitFlag = ref(true)
const eduInfoData = ref([])

// 计算属性
const userId = computed(() => userStore.userInfo.userId)

// 方法
const submit = async stepType => {
  if (!submitFlag.value) return
  if (checkData(eduInfoData.value)) {
    ElMessage.warning('请完善数据后提交！')
    return
  }
  submitFlag.value = false
  try {
    const params = {
      enqId: props.enqId,
      enqJobRequests: eduInfoData.value
    }
    const res = await saveJobInfo(params)
    if (res.code == '200') {
      ElMessage.success('保存成功!')
      await getJobInfoListData()
      submitFlag.value = true
      emit(stepType)
    } else {
      submitFlag.value = true
      ElMessage.error('保存失败!')
    }
  } catch (error) {
    submitFlag.value = true
    ElMessage.error('保存失败!')
  }
}

const getJobInfoListData = async () => {
  try {
    const res = await getJobInfoList({
      enqId: props.enqId
    })
    if (res.code == '200') {
      eduInfoData.value = res.data
    } else {
      ElMessage.error('获取数据失败!')
    }
  } catch (error) {
    ElMessage.error('获取数据失败!')
  }
}

const checkData = data => {
  const arr = data
  const len = arr.length
  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    if (
      !obj.recruitmentReason ||
      !obj.budgetedCount ||
      !obj.recruitCount ||
      !obj.recruitEmergency ||
      !obj.expectArrivalDate
    ) {
      return true
    }
  }
  return false
}

const prevStep = async () => {
  try {
    await ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '保存',
      cancelButtonText: '放弃修改'
    })
    await submit('prevStep')
  } catch (action) {
    ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
    if (action == 'cancel') {
      emit('prevStep')
    }
  }
}

// 生命周期钩子
onMounted(() => {
  getJobInfoListData()
})
</script>

<style scoped lang="scss">
.edu_info_wrap {
  margin-bottom: 16px;
}
.edu_info_header {
  .item {
    text-align: center;
    width: 11%;
    // padding-left: 15px;
    &.index {
      width: 4%;
      text-align: center;
    }
  }

  .item_icon_wrap {
    text-align: center;
    width: 8%;
  }
}
</style>
