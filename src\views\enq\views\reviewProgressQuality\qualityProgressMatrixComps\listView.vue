<template>
  <div class="list_view_wrap">
    <div class="department_level_wrap" v-if="searchViewSign">
      <span>显示部门层级</span>
      <el-select v-model="layerNo" clearable placeholder="请选择" >
        <el-option
          v-for="item in layerNoOption"
          :key="item.codeName"
          :label="item.codeName"
          :value="item.dictCode"
        ></el-option>
      </el-select>
      <!-- <el-select v-model="departmentLevel" placeholder="请选择部门层级"  @change="changeLevel">
                <el-option label="一级" value="1"></el-option>
                <el-option label="二级" value="2"></el-option>
            </el-select> -->
    </div>
    <tableComponent
      :tableData="tableData"
      :needIndex="true"
      @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"
    >
      <template v-slot:oper>
        <el-table-column label="维护进度" width="250">
          <template v-slot="scope">
            <div class="completion_rate flex_row_between">
              <div class="bar_wrap">
                <div
                  class="bar_progress"
                  :class="
                    scope.row.progress < 50
                      ? 'bg_low'
                      : scope.row.progress < 70
                        ? 'bg_normal'
                        : scope.row.progress < 90
                          ? 'bg_middle'
                          : 'bg_high'
                  "
                  :style="{ width: scope.row.progress + '%' }"
                ></div>
              </div>
              <div
                class="completion_rate_num"
                :class="
                  scope.row.progress < 50
                    ? 'color_low'
                    : scope.row.progress < 70
                      ? 'color_normal'
                      : scope.row.progress < 90
                        ? 'color_middle'
                        : 'color_high'
                "
              >
                {{ scope.row.progress }}%
              </div>
            </div>
          </template>
        </el-table-column>
      </template>
      <template v-slot:oper2>
        <el-table-column label="维护质量" width="250">
          <template v-slot="scope">
            <div class="completion_rate flex_row_between">
              <div class="bar_wrap">
                <div
                  class="bar_progress"
                  :class="
                    scope.row.quality < 50
                      ? 'bg_low'
                      : scope.row.quality < 70
                        ? 'bg_normal'
                        : scope.row.quality < 90
                          ? 'bg_middle'
                          : 'bg_high'
                  "
                  :style="{ width: scope.row.quality + '%' }"
                ></div>
              </div>
              <div
                class="completion_rate_num"
                :class="
                  scope.row.quality < 50
                    ? 'color_low'
                    : scope.row.quality < 70
                      ? 'color_normal'
                      : scope.row.quality < 90
                        ? 'color_middle'
                        : 'color_high'
                "
              >
                {{ scope.row.quality }}%
              </div>
            </div>
          </template>
        </el-table-column>
      </template>
    </tableComponent>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getOrgLayNo } from '../../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const props = defineProps({
  searchViewSign: Boolean
})

const route = useRoute()
const enqId = route.query.enqId
const layerNo = ref('')
const layerNoOption = ref([])

const tableDeptData = reactive({
  columns: [
    {
      label: '部门名称',
      prop: 'name'
    },
    {
      label: '部门负责人',
      prop: 'departmentHead',
      className: 'align_center'
    },
    {
      label: '盘点人数',
      prop: 'personnelNum',
      className: 'align_center'
    }
  ],
  data: [
    {
      name: '销售一部',
      departmentLevel: '1',
      departmentHead: '王伟',
      personnelNum: 50,
      quality: 72,
      progress: 89.9
    },
    {
      name: '销售二部',
      departmentLevel: '1',
      departmentHead: '王伟',
      personnelNum: 55,
      quality: 55,
      progress: 92
    },
    {
      name: '销售三部二级部门',
      departmentLevel: '2',
      departmentHead: '王伟',
      personnelNum: 50,
      quality: 50,
      progress: 80
    },
    {
      name: '销售四部二级部门',
      departmentLevel: '2',
      departmentHead: '王伟',
      personnelNum: 75,
      quality: 75,
      progress: 95
    },
    {
      name: '销售五部二级部门',
      departmentLevel: '2',
      departmentHead: '王伟',
      personnelNum: 85,
      quality: 80,
      progress: 65
    }
  ],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})

const tableStaffData = reactive({
  columns: [
    {
      label: '部门名称',
      prop: 'name'
    },
    {
      label: '姓名',
      prop: 'departmentHead',
      className: 'align_center'
    },
    {
      label: '岗位',
      prop: 'personnelNum',
      className: 'align_center'
    }
  ],
  data: [
    {
      name: '销售一部',
      departmentLevel: '1',
      departmentHead: '王伟',
      personnelNum: 50,
      quality: 72,
      progress: 89.9
    },
    {
      name: '销售二部',
      departmentLevel: '1',
      departmentHead: '王伟',
      personnelNum: 55,
      quality: 55,
      progress: 92
    },
    {
      name: '销售三部二级部门',
      departmentLevel: '2',
      departmentHead: '王伟',
      personnelNum: 50,
      quality: 50,
      progress: 80
    },
    {
      name: '销售四部二级部门',
      departmentLevel: '2',
      departmentHead: '王伟',
      personnelNum: 75,
      quality: 75,
      progress: 95
    },
    {
      name: '销售五部二级部门',
      departmentLevel: '2',
      departmentHead: '王伟',
      personnelNum: 85,
      quality: 80,
      progress: 65
    }
  ],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})

const tableData = reactive({
  columns: [],
  data: [],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})

// 部门层级
const getOrgLayNoFun = async () => {
  try {
    const res = await getOrgLayNo({ enqId })
    layerNoOption.value = []
    if (res.length > 0) {
      layerNoOption.value = res.map(item => ({
        codeName: item,
        dictCode: item
      }))
    }
  } catch (error) {
    console.error(error)
  }
}

const handleSizeChange = val => {
  tableData.page.size = val
}

const handleCurrentChange = val => {
  tableData.page.current = val
}

onMounted(() => {
  if (props.searchViewSign) {
    // 部门视角
    Object.assign(tableData, tableDeptData)
  } else {
    // 人员视角
    Object.assign(tableData, tableStaffData)
  }
  getOrgLayNoFun()
})
</script>

<style scoped lang="scss">
.list_view_wrap {
  .department_level_wrap {
    margin: 8px 0 20px;
    span {
      display: inline-block;
      margin: 0 8px 0 0;
    }
  }
  .completion_rate {
    .bar_wrap {
      width: calc(100% - 60px);
      height: 18px;
      background: #ebf4ff;
      position: relative;
      padding-top: 5px;
      .bar_progress {
        background: #70da88;
        height: 8px;
        width: 50%;
        &.bg_high {
          background: #70da88;
        }
        &.bg_middle {
          background: #0099ff;
        }
        &.bg_normal {
          background: #fbb62d;
        }
        &.bg_low {
          background: #ec6941;
        }
      }
    }
    .completion_rate_num {
      font-weight: bold;
      &.not_login {
        color: #ff6d6d;
      }
      &.color_high {
        color: #00b050;
      }
      &.color_middle {
        color: #0099ff;
      }
      &.color_normal {
        color: #fbb62d;
      }
      &.color_low {
        color: #ec6941;
      }
    }
  }
}
</style>
