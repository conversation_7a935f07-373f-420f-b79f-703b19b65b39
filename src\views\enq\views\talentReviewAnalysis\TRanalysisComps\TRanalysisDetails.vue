<template>
  <div class="analysis_details_wrap">
    <div class="page_main_title">
      <div class="goback_geader" @click="router.push('/talentReviewHome/talentReviewAnalysis/TRanalysis')">
        <i class="el-icon-arrow-left"></i>返回
      </div>
      人才盘点分析
    </div>
    <div class="page_second_title marginL_16">{{ enqName }} {{ formatDate(rcreateTime) }}</div>
    <div class="page_section _center clearfix">
      <tabsLink :tabsData="tabsLinkData"></tabsLink>
      <div class="details_content">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getEnqInfo } from '../../../request/api'
import tabsLink from '@/components/talent/tabsComps/tabsLink'

const router = useRouter()
const route = useRoute()

const enqName = ref('')
const rcreateTime = ref('')

const formatDate = dateString => {
  if (!dateString) return ''
  return dateString.split(' ')[0]
}

const tabsLinkData = [
  {
    id: 'asdfqteqa',
    key: 'talentNumber',
    name: '人才数量',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentNumber'
  },
  {
    id: 'asdqrefa',
    key: 'talentStructure',
    name: '人才结构',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentStructure'
  },
  {
    id: 'asdffdgha',
    key: 'talentQuality',
    name: '人才质量',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentQuality'
  },
  {
    id: 'asewrqdfa',
    key: 'talentOrg',
    name: '人才编制',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentOrg'
  },
  {
    id: 'asdffdasa',
    key: 'talentRisk',
    name: '人才风险',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentRisk'
  },
  {
    id: 'asdfadsfa',
    key: 'talentOptimize',
    name: '人才优化',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentOptimize'
  }
]

const getEnqInfoFun = async enqId => {
  try {
    const res = await getEnqInfo({ id: enqId })
    if (res.code == 200) {
      enqName.value = res.data.enqName
      rcreateTime.value = res.data.rcreateTime
    }
  } catch (error) {
    console.error('获取盘点信息失败:', error)
  }
}

onMounted(() => {
  const query = route.query
  const enqId = query.enqId

  tabsLinkData.forEach(item => {
    item.path = `${item.path}?enqId=${enqId}`
  })

  getEnqInfoFun(enqId)

  let path = tabsLinkData[0].path
  if (query.routeIndex !== undefined) {
    path = tabsLinkData[query.routeIndex].path
  }

  router.push({
    path: path,
    query: { enqId: enqId }
  })
})
</script>

<style lang="scss">
.details_content {
  padding-top: 16px;
}
.detail_main_aside {
  float: left;
  width: 150px;
}
.detail_main_content {
  overflow-x: hidden;
  padding: 0 16px 16px;
}
.aside_filter {
  float: left;
  width: 200px;
}
.talent_number_content {
  overflow-x: hidden;
  align-items: flex-start;
  &.page_section {
    padding: 0 16px !important;
  }
  .content_item {
    padding: 0 5px 10px 5px;
    &_main {
      padding: 8px;
      min-height: 200px;
      border: 1px solid #dcdfe6;
    }
    &_title {
      font-size: 14px;
      color: #0099fd;
      margin-bottom: 10px;
    }
    &_content {
      min-height: 200px;
    }
  }
}
</style>
