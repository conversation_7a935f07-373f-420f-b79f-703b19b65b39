<template>
  <div class="task_confirmation_main marginT_8">
    <div class="page_second_title">人员发展</div>
    <div class="department_main marginT_8">
      <div class="personnel_item_wrap_left">
        <div
          class="personnel_item"
          v-for="(item, index) in personnelData"
          :class="{ completed: item.type == 'Y', curr: currIndex === index }"
          :key="index"
          @click="() => selectPersonnel(item, index)"
        >
          <span>{{ item.userName }}</span>
          <i class="icon el-icon-check" v-if="item.type == 'Y'"></i>
          <i class="icon disc" v-else></i>
        </div>
      </div>
      <div class="personnel_item_wrap_right">
        <div>
          <personDevelopMess :enqId="enqId" :personnelDevData="personnelDevData" @getTab="getTabFn"></personDevelopMess>
        </div>
      </div>
    </div>
    <div class="btn_wrap align_center marginT_30">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn" v-show="currentIndex != currentFirstCode"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="() => saveUserEvalFun('')">{{
        nextBtnText
      }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits } from 'vue'
import { getDirectSubordinates, getPersonnelDevelopment, personnelDevNext } from '../../../request/api'
import personDevelopMess from './personDevelopMess.vue'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const currIndex = ref(0)
const personnelData = ref([])
const personnelDevData = ref({})

const getTabFn = data => {
  if (data) {
    getDeptUserPostFun()
  }
}

const getDeptUserPostFun = () => {
  const params = {
    enqId: props.enqId,
    type: 'development'
  }
  getDirectSubordinates(params).then(res => {
    if (res.code == '200') {
      personnelData.value = res.data
      getPersonnelDevelopmentFn(personnelData.value[currIndex.value])
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const getPersonnelDevelopmentFn = data => {
  const params = {
    enqId: props.enqId,
    userId: data.userId
  }
  getPersonnelDevelopment(params).then(res => {
    if (res.code == '200') {
      personnelDevData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const saveUserEvalFun = prevStep => {
  const params = {
    enqId: props.enqId
  }
  personnelDevNext(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      if (prevStep == 'prevStep') {
        emit('prevStep')
      } else {
        nextBtn()
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const selectPersonnel = (item, index) => {
  currIndex.value = index
  getPersonnelDevelopmentFn(item)
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      saveUserEvalFun('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}

const nextBtn = () => {
  emit('nextStep')
}

onMounted(() => {
  getDeptUserPostFun()
})
</script>

<style scoped lang="scss">
.department_main {
  display: flex;
  .personnel_item_wrap_left {
    width: 200px;
    padding-right: 15px;
    border-right: 1px solid #ccc;
    .personnel_item {
      line-height: 30px;
      padding: 0 8px;
      color: #525e6c;
      font-size: 14px;
      background: #f8f8f8;
      margin-bottom: 5px;
      font-weight: bold;
      cursor: pointer;

      &.completed {
        color: #0099fd;
        background: #eef5fb;

        .icon {
          display: block;
        }
      }

      &.curr {
        background: #0099fd;
        color: #fff;

        .icon {
          display: block;
          color: #fff;

          &.disc {
            background: #fff;
          }
        }
      }

      .icon {
        float: right;
        font-weight: bold;
        line-height: 30px;
        text-align: center;
        color: #0099fd;

        &.disc {
          width: 8px;
          height: 8px;
          margin: 10px 4px 0 auto;
          border-radius: 50%;
          background: #ffc000;
        }
      }
    }
  }
  .personnel_item_wrap_right {
    padding-left: 15px;
    width: calc(100% - 200px);
    .project_item {
      padding: 10px 0;
    }
  }
}
</style>
