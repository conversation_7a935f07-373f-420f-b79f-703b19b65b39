<template>
    <div class="project_view_wrap">
        <div class="project_view_main page_one">
            <div class="page_two">
                <div class="back_btn page_third_title">
                    <div class="report_title">
                        {{evalName}}
                    </div>
                    <div
                        class="goback_geader"
                        v-link="
                            '/talentAssessment/assessmentReport/assessmentReport'
                        "
                    >
                        <i class="el-icon-arrow-left"></i>返回
                    </div>
                </div>
                <div class="">
                    <div class="filter_tree_wrap fl">
                        <div class="filter_tree_title">部门筛选</div>
                        <div class="filter_tree">
                            <treeCompRadio
                                :treeData="orgTreeData"
                                :labelKey="'name'"
                                @clickCallback="getOrgCode"
                            ></treeCompRadio>
                        </div>
                    </div>
                    <div class="report_container">
                        <div class=" page_two_top_wrap">
                            <!-- <div class="test_chart_box" id="test_chart_box"></div> -->
                            <div class="project_view_title flex_row_betweens">
                                <div>部门报告</div>
                                <div class="search_wrap">
                                    <el-input
                                        v-model="orgName"
                                        size="mini"
                                        placeholder="搜索"
                                        @keyup.enter.native="searchOrgReport"
                                    ></el-input>
                                    <span
                                        class="search_icon el-icon-search pointer"
                                        @click="searchOrgReport"
                                    ></span>
                                </div>
                            </div>
                            <table-component
                                :tableData="orgReportData"
                                @handleSizeChange="orgSizeChange"
                                @handleCurrentChange="orgPageChange"
                                :needIndex="true"
                            >
                                <template v-slot:oper>
                                    <el-table-column
                                        label="操作"
                                        width="100"
                                        align="center"
                                    >
                                        <template slot-scope="scope">
                                            <el-button
                                                class="page_add_btn"
                                                @click.native.prevent="
                                                    getOrgReportPdf(scope.row)
                                                "
                                                
                                                >报告生成
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </template>
                            </table-component>
                        </div>
                        <div class=" page_two_top_wrap">
                            <div class="project_view_title flex_row_betweens">
                                <div>个人报告</div>
                                <div class="search_wrap">
                                    <el-input
                                        v-model="uesrName"
                                        size="mini"
                                        placeholder="搜索"
                                        @keyup.enter.native="searchUserReport"
                                    ></el-input>
                                    <span
                                        class="search_icon el-icon-search pointer"
                                        @click="searchUserReport"
                                    ></span>
                                </div>
                            </div>
                            <table-component
                                :tableData="userReportData"
                                @handleSizeChange="userSizeChange"
                                @handleCurrentChange="userPageChange"
                                :needIndex="true"
                            >
                                <template v-slot:oper>
                                    <el-table-column
                                        label="操作"
                                        width="100"
                                        align="center"
                                    >
                                        <template slot-scope="scope">
                                            <el-button
                                                class="page_add_btn"
                                                @click.native.prevent="
                                                    getPersonReportPdf(
                                                        scope.row
                                                    )
                                                "
                                                
                                                >报告生成
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </template>
                            </table-component>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="chartBox">
            <img :src="chartImg" alt="" />
        </div>
        <div class="progress_mark align_center" v-show="showProgress">
            <el-progress
                type="circle"
                :percentage="percentage"
                :color="progressColor"
                :stroke-width="10"
            ></el-progress>
        </div>
    </div>
</template>

<script>
    import {
        getEvalInfo,
        scheduleTree,
        queryEvalReportList,
        queryEvalUserReportList,
        getRptChartList,
        getReportChartData,
        uploadRptChart,
        genReportPdf,
        downloadReportPdf,
    } from "../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import treeCompRadio from "@/components/talent/treeComps/treeCompRadio";
    import {
        echartsRenderPage,
        echartsToImg,
        setChartData,
    } from "../../../../../../public/js/echartsimg/echartsToImg";

    export default {
        name: "projectViewContent",
        components: {
            tableComponent,
            treeCompRadio,
        },
        data() {
            return {
                evalName:'',
                evalId: this.$route.query.evalId,
                orgRsize: 10,
                orgCurrPage: 1,
                orgName: "",
                orgCode:'',
                orgRtotal: 0,
                userRsize: 10,
                userCurrPage: 1,
                uesrName: "",
                userRtotal: 0,
                listData: [],
                keyWord: "",
                orgTreeData: [],
                orgReportData: {
                    columns: [
                        {
                            label: "报告名称",
                            prop: "orgName",
                            formatterFun: function (row) {
                                return row.orgName + "--测评报告";
                            },
                        },
                        {
                            label: "人数",
                            prop: "totalNumber",
                        },
                        {
                            label: "分数",
                            prop: "overallScore",
                        },
                    ],
                    data: [],
                    page: {},
                },
                userReportData: {
                    columns: [
                        {
                            label: "姓名",
                            prop: "userName",
                        },
                        {
                            label: "岗位",
                            prop: "postName",
                        },
                        {
                            label: "分数",
                            prop: "overallScore",
                        },
                    ],
                    data: [],
                    page: {},
                },
                //图表测试
                colorIndex: 0,
                chartImg: "",
                chartData: {
                    report: true,
                    lineLegend: [
                        {
                            legendKey: "scoreAva",
                            legendName: "实际平均分",
                        },
                        {
                            legendKey: "targetAva",
                            legendName: "目标平均分",
                        },
                    ],
                    lineData: [
                        { scoreAva: "25%", targetAva: "35%", name: "国宝1" },
                        { scoreAva: "25%", targetAva: "35%", name: "国宝2" },
                        { scoreAva: "25%", targetAva: "35%", name: "国宝3" },
                        { scoreAva: "25%", targetAva: "35%", name: "国宝4" },
                    ],
                    // needAverage: true,
                    legend: [
                        {
                            legendKey: "scoreAva",
                            legendName: "实际",
                        },
                        {
                            legendKey: "targetAva",
                            legendName: "目标",
                        },
                    ],
                    data: [
                        { scoreAva: "20%", targetAva: "20%", name: "国宝1" },
                        { scoreAva: "55%", targetAva: "30%", name: "国宝2" },
                        { scoreAva: "30%", targetAva: "40%", name: "国宝3" },
                        { scoreAva: "34", targetAva: "60%", name: "国宝4" },
                    ],
                },
                showProgress: false,
                percentage: 0,
                progressColor: "#0099ff",
            };
        },
        created() {
            // this.chartImg = echartsToImg("aaaa", "SolidPie", "350:350", this.chartData)
            this.getEvalInfoFun();
            this.scheduleTreeFun();
        },
        mounted() {
            // echartsRenderPage("test_chart_box", "XBar", 1000, 500, this.chartData);
        },
        methods: {
            searchOrgReport() {
                this.getOrgReport();
            },
            orgSizeChange(size) {
                this.orgRsize = size;
                this.getOrgReport();
            },
            orgPageChange(page) {
                this.orgCurrPage = page;
                this.getOrgReport();
            },
            getOrgReport() {
                let params = {
                    evalId: this.evalId,
                    size: this.orgRsize,
                    current: this.orgCurrPage,
                    orgName: this.orgName,
                    orgCode:this.orgCode
                };
                queryEvalReportList(params).then((res) => {
                    if (res.code == "200" && res.data) {
                        this.$set(this.orgReportData, "page", res.page);
                        this.$set(this.orgReportData, "data", res.data);
                    }
                });
            },
            getOrgCode(code) {
                console.log(code);
                this.initPageSize();
                this.orgCode = code;
                this.getOrgReport();
                this.getUserReport();
            },
            initPageSize(){
                this.orgRsize=10;
                this.orgCurrPage=1;
                this.userRsize=10;
                this.userCurrPage=1;
            },
            searchUserReport() {
                this.getUserReport();
            },
            userSizeChange(size) {
                this.userRsize = size;
                this.getUserReport();
            },
            userPageChange(page) {
                this.userCurrPage = page;
                this.getUserReport();
            },
            scheduleTreeFun() {
                scheduleTree({ evalId: this.evalId }).then((res) => {
                    console.log(res);
                    // this.orgTree = this.$util.formatterData(res);
                    this.orgTreeData = res;
                });
            },
            getUserReport() {
                let params = {
                    evalId: this.evalId,
                    size: this.userRsize,
                    current: this.userCurrPage,
                    userName: this.uesrName,
                    orgCode:this.orgCode
                };
                queryEvalUserReportList(params).then((res) => {
                    if (res.code == "200" && res.data) {
                        this.$set(this.userReportData, "page", res.page);
                        this.$set(this.userReportData, "data", res.data);
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            getEvalInfoFun(){
                getEvalInfo({evalId:this.evalId}).then(res => {
                    console.log(res);
                    this.evalName = res.evalName;
                })
            },
            //生成个人报告
            async getPersonReportPdf(row) {
                //显示进度环
                this.showProgress = true;
                let params = {
                    evalId: row.evalId,
                    objectId: row.objectId,
                    orgCode: row.orgCode,
                    postCode: row.postCode,
                    reportType: "1",
                };
                //获取报告图表列表
                await this.getRptChartListFun(params);
                console.log("图片上传结束");
                //生成pdf
                let res = await genReportPdf(params);
                this.percentage = 90;
                if (res.code == 200) {
                    this.percentage = 100;
                    //下载pdf
                    let downParams = {
                        fileType: "",
                        evalId: row.evalId,
                        objectId: row.objectId,
                        orgCode: row.orgCode,
                        postCode: row.postCode,
                    };
                    let downRes = await downloadReportPdf(downParams);
                    const blob = new Blob([downRes]);
                    const elink = document.createElement("a");
                    elink.download = `${row.userName}-个人报告.pdf`;
                    elink.style.display = "none";
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    URL.revokeObjectURL(elink.href); // 释放URL 对象
                    document.body.removeChild(elink);
                } else {
                    this.$msg.warning(res.msg);
                }
                //隐藏进度环
                this.showProgress = false;
                this.percentage = 0;
            },
            // 生成组织报告
            async getOrgReportPdf(row) {
                //显示进度环
                this.showProgress = true;
                let params = {
                    evalId: row.evalId,
                    orgCode: row.orgCode,
                    reportType: "2",
                };
                //获取报告图表列表
                await this.getRptChartListFun(params);
                console.log("图片上传结束");
                //生成pdf
                let res = await genReportPdf(params);
                this.percentage = 90;
                if (res.code == 200) {
                    this.percentage = 100;
                    //下载pdf
                    let downParams = {
                        fileType: "",
                        evalId: row.evalId,
                        orgCode: row.orgCode,
                    };
                    let downRes = await downloadReportPdf(downParams);
                    console.log(downRes);
                    const blob = new Blob([downRes]);
                    const elink = document.createElement("a");
                    elink.download = `${row.orgName}-组织报告.pdf`;
                    elink.style.display = "none";
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    URL.revokeObjectURL(elink.href); // 释放URL 对象
                    document.body.removeChild(elink);
                } else {
                    this.$msg.warning(res.msg);
                }
                //隐藏进度环
                this.showProgress = false;
                this.percentage = 0;
            },
            //获取报告图表列表
            async getRptChartListFun(params) {
                let res = await getRptChartList(params);
                console.log(res);
                if (res.code == 200) {
                    let result = [];
                    for (let i = 0; i < res.data.length; i++) {
                        let params = {
                            url: res.data[i].dataUrl,
                            data: res.data[i].rptChartParam,
                        };
                        result.push(getReportChartData(params));
                    }
                    this.percentage = 20;
                    //获取图表数据
                    await Promise.all(result).then(async (args) => {
                        let imagesArr = [];
                        args.forEach((item, index) => {
                            if (item.code == 200) {
                                let data = item.data;
                                if (
                                    !data ||
                                    (data.chartData &&
                                        data.chartData.length == 0) ||
                                    data.length == 0 ||
                                    (data.area && data.area.length == 0)
                                ) {
                                    console.log("############################");
                                    console.log(res.data[index].chartId);
                                    return;
                                }
                                if (res.data[index].chartType == "14") {
                                    if (data.chartData[0].length == 0) {
                                        console.log("############################");
                                        console.log(res.data[index].chartId);
                                        return;
                                    }
                                }
                                data['padding'] =  res.data[index]['padding'];
                                let renderTypeData = setChartData(
                                    res.data[index],
                                    data
                                );

                                if (res.data[index].chartType == "1") {
                                    renderTypeData.renderData.colorIndex = this
                                        .colorIndex++;
                                }
                                // console.log(res.data[index].chartId);
                                // if (res.data[index].chartId == "119GJ.GJ") {
                                //     console.dir(renderTypeData.renderData);
                                //     echartsRenderPage(
                                //         "test_chart_box",
                                //         "XBar",
                                //         400,
                                //         400,
                                //         renderTypeData.renderData
                                //     );
                                // }

                                //获取图表base64
                                imagesArr.push({
                                    baseImg: echartsToImg(
                                        this.$util.createRandomId(),

                                        renderTypeData.renderType,
                                        res.data[index].aspectRatio,
                                        renderTypeData.renderData
                                    ),
                                    chartId: res.data[index].chartId,
                                });
                            }
                        });
                        let result = [];
                        console.log(imagesArr);
                        imagesArr.forEach((item, index) => {
                            if (item.baseImg) {
                                let imgParams = {
                                    evalId: this.evalId,
                                    chartId: item.chartId,
                                    imgData: item.baseImg,
                                    objectId: params.objectId,
                                    orgCode: params.orgCode,
                                };
                                result.push(uploadRptChart(imgParams));
                                //上传图表图片
                            }
                        });
                        this.percentage = 50;
                        await Promise.all(result).then((args) => {
                            // console.log(args)
                            this.percentage = 80;
                        });
                    });
                } else {
                    this.$msg.warning(res.msg);
                    //隐藏进度环
                    this.showProgress = false;
                    this.percentage = 0;
                }
            },
        },
    };
</script>

<style scoped lang="scss">
    .project_view_wrap {
        .project_view_main {
            .project_view_title {
                margin: 0 0 8px 0;
                padding: 3px 8px;
                font-size: 16px;
                line-height: 28px;
                color: #0099fd;
                font-weight: bold;
                background-color: #f2f8ff;
                border-radius: 3px;
            }
        }
        .filter_tree_wrap {
            width: 220px;
            margin-right: 16px;
            .filter_tree_title {
                line-height: 40px;
                background-color: #f4f4f4;
                color: #525e6c;
                padding-left: 16px;
            }
            .filter_tree {
                border: 1px solid #e5e5e5;
                min-height: 400px;
                border-top: none;
                max-height: 500px;
                overflow: auto;
            }
        }
        .report_container {
            overflow: hidden;
        }
        .page_two_top_wrap{
            padding-bottom: 20px;
        }
        .page_one {
            .project_view_item {
                position: relative;
                padding: 16px 8px 16px;
                margin-bottom: 8px;
                border: 1px solid #d9d9d9;
                overflow: hidden;

                .item_index {
                    width: 50px;
                    font-weight: bold;
                    font-size: 20px;
                    color: #409eff;
                    text-align: center;
                }

                .item_content_wrap {
                    width: 50%;
                    padding: 0 8px;

                    .item_content {
                        // padding-right: 10px;
                        .item_content_list {
                            color: #525e6c;

                            .list_title {
                                font-weight: bold;
                                margin-bottom: 8px;
                            }

                            .list_num {
                                height: 20px;
                                line-height: 20px;
                                color: #409eff;
                                font-weight: bold;
                                font-size: 14px;
                            }
                        }

                        .project_name,
                        .time {
                            line-height: 28px;
                        }

                        .project_name {
                            color: #409eff;
                            font-weight: 700;
                            cursor: pointer;
                        }

                        .time {
                        }

                        .last_line_descript_wrap {
                            height: 28px;
                            line-height: 28px;
                            white-space: nowrap;

                            .left {
                                span {
                                    margin: 0 8px 0 0;
                                }

                                .company_name {
                                    width: 130px;
                                    cursor: pointer;
                                }
                            }

                            .right {
                                span {
                                    margin: 0 4px;
                                    display: inline-block;
                                }

                                .num {
                                    color: #409eff;
                                    font-weight: 700;
                                }
                            }
                        }
                    }
                }

                .progress_details {
                    width: 30%;
                    border-left: 1px solid #d9d9d9;
                }

                .item_oper {
                    width: 80px;
                    padding: 0 8px;
                    border-left: 1px solid #d9d9d9;
                    align-items: center;
                    color: #0099fd;
                    text-align: center;

                    .icon {
                        font-size: 30px;
                    }

                    &_list {
                        width: 100%;
                        cursor: pointer;
                    }
                }
            }
        }

        .page_two {
            .project_view_title {
                .search_wrap {
                    position: relative;

                    .search_icon {
                        position: absolute;
                        top: 7px;
                        right: 5px;
                        font-size: 14px;
                    }

                     .el-input__inner {
                        padding: 0 28px 0 10px;
                        font-size: 14px;
                    }
                }
            }

            .back_btn {
                padding: 0 16px;
                margin-bottom: 16px;
                text-align: right;
                height: 30px;
                line-height: 30px;
                .report_title{
                    float: left;
                    width: 50%;
                    text-align: left;
                    color: #212121;
                    font-weight: bold;
                    font-size: 14px;
                    padding-left: 5px;
                }
                .goback_geader {
                    cursor: pointer;
                    float: right;
                    text-align: right;
                    color: #0099FF;
                    font-weight: normal;
                }
            }

             .has-gutter {
                tr {
                    th {
                        // background: #f2f8ff;
                    }
                }
            }
        }
    }

    .progress_mark {
        position: fixed;
        padding-top: 300px;
        left: 0;
        top: 0;
        width: 100%;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        z-index: 99999;
        .el-progress__text {
            color: #fff;
        }
    }
    .test_chart_box {
        height: 500px;
        border: 1px solid red;
    }
</style>