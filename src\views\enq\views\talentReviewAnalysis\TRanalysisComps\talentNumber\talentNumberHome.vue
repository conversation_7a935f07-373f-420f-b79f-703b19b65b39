<template>
  <div class="detail_main">
    <div class="detail_main_aside">
      <div class="page_third_title">分析主题</div>
      <tabsLink :tabsData="tabsLinkData" :isVertical="true"></tabsLink>
    </div>
    <div class="detail_main_content">
      <div class="page_third_title">分析视图</div>
      <router-view :filterData="filterData"></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { queryOrgWithSubChildren, queryJobClassByOrg } from '../../../../request/api'
import tabsLink from '@/components/talent/tabsComps/tabsLink'

const route = useRoute()
const enqId = ref('')

const tabsLinkData = ref([
  {
    id: '1',
    name: '人员特征',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentNumber/personnelCharacter'
  },
  {
    id: '2',
    name: '组织分布',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentNumber/orgDistribution'
  },
  {
    id: '3',
    name: '岗位分布',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentNumber/postDistribution'
  },
  {
    id: '4',
    name: '岗位工作',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentNumber/postWork'
  },
  {
    id: '5',
    name: '教育经历',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentNumber/eduExperience'
  },
  {
    id: '6',
    name: '工作经历',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentNumber/workExperience'
  }
])

const filterData = reactive({
  orgData: [],
  jobClass: []
})

const queryOrgWithSubChildrenFun = async enqId => {
  try {
    const res = await queryOrgWithSubChildren({ enqId })
    if (res.code == 200) {
      filterData.orgData = res.data
    }
  } catch (error) {
    console.error(error)
  }
}

const queryJobClassByOrgFun = async () => {
  try {
    const res = await queryJobClassByOrg()
    if (res.code == 200) {
      filterData.jobClass = res.data
    }
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  enqId.value = route.query.enqId
  tabsLinkData.value.forEach(item => {
    item.path = `${item.path}?enqId=${enqId.value}`
  })
  queryOrgWithSubChildrenFun(enqId.value)
  queryJobClassByOrgFun()
})
</script>

<style lang="scss"></style>
