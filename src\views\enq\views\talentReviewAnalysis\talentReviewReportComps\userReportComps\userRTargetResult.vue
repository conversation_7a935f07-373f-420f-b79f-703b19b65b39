<template>
    <div class="report_section userR_target_result_wrap">
        <div class="page_third_title">目标与关键结果</div>
        <div class="userR_target_result_main">
            <div class="top_wrap">
                <div class="top_left_wrap marginB_32">
                    <!-- <div class="page_third_title">
                        <span>目标与关键结果得分</span>
                    </div> -->
                    <div class="top_left_main flex_row_around">
                        <li class="annulus_item">
                            <customProcess
                                :size="100"
                                :fontSize="20"
                                :strokeWidth="15"
                                :num="objectivesScore.objectiveScore"
                            />
                            <p>综合得分</p>
                        </li>
                        <li class="annulus_item">
                            <customProcess
                                :size="100"
                                :fontSize="20"
                                :strokeWidth="15"
                                :num="objectivesScore.objectiveSelfScore"
                            />
                            <p>自评</p>
                        </li>
                        <!-- <li class="annulus_item">
								<el-progress type="circle" stroke-linecap='square' :format='setItemText(82)' :percentage="82" :width='60' :height='60'></el-progress>
								<p>同级</p>
							</li> -->
                        <li class="annulus_item">
                            <customProcess
                                :size="100"
                                :fontSize="20"
                                :strokeWidth="15"
                                :num="objectivesScore.objectiveSubScore"
                            />
                            <p>上级</p>
                        </li>
                        <!-- <li class="annulus_item">
								<el-progress type="circle" stroke-linecap='square' :format='setItemText(82)' :percentage="82" :width='60' :height='60'></el-progress>
								<p>下级</p>
							</li> -->
                        <li class="annulus_item">
                            <customProcess
                                :size="100"
                                :fontSize="20"
                                :strokeWidth="15"
                                :num="objectivesScore.objectiveLeaderScore"
                            />
                            <p>分管领导</p>
                        </li>
                        <li class="last_annulus_item" v-if="objectivesScore.objectiveOverallMerit != null">
                            {{ objectivesScore.objectiveOverallMerit }}
                        </li>
                    </div>
                </div>
                <div class="top_right_wrap marginB_32">
                    <div class="ranking_wrap kpi">
                        <div class="page_third_title">
                            <span>目标与关键结果综合排名</span>
                        </div>
                        <ul class="ranking_main flex_row_betweens">
                            <li class="item_wrap">
                                <p class="title">全司排名</p>
                                <p class="number">
                                    <span class="weight">{{
                                        objectivesRanking.wholeRanking
                                    }}</span
                                    >/{{ objectivesRanking.whole }}
                                </p>
                            </li>
                            <li class="item_wrap">
                                <p class="title">本部门排名</p>
                                <p class="number">
                                    <span class="weight">{{
                                        objectivesRanking.orgRanking
                                    }}</span
                                    >/{{ objectivesRanking.org }}
                                </p>
                            </li>
                            <li class="item_wrap">
                                <p class="title">本职位排名</p>
                                <p class="number">
                                    <span class="weight">{{
                                        objectivesRanking.jobRanking
                                    }}</span
                                    >/{{ objectivesRanking.job }}
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="page_third_title">
                <span>目标与关键结果表现</span>
            </div>
            <div class="bottom_wrap">
                <div
                    class="reault_expression_item"
                    v-for="(item, index) in getObjectiveResult"
                    :key="index"
                >
                    <tableComponent
                        :tableData="item.enqObjectiveList"
                        :border="false"
                        :needPagination="false"
                        :needIndex="false"
                    ></tableComponent>
                    <tableComponent
                        :tableData="item.enqObjectiveResults"
                        :border="false"
                        :needPagination="false"
                        :needIndex="false"
                    ></tableComponent>
                </div>
                <div class="synthesize_wrap flex_row_betweens">
                    <div>目标与关键结果得分总汇：{{ objectiveScore }}</div>
                    <div>
                        目标与关键结果上级评价：{{ objectiveOverallMerit }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    // 目标结果
    import { getKeyResults } from "../../../../request/api";
    import customProcess from "@/components/talent/common/customProcess.vue";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    export default {
        name: "userRTargetResult",
        props: ["nextBtnText", "enqId", "userId", "postCode"],
        components: {
            tableComponent,
            customProcess,
        },
        data() {
            return {
                objectivesScore: "",
                objectivesRanking: "",
                getObjectiveResult: "",
                tableData: {
                    columns: [
                        {
                            label: "目标名称",
                            prop: "objectiveName",
                            width: "400",
                        },
                        {
                            label: "目标权重",
                            prop: "weight",
                            width: "",
                        },
                        {
                            label: "自评得分",
                            prop: "selfScore",
                            width: "",
                        },
                        {
                            label: "上级评价",
                            prop: "supScore",
                            width: "",
                        },
                        {
                            label: "领导评价",
                            prop: "leaderScore",
                            width: "",
                        },
                        {
                            label: "综合得分",
                            prop: "overallScore",
                            width: "",
                        },
                        {
                            label: "综合评价",
                            prop: "overallMerit",
                            width: "",
                        },
                    ],
                    data: [],
                },
                tableResultData: {
                    columns: [
                        {
                            label: "关键结果",
                            prop: "resultName",
                            width: "400",
                        },
                        {
                            label: "结果权重",
                            prop: "weight",
                            width: "",
                        },
                        {
                            label: "自评得分",
                            prop: "selfScore",
                            width: "",
                        },
                        {
                            label: "上级评价",
                            prop: "supScore",
                            width: "",
                        },
                        {
                            label: "领导评价",
                            prop: "leaderScore",
                            width: "",
                        },
                        {
                            label: "综合得分",
                            prop: "overallScore",
                            width: "",
                        },
                        {
                            label: "综合评价",
                            prop: "overallMerit",
                            width: "",
                        },
                    ],
                    data: [],
                },
                objectiveScore: "",
                objectiveOverallMerit: "",
            };
        },
        created() {},
        computed: {},
        mounted() {
            this.getKeyResultsFun();
        },
        methods: {
            setItemText(value) {
                return () => {
                    return value;
                };
            },
            getKeyResultsFun() {
                getKeyResults({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    // console.log(res)
                    if (res.code == 200) {
                        this.objectivesScore = res.data.objectivesScore;
                        this.objectivesRanking = res.data.objectivesRanking;
                        this.getObjectiveResult = res.data.getObjectiveResult.map(
                            (item) => {
                                return {
                                    enqObjectiveList: {
                                        columns: this.tableData.columns,
                                        data: item.enqObjectiveList,
                                    },
                                    enqObjectiveResults: {
                                        columns: this.tableResultData.columns,
                                        data: item.enqObjectiveResults,
                                    },
                                };
                            }
                        );
                        this.objectiveScore = res.data.objectiveScore;
                        this.objectiveOverallMerit = res.data.objectiveOverallMerit;
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .userR_target_result_wrap {
        padding: 0 10px;
        height: 480px;
        overflow: auto;
        pointer-events: auto;
        .userR_target_result_main {
            .top_wrap {
                .top_left_wrap {
                    .top_left_main {
                        .annulus_item {
                            text-align: center;
                            p {
                                margin: 10px 0 0 0;
                            }
                            .el-progress {
                                .el-progress__text {
                                    margin: 0 0 0 15%;
                                    width: 42px;
                                    height: 42px;
                                    line-height: 45px;
                                    background: #dae8fd;
                                    border-radius: 50%;
                                }
                            }
                        }
                        .last_annulus_item {
							display: flex;
							align-items: center;
							justify-content: center;
                            width: 100px;
                            height: 100px;
                            line-height: 80px;
                            text-align: center;
                            color: #008fff;
                            background: #dae8fd;
                            border-radius: 50%;
                        }
                    }
                }
                .top_right_wrap {
                    flex: 1;
                    .ranking_wrap {
                        .ranking_main {
                            padding-left: 16px;
                            .item_wrap {
                                flex: 1;
                                padding: 20px 0 0 16px;
                                height: 90px;
                                background: #dae8fd;
                                // text-align: center;
                                color: #008fff;
                                margin-right: 16px;
                                .title {
                                    font-weight: 600;
                                }
                                .number {
                                    margin: 16px 0 0 0;
                                    font-size: 14px;
                                    .weight {
                                        font-size: 18px;
                                        font-weight: 600;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .score_chart_wrap {
                .page_third_title {
                    margin: 15px 0;
                }
                .report_section_content {
                    border: 1px solid pink;
                    width: 100%;
                    height: 300px;
                }
            }
            .bottom_wrap {
                padding: 0 16px;
                .page_third_title {
                    margin: 15px 0;
                }
                .reault_expression_item {
                    border: 1px solid #dcdfe6;
                    margin: 0 0 10px 0;
                    .has-gutter {
                        th {
                            background: #fff !important;

                            .cell {
                                color: #008fff;
                            }
                        }
                    }
                    .el-table::before {
                        background: #fff;
                    }
                    .el-table {
                        tr {
                            background: #fff;
                        }
                        th {
                            border: none;
                        }
                        td {
                            border: none;
                        }
                    }
                }
                .synthesize_wrap {
                    color: #008fff;
                }
            }
        }
    }
</style>
