<template>
  <div class="dialogue">
    <div class="input-card">
      <!-- <div class="add-card" @click="goPath('/question')">
        <el-icon><CirclePlus /></el-icon>
        <span class="text">新建问数</span>
      </div> -->
      <Input ref="InputRef" @send="toDetails()" />
    </div>
  </div>
</template>
<script setup>
defineOptions({ name: 'Question' })
import Input from '@/components/AI/input.vue'
import { useDialogueStore } from '@/stores'

const router = useRouter()

const InputRef = ref(null)
const toDetails = () => {
  useDialogueStore().setFirst(InputRef.value.input)
  router.push(`/question/123`)
}

const goPath = path => {
  router.push(path)
}
//#endregion
</script>
<style lang="scss" scoped>
.dialogue {
  position: relative;
  height: 100%;
  .input-card {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    :deep(.input-main) {
      margin-top: 0;
    }
    .add-card {
      display: flex;
      align-items: center;
      gap: 4px;
      height: 34px;
      width: 104px;
      margin-bottom: 15px;
      padding: 0 10px;
      border: 1px solid #e7e4e4;
      border-radius: 12px;
      cursor: pointer;
      .el-icon {
        vertical-align: middle;
        color: #333;
      }
      .text {
        color: #333;
        font-size: 14px;
      }
      &:hover {
        .el-icon {
          color: #53a9f9;
        }
        .text {
          color: #53a9f9;
        }
      }
    }
  }
}
</style>
