<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getQualityEvaluation,
  getQualityEvaluationItem,
  saveSurveySubmit,
  saveDurveyUserResult,
  saveEvaluationComment
} from '../../../request/api'
import evaluateTableSelect from './evaluateTableSelect.vue'
import qualityEvaluationList from './qualityEvaluationList.vue'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const savaFlag = ref(true)
const currIndex = ref(0)
const oneLevelIndex = ref(0)
const personnelData = ref([])
const expectationPostOptions = ref({})
const defaultActive = ref('')
const defaultOpeneds = ref('')
const type = ref('quality')

function handleOpen(key, keyPath) {}
function handleClose(key, keyPath) {}

function getPotentialDataFn(data) {
  if (data) {
    let arr = []
    data.forEach(item => {
      arr.push({
        comment: item.comment,
        relationType: item.relationType,
        userId: item.userId,
        enqId: props.enqId,
        type: type.value
      })
    })
    saveEvaluationComment(arr).then(res => {
      if (res.code == '200') {
        ElMessage.success('保存成功!')
        getDeptUserPostFun()
      } else {
        ElMessage.error('保存失败!')
      }
    })
  }
}

function getChildDataFn(data) {
  if (data) {
    let params = {
      enqId: props.enqId,
      enqItemOptionRequests: data,
      modelId: personnelData.value[oneLevelIndex.value].childern[currIndex.value].modelId,
      moduleCode: personnelData.value[oneLevelIndex.value].childern[currIndex.value].moduleCode
    }
    saveSurveySubmit(params).then(res => {
      if (res.code == '200') {
        ElMessage.success(res.msg)
        if (currIndex.value < personnelData.value[oneLevelIndex.value].childern.length - 1) {
          currIndex.value++
        } else {
          if (oneLevelIndex.value < personnelData.value.length - 1) {
            currIndex.value = 0
            oneLevelIndex.value++
          } else if (oneLevelIndex.value == personnelData.value.length - 1) {
            currIndex.value++
          }
        }
        if (oneLevelIndex.value < personnelData.value.length - 1) {
          getDeptUserPostFun()
        } else {
          defaultActive.value = personnelData.value[oneLevelIndex.value].childern[currIndex.value].moduleCode
        }
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}

function getDeptUserPostFun() {
  let params = {
    enqId: props.enqId
  }
  getQualityEvaluation(params).then(res => {
    if (res.code == '200') {
      if (res.data && res.data.length != 0) {
        personnelData.value = res.data
        defaultActive.value = personnelData.value[oneLevelIndex.value].childern[currIndex.value].moduleCode
        if (oneLevelIndex.value < personnelData.value.length - 1) {
          getQualityEvaluationItemFun(personnelData.value[oneLevelIndex.value].childern[currIndex.value])
        }
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function checkData(data) {
  let arr = data
  let len = arr.length
  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    let childern = obj.childern
    for (let j = 0; j < childern.length; j++) {
      const row = childern[j]
      if (row['statusName'] == 'N') {
        return true
      }
    }
  }
  return false
}

function selectPersonnel(item, index, indexOne) {
  if (item.layerNo == 1) {
    if (checkData(personnelData.value)) {
      ElMessage.warning('请先答完以上题目！')
      return
    } else {
      currIndex.value = index
    }
  } else {
    currIndex.value = index
    oneLevelIndex.value = indexOne
    defaultActive.value = personnelData.value[oneLevelIndex.value].childern[currIndex.value].moduleCode
    if (indexOne < personnelData.value.length - 1) {
      getQualityEvaluationItemFun(item)
    }
  }
}

function getQualityEvaluationItemFun(item) {
  let params = {
    enqId: props.enqId,
    modelId: item.modelId,
    moduleCode: item.moduleCode
  }
  getQualityEvaluationItem(params).then(res => {
    console.log(res)

    if (res.code == 200) {
      expectationPostOptions.value = res.data
    } else {
      expectationPostOptions.value = {}
    }
  })
}

function submitForm() {
  if (checkData(personnelData.value)) {
    ElMessage.warning('请答完所有评价后提交！')
    return
  }
  let params = {
    enqId: props.enqId,
    type: 'quality',
    modelId: personnelData.value[oneLevelIndex.value].childern[currIndex.value].modelId
  }
  saveDurveyUserResult(params).then(res => {
    if (res.code == '200') {
      nextBtn()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function prevBtn() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submitForm()
    })
    .catch(action => {
      if (action === 'cancel') {
        ElMessage.info('已放弃修改并返回上一步')
        emit('prevStep')
      } else {
        ElMessage.info('取消返回上一步')
      }
    })
}

function nextBtn() {
  emit('nextStep')
}

watch(
  () => expectationPostOptions.value,
  val => {
    // 你可以在这里处理副作用
    // console.log(val)
  },
  { deep: true }
)

onMounted(() => {
  getDeptUserPostFun()
})
</script>

<template>
  <div class="task_confirmation_main PRquality_Evaluate_wrap marginT_16">
    <div class="page_second_title">能力评价</div>
    <div class="department_main marginT_8">
      <div class="personnel_item_wrap_left">
        <el-menu
          :default-active="defaultActive"
          class="left_menu_wrap el-menu-vertical-demo"
          :unique-opened="true"
          @open="handleOpen"
          @close="handleClose"
        >
          <el-sub-menu v-for="(item, index) in personnelData" :key="index" :index="item.parent.moduleCode">
            <template #title>
              <span>{{ item.parent.moduleName }}</span>
            </template>
            <el-menu-item
              v-for="(item1, index1) in item.childern"
              :index="item1.moduleCode"
              :key="index1"
              @click="() => selectPersonnel(item1, index1, index)"
            >
              {{ item1.moduleName }}
              <i
                class="icon disc"
                :class="{
                  completed: item1.statusName == 'Y'
                }"
              ></i>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </div>
      <div class="personnel_item_wrap_right">
        <div class="project_item" v-if="personnelData.length - 1 != oneLevelIndex">
          <span class="moduleName">
            {{
              (personnelData &&
                personnelData[oneLevelIndex]?.parent.moduleName +
                  '/' +
                  personnelData[oneLevelIndex]?.childern[currIndex]?.moduleName) ||
              ''
            }}：
          </span>
          <span class="item">{{ expectationPostOptions?.item || '' }}</span>
        </div>
        <div v-if="personnelData.length - 1 != oneLevelIndex">
          <evaluateTableSelect :types="'quality'" :dataFrom="expectationPostOptions" @getChildData="getChildDataFn" />
        </div>
        <div v-else>
          <qualityEvaluationList :enqId="props.enqId" :type="type" @getPotentialData="getPotentialDataFn" />
        </div>
        <div class="btn_wrap align_center">
          <el-button
            class="page_new_confirm_btn"
            type="primary"
            @click="prevBtn"
            v-show="props.currentIndex != props.currentFirstCode"
            >上一步</el-button
          >
          <el-button class="page_new_confirm_btn" type="primary" @click="submitForm">{{ props.nextBtnText }}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.department_main {
  display: flex;
  .personnel_item_wrap_left {
    width: 220px;
    padding-right: 5px;
    border-right: 1px solid #ccc;
    height: 600px;
    overflow: auto;
    .personnel_item {
      line-height: 30px;
      padding: 0 8px;
      color: #525e6c;
      font-size: 13px;
      background: #f8f8f8;
      margin-bottom: 5px;
      font-weight: bold;
      cursor: pointer;

      &.completed {
        color: #0099fd;
        background: #eef5fb;

        .icon {
          display: block;
        }
      }

      &.curr {
        background: #0099fd;
        color: #fff;

        .icon {
          display: block;
          color: #fff;

          &.disc {
            background: #fff;
          }
        }
      }
    }
    :deep(.left_menu_wrap) {
      border: none;
      width: 205px;
      .el-sub-menu__title {
        line-height: 35px;
        height: 35px;
        // border: 2px solid pink;
        padding: 0 !important;
        font-weight: 700;
        .el-sub-menu__icon-arrow {
          right: 0;
          color: #333;
          font-weight: 700;
        }
      }
      .el-sub-menu__title:hover {
        // background: #49aef2;
        color: #0099fd;
        .el-sub-menu__icon-arrow {
          color: #0099fd;
        }
      }
      .el-sub-menu {
        .el-menu-item {
          width: 190px;
          padding: 0 5px !important;
          height: 35px;
          line-height: 35px;
          // border: 2px solid gold;
          min-width: 190px;

          .icon {
            float: right;
            font-weight: bold;
            line-height: 30px;
            text-align: center;
            color: #0099fd;
            display: inline-block;
            &.disc {
              width: 8px;
              height: 8px;
              margin: 13px 4px 0 auto;
              border-radius: 50%;
              background: #ffc000;
            }
            &.completed {
              background: #0099fd;
            }
          }
        }
        .el-menu-item:hover {
          background: #0099fd;
          color: #fff;
          .icon {
            &.disc {
              background: #fff;
            }
          }
        }
        .is-active {
          background: #0099fd;
          color: #fff;
          .icon {
            color: #fff;
            &.disc {
              background: #fff;
            }
          }
        }
      }
    }
  }
  .personnel_item_wrap_right {
    padding-left: 15px;
    width: calc(100% - 220px);
    .project_item {
      padding: 10px 0;
      font-size: 14px;
      font-weight: bold;
      .moduleName {
        color: #0099ff;
      }
      .item {
        color: #333;
        white-space: pre-wrap;
      }
    }
    :deep(.el-table) {
      // max-height: 312px !important;
    }
    :deep(.el-table__body) {
    }
    :deep(.check_column_act) {
      .cell::after {
        content: '' !important;
      }
    }
  }
}
.PRquality_Evaluate_wrap {
}
</style>
