<template>
  <div class="projects_basic_info_wrap" :class="{ event_none: !isEdit }">
    <div class="projects_basic_info_center">
      <div class="from_wrap">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="150px"
          class="demo-ruleForm"
        >
          <el-form-item :span="12" label="测评起止日期" required>
            <el-col :span="8">
              <el-form-item prop="date">
                <el-date-picker
                  v-model="ruleForm.date"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item label="测评项目名称" prop="name">
            <el-input
              v-model="ruleForm.name"
              placeholder="请输入测评名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="测评类型" prop="region">
            <el-select
              v-model="ruleForm.region"
              placeholder="请选择测评类型"
              disabled
            >
              <el-option
                v-for="item in regionList"
                :label="item.codeName"
                :value="item.dictCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择模型" prop="model">
            <el-radio-group v-model="ruleForm.model">
              <el-radio v-for="item in modelCheckList" :label="item.modelId">{{
                item.modelName
              }}</el-radio>
              <!--                            <el-radio v-for="item in modelCheckList" :label="modelId">{{item.modelName}}</el-radio>-->
              <!--                            <el-radio v-for="item in modelCheckList" :label="modelId">{{item.modelName}}</el-radio>-->
              <!--                            <el-radio v-for="item in modelCheckList" :label="modelId">{{item.modelName}}</el-radio>-->
              <!--                            <el-radio v-for="item in modelCheckList" :label="modelId">{{item.modelName}}</el-radio>-->
            </el-radio-group>
            <!--                        <el-checkbox-group v-model="ruleForm.type">-->
            <!--                            <el-checkbox label="销售与市场能力测评" name="type" border></el-checkbox>-->
            <!--                        </el-checkbox-group>-->
          </el-form-item>
          <el-form-item label="测评项目说明" prop="desc">
            <el-input
              type="textarea"
              rows="5"
              resize="none"
              v-model="ruleForm.desc"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="marginT_30 align_center" v-if="isEdit">
      <el-button
        class="page_confirm_btn"
        type="primary"
        @click="submitForm('ruleForm')"
        >下一步</el-button
      >
    </div>
  </div>
</template>

<script>
import {
  getEvalInfo,
  getModelCheckList,
  getDictItem,
  getDictList,
  createEvalInfo,
} from "../../../request/api";

// import { getDictItem } from "@/views/entp/request/api";

export default {
  name: "projectsBasicInfo",
  components: {},
  props: ["evalId", "isEdit"],
  data() {
    return {
      copyEvalId: "",
      ruleForm: {
        name: "",
        region: "",
        date: [],
        model: "",
        desc: "",
      },
      regionList: [],
      modelCheckList: [],
      rules: {
        name: [
          {
            required: true,
            message: "请输入测评名称",
            trigger: "blur",
          },
        ],
        region: [
          {
            required: true,
            message: "请选择测评类型",
            trigger: "change",
          },
        ],
        date: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],

        model: [
          {
            required: true,
            message: "请选择测评模型",
            trigger: "change",
          },
        ],
        desc: [
          {
            required: true,
            message: "请填写测评项目说明",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    if (this.evalId) {
      this.getEvalInfoFun();
    }
  },
  mounted() {
    this.getDictListFun();
    this.getModelCheckListFun();
  },
  methods: {
    getEvalInfoFun() {
      getEvalInfo({
        evalId: this.evalId,
      }).then((res) => {
        console.log(res);
        this.evalStatus = res.evalStatus;
        this.ruleForm = {
          name: res.evalName,
          region: res.evalType,
          model: res.modelId,
          desc: res.evalDesc,
          date: [res.beginTime.split(" ")[0], res.endTime.split(" ")[0]],
        };
      });
    },
    getDictListFun() {
      getDictItem({
        dictId: "EVAL_TYPE",
      }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.regionList = res.data;
          this.ruleForm.region = "2"; //固定写死为岗能测评
        }
      });
    },
    getModelCheckListFun() {
      getModelCheckList().then((res) => {
        console.log(res);
        this.modelCheckList = res;
      });
    },
    createEvalInfoFun() {
      let buildId = "";
      this.modelCheckList.some((item) => {
        if (item.modelId == this.ruleForm.model) {
          buildId = item.buildId;
          return;
        }
      });
      let params = {
        evalId: this.evalId,
        beginTime: this.ruleForm.date[0],
        endTime: this.ruleForm.date[1],
        evalName: this.ruleForm.name,
        evalType: this.ruleForm.region,
        modelId: this.ruleForm.model,
        // buildId:buildId,
        evalDesc: this.ruleForm.desc,
      };
      console.log(params);
      createEvalInfo(params).then((res) => {
        console.log(res);
        if (res.code == 200) {
          // this.evalId = res.data;
          if (!this.evalId) {
            this.$emit("getEvalId", res.data);
          }
          this.$emit("nextStep");
        } else {
          ElMessage.error(res.msg);
        }
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log(this.ruleForm);
          this.createEvalInfoFun();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.el-input__inner {
  width: 300px;
}
.el-form-item__content .el-range-separator {
  width: 8%;
}

.el-radio {
  position: relative;
  padding: 0 30px 0 10px;
  height: 34px;
  border: 1px solid #e5e5e5;
  line-height: 34px;
  border-radius: 3px;
  margin-bottom: 16px;
  .el-radio__input {
    position: absolute;
    right: 5px;
    top: 4px;
    border-radius: 0;
    outline: none;
    .el-radio__inner {
      border: none;
    }
    &.is-checked {
      .el-radio__original {
        display: none;
      }
      .el-radio__inner {
        width: 16px;
        height: 9px;
        border: none;
        top: 2px;
        background: none;
        border-radius: 0;
        border-left: 2px solid #0099ff;
        border-bottom: 2px solid #0099ff;
        transform: rotate(-45deg);
      }
    }
  }
}
</style>