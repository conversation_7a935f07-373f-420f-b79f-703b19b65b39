<template>
  <div class="report_kpi_wrap">
    <table-component :tableData="tableData" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import tableComponent from '@/components/talent/tableComps/tableComponent.vue'

const tableData = ref({
  columns: [
    {
      label: '指标名称',
      prop: 'name',
      width: '400'
    },
    {
      label: '目标',
      prop: 'target',
      width: ''
    },
    {
      label: '实际表现',
      prop: 'realPerformance',
      width: ''
    },
    {
      label: '权重',
      prop: 'weight',
      width: ''
    },
    {
      label: '上级评价',
      prop: 'higherEvaluation',
      width: ''
    },
    {
      label: 'HR总监',
      prop: 'HRdirector',
      width: ''
    },
    {
      label: '综合评价',
      prop: 'synthesisEvaluate',
      width: ''
    }
  ],
  data: [
    {
      name: '姓名',
      target: '55天',
      realPerformance: '88',
      weight: '25%',
      higherEvaluation: '80',
      HRdirector: '80',
      synthesisEvaluate: 'S.杰出'
    }
  ]
})
</script>

<style scoped lang="scss">
.report_kpi_wrap {
  padding: 16px;
}
</style>
