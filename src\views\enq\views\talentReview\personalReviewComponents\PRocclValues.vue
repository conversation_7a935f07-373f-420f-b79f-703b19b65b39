<template>
  <div class="coord_wrap">
    <div class="page_second_title marginT_16">
      <span>职业价值观</span>
    </div>
    <div class="coord_content">
      <div class="coord_type">
        <div class="coord_type_content">
          <div class="coord_type_head">
            <div class="item name">诊断项目</div>
            <div class="item" v-for="item in resultOptions" :key="item.code">
              <div class="dict_name">{{ item.name }}</div>
            </div>
          </div>
          <div class="coord_type_main">
            <div class="row" v-for="(item, index) in itemList" :key="item.code">
              <div class="item name">{{ index + 1 }}、{{ item.name }}</div>
              <div class="item" v-for="list in resultOptions" :key="list.code">
                <div class="border-box" @click="() => selectChange(item, index, list.code)">
                  <el-icon class="el-icon-check" v-if="calcStatus(item, list.code)"><Check /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="text_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevStep" v-show="currentIndex != currentFirstCode"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="nextStep">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTopicList, saveWorkSurveySubmit } from '../../../request/api'
import { Check } from '@element-plus/icons-vue'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const modelId = ref(null)
const itemList = ref([])
const resultOptions = ref([])

onMounted(() => {
  getList()
})

function getList() {
  let params = {
    enqId: props.enqId,
    modelType: 'professional'
  }
  getTopicList(params).then(res => {
    let data = res.data
    if (res.code == '200') {
      resultOptions.value = data.resultList
      itemList.value = data.itemList
      modelId.value = data.modelId
    }
  })
}

function save(stepType) {
  let params = {
    enqId: props.enqId,
    modelId: modelId.value,
    enqItemOptionRequests: []
  }
  itemList.value.forEach(item => {
    let { code: itemId, selectedCodes: optionNbr, reasonCode: moduleCode } = item
    params.enqItemOptionRequests.push({
      itemId,
      moduleCode,
      optionNbr
    })
  })
  saveWorkSurveySubmit(params).then(res => {
    if (res.code == 200) {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function selectChange(item, index, code) {
  if (item.selectedCodes.includes(code)) {
    return
  } else {
    item.selectedCodes = [code]
  }
}

function calcStatus(item, code) {
  return item.selectedCodes.includes(code)
}

function hasEmptyData(data) {
  for (let index = 0; index < data.length; index++) {
    const obj = data[index]
    if (!obj.selectedCodes || obj.selectedCodes.length == 0) {
      ElMessage.warning(`项目${index + 1}未选择选项!`)
      return true
    }
  }
  return false
}

function prevStep() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      save('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}

function nextStep() {
  if (!hasEmptyData(itemList.value)) {
    save('nextStep')
  }
}
</script>

<style scoped lang="scss">
.coord_type_content {
  text-align: center;
  margin-bottom: 10px;
  .item {
    flex: 1;
  }
  .coord_type_head {
    display: flex;
    align-items: stretch;
    line-height: 24px;
    color: #0070c0;
    margin-bottom: 12px;
    background-color: #f2fbfe;
    border: 1px solid #c2e7fa;
    .item {
      flex: 0 0 120px;
      padding: 5px 10px;
      border-right: 1px solid #c2e7fa;
      &:last-of-type {
        border: none;
      }
      &.name {
        flex: 1;
        text-align: left;
      }
    }
  }
  .coord_type_main {
    .row {
      display: flex;
      align-items: center;
      line-height: 24px;
      border: 1px solid #c2e7fa;
      margin-bottom: -1px;
      .item {
        height: 34px;
        flex: 0 0 120px;
        padding: 5px 10px;
        border-right: 1px solid #c2e7fa;
        &:last-of-type {
          border: none;
        }
        &.name {
          flex: 1;
          text-align: left;
        }
      }
      .border-box {
        height: 24px;
        cursor: pointer;
        color: #0070c0;
        font-weight: bold;
        font-size: 18px;
      }
    }
  }
}
</style>
