<script setup>
defineOptions({ name: 'overview' })
const emits = defineEmits(['next'])
const route = useRoute()
const showModule = computed(() => {
  return route.path == '/AI/displayer/overview/module'
})
const activeIndex = ref('3')
const coreList = ref([
  {
    title: '【GTMB】销售营销2B'
  },
  {
    title: '【GTMC】销售营销2C'
  },
  {
    title: '【PLM】产品全生命周期管理'
  },
  {
    title: '【SCM-P&O】供应链 计划订单'
  },
  {
    title: '【SCM-P&S】供应链采购与供应商管理'
  },
  {
    title: '【SCM-M&PL】供应链制造与厂内物流'
  },
  {
    title: '【FPD】成套设备完美交付'
  },
  {
    title: '【E2E-QM】端到端质量管理'
  },
  {
    title: '【ABC】全业务链成本优化'
  },
  {
    title: '【DGA】目标落地论证'
  },
  {
    title: '【S&OP】销售与业务协同'
  },
  {
    title: '【O&PM】组织与人才管理'
  },
  {
    title: '【B&FM】预算与财务管理'
  },
  {
    title: '【PS&D】流程系统与数字化'
  }
])

const typeList = ref([
  {
    name: '整体能力',
    id: 1,
    value: 45.5,
    changeType: null
  },
  {
    name: '流程赋能',
    id: 2,
    value: 54.6,
    changeType: -1
  },
  {
    name: '组织赋能',
    id: 4,
    value: 54.9,
    changeType: 1
  },
  {
    name: '人岗赋能',
    id: 5,
    value: 55.0,
    changeType: 1
  },
  {
    name: '数字化赋能',
    id: 3,
    value: 33.5,
    changeType: 1
  },
  {
    name: 'AI赋能',
    id: 3,
    value: 32.5,
    changeType: -1
  }
])
const checkType = ref(typeList.value[0])
const switchType = item => {
  checkType.value = item
}
const moduleName = ref('')
const componentGroupName = ref('')
const componentName = ref('')
const setModuleName = name => {
  moduleName.value = name
}
const setComponentGroupName = name => {
  componentGroupName.value = name
}
const setComponentName = name => {
  componentName.value = name
}

const router = useRouter()
const back = () => {
  router.back()
  checkType.value = typeList.value[0]
}
</script>
<template>
  <div v-show="showModule">
    <div class="text-3 font-bold color-[#3d3d3d] mb-[12px]">点击核心能力</div>
    <div class="core-list">
      <div
        class="list"
        :class="{ active: activeIndex == index }"
        v-for="(list, index) in coreList"
        @click="activeIndex = index"
      >
        {{ list.title }}
      </div>
    </div>
  </div>

  <div class="page-title-line">查看能力显差</div>
  <div class="content-main-title">
    <SvgIcon v-show="!showModule" class="pointer" width="16px" height="16px" name="icon-back" @click="back()"></SvgIcon>
    {{ coreList[activeIndex].title }} / <span>{{ checkType.name }}</span
    ><span v-if="moduleName"> / {{ moduleName }}</span
    ><span v-if="componentGroupName"> / {{ componentGroupName }}</span
    ><span v-if="componentName"> / {{ componentName }}</span>
  </div>
  <div class="page-content">
    <div class="type_item">
      <div
        class="type_list"
        :class="{ active: checkType.id == list.id }"
        @click="switchType(list)"
        v-for="list in typeList"
      >
        <div class="type_list_title">{{ list.name }}</div>
        <div class="type_list_content">
          <el-progress type="circle" :width="100" :stroke-width="10" :percentage="list.value ? list.value : 0">
            <template #default="{ percentage }">
              <div class="percentage-label">能力指数</div>
              <div class="percentage-value text-5">{{ percentage }}%</div>
            </template>
          </el-progress>
        </div>
        <div class="type_trend">
          <div class="text">预警趋势</div>
          <div class="flex_row_center trend_line_box" v-if="list.changeType == null">
            <div class="trend_line"></div>
          </div>
          <div v-else class="arrow_icon flex_row_center">
            <SvgIcon name="icon-arrow-up" color="#95DC6B" v-if="list.changeType == 1"></SvgIcon>
            <div class="trend_line" v-if="list.changeType == 0"></div>
            <SvgIcon name="icon-arrow-down" color="#FF7D7D" v-if="list.changeType == -1"></SvgIcon>
          </div>
        </div>
      </div>
    </div>
    <router-view
      @setModuleName="setModuleName"
      @setComponentGroupName="setComponentGroupName"
      @setComponentName="setComponentName"
      @changeType="switchType"
    ></router-view>
  </div>
</template>
<style lang="scss" scoped>
.core-list {
  display: flex;
  flex-flow: row wrap;
  gap: 10px;
  margin-bottom: 30px;
  .list {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333333;
    width: calc((100% - 60px) / 7);
    height: 35px;
    padding: 0px 8px;
    border-radius: 5px 5px 5px 5px;
    line-height: 18px;
    background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #c6dbf3;
    text-align: center;
    cursor: pointer;
    &:hover,
    &.active {
      border: 1px solid #53acff;
      color: #40a0ff;
      box-shadow: 0px 0px 10px 0px rgba(124, 182, 237, 0.5);
    }
  }
}

.type_item {
  display: flex;
  align-items: flex-start;
  align-items: stretch;
  gap: 10px;
  margin-bottom: 18px;

  .type_list {
    flex: 1;
    padding: 18px;
    cursor: pointer;
    background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    &.active {
      box-shadow: 0px 4px 10px 0px #cad5e1;
      border: 1px solid #40a0ff;
    }

    .type_list_title {
      width: 185px;
      line-height: 30px;
      background: #eaf4ff;
      border-radius: 30px;
      text-align: center;
      font-weight: 500;
      font-size: 16px;
      color: #40a0ff;
      margin: 0 auto 20px;
    }
  }

  .type_list_content {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    .percentage-label {
      font-size: 16px;
      color: #999999;
      margin-bottom: 10px;
    }
    .percentage-value {
      font-size: 20px;
      font-weight: bold;
    }
  }
}

:deep(.type_trend) {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #373d41;
  text-align: center;
  gap: 10px;

  .text {
  }

  .trend_line_box {
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    height: 16px;
  }

  .trend_line {
    width: 14px;
    height: 4px;
    background: #2ce2ff;
  }

  .arrow_icon {
  }
}

.content-main-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #94a1af;
  line-height: 32px;
  margin-bottom: 10px;
  span:last-of-type {
    color: #3d3d3d;
    font-weight: 500;
  }
}
.pointer {
  cursor: pointer;
}
</style>
