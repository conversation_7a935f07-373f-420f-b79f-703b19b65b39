<script setup>
defineOptions({ name: 'scene' })
const emits = defineEmits(['next'])
const sceneList = ref([
  {
    id: 1,
    title: '市场洞察与趋势预判',
    background: '需快速响应市场变化，但缺乏系统化信息分析能力。',
    point: '企业是否具备持续收集行业数据、识别市场机会与威胁的能力。',
    question: '是否依赖主观经验判断？是否忽视新兴技术或政策对行业的影响？',
    meaning: '避免战略方向偏差，提前布局蓝海市场。'
  },
  {
    id: 2,
    title: '战略意图定义与共识构建',
    background: '创始人愿景未能有效的转化为团队共识，执行层目标模糊。',
    point: '战略目标是否清晰、量化？高层与员工是否理解一致？',
    question: '战略仅停留在口号，部门目标与公司战略脱节。',
    meaning: '确保全员行动对齐，减少资源内耗。'
  },
  {
    id: 3,
    title: '业务设计与商业模式创新',
    background: '传统业务增长乏力，需探索新盈利模式。',
    point: '是否重构客户价值主张？成本结构与收入来源是否可持续？',
    question: '商业模式复制竞争对手，缺乏差异化竞争力。',
    meaning: '突破增长瓶颈，提升盈利韧性。'
  },
  {
    id: 4,
    title: '创新焦点与机会管理',
    background: '资源有限，需聚焦高潜力创新领域。',
    point: '是否建立创新筛选机制？资源是否过度分散？',
    question: '跟风投入“风口”项目，忽视自身能力匹配度。',
    meaning: '优化创新投入产出比，降低试错成本。'
  },
  {
    id: 5,
    title: '战略解码与目标穿透',
    background: '战略目标未能分解为可执行的部门/个人任务。',
    point: '战略是否拆解为KPI？层级目标是否逻辑自洽？',
    question: '基层员工不知自身工作如何贡献战略。',
    meaning: '实现战略到执行的“纵向到底”穿透。'
  },
  {
    id: 6,
    title: '关键任务与资源统筹',
    background: '资源分散导致战略优先级任务推进缓慢。',
    point: '是否识别关键路径任务？资源分配是否符合战略权重？',
    question: '重要项目因资金/人力不足而延期。',
    meaning: '集中资源打“歼灭战”，保障战略落地。'
  },
  {
    id: 7,
    title: '组织协同与变革管理',
    background: '部门壁垒阻碍跨职能协作，变革阻力大。',
    point: '是否建立协同机制？变革中员工适应性如何？',
    question: '流程调整引发内部抵触，协作效率低下。',
    meaning: '打破组织僵化，提升敏捷响应能力。'
  },
  {
    id: 8,
    title: '执行体系与流程构建',
    background: '依赖人治，缺乏标准化流程导致执行波动。',
    point: '关键业务流程是否规范化？是否有PDCA闭环？',
    question: '同类任务重复出错，过度依赖个别员工。',
    meaning: '通过标准化提升执行确定性与效率。'
  },
  {
    id: 9,
    title: '动态校准与偏差修复',
    background: '外部环境突变导致原定战略失效。',
    point: '是否定期复盘战略进展？纠偏机制是否敏捷？',
    question: '战略僵化，未能及时调整导致业绩下滑。',
    meaning: '增强战略弹性，避免系统性风险。'
  }
])

const onNext = () => {
  emits('next')
}
</script>
<template>
  <div class="scene-content">
    <div class="scene-item" v-for="(item, index) in sceneList" :key="item.id">
      <div class="item-title">
        <div class="tag">场景{{ index + 1 }}</div>
        {{ item.title }}
      </div>
      <div class="item-content"><b>背景：</b>{{ item.background }}</div>
      <div class="item-content"><b>评估重点：</b>{{ item.point }}</div>
      <div class="item-content"><b>典型问题：</b>{{ item.question }}</div>
      <div class="item-content"><b>管理意义：</b>{{ item.meaning }}</div>
    </div>
  </div>
  <div class="next-btn" @click="onNext">已了解，查看下一个</div>
</template>
<style lang="scss" scoped>
.scene-content {
  display: flex;
  flex-flow: row wrap;
  align-items: stretch;
  gap: 20px;
  margin-bottom: 20px;
  .scene-item {
    width: calc(33.33% - 15px);
    padding: 28px 13px 28px 19px;
    background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    .item-title {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
      font-size: 16px;
      color: #333333;
      font-weight: 600;
      .tag {
        width: 60px;
        line-height: 21px;
        background: #53a9f9;
        border-radius: 3px 3px 3px 3px;
        text-align: center;
        color: #fff;
        font-size: 16px;
        font-weight: normal;
      }
    }
    .item-content {
      color: #666;
      font-size: 14px;
      line-height: 25px;
    }
  }
}
</style>
