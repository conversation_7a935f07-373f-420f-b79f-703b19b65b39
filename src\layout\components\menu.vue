<template>
  <div class="menu">
    <el-menu :default-active="route.path" class="menu-list" :collapse="false" @select="handleSelect">
      <el-menu-item index="/dialogue" @click="menuChange('/dialogue')">
        <el-icon>
          <img :src="ico01" alt="" />
        </el-icon>
        <span>新建对话</span>
      </el-menu-item>

      <div class="menu-main">
        <MenuItem v-for="item in menuList" :key="item.path" :item="item" />
      </div>
    </el-menu>

    <div class="history">
      <div class="title">30天内</div>
      <div class="history-list">
        <div
          class="hostory-item"
          :class="{ active: item.id == historyActive }"
          v-for="item in hostoryList"
          :key="item.id"
          @click="toDetails(item.id)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import MenuItem from './MenuItem.vue'
import ico01 from '@/assets/imgs/layout/01.webp'
import ico02 from '@/assets/imgs/layout/02.webp'
import ico02ac from '@/assets/imgs/layout/02_ac.webp'
import ico03 from '@/assets/imgs/layout/03.webp'
import ico03ac from '@/assets/imgs/layout/03_ac.webp'
import ico04 from '@/assets/imgs/layout/04.webp'
import ico04ac from '@/assets/imgs/layout/04_ac.webp'
import ico05 from '@/assets/imgs/layout/05.webp'
import ico05ac from '@/assets/imgs/layout/05_ac.webp'
import ico06 from '@/assets/imgs/layout/06.webp'
import ico06ac from '@/assets/imgs/layout/06_ac.webp'
import ico07 from '@/assets/imgs/layout/07.webp'
import ico07ac from '@/assets/imgs/layout/07_ac.webp'
import api from '@/api/index.js'
import { useRouter, useRoute } from 'vue-router'
import { ref, watch } from 'vue'

import entpRoutes from '@/views/entp/router/index.js'
import enqRoutes from '@/views/enq/router/index.js'
import evalRoutes from '@/views/eval/router/index.js'
console.log('enqRoutes', enqRoutes)

const router = useRouter()
const route = useRoute()
const menuList = [
  {
    name: '核心业务导航',
    meta: {
      name: '核心业务导航'
    },
    ico: ico02,
    ico_ac: ico02ac,
    path: '/core'
  },
  {
    name: '组织效能导航',
    meta: {
      name: '组织效能导航'
    },
    ico: ico02,
    ico_ac: ico02ac,
    path: '/org'
  },
  {
    name: '指标透视',
    meta: {
      name: '指标透视'
    },
    ico: ico03,
    ico_ac: ico03ac,
    path: '/indicator/home',
    children: [
      {
        name: '指标透视罗盘',
        meta: {
          name: '指标透视罗盘'
        },
        path: '/indicator/home'
      },
      {
        name: '参考指标库',
        meta: {
          name: '参考指标库'
        },
        path: '/indicator/libraryRefer/typical'
      },
      {
        name: '指标库维护',
        meta: {
          name: '指标库维护'
        },
        path: '/indicator/libraryTending'
      },
      {
        name: '目标值与实际值维护',
        meta: {
          name: '目标值与实际值维护'
        },
        path: '/indicator/targetTending'
      },
      {
        name: '指标诊断与根因分析',
        meta: {
          name: '指标诊断与根因分析'
        },
        path: '/indicator/diagnosticAnalysis'
      },
      {
        name: '指标趋势预测与风险预警',
        meta: {
          name: '指标趋势预测与风险预警'
        },
        path: '/indicator/trendRisk'
      },
      {
        name: '指标智能对标',
        meta: {
          name: '指标智能对标'
        },
        path: '/indicator/benchmarking/'
      },
      {
        name: '指标改善任务一览',
        meta: {
          name: '指标改善任务一览'
        },
        path: '/indicator/improveTasks'
      },
      {
        name: '指标改善效果追踪',
        meta: {
          name: '指标改善效果追踪'
        },
        path: '/indicator/indicatorImprove/'
      }
    ]
  },
  {
    name: 'AI诊断',
    meta: {
      name: 'AI诊断'
    },
    ico: ico04,
    ico_ac: ico04ac,
    children: [
      {
        name: 'AI诊断改善引擎',
        meta: {
          name: 'AI诊断改善引擎'
        },
        path: '/AI/home'
      },
      {
        name: '快速了解能力诊断',
        meta: {
          name: '快速了解能力诊断'
        },
        path: '/AI/diagnosis'
      },
      {
        name: '能力显差概览',
        meta: {
          name: '能力显差概览'
        },
        path: '/AI/displayer/overview/module'
      },
      {
        name: '靶点诊断仪（能力诊断）',
        meta: {
          name: '靶点诊断仪（能力诊断）'
        },
        path: '/AI/targetSpotDiagnosis'
      },
      {
        name: '靶点诊断仪（能力分析）',
        meta: {
          name: '靶点诊断仪（能力分析）'
        },
        path: '/AI/analysis'
      }
    ]
  },
  {
    name: '产业适配引擎',
    meta: {
      name: '产业适配引擎'
    },
    ico: ico05,
    ico_ac: ico05ac,
    path: '/industry'
  },
  {
    name: '价值实证案例库',
    meta: {
      name: '价值实证案例库'
    },
    ico: ico06,
    ico_ac: ico06ac,
    path: '/case'
  },
  {
    name: '管理增效工具箱',
    meta: {
      name: '管理增效工具箱'
    },
    ico: ico06,
    ico_ac: ico06ac,
    path: '/tools'
  },
  {
    name: '数据管理',
    meta: {
      name: '数据管理'
    },
    ico: ico06,
    ico_ac: ico06ac,
    path: '/dataManage',
    children: entpRoutes[0].children
  },
  {
    name: '人才盘点',
    meta: {
      name: '人才盘点'
    },
    ico: ico06,
    ico_ac: ico06ac,
    path: '/enq',
    children: enqRoutes[0].children
  },
  {
    name: '人才测评',
    meta: {
      name: '人才测评'
    },
    ico: ico06,
    ico_ac: ico06ac,
    path: '/eval',
    children: evalRoutes[0].children
  },
  {
    name: '我的关注',
    meta: {
      name: '我的关注'
    },
    ico: ico07,
    ico_ac: ico07ac,
    path: '/myFollow'
  }
]

function menuChange(path) {
  router.push(path)
}

function handleSelect(index) {
  // 菜单选择事件处理
  if (index !== '/dialogue') {
    menuChange(index)
  }
}

//#region 历史记录
const historyActive = ref('')
const hostoryList = ref([])
const getSessionList = () => {
  api.dialogue.sessionList().then(res => {
    if (res.code == 200) {
      hostoryList.value = res.data.slice(0, 3)
    }
  })
}
const toDetails = id => {
  router.push(`/dialogue/${id}`)
}
getSessionList()
watch(
  () => route.fullPath,
  (newPath, oldPath) => {
    // getSessionList()
    historyActive.value = route.params.id
  },
  {
    immediate: true
  }
)
//#endregion
</script>

<style lang="scss" scoped>
.menu {
  height: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;

  :deep(.el-menu) {
    border-right: none;
  }

  :deep(.el-menu-item),
  :deep(.el-sub-menu__title) {
    height: 40px;
    line-height: 40px;

    &:hover,
    &.is-active {
      background: rgba(134, 171, 211, 0.1);
      border-radius: 10px;
    }
  }

  :deep(.el-menu-item.is-active) {
    color: #53a9f9;
  }

  :deep(.el-icon) {
    img {
      width: 16px;
      height: 16px;
      margin-right: 10px;
    }
  }

  .menu-list {
    flex: 1;
    overflow-y: auto;
    padding: 20px 12px;
  }

  .history {
    padding: 0 12px 20px;

    .title {
      font-size: 12px;
      color: #999999;
      margin-bottom: 8px;
    }

    .history-list {
      .hostory-item {
        line-height: 40px;
        padding: 0 12px;
        cursor: pointer;
        font-size: 14px;
        color: #333333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        border-radius: 10px;

        &:hover,
        &.active {
          background: rgba(134, 171, 211, 0.1);
          color: #53a9f9;
        }
      }
    }
  }
}
</style>
