<template>
  <div class="view_model_wrap bg_write">
    <div class="page_main_title">
      <div class="goback_geader" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
      模型中心
    </div>
    <div class="view_page_section">
      <div class="second_title_rh align_right">
        <span class="tip_item">{{ modelTypeName }}</span>
        <span class="tip_name">{{ modelName }}</span>
        <span> {{ modelCreateTime }} </span>
      </div>
      <div class="flex_row_between marginT_30">
        <div class="view_model_lf">
          <div class="lf_top">
            <div class="page_second_title">
              <div>模型简介</div>
            </div>
            <div class="lf_top_main four_overflow_elps" :title="modelIntro">
              {{ modelIntro }}
            </div>
          </div>
          <div class="lf_bottom marginT_16">
            <div class="page_second_title">
              <div>模型能力词典构成</div>
            </div>
            <div class="chart_wrap chart_wrap_over1">
              <div id="chartWrap1" class="bar_chart"></div>
            </div>
          </div>
        </div>
        <div class="view_model_rh">
          <div class="page_second_title">
            <div>模型知识图谱</div>
          </div>
          <div class="chart_wrap chart_wrap_over2">
            <div id="chartWrap2" class="graph_chart"></div>
            <!-- <graphChartComponent :chartData="graphChartData" :width="510" :height="520"></graphChartComponent>-->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getPlatformModelInfo, getPlatformModelChartData } from '../../request/api'
// // import {echartsRenderPage} from "../../../../../public/js/echartsimg/echartsToImg"
// import graphChartComponent from "@/components/echartsComps/relationgraph/graphChartComponent";

export default {
  name: 'viewModel',
  components: {
    // graphChartComponent
  },
  data() {
    return {
      modelId: this.$route.query.modelId,
      modelIntro: '',
      modelName: '',
      modelCreateTime: '',
      modelTypeName: '',
      chartData: {
        data: []
      },
      graphChartData: {
        data: []
      }
    }
  },
  created() {},

  mounted() {
    this.getPlatformModelInfoFun()
    // echartsRenderPage("chartWrap1", "YBar", 400, 300, this.chartData)
    // echartsRenderPage("chartWrap2", "Graph", 510, 510, this.graphChartData)
  },
  methods: {
    async getPlatformModelInfoFun() {
      await getPlatformModelInfo({
        modelId: this.modelId
      }).then(res => {
        // console.log(res)
        this.modelIntro = res.modelDesc
        this.modelName = res.modelName
        this.modelCreateTime = res.rcreateTime.split(' ')[0]
        this.modelTypeName = res.modelTypeName
        this.graphChartData.data = []
        this.graphChartData.data.push({
          name: res.modelName,
          children: []
        })
        this.getPlatformModelChartDataFun()
      })
    },
    getPlatformModelChartDataFun() {
      getPlatformModelChartData({
        modelId: this.modelId
      }).then(res => {
        console.log(res)
        this.chartData.data = []
        if (res.length > 0) {
          // 柱状图
          res.forEach(item => {
            this.chartData.data.push({
              name: item.moduleName,
              value: item.count
            })
          })
          // echartsRenderPage("chartWrap1", "YBar", null, null, this.chartData)
          // 关系图
          // console.log(this.graphChartData.data[0].name)
          this.graphChartData.data[0].children = res.map(item => {
            return {
              name: item.moduleName + '$$' + item.moduleCode,
              children: item.childrenList.map(item1 => {
                return {
                  name: item1.moduleName + '$$' + item.moduleCode + item1.moduleCode
                }
              })
            }
          })
          console.log(this.graphChartData)
          // echartsRenderPage("chartWrap2", "Graph", null, null, this.graphChartData)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.view_model_wrap {
  .view_page_title {
    padding: 5px 10px;
    font-size: 14px;
    line-height: 28px;
    background-color: #f4f4f4;
    border-radius: 3px;
  }

  .view_page_section {
    padding: 20px;

    .second_title_rh {
      padding: 0 20px;
      height: 45px;
      line-height: 45px;
      background: #f4f4f4;

      span {
        padding: 0 10px;
        font-size: 14px;

        &.tip_item {
          padding: 4px 10px;
          border-radius: 3px;
        }

        &.tip_name {
          font-weight: bold;
        }
      }
    }

    .flex_row_between {
      align-items: normal;
    }

    .view_model_lf {
      width: 340px;

      .lf_top {
        .lf_top_main {
          margin-top: 20px;
          padding: 10px;
          height: 110px;
          border: 1px solid #e5e5e5;
          line-height: 24px;
        }
      }

      .lf_bottom {
        margin-top: 20px;
      }
    }

    .view_model_rh {
      width: 800px;
      height: 600px;
      padding-left: 20px;
    }
  }
  .chart_wrap_over1 {
    width: 100%;
    height: 400px;
    overflow-y: auto;
    .bar_chart {
      width: 100%;
      height: 100%;
    }
  }
  .chart_wrap_over2 {
    width: 100%;
    height: 600px;
    overflow: auto;
    .graph_chart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
