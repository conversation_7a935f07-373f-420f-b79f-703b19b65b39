<template>
    <div class="edu_info_wrap">
        <div class="page_second_title">
            工作履历
        </div>
        <div class="oper_btn_wrap">
            <el-button type="primary" class="page_add_btn" @click="addItem">新增</el-button>
        </div>
        <div class="edu_info_table">
            <div class="edu_info_center">
                <div class="edu_info_header">
                    <div class="item long">公司名称</div>
                    <div class="item long">开始日期</div>
                    <div class="item long">结束日期</div>
                    <div class="item">岗位职层</div>
                    <div class="item short">岗位类型</div>
                    <div class="item short">业务领域</div>
                    <div class="item short">同岗位</div>
                    <div class="item short">同行业</div>
                    <div class="item item_icon_wrap">操作</div>
                </div>
                <div class="edu_info_mmain">
                    <job-experence-item :workData="workData" v-on:deleteItem="deleteItem"></job-experence-item>
                </div>
            </div>
        </div>
        <div class="align_center padd_TB_30">
            <el-button type="primary" class="page_confirm_btn" @click="submit">确认</el-button>
        </div>
    </div>
</template>
 
<script>
import jobExperenceItem from "./CSjobExperenceItem";
import {
    getExperience,
    addExperience,
    delExperience
} from "../../../request/api";

export default {
    name: "CSjobExperence",
    components: {
        jobExperenceItem
    },
    data() {
        return {
            staffId: null, //待获取
            submitFlag: true, //是否可以调用确认接口状态
            workData: [
                // {
                    // {
                    //     companyName: "公司名字",
                    //     beginDate: "2019-7-15",
                    //     endDate: "2019-7-16",
                    //     jobLevelCode: "主管级",
                    //     jobClassCode: "团队管理",
                    //     bizDomainCode: "销售",
                    //     postRelated: "Y",
                    //     industryRelated: "N",
                    //     userId: "1"
                    // }
                // }
            ]
        };
    },
    created() {
        // 跟据路由信息判断是编辑还是新增
        this.type = this.$route.name == "editStaff" ? "edit" : "create";
        if (this.type == "edit") {
            console.log("编辑");
            let id = this.$route.query.userId;
            this.staffId = id;
            this.getExperienceData();
        }else{
            this.staffId = this.$store.state.createStaffId;
        }
        
    },
    methods: {
        deleteItem(item, index) {
            let that = this;
            if (!item.hasOwnProperty("experienceId")) {
                // 以当前行数据是否包含 educationId 字段判断是否是入库的数据
                // 未入库的数据直接删除，不调用接口
                console.log("不调用删除接口，直接删除");
                this.$confirm("确认删除此条信息？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                        this.workData.splice(index, 1);
                        this.$message({
                            type: "success",
                            message: "删除成功!"
                        });
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除"
                        });
                    });
            } else {
                let experienceId = item.experienceId;
                // 调用删除接口
                console.log("调用删除接口");
                this.$confirm("确认删除此条信息？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                        delExperience({
                            experienceId: experienceId,
                            userId: this.staffId
                        }).then(res => {
                            if (res.code == "200") {
                                this.$message({
                                    type: "success",
                                    message: "删除成功!"
                                });
                                // 删除当前数据
                                this.workData.splice(index, 1);
                            } else {
                                this.$message.error("删除失败！");
                            }
                        });
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除"
                        });
                    });
            }
        },
        addItem() {
            let obj = this.workData[this.workData.length - 1];
            let addObj = {
                companyName: "公司名字",
                beginDate: "2019-7-15",
                endDate: "2019-7-16",
                jobLevelCode: "主管级",
                jobClassCode: "团队管理",
                bizDomainCode: "销售",
                postRelated: "Y",
                industryRelated: "N",
                userId: this.staffId
            };
            addObj = {
                companyName: "",
                jobLevelCode: "",
                beginDate:'',
                endDate:'',
                jobClassCode: "",
                bizDomainCode: "",
                postRelated: "",
                industryRelated: "",
                dateCopy: [], // 开始-结束日期选择插件使用数据
                userId: this.staffId
            };
            if (!obj) {
                this.workData.push(addObj);
                return;
            }

            if (this.checkData(this.workData)) {
                this.$message({
                    message: "请完善当前数据后新增！",
                    type: "warning"
                });
                return;
            }
            this.workData.push(addObj);
        },
        submit() {
            let that = this;
            if (!this.submitFlag) return;
            if (this.checkData(this.workData)) {
                // this.$message.error("请完善数据后提交！");
                this.$message({
                    message: "请完善数据后提交！",
                    type: "warning"
                });
                return;
            }

            this.submitFlag = false;
			let backupData = this.$util.deepClone(this.workData);
			backupData.forEach(obj => {
				// obj.beginDate = obj.dateCopy[0];
				// obj.endDate = obj.dateCopy[1];
				if (typeof(obj.jobClassCode) == 'string') {
					obj.jobClassCode = obj.jobClassCode
				} else {
					obj.jobClassCode = obj.jobClassCode[obj.jobClassCode.length - 1];
				}
				if (typeof(obj.jobLevelCode) == 'string') {
					obj.jobLevelCode = obj.jobLevelCode
				} else {
					obj.jobLevelCode = obj.jobLevelCode[obj.jobLevelCode.length - 1];
				}
			});
            let params = backupData;
            addExperience(params).then(res => {
                if (res.code == "200") {
                    this.$message({
                        type: "success",
                        message: "保存成功!"
                    });
                    this.submitFlag = true;
                    this.getExperienceData();
                } else {
                    this.submitFlag = true;
                    this.$message.error("保存失败!");
                }
            });
        },
        getExperienceData() {
            getExperience({
                current: "1",
                size: "10",
                userId: this.staffId
            }).then(res => {
                console.log(res);
                if (res.code == "200") {
                    res.data.forEach(obj => {
                        // obj.dateCopy = [obj.beginDate, obj.endDate];
                    });
                    this.workData = res.data;
                }
            });
        },
        checkData(data) {
            // 非空字段
            let verifiArr = [
                "companyName",
                "jobLevelCode",
                "jobClassCode",
                "bizDomainCode",
                "postRelated",
                "industryRelated",
                "beginDate",
            ];
            // 校验数据 是否有空值
            let arr = data;
            let len = arr.length;
            // 校验数据中有没有空值
            for (let index = 0; index < len; index++) {
                const obj = arr[index];
                if (this.$util.objHasEmpty(obj, verifiArr)) {
                    // 检测到有空值跳出遍历
                    console.log("有空值");
                    return true;
                } else {
                    console.log("没有空值");
                    // return true;
                }
            }
        }
    }
};
</script>
 
<style scoped lang="scss">
    .page_second_title{
        margin: 10px 0;
        padding-bottom: 10px;
        border-bottom: 1px solid #e5e5e5;
        position: relative;
    }
    .edu_info_wrap{
        width: 100%;
        overflow: hidden;
    }
    .edu_info_table{
        width: 100%;
        overflow-x: auto;
    }
    .edu_info_center{
        // width: 1400px;
    }
.edu_info_header {
    .item {
        width: 15%;
        text-align: center;
        &.short{
            width:10%;
        }
    }
    .item_icon_wrap {
        text-align: center;
        width: 5%;
    }
}
</style>