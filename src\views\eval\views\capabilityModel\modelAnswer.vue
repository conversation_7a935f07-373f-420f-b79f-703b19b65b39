<template>
    <div class="model_answer_wrap bg_write">
        <div class="page_main_title clearfix">
            {{pageTitle}}
            <div class="goback_geader" @click="goBackBtn">
                <i class="el-icon-arrow-left"></i>返回
            </div>
        </div>
        <div class="model_answer_tips flex_row_betweens" v-if="!closeTipSign">
            <span class="tip">
                小提示：点击下方的能力模块和组件，快速定位至题目，已评估完成组件和模块显示绿色，未完成的显示灰色，当前模块和组件显示蓝色
            </span>
            <span class="el-icon-close" @click="closeTip"></span>
        </div>
        <div class="page_section">
            <div class="answer_surplus_wrap">
                <div class="answer_surplus_info_wrap flex_row_betweens" v-if=" unAnswerItemCount != 0">
                    <div class="unanswered">剩余<span>{{unAnswerItemCount}}</span>未答</div>
                    <div>仅显示未答   
                        <el-switch
                            v-model="showAnswerSign"
                            active-color="#449CFF"
                            inactive-color="#DCDCDC">
                        </el-switch>
                    </div>
                </div>
                <ul class="answer_surplus_list">
                    <li v-for="(item,index) in unAnswerItemIds"  @click="checkUnAnswer(item)" :class="{'check_UnAnswer':itemId == item}"><i></i>{{item}}</li>
                </ul>
                <div class="">
                    <div class="ablitity_dict_warp">
                        <div class="ablitity_dict_title">能力分类与词典</div>
                        <step-bar :stepData="stepData" :currentIndex="currentIndex" :needClick="needClick" @stepClick="stepClick"></step-bar>
                        <div class="dict_list_wrap">
                            <p class="flex_row_betweens">
                                <span class="icon" @click="dictListShow" v-if="dictListShowSign"><i class="el-icon-caret-top"></i>收起</span>
                                <span class="icon" @click="dictListShow" v-if="!dictListShowSign"><i class="el-icon-caret-bottom"></i>展开</span>
                                <span class="line"></span>
                            </p>
                            <ul class="dict_list" v-if="dictListShowSign">
                                <li v-for="(item,index) in dictList" @click="checkDict(item.moduleCode)" 
                                    :class="{'finish_check':item.select == true,'active_check':item.moduleCode == curCheckModuleCode}" 
                                    >
                                    <i></i>{{item.moduleName}}
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- <component :is="compArr[currentIndex]" :modelId="modelId" @prevStep="prev" @nextStep="next"></component> -->
                    <personality-traits @prevStep="prev" @nextStep="next" @submit="submit"
                        :currentItemId='currentItemId' :lastBuildId='lastBuildId' 
                        :switchAnswer='switchAnswer' :type ='type' :questionInfo='questionInfo'
                    ></personality-traits>
                     
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {getUserModule,getItemInfo,getEvalInfo} from "../../request/api"
    import stepBar from "@/components/talent/stepsComps/stepBar";
    import personalityTraits from './modelAnswerComponents/personalityTraits'
    export default {
        name: "modelAnswer",
        data(){
            return{
                type:this.$route.query.type ? this.$route.query.type : '',
                lastBuildId:this.$route.query.lastBuildId ? this.$route.query.lastBuildId : this.$route.query.evalId,
                showAnswerSign:false,
                unAnswerItemIds:[],
                dictListShowSign:true,
                closeTipSign:false,
                dictList:[],
                needClick:true,
                currentIndex:0,
                stepData: [],
                unAnswerItemCount:'',
                nextItemId:'',
                preItemId:'',
                itemId:'',
                curCheckModuleCode:'',
                questionInfo:{},
                // 参数部分
                currentItemId:null,
                moduleCode:'',
                switchAnswer:false,
                pageTitle:''
            }
        },
        components:{
            stepBar,
            personalityTraits,
        },
        created(){
            this.getUserModuleFun()
            this.getEvalInfoFun()
        },
        mounted(){
        },
        methods:{
            // 返回
            goBackBtn(){
                if(this.type == 'eval'){
                    //回测评项目列表
                    this.$router.push({
                        path: "/talentAssessment/talentAssessmentManagement/evaluationItemList"
                    })
                }else{
                    //回参与建模列表
                    this.$router.push({
                        path: "/talentAssessment/modelPart",
                    })
                }
            },
            //获取测评title
            getEvalInfoFun(){
                getEvalInfo({
                    evalId:this.lastBuildId
                }).then(res=>{
                    // console.log(res)
                    this.pageTitle=res.evalName
                })
            },
            //获取用户答题对应模块
            getUserModuleFun(){
                getUserModule({
                    evalId:this.lastBuildId,
                    switchAnswer:this.switchAnswer
                }).then(res=>{
                    // console.log(res)
                    this.stepData = []
                    res.forEach((item,index)=>{
                        this.stepData.push({
                            name:item.moduleName,
                            code:index,
                            moduleCode:item.moduleCode,
                            childrenList:item.childrenList,
                            select:item.select
                        })
                    })
                    this.getItemInfoFun('',false)
                    for(let i = 0;i<this.stepData.length;i++){
                        if(this.stepData[i].select){
                            this.stepData[i].state = "inProgress";
                            this.stepData[i].enqProgress = "Y";
                        }
                    }
                })
            },
            // 获取答题信息
            getItemInfoFun(val,saveModuleCodeSign){
                if(saveModuleCodeSign == false){
                    this.moduleCode = ''
                }
                getItemInfo({
                    currentItemId:this.currentItemId,
                    evalId:this.lastBuildId,
                    moduleCode:val ? val : this.moduleCode,
                    switchAnswer:this.switchAnswer
                }).then(res=>{
                    // console.log(res);
                    this.questionInfo = res
                    this.unAnswerItemCount = res.unAnswerItemCount;
                    this.unAnswerItemIds = res.unAnswerItemIds;
                    this.nextItemId = res.nextItemId
                    this.preItemId = res.preItemId
                    this.itemId = res.itemId
                    this.moduleCode = res.componentCode
                    this.curCheckModuleCode = res.componentCode
                    for(let i = 0;i<this.stepData.length;i++){
                        if(this.stepData[i].moduleCode == res.moduleCode){
                            this.dictList = this.stepData[i].childrenList
                            this.currentIndex = i
                        }
                    }
                })
            },
            // 关闭提示
            closeTip(){
                this.closeTipSign = !this.closeTipSign
            },
            //选择未答题
            checkUnAnswer(answerCode){
                if(this.itemId == answerCode){
                    return;
                }
                this.currentItemId = answerCode
                this.getItemInfoFun('',false)
            }, 
            // 选择能力分类
            stepClick(code ,index){
                this.currentIndex =index;
                this.dictList = this.stepData[index].childrenList
                this.currentItemId = null
                this.getItemInfoFun(this.stepData[index].moduleCode,'')
            },
            dictListShow(){
                this.dictListShowSign = !this.dictListShowSign
            },
            // 选择能力分类下某个词典
            checkDict(dictCode){
                if(this.moduleCode == dictCode){
                    return
                }
                this.currentItemId = null
                this.getItemInfoFun(dictCode,'')
            },
            next() {
                this.currentItemId = this.nextItemId
                this.getUserModuleFun()
            },
            prev() {
                this.currentItemId = this.preItemId
                this.getUserModuleFun()
            },
            submit() {
                if((this.unAnswerItemIds.length == 1 && this.unAnswerItemIds[0] ==this.itemId) || this.unAnswerItemIds.length == 0){
                    // 所有题目都答完
                    this.goBackBtn()
                    
                }else{
                    // 还有题目没答完
                    this.currentItemId = null
                    this.getUserModuleFun()
                }
            },
        },
        watch:{
            showAnswerSign(val){
                this.switchAnswer = val
                this.currentItemId = null
                // this.moduleCode = ''
                this.getUserModuleFun()
            }
        }
    }
</script>

<style scoped lang='less'>
.model_answer_wrap{
    .model_answer_tips{
        margin:0 16px 0 16px; 
        padding: 0 10px 0;
        color:#CE837A;
        background: #F7EFEE;
        font-size: 10px;
        line-height: 20px;
        .el-icon-close{
            height: 20px;
            line-height: 20px;
            cursor: pointer;
        }
    }
    .page_section{
        .answer_surplus_wrap{
            .answer_surplus_info_wrap{
                height: 30px;
                line-height: 30px;
                .unanswered{
                    span{
                        font-size: 16px;
                        color: #ED6942;
                        font-weight: 700;
                        display: inline-block;
                    }
                }
            }
        }
        .answer_surplus_list{
            li{
                margin: 0px 10px 0 0;
                display: inline-block;
                height: 30px;
                line-height: 30px;
                color: #C9C9C9;
                cursor: pointer;
                i{
                    margin: 0 3px 0 0;
                    line-height: 40px;
                    vertical-align:middle;
                    display: inline-block;
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background: #C9C9C9;
                }
            }
            .check_UnAnswer{
                color: #449CFF;
                i{
                    background: #449CFF;
                }
            }
        }
        .ablitity_dict_warp{
            margin: 5px 0 0 0;
            padding: 13px 17px;
            background: #EBF4FF;
            .ablitity_dict_title{
                width: 113px;
                height: 30px;
                font-size: 16px;
                font-weight: bold;
                color: #009AFF;
            }
            .dict_list_wrap{
                p{
                    .icon{
                        // text-align: left;
                        margin: 0 0 0 -5px;
                        cursor: pointer;
                        i{
                            font-size: 19px;
                            color: #449CFF;
                        }
                    }
                    .line{
                        flex: 1;
                        margin: 0 0 0 5px;
                        display: inline-block;
                        height: 12px;
                        border-bottom: 1px solid #C8E1FF;
                    }
                }
                .dict_list{
                    margin: 15px 0 0 0;
                    li{
                        margin: 0 10px 0 0;
                        display: inline-block;
                        // width: 125px;
                        height:35px;
                        color: #A0A0A0;
                        cursor: pointer;
                        i{
                            margin: 0 8px 0 0;
                            line-height: 40px;
                            vertical-align:middle;
                            display: inline-block;
                            width: 6px;
                            height: 6px;
                            border-radius: 50%;
                            background: #C9C9C9;
                        }
                    }
                    .finish_check{
                        color: #8CDA9E;
                        i{
                            background: #8CDA9E;
                        }
                    }
                    .active_check{
                        color: #009AFF;
                        i{
                            background: #009AFF;
                        }
                    }
                }
            }
        }
    }
}
.step_bar_wrap{
    
    .step_item{
        color: #B4D3F9;
    }
    .step_item::before{
        background: #D4E5FA;
    }
    .step_item::after{
        background: #D4E5FA;
    }
    .step_item_icon_wrap{
        .step_item_icon{
            background: #D4E5FA;
            .icon_num{
                background: #B4D3F9;
            }
        }
    }
    .completed{
        .step_item_icon_wrap .step_item_icon{
            background: #b0e6bc;
            .icon_num{
                background: #8CDA9E;
            }
        }
        .step_text{
            color: #8CDA9E;
        }
        &::after,
        &::before {
            background-color: #b0e6bc !important;
        }
    }
    .inProgress{
        .step_item_icon_wrap .step_item_icon{
            background: #449CFF;
            .icon_num{
                background: #449CFF;
            }
        }
        .step_text{
            color: #449CFF;
        }
        &::after,
        &::before {
            background-color: #0099FF !important;
        }
    }

}
</style>