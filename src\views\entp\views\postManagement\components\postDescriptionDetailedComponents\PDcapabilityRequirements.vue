<template>
  <div class="capability_requirements_main">
    <div class="page_second_title">能力要求</div>
    <div class="flex_row_wrap_start padd_LR_16">
      <div class="capability_item" v-for="item in capabilityData" :key="item.id">
        <div class="title flex_row_between">
          <div class="text bold">{{ item.moduleName }}</div>
          <div class="score_bar flex_row_between">
            <div class="flex_row_between expected">
              <div class="bar">
                <span class="bar_title">目标</span>
                <span class="bar_inside" :style="{ width: item.expectedScore + '%' }"></span>
              </div>
              <div class="score">{{ item.expectedScore }}</div>
            </div>
          </div>
        </div>
        <div class="capability_item_content clearfix">
          <div class="item" v-for="list in item.children" :key="list.id">
            <div class="title flex_row_between">
              <div class="text">{{ list.moduleName }}</div>
              <div class="score_bar flex_row_between">
                <div class="flex_row_between expected">
                  <div class="bar">
                    <span class="bar_inside" :style="{ width: list.expectedScore + '%' }"></span>
                  </div>
                  <div class="score">{{ list.expectedScore }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="align_center" v-if="capabilityData.length == 0">该职位没有参与过测评</div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { jobCapabilityRequirements } from '../../../../request/api'

const props = defineProps({
  jobCode: String
})

const capabilityData = ref([])

async function jobCapabilityFun() {
  if (!props.jobCode) return

  const res = await jobCapabilityRequirements({
    jobCode: props.jobCode
  })

  if (res.code == '200' && res.data) {
    capabilityData.value = res.data
  }
}

onMounted(jobCapabilityFun)
watch(() => props.jobCode, jobCapabilityFun)
</script>

<style scoped lang="scss">
.page_second_title {
  margin: 10px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
}
.capability_item {
  width: 260px;
  padding: 20px 16px 10px;
  margin: 0 16px 16px 0;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);

  .title {
    line-height: 22px;
    color: #0099fd;
    padding-bottom: 8px;
    margin-bottom: 8px;
    border-bottom: 1px solid #f2f2f2;
    .text {
      flex: 1;
    }
    .score_bar {
      flex: 1;
      align-items: center;
      font-size: 14px;
      .expected {
        .bar {
          .bar_title {
            color: #92d050;
          }
          .bar_inside {
            background-color: #92d050;
          }
        }
        .score {
          color: #92d050;
        }
      }
      .bar {
        position: relative;
        width: 57px;
        height: 6px;
        background: #ebf4ff;
        border-radius: 3px;
        margin-right: 3px;
        .bar_title {
          position: absolute;
          top: -20px;
          left: 10px;
          font-size: 12px;
          line-height: 1;
        }
        .bar_inside {
          position: absolute;
          width: 65%;
          height: 100%;
          top: 0;
          left: 0;
          background: #0099ff;
          border-radius: inherit;
        }
      }
    }
  }
}
</style>
