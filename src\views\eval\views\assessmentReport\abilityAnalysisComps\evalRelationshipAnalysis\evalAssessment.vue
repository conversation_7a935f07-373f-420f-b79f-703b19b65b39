<template>
    <div class="">
        <div class="page_third_title">360度评估
            <div class="fr">
                <el-button type="primary" @click="exportExcel" size="mini">导出</el-button>
            </div>
        </div>
        <table-component
            :tableData="tableData"
            :needIndex="true"
            :loading="loading"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
        ></table-component>
    </div>
</template>
 
<script>
    import { assessment,allExportData } from "../../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "evalAssessment",
        props: ["orgCode", "evalId"],
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading: false,
                tableData: {
                    columns: [
                        {
                            label: "姓名",
                            prop: "objectName",
                        },
                        {
                            label: "岗位",
                            prop: "postName",
                        },
                        {
                            label: "目标",
                            prop: "expectedScore",
                        },
                        {
                            label: "自评",
                            prop: "selfScore",
                        },
                        {
                            label: "下级评",
                            prop: "subScore",
                        },
                        {
                            label: "同级评",
                            prop: "peerScore",
                        },
                        {
                            label: "上级评",
                            prop: "supScore",
                        },
                        {
                            label: "他评",
                            prop: "otherScore",
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        components: {
            tableComponent,
        },
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
        },

        methods: {
            getData() {
                this.loading = true;
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                assessment(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                        this.loading = false;
                    } else {
                        this.loading = false;
                    }
                });
            },
            handleSizeChange(size) {
                this.pageSize = size;
                this.getData();
            },
            handleCurrentChange(current) {
                this.currPage = current;
                this.getData();
            },
            exportExcel(){
                let params = {
                    evalId:this.evalId,
                    orgCode:this.orgCode,
                    type:'poor'
                }
                allExportData(params).then(res => {
                    console.log(res);
                    this.$exportDownload(res.data,'360度评估&&自评他评差距');
                })
            }
        },
    };
</script>
 
<style scoped lang="scss">
</style>