<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Select } from '@element-plus/icons-vue'

const props = defineProps({
  maxHeight: {
    type: [String, Number],
    default: '312'
  },
  tableData: {
    type: Object,
    default: () => ({})
  },
  type: {
    type: String,
    default: 'radio'
  },
  numLength: {
    type: Number,
    default: 1
  }
})
const emit = defineEmits(['tableDataFn'])

const tableDom = ref(null)

const normalColumns = computed(() => (props.tableData.tableTitle || []).filter(item => !item.canCheck))
const checkColumns = computed(() => (props.tableData.tableTitle || []).filter(item => item.canCheck))

watch(
  () => props.tableData,
  () => {
    nextTick(() => {
      tableDom.value?.doLayout && tableDom.value.doLayout()
    })
  },
  { deep: true }
)

function cellClick(row, column, cell, event) {
  if (!row['targetKpiCode-' + column.property.split('-')[1]]) return
  if (row[column.property] !== true && row[column.property] !== false) return

  let checkKpiCode
  for (let i = 0; i < props.tableData.data.length; i++) {
    if (props.tableData.data[i].index === row.index) {
      if (props.type === 'radio') {
        // 单选
        props.tableData.data[i].selectedCodes = []
        for (let j = 0; j < props.tableData.tableTitle.length - props.numLength; j++) {
          props.tableData.data[i]['targetName-' + (j + 1)] = false
        }
        checkKpiCode = props.tableData.data[i].kpiCodeList[column.property.split('-')[1] - 1]
        props.tableData.data[i][column.property] = true
        props.tableData.data[i].selectedCodes.push(checkKpiCode)
      } else {
        // 多选
        checkKpiCode = props.tableData.data[i].kpiCodeList[column.property.split('-')[1] - 1]
        if (props.tableData.data[i][column.property] === true) {
          props.tableData.data[i][column.property] = false
          const idx = props.tableData.data[i].selectedCodes.indexOf(checkKpiCode)
          if (idx > -1) props.tableData.data[i].selectedCodes.splice(idx, 1)
        } else {
          props.tableData.data[i][column.property] = true
          props.tableData.data[i].selectedCodes.push(checkKpiCode)
        }
      }
      emit('tableDataFn', props.tableData.data)
    }
  }
}

function tableRowClassName({ row, column, rowIndex, columnIndex }) {
  if (row[columnIndex]?.label?.length > 5) {
    return 'check_column_act'
  }
}
</script>

<template>
  <div class="contanier_box">
    <div class="page_section_box">
      <el-table
        ref="tableDom"
        class="table_wrap"
        :data="tableData.data"
        style="width: 100%"
        :max-height="maxHeight"
        @cell-click="cellClick"
        :header-cell-class-name="tableRowClassName"
        border
      >
        <el-table-column
          v-for="item in normalColumns"
          :min-width="item.width"
          :label="item.label"
          :prop="item.prop"
          :key="item.code"
          :fixed="item.fixed"
          align="center"
        />
        <el-table-column
          v-for="item in checkColumns"
          :min-width="item.width"
          class-name="check_column"
          :show-overflow-tooltip="item.label.length > 5"
          :label="item.label"
          :prop="item.prop"
          :key="item.code"
          align="center"
        >
          <template #header="scope">
            <div>
              <span class="table_title_indexs" :title="scope.column.label">{{ scope.column.label }}</span>
            </div>
          </template>
          <template #default="scope">
            <span
              :class="{
                'el-icon-check': scope.row[item.prop],
                'el-icon-disabled_check': !scope.row[item.disabledSign],
                check_box_wrap: true
              }"
            >
              <el-icon v-if="scope.row[item.prop]"><Select /></el-icon>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style scoped lang="scss">
.contanier_box {
  overflow: auto;
  .page_section_box {
    padding-bottom: 20px;
    :deep(.table_wrap) {
      .el-table__row {
        height: 41px;
      }
    }
    :deep(.table_wrap tbody .cell) {
      cursor: pointer;
    }
    :deep(.table_wrap thead) {
      font-size: 12px;
      .check_column {
        .cell {
          // height: 30px;
          span {
            line-height: 18px;
            display: block;
            // height: 30px;
            // word-wrap: break-word;
            // letter-spacing: 20px;
          }
        }
      }
    }
    :deep(.table_wrap .check_box_wrap) {
      color: #0099fd;
      font-weight: 700;
    }
    .align_center {
      margin: 15px 0 0;
    }
    :deep(.table_wrap tbody) {
      :deep(.check_column) {
        .cell {
          width: 30px;
          height: 30px;
          padding: 0;
          margin: 5px 0 4px;
        }
        .check_box_wrap {
          display: inline-block;
          width: 28px;
          height: 28px;
          border: 1px solid #dcdfe6;
          color: #000;
          text-align: center;
          line-height: 28px;
          // // margin-right: 3px;
          cursor: pointer;
        }
        .check_box_wrap:hover {
          border: 1px solid #0099ff;
        }
        .el-icon-check {
          border: 1px solid #0099ff;
          color: #fff;
          background: #0099ff;
        }
        .el-icon-disabled_check {
          cursor: no-drop;
          background: #edf2fc;
        }
        .el-icon-disabled_check:hover {
          border: 1px solid #dcdfe6;
        }
      }
    }
    :deep(.check_column_act) {
      .cell::after {
        content: '';
        font-weight: bold;
        position: absolute;
        bottom: -0px;
        height: 9px;
        right: 0;
        left: -8px;
      }
    }
  }
}
</style>
