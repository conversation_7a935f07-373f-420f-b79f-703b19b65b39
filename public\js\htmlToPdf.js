import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'

/******
 * id: String 需要导出为pdf的dom对象
 * title: String pdf文件名
 * callback: 回调函数
 * singlepage: Boolean, 是否是单页pdf模式
 * 
 * eg：
 * this.getPdf(id,title,callback,singlepage)
 */

// 单页

export default {
    install(Vue, options) {
        /**
         *
         * @param {String || Array[String]} id 需要导出为pdf的dom对象 id, 按模块分页时 Arrar[String]
         * @param {String} title pdf文件名
         * @param {Boolean} singlepage 是否单页pdf模式
         * @param {Boolean} moduleCut 是否按模块分页，单页模式不生效
         * @param {Boolean} hasCover 是否有封面  封面单独一页 宽高比 1: 1.43
         * @param {Funciton} callback 回调函数
         */
        Vue.prototype.getPDF = function ({domId, title, singlepage = true, moduleCut = false, hasCover = false, callback,err}) {
            try {
                const ele = document.getElementById(domId);
                if (singlepage) {
                    // 单页pdf
                    // 获取dom高度、宽度
                    var width = ele.offsetWidth / 4
                    var height = ele.offsetHeight / 4

                    console.log(ele.offsetHeight);

                    html2Canvas(document.querySelector(`#${domId}`), {
                        // allowTaint: true
                        useCORS: true,//看情况选用上面还是下面的，
                        scale: 1
                    }).then(function (canvas) {
                        var context = canvas.getContext('2d')
                        context.mozImageSmoothingEnabled = false
                        context.webkitImageSmoothingEnabled = false
                        context.msImageSmoothingEnabled = false
                        context.imageSmoothingEnabled = false
                        var pageData = canvas.toDataURL('image/jpeg',)
                        console.log(pageData);
                        var img = new Image()
                        img.src = pageData

                        img.onload = function () {
                            // 获取dom高度、宽度
                            img.width = img.width / 2
                            img.height = img.height / 2
                            img.style.transform = 'scale(0.5)'
                            if (width > height) {
                                // 此可以根据打印的大小进行自动调节
                                // eslint-disable-next-line
                                var PDF = new JsPDF('l', 'mm', [
                                    width * 0.505,
                                    height * 0.545
                                ])
                            } else {
                                // eslint-disable-next-line
                                var PDF = new JsPDF('p', 'mm', [
                                    width * 0.505,
                                    height * 0.545
                                ])
                            }
                            PDF.addImage(
                                pageData,
                                'jpeg',
                                0,
                                0,
                                width * 0.505,
                                height * 0.545
                            )
                            callback();
                            PDF.save(title + '.pdf')
                        }
                    })
                } else {
                    // 多页
                    if(moduleCut){
                        toPdf(domId,title, hasCover, callback)
                        return
                    }
                    html2Canvas(ele, {
                        // allowTaint: true
                        useCORS: true//看情况选用上面还是下面的，
                    }).then(async function (canvas) {
                        // pdf边距留白
                        const pl = 10;
                        const pt = 10;
                        const PDF = new JsPDF("", "mm", "a4");

                        let renderedHeight = 0;
                        const ctx = canvas.getContext("2d");
                        //a4纸的尺寸[210mm,297mm]
                        const a4w = 210 - pl * 2;
                        const a4h = 297 - pt * 2; //A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
                        const imgHeight = Math.floor((a4h * canvas.width) / a4w); //按A4显示比例换算一页图像的像素高度

                        while (renderedHeight < canvas.height) {
                        const page = document.createElement("canvas");
                        page.width = canvas.width;
                        page.height = Math.min(imgHeight, canvas.height - renderedHeight); //可能内容不足一页

                        //用getImageData剪裁指定区域，并画到前面创建的canvas对象中
                        const imgData = ctx.getImageData(
                            0,
                            renderedHeight,
                            canvas.width,
                            Math.min(imgHeight, canvas.height - renderedHeight)
                        );
                        page.getContext("2d",{ willReadFrequently: true }).putImageData(imgData, 0, 0);
                        const pageData = page.toDataURL("image/jpeg", 1.0);
                        PDF.addImage(
                            pageData,
                            "JPEG",
                            pl,
                            pt,
                            a4w,
                            Math.min(a4h, (a4w * page.height) / page.width)
                        ); //添加图像到页面，保留10mm边距

                        renderedHeight += imgHeight;
                        if (renderedHeight < canvas.height) {
                            // 报告封面 第一页单独一页
                            PDF.addPage(); //如果后面还有内容，添加一个空页
                        }
                        // delete page;
                        }
                        await PDF.save(title + ".pdf", { returnPromise: true });
                        callback();
                    })
                }
            } catch (error) {
                err(error)
            }
        },
        // Vue.prototype.getDomPdf = async function (idArr, title, singlepage, callback) {
        //     console.log(idArr);
        //     let ajaxDom = [];
        //     idArr.forEach(id => {
        //         console.log(id);
        //         console.log(document.querySelector(`#${id}`));
        //         ajaxDom.push(html2Canvas(document.querySelector(`#${id}`)))
        //     });
        //     let canvasArr = await Promise.all(ajaxDom);
        //     console.log(canvasArr);
        //     let PDF = new JsPDF('', 'pt', 'a4')
        //     let position = 0
        //     // pdf边距留白
        //     let pl = 0;
        //     let pt = 0;

        //     canvasArr.forEach((canvas, index) => {
        //         if (index != 0) {
        //             pl = 20;
        //             pt = 30;
        //             PDF.addPage()
        //         }
        //         let canvasWidth = canvas.width
        //         let canvasHeight = canvas.height
        //         console.log(canvasWidth);
        //         // a4纸的宽高 592.28 841.89
        //         // a4纸内容显示的宽高 去除边距
        //         let a4W = 592.28 - (pl * 2);
        //         let a4H = 841.89 - (pt * 2);
        //         //一页pdf显示html页面生成的canvas高度;
        //         let pageHeight = canvasWidth / a4W * a4H

        //         //未生成pdf的html页面高度
        //         let leftHeight = canvasHeight;
        //         //页面偏移 
        //         //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
        //         let imgWidth = a4W
        //         let imgHeight = a4W / canvasWidth * canvasHeight
        //         let pageData = canvas.toDataURL('image/jpeg', 1.0)
        //         // console.log(pageData);
        //         //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
        //         //当内容未超过pdf一页显示的范围，无需分页
        //         if (leftHeight < pageHeight) {
        //             PDF.addImage(pageData, 'JPEG', pl, pt, imgWidth, imgHeight)
        //         } else {
        //             while (leftHeight > 0) {
        //                 let imgh = Math.min(imgHeight, pageHeight)
        //                 PDF.addImage(pageData, 'JPEG', pl, position + pt, imgWidth, imgh)
        //                 leftHeight -= pageHeight
        //                 position = position - 841.89
        //                 // position = position - a4H
        //                 //避免添加空白页
        //                 if (leftHeight > 0) {
        //                     PDF.addPage()
        //                 }
        //             }
        //         }
        //     })
        //     await PDF.save(title + ".pdf", { returnPromise: true });
        //     callback();
        // },
        // 多页pdf，按模块、高度分页
        /**
         * 
         * @param {Array} idArr 页面需要导出pdf的dom id 数组
         * @param {String} title pdf文件名称
         * @param {Boolean} hasCover 是否有封面
         * @param {Function} callback 生成完成后回调方法
         */
        Vue.prototype.toPdf = async function (idArr, title, hasCover, callback) {
            hasCover = hasCover || false
            let ajaxDom = [];
            idArr.forEach(id => {
                ajaxDom.push(html2Canvas(document.querySelector(`#${id}`)))
            });
            let canvasArr = await Promise.all(ajaxDom);
            console.log(canvasArr);
            var pdf = new JsPDF('p', 'mm', 'a4');    //A4纸，纵向
            // pdf边距留白
            let pl = 0;
            let pt = 0;

            canvasArr.forEach((canvas, index) => {
                if (index != 0) {
                    // pl = 10;
                    pt = 10;
                    pdf.addPage()
                }
                let renderedHeight = 0
                let ctx = canvas.getContext('2d');
                let a4w = 210 - (pl * 2); let a4h = 297 - (pt * 2);   //A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
                let imgHeight = Math.floor(a4h * canvas.width / a4w);    //按A4显示比例换算一页图像的像素高度

                while (renderedHeight < canvas.height) {
                    let page = document.createElement("canvas");
                    page.width = canvas.width;
                    page.height = Math.min(imgHeight, canvas.height - renderedHeight);//可能内容不足一页

                    //用getImageData剪裁指定区域，并画到前面创建的canvas对象中
                    let imgData = ctx.getImageData(0, renderedHeight, canvas.width, Math.min(imgHeight, canvas.height - renderedHeight));
                    page.getContext('2d').putImageData(imgData, 0, 0);
                    let pageData = page.toDataURL('image/jpeg', 1.0)
                    pdf.addImage(pageData, 'JPEG', pl, pt, a4w, Math.min(a4h, a4w * page.height / page.width));    //添加图像到页面，保留10mm边距

                    renderedHeight += imgHeight;
                    if (renderedHeight < canvas.height) {
                        if (index != 0 && hasCover) {
                            // 报告封面 第一页单独一页
                            pdf.addPage();//如果后面还有内容，添加一个空页
                        }
                    }
                    // delete page;
                }
            })
            callback();
            // pdf.save(title + '.pdf')
            await pdf.save(title + ".pdf", { returnPromise: true });
        }
    }
}

const toPdf = async function (idArr, title, hasCover, callback) {
    var logo = document.getElementById("logo");  
    console.log(logo.naturalWidth);
    console.log(logo.naturalHeight);
    let naturalWidth = logo.naturalWidth;
    let naturalHeight = logo.naturalHeight;
    var logoSrc = logo.getAttribute('src');
    var logoType = logoSrc.split(".").pop().toUpperCase();

    let ratio = 1
    let logoW = 24
    if(logoSrc){
        ratio = 6 / naturalHeight 
        logoW = naturalWidth * ratio

    }

    if(logoType == "JPG") logoType = "JPEG";
    console.log('logoSrc',logoSrc);

    console.log(logoType);
    
    
    hasCover = hasCover || false
    let ajaxDom = [];
    idArr.forEach(id => {
        ajaxDom.push(html2Canvas(document.querySelector(`#${id}`)))
    });
    let canvasArr = await Promise.all(ajaxDom);
    console.log(canvasArr);
    var pdf = new JsPDF('p', 'mm', 'a4');    //A4纸，纵向
    // pdf边距留白
    let pl = 0;
    let pt = 0;
    let logoH = 0; // 添加页头logo, 100px 大概20mm， 封面不添加
    let a4h = 297; //A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
    let a4w = 210; 
    canvasArr.forEach((canvas, index) => {
        if (index != 0) {
            logoH = 5
            // pl = 10;
            pt = 15;
            pl = 15;
            a4h = 297 - (pt * 2) - logoH; 
            a4w = 210 - (pl * 2); 
            pdf.addPage()
        }
        let renderedHeight = 0
        let ctx = canvas.getContext('2d');
        // let a4w = 210 - (pl * 2); 
        // let a4h = 297 - (pt * 2) - 20;   //A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
        let imgHeight = Math.floor(a4h * canvas.width / a4w);    //按A4显示比例换算一页图像的像素高度

        while (renderedHeight < canvas.height) {
            let page = document.createElement("canvas");
            page.width = canvas.width;
            page.height = Math.min(imgHeight, canvas.height - renderedHeight);//可能内容不足一页

            //用getImageData剪裁指定区域，并画到前面创建的canvas对象中
            let imgData = ctx.getImageData(0, renderedHeight, canvas.width, Math.min(imgHeight, canvas.height - renderedHeight));
            page.getContext('2d').putImageData(imgData, 0, 0);
            let pageData = page.toDataURL('image/jpeg', 1.0)
            pdf.addImage(pageData, 'JPEG', pl, pt+logoH, a4w, Math.min(a4h, a4w * page.height / page.width));    //添加图像到页面，保留10mm边距
            if(logoSrc){
                // 有logo
                if(index != 0 ){
                    //添加页眉的logo
                    pdf.addImage(logo, logoType, 15, 8, logoW, 6);
                }
            }
            renderedHeight += imgHeight;
            if (renderedHeight < canvas.height) {
                if (index != 0 && hasCover) {
                    // 报告封面 第一页单独一页
                    pdf.addPage();//如果后面还有内容，添加一个空页
                }
            }
            // delete page;
        }
    })
    callback();
    // pdf.save(title + '.pdf')
    await pdf.save(title + ".pdf", { returnPromise: true });
}
