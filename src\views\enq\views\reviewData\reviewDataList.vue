<template>
  <div class="review_data_list_wrap">
    <div class="page_main_title">盘点数据</div>
    <div class="page_section review_data_list_main clearfix">
      <div class="training_activities_center clearfix">
        <div class="training_activities_item flex_row_between" v-for="(item, index) in listData" :key="item.enqId">
          <div class="item_index">{{ index + 1 + (page.current - 1) * page.size }}</div>
          <div class="item_content_wrap">
            <div class="item_content flex_row_between">
              <div class="item_content_list item_info">
                <div class="list_title">盘点项目名称</div>
                <div class="list_text">
                  <p class="item_pro_name overflow_elps" :title="item.enqName">{{ item.enqName }}</p>
                </div>
              </div>
              <div class="item_content_list range_date_wrap">
                <div class="list_title">盘点项目起止日期</div>
                <div class="list_text">
                  {{ item.beginDate }}
                  <span v-if="item.beginDate">~</span>
                  {{ item.endDate }}
                </div>
              </div>
              <div class="item_content_list check_progress_wrap">
                <div class="list_title">盘点提交状态</div>
                <div class="list_text">
                  <div class="item_pro_name flex_row_start">
                    <div class="check_pro flex_row_between">
                      <span class="overflow_elps" :title="item.enqSubmitTotal">{{ item.enqSubmitTotal }}</span>
                      <p>提交</p>
                    </div>
                    <div class="check_pro">/</div>
                    <div class="check_pro flex_row_between">
                      <span class="overflow_elps" :title="item.enqTotal">{{ item.enqTotal }}</span>
                      <p>总数</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="item_oper flex_row_start">
            <div class="item_oper_list" @click="progressInfo(item.enqId, item.enqName, 'A')">
              <i class="icon el-icon-s-order"></i>
              <div class="text">数据查询</div>
            </div>
            <div class="item_oper_list" @click="progressInfo(item.enqId, item.enqName, 'B')">
              <i class="icon el-icon-s-data"></i>
              <div class="text">数据确认</div>
            </div>
          </div>
        </div>
        <div class="pagination_wrap">
          <el-pagination
            :page-sizes="[10, 20, 50, 100]"
            @size-change="handleSizeChange"
            :current-page="page.current"
            :page-size="page.size"
            @current-change="handleCurrentChange"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getReviewDataList } from '../../request/api'

const router = useRouter()

const listData = ref([])
const page = reactive({
  current: 1,
  size: 10,
  total: 0
})

const getReviewDataListFun = async () => {
  try {
    const res = await getReviewDataList({
      current: page.current,
      size: page.size
    })
    if (res.code == 200) {
      listData.value = res.data
      Object.assign(page, res.page)
    }
  } catch (error) {
    console.error(error)
  }
}

const handleSizeChange = val => {
  page.current = 1
  page.size = val
  getReviewDataListFun()
}

const handleCurrentChange = val => {
  page.current = val
  getReviewDataListFun()
}

const progressInfo = (val, name, type) => {
  if (type == 'A') {
    // 数据查询
    router.push({
      path: '/talentReviewHome/reviewDataHome/reviewDataView',
      query: {
        enqId: val,
        enqName: name
      }
    })
  } else if (type == 'B') {
    // 数据确认
    router.push({
      path: '/talentReviewHome/reviewDataHome/reviewDataValidation',
      query: {
        enqId: val,
        enqName: name
      }
    })
  }
}

onMounted(() => {
  getReviewDataListFun()
})
</script>

<style scoped lang="scss">
.review_data_list_wrap {
  .review_data_list_main {
    .training_activities_center {
      .training_activities_item {
        position: relative;
        padding: 12px 30px;
        margin-bottom: 8px;
        border: 1px solid #e5e5e5;
        overflow: hidden;
        .progress_state {
          width: 100px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          position: absolute;
          background: #90d99e;
          transform: rotate(-45deg);
          transform-origin: center;
          font-size: 10px;
          color: #fff;
          left: 0;
          left: -28px;
          top: 7px;
        }
        .item_index {
          width: 50px;
          text-align: center;
          font-weight: bold;
          font-size: 20px;
          color: #0099FF;
        }
        .item_content_wrap {
          width: 65%;
          padding: 0 8px;
          .item_content {
            padding-right: 10px;
            .item_content_list {
              color: #525e6c;
              .list_title {
                font-weight: bolder;
                line-height: 38px;
                margin-bottom: 10px;
              }
              .list_text {
                height: 38px;
                .item_pro_name {
                  line-height: 38px;
                  .check_pro {
                    height: 38px;
                    line-height: 38px;
                    margin: 0 5px 0 0;
                    span {
                      display: inline-block;
                      font-weight: bold;
                      color: #0099FF;
                      font-size: 16px;
                      padding-right: 5px;
                    }
                  }
                }
              }
            }
          }
        }
        .item_oper {
          .item_oper_list {
            margin-right: 20px;
            cursor: pointer;
            .icon {
              display: inline-block;
              width: 16px;
              height: 16px;
              margin-right: 5px;
              background: #0099FF;
              border-radius: 2px;
            }
            .text {
              display: inline-block;
              color: #0099FF;
            }
          }
        }
      }
      .pagination_wrap {
        margin-top: 20px;
        text-align: center;
      }
    }
  }
}
</style>
