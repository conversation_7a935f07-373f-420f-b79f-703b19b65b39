<script setup>
import Input from './input.vue'
import api from '@/api/index.js'
import MarkdownIt from 'markdown-it'
import * as echarts from 'echarts'
import service, { cancelRequest } from '@/api/request.js'
import { useDialogueStore } from '@/stores'

const router = useRouter()
const props = defineProps({ id: String })
const conversationId = ref(props.id || '')
const InputRef = ref(null)
const chatContent = ref(null) //装会话的容器
const isScrolling = ref(false) //用于判断用户是否在滚动

const md = new MarkdownIt()
// 修改表格的 HTML 结构
md.renderer.rules.table_open = () => '<table style="border-collapse: collapse; width: 100%;">'
md.renderer.rules.table_close = () => '</table>'
// 修改表头单元格样式
md.renderer.rules.th_open = () =>
  '<th style="border: 1px solid #ddd; padding: 8px; background: #f5f5f5; text-align: center;">'
md.renderer.rules.td_open = () => '<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">'

// 对话内容
const list = ref([])
const loading = ref(false)
let chatMessageId = ref('')
const getConverse = async text => {
  useDialogueStore().setFirst('')
  // 最后一次问答记录用于后面结束未停止的接口
  useDialogueStore().setLast({ conversationId: conversationId.value == '123' ? '' : conversationId.value, query: text })
  loading.value = true
  // 输入框按钮
  InputRef.value.loading = true
  list.value.push({
    type: 'ask',
    text: text
  })
  InputRef.value.input = ''
  service({
    url: '/workflowQuestion/haier/chatMessages',
    method: 'POST',
    params: { conversationId: conversationId.value == '123' ? '' : chatMessageId.value, query: text },
    responseType: 'stream',
    onDownloadProgress: progressEvent => {
      loading.value = false
      const chunk = progressEvent.event.currentTarget.responseText
      const lines = chunk.split('\n')
      let contentThink = ''
      for (const line of lines) {
        if (line.startsWith('data:')) {
          const data = line.slice(5).trim()
          try {
            const jsonData = JSON.parse(data)
            chatMessageId.value = jsonData?.conversation_id
            const lastIndex = list.value.length - 1
            const lastItem = list.value[lastIndex]
            // 判断是否是回答的第一次
            if (lastItem && lastItem.type == 'answer') {
              if (jsonData.answer) {
                const result = extractThinkContent(jsonData.answer)
                if (jsonData?.event == 'message') {
                  if (result.contentAfterThink.includes('echarts')) {
                    let arr = result.contentAfterThink.split('```')
                    list.value[lastIndex].think = result.thinkContent
                    list.value[lastIndex].id = jsonData.message_id
                    list.value[lastIndex].text = md.render(contentThink)
                    list.value[lastIndex].option = JSON.parse(arr[1].substring(8))
                    setTimeout(() => {
                      renderEcharts(jsonData.message_id, JSON.parse(arr[1].substring(8)))
                    }, 0)
                  } else {
                    contentThink += result.contentAfterThink
                    list.value[lastIndex].think = result.thinkContent
                    list.value[lastIndex].text = md.render(contentThink)
                    list.value[lastIndex].id = jsonData.message_id
                  }
                }
              }
              if (jsonData?.event == 'error') {
                list.value[lastIndex].text = '抱歉，我还在学习中......'
                list.value[lastIndex].id = jsonData.message_id
              }
            } else {
              list.value.push({
                type: 'answer',
                think: '',
                text: '思考中...'
              })
            }
            chatContent.value.scrollTop = chatContent.value.scrollHeight - chatContent.value.offsetHeight
          } catch (error) {
            console.error('Failed to parse JSON data:', error)
          }
        }
      }
    }
  })
    .finally(() => {
      loading.value = false
      InputRef.value.loading = false
    })
    .catch(err => {
      if (list.value.length != 0) list.value[list.value.length - 1].think = '回答已终止'
    })
}

// 取消
const cancel = () => {
  cancelRequest(`post/workflowQuestion/haier/chatMessages${useDialogueStore().lastData}`)
  InputRef.value.loading = false
}
// 历史会话内容
const getConversation = type => {
  list.value = []
  loading.value = true
  api.dialogue
    .questionConversion({ conversationId: conversationId.value })
    .then(res => {
      const data = res.data
      data.reverse().forEach(item => {
        list.value.push({
          type: 'ask',
          text: item.query
        })
        if (!item.outputs) {
          list.value.push({
            type: 'answer',
            text: '抱歉，我还在学习中......'
          })
          return
        }
        const answer = extractThinkContent(JSON.parse(item.outputs).answer)
        if (answer.contentAfterThink.includes('echarts')) {
          let arr = JSON.parse(item.outputs).answer.split('```')
          list.value.push({
            type: 'answer',
            id: item.taskId,
            think: answer.thinkContent,
            text: md.render(arr[0]),
            option: JSON.parse(arr[1].substring(8))
          })
          setTimeout(() => {
            renderEcharts(item.taskId, JSON.parse(arr[1].substring(8)))
          }, 0)
        } else {
          list.value.push({
            type: 'answer',
            id: item.taskId,
            think: answer.thinkContent,
            text: md.render(answer.contentAfterThink)
          })
        }
      })
    })
    .finally(() => {
      loading.value = false
      if (type == 'second') {
        getConverse(useDialogueStore().secondInput)
      }
      scrollToBottom()
    })
}

// 渲染echarts
function renderEcharts(id, option) {
  const chartDiv = document.getElementById(id)
  const chartOption = option

  // 初始化 ECharts 实例
  const myChart = echarts.init(chartDiv)
  // 应用配置
  myChart.setOption(chartOption)
}
// 处理think数据
function extractThinkContent(str) {
  const startIndex = str.indexOf('<think>')
  if (startIndex == -1) {
    return {
      thinkContent: '',
      contentAfterThink: str
    }
  }
  const start = startIndex + '<think>'.length
  const endIndex = str.indexOf('</think>', start)
  if (endIndex == -1) {
    return {
      thinkContent: str.slice(start),
      contentAfterThink: ''
    }
  }
  // 截取 </think> 到字符串末尾的内容
  const contentAfterThink = str.slice(endIndex + '</think>'.length)
  return {
    thinkContent: str.slice(start, endIndex),
    contentAfterThink
  }
}
// 判断历史还是新对话
function isNewConversation() {
  // 结束请求
  cancelRequest(`post/workflowQuestion/chatMessages${useDialogueStore().lastData}`)
  if (useDialogueStore().firstInput) {
    getConverse(useDialogueStore().firstInput)
  } else {
    // 历史对话
    getConversation()
  }
}
// 对话
const addConverse = () => {
  if (InputRef.value.input) {
    // if (conversationId.value == '123') {
    //   useDialogueStore().setFirst('')
    //   router.replace(`/question/${chatMessageId.value}`)
    // } else {
    getConverse(InputRef.value.input)
    // }
  }
}
// 操作
const copy = text => {
  // 去除 HTML 标签
  const cleanText = text.replace(/<[^>]*>/g, '')
  navigator.clipboard.writeText(cleanText).then(() => {
    ElMessage({
      message: '复制成功',
      type: 'success'
    })
  })
}
//#region 页面滚动

function scrollToBottom() {
  nextTick(() => {
    //注意要使用nexttick以免获取不到dom
    if (!isScrolling.value) {
      chatContent.value.scrollTop = chatContent.value.scrollHeight - chatContent.value.offsetHeight
    }
  })
}
function handleScroll() {
  const scrollContainer = chatContent.value
  const scrollTop = scrollContainer.scrollTop
  const scrollHeight = scrollContainer.scrollHeight
  const offsetHeight = scrollContainer.offsetHeight
  if (scrollTop + offsetHeight < scrollHeight) {
    // 用户开始滚动并在最底部之上，取消保持在最底部的效果
    isScrolling.value = true
  } else {
    // 用户停止滚动并滚动到最底部，开启保持到最底部的效果
    isScrolling.value = false
  }
}

const goPath = path => {
  router.push(path)
}
//#endregion
onMounted(() => {
  // isNewConversation()
  chatContent.value.addEventListener('scroll', handleScroll)
})
watch(
  () => props.id,
  (newId, oldId) => {
    conversationId.value = newId
    isNewConversation()
  }
)
</script>
<template>
  <div class="main">
    <div class="frame" ref="chatContent">
      <template v-for="(item, index) in list" :key="index">
        <div class="ask" v-if="item.type == 'ask'">{{ item.text }}</div>
        <div class="answer answer-mobile" v-else>
          <div class="profile">
            <img src="@/assets/imgs/dialogue/logo.jpg" alt="" />
          </div>
          <div class="content">
            <div class="think">{{ item.think }}</div>
            <div class="text" v-if="item.text">
              <div v-html="item.text"></div>
              <div :id="item.id" class="echarts" v-if="item.option"></div>
            </div>
            <div class="operation">
              <div class="copy" title="复制" @click="copy(item.text)"></div>
              <div class="refresh" title="重新生成"></div>
              <div class="like" title="喜欢"></div>
              <div class="rubbish" title="不喜欢"></div>
            </div>
          </div>
        </div>
      </template>
      <!-- 加载 -->
      <div class="answer" v-if="loading">
        <div class="profile">
          <img src="@/assets/imgs/dialogue/logo.jpg" alt="" />
        </div>
        <div class="content" v-loading="loading">
          <div class="text" style="width: 50px"></div>
        </div>
      </div>
    </div>
    <!-- <div class="add-card" @click="goPath('/question')">
      <el-icon><CirclePlus /></el-icon>
      <span class="text">新建对话</span>
    </div> -->
    <Input class="input" ref="InputRef" @send="addConverse()" @pause="cancel()" />
  </div>
</template>
<style lang="scss" scoped>
.main {
  height: 100%;
  display: flex;
  flex-direction: column;
  .frame {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 0 10px;
    flex-grow: 1;
    .ask {
      max-width: 95%;
      background: #cae5ff;
      border-radius: 8px 8px 8px 8px;
      color: #333333;
      line-height: 30px;
      font-size: 16px;
      padding: 10px 16px;
      align-self: flex-end;
      margin-bottom: 20px;
    }
    .answer {
      max-width: 90%;
      align-self: flex-start;
      display: flex;
      margin-bottom: 20px;
      .profile {
        width: 40px;
        height: 40px;
        background: #ffffff;
        flex-shrink: 0;
        margin-right: 10px;
        border-radius: 50%;
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: contain;
        }
      }
      .content {
        max-width: 100%;
        .text {
          overflow-x: auto;
          background: #ffffff;
          box-shadow: 0px 0px 6px 0px #d8e9ff;
          border-radius: 8px 8px 8px 8px;
          color: #3d3d3d;
          font-size: 16px;
          line-height: 29px;
          padding: 20px 40px;
          text-align: justify;
        }
        .echarts {
          width: 600px;
          max-width: 100%;
          height: 400px;
        }
        .think {
          font-size: 14px;
          color: #8b8b8b;
          margin-bottom: 5px;
        }
        :deep(.el-loading-mask) {
          border-radius: 8px;
        }
        .operation {
          display: flex;
          margin-top: 20px;
          div {
            width: 14px;
            height: 14px;
            cursor: pointer;
            margin-right: 16px;
            background-size: 14px 14px !important;
          }
          .copy {
            background: url('../../assets/images/dialogue/copy.webp') no-repeat 0 0;
            &:hover {
              background: url('../../assets/images/dialogue/copy_ac.webp') no-repeat 0 0;
            }
          }
          .refresh {
            background: url('../../assets/images/dialogue/refresh.webp') no-repeat 0 0;
            &:hover {
              background: url('../../assets/images/dialogue/refresh_ac.webp') no-repeat 0 0;
            }
          }
          .like {
            background: url('../../assets/images/dialogue/like.webp') no-repeat 0 0;
            &:hover {
              background: url('../../assets/images/dialogue/like_ac.webp') no-repeat 0 0;
            }
          }
          .rubbish {
            background: url('../../assets/images/dialogue/rubbish.webp') no-repeat 0 0;
            &:hover {
              background: url('../../assets/images/dialogue/rubbish_ac.webp') no-repeat 0 0;
            }
          }
        }
      }
    }
  }
  :deep(.input-main) {
    margin-top: 0;
  }
  .add-card {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    gap: 4px;
    height: 34px;
    width: 104px;
    // margin-top: 50px;
    margin-bottom: 15px;
    padding: 0 10px;
    border: 1px solid #e7e4e4;
    border-radius: 12px;
    cursor: pointer;
    .el-icon {
      vertical-align: middle;
      color: #333;
    }
    .text {
      color: #333;
      font-size: 14px;
    }
    &:hover {
      .el-icon {
        color: #53a9f9;
      }
      .text {
        color: #53a9f9;
      }
    }
  }
}
</style>
