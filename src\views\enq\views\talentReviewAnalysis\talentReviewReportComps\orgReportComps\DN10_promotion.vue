<template>
    <div class="org_report_main promotion_wrap" :class="{'marginB_16':isPdf}">
        <slot></slot>
        <div class="page_second_title">人员晋升结构</div>

        <el-row :gutter="16">
            <!-- <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title" class="chart_box_list_wrap ">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box chart_box_wrap" :id="col.chartDomId"></div>
            </el-col> -->
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title" class="left_chart_box chart_box_list_wrap">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box chart_box_wrap" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="col.elSpan"  v-for="col in chartCenterDom" :key="col.title" class="chart_box_list_wrap">
                    <div class="item_title">{{ col.title }}</div>
                    <div class="chart_box chart_box_wrap" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="col.elSpan" v-for="col in chartRightDom" :key="col.title" class="chart_box_list_wrap ">
                    <div class="item_title">{{ col.title }}</div>
                    <div class="chart_box chart_box_wrap" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
            <!-- <el-col :span="24">
                <div class="item_title">上级对员工继任周期与自己晋升预期周期</div>
                <div class="sup_table_wrap">
                    <div class="line"></div>
                    <el-table
                        :data="tablePData.data"
                        :span-method="arraySpanMethod"
                        :cell-class-name="setCellStyle"
                        border
                        style="width: 100%">
                        <el-table-column
                            :show-overflow-tooltip="true"
                            v-for="col in tablePData.columns"
                            :prop="col.prop"
                            :key="col.prop"
                            :label="col.label"
                            :sortable="col.sortable"
                            :width="col.width"
                            :fixed="col.fixed"
                            :class-name="col.className"
                            :align="col.align"
                            :formatter="col.formatterFun">
                        </el-table-column>
                    </el-table>
                    <ul class="bottom_tips flex_row_start">
                        <li class='green'>考虑岗位晋升</li>
                        <li class='yellow'>考虑加速培养</li>
                        <li class='red'>暂不考虑晋升</li>
                    </ul>
                 </div>
            </el-col> -->
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        orgExpectationCycle,
        orgExpectedDetails,
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import listComp from "./components/listComp.vue";
    export default {
        name: "orgRPromotion",
        props: ["enqId", "orgCode","isPdf"],
        components: { tableComps,listComp },
        data() {
            return {
                size: 10,
                current: 1,
                chartDom: [
                    {
                        chartDomId:this.$util.createRandomId(),
                        // title: "本组织及所有下级组织在岗时长",
                        title: "各部门在本岗位上工作时长分布",
                        elSpan: 24,
                        height: "445",
                        chartType: "XStack",
                        dataKey:'promotionStructure'
                    },
                ],
                chartCenterDom: [
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "员工个人晋升期望周期分布",
                        elSpan: 12,
                        height: "445",
                        chartType: "YBar",
                        dataKey:'expectationCycle'
                    },
                ],
                chartRightDom: [
                     {
                        chartDomId:this.$util.createRandomId(),
                        title: "上级对员工晋升周期预估",
                        elSpan: 12,
                        height: "445",
                        chartType: "YBar",
                        dataKey:'superiorExpectationCycle'
                    },
                //     {
                //         chartDomId: "promotionPossibilities",
                //         title: "上级对员工晋升可能性评估",
                //         elSpan: 6,
                //         // height: "250",
                //         chartType: "YBar",
                //         dataKey:'promotionPossibilities'
                //     },
                //      {
                //         chartDomId: "possibilityClassification",
                //         title: "上级对员工继任周期与晋升可能性分级",
                //         elSpan: 6,
                //         // height: "250",
                //         chartType: "XStack",
                //         dataKey:'possibilityClassification'
                //     },
                ],
                listArr: [
                    {
                        title: "晋升期望详情",
                        ajaxUrl: orgExpectedDetails,
                        // isAsyncColumns: true,
                        columns: [
                            {
                            label: "部门",
                            prop: "orgName",
                        },
                        {
                            label: "姓名",
                            prop: "userName",
                        },
                        {
                            label: "岗位",
                            prop: "jobName",
                        },
                        {
                            label: "人才类别",
                            prop: "talentType",
                        },
                        {
                            label: "个人发展类型",
                            prop: "developmentType",
                        },
                        {
                            label: "下一晋升职位类型",
                            prop: "jobType",
                        },
                        {
                            label: "下一职位名称",
                            prop: "supJobName",
                        },
                        {
                            label: "个人预计周期",
                            prop: "expectationCycle",
                        },
                        // {
                        //     label: "晋升可能性",
                        //     prop: "promotion_possibility",
                        // },
                        {
                            label: "上级预计继任周期",
                            prop: "superiorExpectationCycle",
                        },
                        ],
                    },
                ],
                tablePData:{
                    columns:[

                    ],
                    data:[]
                },
                
            };
        },
        created() {
            this.getData();
            // this.orgExpectedDetailsFn();
        },
        mounted() {},
        methods: {
            initChart(data,arrData) {
                arrData.forEach((chart) => {
                    // XStack图表所需数据格式处理
                    let formatterChartArr = [
                        "promotionStructure",
                        "possibilityClassification",
                    ];
                    let chartData = {};
                    if (formatterChartArr.includes(chart.dataKey)) {
                        chartData = data[chart.dataKey];
                        chartData["padding"] = 120;
                    } else {
                        chartData["data"] = data[chart.dataKey];
                        chartData["padding"] = 100;

                    }
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgExpectationCycle(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        let data = res.data;
                        this.initChart(data,this.chartDom);
                        this.initChart(data,this.chartCenterDom);
                        this.initChart(data,this.chartRightDom);
                        this.tablePData.columns = [
                            {
                                label: "上级预测",
                                prop: "supName",
                                width:'80',
                            },
                            {
                                label: "员工晋升预期",
                                prop: "",
                                width:'110',
                            },
                        ]
                        let tableTitle = res.data.personnelPromotion.legend.map(item=>{
                            return{
                                label:item.name,
                                prop:item.code.replace(/\./g,'-')
                            }
                        })
                        this.tablePData.columns = this.tablePData.columns.concat(tableTitle)
                        this.tablePData.data = this.dotToline(res.data.personnelPromotion.dataList, "key");
                    }
                });
            },
            dotToline(param, type, valueKey) {
                if (Array.isArray(param)) {
                    if (param.length == 0) {
                        return;
                    }
                    param.forEach((item) => {
                        if (typeof item == "object") {
                            for (const key in item) {
                                if (item.hasOwnProperty(key)) {
                                    if (type == "key") {
                                        let newKey = key.split(".").join("-");
                                        item[newKey] = item[key];
                                    }else if(type == "value"){
                                        let val = item[valueKey];
                                        item[valueKey] = val.split(".").join("-");
                                    }
                                }
                            }
                        }
                    });
                    return param;
                }
            },
            arraySpanMethod({ row, column, rowIndex, columnIndex }) {
                if (columnIndex == 0) {
                    return [1, 2];
                } else if (columnIndex == 1) {
                    return [0, 0];
                }
            },
            setCellStyle({row,column,rowIndex,columnIndex}){
                // console.log(column)
                // if(columnIndex != 0){
                //     if (rowIndex == 1) {
                //         return 'green';
                //     } else if (rowIndex == 3) {
                //         return 'yellow';
                //     }
                //         return 'red';
                // }
            },
        },
    };
</script>
 
<style scoped lang="scss">
.promotion_wrap{
    .chart_box_wrap{
    }
    .chart_box_list_wrap{
        .chart_box_wrap{
            margin: 0 0 10px 0;
            min-height: 200px !important;
        }
    }
    .self_table_wrap{
        width: 100%;
        .green{
           background: #92dd6d; 
        }
        .yellow{
            background: #e6a23c;
        }
        .red{
            background: #f56c6c;
        }
    
    }
    .sup_table_wrap{
        position: relative;
        .line{
            position: absolute;
            top: 0px;
            left: 0px;
            height: 1px;
            width: 200px;
            background: #EBEEF5;
            z-index: 999;
            transform-origin: left;
            transform: rotate(17deg);
        }
        .el-table{
            .el-table__header{
                thead{
                    tr{
                        height: 60px;
                        th:nth-child(1){
                            border-right: 0;
                            .cell{
                                position: absolute;
                                bottom: 4px;
                                left: 2px;
                            }
                        }
                        th:nth-child(2){
                            .cell{
                                position: absolute;
                                top: 4px;
                                right: 2px;
                                white-space: nowrap;
                            }
                        }
                    }
                }
            }
            .el-table__body{
                tr{
                    td{

                    }
                    th:nth-child(1){
                    }
                }
            }
            .green{
                background: #92d050; 
            }
            .yellow{
                background: #fff2cc;
            }
            .red{
                background: #ffe2e2;
            }
            
        }
        .green{
           background: #92dd6d; 
        }
        .yellow{
            background: #e6a23c;
        }
        .red{
            background: #f56c6c;
        }
        .bottom_tips{
            margin: 4px 0 0 0;
            li{
                width: 90px;
                height: 30px;
                line-height: 30px;
                color: #fff;
                text-align: center;
                border-radius: 4px;
                font-size: 12px;
            }
            
            li:nth-child(2){
                margin: 0 4px;
            }
        }
    }
}








</style>