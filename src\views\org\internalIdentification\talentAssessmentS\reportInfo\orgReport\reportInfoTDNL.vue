<script setup>
import Table from "@/components/table/simplenessTable.vue";
// 3
const columns = ref([
  {
    label: "二级能力",
    prop: "a",
    width: 260,
  },
  {
    label: "三级能力",
    prop: "b",
  },
  {
    label: "能力得分",
    prop: "c",
  },
  {
    label: "新手入门",
    prop: "d",
  },

  {
    label: "合格执行者",
    prop: "e",
  },
  {
    label: "骨干能手",
    prop: "f",
  },
  {
    label: "资深专家",
    prop: "g",
  },
  {
    label: "业内标杆",
    prop: "h",
  },
]);
const data = ref([
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "已完成",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);

const columns2 = ref([
  {
    label: "人员能力级别",
    prop: "a",
    width: 260,
  },
  {
    label: "人数",
    prop: "b",
  },
  {
    label: "人员",
    prop: "c",
  },
]);
const data2 = ref([
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "已完成",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);
const chartList = ref([
  "销售战略",
  "市场洞察",
  "客户洞察",
  "市场营销",
  "商机管理",
  "客户管理",
  "渠道管理",
  "销售运营",
  "销售团队管理",
  "销售管理数字化",
]);

const closeInfo = (i) => {
  emits("closeReportInfo", true);
};

const circleColor = (v, o) => {
  if (o == "g") {
    if (v < 50) {
      return "green_bg2";
    } else if (v >= 50) {
      return "green_bg1";
    }
  } else if (o == "r") {
    if (v < 50) {
      return "red_bg2";
    } else if (v >= 50) {
      return "red_bg1";
    }
  }
};
onMounted(() => {});
</script>
<template>
  <div class="com_right_wrap">
    <div class="t">团队能力图谱</div>
    <div class="info_section_wrap">
      <div class="section_title justify-between">
        <div class="page-title-line">能力图谱</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="chart_list_wrap justify-start">
        <div class="chart_wrap" v-for="(item, index) in chartList">
          <div class="c_title">{{ item }}</div>
          <div class="chart_box" id="index+1"></div>
        </div>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">能力图谱整体表现人数分布（销售战略）</div>
      <div class="section_main">
        <Table
          :roundBorder="false"
          :columns="columns"
          :data="data"
          headerColor
          showIndex
        >
        </Table>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">
        能力图谱具体人员能力表现（销售战略-销售差距分析）
      </div>
      <div class="section_main">
        <Table
          :roundBorder="false"
          :columns="columns2"
          :data="data2"
          headerColor
          showIndex
        >
        </Table>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
