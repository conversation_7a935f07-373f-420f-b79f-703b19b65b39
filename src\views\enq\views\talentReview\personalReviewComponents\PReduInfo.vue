<template>
  <div class="edu_info_wrap">
    <div class="clearfix">
      <div class="page_second_title marginT_16">
        <span>教育信息</span>
      </div>
      <div class="text_right">
        <el-button class="page_add_btn" type="primary" @click="addItem">新增</el-button>
      </div>
      <div class="edu_info_center marginT_16">
        <div class="edu_info_header">
          <div class="item">毕业院校</div>
          <div class="item">毕业日期</div>
          <div class="item">学历</div>
          <div class="item">是否最高学历</div>
          <div class="item">与当前岗位相关</div>
          <div class="item">与当前行业相关</div>
          <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
          <edu-info-item :eduInfoData="eduInfoData" @deleteItem="deleteItem" />
          <div class="align_center marginT_30">
            <el-button
              class="page_confirm_btn"
              type="primary"
              @click="prevStep"
              v-show="currentIndex != currentFirstCode"
              >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="submit('nextStep')">{{ nextBtnText }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { updateEnqUserEducation, delEnqUserEducation, getEnqUserEducation } from '../../../request/api.js'
import eduInfoItem from './PReduInfoItem.vue'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  currentIndex: Number,
  currentFirstCode: Number
})

const emit = defineEmits(['nextStep', 'prevStep'])

const userStore = useUserStore()

// 响应式状态
const submitFlag = ref(true)
const eduInfoData = ref([])

// 计算属性
const userId = computed(() => userStore.userInfo.userId)

// 方法
const getEducationData = async () => {
  try {
    const res = await getEnqUserEducation({
      enqId: props.enqId
    })
    if (res.code == 200) {
      eduInfoData.value = res.data
    } else {
      ElMessage.error('获取数据失败!')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败!')
  }
}

const submit = async stepType => {
  if (!submitFlag.value) return
  if (checkData(eduInfoData.value)) {
    ElMessage.warning('请完善数据后提交！')
    return
  }
  submitFlag.value = false
  try {
    const params = {
      enqUserEducations: eduInfoData.value
    }
    const res = await updateEnqUserEducation(eduInfoData.value)
    if (res.code == 200) {
      ElMessage.success('保存成功!')
      await getEducationData()
      submitFlag.value = true
      emit(stepType)
    } else {
      submitFlag.value = true
      ElMessage.error('保存失败!')
    }
  } catch (error) {
    submitFlag.value = true
    ElMessage.error('保存失败!')
  }
}

const checkData = data => {
  const arr = data
  const len = arr.length
  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    if (
      !obj.graduateSchool ||
      !obj.graduateDate ||
      !obj.qualification ||
      !obj.highestQualification ||
      !obj.postRelated ||
      !obj.industryRelated
    ) {
      return true
    }
  }
  return false
}

const deleteItem = async (item, index) => {
  try {
    const res = await delEnqUserEducation({
      id: item.id
    })
    if (res.code == 200) {
      ElMessage.success('删除成功!')
      eduInfoData.value.splice(index, 1)
    } else {
      ElMessage.error('删除失败!')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('删除失败!')
  }
}

const addItem = () => {
  eduInfoData.value.push({
    enqId: props.enqId,
    id: Date.now().toString(),
    graduateSchool: '',
    graduateDate: '',
    qualification: '',
    highestQualification: '',
    postRelated: '',
    industryRelated: ''
  })
}

const prevStep = async () => {
  try {
    await ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '保存',
      cancelButtonText: '放弃修改'
    })
    await submit('prevStep')
  } catch (action) {
    ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
    if (action == 'cancel') {
      emit('prevStep')
    }
  }
}

// 生命周期钩子
onMounted(() => {
  getEducationData()
})
</script>

<style scoped lang="scss">
.edu_info_wrap {
  margin-bottom: 16px;
}

.edu_info_header {
  .item {
    width: 13%;
  }

  .item_icon_wrap {
    text-align: center;
    width: 8%;
  }
}
</style>
