<template>
    <div>
        <div class="edu_info_item" v-for="(item, index) in itemData" :key="item.id">
            <el-select class="item" v-model="item.activityType" placeholder>
                <el-option label="业务类" value="业务类"></el-option>
                <el-option label="行政类" value="行政类"></el-option>
            </el-select>
            <el-input class="item" v-model="item.postType" placeholder></el-input>
            <el-input class="item" v-model="item.taskName" placeholder></el-input>
            <div class="item item_icon_wrap">
                <i class="item_icon el-icon-delete" @click="deleteItem(item,index)"></i>
            </div>
        </div>
    </div>
</template>
 
<script>
export default {
    name: "workActivityItem",
    props: {
        itemData: {
            type: Array,
            default: function() {
                return [
                    {
                        id: "1",
                        activityType: "",
                        postType: "",
                        taskName: ""
                    }
                ];
            }
        }
    },
    data() {
        return {
            postSequence: "",
            activityType: "",
            taskName: "",
            state: "",
            sortIndex: "0"
        };
    },
    methods: {
        deleteItem(item, index) {
            this.$emit("deleteItem", index);
        }
    }
};
</script>
 
<style scoped lang="scss">
.edu_info_item {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    margin-bottom: 8px;
    // padding: 0 16px;
    .item {
        // flex: 1;
        width: 32%;
        padding: 0 4px;
        line-height: 40px;
        &.school_name {
            width: 27%;
        }
    }
    .item_icon_wrap {
        text-align: center;
        width: 5%;
        min-width: 40px;
        .item_icon {
            font-size: 20px;
            color: #0099fd;
            cursor: pointer;
        }
    }
}
</style>