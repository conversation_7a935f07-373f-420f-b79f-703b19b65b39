<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in eduInfoData" :key="item.id">
      <div class="item item_icon_wrap">{{index+1}}</div>
      <div class="item item_icon_wrap">{{item.userName}}</div>
      <div class="item">{{item.SZPXLC }}</div>
      <div class="item item_icon_wrap">{{item.SZPZDX }}</div>
      <div class="item item_icon_wrap">{{item.SZPZRX}}</div>
      <div class="item item_icon_wrap">{{item.SZPZSS}}</div>
      <div class="item item_icon_wrap">{{item.SZPZYN}}</div>
      <div class="item item_icon_wrap">{{item.SZPGLN}}</div>
      <div class="item item_icon_wrap">{{item.SZPZXL}}</div>
      <div class="item item_icon_wrap">{{item.SZPTDX}}</div>
      <div class="item item_icon_wrap">{{item.SZPCXN}}</div>
      <div class="item item_icon_wrap">{{item.qualityOverallScore}}</div>
      <div class="item item_icon_wrap">{{item.qualityLevelSys}}</div>
      <el-select class="item" v-model="item.actualQualityGrade" placeholder="请选择">
        <el-option
          v-for="(item,index) in qualificationOptions"
          :label="item.codeName"
          :value="item.dictCode"
          :key="index"
        ></el-option>
      </el-select>
    </div>
  </div>
</template>

<script>
export default {
  name: "personInfoItem",
  props: {
    eduInfoData: {
      type: Array,
      default: function() {
        return [{}];
      }
    }
  },
  data() {
    return {
      qualificationOptions: []
    };
  },
  created() {
    this.$getDocList(["ACTUAL_GRADE"]).then(res => {
      this.qualificationOptions = res.ACTUAL_GRADE;
    });
  },
  methods: {}
};
</script>

<style scoped lang="scss">
.edu_info_item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  padding: 8px 16px;

  .item {
    width: 9%;
  }

  .item_icon_wrap {
    text-align: center;
    width: 6%;

    .item_icon {
      font-size: 20px;
      color: #0099fd;
      cursor: pointer;
    }
  }
}
</style>
