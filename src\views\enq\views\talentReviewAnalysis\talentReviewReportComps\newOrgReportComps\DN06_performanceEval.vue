<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <div class="page_second_title">业绩评价</div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24">
                <listComp
                    :options="listConfig"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        getAchievementEval,
        getAchievementUser,
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import listComp from "./components/listComp.vue";

    export default {
        name: "orgRPerformanceEval",
        props: {
            enqId: String,
            orgCode: String,
            isPdf: {
                type: Boolean,
                default: false,
            },
        },
        components: { tableComps, listComp },
        data() {
            return {
                chartDom: [
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "业绩评价得分",
                        elSpan: 6,
                        chartHeight: "200",
                        chartType: "YBar",
                        dataKey: "achievementEval",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "业绩评价",
                        elSpan: 6,
                        chartHeight: "200",
                        chartType: "YBar",
                        dataKey: "achievementEval",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "整体业绩实际",
                        elSpan: 12,
                        chartType: "XBar",
                        dataKey: "achievementActual",
                    },
                ],
                listConfig: {
                    title: "人员素质详情",
                    ajaxUrl: getAchievementUser,
                    isAsyncColumns: true,
                    columns: [
                        {
                            label: "姓名",
                            prop: "userName",
                        },
                    ],
                    afterColumns: [
                        {
                            label: "系统评级",
                            prop: "sysGrade",
                        },
                        {
                            label: "上级评价等级",
                            prop: "grade",
                        },
                    ],
                },
            };
        },
        created() {
            this.getData();
        },
        mounted() {},
        methods: {
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getAchievementEval(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.initChart(res.data);
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>