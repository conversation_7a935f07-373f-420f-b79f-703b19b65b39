<template>
  <div class="development_channel_main">
    <div class="page_second_title">发展通道</div>
    <div class="chart_wrap">
      <basicScatterPlot v-if="showChart" :chartData="chartData" :chartConfig="config" :width="800" :height="460" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import basicScatterPlot from '@/components/talent/echartsComps/scatterPlot/basicScatterPlot.vue'

const showChart = ref(false)

const config = ref({
  xAxisName: '在司工龄',
  yAxisName: '平均晋升周期',
  name: 'name',
  x: 'workingYears',
  y: 'promotion',
  size: 'personnelNum',
  valueFormatterKey: '年'
})

const chartData = ref([
  { name: '见习生', personnelNum: 5, promotion: 0.5, workingYears: 0.8 },
  { name: '见习销售代表', personnelNum: 5, promotion: 2, workingYears: 2 },
  { name: '销售代表', personnelNum: 10, promotion: 1.5, workingYears: 3 },
  { name: '销售主管', personnelNum: 25, promotion: 3, workingYears: 4 },
  { name: '销售经理', personnelNum: 25, promotion: 4, workingYears: 5.5 }
])
</script>

<style scoped lang="scss">
.page_second_title {
  margin: 10px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
}
</style>
