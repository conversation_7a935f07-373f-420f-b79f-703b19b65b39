<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <slot></slot>
        <div class="page_second_title">人才离职风险</div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="8">
                <div class="item_title">离职风险矩阵</div>
                <talent-matrix
                    :headTitle="'离职影响'"
                    :asideTitle="'离职风险'"
                    :matrixHeight="200"
                    :matrixData="retentionMatrix"
                ></talent-matrix>
            </el-col>
            <el-col :span="24">
                <div class="item_title">潜在离职原因</div>
                <div class="chart_box" id="quit_reason"></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
            <!-- <el-col :span="24">
                <div class="item_title">各职位人员潜在离职原因</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentPostChange"
                    @handleSizeChange="handleSizePostChange"
                    :tableData="tablePostData"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf">更多数据请查看网页版报告</div>
            </el-col> -->
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    // import {
    //     orgRetentionRisk,
    //     orgRiskDetails,
    //     orgRetentionMatrix,
    // } from "../../../../request/api";
    import {
        getOrgRetentionRisk,
        getOrgRetentionRiskList,
        getJobRetentionRiskList,
        getUserRetentionRiskList,
        getRetentionRiskList,
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import talentMatrix from "@/components/talent/common/talentMatrix";
    import listComp from "./components/listComp.vue";
    export default {
        name: "orgRRisk",
        props: ["enqId", "orgCode", "isPdf"],
        components: { tableComps, talentMatrix, listComp },
        data() {
            return {
                potentialReasonsForLeaving: {
                    data: [],
                    legend: [],
                },
                size: 10,
                current: 1,
                retentionMatrix: [],
                substitutability: [],
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "离职可能性",
                        elSpan: 8,
                        chartType: "YBar",
                        dataKey: "possibility",
                        paddingL:150
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "离职对业务的影响",
                        elSpan: 8,
                        chartType: "YBar",
                        dataKey: "influence",
                        paddingL:100
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "人员是否可替代",
                        elSpan: 8,
                        chartHeight: "400",
                        chartType: "YBar",
                        dataKey: "replaceable",
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "高离职可能性人才类别",
                        elSpan: 16,
                        chartType: "XBar",
                        dataKey: "talentCategory",
                    },
                ],
                listArr: [
                    {
                        title: "各组织人员潜在离职原因",
                        ajaxUrl: getOrgRetentionRiskList,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                            {
                                label: "部门负责人",
                                prop: "leaderName",
                            },
                        ],
                    },
                    {
                        title: "人才离职潜在原因",
                        ajaxUrl: getUserRetentionRiskList,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                            {
                                label: "姓名",
                                prop: "leaderName",
                            },
                            {
                                label: "岗位",
                                prop: "postName",
                            },
                        ],
                    },
                    {
                        title: "人才离职风险详情",
                        ajaxUrl: getRetentionRiskList,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                            {
                                label: "岗位",
                                prop: "postName",
                            },
                            {
                                label: "人才类别",
                                prop: "talentType",
                            },
                            {
                                label: "离职风险",
                                prop: "retention_risk",
                            },
                            {
                                label: "离职影响",
                                prop: "effects",
                            },
                            {
                                label: "离职可替代",
                                prop: "substitution",
                            },
                        ],
                    },
                    // {
                    //     title: "各职位人员潜在离职原因",
                    //     ajaxUrl: getJobRetentionRiskList,
                    //     isAsyncColumns: true,
                    //     columns: [
                    //         {
                    //             label: "部门",
                    //             prop: "orgName",
                    //         },
                    //         {
                    //             label: "职位",
                    //             prop: "postName",
                    //         },
                    //     ],
                    // },
                ],
            };
        },
        created() {
            // this.getData();
            // this.orgRiskDetailsFn();
            // this.orgRetentionMatrixFn();
            this.getOrgRetentionRiskFun();
            // this.getRetentionRiskListFun();
        },
        mounted() {
            // this.getOrgRetentionRiskListFun();
            // this.getJobRetentionRiskListFun();
            // this.getUserRetentionRiskListFun();
        },
        methods: {
            getOrgRetentionRiskFun() {
                getOrgRetentionRisk({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then((res) => {
                    if (res.code == 200) {
                        this.initChart(res.data);
                        // 离职风险矩阵
                        this.retentionMatrix = res.data.getRiskMatrix;
                        // 潜在离职原因
                        this.potentialReasonsForLeaving.data =
                            res.data.potentialReasonsForLeaving.chartData;
                        this.potentialReasonsForLeaving.legend =
                            res.data.potentialReasonsForLeaving.legend;
                        echartsRenderPage(
                            "quit_reason",
                            "XBar",
                            null,
                            "260",
                            this.potentialReasonsForLeaving
                        );
                    }
                });
            },
            initChart(data) {
                this.chartDom.forEach((chart, index) => {
                    let chartData = {
                        data: data[chart.dataKey],
                        padding: 115

                    };
                    if(chart.paddingL){
                        chartData.padding = chart.paddingL
                    }
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                    // if(chart.dataKey == 'possibility'){
                    //     this.chartDom[index].tips = '167位员工离职风险高'
                    // }else if(chart.dataKey == 'influence'){
                    //     this.chartDom[index].tips = '234位人员的核心能力在后20%之列'
                    // }else if(chart.dataKey == 'replaceable'){
                    //     this.chartDom[index].tips = '148位员工离职可能性高'
                    // }else if(chart.dataKey == 'talentCategory'){
                    //     this.chartDom[index].tips = '有'+ chartData.data[0].value+'位'+chartData.data[0].name+ chartData.data[1].value+'位'+chartData.data[1].name+'高离职风险'
                    // }
                });
            },
            // 各组织人员潜在离职原因
            getOrgRetentionRiskListFun() {
                let params = {
                    size: this.tableOrgData.page.size,
                    current: this.tableOrgData.page.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getOrgRetentionRiskList(params).then((res) => {
                    this.tableOrgData.columns = [
                        {
                            label: "部门",
                            prop: "orgName",
                        },
                        {
                            label: "部门负责人",
                            prop: "leaderName",
                        },
                    ];
                    if (res.code == 200) {
                        let tableTitle = res.data.legend.map((item) => {
                            return {
                                label: item.name,
                                prop: item.code.replace(/\./g, "-"),
                            };
                        });
                        this.tableOrgData.columns =
                            this.tableOrgData.columns.concat(tableTitle);
                        this.tableOrgData.data = this.dotToline(
                            res.data.dataList,
                            "key"
                        );
                        this.tableOrgData.page = res.page;
                    }
                });
            },
            handleCurrentOrgChange(current) {
                this.tableOrgData.page.current = current;
                this.getOrgRetentionRiskListFun();
            },
            handleSizeOrgChange(size) {
                this.tableOrgData.page.size = size;
                this.getOrgRetentionRiskListFun();
            },
            // 各职位人员潜在离职原因
            getJobRetentionRiskListFun() {
                let params = {
                    size: this.tablePostData.page.size,
                    current: this.tablePostData.page.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getJobRetentionRiskList(params).then((res) => {
                    // console.log(res)
                    if (res.code == 200) {
                        this.tablePostData.columns = [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                            {
                                label: "职位",
                                prop: "postName",
                            },
                        ];
                        let tableTitle = res.data.legend.map((item) => {
                            return {
                                label: item.name,
                                prop: item.code.replace(/\./g, "-"),
                            };
                        });
                        this.tablePostData.columns =
                            this.tablePostData.columns.concat(tableTitle);
                        this.tablePostData.data = this.dotToline(
                            res.data.dataList,
                            "key"
                        );
                        this.tablePostData.page = res.page;
                    }
                });
            },
            handleCurrentPostChange(current) {
                this.tablePostData.page.current = current;
                this.getJobRetentionRiskListFun();
            },
            handleSizePostChange(size) {
                this.tablePostData.page.size = size;
                this.getJobRetentionRiskListFun();
            },
            // 人才离职潜在原因
            getUserRetentionRiskListFun() {
                let params = {
                    size: this.tableStaffData.page.size,
                    current: this.tableStaffData.page.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getUserRetentionRiskList(params).then((res) => {
                    // console.log(res)
                    if (res.code == 200) {
                        this.tableStaffData.columns = [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                            {
                                label: "姓名",
                                prop: "leaderName",
                            },
                            {
                                label: "岗位",
                                prop: "postName",
                            },
                        ];
                        let tableTitle = res.data.legend.map((item) => {
                            return {
                                label: item.name,
                                prop: item.code.replace(/\./g, "-"),
                            };
                        });
                        this.tableStaffData.columns =
                            this.tableStaffData.columns.concat(tableTitle);
                        this.tableStaffData.data = this.dotToline(
                            res.data.dataList,
                            "key"
                        );
                        this.tableStaffData.page = res.page;
                    }
                });
            },
            handleCurrentStaffChange(current) {
                this.tableStaffData.page.current = current;
                this.getUserRetentionRiskListFun();
            },
            handleSizeStaffChange(size) {
                this.tableStaffData.page.size = size;
                this.getUserRetentionRiskListFun();
            },
            // 人才离职风险详情
            getRetentionRiskListFun() {
                let params = {
                    size: this.tableRiskData.page.size,
                    current: this.tableRiskData.page.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getRetentionRiskList(params).then((res) => {
                    if (res.code == 200) {
                        this.tableRiskData.data = res.data;
                        this.tableRiskData.page = res.page;
                    }
                });
            },
            handleCurrentRiskChange(current) {
                this.tableRiskData.page.current = current;
                this.getRetentionRiskListFun();
            },
            handleSizeRiskChange(size) {
                this.tableRiskData.page.size = size;
                this.getRetentionRiskListFun();
            },
            dotToline(param, type, valueKey) {
                if (Array.isArray(param)) {
                    if (param.length == 0) {
                        return;
                    }
                    param.forEach((item) => {
                        if (typeof item == "object") {
                            for (const key in item) {
                                if (item.hasOwnProperty(key)) {
                                    if (type == "key") {
                                        let newKey = key.split(".").join("-");
                                        item[newKey] = item[key];
                                    } else if (type == "value") {
                                        let val = item[valueKey];
                                        item[valueKey] = val.split(".").join("-");
                                    }
                                    // delete item[key];
                                }
                            }
                        }
                    });
                    return param;
                }
            },
            // --------
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgRetentionRisk(params).then((res) => {
                    if (res.code == "200") {
                        this.initChart(res.data);
                    }
                });
            },
            orgRiskDetailsFn() {
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgRiskDetails(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
            orgRetentionMatrixFn() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgRetentionMatrix(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.substitutability = res.data.substitutability;
                        this.retentionMatrix = res.data.retentionMatrix;
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>