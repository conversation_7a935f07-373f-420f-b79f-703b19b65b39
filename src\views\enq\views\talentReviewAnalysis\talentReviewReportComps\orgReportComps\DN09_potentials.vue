<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <slot></slot>
        <div class="page_second_title">人员潜力</div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24">
                <div class="item_title">潜力评价整体表现</div>
                <div class="distribution_box">
                    <div class="box_title">评价内容</div>
                    <div class="box_item" v-for="item in moduleList">
                        <div class="item_list">
                            <div class="item_text head_text">
                                {{ item.disctionary.name }}
                            </div>
                            <div
                                class="item_text"
                                v-for="list in item.modelOption"
                            >
                                {{ list.optionContent }}
                            </div>
                        </div>
                        <div class="item_num">
                            <div class="num head_num">
                                {{ item.disctionary.score }}分
                            </div>
                            <div class="num" v-for="list in item.modelOption">
                                {{ list.userNum }}人
                            </div>
                        </div>
                    </div>
                </div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    // import {
    //     orgPersonalDeve,
    //     orgTalentDeve,
    //     performanceMatrix,
    //     potentialMatrix,
    // } from "../../../../request/api";
    import {
        getPersonnelPotential,
        getPotentialDetails,
        getPotentialUserdist,
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import talentClassifyMatrix from "@/components/talent/common/talentClassifyMatrix";
    import talentMatrix from "@/components/talent/common/talentMatrix";
    import listComp from "./components/listComp.vue";
    export default {
        name: "orgRPotentials",
        props: ["enqId", "orgCode", "isPdf"],
        components: { tableComps, talentClassifyMatrix, talentMatrix, listComp },
        data() {
            return {
                size: 10,
                current: 1,
                kpiRankOption: [],
                competenceRankOptions: [],
                developmentOptions: [],
                developmentCapability: [],
                kpiCapablity: {},
                moduleList: [],
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "人员潜力分布",
                        elSpan: 8,
                        chartHeight: "200",
                        chartType: "YBar",
                        dataKey: "potentialEval",
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "整体潜力实际",
                        elSpan: 16,
                        chartType: "XBar",
                        dataKey: "potentialActual",
                    },
                ],
                listArr: [
                    {
                        title: "人员潜力详情",
                        ajaxUrl: getPotentialDetails,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                        ],
                        afterColumns: [
                            {
                                label: "系统评级",
                                prop: "sysGrade",
                            },
                            {
                                label: "上级评价等级",
                                prop: "grade",
                            },
                        ],
                    },
                ],
            };
        },
        created() {
            this.getPersonnelPotentialFun();
            // this.getPotentialDetailsFun()
        },
        mounted() {},
        methods: {
            getPersonnelPotentialFun() {
                getPersonnelPotential({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then((res) => {
                    if (res.code == 200) {
                        this.initChart(res.data);
                    }
                });
                getPotentialUserdist({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then((res) => {
                    console.log(res);
                    this.moduleList = res.data;
                });
            },
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                        padding: 115

                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },

            // ---------
            // init() {
            //     let docList = [
            //         "KPI_RANK",
            //         "COMPETENCE_RANK",
            //         "DEVELOPMENT_POTENTIAL",
            //     ];
            //     this.$getDocList(docList).then((res) => {
            //         this.kpiRankOption = this.$util
            //             .deepClone(res.KPI_RANK)
            //             .reverse();
            //         this.competenceRankOptions = res.COMPETENCE_RANK;
            //         this.developmentOptions = res.DEVELOPMENT_POTENTIAL;
            //     });
            //     this.getData();
            //     this.orgRiskDetailsFn();
            //     this.performanceMatrixFn();
            //     this.potentialMatrixFn();
            // },

            // getData() {
            //     let params = {
            //         enqId: this.enqId,
            //         orgCode: this.orgCode,
            //     };
            //     orgTalentDeve(params).then((res) => {
            //         if (res.code == "200") {
            //             this.initChart(res.data);
            //         }
            //     });
            // },
            // orgRiskDetailsFn() {
            //     let params = {
            //         size: this.size,
            //         current: this.current,
            //         enqId: this.enqId,
            //         orgCode: this.orgCode,
            //     };
            //     orgPersonalDeve(params).then((res) => {
            //         console.log(res);
            //         if (res.code == 200) {
            //             this.$set(this.tableData, "data", res.data);
            //             this.$set(this.tableData, "page", res.page);
            //         }
            //     });
            // },
            // // 能力绩效矩阵
            // performanceMatrixFn() {
            //     let params = {
            //         enqId: this.enqId,
            //         orgCode: this.orgCode,
            //     };
            //     performanceMatrix(params).then((res) => {
            //         console.log(res);
            //         if (res.code == 200) {
            //             this.kpiCapablity = res.data;
            //         }
            //     });
            // },
            // // 能力潜力矩阵
            // potentialMatrixFn() {
            //     let params = {
            //         enqId: this.enqId,
            //         orgCode: this.orgCode,
            //     };
            //     potentialMatrix(params).then((res) => {
            //         console.log(res);
            //         if (res.code == 200) {
            //             this.developmentCapability = res.data;
            //         }
            //     });
            // },
        },
    };
</script>
 
<style scoped lang="scss">
.distribution_box {
        .box_title {
            background-color: #00b0f0;
            color: #fff;
            line-height: 28px;
            padding-left: 16px;
        }
        .box_item {
            border-bottom: 1px solid #00b0f0;
            border-left: 1px solid #00b0f0;
            color: #00b0f0;
            .item_text {
                flex: 1;
                padding: 8px 5px;
                &.head_text {
                    flex: 0 0 120px;
                    text-align: center;
                    color: #333;
                    border-right: 1px solid #00b0f0;
                }
            }
            .item_num {
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                justify-content: flex-start;
                line-height: 28px;
                text-align: center;
                .num {
                    flex: 1;
                    &.head_num {
                        flex: 0 0 120px;
                        border-right: 1px solid #00b0f0;
                    }
                }
            }
        }
        .item_list {
            display: flex;
            flex-flow: row nowrap;
            align-items: stretch;
            justify-content: flex-start;
            border-bottom: 1px solid #00b0f0;
        }
    }
</style>