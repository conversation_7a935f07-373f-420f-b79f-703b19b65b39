// 数据管理
import Layout from '@/layout/index.vue'
import entpRoutes from '@/views/entp/router/index.js'
const dataManage = [
  {
    path: '/dataManage',
    component: Layout,
    redirect: '/dataManage/home',
    children: [
      {
        path: '/dataManage/home',
        meta: {
          title: '数据管理'
        },
        component: () => import('@/views/dataManage/home.vue'),
        children: [...entpRoutes]
      }
    ]
  }
]

export default dataManage
