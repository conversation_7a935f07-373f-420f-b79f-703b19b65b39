<!--
避免在同一个页面中使用相同的tabs组件，但样式不同
仿照tabs编写的组件
功能：
点击不用同选项返回该项的数据及该项的下标

用处: 选项对应的dom相同，只做切换数据显示
-->
<template>
  <div class="tabs_custom_main" ref="tabsCustom">
    <div
      class="tabs_item"
      v-for="(item, index) in tabsData"
      :key="item.name"
      :ref="item.name == activeIndex ? 'active' : ''"
      :class="{ active: item.name == activeIndex }"
      @click="tabsClick(index, item.name, $event)"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
const noop = () => {}
const props = defineProps({
  tabsData: {
    type: Array,
    default: () => [{ label: '', name: '' }]
  },
  activeName: {
    type: String,
    default: 0
  },
  defaultWidth: {
    type: Number,
    default: 28
  },
  handleClick: {
    type: Function,
    default: () => {}
  }
})
const activeIndex = ref(props.activeName)
const tabsCustom = ref(null)
onMounted(() => {
  if (tabsCustom.value) {
    // 自动激活第一个tab
    const activeTab = tabsCustom.value.querySelector('.active')
    if (activeTab) activeTab.click()
  }
})
function tabsClick(index, tabDataName, e) {
  activeIndex.value = tabDataName
  props.handleClick(props.tabsData[index], index)
}
</script>

<style scoped lang="scss">
.tabs_custom_main {
  position: relative;
  width: 100%;
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  margin-bottom: 15px;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background: #e4e7ed;
    z-index: 1;
  }
  .tabs_item {
    position: relative;
    cursor: pointer;
    margin-right: 20px;
    line-height: 45px;
    padding-left: 12px;
    padding-right: 12px;
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 0;
      width: 0;
      height: 2px;
      background: #0099ff;
      z-index: 2;
    }
    &:hover {
      color: #0099ff;
    }
    &.active {
      color: #0099ff;
      &::after {
        transition: width ease-in-out 0.3s;
        width: 100%;
      }
    }
    &:first-of-type {
      .default_bar {
        display: block;
      }
    }
    &:last-of-type {
      margin-right: 0;
    }
  }
}
</style>
