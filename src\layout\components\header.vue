<template>
  <div class="header">
    <Breadcrumb />
    <div class="header-right">
      <div class="information">
        <img src="@/assets/imgs/layout/info.webp" alt="" />
      </div>
      <div class="user-name">{{ userStore.userInfo.userName }}</div>
      <div class="user">
        <el-dropdown>
          <img src="@/assets/imgs/layout/user.webp" alt="" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>
<script setup>
import Breadcrumb from './breadcrumb.vue'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const logout = () => {
  console.log('退出登录')
  userStore.logout()
}
</script>
<style lang="scss" scoped>
.header {
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0);
  padding-right: 10px;
  .breadcrumb {
    flex: 1;
    padding-left: 20px;
    a {
      color: #6da0ac;
    }
  }
  .header-right {
    display: flex;
    align-items: center;
    .user-name {
      margin-right: 20px;
    }
  }
  .information,
  .user {
    width: 36px;
    height: 36px;
    margin-right: 20px;
    border-radius: 50%;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
    }
    &:hover {
      box-shadow: 0px 0px 10px 0px rgba(124, 182, 237, 0.5);
    }
  }
}
</style>
