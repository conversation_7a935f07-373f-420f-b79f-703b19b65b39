<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">离职类型</div>
          <div class="content_item_content" id="quit_type"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">离职岗位职层</div>
          <div class="content_item_content" id="quit_job_level"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">离职部门</div>
          <div class="content_item_content" id="quit_org"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">离职岗位</div>
          <div class="content_item_content" id="quit_post"></div>
        </div>
      </div>
      <!-- <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title clearfix">
                        详情列表
                        <el-button class="fr" type="primary" size="mini"
                            >导出</el-button
                        >
                    </div>
                    <div class="content_item_content">
                        <tableComponet
                            @handleSizeChange="handleSizeChange"
                            @handleCurrentChange="handleCurrentChange"
                            :tableData="tableData"
                            :needIndex="true"
                        ></tableComponet>
                    </div>
                </div>
            </div> -->
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { queryCompetenceList, talentQuit } from '../../../../../request/api.js'
import asideFilterBar from '../../asideFilterBar.vue'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'

const route = useRoute()
const enqId = ref(route.query.enqId)
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref({})

const quitType = reactive({
  data: []
})

const quitJobLevel = reactive({
  data: []
})

const quitOrg = reactive({
  data: []
})

const quitPost = reactive({
  data: []
})

const page = ref(1)
const size = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '员工编码',
      prop: 'employee_code'
    },
    {
      label: '员工姓名',
      prop: 'user_name'
    },
    {
      label: '所属组织',
      prop: 'org_name'
    },
    {
      label: '任职岗位',
      prop: 'post_name'
    },
    {
      label: '职层',
      prop: 'job_level_name'
    },
    {
      label: '上级姓名',
      prop: 'competence'
    },
    {
      label: '离职日期',
      prop: 'development'
    },
    {
      label: '离职状态',
      prop: 'talent'
    },
    {
      label: '离职原因',
      prop: 'a'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('quit_type', 'YBar', '350', '220', quitType)
  echartsRenderPage('quit_job_level', 'YBar', '350', '220', quitJobLevel)
  echartsRenderPage('quit_org', 'YBar', '360', '220', quitOrg)
  echartsRenderPage('quit_post', 'YBar', '360', '220', quitPost)
}

const talentQuitFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await talentQuit(params)
    if (res.code == 200) {
      const data = res.data
      quitType.data = data.quitType
      quitJobLevel.data = data.quitJobLevel
      quitOrg.data = window.$util.addPercentSign(data.quitOrg, 'value')
      quitPost.data = data.quitPost
      initChart()
    }
  } catch (error) {
    console.error('获取离职数据失败:', error)
  }
}

const getCode = (orgCode, jobClassCode) => {
  jobClassCode.value = jobClassCode
  orgCode.value = orgCode
  talentQuitFun()
  getTableData()
}

const handleCurrentChange = size => {
  page.value = size
  getTableData()
}

const handleSizeChange = page => {
  size.value = page
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryCompetenceList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error('获取表格数据失败:', error)
  }
}

onMounted(() => {
  talentQuitFun()
  filterData.value = route.attrs.filterData
  getTableData()
})
</script>

<style scoped lang="scss"></style>
