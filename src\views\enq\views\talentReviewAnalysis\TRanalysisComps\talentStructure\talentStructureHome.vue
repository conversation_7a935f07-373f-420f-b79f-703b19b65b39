<template>
  <div class="detail_main">
    <div class="detail_main_aside">
      <div class="page_third_title">分析主题</div>
      <tabsLink :tabsData="tabsLinkData" :isVertical="true"></tabsLink>
    </div>
    <div class="detail_main_content">
      <div class="page_third_title">分析视图</div>
      <router-view :filterData="filterData"></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { queryOrgWithSubChildren, queryJobClassByOrg } from '../../../../request/api'
import tabsLink from '@/components/talent/tabsComps/tabsLink'

const route = useRoute()

const tabsLinkData = ref([
  {
    id: 'asdfsdfqteqa',
    name: '岗位结构',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentStructure/postStructure'
  },
  {
    id: 'asdqrefafasdfa',
    name: '工作年限结构',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentStructure/yearsStructure'
  },
  {
    id: 'asasdfdqrefasffa',
    name: '晋升结构',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentStructure/promoteStructure'
  },
  {
    id: 'asdqrefaasfagdasdfa',
    name: '任职资格-工作经验',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentStructure/workExperienceTS'
  },
  {
    id: 'aadsfsdqfasdfrefa',
    name: '任职资格-沟通',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentStructure/communicate'
  },
  {
    id: 'asfadqrefdasfa',
    name: '任职资格-工作环境',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentStructure/workAmbient'
  }
])

const filterData = reactive({
  orgData: [],
  jobClass: []
})

const queryOrgWithSubChildrenFun = async enqId => {
  try {
    const res = await queryOrgWithSubChildren({ enqId })
    if (res.code == 200) {
      filterData.orgData = res.data
    }
  } catch (error) {
    console.error(error)
  }
}

const queryJobClassByOrgFun = async () => {
  try {
    const res = await queryJobClassByOrg()
    if (res.code == 200) {
      filterData.jobClass = res.data
    }
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  const enqId = route.query.enqId
  tabsLinkData.value.forEach(item => {
    item.path = `${item.path}?enqId=${enqId}`
  })
  queryOrgWithSubChildrenFun(enqId)
  queryJobClassByOrgFun()
})
</script>

<style lang="scss" scoped>
.post_list_wrap {
  padding: 16px;
  display: flex;
  flex-flow: row wrap;
  .post_list {
    padding: 3px 5px;
    margin-right: 6px;
    border: 1px solid #0099fd;
    color: #0099fd;
  }
}
</style>
