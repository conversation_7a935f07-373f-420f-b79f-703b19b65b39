// 切换显示不同的tab-pane
<template>
  <div
    :class="{
      left: !isDefaultTheme,
      need_title: needTitle,
      third_title: titleLevel == 'third',
      size_mini: size == 'mini'
    }"
    class="clearfix"
  >
    <div :class="'page_' + titleLevel + '_title'" v-if="needTitle">{{ title }}</div>
    <el-tabs :tab-position="positionFun()" v-model="tabsDefaultName" @tab-click="changeTab">
      <el-tab-pane
        v-for="tab in tabsData"
        :label="tab.label"
        :name="tab.name"
        :key="tab.name"
        :disabled="tab.show == undefined ? false : !tab.show"
      >
        <keep-alive v-if="keep">
          <component :is="tab.componentName"></component>
        </keep-alive>
        <component v-else :is="tab.componentName"></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, defineProps, defineEmits } from 'vue'

const props = defineProps({
  tabsData: Array,
  leaveFun: Function,
  tabClick: Function,
  tabsDefault: String,
  keep: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'normal'
  },
  isDefaultTheme: {
    type: Boolean,
    default: false
  },
  disabledTabs: {
    type: Boolean,
    default: false
  },
  needPane: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: '标题'
  },
  needTitle: {
    type: Boolean,
    default: false
  },
  titleLevel: {
    type: String,
    default: 'second'
  }
})
const emit = defineEmits(['tabsChange'])

const tabsDefaultName = ref('')

watch(
  () => props.tabsDefault,
  val => {
    tabsDefaultName.value = val
  },
  { immediate: true }
)

onMounted(() => {
  if (!props.tabsDefault) {
    tabsDefaultName.value = props.tabsData[0]?.name || ''
  } else {
    tabsDefaultName.value = props.tabsDefault
  }
})

function positionFun() {
  return props.isDefaultTheme ? 'top' : 'left'
}
function changeTab(tab) {
  emit('tabsChange', { name: tab.props.name })
}
</script>

<style scoped lang="scss">
.left {
  :deep(.el-tabs--left .el-tabs__header.is-left) {
    width: 240px;
    padding: 10px 32px 0 16px;
    border-right: 1px solid #e4e7ed;
    margin-right: 36px;
  }

  :deep(.el-tabs--left .el-tabs__item.is-left) {
    text-align: center;

    &.is-disabled {
      color: #ababab;
      cursor: not-allowed;
    }
  }

  :deep(.el-tabs__active-bar) {
    display: none;
  }

  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }

  :deep(.el-tabs__item) {
    text-align: center;
    background-color: #ebf4ff;
    margin-bottom: 16px;
    line-height: 40px;
    font-size: 14px;
    font-weight: 500;
    color: #525e6c;
    border-radius: 2px;
    cursor: pointer;
  }

  :deep(.el-tabs__item.is-active) {
    background-color: #0099ff;
    color: #fff !important;
    cursor: pointer !important;
  }
  &.size_mini {
    :deep(.el-tabs--left .el-tabs__header.is-left) {
      width: 114px;
      padding: 10px 16px 0 0px;
      border-right: 1px solid #e4e7ed;
      margin-right: 16px;
    }
    :deep(.el-tabs__item) {
      background-color: transparent;
      line-height: 32px;
      height: 32px;
      color: #222222;
      margin-bottom: 2px;
      &.is-active {
        background-color: #0099ff;
        color: #fff !important;
        cursor: pointer !important;
      }
    }
  }
}

.need_title {
  :deep(.el-tabs--left),
  .el-tabs--right {
    overflow: visible;
  }

  :deep(.el-tabs__content) {
    margin-top: -37px;
  }

  :deep(.el-tabs--left .el-tabs__header.is-left) {
    // width: 220px !important;
    padding: 10px 16px 0 16px !important;
    border-right: 1px solid #e4e7ed;
    margin-right: 16px !important;
    margin-top: 44px;
  }

  &.third_title {
    :deep(.el-tabs__content) {
      margin-top: -44px;
    }
  }
}
</style>
