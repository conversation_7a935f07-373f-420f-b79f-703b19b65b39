<template>
  <div class="dialogue-detail">
    <ChatScheduling :id="sessionId" style="height: calc(100vh - 130px)"></ChatScheduling>
  </div>
</template>
<script setup>
defineOptions({ name: 'SchedulDetail' })
import ChatScheduling from '@/components/AI/chatScheduling.vue'
const route = useRoute()
const sessionId = ref('')
onMounted(() => {
  sessionId.value = route.params.id
})
watch(
  () => route.fullPath,
  () => {
    sessionId.value = route.params.id
  }
)
</script>
