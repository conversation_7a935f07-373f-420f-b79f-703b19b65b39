<template>
  <div class="variation public">
    <div class="table-main">
      <el-table ref="tableDataRef" :data="tableData" highlight-current-row style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="key1" label="指标名称" width="180" />
        <el-table-column prop="key2" label="目标类别" />
        <el-table-column prop="key3" label="指标单位" />
        <el-table-column prop="key4" label="责任人" />
        <el-table-column prop="key5" label="当期" />
        <el-table-column prop="key6" label="上期" />
        <el-table-column prop="key7" label="当期目标" />
        <el-table-column prop="key8" label="上期目标" />
        <el-table-column prop="key9" label="目标变化">
          <template #default="scope">
            <div
              :style="{
                color: scope.row.key9[0] == '+' ? '#40D476' : scope.row.key9[0] == '-' ? '#FF5E4C' : 'rgb(96, 98, 102)'
              }"
            >
              {{ scope.row.key9 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="key10" label="当前实际" />
        <el-table-column prop="key11" label="上期实际" />
        <el-table-column prop="key12" label="实际变化">
          <template #default="scope">
            <div
              :style="{
                color:
                  scope.row.key12[0] == '+' ? '#40D476' : scope.row.key12[0] == '-' ? '#FF5E4C' : 'rgb(96, 98, 102)'
              }"
            >
              {{ scope.row.key12 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column>
          <template #default="scope">
            <div
              class="btn"
              @click="
                openAi(
                  `${scope.row.key1}、${scope.row.key2}、当期${scope.row.key5}、上期${scope.row.key6}、当前目标${scope.row.key7}、上期目标${scope.row.key8}、目标变化${scope.row.key9}、当前实际${scope.row.key10}、上期实际${scope.row.key11}、实际变化${scope.row.key12}`
                )
              "
            >
              AI解读
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 已选择 -->
    <div class="active">
      <div class="title">
        <div class="text">已选指标：</div>
        <div class="name">库存周转天数</div>
      </div>
      <div class="page-title-line">同岗位人员指标表现（库存周转天数）</div>
      <el-table ref="tableDataRef2" highlight-current-row :data="table1" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="key1" label="指标名称" />
        <el-table-column prop="key2" label="人员" />
        <el-table-column prop="key3" label="部门" />
        <el-table-column prop="key4" label="岗位" />
        <el-table-column prop="key5" label="指标目标" />
        <el-table-column prop="key6" label="实际表现" />
        <el-table-column prop="key7" label="达成率" />
        <el-table-column prop="key8" label="同组织排名" />
      </el-table>
      <div class="page-title-line">关联人员能力（刘淇）</div>
      <el-table :data="table2" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="key1" label="人员能力" />
        <el-table-column prop="key2" align="center" label="相关性" />
        <el-table-column prop="key3" label="关联逻辑" width="600" />
        <el-table-column prop="key4" align="center" label="上期能力" width="100">
          <template #default="scope">
            <div class="num" :style="{ background: scope.row.key4 >= 60 ? '#40D476' : '#FF5E4C' }">
              {{ scope.row.key4 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="key5" align="center" label="本期能力" width="100">
          <template #default="scope">
            <div class="num" :style="{ background: scope.row.key5 >= 60 ? '#40D476' : '#FF5E4C' }">
              {{ scope.row.key5 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="key6" align="center" label="能力变化" width="100">
          <template #default="scope">
            <div class="num" :style="{ background: scope.row.key6 >= 60 ? '#40D476' : '#FF5E4C' }">
              {{ scope.row.key6 }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="page-title-line">能力变化对指标的影响（基于最佳实践库）</div>
      <el-table :data="table3" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="key1" label="能力项" />
        <el-table-column prop="key2" align="center" label="提升值" />
        <el-table-column prop="key3" label="能力对指标的影响" width="900" />
      </el-table>
      <div class="page-title-line">决策风格变化对指标的影响（基于最佳实践库）</div>
      <el-table :data="table4" :stripe="true" style="width: 100%">
        <el-table-column prop="key1" label="决策模式风格（上次）" />
        <el-table-column prop="key2" label="决策模式风格（本次）" />
        <el-table-column prop="key3" label="决策风格变化对指标的影响" width="980" />
      </el-table>
      <div class="page-title-line">人员关键任务执行情况</div>
      <el-table :data="table5" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="key1" label="任务名称" width="200" />
        <el-table-column prop="key2" label="关联能力" />
        <el-table-column prop="key3" label="决策风格变化对指标的影响" width="400" />
        <el-table-column prop="key4" label="责任人" />
        <el-table-column prop="key5" label="优先级" />
        <el-table-column prop="key6" label="输出成果" width="180" />
        <el-table-column prop="key7" label="状态" />
      </el-table>
    </div>
  </div>
</template>
<script setup>
const openAi = inject('openAi')
const table5 = [
  {
    key1: '库存策略与 S&OP 协同培训',
    key2: '库存结构优化',
    key3: '1. 解析 EOQ 模型与 S&OP 库存目标联动逻辑 2. 案例分析 “如何通过 S&OP 降低滞销品库存占比” 3. 开发《库存策略 S&OP 适配度评估表》',
    key4: '人员名称1',
    key5: '高',
    key6: '《库存结构优化报告》',
    key7: '已完成'
  },
  {
    key1: '库存策略与 S&OP 协同培训',
    key2: '库存结构优化',
    key3: '1. 解析 EOQ 模型与 S&OP 库存目标联动逻辑 2. 案例分析 “如何通过 S&OP 降低滞销品库存占比” 3. 开发《库存策略 S&OP 适配度评估表》',
    key4: '人员名称1',
    key5: '高',
    key6: '《库存结构优化报告》',
    key7: '已完成'
  },
  {
    key1: '库存策略与 S&OP 协同培训',
    key2: '库存结构优化',
    key3: '1. 解析 EOQ 模型与 S&OP 库存目标联动逻辑 2. 案例分析 “如何通过 S&OP 降低滞销品库存占比” 3. 开发《库存策略 S&OP 适配度评估表》',
    key4: '人员名称1',
    key5: '高',
    key6: '《库存结构优化报告》',
    key7: '已完成'
  }
]
const table4 = [
  {
    key1: '经验依赖型',
    key2: '数据驱动型',
    key3: "从 ' 历史经验套用 ' 转为 ' 数据建模分析 '，人员通过引入机器学习算法、实时销售数据（延迟≤15 分钟）动态调整安全库存（冗余度降低 20%），需求预测准确率从 75% 提升至 88%，滞销品积压率下降 18%，库存周转天数从 82 天缩短至 68 天，数据驱动决策使周转效率提升 17%。"
  },
  {
    key1: '高风险型',
    key2: '中风险型',
    key3: "从 ' 激进压缩库存 / 单一爆品押注 ' 转为 ' 风险量化 + 试点验证 '，人员建立风险评估矩阵（如需求波动系数＞30% 时安全库存上浮 15%），高风险策略先进行单区域试销（如新品试产 5000 台验证需求），断货率从 12% 降至 5%，滞销品占比从 25% 降至 12%，库存周转天数波动幅度收窄 30%，稳定在 70-75 天区间，风险可控性提升带动周转质量优化。"
  },
  {
    key1: '小组合作型',
    key2: '跨部门协同',
    key3: "从 ' 过度共识导向（决策拖延）' 转为 ' 责任主体明确 + 流程化协同 '，人员通过 RACI 矩阵界定库存策略最终责任人（如供应链规划经理），建立 24 小时跨部门响应机制（如促销需求调整 4 小时内同步至采购计划），需求到供应的决策链条从 72 小时缩短至 24 小时，库存策略执行偏差率从 35% 降至 15%，因协作低效导致的库存滞留时间减少 40%，周转天数加速 10-15 天。"
  },
  {
    key1: '性价比平衡',
    key2: '高投入高回报型',
    key3: "从 ' 成本优先（限制系统 / 人力投入）' 转为 ' 效率优先（资源聚焦关键环节）'，人员主导部署智能库存管理系统（如 SAP IBP），实现数据自动采集（覆盖率 95%）、智能补货建议（准确率 85%），并投入专项培训提升团队算法应用能力，库存周转天数计算颗粒度从月度细化至周度，策略调整频次提升 3 倍，试点品类周转天数缩短 25%，带动整体指标下降 12-20 天（需关注长期 ROI 平衡）。"
  }
]
const table3 = [
  {
    key1: '物流与仓储优化能力',
    key2: '5',
    key3: '人员通过专业库位规划（如 ABC 分类法应用）、仓储流程设计（如波次拣货策略），使库存出入库效率提升 40%，滞销品仓储占用面积减少 30%；通过物流网络优化（如区域仓选址分析、配送路线规划），缩短在途库存周期 15%-20%，直接推动库存周转天数压缩 8-12 天。'
  },
  {
    key1: '需求预测分析能力',
    key2: '4',
    key3: '分析师运用机器学习算法、市场调研数据（如消费者问卷设计与分析），将需求预测准确率从 70% 提升至 85%，避免过量采购（滞销库存占比下降 10%）和缺货（断货率降低 60%），安全库存设定误差率从 25% 降至 10%，带动库存周转天数减少 10-18 天。'
  },
  {
    key1: '跨部门沟通协调能力',
    key2: '4',
    key3: '人员通过跨部门协作机制设计（如 RACI 矩阵制定、周度协同会议主持），实现销售促销计划（提前 7 天同步）、生产产能数据（实时共享）与库存策略的高效对齐，需求变化响应时效从 72 小时缩短至 24 小时，减少因信息滞后导致的库存积压或紧急采购，间接缩短周转天数 6-10 天。'
  },
  {
    key1: '供应链规划能力',
    key2: '3',
    key3: '规划人员运用 S&OP（销售与运营计划）、DRP（分销资源计划）等工具，统筹采购周期（如长周期原料提前 6 个月锁定产能）、生产排期（如旺季产能预分配）与库存策略（如 JIT 模式推行），使原材料库存周转天数从 50 天降至 35 天，成品库存与市场需求匹配度提升 30%，整体库存周转效率提高 20%-25%。'
  },
  {
    key1: '生产计划协调能力',
    key2: '3',
    key3: '通过产能负荷分析（如瓶颈设备识别与排程优化）、插单优先级算法（如客户价值导向排序），将生产计划达成率从 75% 提升至 90%，在制品库存积压减少 25%；快速响应订单变更（48 小时内完成计划调整），避免生产过剩导致的成品库存滞留，推动周转天数减少 10-15 天。'
  },
  {
    key1: '库存成本管控能力',
    key2: '3',
    key3: '运用 EOQ（经济订货批量）模型、库存持有成本核算（如资金占用费、仓储损耗分摊），识别低效库存（周转次数＜2 次 / 年的品类占比从 25% 降至 15%），推动滞销品阶梯式降价（首月降 10%、次月降 20%）和临期品快速清仓，库存周转天数加速 12%-20%。'
  }
]
const table2 = [
  {
    key1: '供应链规划能力',
    key2: '极高',
    key3: '供应链规划决定库存结构合理性，直接影响各环节库存水位。全局性资源调配和网络设计（如仓网布局、补货周期）可系统性降低冗余库存，缩短周转周期。',
    key4: '66',
    key5: '69',
    key6: '+3'
  },
  {
    key1: '需求预测分析能力',
    key2: '极高',
    key3: '精准的需求预测是库存计划的核心依据。预测偏差将导致库存积压或短缺，直接影响周转天数。动态预测能力（如滚动预测、AI模型）可显著提升供需匹配度。',
    key4: '55',
    key5: '58',
    key6: '+3'
  },
  {
    key1: '库存策略制定能力',
    key2: '极高',
    key3: '制定科学的安全库存策略、ABC分类管理、呆滞品处理机制，直接影响库存周转效率。策略制定者需平衡服务水平与库存成本，避免过度囤积或频繁断货。',
    key4: '69',
    key5: '72',
    key6: '+3'
  },
  {
    key1: '数据分析与建模能力',
    key2: '高',
    key3: '通过数据挖掘识别库存异常（如库龄超标SKU）、建立库存优化模型（如动态安全水位计算），为决策提供量化依据。缺乏数据驱动易导致经验主义误判。',
    key4: '69',
    key5: '72',
    key6: '+3'
  },
  {
    key1: '供应链协同能力',
    key2: '高',
    key3: '跨部门/企业协同（如CPFR、VMI）可打破信息孤岛，减少牛鞭效应。协同能力不足将导致各环节局部优化，放大整体库存波动（如生产过量、采购冗余）。',
    key4: '52',
    key5: '55',
    key6: '+3'
  },
  {
    key1: '采购与库存联动能力',
    key2: '高',
    key3: '采购批量、频次与库存策略的匹配度直接影响库存水位。联动能力强的采购团队能动态调整订货策略（如经济批量优化、JIT采购），避免集中到货导致的库存峰值。',
    key4: '67',
    key5: '70',
    key6: '+3'
  },
  {
    key1: '生产计划协调能力',
    key2: '高',
    key3: '生产计划与库存策略的协同能力决定在制品和成品库存量。柔性生产能力（如小批量生产、快速换线）可减少生产过剩，避免因排产僵化导致的库存积压。',
    key4: '64',
    key5: '67',
    key6: '+3'
  },
  {
    key1: '物流与仓储优化能力',
    key2: '中高',
    key3: '仓储作业效率（如拣货速度、库位规划）影响库存周转速度，物流时效性决定在途库存天数。但该能力更多影响操作效率，对库存策略的全局性影响弱于前几项。',
    key4: '69',
    key5: '72',
    key6: '+3'
  },
  {
    key1: '跨部门沟通协调能力',
    key2: '中',
    key3: '沟通能力不足会导致信息滞后（如销售未及时共享促销计划），间接引发库存失衡。但属于支撑性能力，需通过影响其他核心能力（如预测、计划）间接作用于库存周转。',
    key4: '52',
    key5: '55',
    key6: '+3'
  },
  {
    key1: '库存成本管控能力',
    key2: '中高',
    key3: '成本管控意识驱动库存精细化管理（如减少高价值物料囤积），但过度关注成本可能牺牲周转效率（如为降低采购单价而大批量订货）。需与周转目标协同优化。',
    key4: '64',
    key5: '67',
    key6: '+3'
  }
]
const table1 = [
  {
    key1: '库存周转天数',
    key2: '王伟',
    key3: '',
    key4: '',
    key5: '68（+2）',
    key6: '67（+3）',
    key7: '98.5%（+1.6%）',
    key8: '1（+1）'
  },
  {
    key1: '库存周转天数',
    key2: '汪勇',
    key3: '',
    key4: '',
    key5: '',
    key6: '',
    key7: '',
    key8: ''
  },
  {
    key1: '库存周转天数',
    key2: '刘淇',
    key3: '',
    key4: '',
    key5: '',
    key6: '',
    key7: '',
    key8: ''
  }
]
const tableData = [
  {
    key1: '供应商交付准时率',
    key2: '年度目标',
    key3: '',
    key4: '',
    key5: '2025年',
    key6: '2024年',
    key7: '98%',
    key8: '95%',
    key9: '+3%',
    key10: '95%',
    key11: '92%',
    key12: '+3%'
  },
  {
    key1: '库存周转天数',
    key2: '年度目标',
    key3: '',
    key4: '',
    key5: '2025年',
    key6: '2024年',
    key7: '80',
    key8: '85',
    key9: '-5',
    key10: '82',
    key11: '86',
    key12: '-4%'
  },
  {
    key1: '供应链协同指数',
    key2: '年度目标',
    key3: '',
    key4: '',
    key5: '2025年',
    key6: '2024年',
    key7: '85分',
    key8: '85分',
    key9: '无变化',
    key10: '82.5分',
    key11: '无变化',
    key12: '无变化'
  },
  {
    key1: '替代供应商储备率',
    key2: '月度目标',
    key3: '',
    key4: '',
    key5: '2025-05',
    key6: '2025-04',
    key7: '85%',
    key8: '80%',
    key9: '+5%',
    key10: '75%',
    key11: '77%',
    key12: '-2%'
  },
  {
    key1: '物流成本占比',
    key2: '月度目标',
    key3: '',
    key4: '',
    key5: '2025-05',
    key6: '2025-04',
    key7: '4.5%',
    key8: '4.5%',
    key9: '无变化',
    key10: '4.8%',
    key11: '无变化',
    key12: '无变化'
  }
]
const tableDataRef = ref(null)
const tableDataRef2 = ref(null)
onMounted(() => {
  tableDataRef.value.setCurrentRow(tableData[1])
  tableDataRef2.value.setCurrentRow(table1[2])
})
</script>
<style lang="scss" scoped>
@import '../indicatorImprove.scss';
</style>
