<template>
  <div class="riskEarlyWarning all">
    <div class="key-index">
      <div class="table-main">
        <el-table ref="tableDataRef" :data="tableData" highlight-current-row>
          <el-table-column type="index" align="center" label="序号" width="80" />
          <el-table-column prop="kpiName" label="指标名称" width="200" />
          <el-table-column prop="targetClass" label="目标类别" width="120" />
          <el-table-column prop="targetCycle" label="目标期间" />
          <el-table-column prop="kpiUnit" label="指标单位" />
          <el-table-column prop="personLiable" label="责任人" />
          <el-table-column prop="targetValue" label="指标目标" />
          <el-table-column prop="actualPerformance" label="实际表现" />
          <el-table-column prop="threshold" width="200" label="阈值" show-overflow-tooltip />
          <el-table-column>
            <template #default="scope">
              <div v-if="scope.row.kpiName == '库存周转天数'" class="btn" @click="isActive = true">风险预测</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 已选择 -->
    <div class="active" v-if="isActive">
      <div class="title">
        <div class="text">已选指标：</div>
        <div class="name">库存周转天数</div>
      </div>
      <div class="page-title-line">风险预警（库存周转天数）</div>
      <el-table :data="table1" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="60" />
        <el-table-column prop="key1" label="指标" />
        <el-table-column prop="key2" label="预警等级" />
        <el-table-column prop="key3" label="触发规制（阈值规则）" width="180" />
        <el-table-column prop="key4" label="触发规制（动态基线）" width="180" />
        <el-table-column prop="key5" label="是否触发" width="180" />
        <el-table-column prop="key6" label="风险传导模拟" width="280" />
        <el-table-column prop="key7" align="center" label="改善任务" width="80" />
        <el-table-column prop="key8" align="center" label="状态" width="70" />
        <el-table-column>
          <template #default="scope">
            <div
              class="btn"
              @click="
                openAi(
                  `${scope.row.key1}、${scope.row.key2}、${scope.row.key3}、${scope.row.key4}、${scope.row.key5}、${scope.row.key6}`
                )
              "
            >
              AI报告
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="page-title-line">改善任务（库存周转天数）</div>
      <el-table :data="table2" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="60" />
        <el-table-column prop="key1" label="可能触发风险事件" width="160" />
        <el-table-column prop="key2" label="应对举措" />
        <el-table-column prop="key3" label="关键行动" width="600" />
        <el-table-column prop="key4" label="责任人" />
        <el-table-column prop="key5" align="center" label="优先级" width="70" />
        <el-table-column prop="key6" label="输出成果" width="180" />
        <el-table-column prop="key7" align="center" label="状态" width="70" />
      </el-table>
    </div>
  </div>
</template>
<script setup>
defineOptions({ name: 'RiskEarlyWarning' })
import { trendRisk } from '@/assets/data/data9.js'

const isActive = ref(false)
const openAi = inject('openAi')
const tableData = ref(trendRisk.warningList)
const tableDataRef = ref(null)
onMounted(() => {
  tableDataRef.value.setCurrentRow(trendRisk.warningList[2])
})
const table1 = [
  {
    key1: '库存周转天数',
    key2: '关注级',
    key3: '偏离基准 10%-15%',
    key4: '对比同期下降 10%',
    key5: '动态基线触发',
    key6: '库存周转天数↑15% → 仓储成本占比↑2% → 现金流周转率↓0.5 次 → 紧急采购频次↑3 次 / 月 → 客户订单交付准时率↓5%',
    key7: '3 项',
    key8: '处理中'
  },
  {
    key1: '库存周转天数',
    key2: '警示级',
    key3: '偏离基准 15%-25%',
    key4: '对比同期下降 20%',
    key5: '阈值规则触发',
    key6: '库存周转天数↑22% → 呆滞库存金额↑300 万 → 资金占用成本↑80 万 / 年 → 采购计划准确率↓12% → 供应商交货延迟率↑8% → 客户投诉率↑10%',
    key7: '3 项',
    key8: '已关闭'
  },
  {
    key1: '库存周转天数',
    key2: '严重级',
    key3: '偏离 > 25%',
    key4: '对比同期下降 30%',
    key5: '阈值规则触发',
    key6: '库存周转天数↑35% → 仓储爆仓率↑40% → 现金流断裂风险↑橙色 → 生产停线频次↑2 次 / 周 → 核心客户流失率↑15% → 银行授信额度下调 20%',
    key7: '3 项',
    key8: '已关闭'
  }
]
const table2 = [
  {
    key1: '仓储成本超支风险',
    key2: '库存结构优化',
    key3: '1. 实施呆滞库存三级分类清理（红 / 黄 / 蓝），90 天以上库龄物料 30 日内通过促销、调拨、报废处理，压减占比至 15% 以下；2. 部署 AI 动态安全库存模型，A 类物料周转压缩至 15 天，ERP 系统自动预警补货；3. 引入自动化仓储系统，60 日内提升存储密度 30%，优化库位动线；4. 重谈仓储合同，40% 租金转为按周转量计费，设置达标折扣条款。',
    key4: '供应链总监',
    key5: '高',
    key6: '《库存结构优化报告》',
    key7: '进行中'
  },
  {
    key1: '现金流周转压力',
    key2: '资金效率提升',
    key3: '1. 客户信用政策与库存周转挂钩，周转超 45 天账期压缩至 30 天，2 周内完成全客户重评；2. 启动存货质押融资，争取利率≤LPR+150BP，10 日内到账 4800 万元；3. 战略供应商延长 15 天账期，同步争取 2% 现金折扣，3 周内完成 TOP10 供应商谈判；4. 建立现金流三级预警模型，模拟不同库存水位资金缺口，5 日内完成压力测试系统。',
    key4: 'CFO',
    key5: '中',
    key6: '《现金流压力测试报告》',
    key7: '进行中'
  },
  {
    key1: '紧急采购成本攀升',
    key2: '供应弹性增强',
    key3: '1. 认证 3 家以上 72 小时应急供应商，签订加急订单协议，2 周内录入系统；2. 与 TOP5 供应商试点 VMI，转移安全库存至前置仓，30 日内完成数据对接；3. 部署智能补货算法，按小时扫描库存自动生成订单，采购每日审核，压缩补货周期；4. 按销售额 2% 计提战略储备金，制定管理办法，1 周内完成建账。',
    key4: '采购总监',
    key5: '高',
    key6: '《供应商应急管理手册》',
    key7: '进行中'
  },
  {
    key1: '客户交付准时率下降',
    key2: '交付体系升级',
    key3: '1. CRM 系统上线 ATP 可视化看板，15 分钟更新可承诺库存及交期，2 周内完成培训；2. 预留 5% 产能作为紧急订单通道，制定插单审批流程，3 日内完成产能配置；3. 建立三大区域中心仓，预存畅销品，4 周内实现区域 24 小时达配送；4. 实施客户分级服务，VIP 客户享库存预留与专属物流，1 周内完成分级评定及协议起草。',
    key4: '客户服务总监',
    key5: '低',
    key6: '《客户交付保障白皮书》',
    key7: '进行中'
  }
]
</script>
<style lang="scss" scoped>
@import './trendRisk.scss';
</style>
