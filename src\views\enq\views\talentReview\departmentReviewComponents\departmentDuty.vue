<template>
  <div class="post_process_main">
    <div class="departmen_main">
      <div class="marginT_30">
        <div class="page_second_title">部门职责分配</div>
      </div>
      <div class="post_process_table marginT_20">
        <el-table :data="tableData" :stripe="false" @cell-click="setCellStyle">
          <el-table-column type="index" label="序号" width="60"></el-table-column>
          <el-table-column prop="respName" label="部门职责，分解到岗位后，将成为相应岗位的岗位职责"></el-table-column>
          <el-table-column
            v-for="item in postColumns"
            :key="item.postCode"
            :prop="item.postCode"
            :label="item.postName"
            width="40"
            class="post_column"
            class-name="post_column"
          >
            <template #default="scope">
              <div class="post_ipt" :class="{ active: scope.row[item.postCode] }">
                <el-icon class="icon_check"><Check /></el-icon>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="oper_btn_wrap align_center">
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import { getOrgResp, saveOrgResp } from '../../../request/api'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  orgCode: String
})

const emit = defineEmits(['prevStep', 'nextStep'])

const postCodeArr = ref([])
const resData = ref([])
const tableData = ref([])
const postColumns = ref([])

onMounted(() => {
  getOrgRespFun()
})

watch(
  tableData,
  (val, oldVal) => {
    // Deep watch for tableData changes
  },
  { deep: true }
)

const saveOrgRespFun = async stepType => {
  try {
    const copyData = window.$util.deepClone(tableData.value)
    const res = await saveOrgResp(copyData)

    if (res.code == 200) {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('保存失败')
  }
}

const getOrgRespFun = async () => {
  try {
    const res = await getOrgResp({
      enqId: props.enqId,
      orgCode: props.orgCode
    })

    if (res.code == 200) {
      postColumns.value = res.data.enqPostInfoList
      resData.value = res.data.enqOrgRespPostList
      formatterData()
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const formatterData = () => {
  // 取出columns的code
  const codeArr = postColumns.value.map(item => item.postCode)
  postCodeArr.value = codeArr

  resData.value.forEach((item, index) => {
    const checkedPostArr = item.enqOrgRespPostList.map(post => post.postCode)
    item.postCode = checkedPostArr

    codeArr.forEach(code => {
      const checkFlag = item.postCode.includes(code)
      item.index = index
      item[code] = checkFlag
    })
  })

  tableData.value = resData.value
}

const setCellStyle = (row, column) => {
  const key = column.property
  if (!postCodeArr.value.includes(key)) {
    return
  }

  row[key] = !row[key]
  const rowIndex = row.index
  const postCode = row.postCode
  const index = postCode.indexOf(key)

  if (index > -1) {
    row.postCode.splice(index, 1)
  } else {
    row.postCode.push(key)
  }

  tableData.value[rowIndex] = { ...row }
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      saveOrgRespFun('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') {
        emit('prevStep')
      }
    })
}

const nextBtn = () => {
  saveOrgRespFun('nextStep')
}

const checkData = data => {
  for (const obj of data) {
    if (window.$util.objHasEmpty(obj, ['kpiGoal', 'kpiScore'])) {
      console.log('有空值')
      return true
    }
    console.log('没有空值')
  }
  return false
}
</script>

<style scoped lang="scss">
.post_process_table {
}

.post_column {
  margin-right: 4px;
}
.post_ipt {
  width: 30px;
  height: 30px;
  border: 1px solid #e5e5e5;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  color: #000;
  margin-right: 3px;
  &.active {
    background: #0099ff;
    color: #fff;
    font-size: 20px;
    .icon_check {
      visibility: visible;
    }
  }

  .icon_check {
    visibility: hidden;
  }
}

.el-table__body tr {
  &:nth-child(even) {
    background: #fff;
  }

  td {
    padding: 7px 0;
  }
}

.el-table th > .cell {
  padding-left: 10px;
  padding-right: 10px;
  margin-right: 4px;
}

.el-table__row {
  .cell {
    padding: 0;
  }
  td {
    &:first-child {
      padding-left: 20px;
    }
  }
}

.el-table th {
  background-color: #ebf4ff !important;
}

.el-table thead {
  color: #0099ff;
}
.el-table tr {
  &:nth-child(even) {
    background: #f4f4f4;
  }
}
</style>
