<script setup>
import ChatFrame from './chatFrame.vue'
import { sessionAdd } from '@/api/modules/dialogue.js'
import { useDialogueStore } from '@/stores'

const dialogVisible = ref(false)
// 新建会话
const sessionId = ref('')
const addSession = input => {
  dialogVisible.value = true
  sessionAdd({ message: input, sessionType: 'executive' }).then(res => {
    if (res.code == 200) {
      sessionId.value = res.msg
      useDialogueStore().setFirst(input)
      dialogVisible.value = true
    }
  })
}

defineExpose({
  addSession
})
</script>
<template>
  <el-dialog
    class="ai-dialog"
    width="538px"
    :show-close="false"
    v-model="dialogVisible"
    :modal="false"
    :destroy-on-close="true"
  >
    <template #header="{ titleId, titleClass }">
      <div class="my-header">
        <h4 :id="titleId" :class="titleClass">AI整体解读</h4>
      </div>
    </template>
    <ChatFrame :id="sessionId" style="height: 600px"></ChatFrame>
  </el-dialog>
</template>
<style lang="scss" scoped></style>
