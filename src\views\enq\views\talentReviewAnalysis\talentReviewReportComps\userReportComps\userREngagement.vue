<template>
    <div class="report_section userR_engagement_wrap">
        <div class="page_second_title">敬业度与满意度</div>
        <div class="userR_engagement_main">
            <div class="first_section_wrap">
                <div class="page_third_title">整体敬业度表现</div>
                <div class="first_section_main flex_row_start">
                    <div class="chart_box_wrap" :id="fanChartId"></div>
                    <div class="info_wrap">
                        整体敬业度得分为{{
                            engagementScore
                        }}分，处于高敬业度区域，通常情况下员工的敬业度越高，离职率就越低；组织赋能感越高，员工离职率就越低；
                    </div>
                </div>
            </div>
            <div class="first_section_wrap marginB_32">
                <div class="table_wrap">
                    <tableComponent
                        :tableData="tableData"
                        :border="true"
                        :needPagination="false"
                        :needIndex="false"
                        :overflowTooltip="!isPdf"
                    ></tableComponent>
                </div>
            </div>
            <div class="second_section_wrap marginB_32 ">
                <div class="page_third_title">
                    <span>满意度</span>
                </div>
                <div class="myd_wrap flex_row_start">
                    <div class="myd_score">
                        <customProcess
                            :size="150"
                            :strokeWidth="20"
                            :num="satisfactionScore"
                        />
                    </div>
                    <div
                        class="chart_box_wrap bar_chart_box_wrap"
                        :id="driveChartId"
                    ></div>
                </div>
            </div>
            <!-- <div class="second_section_wrap drive_wrap">
                <div class="page_third_title">
                    <span>敬业度驱动因素</span>
                </div>
                <div class="flex_row_betweens">
                    <div class="drive_table_wrap">
                        <tableComponent
                            :tableData="tableDataDrive"
                            :border="true"
                            :needPagination="false"
                            :needIndex="false"
                        ></tableComponent>
                    </div>
                    <div class="drive_table_wrap">
                        <tableComponent
                            :tableData="tableDataLast"
                            :border="true"
                            :needPagination="false"
                            :needIndex="false"
                        ></tableComponent>
                    </div>
                </div>
            </div>
            <div class="first_section_wrap">
                <div class="page_third_title">
                    <span>敬业度与满意度差距分析</span>
                </div>
                <div class="table_wrap">
                    <tableComponent
                        :tableData="tableDataGap"
                        :border="true"
                        :needPagination="false"
                        :needIndex="false"
                    ></tableComponent>
                </div>
            </div> -->
            <div class="third_section_wrap marginB_32">
                <div class="page_third_title">
                    <span>敬业度与满意度定位</span>
                </div>
                <div class="table_wrap flex_row_start">
                    <!-- <div class="left_wrap">
                        <div class="ordinate">满意度</div>
                        <div class="el-icon-caret-top"></div>
                        <div class="left_main">
                            <div class="flex_row_start">
                                <div class="item_wrap item_01_wrap">
                                    <p class="title">B、不敬业、满意</p>
                                    <div class="info">士气激励</div>
                                    <div
                                        class="info"
                                        v-if="location.location == 'B'"
                                    >
                                        {{ location.userName }}
                                    </div>
                                </div>
                                <div class="item_wrap item_02_wrap">
                                    <p class="title">A、敬业、满意</p>
                                    <div class="info">保持现状</div>
                                    <div
                                        class="info"
                                        v-if="location.location == 'A'"
                                    >
                                        {{ location.userName }}
                                    </div>
                                </div>
                            </div>
                            <div class="flex_row_start">
                                <div class="item_wrap item_03_wrap">
                                    <p class="title">D、不敬业、不满意</p>
                                    <div class="info">提升满意度</div>
                                    <div
                                        class="info"
                                        v-if="location.location == 'D'"
                                    >
                                        {{ location.userName }}
                                    </div>
                                </div>
                                <div class="item_wrap item_04_wrap">
                                    <p class="title">C、敬业、不满意</p>
                                    <div class="info">防止员工流失</div>
                                    <div
                                        class="info"
                                        v-if="location.location == 'C'"
                                    >
                                        {{ location.userName }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="el-icon-caret-right"></div>
                        <div class="abscissa">敬业度</div>
                    </div> -->
                    <div class="right_wrap">
                        <tableComponent
                            :tableData="tableDataSatisfaction"
                            :border="true"
                            :needPagination="false"
                            :needIndex="false"
                        :overflowTooltip="!isPdf"
                        ></tableComponent>
                    </div>
                </div>
            </div>
            <div class="first_section_wrap">
                <div class="page_third_title">
                    <span>TA 认为的主要改善方向 top 5</span>
                </div>
                <div class="table_wrap">
                    <tableComponent
                        :tableData="tableDataSuggestion"
                        :border="true"
                        :needPagination="false"
                        :needIndex="false"
                        :overflowTooltip="!isPdf"
                    ></tableComponent>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    // 敬业度
    import { getEngagement } from "../../../../request/api";
    import customProcess from "@/components/talent/common/customProcess.vue";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    export default {
        name: "userREngagement",
        props: ["nextBtnText", "enqId", "userId", "postCode","isPdf"],
        components: {
            tableComponent,
            customProcess,
        },
        data() {
            return {
                fanChartId:this.$util.createRandomId(),
                fanChart: {
                    data: [
                        {
                            name: "危险区域",
                            min: "0%",
                            max: "30%",
                        },
                        {
                            name: "问题区域",
                            min: "30%",
                            max: "45%",
                        },
                        {
                            name: "稳定区域",
                            min: "45%",
                            max: "65%",
                        },
                        {
                            name: "高绩效区域",
                            min: "65%",
                            max: "100%",
                        },
                    ],
                    actual: "66%",
                },
                engagementScore: "",
                tableData: {
                    columns: [
                        {
                            label: "词典分类",
                            prop: "module_name",
                            width: "",
                        },
                        {
                            label: "词典",
                            prop: "item_name",
                            width: "",
                        },
                        {
                            label: "行为描述",
                            prop: "item_desc",
                            width: "",
                        },
                        {
                            label: "得分",
                            prop: "actual_score",
                            width: "100",
                        },
                    ],
                    data: [],
                },
                satisfactionScore:null,
                driveChartId:this.$util.createRandomId(),
                getSatisfaction: {
                    data: [],
                },
                tableDataDrive: {
                    columns: [
                        {
                            label: "TOP3",
                            prop: "module_name",
                            width: "",
                        },
                        {
                            label: "行为描述",
                            prop: "module_desc",
                            width: "",
                        },
                    ],
                    data: [
                        {
                            name: "姓名",
                            target: "55天",
                        },
                    ],
                },
                tableDataLast: {
                    columns: [
                        {
                            label: "lAST3",
                            prop: "module_name",
                            width: "",
                        },
                        {
                            label: "行为描述",
                            prop: "module_desc",
                            width: "",
                        },
                    ],
                    data: [
                        {
                            name: "姓名",
                            target: "55天",
                        },
                    ],
                },

                tableDataGap: {
                    columns: [
                        {
                            label: "满意度",
                            prop: "satisfaction",
                            width: "",
                        },
                        {
                            label: "敬业度",
                            prop: "engagement",
                            width: "",
                        },
                        {
                            label: "差距",
                            prop: "disparity",
                            width: "",
                        },
                        {
                            label: "类型",
                            prop: "type",
                            width: "",
                        },
                        {
                            label: "差距说明",
                            prop: "disparityExplain",
                            width: "500",
                        },
                    ],
                    data: [],
                },
                tableDataSatisfaction: {
                    columns: [
                        {
                            label: "区域",
                            prop: "name",
                            width: "",
                        },
                        {
                            label: "定位",
                            prop: "target",
                            width: "",
                        },
                        {
                            label: "建议",
                            prop: "realPerformance",
                            width: "400",
                        },
                    ],
                    data: [
                        {
                            code: "A",
                            name: "A、敬业、满意",
                            target: "",
                            realPerformance:
                                "该群体属于敬业型员工，员工因为对企业现状满意故表现出敬业的状态，该群体是企业长期发展的保障，企业需要保留该部分员工",
                        },
                        {
                            code: "B",
                            name: "B、不敬业、满意",
                            target: "",
                            realPerformance:
                                "该群体属于安逸型员工，员工认可目前的激励方式，但是不愿意付出更多努力为企业工作，企业需加强惩罚机制，转变此群体的观念，使其成为敬业的员工。",
                        },
                        {
                            code: "C",
                            name: "C、敬业、不满意",
                            target: "",
                            realPerformance:
                                "该群体属于自我驱动型员工，员工可能因为对企业未来抱着憧憬或者希望自己有更好的发展平台而努力工作，但是他们对现状并不满意，企业应提高其满意度防止流失风险",
                        },
                        {
                            code: "D",
                            name: "D、不敬业、不满意",
                            target: "",
                            realPerformance:
                                "该群体属于激发型员工，员工因为对企业不满意所以不敬业，过多的员工对公司存在负面情绪将成为公司发展的障碍。因此，如何更好地发挥工的作用，通过提高其满意度来提升其敬业度水平是企业下一步关注的问题。",
                        },
                    ],
                },
                location: "",
                tableDataSuggestion: {
                    columns: [
                        {
                            label: "机会领域",
                            prop: "moduleName",
                            width: "140",
                        },
                        {
                            label: "典型表现",
                            prop: "typicalIssue",
                            width: "",
                        },
                        {
                            label: "具体评估项",
                            prop: "itemName",
                            width: "",
                        },
                        {
                            label: "得分",
                            prop: "actualScore",
                            width: "100",
                        },
                        {
                            label: "改善方向",
                            prop: "improvementSuggestion",
                            width: "",
                        },
                    ],
                    data: [],
                },
            };
        },
        created() {},
        computed: {},
        mounted() {
            this.getEngagementFun();
        },
        methods: {
            getEngagementFun() {
                getEngagement({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    if (res.code == 200) {
                        // 整体敬业度
                        this.fanChart.actual = res.data.engagementScore + "%";
                        echartsRenderPage(
                            this.fanChartId,
                            "Fan",
                            "300",
                            "280",
                            this.fanChart
                        );
                        this.engagementScore = res.data.engagementScore;
                        this.tableData.data = res.data.getEngagement;
                        this.getSatisfaction.data = res.data.getSatisfaction.map(
                            (item) => {
                                return {
                                    value: item.actual_score,
                                    name: item.module_name,
                                    code: item.module_code,
                                };
                            }
                        );
                        this.getSatisfaction['padding'] = 130
                        this.satisfactionScore = res.data.satisfactionScore;
                        echartsRenderPage(
                            this.driveChartId, //id
                            "XBar", // 图表类型
                            null, //宽
                            "300", //高
                            this.getSatisfaction //图表数据
                        );
                        this.tableDataDrive.data = res.data.topThree;
                        this.tableDataLast.data = res.data.lastThree;
                        this.tableDataGap.data = [];
                        this.tableDataGap.data.push(res.data.disparityAnalysis);
                        this.location = res.data.location;
                        for (
                            let i = 0;
                            i < this.tableDataSatisfaction.data.length;
                            i++
                        ) {
                            if (
                                this.tableDataSatisfaction.data[i].code ==
                                this.location.location
                            ) {
                                this.tableDataSatisfaction.data[i].target =
                                    this.location.userName;
                            }
                        }
                        this.tableDataSuggestion.data =
                            res.data.getSuggestionsImprovement;
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .userR_engagement_wrap {
        padding: 0 10px;
        height: 480px;
        overflow: auto;
        pointer-events: auto;
        .userR_engagement_main {
            .first_section_wrap {
                margin: 20px 0 0 0;
                .page_second_title {
                    margin: 0 0 10px 0;
                }
                .first_section_main {
                    .chart_box_wrap {
                        // border: 1px solid #dcdfe6;
                        width: 300px;
                        height: 200px;
                    }
                    .info_wrap {
                        margin: 0 0 0 15px;
                    }
                }
            }
            .second_section_wrap {
                margin: 20px 0 32px 0;
                .page_second_title {
                    margin: 0 0 10px 0;
                }
                .bar_chart_box_wrap {
                    // border: 1px solid #dcdfe6;
                    width: 100%;
                    height: 300px;
                    margin-left: 16px;
                }
            }
            .drive_wrap {
                .drive_table_wrap {
                    width: 49%;
                }
            }
            .third_section_wrap {
                .page_second_title {
                    margin: 0 0 10px 0;
                }
                .table_wrap {
                    .left_wrap {
                        position: relative;
                        .ordinate {
                            position: absolute;
                            width: 20px;
                            top: 80px;
                        }
                        .el-icon-caret-top {
                            position: absolute;
                            top: -10px;
                            left: 18px;
                        }
                        .el-icon-caret-right {
                            position: absolute;
                            bottom: 23.5px;
                            right: -9px;
                        }
                        .abscissa {
                            line-height: 30px;
                            text-align: center;
                        }
                        .left_main {
                            margin: 0 0 0 25px;
                            width: 400px;
                            height: 250px;
                            border-left: 1px solid #666;
                            border-bottom: 1px solid #666;
                            .item_wrap {
                                width: 197px;
                                height: 124px;
                                padding: 10px 0 0 15px;
                                .title {
                                    height: 40px;
                                    line-height: 40px;
                                    font-size: 16px;
                                    font-weight: 600;
                                }
                                .info {
                                }
                            }
                            .item_01_wrap {
                                background: #e5f7fd;
                                color: #0099ff;
                            }
                            .item_02_wrap {
                                background: #f4fbea;
                                color: #92d050;
                            }
                            .item_03_wrap {
                                background: rgb(248, 244, 236);
                                color: rgb(236, 174, 58);
                            }
                            .item_04_wrap {
                                background: rgb(238, 219, 245);
                                color: rgb(91, 4, 122);
                            }
                        }
                    }
                    .right_wrap {
                        margin: 0 0 0 15px;
                        flex: 1;
                    }
                }
            }
        }
    }
</style>
