<template>
    <div class="ability_matching_dict_wrap">
        <div class="staff_relevancy_main">
            <div class="page_third_title">能力改善优先度</div>
            <el-table
                class="table_wrap"
                :data="tableData.data"
                height="500"
                :v-loading="loading"
                border
            >
                <el-table-column type="index" align="center"></el-table-column>
                <el-table-column
                    v-for="(item, index) in tableData.tableTitle"
                    :label="item.label"
                    :prop="item.prop"
                    align="center"
                    :width="item.width"
                >
                    <el-table-column
                        v-for="item1 in item.childrenLabels"
                        :key="item.prop"
                        :label="item1.label"
                        :prop="item1.prop"
                        :class-name="item1.class"
                        align="center"
                        :formatter="item1.formatterFun"
                    >
                    </el-table-column>
                </el-table-column>
            </el-table>
            <coustomPagination
                :total="total"
                @pageChange="pageChange"
            ></coustomPagination>
        </div>
    </div>
</template>
 
<script>
    import coustomPagination from "@/components/talent/paginationComps/coustomPagination";
    import { getTrainProposalInfo } from "../../../../request/api";
    export default {
        name: "referAbilityModule",
        props: ["orgCode", "evalId"],
        components: { coustomPagination },
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading: false,
                tableData: {
                    tableTitle: [
                        {
                            label: "能力模块",
                            prop: "module_name",
                            width: 140,
                        },
                        {
                            label: "重要度",
                            childrenLabels: [
                                {
                                    label: "重要",
                                    prop: "importanceA",
                                    class: "red",
                                },
                                {
                                    label: "一般",
                                    prop: "importanceB",
                                },
                                {
                                    label: "不重要",
                                    prop: "importanceC",
                                    class: "yellow",
                                },
                                {
                                    label: "占比",
                                    prop: "importanceRatio",
                                    formatterFun(data) {
                                        return data["importanceRatio"] ? data["importanceRatio"] + "%" : '';
                                    },
                                },
                            ],
                        },
                        {
                            label: "紧急度",
                            childrenLabels: [
                                {
                                    label: "紧急",
                                    prop: "emergencyMapA",
                                    class: "red",
                                },
                                {
                                    label: "一般",
                                    prop: "emergencyMapB",
                                },
                                {
                                    label: "不紧急",
                                    prop: "emergencyMapC",
                                    class: "yellow",
                                },
                                {
                                    label: "占比",
                                    prop: "emergencyRatio",
                                    formatterFun(data) {
                                        return data["emergencyRatio"] ? data["emergencyRatio"] + "%" : '';
                                    },
                                },
                            ],
                        },
                    ],
                    data: [
                        {
                            index: 1,
                            dict: "销售一部",
                            standardPerson: 56,
                            standardDuty: "72%",
                            bestPerson: 56,
                            bestDuty: "72%",
                            forwardPerson: 56,
                            forwardDuty: "72%",
                            negativePerson: 56,
                            negativeDuty: "72%",
                        },
                        {
                            index: 2,
                            dict: "销售一部",
                            standardPerson: 56,
                            standardDuty: "72%",
                            bestPerson: 56,
                            bestDuty: "72%",
                            forwardPerson: 56,
                            forwardDuty: "72%",
                            negativePerson: 56,
                            negativeDuty: "72%",
                        },
                    ],
                },
            };
        },
        created() {},
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
        },
        methods: {
            getData() {
                this.loading = true;
                let params = {
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                getTrainProposalInfo(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.tableData, "data", res.data);
                        this.loading = false;
                    }else {
                        this.loading = false;
                    }
                });
            },
            pageChange(size, currPage) {
                this.pageSize = size;
                this.currPage = currPage;
                this.getData();
            },
            cellClick(row, column, cell, event) {
                // if (row[column.property] != true && row[column.property] != false) {
                //     return;
                // }
                // for (let i = 0; i < this.tableData.data.length; i++) {
                //     if (this.tableData.data[i].index == row.index) {
                //         if (this.tableData.data[i][column.property] == true) {
                //             this.tableData.data[i][column.property] = false;
                //         } else {
                //             this.tableData.data[i][column.property] = true;
                //         }
                //         return;
                //     }
                // }
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .ability_matching_dict_wrap {
        .staff_relevancy_main {
            .table_wrap tbody td {
                padding: 0 !important;
            }
            .table_wrap tbody .cell {
                padding: 12px 0;
                height: 100%;
                cursor: pointer;
            }
            .table_wrap .check_box_wrap {
                color: #0099fd;
                font-weight: 700;
            }
        }
    }
    .el-table thead.is-group th.red {
        color: #FF8181;
    }
    .el-table thead.is-group th {
        color: #00B0F0;
    }
    .el-table thead.is-group th.yellow {
        color: #FFC000;
    }
</style>