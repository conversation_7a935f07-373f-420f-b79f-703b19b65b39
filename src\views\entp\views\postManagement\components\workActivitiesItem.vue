<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in itemData" :key="item.id">
      <span class="item num">{{ index + 1 }}</span>
      <el-select class="item" v-model="item.jobActivityType" placeholder>
        <el-option
          v-for="activity in jobActivityList"
          :label="activity.codeName"
          :value="activity.dictCode"
        ></el-option>
      </el-select>
      <el-input class="item" v-model="item.jobActivityName" placeholder></el-input>
      <el-input class="item" v-model="item.jobClassName" disabled></el-input>
      <el-select class="item" v-model="item.status" placeholder>
        <el-option label="启用" value="Y"></el-option>
        <el-option label="关闭" value="N"></el-option>
      </el-select>
      <!--            <el-input type="number" class="item" v-model="item.sortNbr" placeholder>-->
      <!--            </el-input>-->
      <div class="item control">
        <el-button
          class="color_danger icon_del"
          @click.native.prevent="deleteItem(item, index)"
          link
          icon="el-icon-delete"
        ></el-button>
      </div>
    </div>
    <div class="pagination_wrap">
      <el-pagination
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        :current-page="pageBean.current"
        :page-size="pageBean.size"
        @current-change="handleCurrentChange"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageBean.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { getDict } from '../../../request/api'
export default {
  name: 'workActivityItem',
  props: {
    itemData: {
      type: Array,
      default: function () {
        return [
          {
            jobActivityCode: '',
            jobActivityName: '',
            jobActivityType: '',
            jobClassCode: '',
            jobClassName: '',
            sortNbr: '',
            status: 'Y'
          }
        ]
      }
    },
    pageBean: {
      type: Object,
      default: function () {
        return {
          current: 1,
          size: 10,
          total: 0
        }
      }
    }
  },
  data() {
    return {
      jobActivityList: []
    }
  },
  mounted() {
    this.getDictFun()
  },
  methods: {
    //
    getDictFun() {
      let params = {
        dictId: 'JOB_ACTIVITY_TYPE'
      }
      getDict(params).then(res => {
        console.log(res)
        if (res.code == 200) {
          this.jobActivityList = res.data
        }
      })
    },
    deleteItem(item, index) {
      this.$emit('deleteItem', item, index)
    },
    handleSizeChange(size) {
      this.$emit('handleSizeChange', size)
    },
    handleCurrentChange(page) {
      this.$emit('handleCurrentChange', page)
    }
  }
}
</script>

<style scoped lang="scss">
.edu_info_item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  .item {
    width: 30%;
    text-align: center;
    &.num,
    &.control {
      width: 10%;
      padding: 0;
      button {
        padding: 4px;
      }
    }
  }
}
</style>
