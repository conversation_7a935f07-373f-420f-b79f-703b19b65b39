import service from '../request'

// 用户登录
export const login = data =>
  service({
    url: '/auth/login',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data
  })

// 获取用户信息
export const getProfile = () =>
  service({
    url: '/user/profile',
    method: 'get'
  })

// 更新用户信息
export const updateProfile = data =>
  service({
    url: '/user/profile',
    method: 'put',
    data
  })
