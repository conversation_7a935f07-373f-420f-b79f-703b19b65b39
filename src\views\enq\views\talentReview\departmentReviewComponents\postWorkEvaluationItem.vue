<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in eduInfoData" :key="item.id">
      <div class="item item_icon_wrap">{{ index + 1 }}</div>
      <div class="item item_icon_wrap">
        <el-tooltip class="item" effect="dark" :content="item.userName" placement="top">
          <div>{{ item.userName }}</div>
        </el-tooltip>
      </div>
      <div class="item item_icon_wrap">
        <el-tooltip class="item" effect="dark" :content="item.orgName" placement="top">
          <div>{{ item.orgName }}</div>
        </el-tooltip>
      </div>
      <div class="item item_icon_wrap">
        <el-tooltip class="item" effect="dark" :content="item.postName" placement="top">
          <div>{{ item.postName }}</div>
        </el-tooltip>
      </div>
      <div class="item item_icon_wrap">
        <el-tooltip class="item" effect="dark" :content="item.jobLevelName" placement="top">
          <div>{{ item.jobLevelName }}</div>
        </el-tooltip>
      </div>
      <el-select class="item" v-model="item.overtimeNtensity" placeholder="请选择">
        <el-option
          v-for="opt in OVERTIME_INTENSITY"
          :key="opt.dictCode"
          :label="opt.codeName"
          :value="opt.dictCode"
        ></el-option>
      </el-select>
      <div class="item item_icon_wrap special">
        <el-tooltip class="item" effect="dark" :content="item.selfEvalSaturation" placement="top">
          <div>{{ item.selfEvalSaturation }}%</div>
        </el-tooltip>
      </div>
      <el-select class="item" v-model="item.actualWorkingSaturation" placeholder="请选择">
        <el-option
          v-for="opt in WORKING_SATURATION"
          :key="opt.dictCode"
          :label="opt.codeName"
          :value="opt.dictCode"
        ></el-option>
      </el-select>
      <el-select class="item" v-model="item.goalQuantization" placeholder="请选择">
        <el-option
          v-for="opt in TARGET_QUANTIFICATION"
          :key="opt.dictCode"
          :label="opt.codeName"
          :value="opt.dictCode"
        ></el-option>
      </el-select>
      <el-select class="item" v-model="item.resultAssessment" placeholder="请选择">
        <el-option
          v-for="opt in RESULT_ASSESSMENT"
          :key="opt.dictCode"
          :label="opt.codeName"
          :value="opt.dictCode"
        ></el-option>
      </el-select>
      <el-select class="item" v-model="item.workEfficiencyPerformance" placeholder="请选择">
        <el-option
          v-for="opt in WORK_EFFICIENCY"
          :key="opt.dictCode"
          :label="opt.codeName"
          :value="opt.dictCode"
        ></el-option>
      </el-select>
      <el-select class="item" v-model="item.workQualityPerformance" placeholder="请选择">
        <el-option
          v-for="opt in WORK_QUANLITY"
          :key="opt.dictCode"
          :label="opt.codeName"
          :value="opt.dictCode"
        ></el-option>
      </el-select>
      <el-select class="item" v-model="item.workObjectivesReach" placeholder="请选择">
        <el-option
          v-for="opt in WORK_OBJECTIVES"
          :key="opt.dictCode"
          :label="opt.codeName"
          :value="opt.dictCode"
        ></el-option>
      </el-select>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user.js'

const props = defineProps({
  eduInfoData: {
    type: Array,
    default: () => [{}]
  }
})

const OVERTIME_INTENSITY = ref([])
const WORKING_SATURATION = ref([])
const TARGET_QUANTIFICATION = ref([])
const RESULT_ASSESSMENT = ref([])
const WORK_EFFICIENCY = ref([])
const WORK_QUANLITY = ref([])
const WORK_OBJECTIVES = ref([])

const userStore = useUserStore()

onMounted(async () => {
  const res = await userStore.getDocList([
    'OVERTIME_INTENSITY',
    'WORKING_SATURATION',
    'TARGET_QUANTIFICATION',
    'RESULT_ASSESSMENT',
    'WORK_EFFICIENCY',
    'WORK_QUANLITY',
    'WORK_OBJECTIVES'
  ])
  OVERTIME_INTENSITY.value = res.OVERTIME_INTENSITY
  WORKING_SATURATION.value = res.WORKING_SATURATION
  TARGET_QUANTIFICATION.value = res.TARGET_QUANTIFICATION
  RESULT_ASSESSMENT.value = res.RESULT_ASSESSMENT
  WORK_EFFICIENCY.value = res.WORK_EFFICIENCY
  WORK_QUANLITY.value = res.WORK_QUANLITY
  WORK_OBJECTIVES.value = res.WORK_OBJECTIVES
})
</script>

<style scoped lang="scss">
.edu_info_item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  padding: 8px 16px;

  .item {
    width: 9%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
  }

  .item_icon_wrap {
    text-align: center;
    width: 4%;

    .item_icon {
      font-size: 20px;
      color: #0099fd;
      cursor: pointer;
    }
  }
  .special {
    width: 7%;
  }
  .el-tooltip {
    width: 100%;
  }
}
</style>
