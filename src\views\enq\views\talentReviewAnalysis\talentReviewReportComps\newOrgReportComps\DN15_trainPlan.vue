<template>
    <div class="org_report_main" :class="{'marginB_16':isPdf}">
        <div class="page_second_title">培训计划</div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
            <!-- <el-col :span="24">
                <div class="item_title">培训计划详情</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentChange"
                    @handleSizeChange="handleSizeChange"
                    :tableData="tableData"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf">更多数据请查看网页版报告</div>
            </el-col> -->
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        orgTrain,
        orgTrainList,
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import listComp from "./components/listComp.vue";

    export default {
        name: "orgRTrain",
        props: ["enqId", "orgCode","isPdf"],
        components: { tableComps,listComp },
        data() {
            return {
                size: 10,
                current: 1,
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "培训计划计划数",
                        elSpan: 5,
                        chartHeight: "400",
                        chartType: "YBar",
                        dataKey:'planByOrg'
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "培训计划月份",
                        elSpan: 9,
                        chartType: "XBar",
                        dataKey:'planMonth'
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "主要覆盖岗位类型",
                        elSpan: 5,
                        chartType: "YBar",
                        dataKey:'planByClass'
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "培训内容类型",
                        elSpan: 5,
                        chartType: "YBar",
                        dataKey:'planByCode'
                    },
                ],
                listArr: [
                    {
                        title: "培训计划详情",
                        ajaxUrl: orgTrainList,
                        columns: [
                            {
                            label: "部门",
                            prop: "org_name",
                        },
                        {
                            label: "姓名",
                            prop: "org_leader_name",
                        },
                        {
                            label: "岗位名称",
                            prop: "post_name",
                        },
                        {
                            label: "培训项目名称",
                            prop: "training_name",
                        },
                        {
                            label: "培训日期",
                            prop: "training_date",
                        },
                        {
                            label: "月份",
                            prop: "month",
                        },
                        {
                            label: "课程类型",
                            prop: "code_name",
                        },
                        ],
                    },
                ],
                tableData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "org_name",
                        },
                        {
                            label: "姓名",
                            prop: "org_leader_name",
                        },
                        {
                            label: "岗位名称",
                            prop: "post_name",
                        },
                        {
                            label: "培训项目名称",
                            prop: "training_name",
                        },
                        {
                            label: "培训日期",
                            prop: "training_date",
                        },
                        {
                            label: "月份",
                            prop: "month",
                        },
                        {
                            label: "课程类型",
                            prop: "code_name",
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
            };
        },
        created() {
            this.getData();
            this.orgTrainListFn();
        },
        mounted() {},
        methods: {
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTrain(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.initChart(res.data)
                    }
                });
            },
            orgTrainListFn() {
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTrainList(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
            handleCurrentChange(current) {
                this.current = current;
                this.orgTrainListFn();
            },
            handleSizeChange(size) {
                this.size = size;
                this.orgTrainListFn();
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>