<template>
  <div class="result_main">
    <div class="border_box" v-for="(item, indexs) in tableBoxData.enqObjectiveResultList" :key="indexs">
      <div class="post_process_table">
        <el-table class="table_wrap" :data="item.enqObjectiveList">
          <el-table-column prop="objectiveName" label="目标名称" width="220px">
            <template #default="scope">
              <el-input v-model="scope.row.objectiveName" :disabled="scope.row.select" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="weight" label="目标权重">
            <template #default="scope">
              <el-input v-model="scope.row.weight" :disabled="scope.row.select" size="mini">
                <template #append>%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="selfScore" label="自评得分">
            <template #default="scope">
              <el-input v-model="scope.row.selfScore" :disabled="true" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="supScore" label="上级评价" v-if="relationCode == 1 || relationCode == 3">
            <template #default="scope">
              <el-input v-model="scope.row.supScore" :disabled="true" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="leaderScore" label="领导评价" v-if="relationCode == 2 || relationCode == 3">
            <template #default="scope">
              <el-input v-model="scope.row.leaderScore" :disabled="true" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="overallScore" label="综合得分">
            <template #default="scope">
              <el-input v-model="scope.row.overallScore" :disabled="true" size="mini"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="post_process_table">
        <el-table class="table_wrap" :data="item.enqObjectiveResults">
          <el-table-column prop="resultName" label="关键结果" width="220px">
            <template #default="scope">
              <el-input v-model="scope.row.resultName" :disabled="scope.row.select" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="weight" label="目标权重">
            <template #default="scope">
              <el-input v-model="scope.row.weight" :disabled="scope.row.select" size="mini">
                <template #append>%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="selfScore" label="自评得分">
            <template #default="scope">
              <el-input v-model="scope.row.selfScore" :disabled="true" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="supScore" label="上级评价" v-if="relationCode == 1 || relationCode == 3">
            <template #default="scope">
              <el-input v-model="scope.row.supScore" :disabled="scope.row.select" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="leaderScore" label="领导评价" v-if="relationCode == 2 || relationCode == 3">
            <template #default="scope">
              <el-input v-model="scope.row.leaderScore" :disabled="scope.row.select" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="overallScore" label="综合得分">
            <template #default="scope">
              <el-input v-model="scope.row.overallScore" :disabled="true" size="mini"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="total_box">
      <div>目标与关键结果得分汇总：{{ tableBoxData.objectiveScore }}</div>
      <div>
        <span>目标与关键结果表现：</span>
        <el-select v-model="tableBoxData.objectiveOverallMerit" placeholder="请选择" size="mini">
          <el-option
            v-for="(item, index) in qualificationOptions"
            :label="item.codeName"
            :value="item.dictCode"
            :key="index"
          ></el-option>
        </el-select>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, defineProps } from 'vue'
import { useUserStore } from '@/stores/modules/user.js'

const props = defineProps({
  tableBoxData: {
    type: Object,
    default: () => ({})
  },
  relationCode: {
    type: Number,
    default: null
  }
})

const qualificationOptions = ref([])
const userStore = useUserStore()

onMounted(async () => {
  const res = await userStore.getDocList(['ACTUAL_GRADE'])
  qualificationOptions.value = res.ACTUAL_GRADE
})
</script>
<style lang="scss" scoped>
.result_main {
  .border_box {
    margin-top: 10px;
    padding: 10px 5px;
    border: 1px solid #eee;
    .table_wrap {
      margin-bottom: 16px;
    }
  }
  .total_box {
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
  }
}
</style>
