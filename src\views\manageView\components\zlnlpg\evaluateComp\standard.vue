<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'

defineOptions({ name: 'standard' })

const columns = ref([
  {
    label: '序号',
    type: 'index',
    width: 80
  },
  {
    label: '评估项目类型',
    prop: 'type',
    width: 120
  },
  {
    label: '参考标准',
    prop: 'reference'
  }
])
const tableData = ref([
  {
    type: '流程端到端闭环',
    reference: '制定了包含数据采集→规则制定→审批发布→执行监控全环节流程，每季度闭环验证标准适用性',
    flag: 'Y'
  },
  {
    type: '输出文档',
    reference: '输出了《大客户评分模型说明书》《分级白皮书》等文档，需符合分析报告类"数据可靠性"标准',
    flag: 'N'
  },
  {
    type: '业务规则',
    reference: '定义了触发条件：当TOP10客户流失率>15%时启动规则修订，决策逻辑包含收入贡献、战略协同等权重算法',
    flag: 'Y'
  },
  {
    type: '业务KPI',
    reference: '设定了"大客户识别准确率≥90%""标准更新及时率100%"等指标',
    flag: 'N'
  },
  {
    type: '组织设置',
    reference: '设立了跨部门战略客户委员会，明确例会频次（每月1次）和决策权限',
    flag: 'Y'
  },
  {
    type: '岗位角色职责',
    reference: '定义了数据分析师负责模型维护、销售总监负责规则审批等职责',
    flag: 'N'
  },
  {
    type: '岗位协同RACI',
    reference: '设立了RACI矩阵，R(执行)=区域销售；A(审批)=委员会；C(咨询)=财务；I(知会)=客服',
    flag: 'Y'
  },
  {
    type: '组织岗位KPI',
    reference: '委员会成员考核“标准优化贡献度"，权重占绩效考核20%',
    flag: 'N'
  },
  {
    type: '人员动力',
    reference: '将客户分级准确率与销售提成系数挂钩（误差率每降1%提成+0.5%）',
    flag: 'Y'
  },
  {
    type: '人员能力要求',
    reference: '要求客户经理掌握RFM模型应用能力（需通过L3级认证）',
    flag: 'N'
  },
  {
    type: '人员能力评估',
    reference: '每半年开展模型应用实战考核，得分<80分者需复训',
    flag: 'Y'
  },
  {
    type: '能力培训',
    reference: '开发《大客户识别工作坊》课程，包含沙盘模拟和案例库',
    flag: 'Y'
  },
  {
    type: '系统赋能',
    reference: 'CRM系统需支持自定义评分规则配置和自动分级',
    flag: 'N'
  },
  {
    type: '数据治理',
    reference: '主数据字段完整率≥95%，历史交易数据覆盖近3年',
    flag: 'Y'
  },
  {
    type: '系统集成',
    reference: 'ERP与CRM系统客户数据实时同步，延迟≤5分钟',
    flag: 'N'
  },
  {
    type: '系统改善及规划',
    reference: '每年度评估系统对新型客户分类分级与其他大客户管理的支持度'
  }
])
</script>
<template>
  <div class="eval-answer-content">
    <div class="module-name">大客户管理</div>
    <div class="tip">提示1：请仔细阅读能力描述，结合实际情况，做出有无判断</div>
    <div class="model-list-wrap">
      <div class="model-list active">大客户分类分级</div>
      <div class="model-list">大客户关系管理</div>
      <div class="model-list">大客户价值挖掘</div>
      <div class="model-list">大客户绩效管理</div>
      <div class="model-list">我的改善期望</div>
    </div>
    <div class="eval-answer-main">
      <div class="comp-wrap">
        <div class="comp-item active">
          <div class="comp-name">明确大客户评价标准</div>
          <div class="comp-status">进行中</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">建立客户数据模板</div>
          <div class="comp-status">未开始</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">客户分级与标签化管理</div>
          <div class="comp-status">未开始</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">大客户资源匹配论证</div>
          <div class="comp-status">未开始</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">大客户作战地图绘制</div>
          <div class="comp-status">未开始</div>
        </div>
      </div>
      <div class="eval-answer">
        <div class="title">能力描述（明确大客户评价标准）</div>
        <div class="desc">
          明确大客户评价标准需建立数据驱动的量化评估体系：首先通过ERP/CRM系统集成提取交易频次、订单金额、利润率等核心数据（信息赋能-数据治理），结合行业对标制定多维度评分卡（流程赋能-业务规则）。由战略客户委员会（组织赋能-组织设置）牵头，市场、销售、财务组成专项组（岗位协同RACI），按季度更新标准并生成《大客户分级白皮书》（流程赋能-输出文档）。系统自动计算客户等级并推送至相关岗位（信息赋能-系统集成），同时将分级准确率纳入销售团队KPI（流程赋能-业务KPI）。每年开展客户经理能力认证（人岗赋能-能力培训），确保规则执行一致性。
        </div>
        <div class="eval-amswer-box">
          <div class="eval-answer-title">1、针对上述能力，请开展详细评估</div>
          <div class="eval-answer-table">
            <SimplenessTable :roundBorder="false" :columns="columns" :data="tableData">
              <template v-slot:oper>
                <el-table-column prop="oper" label="有无判断" width="130">
                  <template v-slot="scope">
                    <div class="answer-oper">
                      <div class="btn" :class="{ active: scope.row.flag == 'N' }">无</div>
                      <div class="btn" :class="{ active: scope.row.flag == 'Y' }">有</div>
                    </div>
                  </template>
                </el-table-column>
              </template>
            </SimplenessTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.eval-answer-content {
  .module-name {
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 16px;
    margin-bottom: 10px;
  }
  .tip {
    line-height: 35px;
    background: #eff4f9;
    border-radius: 5px 5px 5px 5px;
    font-size: 14px;
    color: #40a0ff;
    padding-left: 14px;
    margin-bottom: 14px;
  }
  .model-list-wrap {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 40px;
    border-bottom: 1px solid #d8d8d8;
    padding-left: 20px;
    .model-list {
      padding: 19px 20px;
      border-bottom: 3px solid transparent;
      margin-bottom: -1px;
      font-size: 14px;
      color: #3d3d3d;
      cursor: pointer;
      &.active {
        font-weight: 600;
        color: #40a0ff;
        border-bottom-color: #40a0ff;
      }
    }
  }
  .eval-answer-main {
    display: flex;
    align-items: flex-start;
    .comp-wrap {
      flex: 0 0 266px;
      border-right: 1px solid #d8d8d8;
      margin-right: 20px;
      .comp-item {
        border-bottom: 1px solid #d8d8d8;
        border-right: 3px solid transparent;
        padding: 10px 20px;
        color: #333;
        cursor: pointer;
        &.active {
          color: #53a9f9;
          border-right-color: #40a0ff;
          background: linear-gradient(-90deg, rgba(64, 160, 255, 0.3) 0%, rgba(64, 160, 255, 0) 100%);
          .comp-status {
            color: inherit;
          }
        }
        .comp-name {
          font-size: 16px;
          margin-bottom: 10px;
        }
        .comp-status {
          color: #888;
          font-size: 16px;
        }
      }
    }
    .eval-answer {
      padding-top: 20px;
      .title {
        font-weight: 600;
        font-size: 16px;
        color: #3d3d3d;
        line-height: 16px;
        margin-bottom: 12px;
      }
      .desc {
        font-size: 14px;
        color: #666666;
        line-height: 24px;
        padding-bottom: 20px;
        border-bottom: 1px dashed #e7e7e7;
        margin-bottom: 20px;
      }
      .eval-amswer-box {
        .eval-answer-title {
          font-weight: 600;
          font-size: 16px;
          color: #3d3d3d;
          line-height: 16px;
          margin-bottom: 20px;
        }
        .eval-answer-table {
          :deep(.el-table th.el-table__cell) {
            background: #e8eff7;
            color: #3d3d3d;
          }
        }
      }
      .answer-oper {
        display: flex;
        gap: 10px;
        .btn {
          width: 50px;
          line-height: 24px;
          background: #d8d8d8;
          text-align: center;
          border-radius: 3px 3px 3px 3px;
          font-size: 12px;
          color: #898989;
          cursor: pointer;
          &.active {
            background: #40a0ff;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
