<template>
  <div class="report_scope_wrap">
    <div class="report_scope_main flex_row_betweens">
      <div class="main_left page_section">
        <div class="main_left_top flex_row_betweens">
          <div class="report_range" :class="{ event_none: !isEdit }">
            <p class="page_second_title">报告生成范围</p>
            <div class="descript">
              组织报告：按照组织维度生成盘点报告，涉及的组织层级为：集团、公司与部门且仅组织负责人查看；
              个人报告盘点人员及其上级查看）
            </div>
            <p class="select_wrap">
              <el-checkbox v-model="reportOrgFlag" border>组织报告</el-checkbox>
              <el-checkbox v-model="reportUserFlag" border>个人报告</el-checkbox>
            </p>
          </div>
          <div class="report_range_view">
            <p class="page_second_title">可查看下级报告的范围</p>
            <div class="descript">
              组织报告：按照组织维度生成盘点报告，涉及的组织层级为：集团、公司与部门且仅组织负责人查看；
              个人报告盘点人员及其上级查看）
            </div>
            <p class="select_wrap">
              <el-radio v-model="junior" label="1">直接下级</el-radio>
              <el-radio v-model="junior" label="2">所有下级</el-radio>
            </p>
          </div>
        </div>
        <div class="main_left_bottom">
          <p class="page_second_title">可查看报告统计</p>
          <div class="">
            <table-component maxHeight="500" :tableData="tableData" :needPagination="needPagination"></table-component>
          </div>
        </div>
      </div>
      <!--            <ul class="main_right page_section">-->
      <!--                <li>-->
      <!--                    <p>有权限查看报告人数(人)</p>-->
      <!--                    <div>-->
      <!--                        <el-input type='number' min='0' v-model="viewReport" size="mini"></el-input>-->
      <!--                    </div>-->
      <!--                </li>-->
      <!--                <li>-->
      <!--                    <p>有权限查看个人报告(人)</p>-->
      <!--                    <div>-->
      <!--                        <el-input type='number' min='0' v-model="viewReportPerson" size="mini"></el-input>-->
      <!--                    </div>-->
      <!--                </li>-->
      <!--                <li>-->
      <!--                    <p>有权限查看组织报告(人)</p>-->
      <!--                    <div>-->
      <!--                        <el-input type='number' min='0' v-model="viewReportGroup" size="mini"></el-input>-->
      <!--                    </div>-->
      <!--                </li>-->

      <!--            </ul>-->
    </div>
    <div class="talent_raview_btn_wrap align_center marginT_30" v-if="isEdit">
      <el-button class="page_confirm_btn" type="primary" @click="prev()">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="next()">下一步</el-button>
    </div>
  </div>
</template>

<script>
import { getEvalInfo, getReportScope, saveReportScope } from '../../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'

export default {
  name: 'reportScope',
  components: {
    tableComponent
  },
  props: ['evalId', 'isEdit'],
  data() {
    return {
      needPagination: false,
      reportOrgFlag: 'N',
      reportUserFlag: 'N',
      junior: '1',
      tableData: {
        columns: [
          {
            label: '部门',
            prop: 'orgName'
          },
          {
            label: '岗位',
            prop: 'postName'
          },
          {
            label: '姓名',
            prop: 'userName'
          },
          {
            label: '组织报告',
            prop: 'orgReportCount'
          },
          {
            label: '个人报告',
            prop: 'selfReportCount'
          },
          {
            label: '下级报告',
            prop: 'subReportCount'
          }
        ],
        data: []
      },
      viewReport: '',
      viewReportPerson: '',
      viewReportGroup: ''
    }
  },
  watch: {
    junior(val) {
      this.getReportScopeFun()
    }
  },
  created() {
    this.getEvalInfoFun()
    this.getReportScopeFun()
  },
  methods: {
    getEvalInfoFun() {
      getEvalInfo({
        evalId: this.evalId
      }).then(res => {
        console.log(res)
        this.reportOrgFlag = res.reportOrgFlag == 'Y' ? true : false
        this.reportUserFlag = res.reportUserFlag == 'Y' ? true : false
      })
    },
    getReportScopeFun() {
      getReportScope({
        evalId: this.evalId,
        reportViewScope: this.junior
      }).then(res => {
        console.log(res)
        this.tableData.data = res
      })
    },
    saveReportScopeFun() {
      if (!this.reportOrgFlag && !this.reportUserFlag) {
        this.$msg.warning('请选择报告生成范围！')
        return
      }
      saveReportScope({
        evalId: this.evalId,
        reportOrgFlag: this.reportOrgFlag ? 'Y' : 'N',
        reportUserFlag: this.reportUserFlag ? 'Y' : 'N'
      }).then(res => {
        console.log(res)
        if (res.code == 200) {
          this.$emit('nextStep')
        } else {
          this.$msg.warning(res.msg)
        }
      })
    },
    prev: function () {
      this.$emit('prevStep')
    },
    next: function () {
      this.saveReportScopeFun()
    }
  }
}
</script>

<style scoped lang="scss">
.report_scope_wrap {
  .report_scope_title {
    padding: 3px 8px;
    font-size: 16px;
    line-height: 28px;
    color: #0099fd;
    font-weight: bold;
    background-color: #f2f8ff;
    border-radius: 3px;
  }

  .report_scope_main {
    .main_left {
      .main_left_top {
        min-height: 180px;

        .descript {
          margin: 10px 0;
          line-height: 28px;
        }

        .select_wrap {
          height: 40px;
          line-height: 40px;

          .el-checkbox.is-bordered {
            padding: 0;
            border: none;
          }
        }

        .report_range {
          // width: 278px;
          width: 50%;
          margin-right: 20px;
        }

        .report_range_view {
          // width: 278px;
          width: 50%;
        }
      }

      .main_left_bottom {
        // width: 700px;
        .el-input__inner {
          width: 100%;
        }
      }
    }

    .main_right {
      // width: 270px;
      width: 40%;

      p {
        height: 35px;
        line-height: 35px;
      }

      div {
        .el-input__inner {
          width: 100%;
          height: 35px;
        }
      }
    }
  }
}
</style>
