<script setup>
const tabList = ref([
  {
    name: "销售战略",
    code: 1,
  },
  {
    name: "市场洞察",
    code: 2,
  },
  {
    name: "客户洞察",
    code: 3,
  },
  {
    name: "市场营销",
    code: 4,
  },
  {
    name: "商机管理",
    code: 5,
  },
  {
    name: "客户管理",
    code: 6,
  },
  {
    name: "渠道管理",
    code: 7,
  },
  {
    name: "销售运营",
    code: 8,
  },
  {
    name: "销售团队管理",
    code: 9,
  },
  {
    name: "销售数字化",
    code: 10,
  },
]);
const nldb = ref([
  {
    title: "",
    info: "‌营销与销售‌：27.0分，相对较低，需加强市场洞察、品牌建设和渠道管理能力。",
  },
  {
    title: "",
    info: "产品研发‌：23.1分，同样较低，需提升产品规划、创新机制与敏捷开发能力。",
  },
  {
    title: "",
    info: "采购管理‌：30.1分，处于中等水平，需进一步优化供应商管理、成本控制和风险管理。",
  },
  {
    title: "",
    info: "生产管理‌：38.7分，表现相对较好，但仍需持续优化设备维护、工艺标准化和生产成本。",
  },
  {
    title: "",
    info: "供应链产销协同‌：32.5分，需加强需求预测、库存优化和物流网络规划。",
  },
  {
    title: "",
    info: "组织人才发展‌：21.9分，最低，需重点提升人才梯队建设、绩效与激励机制和培训发展体系。",
  },
  {
    title: "",
    info: "产业分析与跟踪‌：24.8分，需加强行业趋势分析、政策法规跟踪和市场需求洞察。",
  },
]);
const tabActSign = ref(1);
</script>
<template>
  <div class="com_right_wrap">
    <div class="t">专项能力综合表现</div>
    <div class="tab_list_wrap justify-between">
      <div
        class="item"
        :class="{ act: i.code == tabActSign }"
        v-for="i in tabList"
      >
        {{ i.name }}
      </div>
    </div>
    <div class="t">专项能力：销售战略</div>
    <div class="info_section_wrap">
      <div class="section_title justify-between">
        <div class="page-title-line">整体得分与级别</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="section_main justify-between">
        <div class="i_wrap chart_wrap">
          <div class="c_title">能力指数</div>
          <div class="chart_box"></div>
        </div>
        <div class="i_wrap chart_wrap">
          <div class="c_title">能力阶段</div>
          <div class="chart_box"></div>
        </div>
        <div class="i_wrap desc_wrap">
          <div class="t">整体得分与级别详情</div>
          <div class="desc_main">
            <div class="">
              本次测评整体得分 61.8 分，处于筑基阶段 ，距离上一层级 协作阶段
              ，还有 4.2 分的差距；
            </div>
            <div class="">
              <span class="bolder">阶段定义：</span>
              团队处于能力构建初期，成员技能分散，需系统性能力补足。
            </div>
            <div class="bolder">核心特征：</div>
            <div class="">
              <span class="icon_s"></span>
              多数成员得分低于50分，能力薄弱领域显著；
            </div>
            <div class="">
              <span class="icon_s"></span>
              团队缺乏核心骨干，协作依赖个别高分段成员；
            </div>
            <div class="">
              <span class="icon_s"></span>
              需优先解决基础技能短板，避免效率瓶颈。
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="section_title justify-between">
        <div class="page-title-line">各项能力对标</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="section_main section2_main justify-between">
        <div class="i_wrap chart_wrap">
          <div class="c_title">能力得分对比</div>
          <div class="chart_box"></div>
        </div>
        <div class="i_wrap desc_wrap">
          <div class="t">各项能力对比详情</div>
          <div class="desc_main">
            <div class="" v-for="item in nldb">
              <span class="icon_s"></span>
              {{ item.info }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
