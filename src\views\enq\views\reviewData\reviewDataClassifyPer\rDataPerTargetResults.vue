<template>
  <div class="job_grade_wrap rDataPer_target_results_wrap bg_write">
    <div class="page_main_title flex_row_betweens">
      <div class="title flex_row_start">
        <p>盘点数据查看</p>
        <div class="check_title" v-if="enqName"><span>/</span>{{ enqName }}</div>
        <div class="check_title"><span>/</span>个人盘点数据-目标与结果</div>
      </div>
      <div class="goback_geader" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section">
      <div class="page_section job_grade_center clearfix">
        <div class="filter_bar_wrap">
          <div class="flex_row_start">
            <div class="filter_item title">筛选</div>
            <div class="filter_item">
              <el-input v-model="userName" placeholder="按姓名模糊查询" clearable></el-input>
            </div>
            <div class="filter_item">
              <el-input v-model="objectiveName" placeholder="按目标名称模糊查询" clearable></el-input>
            </div>
            <div class="filter_item">
              <el-input v-model="resultName" placeholder="按结果名称模糊查询" clearable></el-input>
            </div>
            <div class="filter_item">
              <el-button class="page_add_btn" type="primary" @click="getTargetDataFun">查询</el-button>
            </div>
          </div>
          <div class="filter_item">
            <el-button class="page_add_btn" type="primary" @click="exportData">导出</el-button>
          </div>
        </div>
        <table-component :needIndex="true" :tableData="tableData" :needPagination="false"> </table-component>
      </div>
    </div>
  </div>
</template>

<script>
import tableComponent from '@/components/talent/tableComps/tableComponent'
import { getPersonalObjective, exportDataConfirm, exportListDownload } from '../../../request/api'
export default {
  name: 'rDataPerTargetResults',
  components: {
    tableComponent
  },
  props: [''],
  data() {
    return {
      enqId: this.$route.query.enqId,
      enqName: this.$route.query.enqName,
      userName: '',
      objectiveName: '',
      resultName: '',
      tableData: {
        columns: [
          {
            label: '一级组织',
            prop: 'oneLevelName'
          },
          {
            label: '二级组织',
            prop: 'twoLevelName'
          },
          {
            label: '三级组织',
            prop: 'threeLevelName'
          },
          {
            label: '四级组织',
            prop: 'fourLevelName'
          },
          {
            label: '姓名',
            prop: 'userName'
          },
          {
            label: '岗位',
            prop: 'postName'
          },
          {
            label: '目标名称',
            prop: 'objectiveName'
          },
          {
            label: '目标权重',
            prop: 'objectiveWeight'
          },
          {
            label: '目标自评',
            prop: 'objectiveSelfScore'
          },
          {
            label: '关键结果',
            prop: 'resultName'
          },
          {
            label: '结果权重',
            prop: 'resultWeight'
          },
          {
            label: '结果自评',
            prop: 'resultSelfScore'
          }
        ],
        data: [],
        page: {
          current: 1,
          size: 10,
          total: 0
        }
      }
    }
  },
  mounted() {
    this.getPersonalObjectiveFun()
  },
  methods: {
    getPersonalObjectiveFun() {
      getPersonalObjective({
        userName: this.userName,
        objectiveName: this.objectiveName,
        resultName: this.resultName,
        enqId: this.enqId
        // current:this.tableData.page.current,
        // size:this.tableData.page.size
      }).then(res => {
        if (res.code == 200) {
          console.log(res.code)
          this.tableData.data = res.data
          // this.tableData.page = res.page
        }
      })
    },
    //  //pageSize 改变时会触发
    // handleSizeChange(size) {
    //     console.log(size);
    //     this.tableData.page.current = 1;
    //     this.tableData.page.size = size;
    //     this.getPersonalObjectiveFun();
    // },
    // //currpage 改变时会触发
    // handleCurrentChange(page) {
    //     this.tableData.page.current = page;
    //     this.getPersonalObjectiveFun();
    // },
    getTargetDataFun() {
      this.getPersonalObjectiveFun()
    },
    exportData() {
      this.exportDataConfirmFun()
    },
    exportDataConfirmFun() {
      exportDataConfirm({
        enqId: this.enqId,
        type: 'a'
      }).then(res => {
        this.exportDownloadFun(res)
      })
    },
    exportDownloadFun(res) {
      const blob = new Blob([res])
      const elink = document.createElement('a')
      elink.download = this.enqName + '-个人盘点数据-目标与结果列表.xlsx'
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    }
  }
}
</script>

<style scoped lang="scss"></style>
