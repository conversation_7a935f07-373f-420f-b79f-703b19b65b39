<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getCmpyBizDomain, getCmpyJobClass, getCmpyJobLevel } from '../../../request/api'
import { formatterData, deepClone, compareDate } from '@/utils/utils'
import { Delete } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/modules/user'
const userStore = useUserStore()
const props = defineProps({
  workData: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['deleteItem'])

const yesOrNo = ref([])
const jobLevelOption = ref([])
const jobClassOption = ref([])
const bizDomainOption = ref([])

onMounted(() => {
  userStore.getDocList?.(['YES_NO', 'JOB_LEVEL']).then(res => {
    yesOrNo.value = res.YES_NO
    // jobLevelOption.value = res.JOB_LEVEL
  })
  getCmpyBizDomain().then(res => {
    if (res.code == '200') {
      bizDomainOption.value = res.data
    } else {
      ElMessage.error('获取业务领域信息失败！')
    }
  })
  getCmpyJobClass().then(res => {
    if (res.code == '200') {
      jobClassOption.value = formatterData(res.data)
    } else {
      ElMessage.error('获取岗位类型信息失败！')
    }
  })
  getCmpyJobLevel().then(res => {
    if (res.code == '200') {
      jobLevelOption.value = formatterData(res.data)
    } else {
      ElMessage.error('获取岗位职层信息失败！')
    }
  })
})

function deleteItem(item, index) {
  emit('deleteItem', item, index)
}
function handleChange(codeArr, type) {
  // 可根据需要处理
}
function endDateChange(val, rowData) {
  const data = deepClone(rowData)
  if (val) {
    if (!compareDate(data.endDate, data.beginDate)) {
      ElMessage.warning('结束日期需大于开始日期')
      rowData.endDate = ''
    }
  }
}
</script>

<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in props.workData" :key="item.id">
      <el-input class="item long" v-model.trim="item.companyName" placeholder="填写公司名称"></el-input>
      <el-select class="item short" v-model="item.currentCompany" placeholder>
        <el-option v-for="yn in yesOrNo" :label="yn.codeName" :key="yn.dictCode" :value="yn.dictCode"></el-option>
      </el-select>
      <el-date-picker
        class="item long"
        value-format="YYYY-MM-DD"
        v-model="item.beginDate"
        type="date"
        placeholder="选择日期"
      ></el-date-picker>
      <el-date-picker
        class="item long"
        value-format="YYYY-MM-DD"
        v-model="item.endDate"
        type="date"
        @change="val => endDateChange(val, item)"
        placeholder="选择日期"
      ></el-date-picker>
      <el-select class="item" v-model="item.jobLevelCode" placeholder="选择职层">
        <el-option v-for="jl in jobLevelOption" :key="jl.code" :label="jl.value" :value="jl.code"></el-option>
      </el-select>
      <el-select class="item" v-model="item.highestJobLevel" placeholder="是否最高职层">
        <el-option label="是" value="Y"></el-option>
        <el-option label="否" value="N"></el-option>
      </el-select>
      <el-cascader
        class="item"
        placeholder="选择岗位类型"
        :options="jobClassOption"
        v-model="item.jobClassCode"
        :change-on-select="true"
        :clearable="true"
        :filterable="true"
        :show-all-levels="false"
        :props="{ value: 'code', label: 'value' }"
        @change="val => handleChange(val, 'jobClassCode')"
      ></el-cascader>
      <el-select class="item" v-model="item.bizDomainCode" placeholder="选择业务领域">
        <el-option
          v-for="bd in bizDomainOption"
          :key="bd.bizDomainCode"
          :label="bd.bizDomainName"
          :value="bd.bizDomainCode"
        ></el-option>
      </el-select>
      <el-select class="item short" v-model="item.postRelated" placeholder>
        <el-option v-for="yn in yesOrNo" :key="yn.dictCode" :label="yn.codeName" :value="yn.dictCode"></el-option>
      </el-select>
      <el-select class="item short" v-model="item.industryRelated" placeholder>
        <el-option v-for="yn in yesOrNo" :key="yn.dictCode" :label="yn.codeName" :value="yn.dictCode"></el-option>
      </el-select>
      <div class="item item_icon_wrap">
        <el-icon class="item_icon" @click="() => deleteItem(item, index)"><Delete /></el-icon>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// ... existing code ...
</style>

<style scoped lang="scss">
.edu_info_item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  /*padding: 5px 10px;*/

  .item {
    // flex: 1;
    width: 11%;

    &.short {
      width: 7%;
    }

    &.long {
      width: 15%;
    }

    &.long2 {
      width: 30%;
    }
  }

  .item_icon_wrap {
    text-align: center;
    width: 5%;

    .item_icon {
      font-size: 20px;
      color: #0099fd;
      cursor: pointer;
    }
  }
}

.el-input__inner {
  // padding:0 5px;
  padding-right: 5px;
}
.el-range-input .el-date-editor .el-range-separator,
.el-date-editor .el-range-input {
  padding: 0 1px;
}
.el-date-editor .el-range-input {
  height: 26px;
}
</style>
