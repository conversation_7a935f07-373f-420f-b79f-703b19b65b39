<template>
  <el-menu
    class="aside_filter_wrap"
    :class="size"
    active-text-color="#0099fd"
    :default-openeds="[defaultActive]"
    unique-opened
  >
    <template v-for="item in itemData" :key="item.id">
      <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.id.toString()">
        <template #title>
          <span>{{ item.name }}</span>
        </template>
        <el-menu-item
          v-for="child in item.children"
          :index="child.id.toString()"
          :key="child.id"
          :data-index="child.id"
          @click="postId(child.id)"
        >
          <i class="check_icon el-icon-check"></i>
          {{ child.name }}
        </el-menu-item>
      </el-sub-menu>
      <el-menu-item v-else :index="item.id" @click="postId(item.id)">
        <template #title>
          <span>{{ item.name }}</span>
        </template>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  defaultActive: {
    type: String,
    default: '1'
  },
  itemData: {
    type: Array,
    default: () => []
  },
  size: {
    type: String,
    default: 'normal'
  }
})

const emit = defineEmits(['getId'])

function postId(id) {
  emit('getId', id)
}
</script>

<style scoped lang="scss">
.aside_filter_wrap {
  width: 100%;
  border: none;
  &.small {
    :deep(.el-submenu__title) {
      height: auto;
      line-height: 30px;
      padding: 0 8px !important;
    }
    .el-menu-item {
      height: auto;
      line-height: 26px;
      min-width: auto;
      padding: 0 16px !important;
      font-size: 12px;
      .check_icon {
        // display: none;
      }
      &.is-active {
        font-weight: normal;
        .check_icon {
          // display: inline;
        }
      }
    }
    :deep(.el-submenu__icon-arrow) {
      display: none;
    }
    :deep(.el-sub-menu.is-active .el-submenu__title) {
      font-weight: normal;
      font-size: 12px;
    }
  }
  :deep(.el-submenu__icon-arrow) {
    right: inherit;
    left: 16px;
    top: calc(50% + 2px);
  }
  .el-menu {
    border: none;
  }
  :deep(.el-sub-menu.is-active .el-submenu__title) {
    /*font-weight: bold;*/
    /*background-color: #f2f2f2;*/
  }
  :deep(.el-menu-item) {
    color: #909399;
    font-size: 12px;
    .check_icon {
      font-weight: bold;
      display: none;
    }
    &.is-active {
      .check_icon {
        display: inline;
      }
    }
  }
  :deep(.el-submenu__title) {
    color: #909399;
    padding-left: 34px !important;
  }
  :deep(.el-menu-item) {
    padding-left: 34px !important;
  }
  :deep(.el-menu-item.is-active) {
    /*font-weight: bold;*/
  }
}
</style>
