<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in eduInfoData" :key="item.id">
      <el-input class="item school_name" v-model="item.graduateSchool" placeholder="填写院校名称" />
      <el-date-picker
        class="item"
        value-format="YYYY-MM-DD"
        v-model="item.graduateDate"
        type="date"
        placeholder="选择日期"
      />
      <el-select class="item" v-model="item.qualification" placeholder="选择学历">
        <el-option
          v-for="option in qualificationOptions"
          :key="option.dictCode"
          :label="option.codeName"
          :value="option.dictCode"
        />
      </el-select>
      <el-select class="item" v-model="item.highestQualification" placeholder="是否最高学历">
        <el-option label="是" value="Y" />
        <el-option label="否" value="N" />
      </el-select>
      <el-select class="item" v-model="item.postRelated" placeholder="选择是否相关">
        <el-option v-for="option in yseOrNo" :key="option.dictCode" :label="option.codeName" :value="option.dictCode" />
      </el-select>
      <el-select class="item" v-model="item.industryRelated" placeholder="选择是否相关">
        <el-option v-for="option in yseOrNo" :key="option.dictCode" :label="option.codeName" :value="option.dictCode" />
      </el-select>
      <div class="item item_icon_wrap">
        <el-icon class="item_icon" @click="deleteItem(item, index)"><Delete /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
  eduInfoData: {
    type: Array,
    default: () => [
      {
        id: '1',
        schoolName: '',
        graduationDate: '',
        education: '',
        post: '',
        industry: ''
      }
    ]
  }
})

const emit = defineEmits(['deleteItem'])

const userStore = useUserStore()

// 响应式状态
const yseOrNo = ref([])
const qualificationOptions = ref([])

// 方法
const deleteItem = (item, index) => {
  emit('deleteItem', item, index)
}

// 生命周期钩子
onMounted(async () => {
  try {
    const docList = await userStore.getDocList(['QUALIFICATION', 'YES_NO'])
    qualificationOptions.value = docList.QUALIFICATION
    yseOrNo.value = docList.YES_NO
  } catch (error) {
    console.error(error)
  }
})
</script>

<style scoped lang="scss">
.edu_info_item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  padding: 8px 16px;

  .item {
    width: 23%;
  }

  .item_icon_wrap {
    text-align: center;
    width: 8%;

    .item_icon {
      font-size: 20px;
      color: #0099fd;
      cursor: pointer;
    }
  }
}
</style>
