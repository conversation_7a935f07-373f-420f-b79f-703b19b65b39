<script setup>
defineOptions({ name: 'indicator' })
const gnjj = ref([
  {
    title: '行业典型指标',
    info: '提供行业核心指标库，可查看指标定义、计算方法及趋势，AI 分析影响因素并给出管理建议，助您快速掌握指标特性与优化方向；'
  },
  {
    title: '典型岗位承接',
    info: '展示各岗位需承接的指标，包括指标概述、承接逻辑及考核建议，支持 AI 解读岗位指标责任，清晰定位关键岗位的指标关联与考核重点。'
  },
  {
    title: '指标对应能力',
    info: '挖掘指标背后的核心能力，提供能力提升路径，AI 解读为改善指标需强化的能力项，助力明确能力建设方向与行动措施。'
  }
])
const appList = [
  {
    path: '/indicator/libraryRefer',
    title: '参考指标库',
    desc: '提供行业典型指标模板，支持按职能 / 层级快速查询参考。'
  },
  {
    path: '/indicator/libraryTending',
    title: '指标库维护',
    desc: '企业自定义专属指标，关联组织架构与岗位，支持增删改查。'
  },
  {
    path: '/indicator/targetTending',
    title: '目标值与实际值维护',
    desc: '分层级设定指标目标值，手动录入或自动同步指标实际数据。'
  },
  {
    path: '/indicator/diagnosticAnalysis',
    title: '指标诊断与根因分析',
    desc: '揭示KPI指标背后的业务能力支撑关系，定位能力短板'
  },
  {
    path: '/indicator/trendRisk',
    title: '指标趋势预测与风险预警',
    desc: '多维度看。'
  },
  {
    path: '/indicator/benchmarking',
    title: '指标智能对标',
    desc: '指标智能对标'
  },
  {
    path: '/indicator/improveTasks',
    title: '指标改善任务一览',
    desc: '根据'
  },
  {
    path: '/indicator/indicatorImprove',
    title: '指标改善效果追踪',
    desc: '追踪各类任务与项目对指标的阶段性影响。'
  }
]

const router = useRouter()
const jump = item => {
  router.push(item.path)
}
</script>
<template>
  <div class="indicator_home_wrap page-container">
    <div class="page-title-line">应用定位</div>
    <div class="location border p-[16px] text-[16px] mb-[32px]">
      “指标透视罗盘” 是集指标管理与智能分析于一体的功能模块，可查看参考指标库并维护企业专属指标库及目标、实际值，通过 AI
      分析实现根因定位与改善建议、趋势预测与风险预警、智能对标与策略制定，生成指标改善关键任务，支持改善效果追踪，形成指标管理闭环。
    </div>
    <div class="page-title-line">功能导航</div>
    <div class="location border p-[20px] text-[16px] mb-[32px]">
      <img class="img" src="../../assets/imgs/indicator/img_03.png" alt="" srcset="" />
      <div class="h-[1px] bg-[#d8d8d8] mb-[20px]"></div>
      <div class="page-title-icon">
        <SvgIcon name="indicator-scene-icon"></SvgIcon>
        <div class="text">功能简介</div>
      </div>
      <div class="gnjj_content_wrap">
        <div class="item_wrap justify-start" v-for="item in gnjj">
          <span class="icon"></span>
          <span class="title">{{ item.title }}</span>
          <span class="info">{{ item.info }}</span>
        </div>
      </div>
    </div>
    <div class="page-title-line">应用入口</div>
    <div class="app-wrap">
      <div class="app-list border" @click="jump(item)" v-for="item in appList">
        <div class="app-title">{{ item.title }}</div>
        <div class="app-desc">{{ item.desc }}</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.indicator_home_wrap {
  .justify-start {
    display: flex;
    justify-content: flex-start;
  }
  .location {
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    .img {
      width: 1332px;
      height: 140px;
      margin: 0 auto 25px;
    }
    .gnjj_content_wrap {
      .item_wrap {
        margin-bottom: 12px;
        position: relative;
        .icon {
          position: absolute;
          top: 8px;
          width: 11px;
          height: 11px;
          background: url('@/assets/imgs/indicator/icon_06.png') no-repeat center center;
          background-size: 100% 100%;
        }
        .title {
          margin: 0 10px 0 21px;
          color: #40a0ff;
          white-space: nowrap;
        }
      }
    }
  }
  .border {
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
  }
  .bg-gradient {
    background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), #ffffff;
  }
  .app-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    .app-list {
      width: calc(20% - 16px);
      padding: 20px;
      cursor: pointer;
      background: url('../../assets/imgs/indicator/img_04.png') no-repeat center center;
      background-size: 100% 100%;
      border-color: transparent;
      &:hover {
        background: url('../../assets/imgs/indicator/img_05.png') no-repeat center center;
        background-size: 100% 100%;
        border-color: #53a9f9;
        .app-title {
          color: #53a9f9;
        }
      }
      .app-title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        margin-bottom: 6px;
        .app-desc {
          font-size: 14px;
          color: #666666;
          line-height: 22px;
        }
      }
    }
  }
}
</style>
