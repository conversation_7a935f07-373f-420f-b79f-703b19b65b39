<script setup>
import MarkdownIt from 'markdown-it'
import service, { cancelRequest } from '@/api/request.js'
import { useDialogueStore } from '@/stores'
const props = defineProps({
  type: String
})
// 对话内容
const list = ref([])
const loading = ref(false)
const getConverse = async code => {
  useDialogueStore().setFirst('')
  // 最后一次问答记录用于后面结束未停止的接口
  useDialogueStore().setLast({
    sessionType: 'common',
    id: code,
    agentType: props.type
  })
  loading.value = true
  list.value = []
  list.value.push({
    type: 'ask',
    text: code
  })
  service({
    url: '/agent/aiDiagnosis',
    method: 'POST',
    data: {
      sessionType: 'common',
      id: code,
      agentType: props.type
    },
    responseType: 'stream',
    onDownloadProgress: progressEvent => {
      loading.value = false
      const chunk = progressEvent.event.currentTarget.responseText
      const lines = chunk.split('\n')
      for (const line of lines) {
        if (line.startsWith('data:')) {
          const data = line.slice(5).trim()
          try {
            const jsonData = JSON.parse(data)
            if (jsonData?.data?.answer) {
              const lastIndex = list.value.length - 1
              const lastItem = list.value[lastIndex]
              const result = extractThinkContent(jsonData.data.answer)
              // 判断是否是回答的第一次
              if (lastItem && lastItem.type == 'answer') {
                // 判断时候是思考过程
                if (jsonData.data.running_status) {
                  list.value[lastIndex].text = '思考中...'
                } else {
                  list.value[lastIndex].think = result.thinkContent
                  list.value[lastIndex].text = new MarkdownIt().render(result.contentAfterThink)
                }
              } else {
                list.value.push({
                  type: 'answer',
                  think: '',
                  text: '思考中...'
                })
              }

              chatContent.value.scrollTop = chatContent.value.scrollHeight - chatContent.value.offsetHeight
            }
          } catch (error) {
            // console.error('Failed to parse JSON data:', error)
          }
        }
      }
    }
  })
    .finally(() => {
      loading.value = false
    })
    .catch(err => {
      if (list.value.length != 0) list.value[list.value.length - 1].think = '回答已终止'
    })
}

// 处理think数据
function extractThinkContent(str) {
  const startIndex = str.indexOf('<think>')
  if (startIndex == -1) {
    return {
      thinkContent: '',
      contentAfterThink: str
    }
  }
  const start = startIndex + '<think>'.length
  const endIndex = str.indexOf('</think>', start)
  if (endIndex == -1) {
    return {
      thinkContent: str.slice(start),
      contentAfterThink: ''
    }
  }
  // 截取 </think> 到字符串末尾的内容
  const contentAfterThink = str.slice(endIndex + '</think>'.length)
  return {
    thinkContent: str.slice(start, endIndex),
    contentAfterThink
  }
}
// 判断历史还是新对话
function isNewConversation(code) {
  // 结束请求
  cancelRequest(`post/agent/aiDiagnosis${useDialogueStore().lastData}`)
  getConverse(code)
}

//#region 页面滚动
const chatContent = ref(null) //装会话的容器
const isScrolling = ref(false) //用于判断用户是否在滚动

function handleScroll() {
  const scrollContainer = chatContent.value
  const scrollTop = scrollContainer.scrollTop
  const scrollHeight = scrollContainer.scrollHeight
  const offsetHeight = scrollContainer.offsetHeight
  if (scrollTop + offsetHeight < scrollHeight) {
    // 用户开始滚动并在最底部之上，取消保持在最底部的效果
    isScrolling.value = true
  } else {
    // 用户停止滚动并滚动到最底部，开启保持到最底部的效果
    isScrolling.value = false
  }
}
//#endregion
onMounted(() => {
  // isNewConversation()
  chatContent.value.addEventListener('scroll', handleScroll)
})
defineExpose({
  isNewConversation
})
</script>
<template>
  <div class="main">
    <div class="frame" ref="chatContent">
      <template v-for="(item, index) in list" :key="index">
        <div class="answer" v-if="item.type !== 'ask'">
          <div class="content">
            <div class="think">{{ item.think }}</div>
            <div class="text" v-if="item.text" v-html="item.text"></div>
          </div>
        </div>
      </template>
      <!-- 加载 -->
      <div class="answer" v-if="loading">
        <div class="content" v-loading="loading">
          <div class="text" style="width: 50px"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.main {
  height: 100%;
  display: flex;
  flex-direction: column;
  .frame {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 0 10px;
    flex-grow: 1;
    .ask {
      max-width: 95%;
      background: #cae5ff;
      border-radius: 8px 8px 8px 8px;
      color: #333333;
      line-height: 30px;
      font-size: 16px;
      padding: 10px 16px;
      align-self: flex-end;
      margin-bottom: 20px;
    }
    .answer {
      align-self: flex-start;
      display: flex;
      margin-bottom: 20px;
      .profile {
        width: 40px;
        height: 40px;
        background: #ffffff;
        flex-shrink: 0;
        margin-right: 10px;
        border-radius: 50%;
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      .content {
        .text {
          // background: #ffffff;
          // box-shadow: 0px 0px 6px 0px #d8e9ff;
          // border-radius: 8px 8px 8px 8px;
          color: #3d3d3d;
          font-size: 16px;
          line-height: 29px;
          // padding: 20px 40px;
          text-align: justify;
        }
        .think {
          font-size: 14px;
          color: #8b8b8b;
          margin-bottom: 5px;
        }
        :deep(.el-loading-mask) {
          border-radius: 8px;
        }
      }
    }
  }
}
</style>
