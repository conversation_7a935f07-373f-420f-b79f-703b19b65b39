<template>
  <div class="department_process_main">
    <div class="page_second_title marginT_30">部门主要参与流程</div>
    <div class="department_main marginT_20">
      <div class="aside_wrap">
        <div class="title">流程分类</div>
        <div class="tree_list">
          <treeCompCheckbox
            :treeData="treeData"
            :nodeKey="'bizProcessCode'"
            :labelKey="'bizProcessName'"
            @node-click-callback="nodeClickCallback"
          ></treeCompCheckbox>
        </div>
      </div>
      <div class="process_table fr clearfix">
        <div class="table_wrap">
          <tableComponent :needIndex="true" :needPagination="false" :tableData="tableData">
            <template #prefix>
              <el-table-column align="left" width="50">
                <template #header>
                  <el-checkbox v-model="checkAll" @change="changeCheckAll"></el-checkbox>
                </template>
                <template #default="scope">
                  <el-checkbox v-model="scope.row.check" @change="changeCheck($event, scope.row)"></el-checkbox>
                </template>
              </el-table-column>
            </template>
          </tableComponent>
        </div>
        <div class="result_wrap fr">
          <div class="title">已选</div>
          <div class="selected_list">
            <div
              class="result_classify"
              v-show="item.list.length > 0"
              v-for="(item, classifyIndex) in resultProcessList"
              :key="item.classifyCode"
            >
              <div class="result_title">{{ item.classifyName }}</div>
              <div class="result_item" v-for="(list, index) in item.list" :key="list.bizProcessCode">
                {{ list.bizProcessName }}
                <el-icon class="del_icon fr" @click="deleteResultList(classifyIndex, index, item.classifyCode, list)">
                  <Minus />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="clearfix"></div>
    <div class="marginT_20 align_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Minus } from '@element-plus/icons-vue'
import { getProcessTree, getBizProcessByCode, saveEnqOrgBizProcess } from '../../../request/api'
import treeCompCheckbox from '@/components/talent/treeComps/treeCompCheckbox.vue'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  orgCode: String
})

const emit = defineEmits(['prevStep', 'nextStep'])

const checkAll = ref(false)
const backupData = ref(null) // 备份右侧渲染已选列表数据
const checkedCodeArr = ref([]) // 储存已选列表的唯一标识，用来做反选中列表
const resultProcessList = ref([]) // 右侧渲染列表数据
const treeData = ref([])
const tableData = reactive({
  columns: [
    {
      label: '流程编码',
      prop: 'bizProcessCode',
      width: 100,
      sortable: true
    },
    {
      label: '流程名称',
      prop: 'bizProcessName',
      width: 120
    },
    {
      label: '流程描述',
      prop: 'bizProcessDesc'
    },
    {
      label: '流程层级',
      prop: 'layerNo',
      width: 80,
      formatterFun: obj => {
        const level = obj.layerNo
        const levelMap = {
          1: '一級',
          2: '二級',
          3: '三級',
          4: '四級',
          5: '五級',
          6: '六級'
        }
        return levelMap[level] || ''
      }
    },
    {
      label: '前置流程',
      prop: 'prevProcessName',
      width: 120
    }
  ],
  data: []
})

onMounted(() => {
  getProcessTreeFun()
})

const getProcessTreeFun = async () => {
  try {
    const res = await getProcessTree({
      enqId: props.enqId,
      orgCode: props.orgCode
    })

    if (res.code == 200) {
      treeData.value = res.data.TreeBiz
      const classifyData = res.data.oneBizProcessList

      resultProcessList.value = classifyData.map(item => ({
        classifyCode: item.bizProcessCode,
        classifyName: item.bizProcessName,
        list: []
      }))

      tableData.data = res.data.enqBizProcessList

      tableData.data.forEach(item => {
        if (item.check) {
          const oneLevelCode = item.oneLevelCode
          checkedCodeArr.value.push(item.bizProcessCode)

          const targetClassify = resultProcessList.value.find(list => oneLevelCode == list.classifyCode)
          if (targetClassify) {
            targetClassify.list.push(item)
          }
        }
      })
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const getBizProcessByCodeFun = async code => {
  try {
    const res = await getBizProcessByCode({
      bizProcessCode: code,
      enqId: props.enqId,
      orgCode: props.orgCode
    })

    if (res.code == 200) {
      const data = res.data.enqBizProcessList
      tableData.data = data.map(item => ({
        ...item,
        check: checkedCodeArr.value.includes(item.bizProcessCode)
      }))
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const saveEnqOrgBizProcessFun = async stepType => {
  try {
    const res = await saveEnqOrgBizProcess([
      {
        bizProcessCode: checkedCodeArr.value,
        enqId: props.enqId,
        orgCode: props.orgCode
      }
    ])

    if (res.code == 200) {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('保存失败')
  }
}

const deleteResultList = (classifyIndex, index, oneLevelCode, row) => {
  const code = row.bizProcessCode
  const codeIndex = checkedCodeArr.value.indexOf(code)

  // 从已选列表的code数组中删除当前这一条数据的code
  checkedCodeArr.value.splice(codeIndex, 1)

  // 从已选列表数据中删除当前这一条
  resultProcessList.value[classifyIndex].list.splice(index, 1)

  // 更新表格中对应项的选中状态
  const tableItem = tableData.data.find(item => code == item.bizProcessCode)
  if (tableItem) {
    tableItem.check = false
  }
}

const nodeClickCallback = val => {
  const code = val.join(',')
  backupData.value = window.$util.deepClone(resultProcessList.value)
  getBizProcessByCodeFun(code)
}

const changeCheckAll = check => {
  changeTableDataCheckAll(check)
}

const changeCheck = (check, row) => {
  getCheckedList(row)
}

const changeTableDataCheckAll = flag => {
  tableData.data.forEach(item => {
    item.check = flag
    getCheckedList(item)
  })
}

const getCheckedList = row => {
  const index = checkedCodeArr.value.indexOf(row.bizProcessCode)
  const oneLevelCode = row.oneLevelCode

  if (row.check) {
    if (index == -1) {
      checkedCodeArr.value.push(row.bizProcessCode)
      const targetClassify = resultProcessList.value.find(item => oneLevelCode == item.classifyCode)
      if (targetClassify) {
        targetClassify.list.push(row)
      }
    }
  } else {
    if (index > -1) {
      checkedCodeArr.value.splice(index, 1)
      const targetClassify = resultProcessList.value.find(item => oneLevelCode == item.classifyCode)
      if (targetClassify) {
        const listIndex = targetClassify.list.findIndex(list => list.bizProcessCode == row.bizProcessCode)
        if (listIndex > -1) {
          targetClassify.list.splice(listIndex, 1)
        }
      }
    }
  }
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      saveEnqOrgBizProcessFun('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') {
        emit('prevStep')
      }
    })
}

const nextBtn = () => {
  if (checkedCodeArr.value.length == 0) {
    ElMessage.warning('请选择部门参与流程！')
    return
  }
  saveEnqOrgBizProcessFun('nextStep')
}
</script>

<style scoped lang="scss">
.aside_wrap {
  float: left;
  width: 220px;
  .title {
    height: 34px;
    line-height: 34px;
    text-align: center;
    background: #ebf4ff;
  }

  border: 1px solid #e5e5e5;
  .tree_list {
    padding: 10px;
    height: 400px;
    overflow-y: auto;
  }
}

.process_table {
  width: 920px;
  .table_wrap {
    float: left;
    width: 700px;
  }

  .result_wrap {
    width: 190px;
    border: 1px solid #e5e5e5;
    .title {
      height: 34px;
      line-height: 34px;
      text-align: center;
      background: #ebf4ff;
    }
    .selected_list {
      padding: 10px;
      height: 400px;
      overflow-y: auto;
    }
    .result_classify {
      margin-bottom: 16px;
      .result_title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .result_item {
        border: 1px solid #e5e5e5;
        margin-bottom: 4px;
        line-height: 30px;
        padding: 0 8px;
        border-radius: 2px;
        &:hover {
          color: #0099ff;
          border-color: #0099ff;
        }
        .del_icon {
          display: inline-block;
          width: 20px;
          height: 20px;
          background: #d2d2d2;
          border-radius: 50%;
          margin-top: 4px;
          cursor: pointer;
          text-align: center;
          font-size: 18px;
          line-height: 20px;
          color: #fff;
          &:hover {
            background: #0099ff;
          }
        }
      }
    }
  }
}
.el-table__row {
  height: 45px;
}
.el-table__header-wrapper {
  .el-checkbox__inner {
    width: 16px;
    height: 16px;
    &::after {
      width: 5px;
      height: 9px;
      top: 0;
    }
  }
}
.el-table__body-wrapper {
  height: 376px;
  /*border: 1px solid #e5e5e5;*/
  overflow-y: auto;
  .el-checkbox__inner {
    width: 16px;
    height: 16px;
    &::after {
      width: 5px;
      height: 9px;
      top: 0;
    }
  }
}
</style>
