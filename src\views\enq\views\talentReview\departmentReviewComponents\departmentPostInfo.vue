<template>
  <div class="department_post_info_main">
    <div class="department_main clear">
      <div class="page_second_title marginT_30">
        部门岗位信息
        <span class="title_tip fs20 main_color">（在维护部门岗位信息时，请忽略现有人员的特征，仅考虑岗位本身）</span>
      </div>
      <!--			<div class="page_second_title right_title fr">岗位工作环境</div>-->
      <div class="department_content marginT_20">
        <el-table :data="tableData">
          <el-table-column type="index" label="序号" width="40"></el-table-column>
          <el-table-column prop="postName" label="岗位名称" width="80"></el-table-column>
          <el-table-column prop="importance" label="重要性">
            <template #default="scope">
              <el-select v-model="scope.row.importance" size="small" placeholder="请选择">
                <el-option
                  v-for="item in importanceOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="qualification" label="教育程度">
            <template #default="scope">
              <el-select v-model="scope.row.qualification" size="small" placeholder="请选择">
                <el-option
                  v-for="item in qualificationOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="workExperience" label="工作经验">
            <template #default="scope">
              <el-select v-model="scope.row.workExperience" size="small" placeholder="请选择">
                <el-option
                  v-for="item in experienceOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="postExperience" label="同岗位经验">
            <template #default="scope">
              <el-select v-model="scope.row.postExperience" size="small" placeholder="请选择">
                <el-option
                  v-for="item in experienceOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="industryExperience" label="行业经验">
            <template #default="scope">
              <el-select v-model="scope.row.industryExperience" size="small" placeholder="请选择">
                <el-option
                  v-for="item in experienceOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="managementExperience" label="管理经验">
            <template #default="scope">
              <el-select v-model="scope.row.managementExperience" size="small" placeholder="请选择">
                <el-option
                  v-for="item in experienceOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="communicationFrequency" label="沟通频率">
            <template #default="scope">
              <el-select v-model="scope.row.communicationFrequency" size="small" placeholder="请选择">
                <el-option
                  v-for="item in frequencyOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="communicationSkill" label="沟通技巧">
            <template #default="scope">
              <el-select v-model="scope.row.communicationSkill" size="small" placeholder="请选择">
                <el-option
                  v-for="item in skillOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="communicationScope" label="沟通范围">
            <template #default="scope">
              <el-select v-model="scope.row.communicationScope" size="small" placeholder="请选择">
                <el-option
                  v-for="item in scopeOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="overtimeFrequency" label="加班频率">
            <template #default="scope">
              <el-select v-model="scope.row.overtimeFrequency" size="small" placeholder="请选择">
                <el-option
                  v-for="item in overTimeOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="travelFrequency" label="出差频率">
            <template #default="scope">
              <el-select v-model="scope.row.travelFrequency" size="small" placeholder="请选择">
                <el-option
                  v-for="item in travelOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="oper_btn_wrap align_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPostInfo, updateEnqPostInfo } from '../../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  orgCode: String
})

const emit = defineEmits(['prevStep', 'nextStep'])

const importanceOptions = ref([])
const qualificationOptions = ref([])
const experienceOptions = ref([])
const frequencyOptions = ref([])
const scopeOptions = ref([])
const skillOptions = ref([])
const overTimeOptions = ref([])
const travelOptions = ref([])
const tableData = ref([])

onMounted(async () => {
  await getDocFun()
  await getPostInfoFun()
})

const getPostInfoFun = async () => {
  try {
    const res = await getPostInfo({
      enqId: props.enqId,
      orgCode: props.orgCode
    })

    if (res.code == 200) {
      tableData.value = res.data.map(item => ({
        enqId: item.enqId,
        postCode: item.postCode,
        postName: item.postName,
        importance: item.importance,
        qualification: item.qualification,
        workExperience: item.workExperience,
        postExperience: item.postExperience,
        industryExperience: item.industryExperience,
        managementExperience: item.managementExperience,
        communicationFrequency: item.communicationFrequency,
        communicationSkill: item.communicationSkill,
        communicationScope: item.communicationScope,
        overtimeFrequency: item.overtimeFrequency,
        travelFrequency: item.travelFrequency
      }))
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const updateEnqPostInfoFun = async stepType => {
  try {
    const res = await updateEnqPostInfo(tableData.value)

    if (res.code == 200) {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('保存失败')
  }
}

const checkData = data => {
  for (const obj of data) {
    if (window.$util.objHasEmpty(obj)) {
      return true
    }
  }
  return false
}

const getDocFun = async () => {
  const docList = [
    'IMPORTANCE',
    'QUALIFICATION',
    'EXPERIENCE_AGE',
    'COMMUNICATION_FREQUENCY',
    'COMMUNICATION_SKILL',
    'COMMUNICATION_SCOPE',
    'OVERTIME_FREQUENCY',
    'TRAVEL_FREQUENCY'
  ]

  try {
    const res = await window.$getDocList(docList)
    importanceOptions.value = res.IMPORTANCE
    qualificationOptions.value = res.QUALIFICATION
    experienceOptions.value = res.EXPERIENCE_AGE
    frequencyOptions.value = res.COMMUNICATION_FREQUENCY
    scopeOptions.value = res.COMMUNICATION_SCOPE
    skillOptions.value = res.COMMUNICATION_SKILL
    overTimeOptions.value = res.OVERTIME_FREQUENCY
    travelOptions.value = res.TRAVEL_FREQUENCY
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据字典失败')
  }
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      updateEnqPostInfoFun('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') {
        emit('prevStep')
      }
    })
}

const nextBtn = () => {
  if (checkData(tableData.value)) {
    ElMessage.warning('请完善数据后提交！')
    return
  }
  updateEnqPostInfoFun('nextStep')
}
</script>

<style scoped lang="scss">
.department_main {
  .right_title {
    width: 182px;
  }
}
.el-table__row {
  height: 45px;
}
.el-table .cell {
  padding: 0 5px;
}

.el-input__inner {
  padding: 0 5px;
}
</style>
