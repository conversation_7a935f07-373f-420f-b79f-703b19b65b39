<template>
    <div class="model_mateInfo_relation_wrap">
        <div class="model_mateInfo_relation_main flex_row_betweens">
            <div class="model_mateInfo_relation_left page_section">
                <p class="model_mateInfo_relation_title">被评岗位</p>    
                <ul>
                    <li v-for="(item,index) in postList">{{item.label}}</li>
                </ul>
            </div>
            <div class="model_mateInfo_relation_right page_section">
                <p class="model_mateInfo_relation_title">评价岗位</p>   
                <div class="select_wrap">
                    <span class="descript">同级确认</span>
                    <el-radio v-model="equativeConfirmValue"  v-for="(item,index) in equativeConfirm" :label="item.label">{{item.value}}</el-radio>
                </div>
                <div class="select_wrap">
                    <span class="descript">上级确认</span>
                    <el-radio v-model="supConfirmValue"  v-for="(item,index) in supConfirm" :label="item.label">{{item.value}}</el-radio>
                </div>
                <div class="select_wrap">
                    <span class="descript">下级确认</span>
                    <el-radio v-model="subConfirmValue"  v-for="(item,index) in subConfirm" :label="item.label">{{item.value}}</el-radio>
                </div>

                <el-table class="table_wrap" :data="tableData.data" border height='578px' @cell-click="cellClick">
                    <el-table-column v-for="(item,index) in tableData.tableTitle"
                        :label="item.label"
                        align="center"
                    >
                        <el-table-column  v-for="(item1,index1) in item.childrenLabels"
                            v-if="!item1.canCheck"
                            :label="item1.label"
                            :prop="item1.prop"
                            align="center"
                        >
                        </el-table-column> 
                        <el-table-column v-for="(item1,index1) in item.childrenLabels"
                            v-if="item1.canCheck"
                            :label="item1.label"
                            :prop="item1.prop"
                            align="center"
                        >
                        <template slot-scope="scope">
                            <span :class="{ 'el-icon-check':scope.row[item1.prop],'check_box_wrap':true}"></span>
                        </template>
                        </el-table-column> 
                    </el-table-column> 
                </el-table>
            </div>
        </div>
    </div>
</template>
 
<script>

export default {
    name: "modelMateInfoRelation",
    components: {
    },
    data() {
        return {
            postList:[
                {
                    label:'销售一部销售总监',
                    id:1
                },
                {
                    label:'销售二部销售总监',
                    id:2
                },
                {
                    label:'销售三部销售总监',
                    id:3
                },
                {
                    label:'销售四部销售总监',
                    id:4
                },
                {
                    label:'销售五部销售总监',
                    id:5
                },
            ],
            equativeConfirmValue:'1',
            equativeConfirm:[
                {
                    label:'1',
                    value:'同一上级的其它岗位'
                },
                {
                    label:'2',
                    value:'同一上级的所有岗位'
                },
            ],
            supConfirmValue:'1',
            supConfirm:[
                {
                    label:'1',
                    value:'直接上级'
                },
                {
                    label:'2',
                    value:'直接上级的上级'
                },
            ],
            subConfirmValue:'',
            subConfirm:[
                {
                    label:'1',
                    value:'直接下级'
                },
                {
                    label:'2',
                    value:'直接下级的下级'
                },
            ],
            tableData:{
                tableTitle:[
                    {
                        label:'',
                        childrenLabels:[
                            {
                                label:"",
                                prop:"index",
                                canCheck:false,
                            },
                            {
                                label:"评价关系",
                                prop:"abilityClassity",
                                canCheck:false,

                            },
                            {
                                label:"关系",
                                prop:"abilityDict",
                                canCheck:false,
                            },
                        ]
                    },
                    {
                        label:'个性特质',
                        childrenLabels:[
                            {
                                label:"5",
                                prop:"post1",
                                canCheck:true,
                            },
                        ]
                    },
                    {
                        label:'专业知识',
                        childrenLabels:[
                            {
                                label:"5",
                                prop:"post2",
                                canCheck:true,
                            }
                        ]
                    },
                    {
                        label:'人际互动',
                        childrenLabels:[
                            {
                                label:"4",
                                prop:"post3",
                                canCheck:true,
                            }
                        ]
                    },
                    {
                        label:'领导力',
                        childrenLabels:[
                            {
                                label:"4",
                                prop:"post4",
                                canCheck:true,
                            }
                        ]
                    },
                    {
                        label:'人才管理',
                        childrenLabels:[
                            {
                                label:"4",
                                prop:"post5",
                                canCheck:true,
                            }
                        ]
                    },
                    {
                        label:'客户管理',
                        childrenLabels:[
                            {
                                label:"4",
                                prop:"post6",
                                canCheck:true,
                            }
                        ]
                    },
                    {
                        label:'业务管理',
                        childrenLabels:[
                            {
                                label:"4",
                                prop:"post7",
                                canCheck:true,
                            }
                        ]
                    },
                ],
                data:[
                    {
                        index:1,
                        abilityClassity:'个性特质',
                        abilityDict:'结果导向',
                        post1:false,
                        post2:false,
                        post3:true,
                        post4:false,
                        post5:false,
                        post6:true,
                        post7:false,
                    },
                    {
                        index:2,
                        abilityClassity:'个性特质',
                        abilityDict:'结果导向',
                        post1:false,
                        post2:false,
                        post3:false,
                        post4:true,
                        post5:false,
                        post6:false,
                        post7:false,
                    },
                    {
                        index:3,
                        abilityClassity:'个性特质',
                        abilityDict:'结果导向',
                        post1:false,
                        post2:false,
                        post3:false,
                        post4:false,
                        post5:true,
                        post6:true,
                        post7:false,
                    },
                    {
                        index:4,
                        abilityClassity:'个性特质',
                        abilityDict:'结果导向',
                        post1:true,
                        post2:false,
                        post3:true,
                        post4:false,
                        post5:false,
                        post6:true,
                        post7:false,
                    },
                    {
                        index:5,
                        abilityClassity:'个性特质',
                        abilityDict:'结果导向',
                        post1:false,
                        post2:false,
                        post3:true,
                        post4:true,
                        post5:false,
                        post6:true,
                        post7:false,
                    },
                    {
                        index:6,
                        abilityClassity:'个性特质',
                        abilityDict:'结果导向',
                        post1:true,
                        post2:false,
                        post3:false,
                        post4:false,
                        post5:false,
                        post6:true,
                        post7:false,
                    },
                    {
                        index:7,
                        abilityClassity:'个性特质',
                        abilityDict:'结果导向',
                        post1:true,
                        post2:false,
                        post3:true,
                        post4:false,
                        post5:false,
                        post6:false,
                        post7:false,
                    },
                    {
                        index:8,
                        abilityClassity:'个性特质',
                        abilityDict:'结果导向',
                        post1:false,
                        post2:false,
                        post3:true,
                        post4:false,
                        post5:false,
                        post6:false,
                        post7:false,
                    },
                    {
                        index:9,
                        abilityClassity:'个性特质',
                        abilityDict:'结果导向',
                        post1:true,
                        post2:false,
                        post3:true,
                        post4:false,
                        post5:false,
                        post6:true,
                        post7:false,
                    },
                    {
                        index:10,
                        abilityClassity:'个性特质',
                        abilityDict:'结果导向',
                        post1:true,
                        post2:false,
                        post3:false,
                        post4:false,
                        post5:true,
                        post6:false,
                        post7:false,
                    },
                ]
            },


        };
    },
    created() {
    },
    methods: {
        cellClick(row,column,cell,event){
            // console.log(row)
            // console.log(column)
            // console.log(event)
            // console.log(cell)
            // console.log(row[column.property])
            if(row[column.property] != true && row[column.property] != false){
                return;
            }
            for( let i = 0; i<this.tableData.data.length;i++){
                if(this.tableData.data[i].index == row.index){
                    if(this.tableData.data[i][column.property] == true){
                        this.tableData.data[i][column.property] = false;
                    }else{
                        this.tableData.data[i][column.property] = true;
                    }
                    return;
                }
            }
        },
    }
};
</script>
 
<style scoped lang="scss">
.model_mateInfo_relation_wrap{
    .model_mateInfo_relation_main{
        .model_mateInfo_relation_title{
            padding: 3px 8px;
            font-size: 16px;
            line-height: 28px;
            color: #0099fd;
            font-weight: bold;
            background-color: #f2f8ff;
            border-radius: 3px;
        }
        .model_mateInfo_relation_left{
            width: 25%;
            ul{
                li{
                    margin: 5px 0 0 0;
                    padding: 0 5px;
                    height: 36px;
                    line-height: 36px;
                    cursor: pointer;
                    border: 1px solid #f2f8ff;
                }
                li:hover{
                    border: 1px solid #0099fd;
                    color: #0099fd;
                }
            }

        }
        .model_mateInfo_relation_right{
            width: 75%;
            .select_wrap{
                padding: 0 0 0 10px;
                height: 40px;
                line-height: 40px;
                .descript{
                    display: inline-block;
                    width: 80px;
                    color: #0099fd;
                }
            }
            .table_wrap tbody td{
                padding: 0 !important;
            }
            .table_wrap tbody .cell{
                padding:12px 0;
                height: 100%;
                cursor: pointer;
            }
            .table_wrap .check_box_wrap{
                color: #0099fd;
                font-weight: 700;
            }
        }
    }
}

</style>