<script setup>
import { manageViewList } from '@/assets/data/manageView.js'
defineOptions({ name: 'manageView' })
const router = useRouter()
const toDetail = item => {
  console.log('toDetail', item)
  let title = item.title.split('：')[0]
  router.push({ path: '/manageView/detail', query: { id: item.id, title: title } })
}
</script>
<template>
  <div class="view-wrap">
    <div class="view-content">
      <div class="view-item" v-for="item in manageViewList" :key="item.id">
        <div class="view-item-content">
          <div class="item-icon" :style="{ background: item.iconBgColor }">
            <svg-icon class="icon" :name="item.icon"></svg-icon>
          </div>
          <div class="content-main">
            <div class="item-title">{{ item.title }}</div>
            <div class="item-desc">{{ item.desc }}</div>
          </div>
        </div>
        <div class="detail-btn" @click="toDetail(item)">查看详情</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.view-wrap {
  .view-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 20px;
    .view-item {
      width: calc(25% - 20px);
      padding: 20px 20px 10px;
      box-sizing: border-box;
      background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), rgba(255, 255, 255, 0.5);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      &:hover {
        box-shadow: 0px 0px 10px 0px rgba(83, 172, 255, 0.5);
        border-color: #53acff;
      }

      .view-item-content {
        display: flex;
        align-items: flex-start;
        padding-bottom: 29px;
        border-bottom: 1px solid #d8d8d8;
        margin-bottom: 9px;
        .item-icon {
          flex: 0 0 46px;
          height: 46px;
          border-radius: 4px;
          padding: 11px;
          margin-right: 14px;
          .icon {
            width: 24px;
            height: 24px;
          }
        }
        .content-main {
          flex: 1;
          .item-title {
            font-weight: 600;
            font-size: 16px;
            color: #333333;
            line-height: 16px;
            margin-bottom: 16px;
          }
          .item-desc {
            font-size: 14px;
            color: #666666;
            line-height: 22px;
          }
        }
      }
      .detail-btn {
        text-align: center;
        font-size: 14px;
        color: #666666;
        line-height: 32px;
        border-radius: 10px;
        cursor: pointer;
        &:hover {
          color: #1890ff;
          background: rgba(134, 171, 211, 0.1);
          color: #53a9f9;
        }
      }
    }
  }
}
</style>
