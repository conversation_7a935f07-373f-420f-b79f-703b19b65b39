<script setup>
const props = defineProps(['sectionTabVCheckSign', 'sectionTabVList', 'title'])
const emits = defineEmits(['checkSecTabV'])
const checkSecTabV = c => {
  emits('checkSecTabV', c)
}
</script>
<template>
  <div class="section_tab_v_wrap">
    <div class="v_tab_title">{{ props.title }}</div>
    <div
      class="s_tab_item"
      :class="{ act: props.sectionTabVCheckSign == item.code }"
      v-for="item in props.sectionTabVList"
      :key="item.code"
      @click="checkSecTabV(item.code)"
    >
      {{ item.name }}
    </div>
  </div>
</template>
<style lang="scss" scoped>
.section_tab_v_wrap {
  padding: 12px 20px 18px;
  width: 180px;
  background: linear-gradient(224deg, #d0e4f9 0%, rgba(195, 230, 255, 0.6) 100%);
  border-radius: 8px 8px 8px 8px;
  .v_tab_title {
    color: #3d3d3d;
  }
  .s_tab_item {
    margin-top: 10px;
    width: 140px;
    height: 35px;
    line-height: 35px;
    border-radius: 5px 5px 5px 5px;
    text-align: center;
    color: #6c757e;
    font-size: 14px;
    border: 1px solid #a5c1dc;
    cursor: pointer;
    &.act {
      color: #fff;
      background: #83c1ff;
      border-color: #83c1ff;
    }
  }
}
</style>
