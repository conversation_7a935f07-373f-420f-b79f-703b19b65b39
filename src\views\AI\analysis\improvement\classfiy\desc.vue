<script setup>
defineOptions({ name: 'descCom' })
const data = ref([
  {
    name: '策略名称',
    value: '需求计划全流程端到端贯通'
  },
  {
    name: '所属一级策略',
    value: '流程赋能'
  },
  {
    name: '所属二级策略',
    value: '流程端到端'
  },
  {
    name: '概要目标',
    value: '建立覆盖市场调研、数据整合、计划评审的全流程体系，实现需求计划全链路可视化与闭环管理。'
  },
  {
    name: '策略背景',
    value:
      '需求计划各环节存在信息断层，市场调研数据、销售数据与生产数据未有效贯通，导致需求计划与实际产能匹配度低，2024 年需求计划调整频繁，订单交付延迟率达 20%。'
  },
  {
    name: '核心目标',
    value:
      '实现需求计划全流程可视化，关键节点信息实时共享；闭环管理需求计划，提升计划执行准确性，订单交付延迟率降至 10% 以下。'
  },
  {
    name: '关键举措概览',
    value:
      '1.定义 10 大核心流程阶段（线索筛选→需求确认→方案设计→商务谈判→合同签署→交付启动→进度管控→验收结算→售后运维→复盘归档）； 2.开发流程闭环监控模型，设置阶段准入 / 准出标准（如未通过需求评审禁止进入方案设计） 3. 建立异常流程熔断机制，超过标准时长 1.5 倍自动触发升级处理'
  },
  {
    name: '实施步骤概览',
    value:
      '第 1-2 月：流程调研与蓝图设计；第 3-6 月：平台搭建与数据集成；第 7-10 月：试点运行与优化；第 11-12 月：全流程推广'
  },
  {
    name: '责任部门',
    value: '供应链计划部牵头，市场部、销售部、生产部、IT 部协同'
  },
  {
    name: '所需资源',
    value: '数字化平台建设预算、跨部门协作团队'
  },
  {
    name: '时间节点',
    value: '12 个月'
  },
  {
    name: '评估指标',
    value: '流程可视化覆盖率、计划执行准确率、订单交付延迟率'
  }
])
</script>
<template>
  <div class="page">
    <div class="item" v-for="item in data">
      <div class="item-name">{{ item.name }}</div>
      <div class="item-value">{{ item.value }}</div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.page {
  border: 1px solid #c6dbf3;
  .item {
    display: flex;
    align-items: stretch;
    &:last-of-type {
      .item-name,
      .item-value {
        border-bottom: none;
      }
    }

    .item-name {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 40px;
      flex: 0 0 130px;
      font-size: 14px;
      color: #93abcb;
      background: #eaf4ff;
      border-bottom: 1px solid #c6dbf3;
      border-right: 1px solid #c6dbf3;
    }
    .item-value {
      flex: 1;
      min-height: 40px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #c6dbf3;
      line-height: 21px;
      padding: 5px 16px;
      color: #333333;
    }
  }
}
</style>
