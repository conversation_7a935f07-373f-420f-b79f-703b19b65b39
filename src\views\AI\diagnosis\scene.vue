<script setup>
defineOptions({ name: 'scene' })
const emits = defineEmits(['next'])
const activeIndex = ref('3')
const coreList = ref([
  {
    title: '【GTMB】销售营销2B'
  },
  {
    title: '【GTMC】销售营销2C'
  },
  {
    title: '【PLM】产品全生命周期管理'
  },
  {
    title: '【SCM-P&O】供应链 计划订单'
  },
  {
    title: '【SCM-P&S】供应链采购与供应商管理'
  },
  {
    title: '【SCM-M&PL】供应链制造与厂内物流'
  },
  {
    title: '【FPD】成套设备完美交付'
  },
  {
    title: '【E2E-QM】端到端质量管理'
  },
  {
    title: '【ABC】全业务链成本优化'
  },
  {
    title: '【DGA】目标落地论证'
  },
  {
    title: '【S&OP】销售与业务协同'
  },
  {
    title: '【O&PM】组织与人才管理'
  },
  {
    title: '【B&FM】预算与财务管理'
  },
  {
    title: '【PS&D】流程系统与数字化'
  }
])
const sceneList = ref([
  {
    title: '市场洞察与趋势预判',
    background: '市场变化快，缺系统化分析，战略决策滞后。',
    point: '能否多维度采集数据并识别机会 / 威胁？',
    question: '依赖经验判断，忽视技术 / 政策影响。',
    meaning: '避免战略偏差，提前布局。'
  },
  {
    title: '多渠道需求预测精准度',
    background: 'TOB/TOC/ 外销需求差异大，预测模型未分层适配。',
    point: '是否按渠道（TOB/TOC/ 外销）建立差异化预测模型？',
    question: 'TOB 订单预测粗放，TOC 忽视促销，外销缺本地化数据。',
    meaning: '提升分渠道预测精度，优化库存与交付。'
  },
  {
    title: '主计划协同与产能平衡',
    background: '主计划与产能脱节，产线负荷不均，履约周期长。',
    point: '是否建立 S&OP 机制？主计划能否匹配产能 / 物料约束？',
    question: '销售目标脱离产能，忽视物料瓶颈，插单无标准流程。',
    meaning: '平衡资源分配，减少浪费与延误。'
  },
  {
    title: '库存策略优化与周转提升',
    background: '库存周转低，资金占用高，滞销品积压。',
    point: '是否按 ABC 分类管理？安全库存是否动态计算？有无滞销品处理机制？',
    question: '库存策略 “一刀切”，忽视 VMI/JIT，缺库存风险分析。',
    meaning: '降低库存成本，释放流动资金。'
  },
  {
    title: '订单履约模式适配性',
    background: '多品种小批量订单增加，沿用大规模生产模式。',
    point: '是否按 MTS/MTO/ETO 场景制定差异化履约策略？',
    question: 'MTO/ETO 订单按 MTS 处理，缺优先级规则与资源分配。',
    meaning: '适配订单类型，降低定制化成本。'
  },
  {
    title: '工序计划刚性柔性平衡',
    background: '日计划调整频繁或过刚，产线效率低、异常处理慢。',
    point: '工序排程是否考虑设备 / 人员约束？异常事件能否快速响应？',
    question: '人工排程为主，异常处理滞后，工单数据不实时。',
    meaning: '平衡计划稳定性与灵活性，提升生产效率。'
  },
  {
    title: '物料需求计划精准度',
    background: '自有 / 外协工厂备料脱节，物料齐套率低、停产频发。',
    point: '物料需求是否与主计划、库存联动？外协备料是否考虑其产能波动？',
    question: '按理论 BOM 备料，忽视损耗与外协沟通，系统未对接。',
    meaning: '提升物料匹配度，降低断供与冗余。'
  },
  {
    title: '订单全链路可视化与响应',
    background: '订单执行信息滞后，异常处理低效，客户投诉率高。',
    point: '能否全链路（生产 / 物流）可视化？是否建立异常分级预警机制？',
    question: '人工跟踪进度，跨部门处理责任不清，响应慢。',
    meaning: '实时监控异常，提升客户满意度。'
  },
  {
    title: '多级库存协同与风险管控',
    background: '总部 - 区域仓 - 经销商库存割裂，牛鞭效应显著。',
    point: '是否打通多级库存数据？能否动态调整安全库存应对波动？',
    question: '经销商库存黑箱，区域仓滞销 / 断供并存，缺风险预判。',
    meaning: '优化库存分布，提升整体周转效率。'
  },
  {
    title: '计划数字化成熟度评估',
    background: '计划依赖手工处理，系统模块割裂，数据准确率低。',
    point: '是否部署集成化系统（如 SAP IBP）？能否支持 AI 预测与智能排程？',
    question: '系统数据孤岛，手工同步计划，缺数据校验机制。',
    meaning: '提升数字化水平，支撑敏捷供应链转型。'
  }
])

const onNext = () => {
  emits('next')
}
</script>
<template>
  <div class="core-list">
    <div
      class="list"
      :class="{ active: activeIndex == index }"
      v-for="(list, index) in coreList"
      @click="activeIndex = index"
    >
      {{ list.title }}
    </div>
  </div>
  <div class="page-title-line">典型评估场景</div>
  <div class="scene-content">
    <div class="scene-item" v-for="(item, index) in sceneList" :key="item.id">
      <div class="item-title">
        <div class="tag">场景{{ index + 1 }}</div>
        {{ item.title }}
      </div>
      <div class="item-content"><b>背景：</b>{{ item.background }}</div>
      <div class="item-content"><b>评估重点：</b>{{ item.point }}</div>
      <div class="item-content"><b>典型问题：</b>{{ item.question }}</div>
      <div class="item-content"><b>管理意义：</b>{{ item.meaning }}</div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.core-list {
  display: flex;
  flex-flow: row wrap;
  gap: 10px;
  margin-bottom: 30px;
  .list {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333333;
    width: calc((100% - 60px) / 7);
    height: 35px;
    padding: 0px 8px;
    border-radius: 5px 5px 5px 5px;
    line-height: 18px;
    background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #c6dbf3;
    text-align: center;
    cursor: pointer;
    &:hover,
    &.active {
      border: 1px solid #53acff;
      color: #40a0ff;
      box-shadow: 0px 0px 10px 0px rgba(124, 182, 237, 0.5);
    }
  }
}
.scene-content {
  display: flex;
  flex-flow: row wrap;
  align-items: stretch;
  gap: 20px;
  margin-bottom: 20px;
  .scene-item {
    width: calc(33.33% - 15px);
    padding: 28px 13px 28px 19px;
    background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    .item-title {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 15px;
      font-size: 16px;
      color: #333333;
      font-weight: 600;
      .tag {
        width: 60px;
        line-height: 21px;
        background: #53a9f9;
        border-radius: 3px 3px 3px 3px;
        text-align: center;
        color: #fff;
        font-size: 16px;
        font-weight: normal;
      }
    }
    .item-content {
      color: #666;
      font-size: 14px;
      line-height: 25px;
    }
  }
}
</style>
