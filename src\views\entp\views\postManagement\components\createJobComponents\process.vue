<template>
    <div class="department_process_main">
        <div class="page_second_title">流程分类</div>
        <div class="department_main">
            <div class="aside_wrap">
                <div class="aside_tree_title ">
                    一/二级流程
                </div>
                <div class="aside_tree_list">
                    <tree-comp-radio :treeData="treeData" :needCheckedFirstNode="false" :canCancel="true" @clickCallback="clickCallback"></tree-comp-radio>
                </div>
            </div>
            <div class="process_table">
                <tableComponent
                    :tableData="tableData"
                    :checkSelection='checkSelection'
                    :selectionStatus=true 
                    @selectionChange ='selectionChange'
                    @handleSizeChange='handleSizeChange'  
                    @handleCurrentChange='handleCurrentChange'
                    :size="'mini'"
                ></tableComponent>
                <div class="align_center paddT_12 marginT_16">
                    <el-button class="page_confirm_btn" type="primary" @click="submit">保 存</el-button>
                </div>
            </div>
        </div>

    </div>
</template>
 
<script>
import {getProcessTree,getProcessList,modifyJobBizProcess,jobBizProcessInfo} from '../../../../request/api'
import treeCompRadio from "@/components/talent/treeComps/treeCompRadio";
import tableComponent from "@/components/talent/tableComps/tableComponent";
export default {
    name: "process",
    props: {
       jobCode:String, 
    },
    components: {
        treeCompRadio,
        tableComponent
    },
    data() {
        return {
            treeData: [],
            tableData: {
                columns: [
                    {
                        label: "流程编码",
                        prop: "bizProcessCode",
                        width: 100
                    },
                    {
                        label: "流程名称",
                        prop: "bizProcessName",
                        width: 120
                    },
                    {
                        label: "流程描述",
                        prop: "bizProcessDesc",
                    },
                    {
                        label: "层级",
                        prop: "layerNo",
                        width: 60
                    },
                    {
                        label: "前置流程",
                        prop: "prevProcessName",
                        width: 120
                    }
                ],
                data: [],
                page:{
                    total:0,
                    current:1,
                    size:10
                }
            },
            checkBizProcessCode:'',
            jobBizProcessList :[],
            checkSelection:[]
        };
    },
    created(){
        
    },
    mounted(){
       
    },
    methods: {
        // 业务流程树
        getProcessTreeFun(){
            getProcessTree({
                bizProcessCode:'',
                companyId:this.companyId,
                layerNo:'',
            }).then(res=>{
                // console.log(res)
                if(res.code == 200){
                    this.treeData = res.data
                }else{
                    this.treeData = []
                }
                
            })
        },
        // 选择流程树
        clickCallback(val,isLastNode){
            // console.log(val,isLastNode)
            this.checkBizProcessCode = val
            this.tableData.page.current = 1
            this.getProcessListFun()
        },
        // 流程列表
        getProcessListFun(){
            getProcessList({
                bizProcessCode:this.checkBizProcessCode,
                companyId:this.companyId,
                current:this.tableData.page.current,
                size:this.tableData.page.size,
            }).then(res=>{
                // console.log(res)
                if(res.code == 200){
                    if(res.data.length > 0){
                        this.tableData.data = res.data.map(item=>{
                            return {
                                bizProcessCode:item.bizProcessCode,
                                bizProcessName:item.bizProcessName,
                                bizProcessDesc:item.bizProcessDesc,
                                layerNo:item.layerNo,
                                parentProcessName:item.parentProcessName,
                                prevProcessName:item.prevProcessName
                            }
                        })
                        this.getJobProcessFun()
                    }else{
                        this.tableData.data = []
                    }
                    this.tableData.page.total = res.total
                }else{
                    this.tableData.data = []
                    this.tableData.page = {
                        total:0,
                        current:1,
                        size:10,
                    }
                }
            })
        },
        // 翻页
        handleSizeChange(handleSizeChange){
            // console.log(handleSizeChange)
            this.tableData.page.size = handleSizeChange
            this.getProcessListFun();
        },
        handleCurrentChange(handleCurrentChange){
            // console.log(handleCurrentChange)
            this.tableData.page.current = handleCurrentChange
            this.getProcessListFun();
        },
        // 已配置流程
        getJobProcessFun(){
            this.checkSelection = []
            jobBizProcessInfo({
                jobCode:this.jobCode
            }).then(res=>{
                console.log(res)
                if(res.code == 200){
                    // this.selection = res.data
                    if(res.data.length > 0){
                        for(let j = 0;j<res.data.length;j++){
                            // if(res.data[j])
                            for(let i = 0;i<this.tableData.data.length;i++){
                                if(res.data[j] == this.tableData.data[i].bizProcessCode){
                                    this.checkSelection.push(this.tableData.data[i])
                                }
                            }
                        }
                        console.log(this.checkSelection)
                    }
                }
            })
        },
        // 配置流程
        selectionChange(selectionChange){
            this.jobBizProcessList  = selectionChange.map(item=>{
                return {
                    bizProcessCode:item.bizProcessCode,
                    jobCode:this.jobCode
                }
            })  
        },
        createJobProcessFun(){
            modifyJobBizProcess(
                this.jobBizProcessList
            ).then(res=>{
                console.log(res)
                if(res.code == 200){
                     this.$emit('submitSuccessTab','process')
                     this.$msg.success(res.msg)
                }else{
                    this.$msg.error(res.msg)
                }
            })
        },
        submit(){
            if(this.jobBizProcessList.length > 0){
                this.createJobProcessFun()
            }else{
                this.$msg.warning('请选择配置流程！')
            }
            
        }
    },
    watch:{
        companyId:{
            immediate:true,
            handler(val){
                if(val){
                    this.getProcessTreeFun()
                    this.getProcessListFun()
                }
            }
        }
    },
    computed:{
        companyId(){
            return this.$store.state.userInfo.companyId
        }
    }

};
</script>
 
<style scoped lang="scss">
    .department_main{
        margin-top: 20px;
        display: flex;
    }
.aside_wrap {
    width: 200px;
    min-height: 365px;
    border: 1px solid #e5e5e5;
    .aside_tree_title{
        padding: 5px;
        background: #EBF4FF;
        font-size: 14px;
    }
    .aside_tree_list{
        padding: 5px;
    }

}
    .process_table {
        flex: 1;
        margin-left: 16px;
    }
    .el-table__header tr th{
        line-height: 45px;
        font-size: 14px;
        padding: 0;
    }
    .el-table th>.cell{
        /*padding-left: 13px;*/
        /*padding-right: 12px;*/
        .el-checkbox__input{
            padding-left: 4px;
        }

    }
    .el-table .cell{
        line-height: 45px;
    }
</style>