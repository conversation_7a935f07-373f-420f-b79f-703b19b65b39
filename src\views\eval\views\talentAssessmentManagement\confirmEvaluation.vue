<template>
    <div class="start_prepare bg_write">
        <div class="page_main_title">
            {{ evalType }} / {{ evalName }}
            <div class="goback_geader" @click="goback()">
                <i class="el-icon-arrow-left"></i>返回
            </div>
        </div>
        <div class="page_section">
            <div class="page_second_title">
                在360°测评中，根据岗位评价关系，您需要对下列人员进行评价（同级与协同岗位人员），如无法做出评价，请点击取消
            </div>
            <div class="page_third_title">同级人员</div>
            <div class="item_content flex_row_wrap_start">
                <div
                    class="item"
                    v-for="(item, index) in peerList"
                    :key="item.objectId"
                >
                    <div class="status">
                        <div
                            class="icon el-icon-check"
                            v-if="item.status == 'Y'"
                        ></div>
                        <div class="text fs12" v-else>未选择</div>
                    </div>
                    <div class="user_name">{{ item.objectName }}</div>
                    <div class="org_name">{{ item.orgName }}</div>
                    <div class="post_name">{{ item.postName }}</div>
                    <div class="item_btn_wrap">
                        <el-button
                            class="item_btn"
                            type="primary"
                            size="mini"
                            @click="changeStatus('peerList', index)"
                            v-if="item.status == 'Y'"
                            >取消</el-button
                        >
                        <el-button
                            class="item_btn item_btn_confirm"
                            type="primary"
                            size="mini"
                            @click="changeStatus('peerList', index)"
                            v-else
                            >确认</el-button
                        >
                    </div>
                </div>
            </div>
            <div class="page_third_title" v-show="coopList.length">岗位协同人员</div>
            <div class="item_content flex_row_wrap_start">
                <div class="item" v-for="(item,index) in coopList" :key="item.objectId">
                    <div class="status">
                        <div
                            class="icon el-icon-check"
                            v-if="item.status == 'Y'"
                        ></div>
                        <div class="text fs12" v-else>未选择</div>
                    </div>
                    <div class="user_name">{{ item.objectName }}</div>
                    <div class="org_name">{{ item.orgName }}</div>
                    <div class="post_name">{{ item.postName }}</div>
                    <div class="item_btn_wrap">
                        <el-button
                            class="item_btn"
                            type="primary"
                            size="mini"
                            @click="changeStatus('coopList', index)"
                            v-if="item.status == 'Y'"
                            >取消</el-button
                        >
                        <el-button
                            class="item_btn item_btn_confirm"
                            type="primary"
                            size="mini"
                            @click="changeStatus('coopList', index)"
                            v-else
                            >确认</el-button
                        >
                    </div>
                </div>
            </div>
            <div class="page_third_title">是否隐藏您的信息</div>
            <div class="is_hidden_info">
                <el-radio-group class="radio_wrap" v-model="radio">
                    <el-radio :label="'Y'">是</el-radio>
                    <el-radio :label="'N'">否</el-radio>
                </el-radio-group>
                <div class="tips_text">
                    <div class="text tip_y" v-show="radio == 'Y'">您已选择隐藏个人信息，被评价人员无法了解您的信息，所有评价数据均以 “ 评价人1、2、3 ”进行记录</div>
                    <div class="text tip_n" v-show="radio == 'N'">您已选择不隐藏个人信息，被评价人员在评估结果中可看到您的岗位信息，所有评价数据均以 “ 岗位”进行记录</div>
                </div>
            </div>
        </div>
        <div class="marginT_30 align_center">
            <el-button class="page_confirm_btn" type="primary" @click="toStart"
                >开始测评</el-button
            >
        </div>
    </div>
</template>

<script>
    import {
        getEvalInfo,
        getRelationUser,
        relationConfirm,
    } from "../../request/api";
    import {deepClone} from '../../utils/index.js'
    export default {
        name: "confirmEvaluation",
        data() {
            return {
                evalId: this.$route.query.evalId,
                evalName: "",
                evalType: "",
                evalTypeObj: {
                    1: "调研问卷",
                    2: "岗能测评",
                    3: "业务测评",
                },
                peerList: [],
                coopList: [],
                radio: 'Y',
            };
        },
        created() {
            this.getEvalInfoFun();
            this.getRelationUserFun();
        },
        methods: {
            toStart() {
                let arr = this.peerList.concat(this.coopList);
                // arr = this.$util.deepClone(arr);
                arr = deepClone(arr)

                arr.forEach((item) => {
                    item["evalId"] = this.evalId;
                });
                console.log(arr);
                let params = {
                    list: arr,
                };
                relationConfirm(arr).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$router.push({
                            path: "/talentAssessment/modelAnswer",
                            query: {
                                evalId: this.evalId,
                                type: "eval",
                            },
                        });
                    }
                });
            },
            goback() {
                this.$router.go(-1);
            },
            getEvalInfoFun() {
                getEvalInfo({ evalId: this.evalId }).then((res) => {
                    this.evalName = res.evalName;
                    this.evalType = this.evalTypeObj[res.evalType];
                });
            },
            getRelationUserFun() {
                getRelationUser({ evalId: this.evalId }).then((res) => {
                    console.log(res);
                    this.peerList = res.peer;
                    this.coopList = res.coop;
                });
            },
            changeStatus(type, index) {
                let status = this[type][index]["status"] == "Y" ? "N" : "Y";

                console.log(status);
                // item.status == 'Y' ? 'N' : 'Y';
                this[type][index]["status"] = status
                // this.$set(this[type][index], "status", status);
            },
        },
    };
</script>

<style scoped lang="scss">
    .item_content {
        margin-left: 5px;
        .item {
            position: relative;
            width: 120px;
            height: 152px;
            border: 1px solid #0099fd;
            border-radius: 4px;
            margin: 0 8px 8px 0;
            padding: 8px 16px;
            &:hover {
            }
            .status {
                position: absolute;
                right: 8px;
                top: 4px;
                color: rgb(247, 205, 65);
                text-align: right;

                .icon {
                    color: #0099fd;
                    font-weight: bold;
                    font-size: 22px;
                }
                .el-icon-check{
                    transform: rotate(45deg);
                    width: 12px;
                    height: 20px;
                    border: 2px solid #0099fd;
                    border-style: solid;
                    border-width: 0px 3px 3px 0px;
                    
                }
               
            }
            .user_name {
                color: #0099fd;
                font-size: 18px;
            }
            .item_btn_wrap {
                padding-top: 8px;
                text-align: center;
            }
            .item_btn {
                position: absolute;
                left: 31px;
                bottom: 8px;
                padding: 6px 16px;
                &.item_btn_confirm{
                    background: #C8E7A7;
                    border-color: #C8E7A7;
                    &:hover{
                        background: #B2CB98;
                    }
                }
            }
        }
    }
    .is_hidden_info{
        display: flex;
        align-items: center;
    }
    .radio_wrap{
        width: 120px;
        padding: 3px 0;
        margin-right: 16px;
        margin-left: 4px;
    }
    .tips_text{
        // flex: 1;
        padding: 0 5px;
        line-height: 1;
        color: #00B0F0;
        .text{
            background: #F2FBFE;
        }
        .tip_n{
            color: #FFC000;
            background: #FFFCF2;
        }
    }
</style>