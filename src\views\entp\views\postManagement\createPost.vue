<template>
  <div class="create_post_wrap bg_write">
    <div class="page_main_title">
      {{ pageType == 'edit' ? '编辑岗位' : '新增岗位' }}
      <div class="goback_geader" @click="goback">
        <el-icon><ArrowLeft /></el-icon>返回
      </div>
    </div>
    <div class="page_section">
      <div class="create_post_center clearfix">
        <el-tabs tab-position="left" v-model="tabsDefault" @tab-click="tabClick">
          <el-tab-pane
            v-for="tab in tabsData"
            :key="tab.name"
            :label="tab.label"
            :name="tab.name"
            :disabled="tab.isDisabledSign"
          >
            <component
              v-if="tab.name == tabsDefault"
              :is="components[tabsDefault]"
              :copyOrgCode="copyOrgCode"
              :curPostCodeCopy="curPostCodeCopy"
              :curJobClassCodeCopy="curJobClassCodeCopy"
              @submitSuccessTab="submitSuccessTab"
              @curPostCode="curPostCode"
              @curJobClassCode="curJobClassCode"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, shallowRef, onMounted, defineAsyncComponent } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'

defineOptions({ name: 'CreatePost' })

// Router
const router = useRouter()
const route = useRoute()

// Components
const components = {
  basicInfo: defineAsyncComponent(() => import('./components/createPostComponents/basicInfo')),
  quality: defineAsyncComponent(() => import('./components/createPostComponents/quality')),
  ability: defineAsyncComponent(() => import('./components/createPostComponents/ability')),
  duty: defineAsyncComponent(() => import('./components/createPostComponents/duty')),
  teamwork: defineAsyncComponent(() => import('./components/createPostComponents/teamwork')),
  process: defineAsyncComponent(() => import('./components/createPostComponents/process')),
  CPworkActivity: defineAsyncComponent(() => import('./components/createPostComponents/CPworkActivity'))
}

// Reactive State
const pageType = ref(route.query.pageType == 'edit' ? 'edit' : 'create')
const isaAllocationSign = ref(route.query.isaAllocationSign || false)
const copyOrgCode = ref(route.query.orgCode)
const curPostCodeCopy = ref(route.query.postCode || '')
const curJobClassCodeCopy = ref('')
const tabsDefault = ref('')

const tabsData = ref([
  {
    id: 1,
    label: '基本信息',
    name: 'basicInfo',
    isDisabledSign: false
  },
  {
    id: 2,
    label: '素质要求',
    name: 'quality',
    isDisabledSign: true
  },
  {
    id: 3,
    label: '能力要求',
    name: 'ability',
    isDisabledSign: true
  },
  {
    id: 5,
    label: '协同岗位',
    name: 'teamwork',
    isDisabledSign: true
  }
])

// Methods
const goback = () => {
  router.push({
    name: 'postManagement',
    params: {
      useCache: true
    }
  })
}

const tabClick = (tab, event) => {
  console.log(tab, event)
}

const allTabCalcelDisabled = () => {
  if (!curPostCodeCopy.value) return

  tabsData.value.forEach(tab => {
    if (tab.isDisabledSign) {
      tab.isDisabledSign = false
    }
  })
}

const submitSuccessTab = submitTab => {
  if (submitTab == 'basicInfo') {
    tabsDefault.value = 'quality'
    allTabCalcelDisabled()
  } else if (submitTab == 'quality') {
    tabsDefault.value = 'ability'
  } else if (submitTab == 'ability') {
    tabsDefault.value = 'teamwork'
  }
}

const curPostCode = code => {
  curPostCodeCopy.value = code
}

const curJobClassCode = code => {
  curJobClassCodeCopy.value = code
}

// Lifecycle
onMounted(() => {
  if (isaAllocationSign.value) {
    tabsDefault.value = 'teamwork'
  } else {
    tabsDefault.value = tabsData.value[0].name
  }
  allTabCalcelDisabled()
})
</script>

<style lang="scss" scoped>
.create_post_center {
  padding-top: 28px !important;
}

:deep(.el-input) {
  width: auto;
}

:deep(.el-tabs--left .el-tabs__header.is-left) {
  width: 240px;
  padding: 10px 32px 0 16px;
  border-right: 1px solid #e4e7ed;
  margin-right: 36px;
}

:deep(.el-tabs--left .el-tabs__item.is-left) {
  text-align: center;
}

:deep(.el-tabs__active-bar) {
  display: none;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  text-align: center;
  background-color: #e4e7ed;
  margin-bottom: 16px;
  line-height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #525e6c;
  border-radius: 4px;
  cursor: pointer;

  &.is-active {
    background-color: var(--el-color-primary);
    color: #fff;
  }
}
</style>
