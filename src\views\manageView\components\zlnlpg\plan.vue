<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'
defineOptions({ name: 'plan' })
const reportList = ref([
  { id: 1, title: '20250201 销售预测核心能力测评报告（单人测评）', expand: false },
  { id: 2, title: '20250201 销售竞争力测评报告（多人测评）', expand: true }
])

const toggleViewReport = item => {
  item.expand = !item.expand
}
const planIndex = ref(0)
const planList = ref([
  {
    name: '能力培训',
    count: 3,
    list: [
      {
        name: '产品知识',
        measure: '产品卖点通关培训',
        action: '1. 开发《产品价值阶梯话术》课程\u000b2. 每周通关考核产品核心参数\u000b3. 制作客户常见问题应答手册',
        charge: '产品总监',
        result: '产品知识手册+全员通关认证'
      },
      {
        name: '客户沟通',
        measure: '销售话术标准化工程',
        action: '1. 建立30个典型场景话术库\u000b2. 录音分析改进计划\u000b3. 每月话术实战演练赛',
        charge: '销售经理',
        result: '标准化话术手册+场景案例库'
      },
      {
        name: '谈判技巧',
        measure: '价格谈判特训营',
        action: '1. 设计"让步阶梯"模拟训练\u000b2. 客户砍价录音复盘\u000b3. 制定不同客户类型谈判策略包',
        charge: '销售总监',
        result: '谈判策略地图+案例应对库'
      },
      {
        name: '异议处理',
        measure: '客户拒绝应对训练',
        action: '1. 收集Top20客户拒绝场景\u000b2. 开发《异议处理三板斧》工具卡\u000b3. 角色扮演通关测试',
        charge: '培训主管',
        result: '异议处理手册+应答话术库'
      },
      {
        name: '时间管理',
        measure: '销售日程规划课',
        action: '1. 导入《黄金时段管理法》\u000b2. 制定个人客户拜访路线图\u000b3. 安装时间记录分析APP',
        charge: '人力资源总监',
        result: '个人效率提升计划表'
      }
    ]
  },
  {
    name: '管理工具表单导入',
    count: 3,
    list: [
      {
        name: '销售过程标准化',
        measure: '销售漏斗管理模板导入',
        action:
          '1. 设计《销售阶段推进表》模板（含客户画像、需求痛点、跟进计划）\u000b2. 全员培训模板填写规范\u000b3. 每月抽查模板使用质量',
        charge: '销售运营经理',
        result: '标准化销售漏斗模板库+使用率达标报告'
      },
      {
        name: '客户资源沉淀',
        measure: '客户档案卡工具化',
        action:
          '1. 制定客户信息采集标准（基础信息、采购记录、服务反馈）\u000b2. 开发电子化档案模板\u000b3. 建立客户数据月度更新机制',
        charge: '客户成功经理',
        result: '客户档案完整率提升至90%+'
      },
      {
        name: '任务执行可视化',
        measure: '销售日报/周报模板统一',
        action:
          '1. 设计《日清日结表》（目标/完成量/问题/需支持）\u000b2. 推行钉钉/企业微信在线提报\u000b3. 管理层每日批注反馈',
        charge: '区域销售经理',
        result: '日报提交率100%+问题闭环跟踪表'
      },
      {
        name: '库存动态管理',
        measure: '产品库存预警表导入',
        action: '1. 按SKU设置安全库存阈值\u000b2. 建立库存周转率计算公式\u000b3. 培训仓储人员实时更新数据',
        charge: '供应链主管',
        result: '库存周转率提升20%+滞销品清单'
      },
      {
        name: '竞品对标分析',
        measure: '竞品对比卡工具化',
        action:
          '1. 设计《五维竞品对比卡》（功能/价格/服务/客户评价/优劣势）\u000b2. 销售团队每月提交竞品动态\u000b3. 季度更新竞品库',
        charge: '市场情报专员',
        result: '竞品数据库+动态监测看板'
      }
    ]
  },
  {
    name: '数据管理完善',
    count: 4,
    list: [
      {
        name: '数据采集规范',
        measure: '销售数据录入SOP制定',
        action:
          '1. 明确订单/客户/服务数据录入字段标准\u000b2. 设置数据校验规则（如手机号格式）\u000b3. 每日抽检数据完整性',
        charge: '数据专员',
        result: '数据错误率下降至5%以下'
      },
      {
        name: '数据清洗机制',
        measure: '历史数据治理专项',
        action: '1. 筛选近3年有效销售数据\u000b2. 清理重复/无效客户记录\u000b3. 建立数据修正操作指南',
        charge: 'IT经理',
        result: '清洗后标准数据库+数据字典'
      },
      {
        name: '分析模型落地',
        measure: '销售预测模型构建',
        action: '1. 培训线性回归基础方法\u000b2. 输入历史销量/市场活动/季节因素\u000b3. 输出月度销量预测波动区间',
        charge: '数据分析师',
        result: '销售预测准确率提升至80%+'
      },
      {
        name: '数据安全管控',
        measure: '客户信息分级保护制度',
        action: '1. 划分数据密级（公开/内部/机密）\u000b2. 设置CRM系统访问权限\u000b3. 签订员工保密协议',
        charge: '法务经理',
        result: '数据安全管理制度+权限分配表'
      },
      {
        name: '数据驱动决策',
        measure: '经营分析会数据看板',
        action:
          '1. 开发核心指标仪表盘（成交率/客单价/复购率）\u000b2. 管理层月度数据解读培训\u000b3. 制定问题溯源改进流程',
        charge: '总经理办公室',
        result: '数据决策案例库+改进闭环记录'
      }
    ]
  },
  {
    name: '考核机制完善',
    count: 4,
    list: [
      {
        name: '目标分解科学化',
        measure: 'KPI分层设计',
        action:
          '1. 公司级目标→部门→个人\u000b2. 设置定量指标（销售额）与定性指标（客户满意度）\u000b3. 季度目标滚动调整机制',
        charge: 'HR总监',
        result: '三级KPI指标体系+目标责任书'
      },
      {
        name: '过程考核精细化',
        measure: '销售动作量化考核',
        action:
          '1. 设定每日客户拜访量/商机转化率等过程指标\u000b2. 开发钉钉打卡+CRM记录双验证\u000b3. 周排名公示与针对性辅导',
        charge: '销售主管',
        result: '过程达标率报表+TOP20问题清单'
      },
      {
        name: '激励机制差异化',
        measure: '奖金阶梯设计',
        action:
          '1. 基础目标保底奖金+超额分段提成\u000b2. 设置团队突破奖（大客户/新市场）\u000b3. 季度颁奖典礼与案例分享',
        charge: '财务总监',
        result: '激励方案+奖金发放分析报告'
      },
      {
        name: '末位改进闭环',
        measure: '红黄牌预警机制',
        action: '1. 连续两月未达标者黄牌警告\u000b2. 制定改进计划书+导师帮扶\u000b3. 红牌人员转岗或淘汰',
        charge: 'HRBP',
        result: '预警名单+改进成功率统计'
      },
      {
        name: '价值观融合考核',
        measure: '行为指标纳入评估',
        action: '1. 定义协作精神/客户第一等行为标准\u000b2. 同事互评+上级评价加权计分\u000b3. 行为分与晋升资格挂钩',
        charge: '企业文化经理',
        result: '行为考核评分表+价值观标杆案例'
      }
    ]
  },
  {
    name: '信息系统完善',
    count: 3,
    list: [
      {
        name: '销售流程数字化',
        measure: 'CRM系统功能扩展',
        action: '1. 配置客户跟进提醒、商机预测模块\u000b2. 打通与财务系统的订单同步\u000b3. 全员操作培训（含录单规范）',
        charge: 'IT经理',
        result: 'CRM使用覆盖率100%+操作手册'
      },
      {
        name: '数据自动化采集',
        measure: '智能表单工具部署',
        action: '1. 通过钉钉/企业微信开发客户需求调研表单\u000b2. 设置自动汇总分析功能\u000b3. 异常数据实时预警推送',
        charge: '数据分析师',
        result: '数据采集效率提升50%+异常处理台账'
      },
      {
        name: '移动办公支持',
        measure: '销售APP定制开发',
        action: '1. 集成客户档案查询、报价审批功能\u000b2. 开发签单拍照上传模块\u000b3. 上线任务打卡GPS定位验证',
        charge: '技术开发主管',
        result: '销售APP日均活跃率90%+'
      },
      {
        name: '市场情报整合',
        measure: '舆情监测系统对接',
        action:
          '1. 接入行业舆情关键词监测（竞品动态、政策变化）\u000b2. 生成日报推送管理层\u000b3. 建立信息分级响应机制',
        charge: '市场部经理',
        result: '舆情日报+危机预警案例库'
      },
      {
        name: '数据分析平民化',
        measure: 'BI可视化工具落地',
        action: '1. 培训Excel高级图表+Power BI基础\u000b2. 搭建销售业绩/客户分布动态看板\u000b3. 部门自主分析案例评比',
        charge: '数据分析部负责人',
        result: '自助分析看板+部门级分析报告'
      }
    ]
  },
  {
    name: '组织岗位优化',
    count: 3,
    list: [
      {
        name: '销售职能细分',
        measure: '新增大客户经理岗位',
        action: '1. 定义岗位职责（KA开发、关系维护）\u000b2. 选拔Top3销售转岗试点\u000b3. 制定KA专属激励政策',
        charge: 'HR总监',
        result: '岗位说明书+试点业绩对比报告'
      },
      {
        name: '后台支持强化',
        measure: '销售运营部组建',
        action: '1. 合并数据分析、流程管理职能\u000b2. 招聘专职销售运营专员\u000b3. 建立销售策略支持响应机制',
        charge: '总经理',
        result: '部门架构图+支持需求响应时效统计'
      },
      {
        name: '区域协同升级',
        measure: '战区制架构改革',
        action: '1. 按地域划分战区并任命负责人\u000b2. 赋予战区资源调配权\u000b3. 设定战区业绩对赌机制',
        charge: '销售副总裁',
        result: '战区划分地图+资源使用效率报告'
      },
      {
        name: '客户服务闭环',
        measure: '客户成功团队独立化',
        action: '1. 从销售部分离客户成功职能\u000b2. 设定续费率、NPS考核指标\u000b3. 开发客户健康度评估工具',
        charge: '客户成功总监',
        result: '独立团队考核表+客户健康度仪表盘'
      },
      {
        name: '扁平化管理',
        measure: '减少审批层级',
        action: '1. 合并销售费用报销审批层级（3级→2级）\u000b2. 下放5000元内促销决策权\u000b3. 建立紧急事项绿色通道',
        charge: '运营总监',
        result: '审批流程图+决策效率提升对比'
      }
    ]
  },
  {
    name: '管理制度完善',
    count: 2,
    list: [
      {
        name: '销售行为规范',
        measure: '销售红线制度制定',
        action: '1. 明确禁止行为（虚假承诺、恶意抢单）\u000b2. 设计举报与核查流程\u000b3. 全员签署承诺书并公示',
        charge: '法务经理',
        result: '红线制度文档+违规处理案例库'
      },
      {
        name: '客户归属透明化',
        measure: '商机报备与保护机制',
        action:
          '1. 规定CRM系统商机报备时限（首次接触24小时内）\u000b2. 设定保护期（30天）\u000b3. 争议由跨部门仲裁委员会裁决',
        charge: '销售运营经理',
        result: '商机报备率95%+仲裁案例集'
      },
      {
        name: '费用管控精细化',
        measure: '销售费用包干制推行',
        action: '1. 按区域/客户类型设定费用比例\u000b2. 开发费用预警系统（超支自动锁单）\u000b3. 季度费用使用效率分析',
        charge: '财务总监',
        result: '费用管控SOP+节费标杆案例'
      },
      {
        name: '客户投诉闭环',
        measure: '24小时响应机制',
        action: '1. 制定投诉分级标准（普通/紧急）\u000b2. 要求首响≤2小时、解决≤48小时\u000b3. 每月公示投诉解决满意度',
        charge: '客服主管',
        result: '投诉处理流程图+满意度提升报告'
      },
      {
        name: '知识沉淀制度化',
        measure: '销售案例库管理规范',
        action: '1. 规定优秀案例提交标准（含签约故事、策略）\u000b2. 设置案例积分奖励\u000b3. 季度更新内部战例集',
        charge: '培训经理',
        result: '案例库+积分兑换排行榜'
      }
    ]
  },
  {
    name: '人员能力提升',
    count: 2,
    list: [
      {
        name: '销售基本功强化',
        measure: '标准化销售话术训练营',
        action: '1. 编写产品FABE话术模板\u000b2. 分场景录制示范视频（电话/面谈）\u000b3. 通关考核与现场纠偏',
        charge: '销售培训师',
        result: '话术手册+通关率100%'
      },
      {
        name: '谈判能力升级',
        measure: '高阶谈判技巧工作坊',
        action: '1. 培训价格博弈、异议处理话术\u000b2. 模拟客户砍价实战演练\u000b3. 签订“谈判策略承诺书”',
        charge: '销售总监',
        result: '谈判案例集+签约周期缩短20%'
      },
      {
        name: '客户关系深耕',
        measure: '客户信任建设专项',
        action:
          '1. 培训情感账户维护方法（节日关怀、需求预判）\u000b2. 实施“客户周年计划”（合作满1/3/5年专属服务）\u000b3. 复盘高黏性客户案例',
        charge: '客户成功经理',
        result: '客户黏性提升方案+周年活动效果报告'
      },
      {
        name: '数据分析应用',
        measure: '销售数据解读赋能课',
        action: '1. 培训基础数据指标（转化率、客单价）\u000b2. 实战：从数据图表定位业务问题\u000b3. 编写个人改进计划书',
        charge: '数据分析师',
        result: '数据解读报告+改进计划落实率'
      },
      {
        name: '管理者带教能力',
        measure: '销售主管教练技术认证',
        action: '1. 培训GROW模型、反馈技巧\u000b2. 实施“一带三”团队辅导计划\u000b3. 季度评估团队业绩与人员成长',
        charge: 'HRBP',
        result: '教练认证证书+团队成长档案'
      }
    ]
  }
])

const columns = ref([
  {
    label: '序号',
    type: 'index',
    width: 60,
    align: 'center'
  },
  {
    label: '提升能力',
    prop: 'name',
    width: 140
  },
  {
    label: '举措',
    prop: 'measure',
    width: 160
  },
  {
    label: '关键行动',
    prop: 'action'
  },
  {
    label: '责任人',
    prop: 'charge',
    width: 100
  },
  {
    label: '输出成果',
    prop: 'result',
    width: 180
  }
])
</script>
<template>
  <div class="report-page">
    <div class="head-title">查看如下能力测评对应的推荐行动计划：</div>
    <div class="report-content">
      <div class="report-item" v-for="item in reportList" :key="item.id">
        <div class="item-head">
          <div class="title">{{ item.title }}</div>
          <div class="view-btn" @click="toggleViewReport(item)">{{ item.expand ? '收起' : '查看报告' }}</div>
        </div>
        <div class="report-main" v-if="item.expand">
          <div class="report-page-content">
            <div class="page-title-line">
              <span>能力提升计划（24项）</span>
            </div>
            <div class="plan-wrap">
              <div
                class="plan-item"
                :class="{ active: planIndex == index }"
                v-for="(item, index) in planList"
                :key="item.name"
                @click="planIndex = index"
              >
                <div class="name">{{ item.name }}</div>
                <div class="count">{{ item.count }}项</div>
              </div>
            </div>
            <div class="page-title-line">
              <span>能力提升计划详情（{{ planList[planIndex].name }}）</span>
            </div>
            <SimplenessTable ref="tableRef" :roundBorder="false" :columns="columns" :data="planList[planIndex].list">
            </SimplenessTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.report-page {
  .head-title {
    font-size: 16px;
    color: #888888;
    line-height: 20px;
    margin-bottom: 16px;
  }
}
.report-content {
  .report-item {
    margin-bottom: 15px;

    .item-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 26px 28px;
      background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      margin-bottom: 15px;
      .title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
      }
      .view-btn {
        font-size: 14px;
        color: #fff;
        cursor: pointer;
        line-height: 30px;
        background: #40a0ff;
        border-radius: 3px 3px 3px 3px;
        text-align: center;
        padding: 0 22px;
      }
    }
    .report-main {
      display: flex;
      flex-flow: row nowrap;
      gap: 18px;
      .report-page-content {
        flex: 1;
        background: #ffffff;
        box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
        border-radius: 8px 8px 8px 8px;
        padding: 0px 30px 20px;
        .title {
          font-weight: 500;
          font-size: 16px;
          color: #53a9f9;
          margin-bottom: 20px;
        }
        .page-title-line {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 20px;
          margin-top: 30px;
          .line {
            flex: 1;
            height: 1px;
            background: #e5e5e5;
          }
          .ai-btn {
            width: 73px;
            text-align: center;
            line-height: 30px;
            background: #e1f3ff;
            border-radius: 30px;
            font-weight: 500;
            font-size: 16px;
            color: #40a0ff;
            cursor: pointer;
          }
        }
      }
    }
  }
}
.section {
  flex: 1;
  padding: 20px;
  margin-bottom: 20px;
  &-title {
    font-weight: 600;
    font-size: 16px;
    color: #3d3d3d;
    line-height: 16px;
    margin-bottom: 12px;
  }
  &-desc {
    font-size: 14px;
    color: #666666;
    line-height: 28px;
  }
}
.border {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #c6dbf3;
}

.plan-wrap {
  display: flex;
  flex-flow: row wrap;
  gap: 20px;
  .plan-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: calc((100% - 60px) / 4);
    padding: 0 16px;
    line-height: 38px;
    font-size: 14px;
    color: #333333;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #c6dbf3;
    cursor: pointer;
    &.active {
      border-color: #40a0ff;
      color: #40a0ff;
    }
  }
}
</style>
