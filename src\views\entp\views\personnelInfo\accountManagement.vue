<template>
  <div class="page_container account_management_wrap">
    <div class="page_main_title">账号密码</div>
    <div class="page_section">
      <div class="account_management_center clearfix">
        <div class="info_display_wrap">
          <div class="info_display_item_box">
            <div class="info_display_item">
              <div class="info_display_item_label">账号</div>
              <div class="info_display_item_value">
                {{ phoneNumber }}
              </div>
            </div>
            <div class="info_display_item">
              <div class="info_display_item_label">手机</div>
              <div class="info_display_item_value">
                {{ phoneNumber }}
                <span class="modify fr" @click="showPhoneDialog = true">
                  <el-icon><EditPen /></el-icon>
                </span>
              </div>
            </div>
            <div class="info_display_item">
              <div class="info_display_item_label">邮箱</div>
              <div class="info_display_item_value">
                {{ email }}
                <span class="modify fr" @click="showEmailDialog = true">
                  <el-icon><EditPen /></el-icon>
                </span>
              </div>
            </div>
            <div class="info_display_item">
              <div class="info_display_item_label">密码</div>
              <div class="info_display_item_value">
                **********
                <span class="modify fr" @click="showPasswordDialog = true">
                  <el-icon><EditPen /></el-icon>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 修改手机号 弹窗 -->
    <el-dialog title="修改手机号" v-model="showPhoneDialog" :before-close="dialogClose">
      <el-form>
        <el-form-item class="form_item" label="新手机号：" :label-width="formLabelWidth">
          <el-input v-model="newPhoneNumber" maxlength="11"></el-input>
          <div class="tips">{{ phoneNumberTipText }}</div>
        </el-form-item>
        <el-form-item class="form_item" label="验证码：" :label-width="formLabelWidth">
          <el-input v-model="phoneCode">
            <template v-slot:append>
              <el-button :disabled="phoneVerifyBtnFlag" @click="getValidateCode('sms')">
                <span v-show="phoneCountDownNum">{{ phoneCountDownNum }}s</span>
                {{ phoneVerifyBtnText }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button class="page_clear_btn" @click="showPhoneDialog = false">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="confirmPhoneNum">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 修改邮箱弹窗 -->
    <el-dialog title="修改邮箱" v-model="showEmailDialog" :before-close="dialogClose">
      <el-form>
        <el-form-item class="form_item" label="新邮箱：" :label-width="formLabelWidth">
          <el-input v-model="newEmail"></el-input>
          <div class="tips">{{ emailTipText }}</div>
        </el-form-item>
        <el-form-item class="form_item" label="验证码：" :label-width="formLabelWidth">
          <el-input v-model="emailCode">
            <template v-slot:append>
              <el-button :disabled="emailVerifyBtnFlag" @click="getValidateCode('email')">
                <span v-show="emailCountDownNum">{{ emailCountDownNum }}s</span>
                {{ emailVerifyBtnText }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button class="page_clear_btn" @click="showEmailDialog = false">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="confirmEmail">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 修改密码 弹窗 -->
    <el-dialog title="修改密码" v-model="showPasswordDialog" :before-close="dialogClose">
      <el-form>
        <el-form-item class="form_item" label="原密码：" :label-width="formLabelWidth">
          <el-input type="password" v-model="originUserPasswd" placeholder="请输入原密码"></el-input>
        </el-form-item>
        <el-form-item class="form_item" label="新密码：" :label-width="formLabelWidth">
          <el-input
            type="password"
            v-model="newPassword"
            @blur="verifyPsd(newPassword)"
            placeholder="数字、字母、符号两种及以上的组合,6-20个字符"
          ></el-input>
        </el-form-item>
        <el-form-item class="form_item" label="新密码确认：" :label-width="formLabelWidth">
          <el-input
            type="password"
            v-model="newPasswordConfirm"
            @blur="verifyPsd(newPasswordConfirm)"
            placeholder="数字、字母、符号两种及以上的组合,6-20个字符"
          ></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button class="page_clear_btn" @click="showPasswordDialog = false">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="confirmPassword">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { editAccountInfo, getUserBasicInfo, getCode, checkEmail, checkPhoneNumber, logout } from '../../request/api'
import { useUserStore } from '@/stores/modules/user'
import { EditPen } from '@element-plus/icons-vue'

const userStore = useUserStore()
const userId = computed(() => userStore.userInfo.userId)
const companyId = computed(() => userStore.userInfo.companyId)

const phoneNumber = ref('')
const email = ref('')
const formLabelWidth = '120px'
const showPhoneDialog = ref(false)
const newPhoneNumber = ref('')
const phoneCode = ref('')
const phoneNumberTipText = ref('')
const phoneVerifyBtnFlag = ref(false)
const phoneCountDownNum = ref(0)
const phoneVerifyBtnText = ref('获取验证码')
let phoneSetInterval = null
const showEmailDialog = ref(false)
const newEmail = ref('')
const emailCode = ref('')
const emailTipText = ref('')
const emailVerifyBtnFlag = ref(false)
const emailCountDownNum = ref(0)
const emailVerifyBtnText = ref('获取验证码')
let emailSetInterval = null
const showPasswordDialog = ref(false)
const originUserPasswd = ref('')
const newPassword = ref('')
const newPasswordConfirm = ref('')
const needTips = ref(false)

function verifyPsd(value) {
  if (value.length < 6) {
    ElMessage.warning('密码长度最小为6')
  }
}
async function verifyPhoneNum(value) {
  value = newPhoneNumber.value
  return await checkPhoneNumber({ phoneNumber: value, userId: userId.value }).then(res => res)
}
function confirmPhoneNum() {
  if (!phoneCode.value) {
    ElMessage.warning('请输入验证码！')
    return
  }
  let params = {
    type: 'phoneNumber',
    phoneNumber: newPhoneNumber.value,
    originPhoneNumber: phoneNumber.value,
    code: phoneCode.value
  }
  editAccountInfo(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      getAccountInfo()
      showPhoneDialog.value = false
    } else {
      ElMessage.error(res.msg)
      phoneVerifyBtnFlag.value = true
    }
  })
}
async function verifyEmail(value) {
  value = newEmail.value
  return await checkEmail({ email: value, userId: userId.value }).then(res => res)
}
function confirmEmail() {
  if (!emailCode.value) {
    ElMessage.warning('请输入验证码！')
    return
  }
  let params = {
    type: 'email',
    email: newEmail.value,
    code: emailCode.value
  }
  editAccountInfo(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      getAccountInfo()
      showEmailDialog.value = false
    } else {
      ElMessage.error(res.msg)
    }
  })
}
function confirmPassword() {
  if (originUserPasswd.value.length < 1) {
    ElMessage.warning('请输入原密码！')
    return
  }
  if (newPassword.value.length < 6) {
    ElMessage.warning('密码长度最小为6位！')
    return
  }
  if (newPasswordConfirm.value !== newPassword.value) {
    ElMessage.warning('两次输入密码不一致！')
    return
  }
  let params = {
    type: 'userPasswd',
    originUserPasswd: originUserPasswd.value,
    userPasswd: newPassword.value
  }
  editAccountInfo(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      getAccountInfo()
      showPasswordDialog.value = false
      if (needTips.value) {
        loginOut()
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}
function loginOut() {
  logout().then(res => {
    if (res.code == 200) {
      window.sessionStorage.clear('token')
      ElMessage({
        message: '即将跳转到登录页面，请使用新密码重新登录！',
        type: 'warning'
      })
      setTimeout(() => {
        top.window.location.reload()
      }, 3000)
    }
  })
}
function dialogClose(done) {
  originUserPasswd.value = ''
  newPassword.value = ''
  newPasswordConfirm.value = ''
  emailCode.value = ''
  phoneCode.value = ''
  if (!phoneVerifyBtnFlag.value) {
    phoneNumberTipText.value = ''
  }
  if (!emailVerifyBtnFlag.value) {
    emailTipText.value = ''
  }
  initVerifyStatus()
  done()
}
function getValidateCode(type) {
  let receive = type == 'email' ? newEmail.value : newPhoneNumber.value
  if (type == 'email') {
    if (!newEmail.value) {
      ElMessage.warning('请输入新邮箱地址')
      return
    } else {
      verifyEmail(newEmail.value).then(res => {
        if (res.code == '200') {
          emailTipText.value = ''
          emailCountDownNum.value = 60
          let params = { receive, type }
          getCode(params).then(res => {
            if (res.code == '200') {
              ElMessage.success(res.msg)
              emailVerifyBtnText.value = '后可再次获取'
              emailVerifyBtnFlag.value = true
              countDownNumFun(type)
            } else {
              ElMessage.error(res.msg)
            }
          })
        } else {
          emailTipText.value = res.msg
        }
      })
    }
  } else {
    if (!newPhoneNumber.value) {
      ElMessage.warning('请输入新手机号')
      return
    } else {
      verifyPhoneNum(newPhoneNumber.value).then(res => {
        if (res.code == '200') {
          phoneNumberTipText.value = ''
          phoneCountDownNum.value = 60
          let params = { receive, type }
          getCode(params).then(res => {
            if (res.code == '200') {
              ElMessage.success(res.msg)
              phoneVerifyBtnText.value = '后可再次获取'
              phoneVerifyBtnFlag.value = true
              countDownNumFun(type)
            } else {
              ElMessage.error(res.msg)
            }
          })
        } else {
          phoneNumberTipText.value = res.msg
        }
      })
    }
  }
}
function getAccountInfo() {
  getUserBasicInfo().then(res => {
    if (res.code == '200') {
      phoneNumber.value = res.data.phoneNumber
      email.value = res.data.email
      needTips.value = res.data.needTips
    }
  })
}
function countDownNumFun(type) {
  if (type == 'email') {
    emailSetInterval = setInterval(() => {
      emailCountDownNum.value--
      if (emailCountDownNum.value == 0) {
        clearInterval(emailSetInterval)
        emailSetInterval = null
        emailVerifyBtnText.value = '获取验证码'
        emailVerifyBtnFlag.value = false
      }
    }, 1000)
  } else {
    phoneSetInterval = setInterval(() => {
      phoneCountDownNum.value--
      if (phoneCountDownNum.value == 0) {
        clearInterval(phoneSetInterval)
        phoneSetInterval = null
        phoneVerifyBtnText.value = '获取验证码'
        phoneVerifyBtnFlag.value = false
      }
    }, 1000)
  }
}
function initVerifyStatus() {
  // 预留：如需清理定时器等
}
onMounted(() => {
  getAccountInfo()
})
</script>

<style scoped lang="scss">
.account_management_center {
  padding-left: 16px;
  .info_display_item {
    margin-bottom: 20px;
    .info_display_item_value {
      width: 280px;
      padding: 0 16px;
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      .modify {
        font-size: 20px;
        color: #0099ff;
        cursor: pointer;
      }
    }
  }
}
:deep(.form_item) {
  position: relative;
  width: 480px;
}
.tips {
  position: absolute;
  color: red;
  font-size: 12px;
  line-height: 20px;
}
.code_btn {
  width: 100px;
  height: 30px;
  line-height: 30px;
}
:deep(.el-input-group__append) {
  width: 140px;
  padding: 0;
  text-align: center;
  .el-button {
    width: 140px;
    height: 30px;
    padding: 0;
  }
}
</style>
