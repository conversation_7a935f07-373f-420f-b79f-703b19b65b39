<script setup>
import MarkdownIt from 'markdown-it'
import markdownItContainer from 'markdown-it-container'
import * as echarts from 'echarts'
import service from '@/api/request.js'
import { historicalDetail } from '@/api/modules/project'

defineOptions({ name: 'targetSpotDiagnosisProject' })
const type = ref([
  {
    id: 1,
    name: '整体能力诊断'
  },
  {
    id: 2,
    name: '专项能力诊断'
  },
  {
    id: 3,
    name: '组织赋能诊断'
  },
  {
    id: 4,
    name: '人岗赋能诊断'
  },
  {
    id: 5,
    name: '数字化赋能诊断'
  }
])
const classify = ref([
  {
    id: 1,
    name: '全部'
  },
  {
    id: 2,
    name: '未开始'
  },
  {
    id: 3,
    name: '进行中'
  },
  {
    id: 3,
    name: '已结束'
  }
])
const list = ref([
  {
    id: 1,
    type: '整体能力诊断',
    name: '供应链整体能力诊断',
    time: '2022/05/22~2022/06/22',
    status: '未开始',
    user: '王伟',
    phone: '13856063220',
    module: 12,
    component: 126,
    dna: 1221,
    progress: 30,
    step: [
      {
        name: '诊断管理',
        button: [
          {
            name: '配置'
          },
          {
            name: '删除'
          }
        ]
      },
      {
        name: '参与诊断',
        button: []
      },
      {
        name: '诊断完成',
        button: []
      }
    ]
  },
  {
    id: 1,
    type: '整体能力诊断',
    name: '供应链整体能力诊断',
    time: '2022/05/22~2022/06/22',
    status: '进行中',
    user: '王伟',
    phone: '13856063220',
    module: 12,
    component: 126,
    dna: 1221,
    progress: 70,
    step: [
      {
        name: '诊断管理',
        button: [
          {
            name: '配置'
          }
        ]
      },
      {
        name: '参与诊断',
        button: [
          {
            name: '开始诊断',
            event: () => {
              start()
            }
          }
        ]
      },
      {
        name: '诊断完成',
        button: []
      }
    ]
  },
  {
    id: 1,
    type: '整体能力诊断',
    name: '供应链整体能力诊断',
    time: '2022/05/22~2022/06/22',
    status: '进行中',
    user: '王伟',
    phone: '13856063220',
    module: 12,
    component: 126,
    dna: 1221,
    progress: 80,
    step: [
      {
        name: '诊断管理',
        button: [
          {
            name: '配置'
          }
        ]
      },
      {
        name: '参与诊断',
        button: [
          {
            name: '开始诊断',
            event: () => {
              start()
            }
          }
        ]
      },
      {
        name: '诊断完成',
        button: []
      }
    ]
  },
  {
    id: 1,
    type: '整体能力诊断',
    name: '供应链整体能力诊断',
    time: '2022/05/22~2022/06/22',
    status: '已完成',
    user: '王伟',
    phone: '13856063220',
    module: 12,
    component: 126,
    dna: 1221,
    progress: 100,
    step: [
      {
        name: '诊断管理',
        button: []
      },
      {
        name: '参与诊断',
        button: []
      },
      {
        name: '诊断完成',
        button: [
          {
            name: '查看报告',
            event: () => {
              seeReport()
            }
          }
        ]
      }
    ]
  }
])
const statusColor = {
  未开始: '#ffb000',
  进行中: '#40a0ff',
  已完成: '#52c41a'
}
const dialogVisible = ref(false)
const btnEvent = item => {
  if (item.event) {
    item.event()
  }
}
const router = useRouter()
const start = () => {
  router.push('/AI/targetSpotDiagnosis/project/diagnose')
}
// demo()
const markDownText = ref('')
const seeReport = () => {
  dialogVisible.value = true
  getHistoricalDetail()
}

const loading = ref(false)
//#region 历史
const getHistoricalDetail = () => {
  loading.value = true
  historicalDetail({ conversationId: '25234155-3101-4496-a696-9edd3bd76b88' })
    .then(res => {
      isEcharts.value = true
      let text = JSON.parse(res.data[0].outputs).answer
      markDownText.value = text
    })
    .finally(() => {
      loading.value = false
    })
}
//#endregion
//#region ai报告
const markDown = new MarkdownIt({
  html: true
})
const isEcharts = ref(false)
markDown.use(markdownItContainer, 'echarts', {
  render: function (tokens, idx) {
    const m = tokens[idx].info.trim().match(/^echarts\s+(.*)$/)
    if (tokens[idx].nesting == 1) {
      const data = JSON.parse(m[1])
      if (isEcharts.value) {
        nextTick(() => {
          setTimeout(() => {
            let myChart = echarts.init(document.getElementById('echarts_content_' + data.id))
            myChart.setOption(data.option)
          }, 1000)
          // 可以对返回数据进行渲染，也可以通过数据请求获取数据信息进行渲染
        })
        return `<div id="echarts_content_${data.id}" style="width: 100%;height: 300px;display: flex;align-items:center;justify-content:center">`
      } else {
        return `<div id="echarts_content_${data.id}" style="width: 100%;height: 300px;display: flex;align-items:center;justify-content:center;color:#87CEFA;">echarts图表加载中...`
      }
    } else {
      return `</div>\n`
    }
  }
})
const anewCreate = () => {
  markDownText.value = ''
  isEcharts.value = false
  service({
    url: '/workflowQuestion/chatMessages',
    method: 'post',
    params: { conversationId: '' },
    responseType: 'stream',
    onDownloadProgress: progressEvent => {
      let contentThink = ''
      const chunk = progressEvent.event.currentTarget.responseText
      const lines = chunk.split('\n')
      for (const line of lines) {
        if (line.startsWith('data:')) {
          const data = line.slice(5).trim()
          try {
            const jsonData = JSON.parse(data)
            if (jsonData?.event == 'message') {
              const result = jsonData.answer
              if (jsonData.answer) {
                contentThink += result
                markDownText.value = contentThink
              }
            } else if (jsonData?.event == 'message_end') {
              isEcharts.value = true
              markDownText.value = contentThink
            }
          } catch (error) {
            console.error('Failed to parse JSON data:', error)
          }
        }
      }
    }
  })
    .finally(() => {})
    .catch(err => {})
}
//#endregion
</script>
<template>
  <div class="project">
    <div class="top">
      <div class="title">诊断项目类型 ：</div>
      <ul>
        <li v-for="item in type" :key="item.id">{{ item.name }}</li>
      </ul>
    </div>
    <div class="list">
      <div class="page-title-line">
        如何使用该项评估结果
        <div class="btn">发起诊断项目</div>
      </div>
      <div class="classify">
        <div class="one" :class="{ active: item.id == 1 }" v-for="item in classify" :key="item.id">{{ item.name }}</div>
      </div>
      <div class="list-main">
        <div class="item" v-for="item in list">
          <div class="brief">
            <div class="type">{{ item.type }}</div>
            <div class="brief-main">
              <div class="top">
                <div class="title">{{ item.name }}</div>
                <el-tag class="tag" :color="statusColor[item.status]" size="small">{{ item.status }}</el-tag>
              </div>
              <div class="bot">
                <div class="time">{{ item.time }}</div>
                <div class="name">{{ item.user }} {{ item.phone }}</div>
              </div>
            </div>
          </div>
          <div class="num">
            <ul>
              <li>
                <div class="name">模块</div>
                <div class="text">{{ item.module }}</div>
              </li>
              <li>
                <div class="name">插件</div>
                <div class="text">{{ item.component }}</div>
              </li>
              <li>
                <div class="name">DNA</div>
                <div class="text">{{ item.dna }}</div>
              </li>
            </ul>
          </div>
          <div class="chart">
            <el-progress type="circle" :width="82" :stroke-width="15" :percentage="item.progress" />
          </div>
          <div class="flow">
            <div class="line">
              <div class="demo"></div>
            </div>
            <div class="item" v-for="step in item.step">
              <div class="title">{{ step.name }}</div>
              <div class="ico"></div>
              <div class="btn">
                <div
                  class="button button1"
                  :class="{ button2: btn.name == '删除' }"
                  v-for="btn in step.button"
                  @click="btnEvent(btn)"
                >
                  {{ btn.name }}
                </div>
              </div>
            </div>
            <!-- <div class="item">
              <div class="title">参与诊断</div>
              <div class="ico"></div>
              <div class="btn">
                <div @click="start()" class="button button1">开始诊断</div>
              </div>
            </div>
            <div class="item not">
              <div class="title">诊断完成</div>
              <div class="ico"></div>
              <div class="btn">
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" title="AI报告" width="70%">
    <div class="project-card">
      <div class="tag">整体能力诊断</div>
      <div class="info-card">
        <div class="top">
          <div class="title">供应链整体能力诊断</div>
        </div>
        <div class="bottom">
          <span class="time">2022/05/22~2022/06/22</span>
          <span class="time">王伟 13856063220</span>
        </div>
      </div>
      <div class="inject-card">
        <div class="item-tag">参与人数</div>
        <div class="number"><span>125</span>人</div>
      </div>
      <div class="module-card">
        <div class="item">
          <span class="item-tag">模块</span>
          <span class="text">12</span>
        </div>
        <div class="item">
          <span class="item-tag">组件</span>
          <span class="text">126</span>
        </div>
        <div class="item">
          <span class="item-tag">DNA</span>
          <span class="text">1221</span>
        </div>
      </div>
      <div class="btn">
        <el-button type="primary" @click="anewCreate()">重新生成报告</el-button>
      </div>
    </div>
    <div class="markDown-main">
      <div class="reset-tailwind" v-html="markDown.render(markDownText)"></div>
    </div>
  </el-dialog>
</template>
<style lang="scss" scoped>
.project {
  > .top {
    .title {
      font-size: 14px;
      font-weight: 600;
      color: #3d3d3d;
    }

    ul {
      display: flex;
      margin-top: 15px;

      li {
        width: 266px;
        height: 35px;
        border-radius: 5px 5px 5px 5px;
        font-size: 14px;
        font-weight: 500;
        line-height: 35px;
        text-align: center;
        border: 1px solid #a5c1dc;
        margin-right: 15px;
        background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
        border-radius: 5px 5px 5px 5px;
        border: 1px solid #c6dbf3;
      }
    }
  }

  .list {
    margin-top: 22px;
    .page-title-line {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      .btn {
        width: 92px;
        line-height: 30px;
        background: #40a0ff;
        border-radius: 3px 3px 3px 3px;
        text-align: center;
        color: #fff;
        font-size: 12px;
      }
    }

    > .classify {
      display: flex;
      margin-bottom: 18px;

      .one {
        width: 81px;
        height: 25px;
        background: #ffffff;
        border-radius: 141px 141px 141px 141px;
        text-align: center;
        font-size: 14px;
        color: #6c757e;
        line-height: 25px;
        margin-right: 8px;

        &.active {
          background: #40a0ff;
          color: #fff;
        }
      }
    }

    .list-main {
      > .item {
        // height: 110px;
        background: #ffffff;
        border-radius: 5px 5px 5px 5px;
        border: 1px solid #c6dbf3;
        display: flex;
        padding: 11px 0 11px 20px;
        margin-bottom: 20px;

        .brief {
          display: flex;
          align-items: center;
          border-right: 1px solid #d8d8d8;
          padding-right: 65px;

          .type {
            width: 111px;
            height: 23px;
            background: #e3f1ff;
            border-radius: 5px 5px 5px 5px;
            color: #40a0ff;
            text-align: center;
            line-height: 23px;
            margin-right: 36px;
          }

          .brief-main {
            .top {
              display: flex;
              align-items: center;
              margin-bottom: 20px;

              .title {
                font-size: 18px;
                font-weight: 600;
                margin-right: 10px;
              }

              .tag {
                color: #fff;
              }
            }

            .bot {
              display: flex;
              font-size: 14px;
              color: #acacac;

              .time {
                margin-right: 44px;
              }
            }
          }
        }

        .num {
          padding: 0 45px;
          border-right: 1px solid #d8d8d8;

          ul {
            li {
              display: flex;
              color: #40a0ff;
              align-items: center;
              margin-bottom: 6px;

              .name {
                width: 42px;
                height: 19px;
                background: #e3f1ff;
                border-radius: 46px 46px 46px 46px;
                font-size: 12px;
                line-height: 19px;
                text-align: center;
                margin-right: 10px;
              }

              .text {
                font-size: 16px;
              }
            }
          }
        }

        .chart {
          padding: 0 33px;
          border-right: 1px solid #d8d8d8;
          display: flex;
          align-items: center;
        }

        .flow {
          flex-grow: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 27px;
          position: relative;

          .line {
            padding-right: 50px;
            width: 100%;
            position: absolute;
            top: 50%;
            margin-top: -4px;
            z-index: 5;

            .demo {
              width: 100%;
              height: 8px;
              background: #ededed;
              border-radius: 52px 52px 52px 52px;
            }
          }

          > .item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 10;

            .title {
              font-size: 14px;
              color: #3d3d3d;
            }

            .ico {
              width: 20px;
              height: 20px;
              background-color: #40a0ff;
              border: 6px solid #c6e3ff;
              border-radius: 50%;
              margin: 10px 0 15px;
            }

            .btn {
              width: 100%;
              display: flex;
              flex-flow: row nowrap;
              align-items: center;
              justify-content: center;
              height: 18px;
              gap: 10px;

              .button {
                width: 55px;
                height: 18px;
                font-size: 12px;
                line-height: 18px;
                text-align: center;
                cursor: pointer;
              }

              .button1 {
                background: #40a0ff;
                border-radius: 3px 3px 3px 3px;
                color: #ffffff;
              }

              .button2 {
                background: #ffffff;
                border-radius: 3px 3px 3px 3px;
                border: 1px solid #40a0ff;
                color: #40a0ff;
                margin-left: 5px;
              }
            }
          }

          .not {
            .title {
              color: #898989;
            }

            .ico {
              background-color: #acacac;
              border: 6px solid #e7e7e7;
            }
          }
        }
      }
    }
  }
}
.project-card {
  @include flex-center(row, space-between, stretch);
  width: 100%;
  height: 110px;
  margin-bottom: 24px;
  padding: 10px 35px 10px 22px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #c6dbf3;
  .tag {
    height: 23px;
    left: 23px;
    padding: 0 14px;
    margin-top: 32px;
    border-radius: 5px;
    font-size: 14px;
    color: #40a0ff;
    background-color: #e3f1ff;
  }
  .info-card {
    padding-top: 14px;
    padding-right: 56px;
    // border-right: 1px solid #d8d8d8;
    .top {
      @include flex-center(row, flex-start, center);
      margin-bottom: 15px;
      .title {
        margin-right: 13px;
        font-size: 18px;
        color: #3d3d3d;
        font-weight: 500;
      }
      .progress-tag {
        padding: 3px 8px;
        font-size: 12px;
        line-height: 12px;
        color: #ffffff;
        background: #40a0ff;
        border-radius: 3px;
      }
    }
    .bottom {
      .time {
        margin-right: 44px;
        font-size: 14px;
        line-height: 12px;
        color: #acacac;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  .inject-card {
    padding: 0 100px;
    border-right: 1px solid #d8d8d8;
    border-left: 1px solid #d8d8d8;
    .item-tag {
      width: 80px;
      text-align: center;
      margin-bottom: 17px;
      line-height: 20px;
      font-size: 14px;
      color: #40a0ff;
      border-radius: 42px;
      background-color: #cbe4fd;
    }
    .number {
      text-align: center;
      font-size: 14px;
      color: #3d3d3d;
      span {
        font-size: 30px;
        font-weight: 500;
        color: #40a0ff;
      }
    }
  }
  .module-card {
    padding-top: 4px;
    .item {
      margin-bottom: 5px;
      &:last-child {
        margin-bottom: 0;
      }
      .item-tag {
        padding: 1px 9px;
        margin-right: 11px;
        font-size: 12px;
        line-height: 21px;
        border-radius: 46px;
        color: #40a0ff;
        background-color: #e3f1ff;
      }
      .text {
        color: #40a0ff;
        font-size: 16px;
      }
    }
  }
  .report-type {
    @include flex-center(row, center, center);
    gap: 17px;
    .type-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 0 0 90px;
      text-align: center;
      color: #40a0ff;
      background: #e3f1ff;
      border-radius: 8px 8px 8px 8px;
      padding: 8px 0;
      cursor: pointer;
      &.active {
        background: #40a0ff;
        color: #fff;
      }
      .icon {
        margin-bottom: 4px;
      }
      .name {
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
  .operate-card {
    @include flex-center(column, center, center);
    font-size: 14px;
    color: #40a0ff;
  }
  .btn {
    display: flex;
    align-items: center;
    border-left: 1px solid #d8d8d8;
    padding: 20px;
  }
}
.markDown-main {
  height: 500px;
  overflow-y: auto;
}
:deep(.reset-tailwind) {
  /* 基础表格样式 */
  table {
    width: 100%;
    border-collapse: collapse; /* 边框合并 */
    margin: 1rem 0;
    font-family: 'Segoe UI', sans-serif;
  }

  /* 表头样式 */
  th {
    background-color: #f5f7fa !important;
    border: 1px solid #e9ecef;
    padding: 12px;
    text-align: left; /* 配合 Markdown 对齐使用 */
    font-weight: 600;
  }

  /* 表身样式 */
  td {
    border: 1px solid #e9ecef;
    padding: 12px;
    vertical-align: top; /* 垂直对齐 */
  }

  /* 斑马线效果 */
  tbody tr:nth-child(even) {
    background-color: #f9fafb;
  }

  /* 悬停效果 */
  tbody tr:hover {
    background-color: #f0f3f5;
    cursor: pointer;
  }

  // /* 响应式适配 */
  // @media (max-width: 600px) {
  //   table {
  //     font-size: 0.9em;
  //   }
  // }
}
.reset-tailwind,
.reset-tailwind * {
  all: revert;
}
</style>
