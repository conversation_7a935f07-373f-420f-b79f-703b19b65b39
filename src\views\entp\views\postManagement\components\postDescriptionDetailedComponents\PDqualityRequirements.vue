<template>
  <div class="quality_requirements_main">
    <div class="page_second_title">任职资格</div>
    <div class="quality_requirements_content">
      <div class="quality_requirements_item" v-if="qualityData">{{ qualityData }}</div>
      <div class="quality_requirements_item" v-else>暂无数据</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { qualityRequirements } from '../../../../request/api'

const props = defineProps({
  jobCode: String
})

const qualityData = ref('')

async function jobInfoByCodeFun() {
  if (!props.jobCode) return

  const res = await qualityRequirements({
    jobCode: props.jobCode
  })

  if (res.code == 200) {
    qualityData.value = res.data
  }
}

onMounted(jobInfoByCodeFun)
watch(() => props.jobCode, jobInfoByCodeFun)
</script>

<style scoped lang="scss">
.page_second_title {
  margin: 10px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
}
.quality_requirements_content {
  padding: 16px;
  .quality_requirements_item {
    line-height: 1.5;
  }
}
</style>
