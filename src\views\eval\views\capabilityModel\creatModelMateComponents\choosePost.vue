<template>
    <div class="choose_post_wrap">
        <div class="choose_post_main flex_row_betweens marginT_30">
            <div class="main_left">
                <p class="page_second_title">选择模型适配的岗位</p>
                <div class="flex_row_start marginT_20">
                    <div class="main_left_tree">
                        <p class="choose_post_title">族群</p>
                        <div class="tree_main">
                            <treeCompCheckbox
                                :treeData="jobClassList"
                                @node-click-callback="curChooseData"
                            ></treeCompCheckbox>
                        </div>
                    </div>
                    <div class="main_left_choose">
                        <div class="choose_post_title">
                            <span>职位</span>
                            <span>{{ jobList.length }}职位</span>
                            <span
                                class="relative pointer"
                                @click="choosePostAll"
                            >
                                <span>全选</span>
                                <i
                                    :class="{ active: !chooseJobAllStatus }"
                                    class="el-icon-check icon_check_all"
                                ></i>
                            </span>
                        </div>
                        <ul class="second_level_post_wrap">
                            <li
                                v-for="(item, index) in jobList"
                                @click="chooseSecondLevelPost(item, index)"
                                class="flex_row_betweens"
                                :class="{ active: item.isChoose }"
                            >
                                <span>{{ item.job_name }}</span>
                                <span
                                    v-if="item.isChoose"
                                    class="el-icon-check"
                                ></span>
                                <span v-else class="icon_check"></span>
                            </li>
                            <div class="no_data_row" v-if="jobList.length == 0">
                                暂无数据
                            </div>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="main_right">
                <p class="page_second_title">已选职位</p>
                <div class="marginT_20 select_right">
                    <p class="choose_post_title"></p>
                    <div class="choose_post_title">
                        <span>已选职位</span>
                        <span>{{ checkedJobList.length }}职位</span>
                    </div>
                    <ul class="second_level_post_wrap">
                        <li
                            v-for="(item, index) in checkedJobList"
                            class="flex_row_betweens hover_style"
                        >
                            <span>{{ item.job_name }}</span>
                            <span
                                class="el_del_bg el-icon-minus"
                                @click="removePost(item.job_code, index)"
                            ></span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="main_calc flex_row_betweens">
                <div>
                <p class="page_second_title">预估岗位数</p>
                <div class="marginT_20 marginL_16">
                    <b class="fs21 color_base">{{postCount}}</b>个
                </div>
                </div>
                <div>
                <p class="page_second_title">预估人数</p>
                <div class="marginT_20 marginL_16">
                    <b class="fs21 color_base">{{personCount}}</b>个
                </div>
                </div>
            </div>
        </div>
        <div class="align_center marginT_20">
            <el-button class="page_confirm_btn" type="primary" @click="next()"
                >下一步</el-button
            >
        </div>
    </div>
</template>

<script>
    import { getFilter, getActivePost } from "../../../request/api";
    import treeCompCheckbox from "@/components/talent/treeComps/treeCompCheckbox";

    export default {
        name: "choosePost",
        components: {
            treeCompCheckbox,
        },
        props: ["modelId"],
        data() {
            return {
                personCount:0,
                postCount:0,
                checkedPostList:window.sessionStorage.checkedPostList
                    ? JSON.parse(window.sessionStorage.checkedPostList)
                    : [],
                jobClassList: [],
                jobList: [],
                checkedJobClassList: [],
                checkedJobList: window.sessionStorage.checkedJobList
                    ? JSON.parse(window.sessionStorage.checkedJobList)
                    : [],
                checkedJobCodeList: [],
                chooseJobAllStatus: true,
            };
        },
        created() {
            if (this.checkedJobList.length > 0) {
                this.checkedJobList.forEach((item) => {
                    this.checkedJobCodeList.push(item.job_code);
                    this.personCount += item.userNum;
                    this.postCount += item.postNum;
                });
            }
        },
        mounted() {
            this.getFilterFun();
        },
        methods: {
            choosePostAll() {
                let list = this.jobList;
                if (list.length == 0) {
                    return;
                }
                let chooseStatusArr = [];
                for (let i = 0, len = list.length; i < len; i++) {
                    let chooseStatus = list[i].isChoose;
                    chooseStatusArr.push(chooseStatus);
                }

                // 列表中是否有选中的项
                // 有：全部取消选中
                // 无：全部选中
                this.chooseJobAllStatus = chooseStatusArr.includes(true);
                this.jobList.forEach((item, index) => {
                    this.$set(
                        this.jobList[index],
                        "isChoose",
                        !this.chooseJobAllStatus
                    );
                });
                // 取消选中
                this.jobList.forEach((item, index) => {
                    if (this.chooseJobAllStatus) {
                        let clearCode = item.job_code;
                        this.checkedJobList.some((list, index) => {
                            if (list.job_code == clearCode) {
                                this.checkedJobList.splice(index, 1);
                                this.checkedJobCodeList.splice(index, 1);
                            }
                        });
                    } else {
                        this.$set(this.jobList[index], "isChoose", true);
                        this.checkedJobList.push(item);
                        this.checkedJobCodeList.push(item.job_code);
                    }
                });
                this.calcCheckedJobData();
            },
            calcCheckedJobData(){
                this.postCount = 0;
                this.personCount = 0;
                this.checkedPostList = [];
                let arr = [];
                this.checkedJobList.forEach(item => {
                    this.postCount += item.postNum;
                    this.personCount += item.userNum;
                    arr = arr.concat(item.postInfo);
                })
                this.checkedPostList = arr;
            },
            curChooseData(checkedList) {
                this.checkedJobClassList = checkedList;
                if (this.checkedJobClassList.length == 0) {
                    this.jobList = [];
                    return;
                }
                this.getActivePostFun();
            },
            chooseSecondLevelPost(row, index) {
                if (!this.jobList[index].isChoose) {
                    this.$set(this.jobList[index], "isChoose", true);
                    this.checkedJobList.push(row);
                    this.checkedJobCodeList.push(row.job_code);
                } else {
                    this.$set(this.jobList[index], "isChoose", false);
                    let clearCode = row.job_code;
                    this.checkedJobList.some((item, index) => {
                        if (item.job_code == clearCode) {
                            this.checkedJobList.splice(index, 1);
                            this.checkedJobCodeList.splice(index, 1);
                        }
                    });
                }
                this.calcCheckedJobData();
            },

            removePost(code, index) {
                this.$confirm("确认删除吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.checkedJobList.splice(index, 1);
                        this.checkedJobCodeList.splice(index, 1);
                        this.jobList.some((item, i) => {
                            if (item.job_code == code) {
                                this.$set(this.jobList[i], "isChoose", false);
                            }
                        });
                        this.calcCheckedJobData();
                    })
                    .catch(() => {});
            },
            next() {
                if (this.checkedJobList.length == 0) {
                    this.$msg.warning("请选择职位！");
                    return;
                }
                window.sessionStorage.checkedJobList = JSON.stringify(
                    this.checkedJobList
                );
                window.sessionStorage.checkedPostList = JSON.stringify(
                    this.checkedPostList
                );
                this.$emit("nextStep");
            },

            /*api*/
            getFilterFun() {
                getFilter().then((res) => {
                    if (res.code == 200) {
                        this.jobClassList = res.data.cmpyJobClassList.map(
                            (item) => {
                                return {
                                    code: item.code,
                                    value: item.value,
                                };
                            }
                        );
                    }
                });
            },
            getActivePostFun() {
                getActivePost({
                    jobClassCodes: this.checkedJobClassList.toString(),
                }).then((res) => {
                    if (res.code == 200) {
                        this.jobList = res.data;
                        for (let i = 0; i < this.jobList.length; i++) {
                            if (
                                this.checkedJobCodeList.includes(
                                    this.jobList[i].job_code
                                )
                            ) {
                                this.$set(this.jobList[i], "isChoose", true);
                            }
                        }
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .choose_post_wrap {
        .choose_post_title {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 8px;
            font-size: 16px;
            line-height: 28px;
            background-color: #EBF4FF;
            border-radius: 3px;
            .icon_check_all {
                display: inline-block;
                border: 2px solid #666;
                border-radius: 50%;
                width: 24px;
                height: 24px;
                font-size: 18px;
                font-weight: bold;
                color: #e5f0f9;
                margin-left: 5px;
                line-height: 22px;
                text-align: center;
                margin-right: -5px;
                &.active {
                    color: #0099ff;
                    border-color: #0099ff;
                }
            }
        }

        .main_left_title {
            height: 40px;
            line-height: 40px;
            font-weight: 700;
        }

        .choose_post_main {
            .page_section {
                // height: 400px;
                // overflow: auto;
            }

            .second_level_post_wrap {
                padding: 10px;
                height: 350px;
                overflow: auto;
                li {
                    position: relative;
                    margin: 5px 0 0 0;
                    padding: 0 5px;
                    height: 36px;
                    line-height: 36px;
                    cursor: pointer;
                    border: 1px solid #e4e4e4;

                    .icon_check {
                        position: absolute;
                        border: 1px solid #ddd;
                        border-radius: 50%;
                        right: 5px;
                        top: 5px;
                        width: 24px;
                        height: 24px;
                    }

                    .el-icon-check {
                        height: 35px;
                        font-size: 24px;
                        line-height: 35px;
                        font-weight: bold;
                    }

                    .el_del_bg {
                        position: absolute;
                        right: 5px;
                        top: 5px;
                        width: 24px;
                        height: 24px;
                        background: #ddd;
                        border-radius: 50%;
                        line-height: 24px;
                        font-size: 20px;
                        color: #fff;
                        text-align: center;
                    }

                    .el-icon-remove-outline {
                        height: 35px;
                        font-size: 24px;
                        line-height: 35px;
                        color: #ccc;
                    }
                }

                .active {
                    border: 1px solid #0099FF;
                    color: #0099FF;
                }

                .hover_style:hover {
                    background: #EBF4FF;
                }
            }

            .main_left {
                width: 50%;

                .flex_row_start {
                    .main_left_tree {
                        width: 30%;
                        height: 400px;
                        border: 1px solid #e5e5e5;

                        .tree_main {
                            padding: 10px 0;
                            height: 365px;
                            overflow-y: auto;
                        }
                    }

                    .main_left_choose {
                        // width: 296px;
                        width: 65%;
                        margin-left: 20px;
                        height: 400px;
                        overflow-y: auto;
                        border: 1px solid #e5e5e5;
                    }
                }
            }

            .main_right {
                width: 30%;

                .select_right {
                    height: 400px;
                    overflow-y: auto;
                    border: 1px solid #e5e5e5;
                }
            }
            .main_calc{
                width: 20%;
            }
        }
    }
</style>