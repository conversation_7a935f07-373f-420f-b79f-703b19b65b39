<template>
  <div class="relationship_wrap">
    <div class="level_wrap" :class="{ event_none: !isEdit }">
      <div v-for="(items, indexs) in evaltionList" :key="indexs">
        <div v-if="items.weightList && items.weightList.length !== 0">
          <div class="page_one_title">{{ items.title }}</div>
          <div class="page_second_title">
            设置评价关系权重
            <span class="level_count">(选中的评价关系比例总和需等于100)</span>
          </div>
          <div class="flex_row_start marginT_16">
            <ul class="level_wrap_ul flex_row_start" :class="{ event_none: items.disFlag }">
              <li
                v-for="(item, index) in items.weightList"
                :key="index"
                :class="{ active: item.selected }"
                @click="checkedLevel(items, indexs, item, index)"
              >
                <span>{{ item.relationTypeName }}</span>

                <el-input
                  v-model="item.weight"
                  placeholder="请输入整数"
                  :disabled="items.disFlag"
                  :class="{ border_blue: !items.disFlag }"
                  @click.stop
                >
                  <template #append>%</template>
                </el-input>
              </li>
            </ul>
            <div class="btn_wrap marginT_16" :class="{ event_none: !isEdit }">
              <el-button v-if="items.disFlag" class="page_add_btn" type="primary" @click="switchDisFlag(items, 0)"
                >编辑</el-button
              >
              <el-button v-else class="page_add_btn" type="primary" @click="switchDisFlag(items, 1)">保存</el-button>
            </div>
          </div>
          <div v-if="indexs == 3" class="select_box">
            <div class="page_second_title">KPI绩效结果的维护人</div>
            <el-select
              v-model="userName"
              filterable
              remote
              placeholder="请输入关键词"
              :remote-method="remoteMethod"
              :loading="loading"
              @change="changePerson"
              clearable
            >
              <el-option v-for="item in options" :key="item.userId" :label="item.userName" :value="item"></el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="talent_raview_btn_wrap marginT_30" v-show="evaltionList.length !== 0">
        <el-button class="page_confirm_btn" type="primary" @click="saveEnqRelationData()">确认</el-button>
      </div>
    </div>
    <div class="talent_raview_btn_wrap align_center marginT_30" v-if="isEdit">
      <el-button class="page_confirm_btn" type="primary" @click="prev">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="enqRelationNextFn">下一步</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getEnqRelationshipList, saveEnqRelation, getUserInfoByName, enqRelationNext } from '../../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const props = defineProps({
  getEnqId: {
    type: Function,
    required: true
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['prevStep', 'nextStep'])

const options = ref([])
const loading = ref(false)
const userName = ref('')
const userId = ref('')
const evaltionList = ref([])
const enqId = ref(null)

const remoteMethod = async query => {
  if (query == '') {
    options.value = []
    return
  }

  loading.value = true
  try {
    const res = await getUserInfoByName({
      userName: query,
      enqId: enqId.value
    })
    options.value = res.data
  } catch (error) {
    ElMessage.error(error.message)
  } finally {
    loading.value = false
  }
}

const switchDisFlag = (items, type) => {
  if (type == 0) {
    items.disFlag = !items.disFlag
  }
  if (type == 1) {
    saveEvalRelationFun(items)
  }
}

const checkedLevel = (items, indexs, row, index) => {
  if (!row.selected) {
    items.weightList[index].selected = true
    items.searchConfirmList.push(row.relationType)
  } else {
    items.weightList[index].selected = false
    const idx = items.searchConfirmList.findIndex(item => item == row.relationType)
    if (idx !== -1) {
      items.searchConfirmList.splice(idx, 1)
    }
  }
}

const getRelationInfoFun = async () => {
  try {
    const res = await getEnqRelationshipList({
      enqId: enqId.value
    })

    evaltionList.value = []
    const obj = res.data

    const moduleMap = {
      PN08: {
        title: '素质评价关系',
        code: 'PN08'
      },
      PN09: {
        title: '业绩评价关系',
        code: 'PN09'
      },
      PN10: {
        title: '目标与结果评价关系',
        code: 'PN10'
      },
      PN11: {
        title: 'KPI评价关系',
        code: 'PN11'
      },
      PN18: {
        title: '核心素质评价关系',
        code: 'PN18'
      }
    }

    for (const [key, value] of Object.entries(obj)) {
      if (moduleMap[key] && Object.prototype.hasOwnProperty.call(obj, key)) {
        const moduleInfo = {
          title: moduleMap[key].title,
          disFlag: true,
          enqModelCode: key,
          weightList: value,
          searchConfirmList: []
        }

        evaltionList.value.push(moduleInfo)

        const indx = evaltionList.value.length - 1
        for (const item of value) {
          if (item.weight) {
            evaltionList.value[indx].searchConfirmList.push(item.relationType)
          }
        }

        if (key == 'PN11' && value.length > 0) {
          userName.value = value[0].supplName
          userId.value = value[0].supplId
        }
      }
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const checkData = data => {
  return data.some(item => item.searchConfirmList && item.searchConfirmList.length == 0)
}

const enqRelationNextFn = async () => {
  if (evaltionList.value.length == 0) {
    emit('nextStep')
    return
  }

  if (checkData(evaltionList.value)) {
    ElMessage.warning('请填写评价关系！')
    return
  }

  try {
    const res = await enqRelationNext({
      enqId: enqId.value
    })
    if (res.code == 200) {
      emit('nextStep')
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const saveEvalRelationFun = items => {
  if (items.searchConfirmList.length == 0) {
    ElMessage.warning('请选择评价关系！')
    return
  }

  const params = items.searchConfirmList.map(item => {
    const weightItem = items.weightList.find(data => item == data.relationType)
    return {
      enqId: enqId.value,
      relationType: item,
      weight: weightItem ? weightItem.weight : 0
    }
  })

  let count = 0
  const hasZeroWeight = params.some(item => {
    if (Number(item.weight) == 0) {
      return true
    }
    count += Number(item.weight)
    return false
  })

  if (hasZeroWeight || count !== 100) {
    ElMessage.warning('评价关系权重比例输入错误！')
    return
  }

  items.disFlag = !items.disFlag
}

const changePerson = val => {
  userName.value = val.userName
  userId.value = val.userId
}

const saveEnqRelationData = async type => {
  if (checkData(evaltionList.value)) {
    ElMessage.warning('请填写评价关系！')
    return
  }

  const arr = evaltionList.value.map(item => {
    const base = {
      enqId: enqId.value,
      enqModelCode: item.enqModelCode,
      weightList: item.weightList
    }

    if (item.enqModelCode == 'PN15') {
      return {
        ...base,
        supplUserId: userName.value ? userId.value : '',
        supplUserName: userName.value || ''
      }
    }

    return base
  })

  try {
    const res = await saveEnqRelation(arr)
    if (res.code == 200) {
      ElMessage.success('保存成功！')
      await getRelationInfoFun()
      if (type) {
        emit('prevStep')
      }
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const prev = async () => {
  try {
    await ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '保存',
      cancelButtonText: '放弃修改'
    })
    await saveEnqRelationData('prevStep')
  } catch (action) {
    if (action == 'cancel') {
      ElMessage.info('已放弃修改并返回上一步')
      emit('prevStep')
    } else {
      ElMessage.info('取消返回上一步')
    }
  }
}

onMounted(() => {
  enqId.value = props.getEnqId()
  getRelationInfoFun()
})
</script>

<style scoped lang="scss">
.level_count {
  color: #f00;
}

.page_one_title {
  color: #449cff;
  font-size: 15px;
  font-weight: bold;
  padding: 10px 0;
  border-bottom: 1px solid #ddd;
  margin-bottom: 10px;
}

.relationship_wrap {
  width: 100%;

  .level_wrap {
    .level_wrap_ul {
      width: 900px;
      height: 70px;

      li {
        height: 62px;
        margin-right: 10px;
        border: 1px solid #e5e5e5;
        cursor: pointer;

        span {
          display: block;
          text-align: center;
          line-height: 30px;
          background: #ebf4ff;
        }

        .border_blue {
          .el-input__inner {
            border: 1px solid #0099ff;
          }
        }

        .el-input__inner {
          border-radius: 0;
        }

        .el-input-group__append {
          border-radius: 0;
          margin: 0;
        }

        .el-input.is-disabled {
          .el-input__inner {
            color: #212121;
            background: #fff;
          }
        }

        &.active {
          border: 1px solid #0099ff;

          span {
            background: #0099ff;
            color: #fff;
          }
        }
      }
    }
  }
}

.select_box {
  .el-select {
    width: 350px;
  }
}
</style>
