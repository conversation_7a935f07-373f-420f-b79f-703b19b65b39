<script setup>
import { Search } from '@element-plus/icons-vue'
import SectionTab from '../../components/sectionTab.vue'
import Tree from '@/components/tree/index.vue'
import Table from '../../components/table.vue'
const key = ref('')

const columns = ref([
  {
    label: '组织',
    prop: 'a'
  },
  {
    label: '能力培训',
    prop: 'b'
  },
  {
    label: '管理工具表单导入',
    prop: 'c'
  },
  {
    label: '数据管理完善',
    prop: 'd'
  },
  {
    label: '考核机制完善',
    prop: 'e'
  },
  {
    label: '数字化赋能',
    prop: 'f'
  },
  {
    label: '信息系统完善',
    prop: 'g'
  },
  {
    label: '组织岗位优化',
    prop: 'h'
  },
  {
    label: '管理制度完善',
    prop: 'i'
  },
  {
    label: '人员能力提升',
    prop: 'k'
  },
  {
    label: '总数',
    prop: 'l'
  },
  {
    label: '',
    prop: 'j',
    slot: 'jSlot',
    width: 130
  }
])
const data = ref([
  {
    a: '三级组织名称',
    b: 43,
    c: 54,
    d: 63,
    e: 74,
    f: 83,
    g: 93,
    h: 6,
    i: 60,
    k: 6,
    l: 60,
    j: 3
  }
])

const numberArea = ref([
  {
    num: '0~59'
  },
  {
    num: '60~69'
  },
  {
    num: '70~79'
  },
  {
    num: '80~89'
  },
  {
    num: '90~100'
  }
])

const columns2 = ref([
  {
    label: '提升举措',
    prop: 'a'
  },
  {
    label: '完备性',
    prop: 'b'
  },
  {
    label: '紧迫性',
    prop: 'c'
  },
  {
    label: '工作量',
    prop: 'd'
  }
])
const data2 = ref([
  {
    a: '构建AI驱动的计划策略优化引擎',
    b: '高',
    c: '高',
    d: '高'
  }
])

const columns3 = ref([
  {
    label: '组织名称',
    prop: 'a'
  },
  {
    label: '举措',
    prop: 'b'
  },
  {
    label: '关联策略',
    prop: 'c'
  },
  {
    label: '流程端到端要点核心内容',
    prop: 'd',
    width: 250
  },
  {
    label: '责任人',
    prop: 'e'
  },
  {
    label: '输出成果',
    prop: 'f'
  },
  {
    label: '优先级',
    prop: 'g'
  },
  {
    label: '截止日期',
    prop: 'h'
  },
  {
    label: '进度',
    prop: 'i'
  }
])
const data3 = ref([
  {
    a: '供应链计划部',
    b: '构建AI驱动的计划策略优化引擎',
    c: '供应链优化全流程闭环策略',
    d: '1. 收集历史销售数据及市场趋势数据，构建AI训练数据集（覆盖3年周期），开发集成机器学习算法的策略优化模型； 2. 部署模型至供应链管理系统，设置自动化策略调整机制（如季度策略更新）； 3. 培训计划员使用模型工具，确保策略执行误差率≤5%。',
    e: '张华（AI策略总监',
    f: '《AI计划策略优化模型文档》《用户操作手册》',
    g: '高',
    h: '2025.05.15',
    i: '进行中'
  }
])
</script>
<template>
  <div class="indicator_main">
    <div class="page-title-line">任务类型</div>
    <div class="chart_wrap"></div>
    <div class="tips">已选能力：<span>管理库存</span></div>

    <div class="page-title-line">任务类型分布一览</div>
    <Table :roundBorder="false" :columns="columns" :data="data" headerColor showIndex>
      <template v-slot:jSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>AI解读</el-button>
      </template>
    </Table>

    <div class="page-title-line marginT20">任务对应能力改善矩阵</div>
    <div class="chart_table_wrap justify-between">
      <div class="item_wrap">
        <div class="chart_box"></div>
      </div>
      <div class="item_wrap">
        <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor showIndex> </Table>
      </div>
    </div>

    <div class="page-title-line marginT20">关键任务一览</div>
    <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor showIndex> </Table>
  </div>
</template>
<style lang="scss" scoped>
@import '../../style/common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}

.indicator_main {
  .chart_table_wrap {
    margin: 0 -10px;
    .item_wrap {
      width: 49%;
      margin: 0 10px;
    }
  }
}
</style>
