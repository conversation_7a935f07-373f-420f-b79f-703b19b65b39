<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">沟通频率</div>
          <div class="content_item_content" id="frequency"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">沟通技巧</div>
          <div class="content_item_content" id="skill"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">沟通范围</div>
          <div class="content_item_content" id="scope"></div>
        </div>
      </div>
      <!-- <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">具体岗位</div>
                    <div class="content_item_content">
                        <div class="content_item_content">
                        <div class="post_list_wrap">
                            <div class="post_list" v-for="list in postList" :key="list.code">{{list.name}}</div>
                        </div>
                    </div>
                    </div>
                </div>
            </div> -->
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            具体职位任职资格要求-沟通
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { communication, communicationPost, queryCommunicateList, exportData } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')
const commType = ref('')
const dictCode = ref('')
const postList = ref([])
const filterData = ref([])
const page = ref(1)
const size = ref(10)

const frequency = reactive({
  addEvent: true,
  defaultChecked: true,
  data: []
})

const skill = reactive({
  addEvent: true,
  data: []
})

const scope = reactive({
  addEvent: true,
  data: []
})

const tableData = reactive({
  columns: [
    {
      label: '职位名称',
      prop: 'job_name'
    },
    {
      label: '所属组织',
      prop: 'org_name'
    },
    {
      label: '职位族群',
      prop: 'parent_job_class_name'
    },
    {
      label: '职位序列',
      prop: 'job_class_name'
    },
    {
      label: '职层',
      prop: 'job_level_name'
    },
    {
      label: '职等',
      prop: 'job_grade_name'
    },
    {
      label: '沟通频率',
      prop: 'frequencyCode'
    },
    {
      label: '沟通技巧',
      prop: 'skillCode'
    },
    {
      label: '沟通范围',
      prop: 'scopeCode'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('frequency', 'YBar', '230', '250', frequency, chartCallbackFrequency)
  echartsRenderPage('skill', 'YBar', '230', '250', skill, chartCallbackSkill)
  echartsRenderPage('scope', 'YBar', '230', '250', scope, chartCallbackScope)
}

const communicationFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await communication(params)
    if (res.code == 200) {
      const data = res.data
      frequency.data = window.$util.addPercentSign(data.frequency, 'value')
      commType.value = 'F'
      if (frequency.data.length > 0) {
        dictCode.value = frequency.data[0].code
      }
      await communicationPostFun()
      skill.data = window.$util.addPercentSign(data.skill, 'value')
      scope.data = window.$util.addPercentSign(data.scope, 'value')
      initChart()
    }
  } catch (error) {
    console.error(error)
  }
}

const chartCallbackFrequency = data => {
  commType.value = 'F'
  dictCode.value = frequency.data[data.dataIndex].code
  echartsRenderPage('skill', 'YBar', '230', '250', skill, chartCallbackSkill)
  echartsRenderPage('scope', 'YBar', '230', '250', scope, chartCallbackScope)
  communicationPostFun()
}

const chartCallbackSkill = data => {
  commType.value = 'K'
  dictCode.value = skill.data[data.dataIndex].code
  echartsRenderPage('frequency', 'YBar', '230', '250', frequency, chartCallbackFrequency)
  echartsRenderPage('scope', 'YBar', '230', '250', scope, chartCallbackScope)
  communicationPostFun()
}

const chartCallbackScope = data => {
  commType.value = 'C'
  dictCode.value = scope.data[data.dataIndex].code
  echartsRenderPage('frequency', 'YBar', '230', '250', frequency, chartCallbackFrequency)
  echartsRenderPage('skill', 'YBar', '230', '250', skill, chartCallbackSkill)
  communicationPostFun()
}

const communicationPostFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      commType: commType.value,
      dictCode: dictCode.value
    }
    const res = await communicationPost(params)
    if (res.code == 200) {
      postList.value = res.data.post
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  page.value = 1
  communicationFun()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryCommunicateList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error(error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'c'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '具体职位任职资格要求-沟通')
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  communicationFun()
  getTableData()
})
</script>

<style scoped lang="scss"></style>
