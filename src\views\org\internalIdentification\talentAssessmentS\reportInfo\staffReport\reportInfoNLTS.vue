<script setup>
import Table from '@/components/table/simplenessTable.vue'

const columns = ref([
  {
    label: '季度',
    prop: 'a'
  },
  {
    label: '核心目标',
    prop: 'b'
  },
  {
    label: '专业能力重点',
    prop: 'c'
  },
  {
    label: '管理风格重点',
    prop: 'd'
  }
])
const data = ref([
  {
    a: 'Q1',
    b: '打基础：建立系统认知与基础工具',
    c: '市场分析框架、目标分解工具、数据监控表',
    d: '跨部门沟通机制、数据收集习惯养成'
  }
])

const columns2 = ref([
  {
    label: '能力项',
    prop: 'a'
  },
  {
    label: '目标',
    prop: 'b'
  },
  {
    label: '具体措施',
    prop: 'c',
    width: 460
  }
])
const data2 = ref([
  {
    a: '市场分析与战略规划',
    b: '建立家电市场基础认知，掌握 3 个核心分析维度（政策 / 竞品 / 用户）',
    c: '1. 政策跟踪：每月整理《家电行业政策简报》（如能效补贴、以旧换新），标注对自家产品的影响（如节能冰箱销量可能增长 20%） 2. 竞品档案：建立美的、格力等 5 家对手的《新品数据库》，记录功能（如格力的分布式送风空调）、价格、渠道策略 3. 用户调研：参与 3 场线下门店用户访谈，记录高频需求（如 “妈妈群体需要母婴专属冰箱温区”）'
  }
])

const columns3 = ref([
  {
    label: '能力项',
    prop: 'a'
  },
  {
    label: '目标',
    prop: 'b'
  },
  {
    label: '具体措施',
    prop: 'c',
    width: 460
  }
])
const data3 = ref([
  {
    a: '跨部门协同（团队协作）',
    b: '每周主动发起 1 次跨部门沟通，建立协作好感度',
    c: '1. 主动破冰：Q1 内拜访市场、研发、物流部门各 2 次，带《部门需求调研表》了解他们对生产部的期望（如研发部希望 “零件采购周期缩短 10 天”） 2. 小事做起：帮销售部查 1 次 “某型号冰箱的库存位置”，帮研发部催 1 次 “新材料的检测报告”，积累协作信任 3. 记录进展：用笔记本记录每次跨部门沟通的成果（如 “帮物流部优化了空调外机包装，破损率下降 5%”）'
  }
])

onMounted(() => {})
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">能力提升计划</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        基于能力测评数据，精准识别团队成员能力与目标岗位胜任力要求的匹配程度，通过岗位调整实现
        “人岗适配”，最大化释放人才效能。
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">年度能力提升计划</div>
      <Table :roundBorder="false" :columns="columns" :data="data" headerColor showIndex> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">Q1 能力提升计划（1-3 月：打基础）</div>
      <div class="title marginB20">1、专业能力提升</div>
      <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="title marginB20">2、管理风格改善</div>
      <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor> </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../../../../style/common.scss';
@import './common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
