<template>
    <div class="report_section edu_info_wrap performance_info_main">
        <div class="clearfix">
            <div class="page_second_title marginT_30">
                个人指标完成
                <div class="change_post fr">
                    <div class="kpi_list_wrap flex_row_end">
                        <div class="post_title">指标类型</div>
                        <div
                            class="kpi_list"
                            :class="{ active: kpiType == item.kpi_cycle }"
                            @click="kpiTypeChange(item.kpi_cycle)"
                            v-for="item in kpiTypeOptions"
                            :key="item.kpi_cycle"
                        >
                            {{ item.kpi_codeName }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="event_none marginB_16">
                <div class="kpi_header flex_row_between marginT_16">
                    <div class="kpi_header_item name">指标名称</div>
                    <div class="kpi_header_item unit">单位</div>
                    <!-- <div class="kpi_header_item postName">岗位名称</div> -->
                    <div
                        class="kpi_header_item"
                        v-for="item in columns"
                        :key="item.assessmentDate"
                    >
                        <div class="kpi_header_item_title">
                            {{ item.assessmentDate }}
                        </div>
                        <div class="flex_row_around">
                            <span>实际</span>
                            <span>目标</span>
                        </div>
                    </div>
                </div>
                <div class="kpi_content_wrap">
                    <div
                        class="kpi_content_item edu_info_item flex_row_between"
                        v-for="(item, index) in enqUserKpiData"
                        :key="item.userId + index"
                    >
                        <div class="kpi_content name">{{ item.kpiName }}</div>
                        <div class="kpi_content unit">{{ item.kpiUnit }}</div>
                        <!-- <div class="kpi_content postName">{{item.postName}}</div> -->
                        <div
                            class="kpi_content"
                            v-for="(list, index) in item.enqUserKpiDate"
                            :key="index"
                        >
                            <div class="flex_row_between">
                                <el-input
                                    class="kpi_ipt"
                                    
                                    type="number"
                                    v-model="list.kpiScore"
                                ></el-input>
                                <el-input
                                    class="kpi_ipt"
                                    
                                    type="number"
                                    v-model="list.kpiGoal"
                                ></el-input>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="kpi_classify">
                <div class="kpi_list_box">
                    <div
                        class="kpi_list"
                        :class="{ active: kpiCode == list.kpi_code }"
                        v-for="list in kpiList"
                        :key="list.kpi_code"
                        @click="changeKpi(list.kpi_code)"
                    >
                        {{ list.kpi_name }}
                    </div>
                </div>
                <div class="chart_content" id="chart_dom"></div>
            </div>
        </div>
    </div>
</template>

<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        getEnqUserKpi,
        updateEnqUserKpi,
        getEnqUserPost,
        getKpiCycle,
        getUserCompreKpiReport,
        getUserCompreKpiChart,
    } from "../../../../request/api.js";
    export default {
        name: "userRKpi",
        props: ["nextBtnText", "enqId", "userId", "postCode"],
        data() {
            return {
                kpiType: null,
                kpiTypeOptions: [],
                kpiList: [],
                kpiCode: "",
                columns: [],
                enqUserKpiData: [],
                backupData: {},
                chartData: {
                    legend: [
                        //单柱形图不传
                        {
                            legendName: "部门平均",
                            legendKey: "orgKpi",
                        },
                        {
                            legendName: "同岗位平均",
                            legendKey: "postKpi",
                        },
                        {
                            legendName: "个人",
                            legendKey: "personalKpi",
                        },
                    ],
                    data: [],
                },
            };
        },
        created() {
            // this.enqId = this.$route.params.id;
            this.getKpiCycleFun();
            this.getUserCompreKpiReportFun();
        },

        watch: {
            enqUserKpiData: {
                handler: function (val) {
                    this.backupData[this.kpiType].enqUserKpiData = val;
                },
                deep: true,
            },
            columns: {
                handler: function (val) {
                    this.backupData[this.kpiType].columns = val;
                },
                deep: true,
            },
        },
        methods: {
            getEnqUserKpiFun(kpiType) {
                let params = {
                    enqId: this.enqId,
                    kpiCycle: kpiType,
                    userId: this.userId,
                    postCode: this.postCode,
                };
                this.backupData[kpiType] = {};
                getEnqUserKpi(params).then((res) => {
                    if (res.code == "200") {
                        this.backupData[kpiType].enqUserKpiData =
                            res.data.enqUserKpi;
                        this.backupData[kpiType].columns =
                            res.data.enqUserKpiColumn;

                        this.enqUserKpiData =
                            this.backupData[this.kpiType].enqUserKpiData;
                        this.columns = this.backupData[this.kpiType].columns;
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
            getUserCompreKpiReportFun() {
                getUserCompreKpiReport({
                    enqId: this.enqId,
                    userId: this.userId,
                    postCode: this.postCode,
                }).then((res) => {
                    if (res.code == "200" && res.data) {
                        console.log(res);
                        this.kpiList = res.data;
                        this.kpiCode = this.kpiList[0].kpi_code;
                        this.getUserCompreKpiChartFun();
                    }
                });
            },
            changeKpi(code) {
                this.kpiCode = code;
                this.getUserCompreKpiChartFun();
            },
            getUserCompreKpiChartFun() {
                getUserCompreKpiChart({
                    enqId: this.enqId,
                    userId: this.userId,
                    postCode: this.postCode,
                    kpiCode: this.kpiCode,
                }).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        let data = res.data;
                        data.map((item) => {
                            item["name"] = item.assessmentDate;
                        });
                        this.$set(this.chartData, "data", data);
                        echartsRenderPage(
                            "chart_dom",
                            "XBar",
                            700,
                            250,
                            this.chartData
                        );
                    }
                });
            },
            getKpiCycleFun() {
                let params = {
                    enqId: this.enqId,
                    userId: this.userId,
                    postCode: this.postCode,
                };
                getKpiCycle(params).then((res) => {
                    if (res.code == "200") {
                        this.kpiTypeOptions = res.data;
                        this.kpiTypeOptions.forEach((item) => {
                            let kpiType = item.kpi_cycle;
                            this.kpiType = this.kpiTypeOptions[0].kpi_cycle;
                            this.getEnqUserKpiFun(kpiType);
                        });
                    }
                });
            },
            kpiTypeChange(val) {
                this.kpiType = val;
                if (this.backupData[val]) {
                    console.log("不调用接口");
                    this.enqUserKpiData = this.backupData[val].enqUserKpiData;
                    this.columns = this.backupData[val].columns;
                } else {
                    this.getEnqUserKpiFun(val);
                }
            },
            submit(stepType) {
                let that = this;
                let resultArr = [];
                for (const key in this.backupData) {
                    if (this.backupData.hasOwnProperty(key)) {
                        let list = this.backupData[key].enqUserKpiData;
                        list.forEach((item) => {
                            let kpidata = item.enqUserKpiDate;
                            resultArr = resultArr.concat(kpidata);
                        });
                    }
                }
                if (this.checkData(resultArr)) {
                    this.$msg.warning("请完善数据后提交");
                    return;
                }
                let params = resultArr;
                updateEnqUserKpi(params).then((res) => {
                    if (res.code == "200") {
                        this.$msg.success(res.msg);
                        that.$emit(stepType);
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
            prevBtn() {
                let that = this;
                this.$confirm("即将离开当前页面，是否保存当前页数据?", "提示", {
                    distinguishCancelAndClose: true,
                    confirmButtonText: "保存",
                    cancelButtonText: "放弃修改",
                })
                    .then(() => {
                        this.submit("prevStep");
                    })
                    .catch((action) => {
                        this.$msg.info({
                            message:
                                action == "cancel"
                                    ? "已放弃修改并返回上一步"
                                    : "取消返回上一步",
                        });
                        action == "cancel" ? that.$emit("prevStep") : "";
                    });
            },
            nextBtn() {
                this.submit("nextStep");
            },
            checkData(data) {
                // 校验数据 是否有空值
                let arr = data;
                let len = arr.length;
                // 校验数据中有没有空值
                for (let index = 0; index < len; index++) {
                    const obj = arr[index];
                    console.log(obj);
                    if (this.$util.objHasEmpty(obj, ["kpiGoal", "kpiScore"])) {
                        // 检测到有空值跳出遍历
                        console.log("有空值");
                        return true;
                    } else {
                        console.log("没有空值");
                        // return true;
                    }
                }
            },
        },
    };
</script>

<style scoped lang="scss">
    .edu_info_wrap {
        margin-bottom: 16px;
    }
    .performance_info_main {
        pointer-events: auto;
    }

    .change_post {
        margin-left: 16px;
        .post_title {
            // display: inline-block;
            font-size: 14px;
            margin-right: 16px;
        }
        .kpi_list_wrap {
            align-items: center;
        }
        .kpi_list {
            color: #449cff;
            border: 1px solid #449cff;
            padding: 0px 5px;
            line-height: 20px;
            height: 25px;
            border-radius: 3px;
            margin: 0 8px;
            background: #fff;
            cursor: pointer;
            &.active {
                background: #449cff;
                color: #fff;
            }
        }
    }

    .edu_info_center {
        width: 60%;
    }

    .kpi_header {
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        background: #f4f4f4;
        color: #525e6c;
        font-size: 14px;
        padding: 0 16px;
        height: 56px;
        line-height: 20px;
        text-align: center;
        .kpi_header_item {
            width: 15%;
            &.name {
                text-align: left;
            }

            &.unit {
                width: 50px;
            }

            &.postName {
                width: 100px;
                text-align: left;
            }
        }

        .kpi_header_item_title {
            font-size: 14px;
            margin-bottom: 4px;
        }
    }

    .kpi_content_wrap {
        font-size: 12px;
        .kpi_content_item {
            .kpi_content {
                width: 15%;
                margin-left: 5px;
                .kpi_ipt:first-of-type {
                    margin-right: 5px;
                }

                &.unit {
                    width: 50px;
                    text-align: center;
                    // flex: 0;
                }

                &.postName {
                    width: 100px;
                }
            }
        }
    }

    .column_header_title {
        margin-bottom: 8px;
    }

    .column_ipt:first-of-type {
        margin-right: 5px;
    }
    .kpi_classify {
        padding-top: 32px;
    }
    .kpi_list_box {
        float: left;
        width: 100px;
        margin-right: 16px;
        .kpi_list {
            line-height: 32px;
            text-align: center;
            background: #f8f8f8;
            margin-bottom: 6px;
            cursor: pointer;
            &.active {
                background: #0099fd;
                color: #fff;
            }
        }
    }
    .chart_content {
        overflow: hidden;
    }
</style>
