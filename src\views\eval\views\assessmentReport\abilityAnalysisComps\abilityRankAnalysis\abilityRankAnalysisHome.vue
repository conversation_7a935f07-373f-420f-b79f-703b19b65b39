<template>
  <div class="talent_analysis_wrap bg_write">
    <div class="page_main_title">
      <div class="goback_geader" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
      能力排名分析
    </div>
    <div class="">
      <!-- <div class="page_second_title">
                测评项目
                <span class="fr">{{ evalName }}{{ reportGenTime }}</span>
            </div> -->
      <div class="page_section talent_analysis_center clearfix">
        <!-- <div class="talent_analysis_aside">
                    <tree-comp-radio
                        :treeData="treeData"
                        @clickCallback="treeClick"
                    ></tree-comp-radio>
                </div> -->
        <div class="talent_analysis_main">
          <tabs-diffent-pane :tabsData="tabsData" :isDefaultTheme="true" @tabsChange="tabsChange"></tabs-diffent-pane>
          <div class="" v-if="!this.$route.query.evalId">
            <div class="eval_wrap flex_row_wrap_start">
              <div
                class="eval_item"
                @click="changeEval(item.evalId)"
                v-for="item in projectList"
                :class="{ select: evalId == item.evalId }"
              >
                {{ item.evalName }}
              </div>
            </div>
            <coustom-pagination class="paddT_12" :total="total" @pageChange="pageChange"></coustom-pagination>
          </div>
          <component :orgCode="orgCode" :evalId="evalId" :is="tabsPaneComp[compName]"></component>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { wholeComprehensiveScore, getEvalInfo, evaluationReportList } from '../../../../request/api'
import { getOrgDeptTree } from '@/views/entp/request/api'
import tabsDiffentPane from '@/components/talent/tabsComps/tabsDiffentPane'
import TreeCompRadio from '@/components/talent/treeComps/treeCompRadio.vue'
import CoustomPagination from '@/components/talent/paginationComps/coustomPagination.vue'

const compObj = {
  synthesizeAbilityRank: resolve => require(['./synthesizeAbilityRank'], resolve),
  personalityRank: resolve => require(['./personalityRank'], resolve)
}

export default {
  name: 'synthesisScoreAnalysisHome',
  props: [],
  components: {
    tabsDiffentPane,
    TreeCompRadio,
    CoustomPagination
  },
  data() {
    return {
      current: 1,
      size: 10,
      evalId: this.$route.query.evalId,
      projectList: [],
      total: 0,
      currPage: 1,
      pageSize: 10,
      orgCode: '',
      evalName: '',
      reportGenTime: '',
      compName: '',
      tabsPaneComp: compObj,
      treeData: [],
      tabsData: [
        {
          label: '综合能力排名',
          name: 'synthesizeAbilityRank'
        },
        {
          label: '能力匹配度排名',
          name: 'personalityRank'
        }
      ]
    }
  },
  created() {
    this.compName = this.tabsData[0].name
    // this.getTreeData();
    if (!this.$route.query.evalId) {
      this.evaluationReportListFun()
    } else {
      this.getEvalInfoFun()
    }
  },
  methods: {
    tabsChange(data) {
      this.compName = data.name
    },
    getTreeData() {
      getOrgDeptTree().then(res => {
        console.log(res)
        this.treeData = res
      })
    },
    treeClick(code) {
      console.log(code)
      this.orgCode = code
    },
    getEvalInfoFun() {
      getEvalInfo({ evalId: this.evalId }).then(res => {
        console.log(res)
        this.evalName = res.evalName
        this.reportGenTime = res.reportGenTime
      })
    },
    evaluationReportListFun() {
      let params = {
        current: this.currPage,
        size: this.pageSize
      }
      evaluationReportList(params).then(res => {
        console.log(res)
        if (res.code == '200' && res.data) {
          this.projectList = res.data
          this.total = res.total
          this.evalId = this.projectList[0].evalId
          // this.getEvalInfoFun();
        }
      })
    },
    changeEval(id) {
      this.evalId = id
      // this.getEvalInfoFun();
    },
    pageChange(size, page) {
      this.pageSize = size
      this.currPage = page
      this.evaluationReportListFun()
    }
  }
}
</script>

<style scoped lang="scss">
.page_second_title {
  margin-left: 16px;
}
.talent_analysis_center {
  .talent_analysis_aside {
    width: 200px;
    float: left;
    max-height: 500px;
    margin-right: 16px;
  }
  .talent_analysis_main {
    overflow-x: hidden;
  }
}
.eval_wrap {
  .eval_item {
    background-color: #e5f7fd;
    border: 1px solid #00b0f0;
    line-height: 28px;
    margin-right: 16px;
    padding: 0 8px;
    margin-bottom: 8px;
    width: calc(25% - 16px);
    cursor: pointer;
    &.select {
      background-color: #00b0f0;
      color: #fff;
    }
  }
}
</style>
