import globals from 'globals'
import js from '@eslint/js'
import pluginJs from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'
import eslintPluginPrettier from 'eslint-plugin-prettier/recommended'
import VueEslintParser from 'vue-eslint-parser'

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// 当前文件的目录路径
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
// 读取 .eslintrc-auto-import.json文件
const eslintrcAutoImport = JSON.parse(fs.readFileSync(path.join(__dirname, '.eslintrc-auto-import.json'), 'utf-8'))

/** @type {import('eslint').Linter.Config[]} */
export default [
  { files: ['src/**/.{js,mjs,cjs,vue,jsx}'] },
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        uni: 'readonly',
        wx: 'readonly',
        getApp: 'readonly',
        getCurrentPages: 'readonly',
        ...(eslintrcAutoImport.globals || {})
      },
      parser: VueEslintParser
    }
  },
  js.configs.recommended,
  pluginJs.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  {
    rules: {
      'no-var': 'error', // 要求使用 let 或 const 而不是 var
      'no-multiple-empty-lines': ['warn', { max: 1 }], // 不允许多个空行
      'no-unexpected-multiline': 'error', // 禁止空余的多行
      'no-useless-escape': 'off', // 禁止不必要的转义字符
      // eslint-plugin-vue (https://eslint.vuejs.org/rules/)
      'vue/multi-word-component-names': 'off', // 要求组件名称始终为 “-” 链接的单词
      'vue/no-mutating-props': 'off', // 不允许组件 prop的改变
      'vue/attribute-hyphenation': 'off', // 对模板中的自定义组件强制执行属性命名样式
      indent: ['error', 2], // 缩进使用2个空格
      semi: ['error', 'never'], //语句末尾不加分号
      'no-unused-vars': 'off',
      'vue/require-v-for-key': 'off',
      'vue/require-valid-default-prop': 'off',
    }
  },
  eslintPluginPrettier,
  {
    ignores: ['node_modules/**', 'dist', 'dist-**', 'docs', 'src/static']
  }
]
