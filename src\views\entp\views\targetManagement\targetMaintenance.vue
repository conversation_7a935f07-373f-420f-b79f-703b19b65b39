<template>
  <div class="target_maintenance_wrap bg_write">
    <div class="page_main_title flex_row_betweens">
      <div class="title flex_row_start">
        <p>指标维护</p>
      </div>
      <div v-if="updataTargetSign" class="go_back_btn" @click="goBackBtn"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section">
      <div class="target_maintenance_center clearfix">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title">指标分类</div>
            <div class="btn_wrap">
              <el-button link size="mini" type="plain" class="oper_btn" @click="addTreeNode">新增</el-button>
              <el-button link size="mini" type="plain" class="oper_btn" @click="editTreeNode">修改</el-button>
              <el-button link size="mini" type="plain" class="oper_btn" @click="deleteTreeNode">删除</el-button>
            </div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio
              v-model="defaultCheckedKeys"
              :treeData="treeData"
              :canCancel="true"
              :needCheckedFirstNode="false"
              @clickCallback="clickCallback"
            ></tree-comp-radio>
          </div>
        </div>
        <div class="page_section_main page_shadow" v-if="!updataTargetSign">
          <div class="filter_bar_wrap">
            <div class="flex_row_start">
              <div class="filter_item title">筛选</div>
              <div class="filter_item">
                <el-select v-model="indicatorsDimension" placeholder="指标维度" clearable>
                  <el-option
                    v-for="item in indicatorsDimensionOption"
                    :key="item.dictCode"
                    :label="item.codeName"
                    :value="item.dictCode"
                  ></el-option>
                </el-select>
              </div>
              <div class="filter_item">
                <el-input placeholder="按指标名称查询" suffix-icon="el-icon-search" v-model="filterName"></el-input>
              </div>
              <div class="filter_item">
                <el-button class="page_add_btn" type="primary" @click="keyWordSearch">查询</el-button>
              </div>
            </div>
            <div class="filter_item">
              <el-button class="page_add_btn" type="primary" @click="createTargetBtn">新增</el-button>
            </div>
          </div>
          <table-component
            :tableData="tableData"
            :needIndex="needIndex"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          >
            <template v-slot:oper>
              <el-table-column label="操作" width="100">
                <template v-slot="scope">
                  <el-button
                    @click.prevent="tableEdit(scope.$index, tableData.data)"
                    link
                    :icon="EditPen"
                    class="icon_edit"
                  ></el-button>
                  <el-button
                    class="color_danger icon_del"
                    @click.prevent="tableDeleteRow(scope.$index, tableData.data)"
                    link
                    :icon="DeleteFilled"
                  ></el-button>
                </template>
              </el-table-column>
            </template>
          </table-component>
        </div>
        <div class="page_section_main page_shadow" v-if="updataTargetSign">
          <div class="updata_target_wrap">
            <div class="line_wrap flex_row_betweens">
              <span>指标编码：</span>
              <div>
                <el-input v-model="kpiCode" disabled></el-input>
              </div>
            </div>
            <div class="line_wrap flex_row_betweens">
              <span><i class="required_icon">&#42;</i> 指标名称：</span>
              <div>
                <el-input v-model="kpiName"></el-input>
              </div>
            </div>
            <div class="line_wrap flex_row_betweens">
              <span>指标维度：</span>
              <div>
                <el-select v-model="kpiType" placeholder="请选择" clearable>
                  <el-option
                    v-for="opt in indicatorsDimensionOption"
                    :key="opt.codeName"
                    :label="opt.codeName"
                    :value="opt.dictCode"
                  ></el-option>
                </el-select>
              </div>
            </div>

            <div class="line_wrap flex_row_betweens">
              <span>期间：</span>
              <div>
                <el-select v-model="kpiCycle" placeholder="请选择" @change="kpiCycleChange" clearable>
                  <el-option
                    v-for="opt in kpiCycleOptions"
                    :key="opt.codeName"
                    :label="opt.codeName"
                    :value="opt.dictCode"
                  ></el-option>
                </el-select>
              </div>
            </div>
            <div class="line_wrap flex_row_betweens">
              <span>回溯周期：</span>
              <div>
                <el-input type="number" v-model.number="cycleNbr" disabled></el-input>
              </div>
            </div>
            <div class="line_wrap flex_row_betweens">
              <span>极性：</span>
              <div>
                <el-select v-model="kpiPolarity" placeholder="请选择" clearable>
                  <el-option
                    v-for="opt in kpiPolarityOptions"
                    :key="opt.codeName"
                    :label="opt.codeName"
                    :value="opt.dictCode"
                  ></el-option>
                </el-select>
              </div>
            </div>
            <div class="line_wrap flex_row_betweens">
              <span>单位：</span>
              <div>
                <el-input v-model="kpiUnit"></el-input>
              </div>
            </div>
            <div class="line_wrap flex_row_betweens">
              <span>计算公式：</span>
              <div>
                <el-input type="textarea" v-model="kpiExpression"></el-input>
              </div>
            </div>
            <div class="line_wrap flex_row_betweens">
              <span>指标定义：</span>
              <div>
                <el-input type="textarea" v-model="kpiDesc"></el-input>
              </div>
            </div>
            <div class="align_center">
              <el-button type="primary" class="page_confirm_btn" @click="undateTargetBtn()">确认</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" :title="popupTitleSign ? '新增指标分类' : '修改指标分类'" width="40%" center>
      <div class="line_wrap flex_row_betweens">
        <span>选择上级指标：</span>
        <div>
          <el-cascader
            :disabled="popupTitleSign == true ? false : true"
            :options="treeData"
            v-model="parentKpiClassCode"
            placeholder="请选择指标"
            :change-on-select="true"
            :props="{ label: 'value', value: 'code', expandTrigger: 'hover' }"
            @change="handleItemChange"
            clearable
          >
          </el-cascader>
        </div>
      </div>
      <div class="line_wrap flex_row_betweens">
        <span>指标分类名称：</span>
        <div>
          <el-input v-model="kpiClassName"></el-input>
        </div>
      </div>
      <template v-slot:footer>
        <div class="dialog-footer text_right">
          <el-button class="page_clear_btn" @click="popUpCancal">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="popUpSubmitBtn">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import {
  getKpiClassTree,
  getKpiList,
  getDict,
  createKpiClass,
  getKpiClassDetail,
  updateKpiClass,
  deleteKpiClass,
  createKpi,
  getKpiInfo,
  updateKpi,
  deleteKpi,
  getCycleNbr
} from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio.vue'
import tableComponent from '@/components/talent/tableComps/tableComponent.vue'
import { useUserStore } from '@/stores/modules/user.js'
import { DeleteFilled } from '@element-plus/icons-vue'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

const needIndex = ref(true)
const treeData = ref([])
const indicatorsDimension = ref('')
const indicatorsDimensionOption = ref([])
const kpiClassCode = ref('')
const kpiName = ref('')
const kpiType = ref('')
const filterName = ref('')
const defaultCheckedKeys = ref('')
const kpiClassName = ref('')
const parentKpiClassCode = ref('')
const checkparentKpiClassCode = ref('')
const checkedId = ref('')
const dialogVisible = ref(false)
const popupTitleSign = ref(true)
const updataTargetSign = ref(false)
const kpiCycle = ref('')
const kpiCycleOptions = ref([])
const cycleNbr = ref('')
const kpiCode = ref('')
const kpiPolarity = ref('')
const kpiPolarityOptions = ref([])
const kpiUnit = ref('')
const kpiExpression = ref('')
const kpiDesc = ref('')

const tableData = reactive({
  columns: [
    { label: '指标编码', prop: 'kpiCode' },
    { label: '指标名称', prop: 'kpiName', width: '100' },
    { label: '指标维度', prop: 'kpiType', width: '80' },
    { label: '指标定义', prop: 'kpiDesc' },
    { label: '计算公式', prop: 'kpiExpression' },
    { label: '单位', prop: 'kpiUnit', width: '60' }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})

const getKpiClassTreeFun = () => {
  getKpiClassTree({ companyId: companyId.value }).then(res => {
    treeData.value = res.code == 200 && res.data.length > 0 ? res.data : []
  })
}

const clearPopUpData = () => {
  checkparentKpiClassCode.value = ''
  parentKpiClassCode.value = []
  kpiClassName.value = ''
}

const handleItemChange = val => {
  checkparentKpiClassCode.value = val ? val[val.length - 1] : ''
}

const popUpCancal = () => {
  dialogVisible.value = false
}

const addTreeNode = () => {
  clearPopUpData()
  dialogVisible.value = true
  popupTitleSign.value = true
}

const popUpSubmitBtn = () => {
  if (popupTitleSign.value) {
    createKpiClassFun()
  } else {
    updateKpiClassFun()
  }
}

const createKpiClassFun = () => {
  if (!kpiClassName.value) {
    ElMessageBox.alert('请填写指标分类名称！', '提示')
    return
  }
  createKpiClass({
    kpiClassName: kpiClassName.value,
    parentKpiClassCode: checkparentKpiClassCode.value
  }).then(res => {
    if (res.code == 200) {
      getKpiClassTreeFun()
      ElMessageBox.alert(res.msg, '成功')
      dialogVisible.value = false
    } else {
      ElMessageBox.alert(res.msg, '错误')
    }
  })
}

const clickCallback = (val, isLastNode) => {
  checkedId.value = val
  tableData.page.current = 1
  getKpiListFun()
}

const getKpiClassDetailFun = () => {
  getKpiClassDetail({ kpiClassCode: checkedId.value }).then(res => {
    if (res.code == 200) {
      kpiClassName.value = res.data.kpiClassName
      parentKpiClassCode.value = res.data.parentKpiClassCode
    }
  })
}

const editTreeNode = () => {
  if (!checkedId.value) {
    ElMessageBox.alert('请选择节点！', '提示')
    return
  }
  clearPopUpData()
  popupTitleSign.value = false
  getKpiClassDetailFun()
  dialogVisible.value = true
}

const updateKpiClassFun = () => {
  updateKpiClass({
    kpiClassName: kpiClassName.value,
    kpiClassCode: checkedId.value
  }).then(res => {
    if (res.code == 200) {
      getKpiClassTreeFun()
      ElMessageBox.alert(res.msg, '成功')
      dialogVisible.value = false
    } else {
      ElMessageBox.alert(res.msg, '错误')
    }
  })
}

const deleteTreeNode = () => {
  if (!checkedId.value) {
    ElMessageBox.alert('请选择节点！', '提示')
    return
  }
  ElMessageBox.confirm('确定删除?', '确定', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteKpiClassFun(checkedId.value)
  })
}

const deleteKpiClassFun = val => {
  deleteKpiClass({ kpiClassCode: val }).then(res => {
    if (res.code == 200) {
      checkedId.value = ''
      defaultCheckedKeys.value = ''
      getKpiClassTreeFun()
      tableData.page.current = 1
      getKpiListFun()
      ElMessageBox.alert(res.msg, '成功')
    } else {
      ElMessageBox.alert(res.msg, '错误')
    }
  })
}

const getDictFun = () => {
  indicatorsDimensionOption.value = []
  kpiCycleOptions.value = []
  getDict({ dictId: 'KPI_TYPE' }).then(res => {
    if (res.code == 200) indicatorsDimensionOption.value = res.data
  })
  getDict({ dictId: 'KPI_CYCLE' }).then(res => {
    if (res.code == 200) kpiCycleOptions.value = res.data
  })
  getDict({ dictId: 'KPI_POLARITY' }).then(res => {
    if (res.code == 200) kpiPolarityOptions.value = res.data
  })
}

const getKpiListFun = () => {
  tableData.data = []
  getKpiList({
    kpiClassCode: checkedId.value,
    kpiName: filterName.value,
    kpiType: indicatorsDimension.value,
    current: tableData.page.current,
    size: tableData.page.size
  }).then(res => {
    if (res.code == 200) {
      if (res.data.length > 0) {
        tableData.data = res.data.map(item => ({
          companyId: item.companyId,
          kpiCode: item.kpiCode,
          kpiName: item.kpiName,
          kpiType: item.kpiTypeName,
          kpiDesc: item.kpiDesc,
          kpiExpression: item.kpiExpression,
          kpiUnit: item.kpiUnit,
          sortNbr: item.sortNbr
        }))
      }
      tableData.page.total = res.total
    }
  })
}

const handleSizeChange = val => {
  tableData.page.current = 1
  tableData.page.size = val
  getKpiListFun()
}

const handleCurrentChange = val => {
  tableData.page.current = val
  getKpiListFun()
}

const keyWordSearch = () => {
  tableData.page.current = 1
  getKpiListFun()
}

const clearTargetData = () => {
  kpiCode.value = ''
  kpiCycle.value = ''
  kpiDesc.value = ''
  kpiExpression.value = ''
  kpiName.value = ''
  kpiPolarity.value = ''
  kpiType.value = ''
  kpiUnit.value = ''
  cycleNbr.value = ''
}

const createTargetBtn = () => {
  if (!checkedId.value) {
    ElMessageBox.alert('请选择指标分类！', '提示')
  } else {
    clearTargetData()
    updataTargetSign.value = true
  }
}

const kpiCycleChange = val => {
  getCycleNbrFun()
}

const getCycleNbrFun = () => {
  getCycleNbr({
    dictCode: kpiCycle.value,
    dictId: 'KPI_CYCLE'
  }).then(res => {
    cycleNbr.value = res.code == 200 ? res.data : ''
  })
}

const undateTargetBtn = () => {
  if (kpiCode.value) {
    updateKpiFun()
  } else {
    if (!checkedId.value) {
      ElMessageBox.alert('请选择节点！', '提示')
    } else {
      createKpiFun(checkedId.value)
    }
  }
}

const createKpiFun = val => {
  if (!kpiName.value) {
    ElMessageBox.alert('请填写指标名称!', '提示')
    return
  }
  createKpi({
    kpiClassCode: val,
    kpiCode: kpiCode.value,
    kpiCycle: kpiCycle.value,
    kpiDesc: kpiDesc.value,
    kpiExpression: kpiExpression.value,
    kpiName: kpiName.value,
    kpiPolarity: kpiPolarity.value,
    kpiType: kpiType.value,
    kpiUnit: kpiUnit.value,
    cycleNbr: cycleNbr.value
  }).then(res => {
    if (res.code == 200) {
      getKpiListFun()
      updataTargetSign.value = false
      ElMessageBox.alert(res.msg, '成功')
    } else {
      ElMessageBox.alert(res.msg, '错误')
    }
  })
}

const tableEdit = (index, data) => {
  kpiCode.value = data[index].kpiCode
  updataTargetSign.value = true
  getKpiInfoFun()
}

const getKpiInfoFun = () => {
  getKpiInfo({ kpiCode: kpiCode.value }).then(res => {
    if (res.code == 200) {
      kpiClassCode.value = res.data.kpiClassCode
      kpiCode.value = res.data.kpiCode
      kpiCycle.value = res.data.kpiCycle
      kpiDesc.value = res.data.kpiDesc
      kpiExpression.value = res.data.kpiExpression
      kpiName.value = res.data.kpiName
      kpiPolarity.value = res.data.kpiPolarity
      kpiType.value = res.data.kpiType
      kpiUnit.value = res.data.kpiUnit
      cycleNbr.value = res.data.cycleNbr
    }
  })
}

const updateKpiFun = () => {
  updateKpi({
    kpiClassCode: kpiClassCode.value,
    kpiCode: kpiCode.value,
    kpiCycle: kpiCycle.value,
    kpiDesc: kpiDesc.value,
    kpiExpression: kpiExpression.value,
    kpiName: kpiName.value,
    kpiPolarity: kpiPolarity.value,
    kpiType: kpiType.value,
    kpiUnit: kpiUnit.value,
    cycleNbr: cycleNbr.value
  }).then(res => {
    if (res.code == 200) {
      kpiCode.value = ''
      getKpiListFun()
      updataTargetSign.value = false
      ElMessageBox.alert(res.msg, '成功')
    } else {
      ElMessageBox.alert(res.msg, '错误')
    }
  })
}

const tableDeleteRow = (index, data) => {
  ElMessageBox.confirm('确定删除?', '确定', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteKpiFun(data[index].kpiCode)
  })
}

const deleteKpiFun = val => {
  deleteKpi({ kpiCode: val }).then(res => {
    if (res.code == 200) {
      tableData.page.current = 1
      getKpiListFun()
      ElMessageBox.alert(res.msg, '成功')
    } else {
      ElMessageBox.alert(res.msg, '错误')
    }
  })
}

const goBackBtn = () => {
  updataTargetSign.value = false
}

// 监听 companyId 变化
watch(
  companyId,
  val => {
    if (val) {
      getKpiClassTreeFun()
      getDictFun()
      getKpiListFun()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.target_maintenance_wrap {
  .page_main_title {
    .go_back_btn {
      padding-right: 20px;
      color: #0099ff;
      cursor: pointer;
      font-weight: normal;
      font-size: 14px;
    }
  }
  .line_wrap {
    margin: 0 0 10px 0;
    line-height: 40px;
    span {
      padding: 0 10px 0 0;
      width: 110px;
      text-align: right;
      .required_fields_icon {
        padding: 0;
        color: #f56c6c;
      }
    }
    div {
      flex: 1;
    }
    .el-cascader,
    .el-select {
      width: 100%;
      .el-input--suffix {
        width: 100%;
      }
    }
    .el-textarea .el-textarea__inner {
      resize: none;
      font-family:
        PingFang SC,
        Avenir,
        Helvetica,
        Arial,
        sans-serif !important;
      font-size: 12px;
    }
  }
  .updata_target_wrap {
    width: 600px;
  }
}
</style>
