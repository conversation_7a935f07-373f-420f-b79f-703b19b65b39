<template>
    <div class="report_section">
        <div class="page_second_title">基本信息</div>
        <div class="basic_info">
            <div class="item" v-for="item in basicInfoItem">
                <div class="item_label">{{ item.label }}</div>
                <div class="item_value">{{ basicInfo[item.valueKey] }}</div>
            </div>
        </div>
        <div class="page_second_title">岗位信息</div>
        <div class="basic_info">
            <div class="item" v-for="item in postInfoItem">
                <div class="item_label">{{ item.label }}</div>
                <div class="item_value">{{ basicInfo[item.valueKey] }}</div>
            </div>
        </div>
    </div>
</template>

<script>
    import { getDictList, getUserReportBasicInfo } from "../../../../request/api";

    export default {
        name: "userRBasicInfo",
        props: ["nextBtnText", "enqId", "userId", "postCode"],
        created() {
            this.getEnqUserInfoFun();
        },
        methods: {
            getEnqUserInfoFun() {
                getUserReportBasicInfo({
                    enqId: this.enqId,
                    userId: this.userId,
                    postCode: this.postCode,
                }).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$msg.success("获取信息成功");
                        let data = res.data;
                        this.basicInfo = data;
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
        },
        data() {
            return {
                basicInfo: {},
                basicInfoItem: [
                    {
                        label: "姓名",
                        valueKey: "userName",
                    },
                    {
                        label: "性别",
                        valueKey: "gender",
                    },
                    {
                        label: "出生日期",
                        valueKey: "birthday",
                    },
                    {
                        label: "籍贯",
                        valueKey: "nativePlace",
                    },
                    {
                        label: "家庭所在地",
                        valueKey: "homePlace",
                    },
                    {
                        label: "现常住地",
                        valueKey: "residencePlace",
                    },
                    {
                        label: "工作地址",
                        valueKey: "workPlace",
                    },
                    {
                        label: "民族",
                        valueKey: "nationalityCode",
                    },
                    {
                        label: "婚姻状况",
                        valueKey: "maritalStatus",
                    },
                    {
                        label: "参加工作日期",
                        valueKey: "workBeginDate",
                    },
                ],
                postInfo: {},
                postInfoItem: [
                    {
                        label: "所在部门",
                        valueKey: "orgName",
                    },
                    {
                        label: "岗位名称",
                        valueKey: "postName",
                    },
                    {
                        label: "职层",
                        valueKey: "jobLevelName",
                    },
                    {
                        label: "职等",
                        valueKey: "jobGradeName",
                    },
                    {
                        label: "上级岗位",
                        valueKey: "superiorPostName",
                    },
                    {
                        label: "直接上级",
                        valueKey: "superiorName",
                    },
                    {
                        label: "入司日期",
                        valueKey: "currentEmpDate",
                    },
                    {
                        label: "工龄",
                        valueKey: "workYears",
                    },
                    {
                        label: "本岗位工作时长",
                        valueKey: "currentPostAge",
                    },
                    {
                        label: "最近晋升日期",
                        valueKey: "lastPromotionDate",
                    },
                ],
            };
        },
    };
</script>

<style scoped lang="scss">
    .basic_info {
        width: 90%;
        margin: 0 auto 32px;
        .item{
            display: flex;
            line-height: 32px;
            border-bottom:1px solid #ddd;
            padding: 0 16px;
            .item_label{
                flex: 1;
            }
            .item_value{
                flex: 1;
                color: #0099fd;
            }
        }
    }

    .el-input__inner {
        width: 280px;
    }
</style>
