<script setup>
defineOptions({ name: 'targetSpotDiagnosis' })
const menuList = ref([
  {
    title: '诊断项目',
    path: '/AI/targetSpotDiagnosis/project'
  },
  { title: '诊断进度', path: '/AI/targetSpotDiagnosis/progress' },
  { title: '诊断报告', path: '/AI/targetSpotDiagnosis/report' }
])
const router = useRouter()
const activePath = computed(() => router.currentRoute.value.path)
const toPath = item => {
  activePath.value = item.path
  router.push({ path: item.path })
}
</script>
<template>
  <div class="targetSpotDiagnosis">
    <div class="menu">
      <div class="title">能力诊断 ：</div>
      <ul>
        <li
          v-for="(item, index) in menuList"
          :class="{ active: activePath.indexOf(item.path) != -1 }"
          :key="index"
          @click="toPath(item)"
        >
          {{ item.title }}
        </li>
      </ul>
    </div>
    <RouterView />
  </div>
</template>
<style lang="scss" scoped>
.targetSpotDiagnosis {
  display: flex;
}

.menu {
  width: 180px;
  flex: 0 0 180px;
  height: 194px;
  background: linear-gradient(224deg, #d0e4f9 0%, rgba(195, 230, 255, 0.6) 100%);
  border-radius: 8px 8px 8px 8px;
  padding: 12px 20px 20px;
  margin-right: 19px;

  .title {
    font-size: 14px;
    color: #3d3d3d;
    line-height: 20px;
    font-weight: bold;
    margin-bottom: 6px;
  }

  ul {
    li {
      background: #f0f9ff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #a5c1dc;
      font-size: 14px;
      color: #6c757e;
      height: 35px;
      line-height: 35px;
      text-align: center;
      margin-top: 10px;
      cursor: pointer;
      &.active {
        background: #ffffff;
        border: 1px solid #40a0ff;
        color: #40a0ff;
      }
    }
  }
}
</style>
