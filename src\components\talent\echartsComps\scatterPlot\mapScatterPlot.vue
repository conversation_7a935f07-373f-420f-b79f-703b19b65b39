// 地图散点图
<template>
  <div class="map_scatter_plot_main">
    <div :id="id" class="chart_dom" :style="styleObj"></div>
  </div>
</template>

<script>
import * as eCharts from 'echarts'
// import china from 'echarts/map/json/china.json'
// import chinaCities from 'echarts/map/json/china-cities.json'
const china = []
const chinaCities = []
export default {
  name: 'mapScatterPlot',
  props: {
    chartData: {
      type: Array,
      default: function () {
        return []
      }
    },
    width: {
      type: Number,
      default: 100
    },
    height: {
      type: Number,
      default: 100
    },
    // 地图数据层级
    // 1：省级地图数据
    // 2：市级地图数据
    mapLevel: {
      type: Number,
      default: 1
    }
  },
  components: {},
  data() {
    return {
      id: '',
      styleObj: {
        width: this.width + 'px',
        height: this.height + 'px'
      },
      egData: [
        { name: '石家庄', value: 14 },
        { name: '莱芜', value: 15 }
      ]
    }
  },
  watch: {
    chartData: {
      handler() {
        this.init(this.chartData)
      },
      deep: true
    }
  },
  created() {},
  mounted() {
    if (this.chartData.length == 0) {
      return
    }
    this.init(this.chartData)
  },
  methods: {
    init(chartData) {
      let id = this.$util.createRandomId()
      this.id = id
      let data = this.formatData(chartData)
      this.$nextTick(() => {
        this.toDraw(id, data)
      })
    },
    toDraw(id, data) {
      let _this = this
      eCharts.registerMap('china', china)
      let myChart = eCharts.init(document.getElementById(id))
      if (data.length == 0) {
        myChart.clear()
        return
      }
      let option = {
        backgroundColor: '#F3F3F3',
        tooltip: {
          trigger: 'item',
          formatter: function (val) {
            return val.name + ': ' + val.value[2]
          }
        },
        geo: {
          map: 'china',
          label: {
            emphasis: {
              show: false
            }
          },
          roam: true,
          itemStyle: {
            normal: {
              areaColor: '#F3F3F3',
              borderColor: '#111'
            },
            emphasis: {
              areaColor: '#F3F3F3'
            }
          }
        },
        series: [
          {
            name: 'pm2.5',
            type: 'scatter',
            coordinateSystem: 'geo',
            data: data,
            symbolSize: function (val) {
              return val[2] * 3
            },
            label: {
              normal: {
                formatter: '{b}',
                position: 'right',
                show: true
              },
              emphasis: {
                show: true
              }
            },
            itemStyle: {
              normal: {
                color: '#6B127F'
              }
            },
            zlevel: 2
          },
          {
            name: 'Top 5',
            type: 'effectScatter',
            coordinateSystem: 'geo',
            data: data
              .sort(function (a, b) {
                return b.value[2] - a.value[2]
              })
              .slice(0, 5),
            symbolSize: function (val) {
              return val[2]
            },
            showEffectOn: 'render',
            rippleEffect: {
              brushType: 'stroke'
            },
            hoverAnimation: true,
            label: {
              normal: {
                formatter: '{b}',
                position: 'right',
                show: true
              }
            },
            itemStyle: {
              normal: {
                color: '#6B127F',
                shadowBlur: 10,
                shadowColor: '#333'
              }
            },
            zlevel: 1
          }
        ]
      }

      myChart.setOption(option)
    },
    formatData(data) {
      let result = []
      if (this.mapLevel == 1) {
        data.forEach((item, index) => {
          let obj = {}
          for (const _item of china.features) {
            if (_item.properties.name == item.name) {
              obj.name = _item.properties.name
              obj.value = _item.properties.cp.concat(item.value)
              result.push(obj)
              obj = {}
              break
            } else {
            }
          }
        })
      } else if (this.mapLevel == 2) {
        data.forEach((item, index) => {
          let obj = {}
          for (const _item of chinaCities.features) {
            if (_item.properties.name == item.name) {
              obj.name = _item.properties.name
              obj.value = _item.properties.cp.concat(item.value)
              result.push(obj)
              obj = {}
              break
            } else {
            }
          }
        })
      }
      return result
    }
  }
}
</script>

<style scoped lang="scss">
.map_scatter_plot_main {
  width: 100%;
  margin: 0 auto;
}
</style>
