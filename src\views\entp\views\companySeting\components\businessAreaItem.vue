<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in itemData" :key="item.id">
      <el-input class="item" v-model="item.name" placeholder />
      <el-input class="item" v-model="item.type" placeholder />
      <el-select class="item" v-model="item.state" placeholder>
        <el-option label="启用" value="启用" />
        <el-option label="关闭" value="关闭" />
      </el-select>
      <el-select class="item" v-model="item.sortIndex" placeholder>
        <el-option label="0" value="0" />
        <el-option label="10" value="10" />
        <el-option label="20" value="20" />
        <el-option label="30" value="30" />
      </el-select>
      <div class="item item_icon_wrap">
        <el-icon class="item_icon" @click="deleteItem(item, index)">
          <Delete />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  itemData: {
    type: Array,
    default: () => [
      {
        id: '1',
        name: '',
        type: '',
        state: '',
        sortIndex: ''
      }
    ]
  }
})

// Emits
const emit = defineEmits(['deleteItem'])

// Methods
const deleteItem = (item, index) => {
  emit('deleteItem', index)
}
</script>

<style scoped>
.edu_info_item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 0 16px;
}

.item {
  width: 23%;
  padding: 0 4px;
  line-height: 40px;
}

.item.school_name {
  width: 27%;
}

.item_icon_wrap {
  text-align: center;
  width: 5%;
}

.item_icon {
  font-size: 20px;
  color: var(--el-color-primary);
  cursor: pointer;
}
</style>
