<script setup>
import Table from '../components/table.vue'
import Tree from '@/components/tree/index.vue'
import { libraryTending } from '@/assets/data/data9.js'
defineOptions({ name: 'orgRelevancy' })
const data = ref(libraryTending.kpiList)
const columns = ref([
  {
    prop: 'kpiCode',
    label: '指标编码',
    width: 100
  },
  {
    prop: 'kpiName',
    label: '指标名称',
    width: 250
  },
  {
    prop: 'kpiClassCode',
    label: '指标类型'
  },
  {
    prop: 'kpiType',
    label: '指标类别'
  },
  {
    prop: 'kpiNature',
    label: '指标性质'
  },
  {
    prop: 'kpiExpression',
    label: '计算公式',
    width: 320,
    showOverflowTooltip: true
  },
  {
    prop: 'kpiUnit',
    label: '单位'
  },
  {
    prop: 'dataSource',
    label: '数据来源'
  },
  {
    prop: 'kpiCycle',
    label: '指标周期'
  },
  {
    prop: 'kpiPolarity',
    label: '指标极性'
  },
  {
    prop: 'parentKpiCode',
    label: '上级指标'
  },
  {
    prop: 'effectKpi',
    label: '影响指标',
    align: 'center'
  },
  {
    prop: 'person',
    label: '责任人'
  }
])
const defaultProps = ref({
  children: 'children',
  label: 'label'
})
const treeData = ref([
  {
    id: 1,
    label: 'H公司冰箱公司',
    children: [
      {
        id: 10,
        label: '营销分公司（15）',
        children: [
          {
            id: 100,
            label: '北京分公司',
            children: [
              {
                id: 1000,
                label: 'H营销部'
              },
              {
                id: 1001,
                label: 'S营销部'
              }
            ]
          },
          {
            id: 200,
            label: '成都分公司',
            children: [
              {
                id: 1100,
                label: 'H营销部'
              },
              {
                id: 1101,
                label: 'S营销部'
              }
            ]
          },
          {
            id: 300,
            label: '福州分公司',
            children: [
              {
                id: 1200,
                label: 'H营销部'
              },
              {
                id: 1201,
                label: 'S营销部'
              }
            ]
          },
          {
            id: 400,
            label: '贵阳分公司',
            children: [
              {
                id: 1300,
                label: 'H营销部'
              },
              {
                id: 1301,
                label: 'S营销部'
              }
            ]
          },
          {
            id: 500,
            label: '广州分公司',
            children: [
              {
                id: 1400,
                label: 'H营销部'
              },
              {
                id: 1401,
                label: 'S营销部'
              }
            ]
          },
          {
            id: 600,
            label: '合肥分公司',
            children: [
              {
                id: 1500,
                label: 'H营销部'
              },
              {
                id: 1501,
                label: 'S营销部'
              }
            ]
          },
          {
            id: 700,
            label: '杭州分公司',
            children: [
              {
                id: 1600,
                label: 'H营销部'
              },
              {
                id: 1601,
                label: 'S营销部'
              }
            ]
          },
          {
            id: 800,
            label: '济南分公司',
            children: [
              {
                id: 1700,
                label: 'H营销部'
              },
              {
                id: 1701,
                label: 'S营销部'
              }
            ]
          }
        ]
      }
    ]
  }
])
const defaultChecked = ref([])
const defaultExpand = ref(true)
onMounted(() => {})
</script>
<template>
  <div class="orgRelevancy_wrap">
    <div class="second_wrap justify-between">
      <div class="page-title-line">指标信息</div>
      <div class="second_r justify-between">
        <div class="operate_data justify-between">
          <div class="l_btn operate_btn"><span class="icon icon_import"></span> 导入</div>
          <div class="r_btn operate_btn"><span class="icon icon_export"></span> 导出</div>
        </div>
      </div>
    </div>
    <div class="orgRelevancy_content justify-between">
      <div class="l">
        <div class="title">组织</div>
        <!-- <div class="second_t">未关联指标组织</div> -->
        <div class="tree_wrap">
          <Tree></Tree>
        </div>
      </div>
      <div class="r">
        <Table :data="data" :columns="columns" :showIndex="true" :showSelect="true"></Table>
        <div class="affirm_btn">确认</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.orgRelevancy_wrap {
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
  font-size: 14px;
  .justify-between {
    display: flex;
    justify-content: space-between;
  }
  .second_wrap {
    margin-bottom: 10px;
    height: 36px;
    align-items: center;
    .page-title-line {
      margin-bottom: -5px;
    }
    .second_r {
      .add_btn {
        width: 100px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        color: #fff;
        background: #40a0ff;
        border-radius: 6px 6px 6px 6px;
        cursor: pointer;
        .icon {
          display: inline-block;
          margin-bottom: -2px;
          width: 16px;
          height: 16px;
          background: url('@/assets/imgs/indicator/icon_01.png') no-repeat center;
        }
      }
      .operate_data {
        margin-left: 10px;
        height: 36px;
        line-height: 36px;
        .operate_btn {
          width: 68px;
          text-align: center;
          border: 1px solid #666666;
          cursor: pointer;
          .icon {
            display: inline-block;
            margin-bottom: -2px;
            width: 16px;
            height: 16px;
          }
        }
        .l_btn {
          border-radius: 6px 0 0 6px;
          border-right: 1px solid transparent;
          .icon {
            background: url('@/assets/imgs/indicator/icon_02.png') no-repeat center center;
          }
        }
        .l_btn:hover {
          color: #40a0ff;
          border: 1px solid #40a0ff;
          .icon {
            background: url('@/assets/imgs/indicator/icon_04.png') no-repeat center center;
          }
          + .r_btn {
            border-left: 1px solid transparent;
          }
        }
        .r_btn {
          border-radius: 0 6px 6px 0;
          .icon {
            background: url('@/assets/imgs/indicator/icon_03.png') no-repeat center center;
          }
        }
        .r_btn:hover {
          color: #40a0ff;
          border: 1px solid #40a0ff;
          .icon {
            background: url('@/assets/imgs/indicator/icon_05.png') no-repeat center center;
          }
        }
      }
    }
  }
  .orgRelevancy_content {
    .l {
      padding: 8px;
      margin-right: 20px;
      width: 230px;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      .title {
        margin: 0 auto;
        width: 210px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
        background: #e3effa;
        border-radius: 8px 8px 8px 8px;
      }
      .second_t {
        font-size: 12px;
        height: 40px;
        line-height: 40px;
        color: #40a0ff;
      }
    }
    .r {
      width: calc(100% - 250px);
      .affirm_btn {
        margin: 20px 0 20px;
        width: 100px;
        height: 36px;
        line-height: 36px;
        color: #fff;
        text-align: center;
        background: #40a0ff;
        border-radius: 6px 6px 6px 6px;
      }
    }
  }
}
</style>
