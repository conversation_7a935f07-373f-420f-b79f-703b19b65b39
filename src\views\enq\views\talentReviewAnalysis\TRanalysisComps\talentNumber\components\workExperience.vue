<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">工作经验</div>
          <div class="content_item_content" id="work_exprience"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">最高岗位职层分布</div>
          <div class="content_item_content" id="job_level"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">岗位类型分布</div>
          <div class="content_item_content" id="job_class"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">与当前岗位相同</div>
          <div class="content_item_content" id="curr_post"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">与当前行业相同</div>
          <div class="content_item_content" id="curr_industry"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">业务领域</div>
          <div class="content_item_content" id="biz_domain"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            工作经历分布明细
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { workExperience, queryWorkExpList, exportData } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')

const experienceData = reactive({
  data: []
})

const jobLevel = reactive({
  data: []
})

const jobClass = reactive({
  data: []
})

const currentPost = reactive({
  data: []
})

const currentIndustry = reactive({
  data: []
})

const bizDomain = reactive({
  data: []
})

const filterData = ref([])
const page = ref(1)
const size = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '一级组织',
      prop: 'onLevelName'
    },
    {
      label: '二级组织',
      prop: 'twoLevelName'
    },
    {
      label: '三级组织',
      prop: 'threeLevelName'
    },
    {
      label: '四级组织',
      prop: 'fourLevelName'
    },
    {
      label: '姓名',
      prop: 'userName'
    },
    {
      label: '岗位',
      prop: 'postName'
    },
    {
      label: '公司名称',
      prop: 'companyName'
    },
    {
      label: '开始日期',
      prop: 'beginDate'
    },
    {
      label: '结束日期',
      prop: 'endDate'
    },
    {
      label: '岗位职层',
      prop: 'jobLevelName'
    },
    {
      label: '岗位类型',
      prop: 'code_name'
    },
    {
      label: '业务领域',
      prop: 'bizDomainName'
    },
    {
      label: '同岗位',
      prop: 'postRelated'
    },
    {
      label: '同行业',
      prop: 'industryRelated'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('work_exprience', 'YBar', '230', '200', experienceData)
  echartsRenderPage('job_level', 'YBar', '230', '200', jobLevel)
  echartsRenderPage('job_class', 'Ring', '230', '200', jobClass)
  echartsRenderPage('curr_post', 'Ring', '230', '200', currentPost)
  echartsRenderPage('curr_industry', 'Ring', '230', '200', currentIndustry)
  echartsRenderPage('biz_domain', 'YBar', '230', '200', bizDomain)
}

const workExperienceFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await workExperience(params)
    if (res.code == 200) {
      const data = res.data
      data.experience.map(item => {
        item.name = item.experience
      })
      experienceData.data = window.$util.addPercentSign(data.experience, 'value')
      jobLevel.data = window.$util.addPercentSign(data.jobLevel, 'value')
      jobClass.data = window.$util.addPercentSign(data.jobClass, 'value')
      currentPost.data = window.$util.addPercentSign(data.currentPost, 'value')
      currentIndustry.data = window.$util.addPercentSign(data.currentIndustry, 'value')
      bizDomain.data = window.$util.addPercentSign(data.bizDomain, 'value')
      initChart()
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  page.value = 1
  workExperienceFun()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryWorkExpList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error(error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'd'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '工作经历分布明细')
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  workExperienceFun()
  getTableData()
})
</script>

<style scoped lang="scss"></style>
