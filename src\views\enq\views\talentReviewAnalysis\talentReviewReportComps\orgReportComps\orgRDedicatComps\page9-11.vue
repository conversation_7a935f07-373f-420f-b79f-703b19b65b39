<template>
  <div class="report_page" id="page1">
    <div class="report_section">
      <div class="page_second_title">按工作时间分析</div>
      <div class="report_section_content clearfix">
        <div class="chart_box" id="jydPostAgeChart"></div>
      </div>
      <div class="report_section_content clearfix" v-if="tableData1.length">
        <el-table ref="multipleTable" :data="tableData1">
          <el-table-column
            prop="name"
            label="驱动因素"
            width="300"
          ></el-table-column>
          <el-table-column
            v-for="col in tableCloumns"
            :prop="col.dictCode"
            :key="col.dictCode"
            :label="col.codeName"
          ></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">各工作时间驱动因素</div>
      <div class="report_section_content clearfix">
        <el-table ref="multipleTable" :data="tableData2">
          <el-table-column
            prop="name"
            label="驱动因素"
            width="300"
          ></el-table-column>
          <el-table-column
            v-for="col in tableCloumns2"
            :prop="col.dictCode"
            :key="col.dictCode"
            :label="col.codeName"
          ></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">各工作时间得分差异分析</div>
      <div class="report_section_content clearfix">
        <div class="chart_box" id="mydPostAgeChart"></div>
      </div>
    </div>
    <!-- <div class="report_section">
            <div class="page_second_title">参考示意说明</div>
            <div class="report_section_content clearfix">
                <div class="eg_chart_box" id="eg_chart_11"></div>
                <div class="explain_text">
                    <div class="explain_title">形态一：微笑曲线，在某司龄段出现敬业度拐点:</div>
                    <ul>
                        <li class="line">-  1-3 年员工敬业度最低：</li>
                        <li class="dot">该由于HR招聘时给予应聘者较高的承诺，员工入职1年后，发现现实工作和理想存在偏差，这导致员工敬业度水平下滑。</li>
                        <li class="line">-  3-5 年员工敬业度最低：</li>
                        <li class="dot">职业瓶颈：该群体具有一定的工作经验，渴望更广阔的发展平台，但其一般达到一定的职位，晋升发展空间非常有限，导致敬业度下降。</li>
                        <li class="dot">倦怠心理：入职一段时间后，员工对固化的工作流程、工作任务等已经熟悉，容易出现倦怠。</li>
                        <li class="line">-  5-10 年员工敬业度最低：</li>
                        <li class="dot">该曲线常出现在人员流动率较低的行业。</li>
                    </ul>
                </div>
            </div>
            <div class="report_section_content clearfix">
                <div class="eg_chart_box" id="eg_chart_22"></div>
                <div class="explain_text">
                    <div class="explain_title">形态二：司龄时间越长，敬业度越差:</div>
                    <ul>
                        <li class="line">- 这种情况一般见于销售型企业，刚入职的员工受到业绩导向的强激励，敬业度较高，员工入职一段时间后，现实与预期的差异差导致员工敬业度水平下滑，司龄越长受到激励越不明显，导致员工敬业度随着司龄的增长而下降。</li>
                    </ul>
                </div>
            </div>
            <div class="report_section_content clearfix">
                <div class="eg_chart_box" id="eg_chart_33"></div>
                <div class="explain_text">
                    <div class="explain_title">形态三：司龄时间越长，敬业度越高，整体趋势与市场规律不一致：</div>
                    <ul>
                        <li class="line">- 这种情况一般会发生“论资排辈”的企业，以日企为代表。该类型企业有非常详细的职业生涯规划，并且晋升和加薪大多以司龄为主要参考。司龄越长的员工在企业获得的机会越多，易显示出较高的敬业度。而对于司龄较短的员工，受到体制的影响，其自身的价值难以实现，敬业度较低。</li>
                    </ul>
                </div>
            </div>
        </div> -->
  </div>
</template>
 
<script>
import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js";
import { jfAnalysisPage } from "../../../../../request/api.js";
import tableComp from "@/components/talent/tableComps/tableComponent";
export default {
  name: "",
  props: ["enqId", "orgCode", "isPdf", "userId"],
  components: { tableComp },
  data() {
    return {
      jydPostAgeChart: {
        data: [],
        padding: 100,
      },
      tableData1: [],
      tableData2: [],
      tableCloumns: [],
      tableCloumns2: [],
      mydPostAgeChart: {
        data: [],
        padding: 100,
      },
      egChartData1: {
        data: [
          {
            name: "半年以下",
            value: "86",
          },
          {
            name: "半年到一年",
            value: "77",
          },
          {
            name: "1年到3年",
            value: "70",
          },
          {
            name: "3年到5年",
            value: "60",
          },
          {
            name: "5年到10年",
            value: "80",
          },
          {
            name: "10年以上",
            value: "87",
          },
        ],
      },

      egChartData2: {
        data: [
          {
            name: "半年以下",
            value: "90",
          },
          {
            name: "半年到一年",
            value: "85",
          },
          {
            name: "1年到3年",
            value: "80",
          },
          {
            name: "3年到5年",
            value: "70",
          },
          {
            name: "5年到10年",
            value: "60",
          },
          {
            name: "10年以上",
            value: "50",
          },
        ],
      },
      egChartData3: {
        data: [
          {
            name: "半年以下",
            value: "50",
          },
          {
            name: "半年到一年",
            value: "60",
          },
          {
            name: "1年到3年",
            value: "70",
          },
          {
            name: "3年到5年",
            value: "80",
          },
          {
            name: "5年到10年",
            value: "85",
          },
          {
            name: "10年以上",
            value: "90",
          },
        ],
      },
    };
  },
  created() {
    this.jfAnalysisPageFun();
  },
  mounted() {},
  methods: {
    initChart() {
      echartsRenderPage(
        "jydPostAgeChart",
        "XBar",
        null,
        "280",
        this.jydPostAgeChart
      );
      echartsRenderPage(
        "mydPostAgeChart",
        "XBar",
        null,
        "280",
        this.mydPostAgeChart
      );
      // echartsRenderPage(
      //     "eg_chart_11",
      //     "XBar",
      //     "350",
      //     "230",
      //     this.egChartData1
      // );
      // echartsRenderPage(
      //     "eg_chart_22",
      //     "XBar",
      //     "350",
      //     "230",
      //     this.egChartData2
      // );
      // echartsRenderPage(
      //     "eg_chart_33",
      //     "XBar",
      //     "350",
      //     "230",
      //     this.egChartData3
      // );
    },
    jfAnalysisPageFun() {
      let params = {
        enqId: this.enqId,
        orgCode: this.orgCode,
        userId: this.userId,
        number: "9",
      };
      jfAnalysisPage(params).then((res) => {
        console.log(res);
        if (res.code == "200") {
          let data = res.data.jfAnalysisPage9;
          data.jydPostAgeChart.map((item) => {
            item["value"] = item.score;
          });
          this.$set(this.jydPostAgeChart, "data", data.jydPostAgeChart);
          this.tableData1 = data.jydPostAgeList;
          this.tableData2 = data.mydPostAgeList;
          this.tableCloumns2 = data.mydPostAgeListHeader;
          this.tableCloumns = data.experienceAges;
          data.mydPostAgeChart.map((item) => {
            item["value"] = item.score;
          });
          this.$set(this.mydPostAgeChart, "data", data.mydPostAgeChart);
          this.initChart();
        }
      });
    },
  },
};
</script>
 
<style scoped lang="scss">
.dedicated_main {
  height: 420px;
  overflow-y: auto;
}
.report_section {
  margin-bottom: 32px;
}
.chart_box {
  width: 100%;
  height: 260px;
  /*background: darkkhaki;*/
}
.eg_chart_box {
  float: left;
  width: 350px;
  height: 200px;
  /*background: darkkhaki;*/
  margin-right: 32px;
  overflow: hidden;
}
.explain_text {
  overflow: hidden;
  color: #212121;
  line-height: 24px;
}
</style>