<template>
    <div class="report_section edu_info_wrap performance_info_main">
        <div class="clearfix">
            <div class="page_second_title marginT_30">
                <span>获奖信息</span>
            </div>
            <div class="edu_info_center marginT_16">
                <div class="edu_info_header">
                    <div class="item">奖项名称</div>
                    <div class="item">颁发单位</div>
                    <div class="item">获奖日期</div>
                    <div class="item">获奖类型</div>
                    <div class="item">是否在本公司</div>
                </div>
                <div class="edu_info_mmain">
                    <div
                        class="edu_info_item"
                        v-for="(item, index) in performanceInfoData"
                        :key="item.id"
                    >
                        <div class="item">
                            {{ item.awardName }}
                        </div>
                        <div class="item">
                            {{ item.awardIssuer }}
                        </div>
                        <div class="item">
                            {{ item.awardDate | removeTime }}
                        </div>
                        <div class="item">
                            {{ item.awardType }}
                        </div>
                        <div class="item">
                            {{ item.currentCompany }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { getEnqUserAward } from "../../../../request/api";
    export default {
        name: "userRAwardInfo",
        props: ["nextBtnText", "enqId", "userId"],
        components: {},
        data() {
            return {
                performanceInfoData: [],
            };
        },
        created() {
            this.getEnqUserAwardFun();
        },
        filters: {
            removeTime: function (val) {
                return val ? val.split(" ")[0] : " ";
            },
        },
        methods: {
            getEnqUserAwardFun() {
                getEnqUserAward({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.performanceInfoData = res.data;
                    } else {
                        this.$message.error("获取数据失败!");
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .edu_info_center {
        .item {
            width: 20%;
            padding-left: 10px;
        }
    }
</style>
