<script setup>
import SectionTab from "../../components/sectionTab.vue";
import SectionTabV from "../../components/sectionTabVertical.vue";
import Table from "../../components/table.vue";
import InitiateTalentAssessment from "./initiateTalentAssessment.vue";
const sectionTabVCheckSign = ref(1);
const projectlist = ref([
  {
    title: "人效对标",
    date: "2025-03-06",
    typeList: [
      "人才数量盘点",
      "人才结构盘点",
      "人才质量盘点",
      "人才效能盘点",
      "人才发展盘点",
      "人才风险盘点",
    ],
  },
]);

const stepsData = ref([
  {
    name: "盘点准备",
    finshSign: false,
    btn: ["盘点配置"],
    code: 1,
  },
  {
    name: "个人盘点",
    finshSign: false,
    btn: ["个人盘点", "个人盘点完成", "退回"],
    code: 2,
  },
  {
    name: "数据上传",
    finshSign: false,
    btn: ["数据上传", "上传完成"],
    startSign: false,
    code: 3,
  },
  {
    name: "部门盘点",
    finshSign: false,
    btn: ["部门盘点"],
    startSign: false,
    code: 4,
  },
  {
    name: "盘点完成",
    finshSign: false,
    btn: ["盘点完成", "查看报告"],
    startSign: false,
    code: 4,
  },
]);
const stepsNum = ref(1);

const topTabList = ref([
  {
    name: "经营业绩类信息",
    code: 1,
  },
  {
    name: "成本类信息",
    code: 2,
  },
  {
    name: "人员类信息",
    code: 3,
  },
]);
const topTabChekcSign = ref(2);

const columns = ref([
  {
    label: "一级组织",
    prop: "a",
  },
  {
    label: "二级组织",
    prop: "b",
  },
  {
    label: "三级组织",
    prop: "c",
  },
  {
    label: "四级组织",
    prop: "d",
  },

  {
    label: "部门负责人",
    prop: "e",
    align: "center",
  },
  {
    label: "需维护人数",
    prop: "f",
    align: "center",
  },
  {
    label: "参评人数",
    prop: "g",
    align: "center",
  },
  {
    label: "已完成",
    prop: "h",
    align: "center",
  },
  {
    label: "未完成",
    prop: "i",
    align: "center",
  },
  {
    label: "未开始",
    prop: "j",
    align: "center",
  },
  // {
  //   label: "完成率",
  //   prop: "k",
  //   align: "center",
  // },
]);
const data = ref([
  {
    a: "海信冰箱公司",
    b: "",
    c: "供应链计划",
    d: "",
    e: "",
    f: "21",
    g: "21",
    h: "21",
    i: "0",
    j: "0",
    k: "100",
  },
]);

const columns2 = ref([
  {
    label: "部门名称",
    prop: "a",
    width: 260,
  },
  {
    label: "姓名",
    prop: "b",
  },
  {
    label: "岗位",
    prop: "c",
  },
  {
    label: "状态",
    prop: "d",
  },

  {
    label: "需维护",
    prop: "e",
  },
  {
    label: "已维护",
    prop: "f",
  },
  {
    label: "未维护",
    prop: "g",
  },
  {
    label: "最后登录时间",
    prop: "h",
    width: 160,
  },
  // {
  //   label: "完成率",
  //   prop: "k",
  //   align: "center",
  // },
]);
const data2 = ref([
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "已完成",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);

const pageInfo = ref({
  current: 1,
  size: 10,
  total: 100,
});
const input = ref("https://ceping.xiyiqq.com/710350");
const searchOrg = ref();
const staff = ref();
const searchStatus = ref();
const isInventorySign = ref(false);

const checkSecTabV = (c) => {
  sectionTabVCheckSign.value = c;
};
const stepsStatus = (n) => {
  if (n == stepsNum.value || n < stepsNum.value) {
    return true;
  }
};
const topTabChekc = (c) => {
  topTabChekcSign.value = c;
};

const handleSizeChange = (s) => {};
const handleCurrentChange = (c) => {};

const inventory = () => {
  isInventorySign.value = !isInventorySign.value;
};
const closeInventory = (c) => {
  isInventorySign.value = c;
};
</script>
<template>
  <div class="index_wrap">
    <div class="i_main_wrap">
      <div class="main_r_wrap" v-if="!isInventorySign">
        <div class="btn_m btn" @click="inventory">发起人才盘点</div>
        <div class="t_b_wrap justify-between" v-for="item in projectlist">
          <div class="t_b_l_w">
            <div class="title_w">
              <span class="t">战略绩效闭环运营人才盘点202501 </span>
              <span class="r">进度详情<span class="icon icon_06"></span> </span>
            </div>
            <div class="info_l_w">
              <span>起止日期：2024-09-26至2024-10-27</span>
              <span class="c_span">个人盘点：0提交 0总数</span>
              <span>部门盘点：0提交 0总数</span>
            </div>
            <div class="t_l_w justify-start">
              <div
                class="type_tip"
                v-for="(it, index) in item.typeList"
                :class="{ act_type_tip: index > 2 }"
              >
                {{ it }}
              </div>
            </div>
          </div>
          <div class="t_b_r_w">
            <div class="steps_content justify-start">
              <div
                class="step_item"
                v-for="(item, index) in stepsData"
                :key="item.code"
                :class="{
                  last_item: item.code == 3,
                  act: stepsStatus(item.code),
                }"
              >
                <div class="decorate_wrap">
                  <div class="step_info">
                    <div class="info">{{ item.name }}</div>
                  </div>

                  <div class="line_wrap justify-start">
                    <div class="dot"></div>
                    <div
                      v-if="index + 1 !== stepsData.length"
                      class="line"
                    ></div>
                  </div>
                  <div class="btn_list justify-start">
                    <div class="btn_t" v-for="it in item.btn">{{ it }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="check_progress_wrap">
          <div class="title_main justify-between">
            <div class="t_l">战略绩效闭环运营人才盘点202501—盘点进度</div>
            <div class="t_btn"><span class="icon"></span>返回</div>
          </div>
          <div class="inventory_item_wrap justify-between">
            <div class="le_w">
              <div class="title_second_wrap">
                <span class="title_second">战略绩效闭环运营人才盘点</span>
                <span class="t_icon">人才盘点</span>
              </div>
              <div class="info_section_wrap">
                <span class="text_c text_c_l">2024-09-25 ~ 2024-09-26</span>
                <span class="text_c">海信管理员 hxadmin</span>
              </div>
            </div>
            <div class="ce_w">
              <el-progress type="circle" :percentage="25" :stroke-width="20" />
            </div>
            <div class="ri_w justify-between">
              <div class="r_item_wrap">
                <div class="t_name">盘点人数</div>
                <div class="num"><span>21</span>人</div>
              </div>
              <div class="r_item_wrap">
                <div class="t_name">已完成</div>
                <div class="num"><span>21</span>人</div>
              </div>
              <div class="r_item_wrap">
                <div class="t_name">进行中</div>
                <div class="num"><span>0</span>人</div>
              </div>
              <div class="r_item_wrap">
                <div class="t_name">未开始</div>
                <div class="num"><span>0</span>人</div>
              </div>
            </div>
          </div>
          <div class="tab_steps_03_wrap tab_bottom_long_line_wrap">
            <div
              class="item_wrap"
              :class="{ top_tab_act: topTabChekcSign == it.code }"
              v-for="it in topTabList"
              @click="topTabChekc(it.code)"
            >
              <span class="bot_line"></span>
              {{ it.name }}
            </div>
          </div>
          <div class="tab_content_wrap" v-if="topTabChekcSign == 1">
            <div class="btn_m btn">部门进度统计</div>
            <Table
              :roundBorder="false"
              :columns="columns"
              :data="data"
              headerColor
              showIndex
            >
              <template #default="scope">
                <el-table-column class="" width="120px" label="完成率">
                  <!-- {{ scope.row.k }} -->
                  <el-progress percentage="100" />
                </el-table-column>
              </template>
            </Table>
            <el-pagination
              :current-page="pageInfo.current"
              :page-size="pageInfo.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pageInfo.total"
              :small="small"
              background
              layout=" sizes, prev, pager, next,total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
            <div class="invite_share_wrap">
              <div class="t_tips">还可以继续邀请 10人</div>
              <div class="invite_share_main">
                <div class="m_tips">
                  您可以复制链接或下载二维码，分享至微信群
                </div>
                <div class="invite_share_section justify-between">
                  <div class="invite_share_item l_item">
                    <div class="l_t">测评链接：</div>
                    <div class="">
                      <el-input v-model="input" placeholder="" />
                    </div>
                    <div class="btn_m btn">一键复制</div>
                  </div>
                  <div class="center_line"></div>
                  <div class="invite_share_item r_item justify-start">
                    <div class="qr_code"></div>
                    <div class="r_r_wrap">
                      <div class="qr">测评二维码</div>
                      <div class="btn_m btn">下载</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="btn_tip btn">
                下载测评进度信息，督促相关人员加快进度
              </div>
            </div>
          </div>
          <div class="tab_content_wrap" v-if="topTabChekcSign == 2">
            <div class="search_wrap justify-between">
              <div class="justify-start">
                <div class="search_item justify-start">
                  <span class="label">部门</span>
                  <el-select v-model="searchOrg" placeholder="请选择">
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </div>
                <div class="search_item justify-start">
                  <span class="label">人员</span>
                  <el-input v-model="staff" placeholder="" />
                </div>
                <div class="search_item justify-start">
                  <span class="label">状态</span>
                  <el-select v-model="searchStatus" placeholder="请选择">
                    <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" />
                  </el-select>
                </div>
                <div class="btn">查询</div>
              </div>
              <div class="btn">导出</div>
            </div>
            <Table
              :roundBorder="false"
              :columns="columns2"
              :data="data2"
              headerColor
              showIndex
            >
              <template #default="scope">
                <el-table-column class="" width="120px" label="完成率">
                  <!-- {{ scope.row.k }} -->
                  <el-progress percentage="100" />
                </el-table-column>
              </template>
            </Table>
            <el-pagination
              :current-page="pageInfo.current"
              :page-size="pageInfo.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pageInfo.total"
              :small="small"
              background
              layout=" sizes, prev, pager, next,total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
            <div class="invite_share_wrap">
              <div class="t_tips">还可以继续邀请 10人</div>
              <div class="invite_share_main">
                <div class="m_tips">
                  您可以复制链接或下载二维码，分享至微信群
                </div>
                <div class="invite_share_section justify-between">
                  <div class="invite_share_item l_item">
                    <div class="l_t">测评链接：</div>
                    <div class="">
                      <el-input v-model="input" placeholder="" />
                    </div>
                    <div class="btn_m btn">一键复制</div>
                  </div>
                  <div class="center_line"></div>
                  <div class="invite_share_item r_item justify-start">
                    <div class="qr_code"></div>
                    <div class="r_r_wrap">
                      <div class="qr">测评二维码</div>
                      <div class="btn_m btn">下载</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="btn_tip btn">
                下载测评进度信息，督促相关人员加快进度
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <InitiateTalentAssessment
          @closeInventory="closeInventory"
        ></InitiateTalentAssessment>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.index_wrap {
  .i_main_wrap {
    .main_r_wrap {
      .btn_m {
        width: 138px;
      }
      .t_b_wrap {
        margin: 16px auto 20px;
        padding: 20px;
        height: 191px;
        background: url("@/assets/imgs/org/img_03.png") no-repeat center;
        background-size: 100% 100%;
        .t_b_l_w {
          width: 48%;
          .title_w {
            width: 100%;
            display: flex;
            justify-content: space-between;
            span {
              display: inline-block;
            }
            .r {
              text-align: right;
              color: #40a0ff;
              .icon_06 {
                margin: 0 0 -2px 10px;
                width: 14px;
                height: 14px;
                background: url("@/assets/imgs/org/icon_06.png") no-repeat
                  center;
                background-size: 100% 100%;
              }
            }
          }
          .info_l_w {
            margin: 20px auto 30px;
            font-size: 14px;
            color: #898989;
            white-space: nowrap;
            span {
              display: inline-block;
            }
            .c_span {
              padding: 0 30px;
            }
          }
          .t_l_w {
            margin-right: -10px;
            .type_tip {
              margin: 0px 10px 0 0;
              padding: 0 14px;
              height: 30px;
              line-height: 30px;
              font-size: 12px;
              text-align: center;
              color: #fff;
              background: #40a0ff;
              border-radius: 4px 4px 4px 4px;
              &.act_type_tip {
                color: #40a0ff;
                background: #e3f1ff;
              }
            }
          }
        }
        .t_b_r_w {
          width: 48%;

          .steps_content {
            color: #acacac;
            margin-left: 20px;
            .step_item {
              .decorate_wrap {
                align-items: center;
              }
              .step_info {
                margin: 0 -20px;
                .step_num {
                  margin-right: 10px;
                  width: 30px;
                  height: 30px;
                  line-height: 30px;
                  text-align: center;
                  border-radius: 50%;
                  background: linear-gradient(180deg, #ebf0f4 0%, #ffffff 100%);
                  box-shadow: 0px 15px 24px 0px rgba(165, 186, 217, 0.3),
                    inset 0px 1px 0px 0px #ffffff;
                }
              }
              .line_wrap {
                margin: 10px 0 20px 0px;
                align-items: center;
                .dot {
                  margin: 0 9px;
                  width: 14px;
                  height: 14px;
                  border-radius: 50%;
                  background: #dddddd;
                }
                .line {
                  width: 100px;
                  height: 6px;
                  background: #d8d8d8;
                  border-radius: 37px 37px 37px 37px;
                }
              }
              .btn_list {
                margin: 0 50px 0 -50px;
                justify-content: center;
                flex-wrap: wrap;
                .btn_t {
                  margin: 0 0px 10px;
                  height: 30px;
                  line-height: 30px;
                  text-align: center;
                  padding: 0 14px !important;
                  font-size: 12px;
                  color: #898989;
                  background: #d8d8d8;
                  border-radius: 4px 4px 4px 4px;
                  white-space: nowrap;
                }
              }
            }
            .step_item:nth-child(2) {
              .btn_list {
                .btn_t:nth-child(2) {
                  margin: 0 10px 0 -15px;
                }
                .btn_t:nth-child(3) {
                  margin-right: -15px;
                }
              }
            }
            .step_item:nth-child(5) {
              .btn_list {
                margin-left: -20px;
              }
            }

            .act {
              .step_info {
                .step_num {
                  background: linear-gradient(180deg, #40a0ff 0%, #9fcfff 100%);
                  box-shadow: 0px 15px 24px 0px rgba(165, 186, 217, 0.3),
                    inset 0px 1px 0px 0px #9fcfff;
                  color: #fff;
                }
                .info {
                  color: #40a0ff;
                }
              }
              .line_wrap {
                .dot {
                  background: linear-gradient(180deg, #40a0ff 0%, #9fcfff 100%);
                }
                .line {
                  background: linear-gradient(
                    63deg,
                    #40a0ff 0%,
                    rgba(64, 160, 255, 0.1) 97%
                  );
                }
              }
              .btn_list {
                .btn_t {
                  background: #40a0ff;
                  color: #fff;
                }
              }
            }
          }
        }
      }
      .check_progress_wrap {
        padding: 20px;
        background: #ffffff;
        box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
        border-radius: 8px 8px 8px 8px;
        .title_main {
          .t_l {
          }
          .t_btn {
            width: 74px;
            height: 28px;
            line-height: 28px;
            background: #ffffff;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #40a0ff;
            text-align: center;
            color: #40a0ff;
            cursor: pointer;
            .icon {
              display: inline-block;
              margin: 0px 6px -1px 0;
              width: 16px;
              height: 16px;
              background: url("@/assets/imgs/org/icon_06.png") no-repeat center;
              background-size: 100% 100%;
            }
          }
        }
        .inventory_item_wrap {
          padding: 20px;
          margin: 20px auto;
          background: #ebf5ff;
          border-radius: 5px 5px 5px 5px;
          .le_w {
            width: 46%;
            .title_second_wrap {
              margin-bottom: 12px;
              .title_second {
                color: #3d3d3d;
              }
              .t_icon {
                margin: 0px 0 0 20px;
                display: inline-block;
                width: 92px;
                height: 20px;
                line-height: 20px;
                text-align: center;
                font-size: 12px;
                color: #ffffff;
                background: #40a0ff;
                border-radius: 105px 105px 105px 105px;
              }
            }
            .info_section_wrap {
              color: #acacac;
              font-size: 14px;
              .text_c_l {
                display: inline-block;
                margin-right: 80px;
              }
            }
          }
          .ce_w {
            padding: 0 50px;
            border-left: 1px solid #cadef1;
            border-right: 1px solid #cadef1;
            :deep .el-progress {
              margin-top: 10px;
              .el-progress-circle {
                width: 74px !important;
                height: 74px !important;
                .el-progress-circle__path {
                  width: 10px;
                }
              }
              .el-progress__text {
                font-size: 14px !important;
                color: #40a0ff;
              }
            }
          }
          .ri_w {
            .r_item_wrap {
              margin: 0 20px;
              width: 80px;
              text-align: center;
              .t_name {
                margin: 0 0 17px 0px;
                height: 20px;
                line-height: 20px;
                background: #cbe4fd;
                border-radius: 42px 42px 42px 42px;
                font-size: 14px;
                color: #40a0ff;
              }
              .num {
                span {
                  font-size: 30px;
                  color: #40a0ff;
                  font-weight: 600;
                }
              }
            }
          }
        }
        .tab_content_wrap {
          .btn_m {
            margin: 15px 0;
          }

          .invite_share_wrap {
            .t_tips {
              padding: 0 20px;
              height: 40px;
              line-height: 40px;
              background: #eff3f6;
              color: #40a0ff;
              border-radius: 6px 6px 0px 0px;
            }
            .invite_share_main {
              padding: 13px 20px 29px;
              border: 1px solid #dddddd;
              border-top: 1px solid transparent;
              border-radius: 0 0 6px 6px;
              .invite_share_section {
                margin: 30px 0 0 0;
              }
              .invite_share_item {
                width: 49%;
                padding: 0 60px;
                .btn_m {
                  margin: 0;
                }
                &.l_item {
                  padding-left: 20%;
                  :deep .el-input {
                    margin: 12px 0;
                    width: 310px;
                    font-size: 14px;
                    color: #333333;
                    .el-input__wrapper {
                      background: transparent;
                    }
                  }
                }
                &.r_item {
                  .qr_code {
                    margin-right: 20px;
                    width: 110px;
                    height: 110px;
                    border: 1px solid #dddddd;
                  }
                  .r_r_wrap {
                    .qr {
                      height: 72px;
                      line-height: 72px;
                    }
                  }
                }
              }
              .center_line {
                border-right: 1px solid #d8d8d8;
              }
            }
            .btn_tip {
              margin: 30px auto 20px;
              width: 366px;
            }
          }
          .search_wrap {
            margin: 15px 0 20px;
            .search_item {
              margin-right: 50px;
              span {
                display: inline-block;
                width: 50px;
                line-height: 40px;
              }
              :deep .el-select {
                width: 280px;
                .el-select__wrapper {
                  background: transparent;
                }
              }
              :deep .el-input {
                width: 280px;
                .el-input__wrapper {
                  background: transparent;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
