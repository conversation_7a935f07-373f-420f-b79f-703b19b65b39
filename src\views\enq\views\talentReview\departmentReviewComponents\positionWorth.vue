<template>
  <div class="task_confirmation_main marginT_8">
    <div class="page_second_title">职位价值</div>
    <div class="department_main marginT_8">
      <div class="personnel_item_wrap_left">
        <div
          class="personnel_item"
          v-for="(item, index) in personnelData"
          :class="{ completed: item.status == 'Y', curr: currIndex === index }"
          :key="index"
          @click="selectPersonnel(item, index)"
        >
          <span>{{ item.name }}</span>
          <i class="icon el-icon-check" v-if="item.status == 'Y'"></i>
          <i class="icon disc" v-else></i>
        </div>
      </div>
      <div class="personnel_item_wrap_right">
        <div class="project_item">
          <span class="moduleName">{{ personnelData.length > 0 ? personnelData[currIndex].name : '' }}：</span>
          <span class="item">{{ expectationPostOptions ? expectationPostOptions.item : '' }}</span>
        </div>
        <div>
          <departmentTableSelect :types="'postW'" :dataFrom="expectationPostOptions" @getChildData="getChildDataFn" />
        </div>
        <div class="btn_wrap align_center marginT_30">
          <el-button class="page_confirm_btn" type="primary" @click="prevBtn" v-show="currentIndex !== currentFirstCode"
            >上一步</el-button
          >
          <el-button class="page_confirm_btn" type="primary" @click="submitForm('nextStep')">{{
            nextBtnText
          }}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, getCurrentInstance } from 'vue'
import departmentTableSelect from './departmentTableSelect.vue'
import { jobValueModuleList, jobRequirementsList, saveJobRequirement, jobNext } from '../../../request/api'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const savaFlag = ref(true)
const currIndex = ref(0)
const personnelData = ref([])
const expectationPostOptions = ref({})

const selectPersonnel = (item, index) => {
  currIndex.value = index
  getQualityEvaluationItemFun(item)
}

const getQualityEvaluationItemFun = item => {
  const params = {
    enqId: props.enqId,
    modelId: item.modelId,
    moduleCode: item.code
  }
  jobRequirementsList(params).then(res => {
    if (res.code == '200') {
      expectationPostOptions.value = res.data
    } else {
      expectationPostOptions.value = {}
    }
  })
}

const getChildDataFn = data => {
  if (data) {
    const params = {
      enqId: props.enqId,
      requirementList: data,
      modelId: personnelData.value[currIndex.value].modelId,
      moduleCode: personnelData.value[currIndex.value].code
    }
    saveJobRequirement(params).then(res => {
      if (res.code == '200') {
        ElMessage.success(res.msg)
        getDeptUserPostFun()
        if (currIndex.value !== personnelData.value.length - 1) {
          currIndex.value++
        }
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}

const getDeptUserPostFun = () => {
  const params = {
    enqId: props.enqId
  }
  jobValueModuleList(params).then(res => {
    if (res.code == '200') {
      personnelData.value = res.data
      getQualityEvaluationItemFun(res.data[currIndex.value])
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const submitForm = stepType => {
  const params = {
    enqId: props.enqId,
    modelId: personnelData.value[currIndex.value].modelId
  }
  jobNext(params).then(res => {
    if (res.code == '200') {
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submitForm('prevStep')
    })
    .catch(action => {
      ElMessage.info(action === 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action === 'cancel') emit('prevStep')
    })
}

const nextBtn = () => {
  emit('nextStep')
}

onMounted(() => {
  getDeptUserPostFun()
})
</script>

<style scoped lang="scss">
.department_main {
  display: flex;
  .personnel_item_wrap_left {
    width: 200px;
    padding-right: 15px;
    border-right: 1px solid #ccc;
    .personnel_item {
      line-height: 30px;
      padding: 0 8px;
      color: #525e6c;
      font-size: 14px;
      background: #f8f8f8;
      margin-bottom: 5px;
      font-weight: bold;
      cursor: pointer;

      &.completed {
        color: #0099fd;
        background: #eef5fb;

        .icon {
          display: block;
        }
      }

      &.curr {
        background: #0099fd;
        color: #fff;

        .icon {
          display: block;
          color: #fff;

          &.disc {
            background: #fff;
          }
        }
      }

      .icon {
        float: right;
        font-weight: bold;
        line-height: 30px;
        text-align: center;
        color: #0099fd;

        &.disc {
          width: 8px;
          height: 8px;
          margin: 10px 4px 0 auto;
          border-radius: 50%;
          background: #ffc000;
        }
      }
    }
  }
  .personnel_item_wrap_right {
    padding-left: 15px;
    width: calc(100% - 200px);
    .project_item {
      padding: 10px 0;
      font-size: 16px;
      font-weight: bold;
      .moduleName {
        color: #0099ff;
      }
      .item {
        color: #333;
      }
    }
  }
}
</style>
