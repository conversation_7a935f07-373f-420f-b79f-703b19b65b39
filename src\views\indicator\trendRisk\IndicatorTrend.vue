<template>
  <div class="IndicatorTrend all">
    <div class="key-index">
      <div class="table-main">
        <el-table ref="tableDataRef" :data="tableData" highlight-current-row style="width: 100%">
          <el-table-column type="index" align="center" label="序号" width="80" />
          <el-table-column prop="kpiName" label="指标名称" width="220" />
          <el-table-column prop="targetClass" label="目标类别" width="180" />
          <el-table-column prop="targetCycle" label="目标期间" />
          <el-table-column prop="kpiUnit" label="指标单位" />
          <el-table-column prop="personLiable" label="责任人" />
          <el-table-column prop="targetValue" label="指标目标" />
          <el-table-column prop="actualPerformance" label="实际表现" />
          <el-table-column prop="achieveRate" label="达成率" />
          <el-table-column prop="gap" label="差距" />
          <el-table-column>
            <template #default="scope">
              <div v-if="scope.row.kpiName == '库存周转天数'" class="btn" @click="isActive = true">指标预测</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 已选择 -->
    <div class="active" v-if="isActive">
      <div class="title">
        <div class="text">已选指标：</div>
        <div class="name">库存周转天数</div>
      </div>
      <div class="page-title-line">趋势预测（库存周转天数）</div>
      <el-table :data="table1" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="kpiName" align="center" label="指标" />
        <el-table-column prop="kpiPeriod" align="center" label="指标周期" />
        <el-table-column prop="predictCycle" align="center" label="预测周期" />
        <el-table-column prop="kpiCycle" align="center" label="指标期间" />
        <el-table-column prop="baseValue" align="center" label="预测值（基准）" />
        <el-table-column prop="optimisticValue" align="center" label="预测值（乐观）" />
        <el-table-column prop="pessimisticValue" align="center" label="预测值（悲观）"> </el-table-column>
      </el-table>
      <div class="page-title-line">趋势预测说明（库存周转天数）</div>
      <div v-for="(item, index) in text" :key="index">
        <div class="title">
          <div class="name">{{ index + 1 }}、{{ item.name }}</div>
        </div>
        <div class="decode">
          <div class="paragraph" v-for="(item1, index1) in item.children" :key="index1">
            <a>{{ index1 + 1 }}、{{ item1.title }}：</a>
            {{ item1.details }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
defineOptions({ name: 'IndicatorTrend' })
import { trendRisk } from '@/assets/data/data9.js'
const openAi = inject('openAi')

const isActive = ref(false)
const tableData = ref(trendRisk.kpiTrendPre.kpiTrendPreList)
const tableDataRef = ref(null)
onMounted(() => {
  tableDataRef.value.setCurrentRow(trendRisk.kpiTrendPre.kpiTrendPreList[2])
})
const table1 = ref(trendRisk.kpiTrendPre.trendPreResult)
const text = [
  {
    name: '预测逻辑',
    children: [
      {
        title: '供应链稳定性下降',
        details:
          '2026 年全球物流网络可能因地缘政治冲突加剧，导致原材料运输周期延长 15%-20%，到货不及时引发生产端缓冲库存增加；港口拥堵或空运成本飙升迫使企业提前储备 30% 以上安全库存，直接推高库存总量。'
      },
      {
        title: '需求预测偏差扩大',
        details:
          '市场消费趋势受经济周期波动影响，若 2026 年出现区域性经济衰退，消费者购买力下降导致实际需求低于预测值 20%-25%，已生产商品滞销积压，周转天数因库存消化速度放缓而增加。'
      },
      {
        title: '库存管理效率降低',
        details:
          '仓储自动化系统老化未及时升级，订单拣货效率下降 10%，库存盘点误差率从 3% 上升至 8%，导致滞销品未及时清理，有效库存占比从 90% 降至 75%，周转效率被动拉低。'
      },
      {
        title: '采购策略失误',
        details:
          '为规避原材料涨价风险，采购部门批量囤货导致部分原材料库存超过 6 个月使用量，而同期产品设计变更使该类原材料需求下降 40%，专用料件积压形成呆滞库存，占比超过库存总额 15%。'
      },
      {
        title: '促销活动效果不及预期',
        details:
          '季末清库存促销因定价策略僵化，折扣力度不足或渠道覆盖缺失，滞销品去化率仅达 40%（目标为 70%），导致应周转库存滞留仓库，延长平均周转时间。'
      }
    ]
  },
  {
    name: '最容易受到哪些指标影响',
    children: [
      {
        title: '库存持有成本率',
        details:
          '库存周转天数每增加 1 天，库存持有成本率约上升 0.4%（基于历史数据测算），悲观情景下周转天数增加 3 天，直接导致该指标超预期上升，且与库存总量正相关，形成 “周转慢 - 成本高” 的强关联。'
      },
      {
        title: '现金流周转率',
        details:
          '库存占用资金每增加 1%，现金流周转率下降约 0.6%，因库存周转放缓导致的资金沉淀会直接挤压经营性现金流，尤其在悲观情景下，应收账款周期同步延长时，资金链紧张风险加剧。'
      },
      {
        title: '订单满足率',
        details:
          '有效库存占比每下降 5%，订单满足率下降约 3%，滞销库存积压导致 SKU 可得性降低，紧急订单履约能力削弱，直接影响客户交付体验，形成 “库存高 - 交付差” 的负向循环。'
      },
      {
        title: '滞销库存占比',
        details:
          '周转天数与滞销库存占比呈显著正相关（相关系数 0.85），库龄超 90 天的滞销品每增加 10%，周转天数延长约 2 天，悲观情景下两者相互强化，导致库存结构恶化。'
      },
      {
        title: '供应商交付准时率',
        details:
          '因库存积压导致采购计划频繁调整，供应商订单取消率上升 15%，触发供应商产能调配优先级下降，交付准时率从 90% 降至 75%，形成 “需求波动 - 供应延迟 - 库存进一步积压” 的链式反应。'
      }
    ]
  },
  {
    name: '可能重点影响的关键指标',
    children: [
      {
        title: '库存持有成本率',
        details:
          '库存周转天数从 75 天增加至 78 天（增加 3 天），按年化计算库存持有成本率将上升 1.2%-1.5%。假设库存平均价值 5 亿元，年持有成本率 8%，则成本增加 120-150 万元，侵蚀利润率。'
      },
      {
        title: '现金流周转率',
        details:
          '库存占用资金增加导致经营性现金流减少，若库存总额上升 5%，现金流周转率可能下降 2%-3%，影响短期偿债能力和投资能力。'
      },
      {
        title: '订单满足率',
        details:
          '无效库存增加导致有效库存不足，紧急订单响应率从 95% 降至 85%，客户订单交付延迟率上升 10%，引发客户投诉率增加 20% 以上。'
      },
      {
        title: '滞销库存占比',
        details: '周转天数延长伴随滞销品积压，占比从 8% 升至 15%，需计提更多减值准备，影响资产负债表质量。'
      },
      {
        title: '供应商满意度',
        details:
          '为消化库存可能推迟付款周期，应付账款周转率下降，供应商账期违约率上升，导致供应商交货优先级降低，形成恶性循环。'
      }
    ]
  },
  {
    name: '需要重点改善的能力',
    children: [
      {
        title: '供应链韧性建设能力',
        details:
          '需建立多区域供应商备份体系，关键物料本地产能储备达 40% 以上，物流应急预案覆盖海运、空运、陆运多渠道，通过供应链风险评估系统实时监控运输时效，将原材料到货延迟率控制在 5% 以内。'
      },
      {
        title: '需求预测精准度提升能力',
        details:
          '引入 AI 预测模型融合宏观经济数据、消费者调研、历史销售数据，季度预测误差率从 20% 降至 10% 以下；建立市场反馈快速响应机制，新品试销期缩短至 4 周，及时调整生产计划。'
      },
      {
        title: '库存管理数字化能力',
        details:
          '升级 WMS 系统实现智能货位管理，引入 RFID 盘点技术将盘点误差率控制在 2% 以内，设置滞销品自动预警阈值（库龄超 90 天触发），每月呆滞库存处理率不低于 60%。'
      },
      {
        title: '采购策略动态优化能力',
        details:
          '建立原材料价格波动预警模型，采用阶梯式采购策略（价格波动 ±10% 时自动调整采购量），专用料件采购前与研发部门联动确认技术稳定性，将呆滞料占比控制在 5% 以下。'
      },
      {
        title: '促销策略敏捷调整能力',
        details:
          '基于实时库存数据和市场竞品分析，动态制定促销组合（如买赠、限时折扣、跨渠道联动），滞销品去化率目标提升至 60% 以上；建立客户分层营销体系，高价值客户复购率提升 15% 以拉动有效周转。'
      }
    ]
  }
]
</script>
<style lang="scss" scoped>
@import './trendRisk.scss';
</style>
