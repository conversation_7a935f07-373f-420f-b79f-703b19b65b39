import service from '../request'

// 查看指标类别
export const indicatorCategory = data =>
  service({
    url: '/entp/ce/kpi/class',
    method: 'get',
    params: data
  })

// 查看指标列表
export const indicatorList = data =>
  service({
    url: '/entp/ce/kpi/page',
    method: 'get',
    params: data
  })

// 分类查看指标
export const indicatorCategoryList = data =>
  service({
    url: '/entp/ce/kpi/kpiClass',
    method: 'get',
    params: data
  })

// 查看指标能力列表
export const indicatorAbilityList = data =>
  service({
    url: '/entp/ce/kpi/pageAbility',
    method: 'get',
    params: data
  })

// 查看岗位
export const getPosition = data =>
  service({
    url: '/entp/ce/postKpi/post',
    method: 'get',
    params: data
  })

// 查看指标-分页
export const getPositionList = data =>
  service({
    url: '/entp/ce/postKpi/page',
    method: 'get',
    params: data
  })
