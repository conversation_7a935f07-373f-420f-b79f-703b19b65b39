<script setup>
import SectionTab from '../components/sectionTab.vue'
import indicatorInfo from './indicatorInfo.vue'
import orgRelevancy from './orgRelevancy.vue'
import staffRelevancy from './staffRelevancy.vue'
import projectRelevancy from './projectRelevancy.vue'
defineOptions({ name: 'libraryTendingIndex' })
const router = useRouter()
const route = useRoute()
const tabContentList = ref([indicatorInfo, orgRelevancy, staffRelevancy, projectRelevancy])
const sectionTabCheckSign = ref(1)
const sectionTabList = ref([
  {
    name: '指标信息',
    code: 1
  },
  {
    name: '组织关联',
    code: 2
  },
  {
    name: '人员关联',
    code: 3
  },
  {
    name: '项目关联',
    code: 4
  }
])
const checkSecTab = c => {
  sectionTabCheckSign.value = c
}
</script>
<template>
  <div class="library_tending_wrap">
    <SectionTab
      :sectionTabList="sectionTabList"
      :sectionTabCheckSign="sectionTabCheckSign"
      @checkSecTab="checkSecTab"
    ></SectionTab>
    <div class="content-mian">
      <component :is="tabContentList[sectionTabCheckSign - 1]" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.library_tending_wrap {
}
</style>
