<template>
  <div class="training_activities_center clearfix">
    <div class="training_activities_item flex_row_between" v-for="(item, index) in listData" :key="item.id">
      <div class="progress_state">进行中</div>
      <div class="item_index">{{ index + 1 }}</div>
      <div class="item_content_wrap">
        <div class="item_content flex_row_between">
          <div class="item_content_list">
            <div class="list_title">项目名称</div>
            <div class="list_text">{{ item.projectName }}</div>
          </div>
          <div class="item_content_list">
            <div class="list_title">起止日期</div>
            <div class="list_text">
              <span class="list_num">{{ item.date }}</span>
            </div>
          </div>
          <div class="item_content_list">
            <div class="list_title">盘点进度</div>
            <div class="list_text">
              <span class="list_num">{{ item.progress }}</span
              >提交
              <span class="list_num">/</span>
              <span class="list_num">{{ item.progressTotal }}</span
              >总数
            </div>
          </div>
        </div>
      </div>
      <div class="item_content_wrap progress_details">
        <div class="item_content flex_row_around">
          <div class="item_content_list">
            <div class="list_title">完成度</div>
            <div class="list_text">
              <span class="list_num">{{ item.competenceDictionary }}</span
              >%
            </div>
          </div>
          <div class="item_content_list">
            <div class="list_title">完善中</div>
            <div class="list_text">
              <span class="list_num">{{ item.competenceDictionary }}</span
              >人
            </div>
          </div>
          <div class="item_content_list">
            <div class="list_title">未开始</div>
            <div class="list_text">
              <span class="list_num">{{ item.competenceDictionary }}</span
              >人
            </div>
          </div>
          <div class="item_content_list">
            <div class="list_title">未登录</div>
            <div class="list_text">
              <span class="list_num">{{ item.competenceDictionary }}</span
              >人
            </div>
          </div>
        </div>
      </div>
      <div class="item_oper flex_row_start">
        <div class="item_oper_list" v-link="'/talentReviewHome/reviewProgressQuality/RPmanage/RPdetails?id=' + item.id">
          <i class="icon el-icon-s-data"></i>
          <div class="text">盘点进度</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  listData: {
    type: Array,
    required: true
  }
})
</script>

<style scoped lang="scss">
.training_activities_center {
  .training_activities_item {
    position: relative;
    padding: 16px 8px 16px 30px;
    margin-bottom: 8px;
    border: 1px solid #d9d9d9;
    overflow: hidden;
    .progress_state {
      width: 100px;
      text-align: center;
      position: absolute;
      background: chocolate;
      transform: rotate(-45deg);
      transform-origin: center;
      font-size: 12px;
      color: #fff;
      left: -32px;
      top: 8px;
    }
    .item_index {
      width: 50px;
      font-weight: bold;
      font-size: 20px;
      color: #0070c0;
    }
    .item_content_wrap {
      width: 50%;
      padding: 0 8px;
      .item_content {
        padding-right: 10px;
        .item_content_list {
          color: #525e6c;
          .list_title {
            font-weight: bold;
            margin-bottom: 8px;
          }
          .list_num {
            color: #0070c0;
            font-weight: bold;
            font-size: 16px;
          }
        }
      }
    }
    .progress_details {
      width: 30%;
      border-left: 1px solid #d9d9d9;
    }
    .item_oper {
      width: 100px;
      padding: 0 8px;
      border-left: 1px solid #d9d9d9;
      align-items: center;
      color: #0099fd;
      text-align: center;
      .icon {
        font-size: 30px;
      }
      &_list {
        width: 100%;
        cursor: pointer;
      }
    }
  }
}
</style>
