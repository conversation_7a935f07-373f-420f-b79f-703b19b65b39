<template>
  <!--
    element-plus 单选树组件
    支持：
    1. 外部传入树数据 treeData
    2. 支持 v-model 或内部自管理选中项
    3. 支持默认展开项、是否展开所有、默认展开层级
    4. 支持是否可以取消选中
    5. 选中变化时 emit clickCallback
  -->
  <el-tree
    ref="treeRef"
    :data="treeData"
    :node-key="nodeKey"
    :default-expand-all="defaultExpandAll"
    :default-expanded-keys="expandedKeys"
    :highlight-current="true"
    :expand-on-click-node="false"
    :props="{ label: labelKey }"
    :show-checkbox="false"
  >
    <template #default="{ data }">
      <span class="custom-tree-node" :class="{ selected: selectedKey == data[nodeKey] }">
        <el-radio :model-value="selectedKey" :label="data[nodeKey]" @click="() => handleRadioChange(data)">
          {{ data[labelKey] }}
        </el-radio>
      </span>
    </template>
  </el-tree>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, nextTick, computed } from 'vue'

// props 说明：
// treeData: 树结构数据，必填
// modelValue: v-model 绑定的选中key，可选
// defaultExpandedKeys: 默认展开项
// defaultExpandAll: 是否展开所有项
// canCancel: 是否可以取消选中
// nodeKey: 节点唯一key字段
// labelKey: 节点显示文本字段
// needCheckedFirstNode: 是否默认选中第一个节点
// expandedLevel: 默认展开层级（defaultExpandAll为false时生效）
const props = defineProps({
  treeData: { type: Array, required: true },
  modelValue: { type: [String, Number], default: undefined }, // v-model 可选
  defaultExpandedKeys: { type: Array, default: () => [] },
  defaultExpandAll: { type: Boolean, default: true },
  canCancel: { type: Boolean, default: false },
  nodeKey: { type: String, default: 'code' },
  labelKey: { type: String, default: 'value' },
  needCheckedFirstNode: { type: Boolean, default: true },
  expandedLevel: { type: Number, default: 3 }
})
// emit 说明：
// update:modelValue: v-model 选中项变化
// clickCallback: 选中项变化时，返回 (key, 是否叶子节点, 节点数据)
const emit = defineEmits(['update:modelValue', 'clickCallback'])
const treeRef = ref(null)
const expandedKeys = ref([...props.defaultExpandedKeys])
const innerSelected = ref('') // 内部自管理选中项

// 统一选中项（外部v-model优先，否则用内部）
const selectedKey = computed(() => (props.modelValue !== undefined ? props.modelValue : innerSelected.value))

// 选中项变化时高亮
watch(
  () => selectedKey.value,
  val => {
    console.log('selectedKey', val)
    nextTick(() => {
      treeRef.value && treeRef.value.setCurrentKey(val)
    })
  }
)

// 自动选中第一个节点
watch(
  () => props.treeData,
  val => {
    if (val && val.length > 0 && props.needCheckedFirstNode) {
      const key = val[0][props.nodeKey]
      if (props.modelValue == undefined) {
        innerSelected.value = key
        emit('clickCallback', key)
      } else if (!props.modelValue) {
        emit('update:modelValue', key)
      }
      nextTick(() => {
        treeRef.value && treeRef.value.setCurrentKey(key)
      })
    }
    if (!props.defaultExpandAll) {
      expandedKeys.value = []
      getExpandKeys(val, props.expandedLevel)
    }
  },
  { immediate: true }
)

// 递归获取默认展开keys
function getExpandKeys(data, level, curLevel = 1) {
  if (!Array.isArray(data)) return
  data.forEach(item => {
    if (curLevel <= level) {
      expandedKeys.value.push(item[props.nodeKey])
    }
    if (item.children && item.children.length) {
      getExpandKeys(item.children, level, curLevel + 1)
    }
  })
}

// 处理节点点击和radio点击
function handleNodeClick(data) {
  let lastNode = !(data.children && data.children.length > 0)
  const currentKey = data[props.nodeKey]
  const selected = selectedKey.value
  if (selected == currentKey) {
    if (props.canCancel) {
      if (props.modelValue !== undefined) {
        emit('update:modelValue', '')
      } else {
        innerSelected.value = ''
        console.log(innerSelected.value)
      }
      emit('clickCallback', '', lastNode, data)
      treeRef.value && treeRef.value.setCurrentKey(null)
    }
    // 不可取消时，重复点击已选中节点不触发 emit
  } else {
    if (props.modelValue !== undefined) {
      emit('update:modelValue', currentKey)
    } else {
      innerSelected.value = currentKey
    }
    emit('clickCallback', currentKey, lastNode, data)
    treeRef.value && treeRef.value.setCurrentKey(currentKey)
  }
}
function handleRadioChange(data) {
  handleNodeClick(data)
}
</script>

<style scoped lang="scss">
.custom-tree-node {
  display: flex;
  align-items: center;
  .el-radio {
    margin-right: 4px;
  }
  &.selected {
    color: var(--el-color-primary);
    font-weight: bold;
  }
}
</style>
