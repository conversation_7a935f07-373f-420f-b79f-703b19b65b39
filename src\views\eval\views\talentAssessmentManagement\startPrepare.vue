<template>
  <div class="start_prepare bg_write">
    <div class="page_main_title">
      {{ evalType }} / {{ evalName }}
      <div class="goback_geader" @click="goback()">
        <i class="el-icon-arrow-left"></i>返回
      </div>
    </div>
    <div class="page_section">
      <div class="page_second_title">项目背景</div>
      <div class="text_content marginT_20">
        <p>您好，感谢您在百忙之中参与本次测评；</p>
        <br />
        <p>
          公司的核心能力提升，只有在测评诊断过程中得到广大员工的积极参与和支持，才能帮助企业深度洞察核心能力的短板与差距，我们期待着您作出客观的评价，并表达您的期望。您的认真回答与表达，将为公司的业务能力与核心岗位能力的提升方案制定提供非常重要的依据。
        </p>
        <p>
          1、本次问卷为不记名问卷，所有问题的答案没有对或错之分。北京兮易作为专注核心能力测评、分析改善的咨询公司，我们会对您的回答内容进行保密。
        </p>
        <p>
          2、请仔细阅读每一道测评问题，并结合公司相关业务以及岗位职能认真思考，对每个测评能力项进行精准的判断。
        </p>
        <p>
          3、如果出于某些原因，想对个别问题的实际情况进行补充，请在问题下方的输入框中进行补充，以帮助企业针对性的提升该项能力。
        </p>
        <p>
          4、答题过程中，您可以随时退出，系统会自动保存你的答题结果，下次继续答题时会自动定位到最近一题。
        </p>
        <p>
          5、如有部分测评项目可能与公司或岗位不相关，请选择最接近的一项，并在下方问题测评中给出相应星级的测评，如与公司匹配度、与本岗位匹配度根据匹配程度给出1~3分等。
        </p>
        <p>
          6、测评完成后请对本次测评进行评价，评价包括对参与测评过程的评价以及通过本次测评您建议公司可以采取的一些措施。
        </p>
      </div>
      <div class="page_second_title marginT_30">注意事项</div>
      <div class="text_content marginT_20">
        <p>
          为了顺利完成测评，请在没有外界干扰的环境下进行测评，并关闭一些无关程序，如下载工具等，以确保网络连接畅通
        </p>
        <p>
          如遇到网速较慢、网页卡死等情况，请关闭测评页面，稍后更换较好的网络环境后重新登录，继续作答
        </p>
        <p>
          如遇突发情况，如断网、接听电话、电脑死机、断电等，请关闭浏览器或计算机，当可以作答时再次进行测评
        </p>
        <p>
          请保证您在不被打扰的状态下完成所有题目，点击“提交”之后，您将看到“恭喜您已经成功提交问卷，请为本次测评进行评价”信息，这代表您已成功完成本次调研，在下方对本次测评进行评价打分提交后，系统自动关闭窗口这表示您已全部完成本次测评
        </p>
      </div>
    </div>
    <div class="marginT_30 align_center">
      <el-button class="page_confirm_btn" type="primary" @click="toStart">{{
        status ? "下一步" : "开始测评"
      }}</el-button>
    </div>
  </div>
</template>

<script>
import {
  getEvalInfo,
  getEvalUserInfo,
  getRelationUser,
  getDevelopmentFlag,
} from "../../request/api";
export default {
  name: "startPrepare",
  data() {
    return {
      evalId: this.$route.query.evalId,
      evalName: "",
      evalType: "",
      evalTypeObj: {
        1: "调研问卷",
        2: "岗能测评",
        3: "业务测评",
      },
      status: true, //是否跳转到评价关系确认页面
      jumpStatus: false, // 是否跳转至 对下属发展建议页面
    };
  },
  created() {
    this.getEvalInfoFun();
    this.getDevelopmentFlag();
    this.getEvalUserInfoFun();
  },
  methods: {
    toStart() {
      let path = "";

      if (this.status) {
        path = "/talentAssessment/talentAssessmentManagement/confirmEvaluation";
      } else {
        // switch (this.jumpStatus) {
        //   case 0:
        //     path = "/talentAssessment/modelAnswer";
        //     break;
        //   case 1:
        //     path = "/talentAssessment/personalSummary";
        //     break;
        //   case 2:
        //     path = "/talentAssessment/staffCheckEvaluate";
        //     break;
        //   default:
        //     path = "/talentAssessment/modelAnswer";
        //     break;
        // }
        // path =
        //   this.jumpStatus == "0"
        //     ? "/talentAssessment/modelAnswer"
        //     : this.jumpStatus == 1
        //     ? "/talentAssessment/personalSummary"
        //     : "/talentAssessment/staffCheckEvaluate";
        path = "/talentAssessment/modelAnswer";
      }
      this.$router.push({
        path: path,
        query: {
          evalId: this.evalId,
          type: "eval",
        },
      });
    },
    goback() {
      this.$router.push(
        "/talentAssessment/talentAssessmentManagement/evaluationItemList"
      );
    },
    getEvalInfoFun() {
      getEvalInfo({ evalId: this.evalId }).then((res) => {
        this.evalName = res.evalName;
        this.evalType = this.evalTypeObj[res.evalType];
      });
    },
    getDevelopmentFlag() {
      getDevelopmentFlag({ evalId: this.evalId }).then((res) => {
        console.log(res);
        this.jumpStatus = res;
      });
    },
    getEvalUserInfoFun() {
      getEvalUserInfo({ evalId: this.evalId }).then((res) => {
        getRelationUser({ evalId: this.evalId }).then((r) => {
          console.log(r);
          let flag = true;
          this.peerList = r.peer;
          this.coopList = r.coop;
          if (r.peer.length == 0 && r.coop.length == 0) {
            flag = false;
          }

          if (res && flag) {
            // 进入
            this.status = true;
          } else {
            this.status = false;
          }
        });
      });
    },
    getRelationUserFun() {
      getRelationUser({ evalId: this.evalId }).then((res) => {
        console.log(res);
        this.peerList = res.peer;
        this.coopList = res.coop;
      });
    },
  },
};
</script>

<style scoped lang="scss">
.start_prepare {
  line-height: 28px;
}
</style>