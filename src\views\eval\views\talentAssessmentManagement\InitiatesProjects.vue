<template>
  <div class="Initiates_projects_wrap bg_write">
    <div class="page_main_title">
      测评项目管理
      <div
        class="goback_geader"
        v-link="
          '/talentAssessment/talentAssessmentManagement/evaluationItemList'
        "
      >
        <i class="el-icon-arrow-left"></i>返回
      </div>
    </div>
    <div class="page_section">
      <div class="talent_raview_main padd_TB_16">
        <step-bar
          :stepData="stepData"
          :needClick="!isEdit"
          @stepClick="stepClick"
          :currentIndex="currentIndex"
        ></step-bar>
        <div class="marginT_30">
          <component
            :is="compArr[currentIndex]"
            :evalId="evalId"
            :isEdit="isEdit"
            @getEvalId="getEvalId"
            @prevStep="prev"
            @nextStep="next"
          ></component>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import stepBar from "@/components/talent/stepsComps/stepBar";
import projectsBasicInfo from "./InitiatesProjectsComponents/projectsBasicInfo";
import chooseTestee from "./InitiatesProjectsComponents/chooseTestee";
import chooseEvalObj from "./InitiatesProjectsComponents/chooseEvalObj";
import evalRelationship from "./InitiatesProjectsComponents/evalRelationship";
import reportScope from "./InitiatesProjectsComponents/reportScope";
import allocation from "./InitiatesProjectsComponents/allocation";
import { getEvalInfo } from "../../request/api";
export default {
  name: "InitiatesProjects",
  components: {
    stepBar,
  },
  data() {
    return {
      isEdit: true,
      evalId: this.$route.query.evalId || "",
      type: "EVAL_TYPE",
      compArr: [
        projectsBasicInfo,
        chooseEvalObj,
        evalRelationship,
        reportScope,
        allocation,
      ],
      currentIndex: 0,
      stepData: [
        {
          name: "项目基本信息",
          state: "inProgress",
          enqProgress: "Y",
          code: 0,
        },
        {
          name: "选择测评对象",
          state: "inComplete",
          enqProgress: "N",
          code: 1,
        },
        {
          name: "确认评价关系",
          state: "inComplete",
          enqProgress: "N",
          code: 2,
        },
        {
          name: "设置报告范围",
          state: "inComplete",
          enqProgress: "N",
          code: 3,
        },
        {
          name: "配置与启动",
          state: "inComplete",
          enqProgress: "N",
          code: 4,
        },
      ],
    };
  },
  created() {
    // this.currentIndex = this.currentIndex - 1;
    this.evalId = this.$route.query.evalId || "";
    console.log(this.evalId);
    if (this.evalId) {
      getEvalInfo({ evalId: this.evalId }).then((res) => {
        this.isEdit = res.evalStatus < 2 ? true : false;
        this.stepData.map((item) => {
          item["enqProgress"] = this.isEdit ? "N" : "Y";
        });
      });
    }
  },
  mounted() {},
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getEvalId(evalId) {
      this.evalId = evalId;
    },
    prev: function () {
      if (this.currentIndex == 0) {
        return false;
      }
      this.stepData[this.currentIndex].enqProgress = "Y";
      this.currentIndex--;
    },
    next: function () {
      if (this.currentIndex == this.stepData.length - 1) {
        return false;
      }
      this.stepData[this.currentIndex].enqProgress = "Y";
      this.currentIndex++;
    },
    stepClick(code, index) {
      console.log(index);
      this.currentIndex = index;
    },
  },
};
</script>

<style scoped lang="scss">
.Initiates_projects_wrap {
  .talent_raview_btn_wrap {
    text-align: center;
  }
}

.step_btn_wrap {
  text-align: center;
  padding-top: 30px;
}

.el-checkbox.is-bordered + .el-checkbox.is-bordered {
  margin-left: 0;
}

.evaluation_project_center .el-form-item__content .el-range-separator {
  width: 8%;
}
</style>
