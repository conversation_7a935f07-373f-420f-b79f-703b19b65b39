<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'

defineOptions({ name: 'process' })

const columns = ref([
  {
    type: 'index',
    label: '序号',
    width: 50
  },
  {
    label: '策略',
    prop: 'name',
    width: 100
  },
  {
    label: '建议应用组织',
    prop: 'org',
    width: 90
  },
  {
    label: '应用原因',
    prop: 'reason'
  },
  {
    label: '应用建议',
    prop: 'suggestion'
  },
  {
    label: '关联关键任务',
    prop: 'task',
    width: 70
  }
])
const tableData = ref([
  {
    name: '需求计划全流程端到端贯通',
    org: '计划部',
    reason: '核心能力测评：流程完整性得分 40 分，DNA 显示需求分析与生产计划断层率达 35%，跨系统数据一致性仅 65%',
    suggestion:
      '①打通销售订单 - 库存 - 产能数据链路，在 ERP 中嵌入需求传导模拟模块；②设置智能校验规则，需求变更超 20% 时自动触发跨部门重排会；③开发需求波动热力图，实时预警区域市场异常需求'
  },
  {
    name: '需求计划全流程端到端贯通',
    org: '供应链管理部',
    reason: '核心能力测评：订单交付准时率 78%，DNA 显示需求变更响应时效达 48 小时，紧急插单成功率仅 55%',
    suggestion:
      '①建立需求变更分级响应机制，A 级变更（影响产能≥10%）4 小时内完成排产调整；②集成 MES 系统实时产能数据，需求评审时自动校验交付可行性；③开发插单成本计算器，量化展示紧急调整对供应链的影响'
  },
  {
    name: '需求计划全流程端到端贯通',
    org: '跨部门需求小组',
    reason: '核心能力测评：跨部门协作满意度 60 分，DNA 显示市场与计划部门需求匹配度仅 70%，新品需求误判率 25%',
    suggestion:
      '①推行 “市场 - 计划 - 生产” 联合需求评审会，新品需求需三方签字确认；②建立需求追溯机制，需求变更需附市场调研数据或客户确认单；③开发需求共识看板，实时同步各部门需求优先级'
  },
  {
    name: '需求计划全流程端到端贯通',
    org: '电商事业部',
    reason: '核心能力测评：大促需求预测准确率 68%，DNA 显示历史大促库存周转率仅 1.2，缺货率达 15%',
    suggestion:
      '①接入电商平台搜索 / 加购数据，训练大促专属预测模型；②设置预售需求自动分流规则，超过安全库存 50% 时触发供应商预生产；③开发库存健康度仪表盘，动态展示 SKU 备货风险'
  },
  {
    name: '需求计划全流程端到端贯通',
    org: '海外事业部',
    reason: '核心能力测评：海外需求响应周期 15 天，DNA 显示区域政策适配率 80%，汇率波动导致计划调整率 20%',
    suggestion:
      '①建立区域需求管理子系统，集成当地法规、关税、汇率等动态因子；②设计弹性计划模板，预留 10% 产能应对政策突变；③开发多币种需求对冲模型，自动匹配汇率波动下的最优供应方案'
  }
])

const columns2 = ref([
  {
    label: '组织名称',
    prop: 'name',
    width: 80
  },
  {
    label: '举措',
    prop: 'measure',
    width: 80
  },
  {
    label: '关联策略',
    prop: 'tactics',
    width: 100
  },
  {
    label: '关键行动',
    prop: 'action'
  },
  {
    label: '建议责任人',
    prop: 'personal',
    width: 90
  },
  {
    label: '输出成果',
    prop: 'result',
    width: 80
  },
  {
    label: '优先级',
    prop: 'priority',
    width: 80
  }
])

const tableData2 = ref([
  {
    name: '计划部',
    measure: '构建数据贯通链路',
    tactics: '需求计划全流程端到端贯通',
    action:
      '1. 打通销售订单、库存、产能数据接口，在 ERP 系统中开发「需求传导模拟模块」，实现需求变化对生产计划的实时影响测算；2. 制定《跨系统数据对账管理办法》，每日自动核查销售与计划数据一致性，差异超 5% 触发预警并生成《数据异常报告》；3. 建立需求计划版本管理机制，记录每次调整的市场依据与影响分析，形成《需求计划变更日志》供后续追溯。',
    personal: '计划部总监',
    result: '《数据接口开发方案》《跨系统对账规则》',
    priority: '非常高'
  },
  {
    name: '供应链管理部',
    measure: '优化产能匹配机制',
    tactics: '需求计划全流程端到端贯通',
    action:
      '1. 整合 MES 系统实时产能数据，在需求评审环节增加「产能可行性校验」模块，自动识别超负荷产线并推荐替代方案；2. 制定《紧急插单管理规范》，明确 A 级插单（影响交期≥7 天）的审批流程与产能调配规则，配套开发插单成本测算工具；3. 建立供应链响应时效 KPI，要求需求调整信息同步至供应商≤24 小时，超时自动触发高层介入。',
    personal: '供应链管理总监',
    result: '《产能匹配操作手册》《插单管理规范》',
    priority: '高'
  },
  {
    name: '跨部门需求小组',
    measure: '建立联合评审机制',
    tactics: '需求计划全流程端到端贯通',
    action:
      '1. 制定《跨部门需求评审流程》，明确市场、计划、生产部门的评审权责（如市场部负责需求真实性审核，生产部评估工艺可行性）；2. 开发「需求共识看板」，实时同步各部门评审意见，未达成共识的需求自动进入二次研讨流程；3. 每季度召开需求计划复盘会，输出《跨部门协作效能报告》，针对分歧率超 30% 的环节启动流程优化。',
    personal: '跨部门需求小组组长',
    result: '《需求评审流程手册》《协作效能报告》',
    priority: '高'
  },
  {
    name: '电商事业部',
    measure: '强化大促需求管理',
    tactics: '需求计划全流程端到端贯通',
    action:
      '1. 接入电商平台大促日历与历史交易数据，建立「大促需求预测模型」，自动生成预售阶段的分品类备货计划；2. 开发「库存健康度预警系统」，实时监控大促商品库存周转率，低于 1.5 次 / 月触发补货预警并联动生产排产；3. 制定《电商大促需求对接规范》，明确预售订单转化为正式需求的规则与时间节点，减少计划波动。',
    personal: '电商运营总监',
    result: '《大促需求预测模型文档》《库存预警规则》',
    priority: '非常高'
  },
  {
    name: '海外事业部',
    measure: '适配跨境需求特性',
    tactics: '需求计划全流程端到端贯通',
    action:
      '1. 建立海外市场需求子系统，集成当地政策、关税、汇率等动态因子，自动生成符合区域合规要求的需求计划；2. 开发「多币种需求对冲模块」，根据汇率波动自动调整各区域供货比例，降低汇率风险对成本的影响；3. 制定《海外需求响应标准》，明确跨境需求调整的最小单位（如集装箱整箱量）与响应时效（常规需求≤72 小时，紧急需求≤24 小时）。',
    personal: '海外事业部总监',
    result: '《跨境需求管理系统方案》《海外响应标准手册》',
    priority: '高'
  }
])
</script>
<template>
  <div class="core">
    <div class="core-title">
      <div class="text">策略应用组织建议</div>
    </div>
    <SimplenessTable :columns="columns" :data="tableData"></SimplenessTable>
    <div class="core-title mt-6">
      <div class="text">关联的关键任务</div>
    </div>
    <SimplenessTable :columns="columns2" :data="tableData2"></SimplenessTable>
  </div>
</template>
<style lang="scss" scoped>
.core {
  .core-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
    color: #3d3d3d;
    margin-bottom: 13px;
    .index {
      width: 16px;
      height: 16px;
      background: #40a0ff;
      border-radius: 50%;
      text-align: center;
      line-height: 16px;
      color: #fff;
      font-weight: normal;
      margin-right: 7px;
    }
  }
}
</style>
