<template>
  <el-table-column
    :show-overflow-tooltip="overflowTooltip"
    :prop="colObj.prop"
    :key="colObj.prop"
    :label="colObj.label"
    :sortable="colObj.sortable"
    :width="colObj.width"
    :min-width="colObj.minWidth ? colObj.minWidth : colMinWidth"
    :fixed="colObj.fixed"
    :class-name="colObj.className"
    :align="colObj.align"
    :formatter="colObj.formatterFun"
  >
    <template v-if="colObj.childCols && colObj.childCols.length > 0">
      <TableColumns v-for="col in colObj.childCols" :key="col.prop" :col-obj="col" :overflowTooltip="overflowTooltip" />
    </template>
  </el-table-column>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  colObj: {
    type: Object,
    default: () => ({})
  },
  overflowTooltip: {
    type: Boolean,
    default: true
  },
  colMinWidth: {
    type: [String, Number],
    default: 50
  }
})
</script>

<style lang="scss" scoped></style>
