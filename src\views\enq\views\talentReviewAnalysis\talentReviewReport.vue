<template>
  <div class="talent_review_report_wrap">
    <div class="page_main_title">人才盘点报告</div>
    <div class="page_section">
      <!--            <div class="page_second_title marginB_16">人才盘点报告</div>-->
      <div class="talent_review_report_center clearfix">
        <tabsLink :tabsData="tabsData" :isDefaultTheme="true"></tabsLink>
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import tabsLink from '@/components/talent/tabsComps/tabsLink.vue'

const tabsData = [
  {
    id: '1',
    name: '项目视图',
    path: '/talentReviewHome/talentReviewAnalysis/TRreport/projectView'
  },
  {
    id: '2',
    name: '列表视图',
    path: '/talentReviewHome/talentReviewAnalysis/TRreport/listView'
  }
]
</script>

<style scoped lang="scss"></style>
