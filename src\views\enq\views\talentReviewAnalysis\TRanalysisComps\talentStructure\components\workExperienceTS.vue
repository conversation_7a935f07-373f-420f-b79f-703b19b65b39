<template>
    <div class="talent_main">
        <div class="aside_filter_wrap">
            <asideFilterBar
                :filterData="filterData"
                @getCode="getCode"
            ></asideFilterBar>
        </div>
        <div class="talent_number_content page_section flex_row_wrap_start">
            <div class="content_item el-col-8">
                <div class="content_item_main">
                    <div class="content_item_title">工作经验</div>
                    <div class="content_item_content" id="work"></div>
                </div>
            </div>
            <div class="content_item el-col-8">
                <div class="content_item_main">
                    <div class="content_item_title">同岗位工作经验</div>
                    <div class="content_item_content" id="samePost"></div>
                </div>
            </div>
            <div class="content_item el-col-8">
                <div class="content_item_main">
                    <div class="content_item_title">行业经验</div>
                    <div class="content_item_content" id="industry"></div>
                </div>
            </div>
            <!-- <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">具体岗位</div>
                    <div class="content_item_content">
                        <div class="post_list_wrap">
                            <div
                                class="post_list"
                                v-for="list in postList"
                                :key="list.code"
                            >
                                {{ list.name }}
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title clearfix">
                        具体职位任职资格要求
                        <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
                    </div>
                    <div class="content_item_content">
                        <tableComponet
                            @handleSizeChange="handleSizeChange"
                            @handleCurrentChange="handleCurrentChange"
                            :tableData="tableData"
                            :needIndex="true"
                        ></tableComponet>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
 
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js"
import { getExperience, experiencePost, queryJobQualificationsList, exportData } from "../../../../../request/api.js"
import tableComponet from "@/components/talent/tableComps/tableComponent.vue"
import asideFilterBar from "../../asideFilterBar.vue"

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')
const expType = ref('')
const dictCode = ref('')
const postList = ref([])
const filterData = ref([])
const page = ref(1)
const size = ref(10)

const work = reactive({
    addEvent: true,
    defaultChecked: true,
    data: []
})

const samePost = reactive({
    addEvent: true,
    data: []
})

const industry = reactive({
    addEvent: true,
    data: []
})

const tableData = reactive({
    columns: [
        {
            label: "职位名称",
            prop: "job_name"
        },
        {
            label: "所属组织",
            prop: "org_name"
        },
        {
            label: "职位族群",
            prop: "parent_job_class_name"
        },
        {
            label: "职位序列",
            prop: "job_class_name"
        },
        {
            label: "职层",
            prop: "job_level_name"
        },
        {
            label: "职等",
            prop: "job_grade_name"
        },
        {
            label: "工作经验",
            prop: "workExperienceValue"
        },
        {
            label: "同岗位经验",
            prop: "postExperienceValue"
        },
        {
            label: "行业经验",
            prop: "industryExperienceValue"
        }
    ],
    data: [],
    page: {
        current: 1,
        total: 0,
        size: 10
    }
})

const initChart = () => {
    echartsRenderPage("work", "YBar", "230", "250", work, chartCallbackWork)
    echartsRenderPage("samePost", "YBar", "230", "250", samePost, chartCallbackPost)
    echartsRenderPage("industry", "YBar", "230", "250", industry, chartCallbackIndustry)
}

const getExperienceFun = async () => {
    try {
        const params = {
            enqId: enqId.value,
            jobClassCode: jobClassCode.value,
            orgCode: orgCode.value
        }
        const res = await getExperience(params)
        if (res.code == "200") {
            const data = res.data
            work.data = window.$util.addPercentSign(data.work, "value")
            expType.value = "W"
            if (work.data.length > 0) {
                dictCode.value = work.data[0].code
            }
            await experiencePostFun(work.data)
            samePost.data = window.$util.addPercentSign(data.samePost, "value")
            industry.data = window.$util.addPercentSign(data.industry, "value")
            initChart()
        }
    } catch (error) {
        console.error(error)
    }
}

const chartCallbackWork = (data) => {
    expType.value = "W"
    dictCode.value = work.data[data.dataIndex].code
    echartsRenderPage("samePost", "YBar", "230", "250", samePost, chartCallbackPost)
    echartsRenderPage("industry", "YBar", "230", "250", industry, chartCallbackIndustry)
    experiencePostFun()
}

const chartCallbackPost = (data) => {
    expType.value = "P"
    dictCode.value = samePost.data[data.dataIndex].code
    echartsRenderPage("work", "YBar", "230", "250", work, chartCallbackWork)
    echartsRenderPage("industry", "YBar", "230", "250", industry, chartCallbackIndustry)
    experiencePostFun()
}

const chartCallbackIndustry = (data) => {
    expType.value = "I"
    dictCode.value = industry.data[data.dataIndex].code
    echartsRenderPage("work", "YBar", "230", "250", work, chartCallbackWork)
    echartsRenderPage("samePost", "YBar", "230", "250", samePost, chartCallbackPost)
    experiencePostFun()
}

const experiencePostFun = async (data) => {
    try {
        const params = {
            enqId: enqId.value,
            jobClassCode: jobClassCode.value,
            orgCode: orgCode.value,
            expType: expType.value,
            dictCode: dictCode.value
        }
        const res = await experiencePost(params)
        if (res.code == "200") {
            postList.value = res.data.post
        }
    } catch (error) {
        console.error(error)
    }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
    jobClassCode.value = jobClassCodeVal
    orgCode.value = orgCodeVal
    page.value = 1
    getExperienceFun()
    getTableData()
}

const handleCurrentChange = (pageVal) => {
    page.value = pageVal
    getTableData()
}

const handleSizeChange = (sizeVal) => {
    size.value = sizeVal
    getTableData()
}

const getTableData = async () => {
    try {
        const params = {
            enqId: enqId.value,
            jobClassCode: jobClassCode.value,
            orgCode: orgCode.value,
            current: page.value,
            size: size.value
        }
        const res = await queryJobQualificationsList(params)
        if (res.code == 200) {
            tableData.data = res.data
            tableData.page = res.page
        }
    } catch (error) {
        console.error(error)
    }
}

const exportDataFn = async () => {
    try {
        const params = {
            enqId: enqId.value,
            orgCode: orgCode.value,
            type: 'q'
        }
        const res = await exportData(params)
        window.$exportDownloadFile(res.data, '具体职位任职资格要求')
    } catch (error) {
        console.error(error)
    }
}

onMounted(() => {
    enqId.value = route.query.enqId
    filterData.value = route.attrs.filterData
    getExperienceFun()
    getTableData()
})
</script>
 
<style scoped lang="scss">
</style>