<script setup>
import Table from "../../components/table.vue";
const columns = ref([
  {
    label: "姓名",
    prop: "a",
  },
  {
    label: "岗位",
    prop: "b",
  },
  {
    label: "专业能力",
    prop: "c",
    align: "center",
  },
  {
    label: "优势工作类别",
    prop: "d",
    align: "center",
  },
  {
    label: "优势工作场景",
    prop: "e",
    align: "center",
  },
  {
    label: "优势决策层级",
    prop: "f",
    align: "center",
  },
  {
    label: "工作风险偏好",
    prop: "g",
    align: "center",
  },
  { label: "决策模式偏好", prop: "h", align: "center" },
  { label: "决策风格偏好", prop: "i", align: "center" },
  { label: "团队协作偏好", prop: "j", align: "center" },
  { label: "成本收益偏好", prop: "k", align: "center" },
  {
    label: "操作",
    prop: "l",
    slot: "lSlot",
    align: "center",
    width: 220,
  },
]);
const data = ref([
  {
    a: "王伟",
    b: "销售",
    c: "3",
    d: "规划设计类",
    e: "战略场景",
    f: "部门管理层",
    g: "高风险",
    h: "经济依赖型",
    i: "保守型",
    j: "同小组合作",
    k: "高投入高回报",
  },
]);

const columns2 = ref([
  {
    label: "ID",
    prop: "a",
  },
  {
    label: "一级能力",
    prop: "b",
  },
  {
    label: "二级能力",
    prop: "c",
  },
  {
    label: "能力得分",
    prop: "d",
  },
  {
    label: "同岗位排名",
    prop: "e",
  },
  {
    label: "优势工作类别",
    prop: "f",
  },
  {
    label: "优势工作场景",
    prop: "g",
  },
  {
    label: "优势决策层级",
    prop: "h",
  },
  {
    label: "工作风险偏好",
    prop: "i",
  },
  {
    label: "决策模式偏好",
    prop: "j",
  },
  {
    label: "决策风格偏好",
    prop: "k",
  },
  {
    label: "团队协作偏好",
    prop: "l",
  },
  {
    label: "成本收益偏好",
    prop: "m",
  },
  {
    label: "操作",
    prop: "n",
    slot: "nSlot",
    align: "center",
    width: 120,
  },
]);
const data2 = ref([
  {
    a: "1",
    b: "管理需求设计",
    c: "管理需求机制",
    d: "53",
    e: "3/20",
    f: "规划设计类",
    g: "战略场景",
    h: "部门管理层",
    i: "高风险",
    j: "经验依赖型",
    k: "保守型",
    l: "同小组合作",
    m: "高投入高回报",
  },
]);

const columns3 = ref([
  {
    label: "维度",
    prop: "a",
  },
  {
    label: "数据",
    prop: "b",
  },
  {
    label: "行业基准",
    prop: "c",
  },
  {
    label: "业务解读",
    prop: "d",
    width: 500,
  },
  // {
  //   label: "关联风险",
  //   prop: "e",
  //   width: 300,
  // },
]);
const data3 = ref([
  {
    a: "能力得分",
    b: "40/100",
    c: "及格线60+",
    d: "重大短板：低于岗位胜任阈值（家电业供应链总监要求≥65），预测失误将直接导致库存错配、产能浪费 ",
    e: "",
    f: "",
  },
]);

const columns4 = ref([
  {
    label: "优势标签",
    prop: "a",
  },
  {
    label: "战术价值",
    prop: "b",
    width: 500,
  },
  {
    label: "适用场景举例",
    prop: "c",
  },
  {
    label: "风险警示",
    prop: "d",
  },
]);
const data4 = ref([
  {
    a: "制度构建类",
    b: "擅长建立预测流程规范（如《预测数据录入SOP》） ",
    c: "新品上市前的预测框架搭建",
    d: "过度制度化降低灵活性",
    e: "",
    f: "",
  },
]);

const columns5 = ref([
  {
    label: "能力缺口",
    prop: "a",
    width: 200,
  },
  {
    label: "可能业务表现",
    prop: "b",
  },
  {
    label: "可能的管理后果",
    prop: "c",
  },
  // {
  //   label: "风险警示",
  //   prop: "d",
  // },
]);
const data5 = ref([
  {
    a: "预测模型落地能力不足",
    b: "主导的AI预测模型准确率波动大（促销期常跌破75%）",
    c: "导致安全库存冗余率高达22%（行业标杆≤15%）",
    d: "",
    e: "",
    f: "",
  },
]);

const columns6 = ref([
  {
    label: "改善领域",
    prop: "a",
    width: 200,
  },
  {
    label: "具体行动",
    prop: "b",
  },
  {
    label: "验证指标",
    prop: "c",
  },
  {
    label: "资源支持",
    prop: "d",
  },
]);
const data6 = ref([
  {
    a: "预测颗粒度细化",
    b: "建立核心SKU（占营收70%）的独立预测模型",
    c: "TOP50 SKU预测准确率提升至80%",
    d: "抽调2名数据科学家",
    e: "",
    f: "",
  },
]);

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex !== 4 && columnIndex !== 6) {
    if (rowIndex % 3 === 0) {
      return {
        rowspan: 3,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};
const aiJD = ref([
  {
    t: "",
    data: [
      {
        st: "1、决策偏好冲突（高风险业务场景）：冒险型决策风格 × 低投入高回报偏好",
        infoList: [
          {
            title: "",
            info: "典型矛盾：家电行业销售预测需严谨数据验证（如季节性波动分析），但刘威倾向“快速决策-最小成本试错”模式",
          },
          {
            title: "",
            info: "潜在后果-案例模拟：主导预测项目平均偏差率较高，2024年空调旺季预测仅凭历史数据拍板，未纳入天气因素（厄尔尼诺致冷夏），导致渠道库存积压；",
          },
        ],
      },
      {
        st: "2、协作局限（跨部门割裂）：单部门协作偏好 × 业务管理层决策层级",
        infoList: [
          {
            title: "",
            info: "潜在行为：闭环在供应链部门内做预测，拒绝销售/市场部数据输入（如促销计划、竞品动态）；",
          },
          {
            title: "",
            info: "潜在后果-案例模拟：未协同市场部获取预售数据，导致新品上市预测环节失效，引起销售损失；忽略销售部618活动增量计划，导致促销备货预测时效，引起销售损失；",
          },
        ],
      },
      {
        st: "3、成本收益错配（资源投入误区）：低投入高回报期待 × 预测复杂性现实",
        infoList: [
          {
            title: "",
            info: "潜在行为：压缩数据采购预算（如不购买第三方市场报告），依赖免费公开数据；",
          },
          {
            title: "",
            info: "潜在后果-案例模拟：依赖免费竞品监测数据（延迟 72 小时），未能提前识别小家电品牌「以旧换新」促销策略，错失市场反应窗口期，库存周转天数恶化，呆滞库存增加。",
          },
        ],
      },
    ],
  },
]);

const aiJD2 = ref([
  {
    title: "",
    info: "新品上市季：当刘威主导预测时，强制要求：备货量≤预测值的70%（预留30%安全缓冲）、首单生产批次拆分为3+1模式（3批常规+1批弹性产能）；",
  },
  {
    title: "",
    info: "季节性波动期：当刘威主导换季产品（如冬转春空调 / 风扇切换）预测时，强制要求：✓ 历史季末库存消化率＜75% 时，引入大数据验证机制，调整幅度≥20% 时需供应链总监审批；",
  },
  {
    title: "",
    info: " 供应链波动期：当刘威主导芯片 / 压缩机等关键部件紧缺期预测时，强制要求：预测模型需嵌入「部件交期指数」（供应商交付周期波动系数），交期延迟超 30 天时，预测产能自动下调 20% 并优先保障高毛利机型；建立「预测 - 产能」联动熔断机制：当部件缺口＞30%，立即启动「保供白名单」，预测资源向战略客户（如苏宁 / 京东）倾斜，零售渠道备货量压缩至预测值 50%每周召开「预测 - 采购 - 生产」三方对齐会，要求销售提报《渠道库存穿透表》（需细化至门店 SKU 库存天数），未达标者预测需求驳回率≥40%；",
  },
]);
const aiJD3 = ref([
  {
    title: "",
    info: "短期：任命销售预测专家（总监级）作为其副手，签署双签权协议；",
  },
  {
    title: "",
    info: "中期：将其KPI权重调整为「预测准确性40%+库存周转30%+跨部门协同30%」；",
  },

  {
    title: "",
    info: "长期：若2025Q3前未达能力及格线（≥60分），调岗至标准化流程管理部门。",
  },
  {
    title: "",
    info: "审批权约束：当任一预警触发时，自动提升刘威的决策审批至供应链VP（超越原权限）",
  },
  {
    title: "",
    info: "红黄牌机制：年度触发预警≥3次：黄牌（扣减年度奖金30%），造成实际损失＞¥5000万：红牌（调离预测岗位） ",
  },
  {
    title: "",
    info: "终极目标：通过刚性规则压缩刘威的决策自由度，将其冒险偏好限制在安全阈值内（家电业容忍偏差率±15%），同时利用行业特色工具（气候对冲、技术降规）构建护城河；其制度构建能力（优势项）可复用至供应链风控体系设计，实现人岗再匹配。",
  },
]);
</script>
<template>
  <div class="content-wrap">
    <div class="justify-between">
      <div class="page-title-line">人员能力概要</div>
      <div class=""><span class="pearl_blue">已选指标：</span>库存周转天数</div>
    </div>
    <Table
      :roundBorder="false"
      :columns="columns"
      :data="data"
      headerColor
      showIndex
    >
      <template v-slot:lSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>详情</el-button>
        <el-button class="ai_btn" type="primary" plain round>对比</el-button>
      </template>
    </Table>
    <div class="page-title-line marginT20">人员能力详情（刘威-供应链总监）</div>
    <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor>
      <template v-slot:nSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>AI解读</el-button>
      </template>
    </Table>
    <el-divider />
    <div class="page-title-line marginT20">
      人员能力AI解读（刘威-供应链总监-管理销售预测）
    </div>
    <div class="marginB20">一、能力定位与业务影响</div>
    <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor>
    </Table>
    <div class="marginT20 marginB20">二、行为模式根因诊断</div>
    <div v-for="item in aiJD">
      <div class="marginT20 marginB20 text_b">{{ item.t }}</div>
      <div class="dot_content_wrap marginB20" v-for="item1 in item.data">
        <div class="marginB20 text_b">{{ item1.st }}</div>
        <div class="item_wrap" v-for="item2 in item1.infoList">
          <span class="icon"></span>
          <span class="title"></span>
          <span class="info">{{ item2.info }}</span>
        </div>
      </div>
    </div>
    <div class="marginT20 marginB20">三、优势特性业务适配分析</div>
    <Table :roundBorder="false" :columns="columns4" :data="data4" headerColor>
    </Table>
    <div class="marginT20 marginB20">四、能力缺口问题映射分析</div>
    <Table :roundBorder="false" :columns="columns5" :data="data5" headerColor>
    </Table>
    <div class="marginT20 marginB20">五、典型场景风险预警与策略</div>
    <div class="dot_content_wrap marginB20">
      <div class="item_wrap" v-for="item in aiJD2">
        <span class="icon"></span>
        <span class="title"></span>
        <span class="info">{{ item.info }}</span>
      </div>
    </div>
    <div class="marginT20 marginB20">六、能力改善路径</div>
    <Table :roundBorder="false" :columns="columns6" :data="data6" headerColor>
    </Table>
    <div class="marginT20 marginB20">七、用人建议</div>
    <div class="text_b marginB20">
      结论：刘威在销售预测领域的短板已构成供应链系统性风险，尤其在旺季备战、新品上市等关键场景：
    </div>
    <div class="dot_content_wrap marginB20">
      <div class="item_wrap" v-for="item in aiJD3">
        <span class="icon"></span>
        <span class="title"></span>
        <span class="info">{{ item.info }}</span>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.content-wrap {
  .pearl_blue {
    color: #94a1af;
  }
  :deep .el-table {
    .ai_btn {
      padding: 0 15px;
      height: 24px;
      font-size: 14px;
    }
    &.self_table {
      width: 100%;
      --el-font-size-base: 14px;
      --el-table-header-text-color: #93abcb;
      --el-table-border-color: #c6dbf3;
      --el-table-tr-bg-color: transparent;
      --el-table-header-bg-color: transparent;
      background-color: transparent;

      .el-table__header .el-table__cell {
        // 隐藏table底边框
        background: #eaf4ff;
      }
    }
  }

  .dot_content_wrap {
    // margin: 0 0 40px 0;
    .item_wrap {
      margin-bottom: 0;
      .icon {
        margin: -2px 0px 0 10px;
      }
    }
  }
}
</style>
