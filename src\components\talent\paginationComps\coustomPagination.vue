<template>
  <div class="coustom_pagination_main">
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page="currentPage"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="onSizeChange"
      @current-change="onCurrentPageChange"
    />
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, onMounted } from 'vue'

const props = defineProps({
  total: {
    type: Number,
    default: 0
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100, 300]
  }
})

const emit = defineEmits(['pageChange'])

const pageSize = ref(props.pageSizes[0])
const currentPage = ref(1)

function onSizeChange(val) {
  pageSize.value = val
  currentPage.value = 1
  emit('pageChange', pageSize.value, currentPage.value)
}

function onCurrentPageChange(val) {
  currentPage.value = val
  emit('pageChange', pageSize.value, currentPage.value)
}

// 如果父组件 props 变动时需要同步
watch(
  () => props.pageSizes,
  newSizes => {
    if (Array.isArray(newSizes) && newSizes.length > 0) {
      pageSize.value = newSizes[0]
    }
  }
)
</script>

<style scoped lang="scss">
.coustom_pagination_main {
  padding-top: 30px;
  text-align: right;
}
</style>
