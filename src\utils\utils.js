export const randomString = (length = 8) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export const createRandomId = () => `_${Math.random().toString(36).substr(2)}`

export const formatter = (thistime, fmt) => {
  const $this = new Date(thistime)
  const o = {
    'M+': $this.getMonth() + 1,
    'd+': $this.getDate(),
    'h+': $this.getHours(),
    'm+': $this.getMinutes(),
    's+': $this.getSeconds(),
    'q+': Math.floor(($this.getMonth() + 3) / 3),
    S: $this.getMilliseconds()
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, ($this.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return fmt
}

export const goback = () => window.history.back()

export const isEmptyObj = obj => Object.keys(obj).length == 0

export const objHasEmpty = (obj, verifiKey, ignoreKey = []) => {
  if (!verifiKey || verifiKey.length == 0) {
    for (const key in obj) {
      if (!ignoreKey.includes(key) && Object.prototype.hasOwnProperty.call(obj, key)) {
        const element = obj[key]
        if (
          (Array.isArray(element) && element.length == 0) ||
          (Object.prototype.toString.call(element) == '[object Object]' && isEmptyObj(element)) ||
          (!element && element !== 0)
        ) {
          return true
        }
      }
    }
  } else {
    for (const key in obj) {
      if (verifiKey.includes(key) && Object.prototype.hasOwnProperty.call(obj, key)) {
        const element = obj[key]
        if (
          (Array.isArray(element) && element.length == 0) ||
          (Object.prototype.toString.call(element) == '[object Object]' && isEmptyObj(element)) ||
          (!element && element !== 0)
        ) {
          return true
        }
      }
    }
  }
}

export const findArrItem = (arr, key, val) => {
  let res = ''
  if (!Array.isArray(arr)) {
    console.warn('请传入数组')
    return
  }
  const findItem = arr => {
    for (const item of arr) {
      if (item[key] == val) {
        res = item
        break
      } else if (item.children && item.children.length > 0 && res == '') {
        findItem(item.children)
      }
    }
  }
  findItem(arr)
  return res
}

export const randomColor = () => {
  const r = Math.floor(Math.random() * 10 * 256)
  const g = Math.floor(Math.random() * 10 * 256)
  const b = Math.floor(Math.random() * 10 * 256)
  let color = `#${r.toString(16)}${g.toString(16)}${b.toString(16)}`
  return color.substring(0, 7)
}

export const deepClone = target => {
  if (typeof target == 'object') {
    if (Array.isArray(target)) {
      return target.map(item => deepClone(item))
    } else if (target == null) {
      return null
    } else if (target.constructor == RegExp) {
      return target
    } else {
      const result = {}
      for (const i in target) {
        result[i] = deepClone(target[i])
      }
      return result
    }
  } else {
    return target
  }
}

export const getBase64 = file =>
  new Promise((resolve, reject) => {
    const reader = new FileReader()
    let imgResult = ''
    reader.readAsDataURL(file)
    reader.onload = () => {
      imgResult = reader.result
    }
    reader.onerror = error => {
      reject(error)
    }
    reader.onloadend = () => {
      resolve(imgResult)
    }
  })

export const formatterData = arr => {
  const reduceFun = list => {
    list.forEach(item => {
      if (item.children && item.children.length > 0) {
        reduceFun(item.children)
      } else {
        delete item.children
      }
    })
  }
  reduceFun(arr)
  return arr
}

export const removeEmptyChildren = (arr, attrKey = 'children') => {
  if (!Array.isArray(arr)) {
    console.error('The arr type must be an Array')
    return
  }
  const reduceFun = list => {
    list.forEach(item => {
      if (item.children.length > 0) {
        reduceFun(item[attrKey])
      } else {
        delete item[attrKey]
      }
    })
  }
  reduceFun(arr)
  return arr
}

export const isAsyncAction = type => {
  const typeKey = type.split('')[type.length - 1]
  const apiArr = ['C', 'U', 'R', 'D']
  return apiArr.includes(typeKey)
}

export const getPercent = (arr, formatterKey, sum) => {
  if (arr.length > 0) {
    arr.forEach(item => {
      item[formatterKey] = ((item.value / sum) * 100).toFixed(2) + '%'
    })
  }
  return arr
}

export const addPercentSign = (arr, key) => {
  if (arr.length > 0) {
    arr.forEach(item => {
      item[key] = item[key] + '%'
    })
  }
  return arr
}

export const cellMerging = data => {
  const getRowspan = obj => {
    if (obj.children.length == 0) {
      obj.rowspan = 1
      return 1
    } else {
      let count = 0
      for (let i = 0; i < obj.children.length; i++) {
        count += getRowspan(obj.children[i])
      }
      obj.rowspan = count
      return count
    }
  }
  for (let i = 0; i < data.length; i++) {
    getRowspan(data[i])
  }
  return data
}

export const compareDate = (date1, date2) => {
  const parseYMD = d => {
    if (!d) return null
    if (d instanceof Date) {
      if (isNaN(d)) return null
      return new Date(d.getFullYear(), d.getMonth(), d.getDate())
    }
    if (typeof d === 'number') {
      const dt = new Date(d)
      if (isNaN(dt)) return null
      return new Date(dt.getFullYear(), dt.getMonth(), dt.getDate())
    }
    if (typeof d === 'string') {
      // 兼容 YYYY-MM-DD、YYYY/MM/DD、YYYY-MM-DD HH:mm:ss、YYYY/MM/DD HH:mm:ss
      let s = d.trim()
      // 只取前10位（年月日）
      s = s.slice(0, 10).replace(/-/g, '/')
      const dt = new Date(s)
      if (isNaN(dt)) return null
      return new Date(dt.getFullYear(), dt.getMonth(), dt.getDate())
    }
    return null
  }

  const d1 = parseYMD(date1)
  const d2 = parseYMD(date2)
  if (!d1 || !d2) return null // 或 return 0

  // 只比较年月日
  return d1 > d2 ? 1 : d1 < d2 ? -1 : 0
}

export const debounce = (fn, delay) => {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => fn.apply(this, args), delay)
  }
}

export const cutTimeToHour = value => {
  if (value) {
    const arr = value.split(':')
    return arr[0] + '时'
  }
}

export const isFunction = fn => Object.prototype.toString.call(fn) == '[object Function]'
