<template>
    <div class="task_confirmation_main marginT_30">
        <div class="page_second_title">人员评价</div>
        <div class="personnel_info marginT_20">
            <div>
                <span>人员</span>
                <span>{{ confirmNum }} /</span>
                <span>{{ personnelData.length }}</span>
            </div>
            <div class="flex_row_start">
                <span class="name">{{ personnelName }}</span>
                <!-- <span class="department">{{ personnelDepartment }}</span> -->
                <div class="post_list_wrap flex_row_start">
                    <div
                        class="post_list"
                        :class="{ active: personnelPost == item.postCode }"
                        @click="changePost(item.postCode)"
                        v-for="item in postOptions"
                        :key="item.postCode"
                    >
                        {{ item.postName }}
                    </div>
                </div>
            </div>
            <!-- <el-select class="inline_b" v-model="personnelPost" size="mini" placeholder>
                <el-option
                    v-for="item in postOptions"
                    :label="item.postName"
                    :value="item.postCode"
                    :key="item.postCode"
                ></el-option>
            </el-select> -->
        </div>
        <div class="department_main">
            <div class="personnel_item_wrap">
                <div
                    class="personnel_item"
                    v-for="(item, index) in personnelData"
                    :class="{
                        completed: item.confirmStatus == 'Y',
                        curr: currIndex == index,
                    }"
                    :key="index"
                    @click="selectPersonnel(index)"
                >
                    <span>{{ item.userName }}</span>
                    <i
                        class="icon el-icon-check"
                        v-if="item.confirmStatus == 'Y'"
                    ></i>
                    <i class="icon disc" v-else></i>
                </div>
            </div>
            <div class="personnel_form_wrap clearfix">
                <el-form
                    :model="ruleForm"
                    :rules="rules"
                    ref="ruleForm"
                    label-width="100px"
                    class="demo-ruleForm form_main"
                >
                    <el-form-item label="绩效表现" prop="kpiRank">
                        <el-radio-group v-model="ruleForm.kpiRank" size="mini">
                            <el-radio-button
                                v-for="item in optionsCfg.kpiRank"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="核心能力" prop="competenceRank">
                        <el-radio-group
                            v-model="ruleForm.competenceRank"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.competenceRank"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="发展潜力" prop="developmentPotential">
                        <el-radio-group
                            v-model="ruleForm.developmentPotential"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.developmentPotential"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="人才分类" prop="talentClass">
                        <el-radio-group
                            v-model="ruleForm.talentClass"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.talentClass"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="晋升可能" prop="promotionPossibility">
                        <el-radio-group
                            v-model="ruleForm.promotionPossibility"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.promotionPossibility"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="离职风险" prop="retentionRisk">
                        <el-radio-group
                            v-model="ruleForm.retentionRisk"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.retentionRisk"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="离职影响" prop="dimissionImpact">
                        <el-radio-group
                            v-model="ruleForm.dimissionImpact"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.dimissionImpact"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="内部可替代" prop="innerSubstitution">
                        <el-radio-group
                            v-model="ruleForm.innerSubstitution"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.innerSubstitution"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="外部可替代"
                        prop="externalSubstitution"
                    >
                        <el-radio-group
                            v-model="ruleForm.externalSubstitution"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.externalSubstitution"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="离职可能性"
                        prop="dimissionPossibility"
                    >
                        <el-radio-group
                            v-model="ruleForm.dimissionPossibility"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.dimissionPossibility"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="能否继任" prop="successionPossibility">
                        <el-select
                            v-model="ruleForm.successionPossibility"
                            size="mini"
                            placeholder
                        >
                            <el-option
                                v-for="item in optionsCfg.successionPossibility"
                                :key="item.dictCode"
                                :value="item.dictCode"
                                :label="item.codeName"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="理想岗位" prop="expectationPost">
                        <el-select
                            filterable
                            remote
                            :remote-method="getPostByLikeNameFun"
                            :loading="loading"
                            @change="expectationPostChange"
                            v-model="ruleForm.expectationPostName"
                            size="mini"
                            placeholder
                        >
                            <el-option
                                v-for="item in expectationPostOptions"
                                :label="item.postName"
                                :value="item.postCode"
                                :key="item.postCode"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="预计周期" prop="expectationCycle">
                        <el-select v-model="ruleForm.expectationCycle" size="mini" placeholder>
                            <el-option
                                v-for="item in optionsCfg.expectationCycle"
                                :key="item.dictCode"
                                :value="item.dictCode"
                                :label="item.codeName"
                            ></el-option>
                        </el-select>
                    </el-form-item> -->
                    <el-form-item label="个人优势" prop="strength">
                        <el-input
                            type="textarea"
                            v-model="ruleForm.strength"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="待改善或提升的部分" prop="weakness">
                        <el-input
                            type="textarea"
                            v-model="ruleForm.weakness"
                        ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <div class="paddT_12">
                            <el-button
                                class="page_add_btn"
                                type="primary"
                                @click="submitForm('ruleForm')"
                                >确定</el-button
                            >
                            <el-button
                                class="page_clear_btn"
                                @click="resetForm('ruleForm')"
                                size="mini"
                                >重置</el-button
                            >
                        </div>
                    </el-form-item>
                </el-form>
                <div class="matrix_chart_wrap">
                    <div class="matrix_chart">
                        <div class="matrix_head">
                            <div class="title">核心能力</div>
                            <div class="flex_row_start border">
                                <div
                                    class="item"
                                    v-for="item in matrixCompetenceRank"
                                    :key="item.dictCode"
                                >
                                    {{ item.codeName }}
                                </div>
                            </div>
                        </div>
                        <div class="clearfix">
                            <div class="matrix_aside">
                                <div class="matrix_aside_head flex_row_start">
                                    <div class="title">绩效指标</div>
                                    <div class="flex_col_start border">
                                        <div
                                            class="item"
                                            v-for="item in optionsCfg.kpiRank"
                                            :key="item.dictCode"
                                        >
                                            {{ item.codeName }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="matrix_main">
                                <div
                                    class="matrix_row"
                                    :class="'matrix_row_' + (index + 1)"
                                    v-for="(item, index) in matrixData1"
                                    :key="index"
                                >
                                    <div
                                        class="item"
                                        :class="'item_' + list.key"
                                        v-for="list in item"
                                        :key="list.key"
                                    >
                                        <div class="fs14 marginB_16">
                                            {{ matrixTextObj[list.key] }}
                                        </div>
                                        <el-popover
                                            v-if="list.list.length > 0"
                                            class="popover_dom"
                                            placement="top-start"
                                            title="人员"
                                            trigger="click"
                                        >
                                            <div style="width: 200px">
                                                <span
                                                    v-for="i in list.list"
                                                    :key="i"
                                                    >{{ i }}、</span
                                                >
                                            </div>
                                            <div slot="reference">
                                                {{
                                                    list.size
                                                        ? list.size + " 人"
                                                        : ""
                                                }}
                                            </div>
                                        </el-popover>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="matrix_chart small">
                        <div class="matrix_head">
                            <div class="title">商业风险</div>
                            <div class="flex_row_start border">
                                <div
                                    class="item"
                                    v-for="item in optionsCfg.dimissionImpact"
                                    :key="item.dictCode"
                                >
                                    {{ item.codeName }}
                                </div>
                            </div>
                        </div>
                        <div class="clearfix">
                            <div class="matrix_aside">
                                <div class="matrix_aside_head flex_row_start">
                                    <div class="title">离职风险</div>
                                    <div class="flex_col_start border">
                                        <div
                                            class="item"
                                            v-for="item in optionsCfg.retentionRisk"
                                            :key="item.dictCode"
                                        >
                                            {{ item.codeName }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="matrix_main">
                                <div
                                    class="matrix_row"
                                    :class="'matrix_row_' + (index + 1)"
                                    v-for="(item, index) in matrixData2"
                                    :key="index"
                                >
                                    <div
                                        class="item"
                                        v-for="list in item"
                                        :class="'item_' + list.key"
                                        :key="list.key"
                                    >
                                        <el-popover
                                            v-if="list.list.length > 0"
                                            class="popover_dom"
                                            placement="top-start"
                                            title="人员"
                                            trigger="click"
                                        >
                                            <div style="width: 200px">
                                                <span
                                                    v-for="i in list.list"
                                                    :key="i"
                                                    >{{ i }}、</span
                                                >
                                            </div>
                                            <div slot="reference">
                                                {{
                                                    list.size
                                                        ? list.size + " 人"
                                                        : ""
                                                }}
                                            </div>
                                        </el-popover>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="matrix_chart small">
                        <div class="matrix_head">
                            <div class="title">核心能力</div>
                            <div class="flex_row_start border">
                                <div class="item">高</div>
                                <div class="item">中</div>
                                <div class="item">低</div>
                            </div>
                        </div>
                        <div class="clearfix">
                            <div class="matrix_aside">
                                <div class="matrix_aside_head flex_row_start">
                                    <div class="title">发展潜力</div>
                                    <div class="flex_col_start border">
                                        <div
                                            class="item"
                                            v-for="item in optionsCfg.developmentPotential"
                                            :key="item.dictCode"
                                        >
                                            {{ item.codeName }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="matrix_main">
                                <div
                                    class="matrix_row"
                                    :class="'matrix_row_' + (index + 1)"
                                    v-for="(item, index) in matrixData3"
                                    :key="index"
                                >
                                    <div
                                        class="item"
                                        :class="'item_' + list.key"
                                        v-for="list in item"
                                        :key="list.key"
                                    >
                                        <el-popover
                                            v-if="list.list.length > 0"
                                            class="popover_dom"
                                            placement="top-start"
                                            title="人员"
                                            trigger="click"
                                        >
                                            <div style="width: 200px">
                                                <span
                                                    v-for="i in list.list"
                                                    :key="i"
                                                    >{{ i }}、</span
                                                >
                                            </div>
                                            <div slot="reference">
                                                {{
                                                    list.size
                                                        ? list.size + " 人"
                                                        : ""
                                                }}
                                            </div>
                                        </el-popover>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="oper_btn_wrap align_center">
            <!-- <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button> -->
            <el-button
                class="page_confirm_btn"
                type="primary"
                @click="nextBtn"
                >{{ nextBtnText }}</el-button
            >
        </div>
    </div>
</template>

<script>
    import {
        getDeptUserPost,
        getUserEval,
        saveUserEval,
        getPostByLikeName,
        kpiCapablityInterval,
        retentionInterval,
        developmentCapabilityInterval,
    } from "../../../request/api";

    const matrixTextObj = {
        AA: "明星人才",
        AB: "核心人才",
        AC: "关注人才",
        BA: "核心人才",
        BB: "骨干人才",
        BC: "关注人才",
        CA: "待提升人才",
        CB: "待提升人才",
        CC: "待优化人才",
    };
    export default {
        name: "personnelEvaluation",
        props: ["nextBtnText", "enqId"],
        components: {},
        computed: {
            // orgCode() {
            //     return this.$store.state.userInfo.orgCode;
            // },
        },
        created() {
            // this.enqId = this.$route.params.id;
            let docList = [
                "KPI_RANK",
                "COMPETENCE_RANK",
                "DEVELOPMENT_POTENTIAL",
                "TALENT_CLASS",
                "PROMOTION_POSSIBILITY",
                "RETENTION_RISK",
                "DIMISSION_IMPACT",
                "INNER_SUBSTITUTION",
                "EXTERNAL_SUBSTITUTION",
                "EXPECTATION_CYCLE",
                "DIMISSION_POSSIBILITY",
                "SUCCESSION_POSSIBILITY",
            ];
            this.$getDocList(docList).then((res) => {
                let optionsCfg = {
                    kpiRank: res.KPI_RANK,
                    competenceRank: res.COMPETENCE_RANK,
                    developmentPotential: res.DEVELOPMENT_POTENTIAL,
                    talentClass: res.TALENT_CLASS,
                    promotionPossibility: res.PROMOTION_POSSIBILITY,
                    retentionRisk: res.RETENTION_RISK,
                    dimissionImpact: res.DIMISSION_IMPACT,
                    innerSubstitution: res.INNER_SUBSTITUTION,
                    externalSubstitution: res.EXTERNAL_SUBSTITUTION,
                    // expectationCycle: res.EXPECTATION_CYCLE,
                    dimissionPossibility: res.DIMISSION_POSSIBILITY,
                    successionPossibility: res.SUCCESSION_POSSIBILITY,
                };
                this.matrixCompetenceRank = this.$util.deepClone(
                    res.COMPETENCE_RANK
                );
                this.matrixCompetenceRank = this.matrixCompetenceRank;
                this.optionsCfg = optionsCfg;
            });
            this.getDeptUserPostFun();
            this.getMatrixData();
        },

        methods: {
            getDeptUserPostFun(refreshFlag) {
                let flag = refreshFlag ? true : false;
                let params = {
                    enqId: this.enqId,
                    // orgCode: this.orgCode,
                    moduleId: "D08",
                };
                getDeptUserPost(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.confirmNum = 0;
                        this.personnelData = res.data;
                        this.personnelData.forEach((item) => {
                            if (item.confirmStatus == "Y") {
                                this.confirmNum++;
                            }
                        });
                        if (!flag) {
                            let data = this.personnelData[this.currIndex];
                            this.postOptions = data.postNameList;
                            this.userId = data.userId;
                            this.personnelName = data["userName"];
                            this.formatterPostList(data)

                            // this.personnelDepartment = data["orgName"];
                            // this.personnelPost = data.postNameList[0]["postCode"];
                        }
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
            getUserEvalFun() {
                let params = {
                    enqId: this.enqId,
                    userId: this.userId,
                    postCode: this.personnelPost,
                };
                getUserEval(params).then((res) => {
                    if (res.code == "200") {
                        this.$msg.success(res.msg);
                        let data = res.data;
                        if (data) {
                            for (let key in this.ruleForm) {
                                this.ruleForm[key] = data[key];
                            }
                        } else {
                            this.resetForm("ruleForm");
                            this.ruleForm.expectationPostName = "";
                        }
                    } else {
                        this.$msg.error(res.msg);
                        this.resetForm("ruleForm");
                        this.ruleForm.expectationPostName = "";
                    }
                });
            },
            saveUserEvalFun(prevStep) {
                this.ruleForm.enqId = this.enqId;
                this.ruleForm.userId = this.userId;
                this.ruleForm.postCode = this.personnelPost;
                let params = this.$util.deepClone(this.ruleForm);
                // if (this.postChangeFlag) {
                //     params.expectationPost = params.expectationPostName;
                // }
                saveUserEval(params).then((res) => {
                    if (res.code == "200") {
                        this.$msg.success(res.msg);
                        if (prevStep) {
                            this.$emit("prevStep");
                        }
                        this.getDeptUserPostFun(true);
                        this.getMatrixData();
                        this.savaFlag = true;
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
            formatterPostList(row){
                console.log(row);
                let postList = [];
                for (const key in row.enqOrgInfoMap) {
                    if (Object.hasOwnProperty.call(row.enqOrgInfoMap, key)) {
                        const orgInfo = row.enqOrgInfoMap[key];
                        let orgName = orgInfo.orgName;
                        console.log(orgInfo);
                        let arr = orgInfo.postNameList.map(item => {
                            return {
                                postName:orgName + ' / ' + item.postName,
                                postCode:item.postCode,
                            }
                        })
                        postList=postList.concat(arr);
                    }
                };
                this.postOptions = postList;
                this.personnelPost = postList[0]["postCode"];

            },
            getPostByLikeNameFun(query) {
                if (query !== "") {
                    this.loading = true;
                    let params = {
                        enqId: this.enqId,
                        postName: query,
                    };
                    getPostByLikeName(params).then((res) => {
                        if (res.code == "200") {
                            this.loading = false;
                            this.expectationPostOptions = res.data;
                        }
                    });
                } else {
                    this.options = [];
                }
            },
            expectationPostChange(val) {
                this.postChangeFlag = true;
                this.$set(this.ruleForm, "expectationPost", val);
            },
            selectPersonnel(index) {
                this.currIndex = index;
                this.expectationPostOptions = [];
            },
            submitForm(formName, prevStep) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        if (this.savaFlag) {
                            this.savaFlag = false;
                            this.saveUserEvalFun(prevStep);
                        }
                    } else {
                        console.log("error submit!!");
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },
            formatter(arr) {
                let str = "";
                arr.forEach((item) => {
                    str += item + "、";
                });
                return str;
            },
            getMatrixData() {
                this.kpiCapablityIntervalFun();
                this.retentionIntervalFun();
                this.developmentCapabilityIntervalFun();
            },
            kpiCapablityIntervalFun() {
                kpiCapablityInterval({
                    enqId: this.enqId,
                    // orgCode: this.orgCode,
                }).then((res) => {
                    console.log(res);
                    this.matrixData1 = res;
                });
            },
            retentionIntervalFun() {
                retentionInterval({
                    enqId: this.enqId,
                    // orgCode: this.orgCode,
                }).then((res) => {
                    console.log(res);
                    this.matrixData2 = res;
                });
            },
            developmentCapabilityIntervalFun() {
                developmentCapabilityInterval({
                    enqId: this.enqId,
                    // orgCode: this.orgCode,
                }).then((res) => {
                    console.log(res);
                    this.matrixData3 = res;
                });
            },
            changePost(postCode) {
                this.personnelPost = postCode;
                this.expectationPostOptions = [];
            },
            // prevBtn() {
            //     let that = this;
            //     this.$confirm("即将离开当前页面，是否保存当前页数据?", "提示", {
            //         distinguishCancelAndClose: true,
            //         confirmButtonText: "保存",
            //         cancelButtonText: "放弃修改",
            //     })
            //         .then(() => {
            //             this.submitForm("ruleForm", "prevStep");
            //         })
            //         .catch((action) => {
            //             this.$msg.info({
            //                 message:
            //                     action == "cancel"
            //                         ? "已放弃修改并返回上一步"
            //                         : "取消返回上一步",
            //             });
            //             action == "cancel" ? that.$emit("prevStep") : "";
            //         });
            // },
            nextBtn() {
                for (let index = 0; index < this.personnelData.length; index++) {
                    let status = this.personnelData[index].confirmStatus;
                    let userName = this.personnelData[index].userName;
                    if (status == "N") {
                        this.$msg.error(
                            `请完善人员：${userName}的评价后再次提交！`
                        );
                        return;
                    }
                }
                this.$emit("nextStep");
            },
        },
        watch: {
            currIndex: function (val) {
                let data = this.personnelData[val];
                this.userId = data.userId;
                this.postOptions = data.postNameList;
                this.personnelName = data["userName"];

                this.formatterPostList(data)
                let orgList = [];
                // for (const key in data.enqOrgInfoMap) {
                //     if (Object.hasOwnProperty.call(data.enqOrgInfoMap, key)) {
                //         const orgInfo = data.enqOrgInfoMap[key];
                        
                //         let postList = orgInfo.postNameList.map(item => {
                //             return {
                //                 postName:item.postName,
                //                 postCode:item.postCode
                //             }
                //         })
                //         orgList.push({
                //             orgName:orgInfo.orgName,
                //             orgCode:orgInfo.orgCode,
                //             postList:postList
                //         })
                        
                //     }
                // }

                // this.orgList = orgList;

                // this.activeOrg = orgList[0].orgName
                // this.personnelDepartment = data["orgName"];
                // this.personnelPost = data.postNameList[0]["postCode"];
                this.getUserEvalFun();
            },
            personnelPost: function (val) {
                this.getUserEvalFun();
            },
        },
        data() {
            return {
                savaFlag: true,
                optionsCfg: {},
                currIndex: 0,
                personnelName: "",
                personnelDepartment: "",
                personnelPost: "",
                postOptions: [],
                confirmNum: 0,
                personnelData: [],
                loading: false,
                expectationPostOptions: [],
                ruleForm: {
                    kpiRank: "",
                    competenceRank: "",
                    developmentPotential: "",
                    talentClass: "",
                    promotionPossibility: "",
                    retentionRisk: "",
                    dimissionImpact: "",
                    innerSubstitution: "",
                    externalSubstitution: "",
                    dimissionPossibility: "",
                    successionPossibility: "",
                    // expectationPost: "",
                    // expectationPostName: "",
                    // expectationCycle: "",
                    strength: "",
                    weakness: "",
                },
                rules: {
                    kpiRank: [
                        {
                            required: true,
                            message: "请选择绩效表现",
                            trigger: "change",
                        },
                    ],
                    competenceRank: [
                        {
                            required: true,
                            message: "请选择核心能力",
                            trigger: "change",
                        },
                    ],
                    developmentPotential: [
                        {
                            required: true,
                            message: "请选择发展潜力",
                            trigger: "change",
                        },
                    ],
                    talentClass: [
                        {
                            required: true,
                            message: "请选择人才分类",
                            trigger: "change",
                        },
                    ],
                    promotionPossibility: [
                        {
                            required: true,
                            message: "请选择晋升可能",
                            trigger: "change",
                        },
                    ],
                    retentionRisk: [
                        {
                            required: true,
                            message: "请选择离职风险",
                            trigger: "change",
                        },
                    ],
                    dimissionImpact: [
                        {
                            required: true,
                            message: "请选择离职影响",
                            trigger: "change",
                        },
                    ],
                    innerSubstitution: [
                        {
                            required: true,
                            message: "请选择内部可替代",
                            trigger: "change",
                        },
                    ],
                    externalSubstitution: [
                        {
                            required: true,
                            message: "请选择外部可替代",
                            trigger: "change",
                        },
                    ],
                    dimissionPossibility: [
                        {
                            required: true,
                            message: "请选择离职可能性",
                            trigger: "change",
                        },
                    ],
                    successionPossibility: [
                        {
                            required: true,
                            message: "请选择能否继任",
                            trigger: "change",
                        },
                    ],
                    // expectationPost: [
                    //     {
                    //         required: true,
                    //         message: "请选择理想岗位",
                    //         trigger: "change",
                    //     },
                    // ],
                    // expectationCycle: [
                    //     {
                    //         required: true,
                    //         message: "请选择预计周期",
                    //         trigger: "change",
                    //     },
                    // ],
                    strength: [
                        {
                            required: true,
                            message: "请填写个人优势",
                            trigger: "blur",
                        },
                    ],
                    weakness: [
                        {
                            required: true,
                            message: "请填写待发展",
                            trigger: "blur",
                        },
                    ],
                },
                matrixCompetenceRank: [],
                matrixTextObj: matrixTextObj,
                matrixData1: [],
                matrixData2: [],
                matrixData3: [],
                map: {
                    code1: {
                        orgName: "一部",
                        orgCode:'code1',
                        postNameList: [
                            { postName: "岗位一" },
                            { postName: "岗位二" },
                        ],
                    },
                    code2: {
                        orgName: "二部",
                        postNameList: [
                            { postName: "岗位一" },
                            { postName: "岗位二" },
                        ],
                    },
                },
            };
        },
    };
</script>

<style scoped lang="scss">
    .personnel_info {
        // text-align: right;
        // background: #f4f4f4;
        // line-height: 45px;
        // margin-bottom: 16px;
        // padding: 0 16px;
        font-weight: bold;
        background: #f4f4f4;
        line-height: 25px;
        margin-bottom: 16px;
        padding: 12px 16px;
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        .department {
            // color: #449CFF;
            // border: 1px solid #449CFF;
            // padding: 2px 5px;
            // border-radius: 3px;
            // margin: 0 16px;
            color: #449cff;
            border: 1px solid #449cff;
            padding: 1px 6px;
            line-height: 21px;
            border-radius: 3px;
            margin: 0 16px;
            background: #fff;
        }
        .inline_b {
            width: auto;
        }
        .post_list_wrap {
            margin-left: 8px;
            .post_list {
                cursor: pointer;
                margin-right: 16px;
                &.active {
                    color: #ffc000;
                    // color: #0099FF;
                }
            }
        }
    }

    .personnel_item_wrap {
        width: 120px;
        float: left;
        margin-right: 16px;

        .personnel_item {
            line-height: 30px;
            padding: 0 8px;
            color: #525e6c;
            font-size: 14px;
            background: #f8f8f8;
            margin-bottom: 5px;
            font-weight: bold;
            cursor: pointer;

            &.completed {
                color: #0099fd;
                background: #eef5fb;

                .icon {
                    display: block;
                }
            }

            &.curr {
                background: #0099fd;
                color: #fff;

                .icon {
                    display: block;
                    color: #fff;

                    &.disc {
                        background: #fff;
                    }
                }
            }

            .icon {
                // display: none;
                float: right;
                font-weight: bold;
                line-height: 30px;
                text-align: center;
                color: #0099fd;

                &.disc {
                    width: 8px;
                    height: 8px;
                    margin: 10px 4px 0 auto;
                    border-radius: 50%;
                    background: #ffc000;
                }
            }
        }
    }

    .personnel_form_wrap {
        overflow: hidden;

        .form_main {
            float: left;
            width: 510px;
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-start;
        }
    }

    .matrix_chart_wrap {
        overflow: hidden;
        margin-left: 20px;
        float: right;
    }

    .matrix_chart {
        width: 480px;
        /*float: left;*/
        margin-right: 6px;
        margin-bottom: 16px;

        &.small {
            width: 240px;
            float: left;
            margin-top: 20px;

            .matrix_head {
                line-height: 20px;

                .title {
                    height: 20px;
                    padding-left: 40px;
                }

                .flex_row_start {
                    height: 20px;
                    margin-left: 40px;
                }
            }

            .matrix_aside {
                width: 40px;
                height: 190px;

                .title {
                    width: 20px;
                    height: calc(100% + 40px);
                    margin-top: -40px;
                }

                .flex_col_start {
                    width: 20px;
                }

                .item {
                    line-height: 60px;
                }
            }

            .matrix_main {
                .matrix_row {
                    &_1 {
                        .item_1 {
                            background-color: #e28d80;
                        }

                        .item_2 {
                            background-color: #719dd5;
                        }

                        .item_3 {
                            background-color: #a3d0f3;
                        }
                    }

                    &_2 {
                        .item_1 {
                            background-color: #719dd5;
                        }

                        .item_2,
                        .item_3 {
                            background-color: #a3d0f3;
                        }
                    }

                    &_3 {
                        .item_1,
                        .item_2 {
                            background-color: #a3d0f3;
                        }

                        .item_3 {
                            background-color: #dddee3;
                        }
                    }
                }

                .item {
                    width: 66px;
                    height: 63px;
                }
            }
        }

        .matrix_head {
            width: 100%;
            // padding-left: 45px;
            // margin-left: 45px;
            text-align: left;
            line-height: 30px;

            .title {
                height: 30px;
                background: #fbfbfb;
                padding-left: 100px;
            }

            .flex_row_start {
                height: 30px;
                margin-left: 100px;

                &.border {
                    border-bottom: 1px solid #f6f6f6;
                }
            }

            .item {
                flex: 1;
                text-align: center;
            }
        }

        .matrix_aside {
            float: left;
            width: 100px;
            height: 230px;
            text-align: center;

            .matrix_aside_head {
                height: 100%;
            }

            .title {
                height: calc(100% + 50px);
                padding: 30px 10px 0 5px;
                width: 30px;
                background: #fbfbfb;
                margin-top: -50px;
            }

            .flex_col_start {
                height: 100%;
                width: 70px;

                &.border {
                    border-right: 1px solid #f6f6f6;
                }
            }

            .item {
                flex: 1;
                line-height: 45px;
            }
        }

        .matrix_main {
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .matrix_row {
                width: 100%;
                display: flex;
                flex: row nowrap;
                // &_1 {
                //     .item_1,
                //     .item_2 {
                //         background-color: #e28d80;
                //     }
                //     .item_3 {
                //         background-color: #719dd5;
                //     }
                //     .item_4,
                //     .item_5 {
                //         background-color: #bed269;
                //     }
                // }

                // &_2 {
                //     .item_1 {
                //         background-color: #e28d80;
                //     }
                //     .item_2,
                //     .item_3 {
                //         background-color: #719dd5;
                //     }
                //     .item_4,
                //     .item_5 {
                //         background-color: #bed269;
                //     }
                // }

                // &_3 {
                //     .item_1,
                //     .item_2 {
                //         background-color: #719dd5;
                //     }
                //     .item_3 {
                //         background-color: #a3d0f3;
                //     }
                //     .item_4,
                //     .item_5 {
                //         background-color: #bed269;
                //     }
                // }
                // &_4 {
                //     .item_1,
                //     .item_2,
                //     .item_3 {
                //         background-color: #a3d0f3;
                //     }
                //     .item_4 {
                //         background-color: #bed269;
                //     }
                //     .item_5 {
                //         background-color: #dddee3;
                //     }
                // }

                // &_5 {
                //     .item_1,
                //     .item_2,
                //     .item_3 {
                //         background-color: #a3d0f3;
                //     }
                //     .item_4,
                //     .item_5 {
                //         background-color: #dddee3;
                //     }
                // }
            }

            .item {
                flex: 1;
                // width: 125px;
                height: 76px;
                margin: 0 1px 1px 0;
                padding: 2px;
                color: #fff;
                font-size: 16px;
                overflow: hidden;
                &_AA {
                    background-color: #e28d80;
                }
                &_AB {
                    background-color: #719dd5;
                }
                &_AC {
                    background-color: #a3d0f3;
                }
                &_BA {
                    background-color: #719dd5;
                }
                &_BB {
                    background-color: #bed269;
                }
                &_BC {
                    background-color: #a3d0f3;
                }
                &_CA {
                    background-color: #a3d0f3;
                }
                &_CB {
                    background-color: #a3d0f3;
                }
                &_CC {
                    background-color: #dddee3;
                }
                // 矩阵下方 三个颜色样式
                &_HA,
                &_HH {
                    background-color: #e28d80;
                }
                &_HB,
                &_HM {
                    background-color: #719dd5;
                }
                &_HC,
                &_HL {
                    background-color: #bed269;
                }
                &_MA,
                &_MH {
                    background-color: #719dd5;
                }
                &_MB,
                &_MM {
                    background-color: #bed269;
                }
                &_MC,
                &_ML {
                    background-color: #bed269;
                }
                &_LA,
                &_LH {
                    background-color: #bed269;
                }
                &_LB,
                &_LM {
                    background-color: #bed269;
                }
                &_LC,
                &_LL {
                    background-color: #a3d0f3;
                }
                // &.level_1 {
                //     background-color: #e28d80;
                // }

                // &.level_2 {
                //     background-color: #719dd5;
                // }

                // &.level_3 {
                //     background-color: #bed269;
                // }

                // &.level_4 {
                //     background-color: #a3d0f3;
                // }

                // &.level_5 {
                //     background-color: #dddee3;
                // }
            }
        }
    }

    .el-form-item {
        width: 100%;
        margin-bottom: 14px;
    }

    .el-form-item__content {
        line-height: 30px;
    }

    .el-form-item__label {
        line-height: 30px;
    }

     .el-input__inner {
        width: 100%;
    }

     .el-form-item__error {
        padding-top: 2px;
    }

     .el-textarea {
        padding-top: 10px;
        width: 100%;
    }

    .el-select {
        width: 100%;
    }

    .el-radio-group {
        display: flex;

        label {
            flex: 1;

            span {
                width: 100%;
            }
        }
    }

    .popover_dom {
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .el-popover__reference {
        width: 100%;
        height: 100%;
    }
</style>
