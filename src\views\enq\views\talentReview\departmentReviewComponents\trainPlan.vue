<template>
  <div class="train_plan_main marginT_8">
    <div class="page_second_title">培训计划</div>
    <div class="btn_wrap align_right">
      <el-button class="page_add_btn" type="primary" @click="add">新增</el-button>
    </div>
    <div class="department_main marginT_8">
      <div class="post_process_table">
        <el-table class="table_wrap" :data="tableData">
          <el-table-column prop="trainingName" label="培训课程名称">
            <template #default="scope">
              <el-input v-model="scope.row.trainingName" size="small"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="expectBeginDate" label="期望开始日期">
            <template #default="scope">
              <el-date-picker
                v-model="scope.row.expectBeginDate"
                value-format="YYYY-MM-DD"
                type="date"
                placeholder="选择日期"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column prop="expectEndDate" label="期望结束日期">
            <template #default="scope">
              <el-date-picker
                v-model="scope.row.expectEndDate"
                value-format="YYYY-MM-DD"
                type="date"
                placeholder="选择日期"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column prop="trainingCourse" label="课程类型">
            <template #default="scope">
              <el-select v-model="scope.row.trainingCourse" placeholder="">
                <el-option
                  v-for="item in courseOptions"
                  :label="item.codeName"
                  :value="item.dictCode"
                  :key="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="trainingDemand" label="培训需求说明">
            <template #default="scope">
              <el-input v-model="scope.row.trainingDemand"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="60">
            <template #default="scope">
              <el-button
                :icon="Delete"
                class="icon_del"
                @click="tableDeleteRow(scope.$index, scope.row)"
                link
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="align_center marginT_16">
      <el-button type="primary" class="page_confirm_btn" @click="prevBtn" v-show="currentIndex != currentFirstCode"
        >上一步</el-button
      >
      <el-button type="primary" class="page_confirm_btn" @click="submit('nextStep')">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { trainingPlanList, trainingPlanAdd, delTraining } from '../../../request/api'
import { useUserStore } from '@/stores/modules/user.js'
import { objHasEmpty } from '@/utils/utils.js'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  orgCode: String,
  currentIndex: Number,
  currentFirstCode: Number
})

const emit = defineEmits(['prevStep', 'nextStep'])

const courseOptions = ref([])
const tableData = ref([])
const userStore = useUserStore()

onMounted(async () => {
  const res = await userStore.getDocList(['TRAINING_COURSE'])
  courseOptions.value = res.TRAINING_COURSE

  await getAllEnqOrgTrainingFun()
})

const submit = async stepType => {
  if (checkResultData(tableData.value)) {
    try {
      const res = await trainingPlanAdd({
        enqId: props.enqId,
        list: tableData.value
      })

      if (res.code == 200) {
        ElMessage.success(res.msg)
        await getAllEnqOrgTrainingFun()
        emit(stepType)
      } else {
        ElMessage.error(res.msg)
      }
    } catch (error) {
      console.error(error)
      ElMessage.error('提交失败')
    }
  } else {
    ElMessage.warning('请完善信息后提交！')
  }
}

const add = () => {
  const obj = tableData.value[tableData.value.length - 1]
  const addObj = {
    trainingName: '',
    expectBeginDate: '',
    expectEndDate: '',
    trainingCourse: '',
    trainingDemand: ''
  }

  if (!obj) {
    tableData.value.push(addObj)
    return
  }

  if (checkResultData(tableData.value)) {
    tableData.value.push(addObj)
  } else {
    ElMessage.warning('请完善当前信息后新增！')
  }
}

const getAllEnqOrgTrainingFun = async () => {
  try {
    const res = await trainingPlanList({
      enqId: props.enqId
    })

    if (res.code == 200) {
      tableData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const tableDeleteRow = async (index, row) => {
  if (!Object.prototype.hasOwnProperty.call(row, 'trainingId')) {
    try {
      await ElMessageBox.confirm('确认删除此条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      tableData.value.splice(index, 1)
      ElMessage.success('删除成功!')
    } catch {
      ElMessage.info('已取消删除')
    }
  } else {
    try {
      await ElMessageBox.confirm('确认删除此条信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const res = await delTraining({
        enqId: props.enqId,
        userTrainingId: row.userTrainingId
      })

      if (res.code == 200) {
        ElMessage.success('删除成功!')
        await getAllEnqOrgTrainingFun()
      } else {
        ElMessage.error(res.msg)
      }
    } catch {
      ElMessage.info('已取消删除')
    }
  }
}

const checkResultData = data => {
  return !checkData(data)
}

const checkData = data => {
  const checkArr = ['trainingName', 'expectBeginDate', 'expectEndDate', 'trainingCourse', 'trainingDemand']

  for (const obj of data) {
    if (objHasEmpty(obj, checkArr)) {
      console.log('有空值')
      return true
    }
    console.log('没有空值')
  }
  return false
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submit('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') {
        emit('prevStep')
      }
    })
}
</script>

<style scoped lang="scss">
.post_column {
  margin-right: 4px;
}

.table_wrap {
  margin-bottom: 16px;
}

.post_column.active {
  .post_ipt {
  }
}

.post_ipt {
  width: 30px;
  height: 30px;
  border: 1px solid #ebeef5;
  cursor: pointer;
  color: #000;
  margin-right: 3px;
  text-align: center;
  font-weight: bold;
  line-height: 28px;

  &:hover {
    background: #b3e0fd;
  }

  &.active {
    position: relative;
    background: #0099fd;
    color: #fff;

    .icon {
      display: block;
    }
  }

  .icon {
    display: none;
    width: 100%;
    line-height: 28px;
    font-weight: bold;
  }
}

.el-table th > .cell {
  padding-left: 7px;
  padding-right: 7px;
  margin-right: 4px;
}

.el-table__row .cell {
  // padding: 0 2px;
}

.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: auto;
}
.el-table__header-wrapper {
  background-color: #f4f4f4;
}
</style>
