<script setup>
import simplenessTable from '@/components/table/simplenessTable.vue'
import indicatorDiagnosis from './indicatorDiagnosis.vue'
import { zlzbdcDetail } from '@/assets/data/manageView.js'
defineOptions({ name: 'appDesc' })
const activeType = ref('hxzbyl')
const typeList = ref([
  {
    id: 'hxzbyl',
    title: '核心指标一览'
  },
  {
    id: 'dxgwcj',
    title: '典型岗位承接'
  },
  {
    id: 'zbdynl',
    title: '指标对应能力'
  },
  {
    id: 'zbzd',
    title: '指标诊断'
  },
  {
    id: 'gljy',
    title: '管理建议'
  }
])
const detailData = ref(zlzbdcDetail[activeType.value])
console.log('detailData', detailData.value)
const changeType = item => {
  console.log(item)
  activeType.value = item.id
  if (item.id == 'zbzd') return
  detailData.value = zlzbdcDetail[item.id]
  classfiyActive.value = detailData.value.classfiyList[0].id
  classfiyData.value = {}
  otherList.value = []

  console.log(detailData.value)
}

const classfiyActive = ref('gylrx')
const classfiyData = ref({})
const otherList = ref([])
const otherActive = ref('')
detailData.value.classfiyList.forEach(item => {
  if (item.id == classfiyActive.value) {
    classfiyData.value = item.content
  }
})
const changeClassfiy = item => {
  classfiyActive.value = item.id
  if (item?.children?.length) {
    otherActive.value = item.children[0].id
    classfiyData.value = item.children[0].content
    otherList.value = item.children
    return
  }
  otherList.value = []
  classfiyData.value = item.content
  console.log(classfiyData)
  checkIndicatorName.value = ''
  interpretation.value = ''
}

const changeOtherList = item => {
  // classfiyActive.value = item.id
  otherActive.value = item.id
  classfiyData.value = item.content
  console.log(classfiyData)
}
const checkIndicatorName = ref('')
const interpretation = ref('')
const AIanalysis = item => {
  if (checkIndicatorName.value == item.name) return
  console.log('AIanalysis', item)
  checkIndicatorName.value = item.name
  interpretation.value = item.interpretation
}
</script>
<template>
  <div class="view-detail-wrap">
    <div class="type-wrap">
      <div
        class="type-item"
        :class="{ active: activeType == item.id }"
        @click="changeType(item)"
        v-for="item in typeList"
        :key="item.id"
      >
        {{ item.title }}
      </div>
    </div>
    <indicatorDiagnosis v-show="activeType == 'zbzd'"></indicatorDiagnosis>
    <div class="detail-content" v-show="activeType !== 'zbzd'">
      <div class="classfiy-wrap">
        <div class="classfiy-label">指标类别：</div>
        <div class="classfiy-content">
          <div class="content-main">
            <div class="classfiy-item" v-for="item in detailData.classfiyList" :key="item.id">
              <div class="item-content" :class="{ active: classfiyActive == item.id }" @click="changeClassfiy(item)">
                {{ item.title }}
              </div>
              <div class="item-children" v-show="otherList.length">
                <div
                  class="children-list"
                  :class="{ active: otherActive == list.id }"
                  v-for="list in item.children"
                  :key="list.id"
                  @click="changeOtherList(list)"
                >
                  {{ list.title }}
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="content-other" v-show="otherList.length">
            <div
              class="classfiy-item"
              @click="changeOtherList(item)"
              :class="{ active: otherActive == item.id }"
              v-for="item in otherList"
              :key="item.id"
            >
              {{ item.title }}
            </div>
          </div> -->
        </div>
      </div>
      <div class="content-main">
        <div class="imp-sug" v-if="activeType == 'gljy'">
          <div class="title">
            <SvgIcon name="imp-sug"></SvgIcon>
            指标改善建议
          </div>
          <div class="page-title-line">指标基本信息</div>
          <div class="ind-info">
            <div class="ind-info-item" v-for="item in classfiyData.info" :key="item.label">
              <div class="label">
                <div class="sign"></div>
                {{ item.label }}
              </div>
              <div class="item-content">
                <div class="text" v-for="list in item.text" :key="list">{{ list }}</div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="activeType == 'gljy'">
          <div class="page-title-line">{{ detailData.tableTitle2 }}</div>
          <simplenessTable class="table" :columns="classfiyData.columns2" :data="classfiyData.indicators2">
            <template v-slot:oper>
              <el-table-column width="140">
                <template v-slot="scope">
                  <div class="oper-btn-wrap">
                    <div class="oper-btn" @click="AIanalysis(scope.row)">查看任务</div>
                  </div>
                </template>
              </el-table-column>
            </template>
          </simplenessTable>
        </div>
        <div class="page-title-line" v-if="detailData.tableTitle">
          {{ detailData.tableTitle }}{{ activeType == 'gljy' ? '(供应商分类与绩效管理)' : '' }}
        </div>
        <simplenessTable
          v-if="classfiyData.columns"
          class="table"
          :columns="classfiyData.columns"
          :data="classfiyData.indicators"
        >
          <template v-slot:oper>
            <el-table-column width="140">
              <template v-slot="scope">
                <div class="oper-btn-wrap">
                  <div class="oper-btn" @click="AIanalysis(scope.row)">AI解读</div>
                  <div class="oper-btn" @click="ability(scope.row)">能力评估</div>
                </div>
              </template>
            </el-table-column>
          </template>
        </simplenessTable>
        <div class="interpretation">
          <div class="title">
            已选指标：
            <div class="text">{{ checkIndicatorName }}</div>
          </div>
          <div class="interpretation-content">
            <div class="title">AI解读：{{ checkIndicatorName }}</div>
            <div class="content" v-html="interpretation"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.view-detail-wrap {
  .page-title-line {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .detail-location {
    font-size: 16px;
    color: #3d3d3d;
    line-height: 24px;
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 16px;
    margin-bottom: 30px;
  }
  .table {
    margin-bottom: 30px;
  }
}
.type-wrap {
  display: flex;
  align-items: center;
  flex-flow: row wrap;
  margin-bottom: 18px;
  .type-item {
    width: 311px;
    line-height: 35px;
    font-size: 14px;
    color: #333333;
    border-radius: 5px;
    font-size: 16px;
    text-align: center;
    color: #3d3d3d;
    background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
    border: 1px solid #c6dbf3;
    margin-right: 11px;
    cursor: pointer;
    &.active {
      color: #40a0ff;
      box-shadow: 0px 0px 10px 0px rgba(124, 182, 237, 0.5);
      border: 1px solid #53acff;
    }
  }
}
.detail-content {
  display: flex;
  align-items: flex-start;
  .content-main {
    flex: 1;
  }
}
.classfiy-wrap {
  flex: 0 0 200px;
  padding: 24px 20px;
  background: linear-gradient(224deg, #d0e4f9 0%, rgba(195, 230, 255, 0.6) 100%);
  border-radius: 8px 8px 8px 8px;
  margin-right: 16px;
  .classfiy-label {
    font-size: 14px;
    color: #333333;
    line-height: 35px;
  }
  .classfiy-content {
    .content-main {
    }
    .classfiy-item {
      width: 100%;
      margin-bottom: 10px;
      .item-content {
        line-height: 33px;
        background: #f0f9ff;
        border-radius: 5px 5px 5px 5px;
        border: 1px solid #a5c1dc;
        text-align: center;
        cursor: pointer;
        &.active {
          background: #83c1ff;
          border: 1px solid #83c1ff;
          color: #ffffff;
        }
      }
      .item-children {
        .children-list {
          text-align: center;
          line-height: 28px;
          font-size: 14px;
          color: #6c757e;
          cursor: pointer;
          margin-top: 8px;
          &.active,
          &:hover {
            color: #40a0ff;
          }
        }
      }
    }
  }
}
.oper-btn-wrap {
  display: flex;
  align-items: center;
  .oper-btn {
    min-width: 55px;
    line-height: 18px;
    border-radius: 78px;
    border: 1px solid #589ef9;
    font-size: 12px;
    color: #589ef9;
    text-align: center;
    padding: 0 10px;
    cursor: pointer;
    & + .oper-btn {
      margin-left: 7px;
    }
  }
}
.interpretation {
  .title {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #94a1af;
    line-height: 32px;
    .text {
      color: #3d3d3d;
    }
  }
  .interpretation-content {
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 20px;
    .title {
      font-weight: 600;
      font-size: 16px;
      color: #3d3d3d;
      line-height: 32px;
      margin-bottom: 15px;
    }
    .content {
    }
  }
}
.imp-sug {
  .title {
    font-weight: 600;
    font-size: 18px;
    color: #3d3d3d;
    line-height: 20px;
    margin-bottom: 20px;
  }
  .ind-info {
    background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    padding: 20px;
    margin-top: 20px;
    margin-bottom: 28px;
    .ind-info-item {
      display: flex;
      align-items: flex-start;
      font-size: 14px;
      margin-bottom: 18px;
      .label {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 16px;
        color: #40a0ff;
        line-height: 25px;
        margin-right: 20px;
        .sign {
          width: 8px;
          height: 8px;
          background: linear-gradient(90deg, #59b9fc 5%, #4276f0 97%), #d8d8d8;
          border-radius: 2px 2px 2px 2px;
          margin-right: 12px;
          transform: rotate(-135deg);
        }
      }
      .text {
        font-size: 16px;
        color: #3d3d3d;
        line-height: 25px;
      }
    }
  }
}
</style>
