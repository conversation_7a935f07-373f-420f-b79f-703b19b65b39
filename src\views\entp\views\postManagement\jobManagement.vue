<template>
  <div class="post_management_wrap bg_write">
    <div class="page_main_title">职位管理</div>
    <div class="page_section">
      <div class="post_management_center clearfix">
        <div class="page_section_aside org_chart_aside">
          <div class="aside_tree_title">
            <div>族群序列</div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio
              :treeData="treeData"
              :needCheckedFirstNode="needCheckedFirstNode"
              :canCancel="true"
              :defaultCheckedKeys="defaultCheckedKeys"
              @clickCallback="clickCallback"
            ></tree-comp-radio>
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <div class="filter_bar_wrap">
            <div class="flex_row_start">
              <div class="filter_item title">筛选</div>
              <div class="filter_item">
                <el-cascader
                  :options="jobLevelTreeData"
                  v-model="jobLevelCode"
                  placeholder="职层"
                  :change-on-select="true"
                  :props="{
                    label: 'value',
                    value: 'code',
                    expandTrigger: 'hover'
                  }"
                  @change="val => handleItemChange(val, 'jobLevelTree')"
                  clearable
                >
                </el-cascader>
              </div>
              <div class="filter_item">
                <el-select v-model="jobGradeCode" clearable placeholder="职等">
                  <el-option
                    v-for="item in jobGradeOptions"
                    :key="item.dictCode"
                    :label="item.codeName"
                    :value="item.dictCode"
                  >
                  </el-option>
                </el-select>
              </div>

              <div class="filter_item">
                <el-input v-model="jobName" placeholder="按职位名称进行检索">
                  <template #suffix>
                    <i class="el-input__icon el-icon-search"></i>
                  </template>
                </el-input>
              </div>
              <div class="filter_item">
                <el-button class="page_add_btn" type="primary" @click="searchJob">查询</el-button>
              </div>
            </div>
          </div>
          <div class="filter_bar_wrap">
            <div class="flex_row_start">
              <div class="filter_item title">操作</div>
              <el-button class="page_add_btn" type="primary" @click="createJob">新增</el-button>
              <el-button class="page_add_btn" type="primary" @click="postImport">导入</el-button>
              <el-button class="page_add_btn" type="primary" @click="postExport">导出</el-button>
            </div>
          </div>
          <div>
            <tableComponent
              :tableData="tableData"
              :needIndex="true"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
            >
              <template #oper>
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button
                      @click.prevent="tableEdit(tableData.data[scope.$index])"
                      :icon="Edit"
                      link
                      class="icon_edit"
                    ></el-button>
                    <el-button
                      class="color_danger icon_del"
                      @click.prevent="tableDeleteRow(tableData.data[scope.$index])"
                      link
                      :icon="Delete"
                    ></el-button>
                  </template>
                </el-table-column>
              </template>
            </tableComponent>
          </div>
        </div>
      </div>
    </div>
    <job-import-popUp v-model:show="showPopUp" @importSign="importSign"></job-import-popUp>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete } from '@element-plus/icons-vue'
import {
  queryJobInfoPage,
  delJobInfo,
  getJobClassTree,
  jobLevelTree,
  getJobGradeList,
  exportJobData,
  exportDownload,
  getDictList
} from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio.vue'
import tableComponent from '@/components/talent/tableComps/tableComponent.vue'
import jobImportPopUp from '../tPopUpComps/jobImportPopUp.vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const useCache = ref(route.params.useCache || false)
const treeData = ref([])
const jobLevelTreeData = ref([])
const jobLevelCode = ref('')
const jobGradeCode = ref('')
const jobGradeOptions = ref([])
const needCheckedFirstNode = ref(true)
const defaultCheckedKeys = ref([])
const jobClassCode = ref('')
const parentPostCode = ref('')
const jobName = ref('')
const showFictitiousOrg = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showPopUp = ref(false)
const hasPostSign = ref(false)
const tableData = reactive({
  columns: [
    {
      label: '职位系统编码',
      prop: 'job_code_extn'
    },
    {
      label: '职位名称',
      prop: 'job_name'
    },
    {
      label: '职位类别',
      prop: 'job_type',
      formatterFun: function (data) {
        return jobTypeObj[data['job_type']]
      }
    },
    {
      label: '职位族群',
      prop: 'parentJobClassName'
    },
    {
      label: '岗位序列',
      prop: 'job_class_name'
    },
    {
      label: '职层',
      prop: 'job_level_name'
    },
    {
      label: '职等',
      prop: 'job_grade_name'
    }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})

const jobTypeObj = reactive({})

// 获取字典
getDictList('JOB_TYPE').then(res => {
  if (res.code == 200) {
    let data = res.data
    data.map(item => {
      jobTypeObj[item.dictCode] = item.codeName
    })
  }
})

function init() {
  if (useCache.value) {
    readCacheParams()
  }
  getJobClassTreeFun()
  getJobLevelTree()
  getJobGradeListFun()
}

function readCacheParams() {
  let cacheParams = userStore.getParams('jobMParams')
  jobLevelCode.value = cacheParams.jobLevelCode
  jobClassCode.value = cacheParams.jobClassCode
  defaultCheckedKeys.value.push(jobClassCode.value)
  jobName.value = cacheParams.jobName
  currentPage.value = cacheParams.current
  pageSize.value = cacheParams.size
}

function getJobClassTreeFun() {
  getJobClassTree({}).then(res => {
    if (res.length > 0) {
      treeData.value = res
    } else {
      treeData.value = []
    }
  })
}

function clickCallback(val, isLastNode) {
  jobClassCode.value = val
  currentPage.value = 1
  queryJobInfoFun()
}

function getJobLevelTree() {
  jobLevelTree({}).then(res => {
    if (res.length > 0) {
      jobLevelTreeData.value = window.$util.formatterData(res)
    } else {
      jobLevelTreeData.value = []
    }
  })
}

function getJobGradeListFun() {
  getJobGradeList({}).then(res => {
    if (res.code == 200) {
      if (res.data.length > 0) {
        jobGradeOptions.value = res.data.map(item => {
          return {
            dictCode: item.jobGradeCode,
            codeName: item.jobGradeName
          }
        })
      }
    }
  })
}

function handleItemChange(val, treeType) {
  if (val) {
    if (treeType == 'jobLevelTree') {
      jobLevelCode.value = ''
      jobLevelCode.value = val.length > 0 ? val[val.length - 1] : ''
    }
    if (treeType == 'postTree') {
      parentPostCode.value = ''
      parentPostCode.value = val.length > 0 ? val[val.length - 1] : ''
    }
  }
}

function searchJob() {
  currentPage.value = 1
  queryJobInfoFun()
}

function createJob() {
  if (jobClassCode.value.split('.').length != 2) {
    ElMessage.warning('请选择序列后新增职位')
    return
  }
  router.push({
    path: '/basicSettingHome/postManagement/createJob',
    query: {
      jobClassCode: jobClassCode.value
    }
  })
}

function queryJobInfoFun(val) {
  let data = {
    jobLevelCode: jobLevelCode.value,
    jobClassCode: jobClassCode.value,
    jobName: jobName.value,
    current: currentPage.value,
    size: pageSize.value,
    jobGradeCode: jobGradeCode.value
  }
  let cacheParams = userStore.getParams('jobMParams')
  let params = {}
  if (useCache.value) {
    params = { ...data, ...cacheParams }
  } else {
    params = { ...cacheParams, ...data }
  }
  let obj = {
    params: params,
    stateKey: 'jobMParams'
  }
  userStore.setParams(obj)
  queryJobInfoPage(params).then(res => {
    if (res.code == 200 && res.data) {
      if (val == 'hasPostSign') {
        if (res.page.total > 0) {
          hasPostSign.value = true
        } else {
          hasPostSign.value = false
        }
      }
      tableData.page.total = res.total
      tableData.data = res.data
    } else {
      tableData.data = []
      tableData.page = {
        total: 0,
        current: 1,
        size: 10
      }
    }
    useCache.value = false
  })
}

function handleSizeChange(size) {
  pageSize.value = size
  queryJobInfoFun()
}

function handleCurrentChange(current) {
  currentPage.value = current
  queryJobInfoFun()
}

function tableEdit(row) {
  router.push({
    path: '/basicSettingHome/postManagement/createJob',
    query: {
      jobCode: row.job_code,
      pageType: 'edit'
    }
  })
}

function tableDeleteRow(row) {
  ElMessageBox.confirm('确认删除?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      deleteJobFun(row['job_code'])
    })
    .catch(() => {})
}

function deleteJobFun(val) {
  delJobInfo({
    jobCode: val
  }).then(res => {
    if (res.code == 200) {
      ElMessage.success(res.msg)
      currentPage.value = 1
      queryJobInfoFun()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function postImport() {
  showPopUp.value = true
}

function postExport() {
  exportJobDataFun()
}

function exportJobDataFun() {
  exportJobData({
    jobLevelCode: jobLevelCode.value ? jobLevelCode.value : '',
    jobClassCode: jobClassCode.value ? jobClassCode.value : '',
    jobName: jobName.value ? jobName.value : ''
  }).then(res => {
    if (res.code == 200) {
      exportDownloadFun(res.data)
    } else {
      ElMessage.warning(res.msg)
    }
  })
}

function exportDownloadFun(val) {
  exportDownload({
    fileName: val
  }).then(res => {
    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '职位列表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  })
}

function importSign(importSign) {
  if (importSign) {
    queryJobInfoFun('hasPostSign')
  }
}

onMounted(() => {
  init()
})
</script>

<style scoped lang="scss">
.post_management_wrap {
  .filter_item {
    .el-button {
      margin: 0 10px 0 0;
    }
  }
  .froBid_import {
    background: #dcdfe6;
    border: 1px solid #dcdfe6;
    color: #666;
  }
  .flex_row_start {
    .el-input__inner {
      width: 180px;
    }
  }
  .aside_tree_list {
    height: 662px !important;
  }
}
.span_col {
  line-height: 32px;
  margin: 0 10px;
}

.el_setting {
  padding: 0;
  font-size: 20px;
}
</style>
