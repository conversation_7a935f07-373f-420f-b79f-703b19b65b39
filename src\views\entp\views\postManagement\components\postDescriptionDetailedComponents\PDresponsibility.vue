<template>
  <div class="responsibility_wrap">
    <div class="page_second_title">主要职责</div>
    <div class="responsibility_content">
      <div class="responsibility_title">
        <div class="index"></div>
        <div class="describe">职责描述</div>
        <div class="proportion">占比</div>
      </div>
      <template v-if="dutyData.length > 0">
        <div class="responsibility_item" v-for="(item, index) in dutyData" :key="item.postRespId">
          <div class="index">{{ index + 1 }}</div>
          <div class="describe overflow_elps">{{ item.respDesc }}</div>
          <div class="proportion">{{ item.timePercentage }}<span class="fs14 color_666">%</span></div>
        </div>
      </template>
      <div class="responsibility_item" v-else>
        <p class="no_data_tip">暂无数据</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { jobRespInfo } from '../../../../request/api'

const props = defineProps({
  jobCode: String
})

const dutyData = ref([])

async function jobRespInfoFun() {
  if (!props.jobCode) return

  const res = await jobRespInfo({
    jobCode: props.jobCode
  })

  dutyData.value = []
  if (res.code == 200) {
    dutyData.value = res.data
  }
}

onMounted(jobRespInfoFun)
watch(() => props.jobCode, jobRespInfoFun)
</script>

<style scoped lang="scss">
.responsibility_wrap {
  .responsibility_content {
    .page_second_title {
      margin: 10px 0;
      padding-bottom: 10px;
      border-bottom: 1px solid #e5e5e5;
      position: relative;
    }
    .index {
      width: 50px;
      font-size: 16px;
      color: #0099ff;
    }
    .describe {
      width: 650px;
      margin: 0 20px 0 0;
      line-height: 1.5;
    }
    .proportion {
      width: 80px;
      font-size: 16px;
      color: #0099ff;
    }
    .responsibility_title {
      display: flex;
      flex-flow: row nowrap;
      justify-content: flex-start;
      color: #0099fd;
      font-size: 14px;
      line-height: 30px;
      padding: 5px 16px;
      background: #ebf4ff;
      border: 1px solid #e5e5e5;
      border-bottom: none;
      .describe {
        font-size: 16px;
      }
      .proportion {
        font-size: 16px;
      }
    }
    .responsibility_item {
      display: flex;
      flex-flow: row nowrap;
      justify-content: flex-start;
      line-height: 45px;
      padding: 5px 16px;
      align-items: center;
      border: 1px solid #e5e5e5;
      border-bottom: none;
      font-size: 12px;
      &:last-of-type {
        border-bottom: 1px solid #e5e5e5;
      }
    }
  }
}
</style>
