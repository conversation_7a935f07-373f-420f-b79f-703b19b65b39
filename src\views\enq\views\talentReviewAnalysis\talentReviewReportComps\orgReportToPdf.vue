<template>
  <div class="report_wrap" id="testPdf">
    <div class="logo_wsyjy">
      <!-- <img class="img" id="logo" src="../../../../../../public/images/logo_wenshang3.png" alt="" srcset=""> -->
      <img class="img" id="logo" :src="logoUrl" alt="" srcset="" />
    </div>
    <reportCover
      id="pdf_cover"
      class="pdf_cover"
      :beginDate="beginDate"
      :orgName="orgInfo.orgName"
      :userInfo="userInfo"
      :type="'D'"
    />
    <!-- <div class="page_main_title">
            {{ enqName }}--盘点报告(部门报告) {{ beginDate | removeTime }}
        </div> -->
    <template v-for="(comp, index) in compArr" :key="comp.key">
      <component
        class="pdf_wrap"
        :is="comp.comp"
        :enqId="enqId"
        :orgCode="orgCode"
        :isPdf="true"
        :id="'pdf_module_' + comp.key"
      >
      </component>
    </template>
    <div class="page_section">
      <div class="page_section_center clearfix">
        <!-- <div class="report_header_info">
                    <span class="name">{{ orgInfo.orgName }}</span>
                    <span class>
                        岗位:
                        <span>{{ orgInfo.postCount }}</span>
                    </span>
                    <span class="marginR_16 marginL_16">|</span>
                    <span class>
                        人员:
                        {{ orgInfo.userCount }}
                    </span>
                </div> -->
        <!-- <template v-for="(comp, key) in compArr">
                    <component
                        :key="key"
                        :is="comp"
                        :enqId="enqId"
                        :orgCode="orgCode"
                        :isPdf="true"
                        :id="'pdf_dom_' + key"
                    ></component>
                </template> -->
      </div>
    </div>
  </div>
</template>

<script>
const files = require.context('./orgReportComps', false, /\.vue$/)
let compMap = {
  // DN01(对应模块code):files(报告模块对应的前端文件)
}
for (const fileName of files.keys()) {
  let keyStr = fileName.split('/')[1].split('.')[0].split('_')[0]
  compMap[keyStr] = files(fileName).default
}

import reportCover from './reportCover'
import { getOrgReportOrgInfo, getEnqInfo, getCompanyInfo, fileDownload } from '../../../request/api'
export default {
  name: 'orgReportToPdf',
  props: ['moduleCodeArr'],
  components: {
    reportCover
  },
  data() {
    return {
      logoUrl: '',
      pdfDomArr: ['pdf_cover'],
      enqName: '',
      beginDate: '',
      userInfo: {
        userName: '',
        postName: '',
        orgName: ''
      },
      orgInfo: {
        orgName: '',
        postNum: '',
        staffNum: ''
      },
      enqId: this.$route.query.enqId,
      orgCode: this.$route.query.orgCode
      // compObj: compMap,
    }
  },
  created() {
    this.getOrgReportOrgInfoFun()
    this.getEnqInfoFn()
    getCompanyInfo().then(res => {
      console.log('res :>> ', res)
      console.log(process.env)
      let origin = location.origin
      if (res.logoUrlPath) {
        this.logoUrl = origin + '/edp/api/entp' + res.logoUrlPath
      }
      // /upload/company_info / 1ea0645bae9ae6ea4b1959db87e4ae05.jpg
    })
  },
  computed: {
    compArr() {
      let arr = []
      for (const key in compMap) {
        if (Object.hasOwnProperty.call(compMap, key)) {
          if (this.moduleCodeArr.includes(key)) {
            arr.push({
              key: key,
              comp: compMap[key]
            })
          }
        }
      }
      return arr
    }
  },
  methods: {
    getImgFileStreamFun(id) {
      let params = {
        id: id
      }
      fileDownload(params).then(res => {
        let reader = new FileReader()
        reader.readAsDataURL(res) // 转换为base64，可以直接放入a表情href
        reader.onload = e => {
          this.logoBase64 = 'data:image/png;base64,' + e.target.result.split(',')[1]
        }
      })
    },
    getOrgReportOrgInfoFun() {
      let params = {
        enqId: this.enqId,
        orgCode: this.orgCode
      }
      getOrgReportOrgInfo(params).then(res => {
        console.log(res)
        if (res.code == '200') {
          this.orgInfo = res.data
        }
      })
    },
    getEnqInfoFn() {
      getEnqInfo({ id: this.enqId }).then(res => {
        console.log(res)
        this.enqName = res.data.enqName
        this.beginDate = res.data.beginDate
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import url('./pdf.less');
.report_wrap {
  //    opacity: 0;
  .org_report_main {
    padding: 0 40px;
  }
}
.page_second_title {
  margin-bottom: 16px;
}
.report_header_info {
  color: #212121;
  font-size: 14px;
  margin-bottom: 30px;
  .name {
    color: #0099ff;
    font-size: 16px;
    margin-right: 14px;
    font-weight: bold;
  }
}
.step_bar {
  margin-bottom: 24px;
}
.oper_btn_wrap {
  text-align: center;
  padding-top: 16px;
}
// 去除select下拉箭头
.el-input__suffix {
  display: none;
}
.el-col {
  margin-bottom: 20px;
}
.item_title {
  line-height: 24px;
  color: #0099ff;
  background-color: #e5f7fd;
  font-size: 14px;
  padding-left: 16px;
  margin-bottom: 5px;
}
.chart_box {
  min-height: 200px;
  border: 1px solid #dcdfe6;
}
.table_tip {
  text-align: right;
  color: rgb(248, 131, 131);
}

.logo_wsyjy {
  width: 100%;
  margin-bottom: 20px;
  padding-bottom: 20px;
  // box-shadow:  0px 10px 10px -10px #a5a4a4;
  .img {
    height: 100px;
    width: auto;
  }
}
.pdf_wrap {
  .page_second_title {
    font-size: 30px !important;
    line-height: 50px;
    &::before {
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .page_third_title {
    font-size: 26px !important;
  }
  .chart_box {
    min-height: 500px;
    // height: 300px;
    border: 1px solid #dcdfe6;
    text-align: center;
  }
  .basic_info_wrap .basic_info_item {
    .title {
      font-size: 26px;
    }
    .text {
      font-size: 26px;
    }
  }
  .item_title {
    font-size: 26px !important;
    line-height: 46px !important;
  }
  .el-table {
    font-size: 24px;
    .cell {
      line-height: 38px;
      padding-left: 2px;
      padding-right: 2px;
    }
  }
  .el-table__body tr {
    td {
      font-size: 24px;
      padding: 12px 0;
      line-height: 28px;
      letter-spacing: 2px;
    }
  }
  .distribution_box {
    font-size: 24px;
    line-height: 1.3;
    .box_title {
      line-height: 1.5;
    }
  }
  .manage_advice_wrap {
    font-size: 24px;
    .manage_advice_item .item_info {
      line-height: 30px;
    }
  }
  .explain_text {
    font-size: 24px;
    line-height: 32px;
  }
  .incentive_wrap {
    font-size: 24px;
    .incentive_item .desc_wrap {
      line-height: 28px;
    }
  }
  .staff_authorized_strength {
    font-size: 24px;
  }
  .talent_matrix_chart {
    font-size: 16px;
  }
  .matrix_chart {
    font-size: 16px;
  }
}
</style>
