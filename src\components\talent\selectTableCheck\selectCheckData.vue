<template>
  <div class="select_check_data_wrap">
    <div class="select_check_data_main">
      <el-table
        ref="tableRef"
        :data="tableData.data"
        :border="true"
        :max-height="maxHeight"
        style="width: 100%"
        @cell-click="handleCellClick"
      >
        <el-table-column
          v-for="(item, index) in tableData.tableTitle"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          align="center"
        >
          <template #default="scope">
            <template v-if="item.canCheck">
              <div
                class="check_box"
                :class="{
                  'is-checked': scope.row[item.prop],
                  'is-disabled': !scope.row[item.disabledSign]
                }"
              >
                <el-icon v-if="scope.row[item.prop]"><Check /></el-icon>
              </div>
            </template>
            <template v-else>
              {{ scope.row[item.prop] }}
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Check } from '@element-plus/icons-vue'

const props = defineProps({
  tableData: {
    type: Object,
    default: () => ({
      tableTitle: [],
      data: []
    })
  },
  type: {
    type: String,
    default: 'radio'
  },
  numLength: {
    type: Number,
    default: 1
  },
  maxHeight: {
    type: Number,
    default: 500
  }
})

const emit = defineEmits(['tableDataFn'])

const tableRef = ref(null)

// 监听表格数据变化
watch(
  () => props.tableData,
  () => {
    if (tableRef.value) {
      tableRef.value.doLayout()
    }
  },
  { deep: true }
)

// 处理单元格点击
const handleCellClick = (row, column) => {
  if (!row[`targetKpiCode-${column.property.split('-')[1]}`]) {
    return
  }

  if (typeof row[column.property] !== 'boolean') {
    return
  }

  const rowIndex = props.tableData.data.findIndex(item => item.index == row.index)
  if (rowIndex == -1) return

  const currentRow = props.tableData.data[rowIndex]
  const columnIndex = parseInt(column.property.split('-')[1]) - 1
  const checkKpiCode = currentRow.kpiCodeList[columnIndex]

  if (props.type == 'radio') {
    // 单选模式
    currentRow.selectedCodes = []
    for (let i = 0; i < props.tableData.tableTitle.length - props.numLength; i++) {
      currentRow[`targetName-${i + 1}`] = false
    }
    currentRow[column.property] = true
    currentRow.selectedCodes.push(checkKpiCode)
  } else {
    // 多选模式
    if (currentRow[column.property]) {
      currentRow[column.property] = false
      const codeIndex = currentRow.selectedCodes.indexOf(checkKpiCode)
      if (codeIndex > -1) {
        currentRow.selectedCodes.splice(codeIndex, 1)
      }
    } else {
      currentRow[column.property] = true
      currentRow.selectedCodes.push(checkKpiCode)
    }
  }

  emit('tableDataFn', props.tableData.data)
}
</script>

<style scoped lang="scss">
.select_check_data_wrap {
  .select_check_data_main {
    :deep(.el-table) {
      .el-table__header {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
          padding: 8px 0;
        }
      }

      .el-table__body {
        td {
          padding: 8px;
          cursor: pointer;
        }
      }

      .check_box {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 28px;
        height: 28px;
        border: 1px solid #dcdfe6;
        border-radius: 2px;
        transition: all 0.3s;

        &:hover:not(.is-disabled) {
          border-color: #409eff;
        }

        &.is-checked {
          background-color: #409eff;
          border-color: #409eff;
          color: #fff;
        }

        &.is-disabled {
          background-color: #f5f7fa;
          border-color: #dcdfe6;
          cursor: not-allowed;
        }

        .el-icon {
          font-size: 16px;
          line-height: 1;
        }
      }
    }
  }
}
</style>
