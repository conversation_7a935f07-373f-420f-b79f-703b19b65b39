<template>
    <div class="">
        <div class="page_third_title">认知偏差
            <div class="fr">
                <el-button type="primary" @click="exportExcel" size="mini">导出</el-button>
            </div>
        </div>
        <table-component
            :tableData="tableData"
            :needIndex="true"
            :loading="loading"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
        ></table-component>
    </div>
</template>
 
<script>
    import { cognitiveBias,allExportData } from "../../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "cognitiveBias",
        props: ["orgCode", "evalId"],
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading: false,
                tableData: {
                    columns: [
                        {
                            label: "姓名",
                            prop: "objectName",
                        },
                        {
                            label: "岗位",
                            prop: "postName",
                        },
                        {
                            label: "综合得分",
                            prop: "overallScore",
                        },
                        {
                            label: "最低得分",
                            prop: "mins",
                        },
                        {
                            label: "最高得分",
                            prop: "maxs",
                        },
                        {
                            label: "标准差",
                            prop: "deviation",
                        },
                        {
                            label: "排名",
                            prop: "rank",
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        components: {
            tableComponent,
        },
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
        },

        methods: {
            getData() {
                this.loading = true;
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                cognitiveBias(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                        this.loading = false;
                    } else {
                        this.loading = false;
                    }
                });
            },
            handleSizeChange(size) {
                this.pageSize = size;
                this.getData();
            },
            handleCurrentChange(current) {
                this.currPage = current;
                this.getData();
            },
            exportExcel(){
                let params = {
                    evalId:this.evalId,
                    orgCode:this.orgCode,
                    type:'cognitive'
                }
                allExportData(params).then(res => {
                    console.log(res);
                    this.$exportDownload(res.data,'认知偏差');
                })
            }
        },
    };
</script>
 
<style scoped lang="scss">
</style>