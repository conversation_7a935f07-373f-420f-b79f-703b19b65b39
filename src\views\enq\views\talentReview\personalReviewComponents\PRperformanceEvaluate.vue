<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getKarmaEvaluation,
  getQualityEvaluationItem,
  saveSurveySubmit,
  saveDurveyUserResult,
  saveEvaluationComment
} from '../../../request/api'
import evaluateTableSelect from './evaluateTableSelect.vue'
import qualityEvaluationList from './qualityEvaluationList.vue'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const savaFlag = ref(true)
const currIndex = ref(0)
const personnelData = ref([])
const expectationPostOptions = ref({})
const type = ref('performance')

function getChildDataFn(data) {
  if (data) {
    let params = {
      enqId: props.enqId,
      enqItemOptionRequests: data,
      modelId: personnelData.value[currIndex.value].modelId,
      moduleCode: personnelData.value[currIndex.value].moduleCode
    }
    saveSurveySubmit(params).then(res => {
      if (res.code == '200') {
        ElMessage.success(res.msg)
        getDeptUserPostFun()
        if (currIndex.value != personnelData.value.length - 1) {
          currIndex.value++
        }
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}

function getDeptUserPostFun() {
  let params = {
    enqId: props.enqId
  }
  getKarmaEvaluation(params).then(res => {
    if (res.code == '200') {
      if (res.data && res.data.length != 0) {
        personnelData.value = res.data
        getQualityEvaluationItemFun(res.data[currIndex.value])
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function selectPersonnel(item, index) {
  currIndex.value = index
  getQualityEvaluationItemFun(item)
}

function getQualityEvaluationItemFun(item) {
  let params = {
    enqId: props.enqId,
    modelId: item.modelId,
    moduleCode: item.moduleCode
  }
  getQualityEvaluationItem(params).then(res => {
    if (res.code == '200') {
      expectationPostOptions.value = res.data
    } else {
      expectationPostOptions.value = {}
    }
  })
}

function getPotentialDataFn(data) {
  if (data) {
    let arr = []
    data.forEach(item => {
      arr.push({
        comment: item.comment,
        relationType: item.relationType,
        userId: item.userId,
        enqId: props.enqId,
        type: type.value
      })
    })
    saveEvaluationComment(arr).then(res => {
      if (res.code == '200') {
        ElMessage.success('保存成功!')
        getDeptUserPostFun()
      } else {
        ElMessage.error('保存失败!')
      }
    })
  }
}

function submitForm() {
  let params = {
    enqId: props.enqId,
    types: 'performance',
    modelId: personnelData.value[currIndex.value].modelId
  }
  saveDurveyUserResult(params).then(res => {
    if (res.code == '200') {
      nextBtn()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function prevBtn() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submitForm()
    })
    .catch(action => {
      if (action === 'cancel') {
        ElMessage.info('已放弃修改并返回上一步')
        emit('prevStep')
      } else {
        ElMessage.info('取消返回上一步')
      }
    })
}

function nextBtn() {
  emit('nextStep')
}

watch(
  () => expectationPostOptions.value,
  val => {
    // 你可以在这里处理副作用
    // console.log(val)
  },
  { deep: true }
)

onMounted(() => {
  getDeptUserPostFun()
})
</script>

<template>
  <div class="task_confirmation_main PRperformance_Evaluate_wrap marginT_16">
    <div class="page_second_title">业绩评价</div>
    <div class="department_main marginT_8">
      <div class="personnel_item_wrap_left">
        <div
          class="personnel_item"
          v-for="(item, index) in personnelData"
          :class="{
            completed: item.statusName == 'Y',
            curr: currIndex == index
          }"
          :key="index"
          @click="() => selectPersonnel(item, index)"
        >
          <span>{{ item.moduleName }}</span>
          <i class="icon el-icon-check" v-if="item.statusName == 'Y'"></i>
          <i class="icon disc" v-else></i>
        </div>
      </div>
      <div class="personnel_item_wrap_right">
        <div class="project_item">
          <span class="moduleName">{{ (personnelData && personnelData[currIndex]?.moduleName) || '' }}：</span>
          <span class="item">{{ expectationPostOptions?.item || '' }}</span>
        </div>
        <div v-if="personnelData.length - 1 != currIndex">
          <evaluateTableSelect
            :types="'performance'"
            :dataFrom="expectationPostOptions"
            @getChildData="getChildDataFn"
          />
        </div>
        <div v-else>
          <qualityEvaluationList :enqId="props.enqId" :type="type" @getPotentialData="getPotentialDataFn" />
        </div>
        <div class="btn_wrap align_center marginT_16">
          <el-button
            class="page_new_confirm_btn"
            type="primary"
            @click="prevBtn"
            v-show="props.currentIndex != props.currentFirstCode"
            >上一步</el-button
          >
          <el-button class="page_new_confirm_btn" type="primary" @click="submitForm">{{ props.nextBtnText }}</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.department_main {
  display: flex;
  .personnel_item_wrap_left {
    width: 200px;
    padding-right: 15px;
    border-right: 1px solid #ccc;
    height: 600px;
    overflow: auto;
    .personnel_item {
      line-height: 30px;
      padding: 0 8px;
      color: #525e6c;
      font-size: 14px;
      background: #f8f8f8;
      margin-bottom: 5px;
      font-weight: bold;
      cursor: pointer;

      &.completed {
        color: #0099fd;
        background: #eef5fb;

        .icon {
          display: block;
        }
      }

      &.curr {
        background: #0099fd;
        color: #fff;

        .icon {
          display: block;
          color: #fff;

          &.disc {
            background: #fff;
          }
        }
      }

      .icon {
        float: right;
        font-weight: bold;
        line-height: 30px;
        text-align: center;
        color: #0099fd;

        &.disc {
          width: 8px;
          height: 8px;
          margin: 10px 4px 0 auto;
          border-radius: 50%;
          background: #ffc000;
        }
      }
    }
  }
  .personnel_item_wrap_right {
    padding-left: 15px;
    width: calc(100% - 200px);
    .project_item {
      padding: 10px 0;
      font-size: 14px;
      font-weight: bold;
      .moduleName {
        color: #0099ff;
      }
      .item {
        color: #333;
      }
    }
    :deep(.el-table) {
      // max-height: 370px !important;
      // overflow: auto;
    }
    :deep(.check_column_act) {
      .cell::after {
        content: '' !important;
      }
    }
  }
}
.PRperformance_Evaluate_wrap {
}
</style>
