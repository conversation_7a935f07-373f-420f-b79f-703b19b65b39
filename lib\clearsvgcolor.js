// 清除svg颜色
import fs from 'fs'
// const fs = require('fs')
const files = fs.readdirSync('./src/assets/clearColorSvg').filter(item => item.includes('.svg'))
console.log(files)
if (files.length === 0) {
  // 此处原代码使用 return false 会导致语法错误，因为它不在函数内部。
  // 这里可以直接使用 console.log 输出提示信息，然后使用 break 语句跳出循环（虽然这里没有循环，但可以避免后续代码执行）
  console.log('没有找到svg文件，结束操作')
  process.exit(0) // 结束程序
}
for (let i = 0; i < files.length; i++) {
  const fileName = `./src/assets/clearColorSvg/${files[i]}`
  fs.readFile(fileName, 'utf-8', (error, data) => {
    // const newFileData = data.replace(/\s*(fill|stroke)="(.*?)"|<title.*?title>/g, '').replace(/^<svg /, '<svg fill="currentColor" ');
    const newFileData = data
      .replace(/\s*(fill|stroke)="(.*?)"|<title.*?title>/g, '')
      .replace(/<style>(\S|\s)+?<\/style>/g, '')
      .replace(/^<svg /, '<svg fill="currentColor" ')
    fs.writeFile(fileName, newFileData, 'utf-8', () => {
      console.log('替换完成')
    })
  })
}
