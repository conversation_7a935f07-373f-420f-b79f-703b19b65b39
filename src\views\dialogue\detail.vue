<template>
  <div class="dialogue-detail">
    <ChatFrame :id="sessionId" style="height: calc(100vh - 130px)"></ChatFrame>
  </div>
</template>
<script setup>
defineOptions({ name: 'DialogueDetail' })
import ChatFrame from '@/components/AI/chatFrame.vue'
const route = useRoute()
const sessionId = ref('')
onMounted(() => {
  sessionId.value = route.params.id
})
watch(
  () => route.fullPath,
  () => {
    sessionId.value = route.params.id
  }
)
</script>
