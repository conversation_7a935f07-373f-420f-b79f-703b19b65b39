<template>
    <div class="hierarchic_norm_wrap">
        <div class="hierarchic_norm_main flex_row_betweens">
            <div class="main_left page_section">
                <div class="hierarchic_norm_title flex_row_betweens">
                    <div class="page_second_title">程度分类</div>
                    <div class="align_right" v-if="buildStatus !== '4'">
                        <el-button
                            class="icon_plus"
                            icon="el-icon-plus"
                            link
                            size="mini"
                            @click="creatDegree"
                        ></el-button>
                    </div>
                </div>
                <ul class="degree_list_wrap">
                    <li
                        class="flex_row_betweens"
                        :class="{ active: index == currIndex }"
                        v-for="(item, index) in degreeList"
                        @click="selectItem(index)"
                    >
                        <div class="left">{{ item.gradeName }}</div>
                        <div class="icon_group" v-if="buildStatus !== '4'">
                            <!--                            <span class="el-icon-edit icon_edit" @click="editorDegree"></span>-->
                            <span
                                class="el-icon-delete icon_del"
                                @click="removeDegree(item.gradeCode, index)"
                            ></span>
                        </div>
                    </li>
                    <li class="no_data_tip" v-if="degreeList.length == 0">
                        暂无数据
                    </li>
                </ul>
            </div>
            <div class="main_right" :class="{ event_none: buildStatus == '4' }">
                <div class="degree_info_wrap" v-show="degreeList.length > 0">
                    <el-form
                        :model="ruleForm"
                        :rules="rules"
                        ref="ruleForm"
                        label-position="top"
                        class="demo-ruleForm"
                    >
                        <el-form-item label="分类名称" prop="gradeName">
                            <el-input v-model="ruleForm.gradeName"></el-input>
                        </el-form-item>
                        <el-form-item label="别名" prop="gradeShortName">
                            <el-input
                                v-model="ruleForm.gradeShortName"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="分值" required>
                            <el-col :span="11">
                                <el-form-item prop="beginScore">
                                    <el-input
                                        type="number"
                                        min="0"
                                        max="100"
                                        v-model.number="ruleForm.beginScore"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col class="line align_center" :span="2"
                                >——</el-col
                            >
                            <el-col :span="11">
                                <el-form-item prop="endScore">
                                    <el-input
                                        type="number"
                                        min="0"
                                        max="100"
                                        v-model.number="ruleForm.endScore"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </el-form-item>
                        <el-form-item label="程度说明" prop="gradeDesc">
                            <el-input
                                type="textarea"
                                v-model="ruleForm.gradeDesc"
                                resize="none"
                                placeholder="最多1024个字符"
                            ></el-input>
                        </el-form-item>
                        <div class="align_center" v-if="buildStatus !== '4'">
                            <el-button
                                class="page_add_btn"
                                type="primary"
                                @click="submitItem"
                                >确认</el-button
                            >
                        </div>
                    </el-form>

                    <!-- <div class="line_item_wrap">
                        <div class="descript">分类名称</div>
                        <div class="input_wrap">
                            <el-input v-model="degreeInfo.gradeName"></el-input>
                        </div>
                    </div>
                    <div class="line_item_wrap">
                        <div class="descript">
                            别名<span class="tips">(建议2个文字)</span>
                        </div>
                        <div class="input_wrap">
                            <el-input
                                v-model="degreeInfo.gradeShortName"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line_item_wrap">
                        <div class="descript">分值</div>
                        <div class="input_wrap">
                            <span>
                                <el-input
                                    type="number"
                                    v-model="degreeInfo.beginScore"
                                    @input="getBeginScore"
                                ></el-input>
                            </span>
                            <i>-</i>
                            <span>
                                <el-input
                                    type="number"
                                    v-model="degreeInfo.endScore"
                                    @input="getEndScore"
                                ></el-input>
                            </span>
                        </div>
                    </div>
                    <div class="line_item_wrap">
                        <div class="descript">程度说明</div>
                        <div class="input_wrap">
                            <el-input
                                type="textarea"
                                v-model="degreeInfo.gradeDesc"
                                rows="3"
                                resize="none"
                            ></el-input>
                        </div>
                    </div>
                    <div class="align_center" v-if="buildStatus !== '4'">
                        <el-button
                            class="page_add_btn"
                            type="primary"
                            @click="submitItem"
                            >确认</el-button
                        >
                    </div> -->
                </div>
            </div>
        </div>
        <div class="align_center" v-if="buildStatus !== '4'">
            <el-button class="page_confirm_btn" type="primary" @click="prev()"
                >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="next()"
                >下一步</el-button
            >
        </div>
        <div class="popup_wrap">
            <el-dialog :title="popupName" :visible.sync="dialogFormVisible">
                <div class="line_item_wrap">
                    <div class="descript">分类名称</div>
                    <div class="input_wrap">
                        <el-input
                            ref="degreeName"
                            v-model="degreeName"
                            @keyup.enter.native="addClassify"
                        ></el-input>
                    </div>
                </div>
                <div slot="footer" class="dialog-footer">
                    <el-button
                        class="page_clear_btn"
                        @click="dialogFormVisible = false"
                        >取 消</el-button
                    >
                    <el-button
                        class="page_add_btn"
                        type="primary"
                        @click="addClassify"
                        >确 定</el-button
                    >
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
    import {
        createModelOption,
        getModelGrade,
        deleteModelOption,
        getGradeLastScore,
        gradeCheck,
    } from "../../../request/api";

    export default {
        name: "hierarchicNorm",
        props: ["modelId", "buildStatus"],
        components: {},
        data() {
            var checScore = (rule, value, callback) => {
                if (!value) {
                    if (value == 0) {
                        callback();
                    } else {
                        return callback(new Error("分值不能为空"));
                    }
                }
                setTimeout(() => {
                    if (!Number.isInteger(value)) {
                        callback(new Error("请输入数字值"));
                    } else {
                        if (value > 100) {
                            callback(new Error("分值最大为100"));
                        } else {
                            callback();
                        }
                    }
                }, 1000);
            };
            return {
                currIndex: 0,
                degreeList: [],
                dialogFormVisible: false,
                degreeName: "",
                popupName: "",
                // degreeInfo: {
                //     beginScore: "",
                //     endScore: "",
                //     gradeCode: "",
                //     gradeDesc: "",
                //     gradeName: "",
                //     gradeShortName: "",
                // },
                ruleForm: {
                    beginScore: "",
                    endScore: "",
                    gradeCode: "",
                    gradeDesc: "",
                    gradeName: "",
                    gradeShortName: "",
                },
                rules: {
                    gradeName: [
                        {
                            required: true,
                            message: "请填写分类名称",
                            trigger: "blur",
                        },
                    ],
                    gradeShortName: [
                        {
                            required: true,
                            message: "请填写别名",
                            trigger: "blur",
                        },
                        {
                            min: 0,
                            max: 8,
                            message: "最多8个字符",
                            trigger: "blur",
                        },
                    ],
                    gradeDesc: [
                        {
                            required: true,
                            message: "请填写程度说明",
                            trigger: "blur",
                        },
                        {
                            min: 0,
                            max: 1024,
                            message: "最多1024个字符",
                            trigger: "blur",
                        },
                    ],
                    beginScore: [
                        {
                            required: true,
                            // message: "请填写分值区间",
                            trigger: "blur",
                            validator: checScore,
                        },
                    ],
                    endScore: [
                        {
                            required: true,
                            // message: "请填写分值区间",
                            trigger: "blur",
                            validator: checScore,
                        },
                    ],
                },
            };
        },
        created() {
            if (this.modelId) {
                this.getModelGradeFun();
            }
        },
        methods: {
            // getBeginScore(val) {
            //     if (val > 100) {
            //         this.degreeInfo.beginScore = 100;
            //     } else {
            //         this.degreeInfo.beginScore = val.split(".")[0];
            //     }
            // },
            // getEndScore(val) {
            //     if (val > 100) {
            //         this.degreeInfo.endScore = 100;
            //     } else {
            //         this.degreeInfo.endScore = val.split(".")[0];
            //     }
            // },
            creatDegree() {
                this.dialogFormVisible = true;
                this.popupName = "新增";
                this.$nextTick(() => {
                    this.$refs.degreeName.focus();
                });
            },
            //添加按钮
            addClassify() {
                this.getGradeLastScoreFun().then((res) => {
                    console.log(res);
                    this.degreeList.push({
                        beginScore: res || 0,
                        endScore: "",
                        gradeCode: "",
                        gradeDesc: "",
                        gradeName: this.degreeName,
                        gradeShortName: "",
                    });
                    this.dialogFormVisible = false;
                    this.degreeName = "";
                    this.currIndex = this.degreeList.length - 1;
                    this.ruleForm = this.degreeList[this.currIndex];
                });
            },
            selectItem(index) {
                this.currIndex = index;
                this.ruleForm = this.degreeList[index];
            },
            submitItem() {
                this.submitForm("ruleForm");
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.createModelOptionFun();
                    } else {
                        console.log("error submit!!");
                        return false;
                    }
                });
            },
            removeDegree(moduleCode, index) {
                this.$confirm("此操作将删除该程度分类, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        if (!moduleCode) {
                            this.degreeList.splice(index, 1);
                            this.$message({
                                type: "success",
                                message: "删除成功!",
                            });
                            this.getModelGradeFun();
                            this.currIndex = 0;
                        } else {
                            this.deleteModelOptionFun(moduleCode);
                        }
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            },
            prev() {
                this.$emit("prevStep");
            },
            next() {
                if (this.degreeList.length == 0) {
                    this.$msg.warning("请至少添加一项程度分类");
                    return;
                }else{
                    for (let index = 0; index < this.degreeList.length; index++) {
                        const item = this.degreeList[index];
                        if(item.gradeCode == ""){
                            this.$msg.warning(`能力分类:${item.gradeName} 信息未确认`)
                            return
                        }
                    }
                }
                this.gradeCheckFun();
            },
            getModelGradeFun() {
                getModelGrade({
                    modelId: this.modelId,
                }).then((res) => {
                    console.log(res);
                    this.degreeList = res;
                    if (this.degreeList.length > 0) {
                        this.ruleForm = this.degreeList[this.currIndex];
                    } else {
                        this.ruleForm = {
                            beginScore: "",
                            endScore: "",
                            gradeCode: "",
                            gradeDesc: "",
                            gradeName: "",
                            gradeShortName: "",
                        };
                    }
                });
            },
            //创建、修改能力分类
            createModelOptionFun() {
                if (this.ruleForm.endScore < this.ruleForm.beginScore) {
                    this.$msg.warning("结束分值需大于开始分值！");
                    return;
                }
                let params = {
                    modelId: this.modelId,
                    beginScore: this.ruleForm.beginScore,
                    endScore: this.ruleForm.endScore,
                    gradeCode: this.ruleForm.gradeCode,
                    gradeDesc: this.ruleForm.gradeDesc,
                    gradeName: this.ruleForm.gradeName,
                    gradeShortName: this.ruleForm.gradeShortName,
                };
                console.log(params);
                createModelOption(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$msg.success(res.msg);
                        this.currIndex = 0;
                        this.getModelGradeFun();
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            deleteModelOptionFun(moduleCode) {
                let params = {
                    modelId: this.modelId,
                    optionNbr: moduleCode,
                };
                deleteModelOption(params).then((res) => {
                    if (res.code == 200) {
                        this.$msg.success("删除成功！");
                        this.getModelGradeFun();
                        this.currIndex = 0;
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            async getGradeLastScoreFun() {
                return await getGradeLastScore({
                    modelId: this.modelId,
                }).then((res) => {
                    console.log(res);
                    return res;
                });
            },
            gradeCheckFun() {
                gradeCheck({
                    modelId: this.modelId,
                }).then((res) => {
                    if (res.code == 200) {
                        this.$emit("nextStep");
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .hierarchic_norm_wrap {
        .hierarchic_norm_main {
            margin: 20px 0;

            .hierarchic_norm_title {
                font-size: 16px;
                line-height: 30px;
                height: 30px;
                border-radius: 3px;
            }

            .main_left {
                width: 40%;

                .hierarchic_norm_title {
                    span {
                        margin: 0 0 0 10px;

                        &.creat {
                            font-weight: 700;
                            cursor: pointer;
                        }

                        &.creat:hover {
                            color: #4ab6fd;
                        }
                    }
                }

                .degree_list_wrap {
                    margin-top: 5px;
                    border: 1px solid #e5e5e5;
                    height: 400px;
                    overflow-y: auto;

                    li {
                        height: 40px;
                        line-height: 40px;
                        padding: 0 8px;
                        cursor: pointer;

                        .icon_group {
                            span {
                                margin: 0 0 0 10px;
                                cursor: pointer;
                            }

                            span:hover {
                                color: #4ab6fd;
                            }
                        }
                    }

                    .active {
                        cursor: pointer;

                        .left {
                            color: #fff;
                        }

                        .icon_edit,
                        .icon_del {
                            color: #fff !important;
                        }

                        background: #0099FF;
                    }
                }
            }

            .main_right {
                width: 60%;
                height: 400px;
                border: 1px solid #e5e5e5;
                margin-top: 50px;
                overflow-y: auto;

                .degree_info_wrap {
                    .line_item_wrap {
                        padding: 0 8px;
                        margin-bottom: 10px;

                        .descript {
                            line-height: 32px;
                            font-size: 14px;
                            position: relative;
                            padding-left: 15px;
                            margin-top: 5px;

                            &::after {
                                content: "";
                                position: absolute;
                                width: 10px;
                                height: 10px;
                                left: 0;
                                top: 11px;
                                background: #0099FF;
                                border-radius: 50%;
                            }

                            .tips {
                                display: inline-block;
                                color: #f33333;
                                font-size: 12px;
                            }
                        }

                        .input_wrap {
                            display: flex;
                            span {
                                width: 20%;
                            }
                            i {
                                width: 5%;
                                font-style: normal;
                                line-height: 30px;
                                text-align: center;
                            }
                        }
                    }

                    .btn {
                        margin: 10px 8px 0 10px;
                        text-align: right;
                    }
                }
            }
        }
    }

    .popup_wrap {
        .line_item_wrap {
            display: flex;

            .descript {
                width: 100px;
                height: 30px;
                line-height: 30px;
            }

            .input_wrap {
                width: 85%;
            }
        }
    }

    .demo-ruleForm {
        padding-left: 8px;
        padding-right: 8px;
    }
    .el-form--label-top .el-form-item__label {
        position: relative;
        padding-left: 15px;
    }
    .el-form-item.is-required:not(.is-no-asterisk)
        > .el-form-item__label:before {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        left: 0;
        top: 11px;
        background: #0099FF;
        border-radius: 50%;
    }
</style>