<script setup>
defineOptions({ name: 'libraryRefer' })
const menuList = ref([
  {
    title: '行业典型指标',
    path: '/indicator/libraryRefer/typical',
    children: ['/indicator/libraryRefer/typical']
  },
  {
    title: '典型岗位承接',
    path: '/indicator/libraryRefer/undertake',
    children: ['/indicator/libraryRefer/undertake']
  },
  {
    title: '指标对应能力',
    path: '/indicator/libraryRefer/ability',
    children: ['/indicator/libraryRefer/ability']
  }
])
const active = ref('')
const router = useRouter()
const route = useRoute()
const goPath = path => {
  active.value = path
  router.push(path)
}
</script>
<template>
  <div class="targetSpotDiagnosis">
    <div class="menu">
      <div
        class="item"
        :class="{ active: active == item.path || item.children.includes(route.path) }"
        v-for="item in menuList"
        :key="item.title"
        @click="goPath(item.path)"
      >
        {{ item.title }}
      </div>
    </div>
    <RouterView />
  </div>
</template>
<style lang="scss" scoped>
.menu {
  @include flex-center(row, flex-start, center);
  margin-bottom: 26px;
  .item {
    width: 311px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    margin-right: 12px;
    background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
    border-radius: 5px;
    border: 1px solid #c6dbf3;
    font-size: 14px;
    color: #333333;
    cursor: pointer;
    font-weight: 500;
    &.active {
      color: #40a0ff;
      // background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
      // border-radius: 5px 5px 5px 5px;
      // border: 1px solid #c6dbf3;
    }
  }
}
</style>
