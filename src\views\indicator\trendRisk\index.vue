<template>
  <div class="trendRisk">
    <div class="classify">
      <div class="title">指标类别</div>
      <div class="item active">组织指标</div>
    </div>
    <div class="main">
      <div class="tree">
        <el-input class="w-60 mb-[8px]" placeholder="按组织名称检索" />
        <Tree></Tree>
      </div>
      <div class="right">
        <div class="menu">
          <div
            class="menu-item"
            v-for="item in menuList"
            :class="{ active: item.id == activeStepId }"
            :key="item.id"
            @click="changeStep(item)"
          >
            {{ item.name }}
          </div>
        </div>
        <component :is="activeStep.comp" />
      </div>
    </div>
  </div>
</template>
<script setup>
defineOptions({ name: 'TrendRisk' })
import Tree from '@/components/tree/index.vue'

const menuList = ref([
  { id: 1, name: '指标趋势预测', comp: defineAsyncComponent(() => import('./IndicatorTrend.vue')) },
  { id: 2, name: '风险预警', comp: defineAsyncComponent(() => import('./riskEarlyWarning.vue')) },
  { id: 3, name: '风险视图', comp: defineAsyncComponent(() => import('./riskView.vue')) }
])
const router = useRouter()
const route = useRoute()
const activeStepId = ref(route.query.stepId || menuList.value[0].id)
const activeStep = computed(() => {
  return menuList.value.find(item => item.id == activeStepId.value)
})

router.push({ path: route.path, query: { ...route.query, stepId: activeStepId.value } })
const changeStep = item => {
  activeStepId.value = item.id
  router.push({ path: route.path, query: { ...route.query, stepId: item.id } })
}
const defaultProps = {
  children: 'children',
  label: 'label'
}
const data = ref([
  {
    id: 1,
    label: 'H公司',
    children: [
      {
        id: 4,
        label: '三级组织',
        children: [
          {
            id: 5,
            label: '采购部'
          },
          {
            id: 6,
            label: '供应链计划管理部'
          },
          {
            id: 7,
            label: '电子商务部'
          },
          {
            id: 8,
            label: '欧盟区产品部'
          },
          {
            id: 9,
            label: '工艺部'
          },
          {
            id: 10,
            label: '供应链计划管理部'
          },
          {
            id: 11,
            label: 'GTM部'
          },
          {
            id: 12,
            label: '结构研发部'
          },
          {
            id: 13,
            label: '经营与财务管理部'
          },
          {
            id: 14,
            label: '冷柜研发部'
          },
          {
            id: 15,
            label: '零售与用户运营部'
          },
          {
            id: 16,
            label: '品牌与产品营销部'
          }
        ]
      }
    ]
  }
])
const active = ref(6)
</script>
<style lang="scss" scoped>
.trendRisk {
  .classify {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      color: #333333;
      font-weight: 600;
      margin-right: 8px;
    }

    .item {
      width: 311px;
      height: 35px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #c6dbf3;
      font-size: 14px;
      color: #333333;
      text-align: center;
      line-height: 35px;
      margin-right: 9px;
      background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
      cursor: pointer;

      &.active {
        color: #40a0ff;
        box-shadow: 0px 0px 20px -4px rgba(64, 160, 255, 0.48);
      }
    }
  }
  .main {
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    .tree {
      width: 206px;
      border-radius: 8px;
      border: 1px solid #c6dbf3;
      padding: 8px;
      margin-right: 20px;
      flex-shrink: 0;
    }
    .right {
      // flex-grow: 1;
      width: calc(100% - 226px);
      .menu {
        display: flex;
        margin-bottom: 20px;
        .menu-item {
          width: 193px;
          height: 35px;
          background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
          border-radius: 5px 5px 5px 5px;
          border: 1px solid #c6dbf3;
          color: #333333;
          font-size: 14px;
          font-weight: 500;
          line-height: 35px;
          text-align: center;
          margin-right: 9px;
          cursor: pointer;
          &.active {
            color: #ffffff;
            background: linear-gradient(90deg, #40a0ff 0%, #8cc6fe 50%, #90c8fe 100%);
          }
        }
      }
    }
  }
}
</style>
