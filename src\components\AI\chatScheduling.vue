<script setup>
import { Download } from '@element-plus/icons-vue'
import api from '@/api/index.js'
import MarkdownIt from 'markdown-it'
import service, { cancelRequest } from '@/api/request.js'
import { useDialogueStore } from '@/stores'

const props = defineProps({ id: String })
const workflowRunId = ref(props.id)

const md = new MarkdownIt()
// 修改表格的 HTML 结构
md.renderer.rules.table_open = () => '<table style="border-collapse: collapse; width: 100%;">'
md.renderer.rules.table_close = () => '</table>'
// 修改表头单元格样式
md.renderer.rules.th_open = () =>
  '<th style="border: 1px solid #ddd; padding: 8px; background: #f5f5f5; text-align: center;">'
md.renderer.rules.td_open = () => '<td style="border: 1px solid #ddd; padding: 8px; text-align: center;">'
// 对话内容
const list = ref([])
const loading = ref(false)
const showDownload = ref(false)
let chatId = ref('')
const getConverse = async text => {
  useDialogueStore().setFirst('')
  // 最后一次问答记录用于后面结束未停止的接口
  useDialogueStore().setLast({ fileId: workflowRunId.value })
  loading.value = true
  list.value.push({
    type: 'ask',
    text: text
  })
  service({
    url: '/haier/workflowAps/workflowApsConverse',
    method: 'post',
    params: { fileId: workflowRunId.value },
    responseType: 'stream',
    onDownloadProgress: progressEvent => {
      loading.value = false
      const chunk = progressEvent.event.currentTarget.responseText
      const lines = chunk.split('\n')
      let contentThink = ''
      for (const line of lines) {
        if (line.startsWith('data:')) {
          const data = line.slice(5).trim()
          try {
            const jsonData = JSON.parse(data)
            chatId.value = jsonData.workflow_run_id
            const lastIndex = list.value.length - 1
            const lastItem = list.value[lastIndex]
            if (lastItem && lastItem.type == 'answer') {
              if (jsonData.data.text == 'mark' || jsonData.data.text == 'down') {
                jsonData.data.text = ''
              }
              const result = extractThinkContent(jsonData.data.text)
              // 判断是否是回答的第一次
              if (jsonData?.event == 'text_chunk') {
                contentThink += result.contentAfterThink
                // list.value[lastIndex].think = result.thinkContent
                list.value[lastIndex].text = md.render(contentThink)
              }
            } else {
              list.value.push({
                type: 'answer',
                think: '',
                text: '思考中...'
              })
            }
            chatContent.value.scrollTop = chatContent.value.scrollHeight - chatContent.value.offsetHeight
          } catch (error) {
            // console.error('Failed to parse JSON data:', error)
          }
        }
      }
    }
  })
    .finally(() => {
      showDownload.value = true
      loading.value = false
    })
    .catch(err => {
      if (list.value.length != 0) list.value[list.value.length - 1].think = '回答已终止'
    })
}

let load = ref(false)

// 历史会话内容
const getConversation = () => {
  list.value = []
  loading.value = true
  showDownload.value = true
  api.dialogue
    .scheduleConversion({ workflowRunId: workflowRunId.value })
    .then(res => {
      const data = res.data
      if (!data.outputs) {
        list.value.push({
          type: 'answer',
          text: '抱歉，我还在学习中......'
        })
        return
      }
      const answer = extractThinkContent(data.outputs)
      if (answer.contentAfterThink.includes('markdown')) {
        let arr = answer.contentAfterThink.split('```')
        list.value.push({
          type: 'answer',
          think: answer.thinkContent,
          text: md.render(arr[1].substring(8))
        })
      } else {
        list.value.push({
          type: 'answer',
          think: answer.thinkContent,
          text: md.render(answer.contentAfterThink)
        })
      }
    })
    .finally(() => {
      loading.value = false
      scrollToBottom()
    })
}
// 处理think数据
function extractThinkContent(str) {
  const startIndex = str.indexOf('<think>')
  if (startIndex == -1) {
    return {
      thinkContent: '',
      contentAfterThink: str
    }
  }
  const start = startIndex + '<think>'.length
  const endIndex = str.indexOf('</think>', start)
  if (endIndex == -1) {
    return {
      thinkContent: str.slice(start),
      contentAfterThink: ''
    }
  }
  // 截取 </think> 到字符串末尾的内容
  const contentAfterThink = str.slice(endIndex + '</think>'.length)
  return {
    thinkContent: str.slice(start, endIndex),
    contentAfterThink
  }
}
// 判断历史还是新对话
function isNewConversation() {
  // 结束请求
  cancelRequest(`post/haier/workflowAps/workflowApsConverse${useDialogueStore().lastData}`)
  if (useDialogueStore().firstInput) {
    getConverse(useDialogueStore().firstInput)
  } else {
    // 历史对话
    getConversation()
  }
}

const fullscreenLoading = ref(false)
function exportSchedule() {
  fullscreenLoading.value = true
  api.dialogue.exportSchedule({ workflowRunId: chatId.value || workflowRunId.value }).then(res => {
    fullscreenLoading.value = false
    exportFile('生产计划表.xlsx', res)
  })
}

function exportInventory() {
  fullscreenLoading.value = true
  api.dialogue.exportInventory({ workflowRunId: chatId.value || workflowRunId.value }).then(res => {
    fullscreenLoading.value = false
    exportFile('生产库存表.xlsx', res)
  })
}

function exportFile(name, res) {
  const blob = new Blob([res])
  const elink = document.createElement('a')
  elink.href = window.URL.createObjectURL(blob)
  elink.download = name
  elink.style.display = 'none'
  document.body.appendChild(elink)
  elink.click()
  document.body.removeChild(elink)
  window.URL.revokeObjectURL(elink.href) // 释放URL 对象
}

//#region 页面滚动
const chatContent = ref(null) //装会话的容器
const isScrolling = ref(false) //用于判断用户是否在滚动
function scrollToBottom() {
  nextTick(() => {
    //注意要使用nexttick以免获取不到dom
    if (!isScrolling.value) {
      chatContent.value.scrollTop = chatContent.value.scrollHeight - chatContent.value.offsetHeight
    }
  })
}
function handleScroll() {
  const scrollContainer = chatContent.value
  const scrollTop = scrollContainer.scrollTop
  const scrollHeight = scrollContainer.scrollHeight
  const offsetHeight = scrollContainer.offsetHeight
  if (scrollTop + offsetHeight < scrollHeight) {
    // 用户开始滚动并在最底部之上，取消保持在最底部的效果
    isScrolling.value = true
  } else {
    // 用户停止滚动并滚动到最底部，开启保持到最底部的效果
    isScrolling.value = false
  }
}
//#endregion
onMounted(() => {
  // isNewConversation()
  chatContent.value.addEventListener('scroll', handleScroll)
})
watch(
  () => props.id,
  (newId, oldId) => {
    workflowRunId.value = newId
    isNewConversation()
  }
)
</script>
<template>
  <div class="main">
    <div class="frame" ref="chatContent">
      <template v-for="(item, index) in list" :key="index">
        <div class="answer" v-if="item.type !== 'ask'">
          <div class="profile">
            <img src="@/assets/imgs/dialogue/logo.jpg" alt="" />
          </div>
          <div class="content">
            <div class="think">{{ item.think }}</div>
            <div class="text" v-if="item.text" v-html="item.text"></div>
            <div class="operation" v-if="showDownload">
              <div class="copy" title="导出生产计划表" @click="exportSchedule">
                <el-button
                  type="primary"
                  :loading="load"
                  class="button"
                  v-loading.fullscreen.lock="fullscreenLoading"
                  element-loading-text="首次导出时间较长，请耐心等待..."
                >
                  <el-icon><Download /></el-icon>导出生产计划表
                </el-button>
              </div>
              <div
                class="copy"
                title="导出生产库存表"
                @click="exportInventory"
                v-loading.fullscreen.lock="fullscreenLoading"
                element-loading-text="首次导出时间较长，请耐心等待..."
              >
                <el-button type="primary" :loading="load" class="button">
                  <el-icon><Download /></el-icon>导出生产库存表
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </template>
      <!-- 加载 -->
      <div class="answer" v-if="loading">
        <div class="profile">
          <img src="@/assets/imgs/dialogue/logo.jpg" alt="" />
        </div>
        <div class="content" v-loading="loading">
          <div class="text" style="width: 50px"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.main {
  height: 100%;
  display: flex;
  flex-direction: column;
  .frame {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 0 10px;
    flex-grow: 1;
    .ask {
      max-width: 95%;
      background: #cae5ff;
      border-radius: 8px 8px 8px 8px;
      color: #333333;
      line-height: 30px;
      font-size: 16px;
      padding: 10px 16px;
      align-self: flex-end;
      margin-bottom: 20px;
    }
    .answer {
      align-self: flex-start;
      display: flex;
      margin-bottom: 20px;
      .profile {
        width: 40px;
        height: 40px;
        background: #ffffff;
        flex-shrink: 0;
        margin-right: 10px;
        border-radius: 50%;
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: contain;
        }
      }
      .content {
        .text {
          background: #ffffff;
          box-shadow: 0px 0px 6px 0px #d8e9ff;
          border-radius: 8px 8px 8px 8px;
          color: #3d3d3d;
          font-size: 16px;
          line-height: 29px;
          padding: 20px 40px;
          text-align: justify;
        }
        .think {
          font-size: 14px;
          color: #8b8b8b;
          margin-bottom: 5px;
        }
        :deep(.el-loading-mask) {
          border-radius: 8px;
        }
        .operation {
          display: flex;
          margin-top: 20px;
          div {
            // width: 14px;
            // height: 14px;
            cursor: pointer;
            margin-right: 16px;
            background-size: 14px 14px !important;
          }
          .copy {
            .el-icon {
              font-size: 16px;
            }
            &:hover {
              color: $--color-primary;
            }
          }
          .refresh {
            background: url('../../assets/images/dialogue/refresh.webp') no-repeat 0 0;
            &:hover {
              background: url('../../assets/images/dialogue/refresh_ac.webp') no-repeat 0 0;
            }
          }
          .like {
            background: url('../../assets/images/dialogue/like.webp') no-repeat 0 0;
            &:hover {
              background: url('../../assets/images/dialogue/like_ac.webp') no-repeat 0 0;
            }
          }
          .rubbish {
            background: url('../../assets/images/dialogue/rubbish.webp') no-repeat 0 0;
            &:hover {
              background: url('../../assets/images/dialogue/rubbish_ac.webp') no-repeat 0 0;
            }
          }
        }
      }
    }
  }
}
</style>
