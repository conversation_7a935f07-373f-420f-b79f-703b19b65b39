<template>
  <div class="staff_import_wrap">
    <div class="page_main_title">
      <div class="goback_geader" @click="goback"><i class="el-icon-arrow-left"></i>返回</div>
      职位管理
    </div>
    <div class="page_second_title">职位导入信息--错误一览</div>
    <div class="page_section staff_import_center clearfix">
      <el-table :data="tableData" stripe ref="tableRef" v-if="flag">
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column v-for="col in columns" :prop="col.prop" :key="col.prop" :label="col.label" :width="col.width">
          <template #default="scope">
            <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row[col.prop].msg"
              placement="top"
              :disabled="scope.row[col.prop].accept"
            >
              <el-input
                size="small"
                :class="{ error: !scope.row[col.prop].accept }"
                v-model="scope.row[col.prop].val"
                :type="col.prop == 'userPasswd' ? 'password' : 'text'"
                :disabled="scope.row[col.prop].accept"
              ></el-input>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <coustomPagination @pageChange="pageChange" :total="tableDataCopy.length" />
      <div class="btn_wrap align_center">
        <el-button type="primary" @click="saveStaff">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { importJobData } from '../../request/api'
import coustomPagination from '@/components/talent/paginationComps/coustomPagination.vue'

const route = useRoute()
const router = useRouter()
const tableRef = ref(null)

const flag = ref(true)
const tableData = ref([])
const tableDataCopy = ref([])
const columns = [
  { label: '职位编码', prop: 'jobCodeExtn' },
  { label: '职位名称', prop: 'jobName' },
  { label: '族群', prop: 'parentJobClassName' },
  { label: '序列', prop: 'jobClassName' },
  { label: '行政级别/职层', prop: 'jobLevelName' },
  { label: '职等', prop: 'jobGradeName' }
]

function goback() {
  router.back()
}

function getPageData(pageSize, currentPage) {
  const offset = (currentPage - 1) * pageSize
  const copy = tableDataCopy.value
  tableData.value = copy.slice(offset, offset + pageSize)
}

function pageChange(pageSize, currentPage) {
  getPageData(pageSize, currentPage)
}

function saveStaff() {
  const jobInfoExcelRequests = tableDataCopy.value.map(item => {
    const obj = {}
    columns.forEach(col => {
      obj[col.prop] = item[col.prop].val
    })
    return obj
  })
  importJobData(jobInfoExcelRequests).then(res => {
    if (res.code == '200') {
      tableData.value = []
      tableDataCopy.value = []
      tableData.value = res.data.obj
      tableDataCopy.value = res.data.obj
      flag.value = false
      if (res.data.total == 0) {
        goback()
        return
      }
      getPageData(10, 1)
      nextTick(() => {
        flag.value = true
      })
    } else {
      ElMessage.error(res.msg)
    }
  })
}

onMounted(() => {
  // 这里假设 data 是数组
  let params = route.query.data
  tableData.value = params
  tableDataCopy.value = params
  getPageData(10, 1)
})
</script>

<style scoped lang="scss">
.el-input.error .el-input__inner {
  border-color: red;
}
.el-input--mini .el-input__inner {
  margin: 5px 0;
}
</style>
