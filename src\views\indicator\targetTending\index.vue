<script setup>
import SectionTab from '../components/sectionTab.vue'
import orgIndicator from './orgIndicator.vue'
import personIndicator from './personIndicator.vue'
import projectIndicator from './projectIndicator.vue'
defineOptions({ name: 'targetTendingIndex' })
const router = useRouter()
const route = useRoute()
const tabContentList = ref([orgIndicator, personIndicator, projectIndicator])
const sectionTabCheckSign = ref(1)
const sectionTabList = ref([
  {
    name: '组织指标',
    code: 1
  },
  {
    name: '个人指标',
    code: 2
  },
  {
    name: '项目指标',
    code: 3
  }
])
const checkSecTab = c => {
  sectionTabCheckSign.value = c
}
</script>
<template>
  <div class="target_tending_wrap">
    <div class="s_tab_wrap justify-start">
      <div class="title_s">指标类别</div>
      <SectionTab
        :sectionTabList="sectionTabList"
        :sectionTabCheckSign="sectionTabCheckSign"
        @checkSecTab="checkSecTab"
      ></SectionTab>
    </div>
    <div class="content-mian">
      <component :is="tabContentList[sectionTabCheckSign - 1]" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.target_tending_wrap {
  .s_tab_wrap {
    .title_s {
      margin-right: 20px;
      line-height: 35px;
      color: #333;
      font-weight: 600;
    }
  }
}
</style>
