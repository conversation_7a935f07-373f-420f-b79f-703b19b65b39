<template>
    <div class="model_affirm_wrap bg_write">
        <div class="page_main_title clearfix">
            模型确认
        </div>
        <div class="page_section">
            <div class="model_list_wrap">
                <table-component :tableData="tableData" :needIndex="needIndex" @handleSizeChange="handleSizeChange"
                                 @handleCurrentChange="handleCurrentChange">
                    <template v-slot:oper>
                        <el-table-column label="操作" width="200" align="center">
                            <template slot-scope="scope">
                                <div class="align_center">
                                    <el-button
                                            v-if="scope.row.buildStatus != 4"
                                            @click.native.prevent="affirm(scope.$index, tableData.data)"
                                            type="primary"
                                            class="page_add_btn"
                                    >确认
                                    </el-button>
                                    <el-button
                                            v-if="scope.row.buildStatus == 4"
                                            @click.native.prevent="view(scope.$index, tableData.data)"
                                            type="primary"
                                            class="page_add_btn"
                                    >查看
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                </table-component>
            </div>
            <!-- <div class="pagination_wrap">
                <el-pagination
                    :page-sizes="[20, 50, 100, 200]"
                    :page-size="100"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="400"
                ></el-pagination>
            </div> -->
        </div>
    </div>
</template>

<script>
    import {getConfirmModelPageList} from "../../request/api"
    import tableComponent from "@/components/talent/tableComps/tableComponent";

    export default {
        name: "modelAffirm",
        components: {
            tableComponent
        },
        data() {
            return {
                needIndex: true,
                tableData: {
                    columns: [
                        {
                            label: "模型名称",
                            prop: "modelName"
                        },
                        {
                            label: "能力分类",
                            prop: "moduleCount",
                            formatterFun: (row, column, val) => {
                                return val + "类"
                            }
                        },
                        {
                            label: "能力词典",
                            prop: "itemCount",
                            formatterFun: (row, column, val) => {
                                return val + "类"
                            }
                        },
                        {
                            label: "匹配岗位",
                            prop: "objectCount",
                            formatterFun: (row, column, val) => {
                                return val + "个"
                            }
                        },
                        {
                            label: "最新更新时间",
                            prop: "rmodifyTime",
                            formatterFun: (row, column, val) => {
                                return val ? val.split(" ")[0] : "";
                            }
                        },
                        {
                            label: "状态",
                            prop: "buildStatus",
                            formatterFun: (row, column, val) => {
                                let status = "";
                                switch (val) {
                                    case "1":
                                        status = "未建模";
                                        break;
                                    case "2":
                                        status = "建模中";
                                        break;
                                    case "3":
                                        status = "待确认";
                                        break;
                                    case "4":
                                        status = "已确认";
                                        break;
                                    default:
                                        break;
                                }
                                return status;
                            }
                        }
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10
                    }
                },
            };
        },
        created() {
            this.getConfirmModelPageListFun();
        },
        methods: {
            affirm(index, data) {
                let row = data[index]
                this.$router.push({
                    path: '/talentAssessment/modelValidation',
                    query: {modelId: row.modelId,buildId:row.lastBuildId}
                })
            },
            view(index, data) {
                let row = data[index]
                this.$router.push({
                    path: '/talentAssessment/modelValidation',
                    query: {modelId: row.modelId,buildId:row.lastBuildId,type:"show"}
                })
            },
            //pageSize 改变时会触发
            handleSizeChange(size) {
                console.log(size)
                this.tableData.page.current = 1;
                this.tableData.page.size = size;
                this.getConfirmModelPageListFun();
            },

            //currpage 改变时会触发
            handleCurrentChange(page) {
                this.tableData.page.current = page;
                this.getConfirmModelPageListFun();
            },
            getConfirmModelPageListFun() {
                getConfirmModelPageList({
                    current: this.tableData.page.current,
                    size: this.tableData.page.size,
                }).then(res => {
                    console.log(res)
                    if (res.code == 200) {
                        this.tableData.data = res.data;
                        this.tableData.page = res.page;
                    }
                })
            },

        }
    };
</script>

<style scoped lang="scss">
    .model_affirm_wrap {

    }

</style>