<script setup>
import talentReviewEM from './talentReviewEM.vue'
import talentEM from './talentEM.vue'
const router = useRouter()
const route = useRoute()
const tabContentList = ref([talentReviewEM, talentEM])

const topNavList = ref([
  {
    name: '人才盘点模型',
    code: 1
  },
  {
    name: '人才能力测评模型',
    code: 2
  }
])
const topNavCheckSign = ref(1)

const checkTopNav = c => {
  topNavCheckSign.value = c
}
</script>
<template>
  <div class="evalModel_wrap">
    <div class="top_nav_wrap justify-between">
      <div
        class="item_wrap"
        :class="{ top_act: topNavCheckSign == item.code }"
        @click="checkTopNav(item.code)"
        v-for="item in topNavList"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="content-mian">
      <component :is="tabContentList[topNavCheckSign - 1]" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.justify-end {
  display: flex;
  justify-content: flex-end;
}
.evalModel_wrap {
  .top_nav_wrap {
    margin: 0 auto 16px;
    width: 330px;
    height: 42px;
    line-height: 42px;
    background: #ffffff;
    border-radius: 23px 23px 23px 23px;
    .item_wrap {
      padding: 0 25px;
      cursor: pointer;
    }
    .top_act {
      background: #40a0ff;
      color: #fff;
      border-radius: 23px 23px 23px 23px;
    }
  }
}
</style>
