<template>
    <div class="report_section ">
        <div class="from_wrap clearfix">
            <div class="basic_info marginT_30">
                <div class="page_second_title">承接职责</div>
                <div class="table_wrap">
                    <template>
                        <el-table :data="tableData">
                            <el-table-column prop="respName" label="职责模块" width="180"></el-table-column>
                            <el-table-column prop="respDesc" label="部门职责描述" width="180"></el-table-column>
                            <el-table-column prop="checkFlag" label="">
                                <template slot-scope="scope">
                                    <i class="el-icon-check check_icon" v-if="scope.row.rstatus == 'Y'"></i>
                                </template>
                            </el-table-column>
                        </el-table>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { getUserReportRespList } from "../../../../request/api";
    export default {
        name: "userRBasicInfo",
        props: ["nextBtnText", "enqId", "userId", "postCode"],
        created() {
            this.getUserReportRespListFun();
        },
        methods: {
            getUserReportRespListFun() {
                getUserReportRespList({
                    enqId: this.enqId,
                    userId: this.userId,
                    postCode: this.postCode,
                }).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$msg.success("获取信息成功");
                        let data = res.data;
                        this.tableData = data;
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
        },
        data() {
            return {
                tableData: [
                    {
                        respName: "职责模块",
                        respDesc: "部门职责描述",
                        rstatus: true,
                    },
                ],
            };
        },
    };
</script>

<style scoped lang="scss">
.check_icon{
    font-weight: bold;
    color: #0099fd;
    font-size:18px;
    text-align: center;
}
</style>
