<template>
  <div class="report_wrap" id="downloadPdf">
    <div class="logo_wsyjy">
      <img class="img" id="logo" src="" alt="" srcset="" />
    </div>
    <reportCover id="pdf_cover" :beginDate="beginDate" :userInfo="userInfo" :type="'P'" />
    <!-- <div class="page_main_title">
            {{ enqName }}--盘点报告(个人报告) {{ beginDate | removeTime }}
        </div> -->
    <component
      v-for="code in moduleCodeArr"
      class="pdf_wrap"
      :key="code"
      :is="moduleObj[code]"
      :userId="userId"
      :enqId="enqId"
      :postCode="postCode"
      :isPdf="true"
      :id="'pdf_module_' + code"
    ></component>
    <!-- <div class="page_section">
            <div class="page_section_center clearfix">
                <div class="report_header_info">
                    <span class="name">{{ userInfo.userName }}</span>
                    <span class>{{ userInfo.orgName }}</span>
                    <span class="marginR_16 marginL_16">|</span>
                    <span class>{{ userInfo.postName }}</span>
                </div>
                <component
                    v-for="code in moduleCodeArr"
                    :key="code"
                    :is="moduleObj[code]"
                    :userId="userId"
                    :enqId="enqId"
                    :postCode="postCode"
                    :isPdf="true"
                ></component>
            </div>
        </div> -->
  </div>
</template>

<script>
import { getUserReportUserInfo, getEnqInfo } from '../../../request/api'
import stepBar from '@/components/talent/stepsComps/stepBar'
import reportCover from './reportCover'

import userRBasicInfo from './userReportComps/userRBasicInfo'
import userRDuty from './userReportComps/userRDuty'
import userRActivities from './userReportComps/userRActivities'
import userREduInfo from './userReportComps/userREduInfo'
import userRWorkExperience from './userReportComps/userRWorkExperience'
import userRKpi from './userReportComps/userRKpi'
import userRTalentEval from './userReportComps/userRTalentEval'
import userRTrainInfo from './userReportComps/userRTrainInfo'
import userRAwardInfo from './userReportComps/userRAwardInfo'
import userRPersonalPlan from './userReportComps/userRPersonalPlan'
import userRSelfPlan from './userReportComps/userRSelfPlan' //个人规划
import userRMatche from './userReportComps/userRMatche.vue' //任职匹配
import userRQualityEval from './userReportComps/userRQualityEval.vue' //素质评价
import userRPerformanceEval from './userReportComps/userRPerformanceEval.vue' //业绩评价
import userRTargetResult from './userReportComps/userRTargetResult.vue' //目标结果
import userRKPICur from './userReportComps/userRKPICur.vue' //kpi评价
import userRRisk from './userReportComps/userRRisk' // 离职风险
import userREngagement from './userReportComps/userREngagement' //敬业度
import userRWorkDrive from './userReportComps/userRWorkDrive' //工作驱动
import userRSynergy from './userReportComps/userRSynergy' //协同网络
import userRTalent from './userReportComps/userRTalent' //人才分类

const moduleObj = {
  PN01: userRBasicInfo,
  // P01: userRQualityEval,
  // PN02: userRActivities,
  PN02: userREduInfo,
  PN03: userRWorkExperience,
  // PN04: userRKpi,
  PN04: userRTrainInfo,
  PN05: userRAwardInfo,
  PN06: userRSelfPlan,
  PN07: userRMatche,
  PN08: userRQualityEval,
  // PN10: userRDuty,
  PN09: userRPerformanceEval,
  PN10: userRTargetResult,
  PN11: userRKPICur,
  PN12: userRRisk,
  PN13: userREngagement,
  PN14: userRWorkDrive,
  PN15: userRSynergy,
  PN16: userRTalent
}
export default {
  name: 'userReportPdf',
  components: { stepBar, reportCover },
  props: {
    moduleCodeArr: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  data() {
    return {
      enqId: this.$route.query.enqId,
      postCode: this.$route.query.postCode,
      userInfo: {
        userName: '',
        orgName: '',
        postName: ''
      },
      enqName: '',
      beginDate: '',
      queryUserId: null,
      moduleObj: moduleObj,
      moduleArr: [] //按顺序储存盘点模块的code
    }
  },
  created() {
    let query = this.$route.query
    this.queryUserId = query.userId
    this.getUserReportUserInfoFun()
    this.getEnqInfoFn()
  },
  computed: {
    userId() {
      if (this.queryUserId) {
        console.log('带有参数userId')
        return this.queryUserId
      } else {
        console.log('没有有参数userId，取vuex')
        return this.$store.state.userInfo.userId
      }
    }
  },
  methods: {
    getUserReportUserInfoFun() {
      let params = {
        enqId: this.enqId,
        userId: this.userId,
        postCode: this.postCode
      }
      getUserReportUserInfo(params).then(res => {
        if (res.code == '200') {
          this.userInfo = res.data
        }
      })
    },
    getEnqInfoFn() {
      getEnqInfo({ id: this.enqId }).then(res => {
        console.log(res)
        this.enqName = res.data.enqName
        this.beginDate = res.data.beginDate
      })
    }
  }
}
</script>

<style scoped lang="scss">
.report_wrap {
  .report_section {
    padding: 40px;
    .page_second_title {
      margin-left: -40px;
    }
  }
}
.report_header_info {
  color: #212121;
  font-size: 14px;
  margin-bottom: 16px;
  .name {
    color: #0099ff;
    font-size: 16px;
    margin-right: 14px;
    font-weight: bold;
  }
}
.page_section_center {
  padding-bottom: 30px;
}
// 去除select下拉箭头
.el-input__suffix {
  display: none;
}
.userR_quality_eval_wrap,
.userR_performance_eval_wrap,
.userR_target_result_wrap,
.userR_KPI_cur_wrap,
.userR_risk_wrap,
.userR_engagement_wrap,
.userR_talent_wrap,
.userR_matche_wrap,
.userR_performance_eval_wrap,
.userR_quality_eval_wrap,
.userR_self_plan_wrap,
.userR_synergy_wrap,
.userR_target_result_wrap,
.userR_work_drive_wrap {
  height: auto;
}

.pdf_wrap {
  .page_second_title {
    font-size: 30px !important;
    line-height: 50px;
    &::before {
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .page_third_title {
    font-size: 26px !important;
    line-height: 34px;
  }
  .basic_info {
    font-size: 26px;
    .item {
      line-height: 46px;
    }
  }
  .edu_info_header {
    font-size: 26px;
  }
  .develop_expect {
    font-size: 24px;
  }
  .last_annulus_item,
  .annulus_item {
    font-size: 24px;
  }
  .ranking_wrap {
    width: 280px;
    &.kpi {
      width: 100%;
      .item_wrap {
        height: auto !important;
      }
    }
    .ranking_main .item_wrap {
      line-height: 40px;
      font-size: 22px;
      .number {
        font-size: 18px;
        .weight {
          font-size: 20px;
        }
      }
    }
  }
  .edu_info_header .item.short,
  .edu_info_mmain .item.short {
    width: 8%;
  }
  .edu_info_item .item {
    font-size: 24px;
    line-height: 30px;
    margin-bottom: 20px;
  }
  .parag {
    font-size: 24px;
    line-height: 30px;
  }
  .info_wrap {
    font-size: 24px;
    line-height: 30px;
  }
  .chart_box {
    // min-height: 300px;
  }
  .basic_info_wrap .basic_info_item {
    .title {
      font-size: 26px;
    }
    .text {
      font-size: 26px;
    }
  }
  .top_center_main {
    font-size: 22px;
  }
  .item_title {
    font-size: 26px !important;
    line-height: 46px !important;
  }
  .el-table {
    font-size: 26px;
    .cell {
      line-height: 38px;
    }
  }
  .el-table__body tr {
    td {
      font-size: 24px;
      padding: 12px 0;
      line-height: 28px;
      letter-spacing: 2px;
    }
  }
  .distribution_box {
    font-size: 24px;
    line-height: 1.3;
    .box_title {
      line-height: 1.5;
    }
  }
  .manage_advice_wrap {
    font-size: 24px;
    .manage_advice_item .item_info {
      line-height: 30px;
    }
  }
  .explain_text {
    font-size: 24px;
    line-height: 32px;
  }
  .incentive_wrap {
    font-size: 24px;
    .incentive_item .desc_wrap {
      line-height: 28px;
    }
  }
  .incentive_wrap .incentive_item .desc_wrap {
    line-height: 30px !important;
  }
  .staff_authorized_strength {
    font-size: 24px;
  }
  .talent_matrix_chart {
    font-size: 16px;
  }
  .matrix_chart {
    font-size: 16px;
  }
  &.userR_talent_wrap .userR_talent_main .desc_info_wrap {
    .title {
      font-size: 26px;
      line-height: 34px;
    }
    .desc {
      font-size: 22px;
      line-height: 30px;
    }
  }
}
</style>
