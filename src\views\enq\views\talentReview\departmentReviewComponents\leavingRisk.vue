<template>
  <div class="post_requirement_main">
    <div class="department_main clear marginT_8">
      <div class="page_second_title">离职风险</div>
      <div class="btn_wrap align_right">
        <el-button class="page_add_btn" type="primary" @click="exportDownloadFun()">下载详细数据</el-button>
      </div>
      <div class="department_content marginT_8">
        <el-table :data="tableData">
          <el-table-column type="index" label="序号" align="center" fixed="left"></el-table-column>
          <el-table-column prop="userName" label="姓名" align="center" fixed="left"></el-table-column>
          <el-table-column prop="systemScore" label="系统评分" align="center"></el-table-column>
          <el-table-column prop="retentionRiskpossibility" label="系统判定离职可能性" align="center"></el-table-column>
          <el-table-column prop="dimissionPossibility" label="离职可能性" min-width="130" align="center">
            <template v-slot="scope">
              <el-select clearable v-model="scope.row.dimissionPossibility" placeholder="请选择">
                <el-option
                  v-for="item in option1"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="dimissionEffects" label="离职对业务的影响" min-width="130" align="center">
            <template v-slot="scope">
              <el-select clearable v-model="scope.row.dimissionEffects" placeholder="请选择">
                <el-option
                  v-for="item in option2"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="staffSubstitution" label="人员是否可替代" min-width="130" align="center">
            <template v-slot="scope">
              <el-select clearable v-model="scope.row.staffSubstitution" placeholder="请选择">
                <el-option
                  v-for="item in option3"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="marginT_30 align_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn" v-show="currentIndex != currentFirstCode"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { retentionRiskList, retentionRiskConfirm, exportQualityEvalList } from '../../../request/api'
import { useUserStore } from '@/stores/modules/user'
import { objHasEmpty } from '@/utils/utils'

const userStore = useUserStore()
const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  orgCode: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const option1 = ref([]) // 离职可能性
const option2 = ref([]) // 随业务的影响
const option3 = ref([]) // 可替代
const tableData = ref([])

const { proxy } = getCurrentInstance()

function getPostInfoFun() {
  let params = {
    enqId: props.enqId
  }
  retentionRiskList(params).then(res => {
    if (res.code == 200) {
      tableData.value = res.data
    }
  })
}

function exportDownloadFun() {
  let params = {
    enqId: props.enqId,
    type: 'feel'
  }
  exportQualityEvalList(params).then(res => {
    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '部门业离职风险价列表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  })
}

function checkData(data) {
  let arr = data
  let len = arr.length
  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    if (objHasEmpty(obj, ['staffSubstitution', 'dimissionEffects', 'dimissionPossibility'])) {
      return true
    }
  }
}

function updateEnqPostDemandFun(stepType) {
  if (checkData(tableData.value)) {
    ElMessage({
      message: '请完善数据后提交！',
      type: 'warning'
    })
    return
  }
  let arr = []
  tableData.value.forEach(item => {
    arr.push({
      impactResignationBusiness: item.staffSubstitution,
      personnelBeReplaced: item.dimissionEffects,
      possibilityOfResignation: item.dimissionPossibility,
      userId: item.userId
    })
  })
  let params = {
    enqId: props.enqId,
    list: arr
  }
  retentionRiskConfirm(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function prevBtn() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      updateEnqPostDemandFun('prevStep')
    })
    .catch(action => {
      ElMessage.info({
        message: action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步'
      })
      if (action == 'cancel') emit('prevStep')
    })
}

function nextBtn() {
  updateEnqPostDemandFun('nextStep')
}

onMounted(() => {
  getPostInfoFun()
  userStore
    .getDocList(['POSSIBILITY_OF_RESIGNATION', 'IMPACT_RESIGNATION_BUSINESS', 'PERSONNEL_BE_REPLACED'])
    .then(res => {
      option1.value = res.POSSIBILITY_OF_RESIGNATION
      option2.value = res.IMPACT_RESIGNATION_BUSINESS
      option3.value = res.PERSONNEL_BE_REPLACED
    })
})
</script>

<style scoped lang="scss">
.department_main {
  .right_title {
    width: 182px;
  }
}

.el-table .cell {
  padding: 0 5px;
}

.el-input__inner {
  padding: 0 5px;
}
.el-table__row {
  height: 45px;
}
</style>
