<template>
    <div class="chart_main">
        <div class="_chart" :id="id" :style="styleObj"></div>
    </div>
</template>
 
<script>
export default {
    name: "YbarChartComponent",
    /**
     isPercent : 是否是百分比数字，默认false ， true则在数字后面拼接 “ % ”
     seriesName : 数据表名称，主要用于tooltip显示类似title;
    **/
    props: ["chartData", "width", "height", "isPercent", "seriesName"],
    components: {},
    data() {
        return {
            id: null,
            styleObj: {
                width: this.width + "px",
                height: this.height + "px"
            }
        };
    },
    watch: {
        chartData: function() {
            this.init(this.chartData);
        }
    },
    mounted() {
        if (this.chartData.length == 0) {
            return;
        }
        this.init(this.chartData);
    },
    methods: {
        init(chartData) {
            let id = this.$util.createRandomId();
            this.id = id;
            this.$nextTick(() => {
                this.toDraw(id, chartData);
            });
        },
        toDraw(id, chartData) {
            let _this = this;
            let myChart = this.$EC.init(document.getElementById(id));
            if (chartData.length == 0) {
                myChart.clear();
                return;
            }
            let percentSymbol = _this.isPercent ? "%" : "";
            let seriesName = _this.seriesName ? _this.seriesName : "";
            let options = {
                grid: {
                    left: 10,
                    top: 20,
                    bottom: 10,
                    containLabel: true
                },
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        // 坐标轴指示器
                        type: "shadow" // 触发区域标识，默认直线，可选 'line' | 'shadow'
                    },
                    formatter(params) {
                        let val = params[0];
                        return `${val.seriesName}</br>${val.name} : ${val.value}${percentSymbol}`;
                    }
                },
                yAxis: [
                    {
                        show: false
                    }
                ],
                xAxis: [
                    {
                        type: "category",
                        axisLabel: {
                            interval: 0
                        },
                        data: chartData.map(function(item) {
                            return item.name;
                        })
                    }
                ],
                series: [
                    {
                        name: seriesName,
                        type: "bar",
                        label: {
                            show: true,
                            position: "insideTop",
                            distance: -15,
                            fontSize: 16,
                            color: "#333",
                            formatter: function(params) {
                                return _this.isPercent
                                    ? params.value + "%"
                                    : params.value;
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: "#92D050"
                            }
                        },
                        // barWidth: 15,
                        barMaxWidth: 25,
                        data: chartData.map(function(item) {
                            return item.value;
                        })
                    }
                ]
            };

            myChart.setOption(options);
        }
    }
};
</script>
 
<style scoped lang="scss">
.chart_main {
    width: 100%;
    height: 100%;
    ._chart {
        width: 100%;
        height: 100%;
    }
}
</style>