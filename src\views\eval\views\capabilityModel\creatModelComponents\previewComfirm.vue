<template>
    <div class="preview_comfirm_wrap">
        <div class="preview_comfirm_main ">
            <div class="flex_row_betweens">
                <div class="main_left page_section">
                    <div class="page_second_title">能力模块与能力词典</div>
                    <div class="chart_wrap" id="chartWrap"></div>
                </div>
                <div class="main_right page_section">
                    <div class="page_second_title">能力描述与关键行为</div>
                    <div class="control_main">
<!--                        <tabsDiffentPane :tabsData="tabsData" :isDefaultTheme=true></tabsDiffentPane>-->
                        <el-tabs>
                            <el-tab-pane v-for="item in tabsData" :label="item.moduleName">
                                <div class="company_knowledge_wrap">
                                    <div class="company_knowledge_mian">
                                        <div class="descript_item_wrap">
                                            <p class="title">能力描述</p>
                                            <ul class="info">
                                                <li>{{item.moduleDesc}}</li>
                                            </ul>
                                        </div>
                                        <div class="descript_item_wrap">
                                            <p class="title">行为描述</p>
                                            <ul class="info">
                                                <li>{{item.itemList[0].itemDesc}}</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </el-tab-pane>
                        </el-tabs>
                    </div>
                </div>
            </div>
            <div class="main_bottom page_section">
                <div class="page_second_title">分级标准</div>
                <div class="flex_row_start">
                    <ul class="main_bottom_left flex_row_start">
                        <el-tooltip class="item" effect="dark" :content="item.gradeShortName" placement="top" v-for="(item,index) in standardList">
                            <li :class="{'active' : currStandardIndex == index}"
                                @click="searchStandard(item.code,index)">
                                {{item.gradeShortName}}
                            </li>
                        </el-tooltip>
                    </ul>
                    <div class="main_bottom_right">{{standardInfo}}</div>
                </div>
            </div>
        </div>
        <div class="align_center marginT_16" v-if="buildStatus !== '4'">
            <el-button class="page_confirm_btn" type="primary" @click="prev()">上一步</el-button>
            <el-button class="page_confirm_btn" type="primary" @click="confirm()">确认</el-button>
        </div>
    </div>
</template>

<script>
    import {modelInfoPreview,updateModelStatus} from "../../../request/api"
    // import {echartsRenderPage} from "../../../../../../public/js/echartsimg/echartsToImg"
    import tabsDiffentPane from "@/components/talent/tabsComps/tabsDiffentPane";

    export default {
        name: "previewComfirm",
        props:['modelId','buildStatus'],
        components: {
            tabsDiffentPane
        },
        data() {
            return {
                currStandardIndex:0,
                pageData:{},
                chartData: {
                    data: []
                },
                tabsData: [],
                curStandardCode: 1,
                standardList: [],
                standardInfo: '',
            };
        },
        created() {
        },
        mounted() {
            if(this.modelId){
                this.modelInfoPreviewFun().then((res)=>{
                    this.pageData = res;
                    console.log(res)
                    this.chartData.data = res.module.map(item=>{
                        return {
                            name:item.moduleName,
                            value:item.count
                        }
                    })
                    this.chartData.addEvent=true;
                    this.chartData.defaultChecked=true;
                    this.tabsData = res.module[0].childrenList;
                    echartsRenderPage("chartWrap", "YBar", 290, 290, this.chartData,this.chartCallBack);
                    this.standardList = res.grade;
                    this.standardInfo = this.standardList.length > 0 ? this.standardList[0].gradeDesc : "";

                });
            }
            // this.searchStandard(this.curStandardCode)
        },
        methods: {
            searchStandard(code,index) {
                this.currStandardIndex = index;
                this.standardInfo = this.standardList[index].gradeDesc;
            },
            chartCallBack(params){
              // console.log(params)
                let chartIndex= params.dataIndex;
                this.tabsData = this.pageData.module[chartIndex].childrenList;
            },
            prev(){
                this.$emit("prevStep")
            },
            confirm(){
                this.updateModelStatusFun()
            },
            updateModelStatusFun(){
                updateModelStatus({
                    modelId:this.modelId
                }).then(res=>{
                    if(res.code ==200){
                        this.$msg.success(res.msg);
                        this.$router.go(-1)
                    }
                })
            },
            async modelInfoPreviewFun(){
             return await modelInfoPreview({
                    modelId:this.modelId
                }).then(res=>{
                    return res;
                })
            }
        }
    };
</script>

<style scoped lang="scss">
    .preview_comfirm_wrap {
        .preview_comfirm_main {
            margin: 20px 0;

            .preview_comfirm_title {
                padding: 3px 8px;
                font-size: 16px;
                line-height: 28px;
                color: #0099fd;
                font-weight: bold;
                background-color: #f2f8ff;
                border-radius: 3px;
            }

            .main_left {
                width: 40%;

                .preview_comfirm_title {
                    span {
                        margin: 0 0 0 10px;

                        &.creat {
                            font-weight: 700;
                            cursor: pointer;
                        }

                        &.creat:hover {
                            color: #4ab6fd;
                        }
                    }
                }
            }

            .main_right {
                width: 60%;

                .control_main {
                    width: 100%;
                    height: 300px;
                    overflow-y: auto;
                    .company_knowledge_wrap{
                        .company_knowledge_mian{
                            .descript_item_wrap{
                                margin-top: 10px;
                                .title{
                                    line-height: 32px;
                                    /*font-weight: bold;*/
                                    background: #f4f4f4;
                                    padding-left: 10px;
                                }
                                .info{
                                    li{
                                        line-height: 28px;
                                        padding-left: 10px;
                                    }
                                }

                            }
                        }
                    }
                }
            }

            .main_bottom {
                .flex_row_start {
                    .main_bottom_left {
                        margin: 10px 0 0 0;
                        min-width: 20%;
                        max-width: 40%;
                        border-right: 1px solid #e5e5e5;

                        li {
                            margin: 0 18px 0 0;
                            width: 50px;
                            height: 50px;
                            line-height: 50px;
                            overflow: hidden;
                            text-align: center;
                            border-radius: 50%;
                            background: #EBF4FF;
                            cursor: pointer;
                            font-weight: 700;
                            color: #0099fd;
                        }

                        li:hover {
                            color: #fff;
                            background: #0099FF;
                        }

                        .active {
                            color: #fff;
                            background: #0099FF;
                        }
                    }

                    .main_bottom_right {
                        margin: 10px 0 0 0;
                        padding: 0 0 0 10px;
                        width: 60%;
                        line-height: 24px;
                        height: 72px;
                        overflow-y: auto;
                    }
                }

            }
        }
    }

    .page_container .page_main .page_section {
        padding: 10px;
    }
</style>
