<script setup>
import { ElMessage } from 'element-plus'
import { login } from '@/api/modules/user'
import { useUserStore } from '@/stores'
const userStore = useUserStore()

const loginRef = ref('')
const form = reactive({
  username: '',
  password: '',
  checked: []
})

const rules = reactive({
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' }
    // {
    //   pattern: /^1[3-9]\d{9}$/,
    //   required: true,
    //   message: '请填写正确的手机号码',
    //   trigger: 'blur'
    // }
  ],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  checked: [{ required: true, message: '请同意用户协议、隐私政策' }]
})
const router = useRouter()
const onSubmit = formEl => {
  if (!formEl) return
  formEl.validate(valid => {
    if (valid) {
      console.log(form, '-------------->')
      // if (form.username == 'admin' && form.password == 'xiyi123') {
      //   router.push('/dialogue')
      // } else {
      //   ElMessage.error('账号或密码错误')
      // }
      login(form).then(res => {
        if (res.code == 200) {
          userStore.setToken(res.data)
          userStore.getUserInfo()
          router.push('/dialogue')
        }
      })
    } else {
      console.log('error submit!')
    }
  })
}
</script>
<template>
  <div class="app-login">
    <div class="app-login-card">
      <!-- <img src="@/assets/imgs/logo.webp" alt="" class="logo-img" /> -->
      <div class="login-title">智能体</div>
      <div class="login-form-card">
        <el-form :model="form" ref="loginRef" :rules="rules" style="width: 100%" class="login-form">
          <el-form-item prop="username">
            <el-input v-model="form.username" placeholder="请输入账号" size="large">
              <template #prefix>
                <SvgIcon name="login-phone" class-name="input-pre"></SvgIcon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="form.password" type="password" placeholder="请输入密码" size="large">
              <template #prefix>
                <SvgIcon name="login-password" class-name="input-pre"></SvgIcon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="checked">
            <el-checkbox-group v-model="form.checked">
              <el-checkbox value="checked" name="checked"> 我已阅读并同意用户协议、隐私政策 </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit(loginRef)" class="login-button">登录</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.app-login {
  @include flex-center(row, center, center);
  width: 100vw;
  height: 100vh;
  background: url('../../assets/imgs/login-bgc.webp') no-repeat center;
  .app-login-card {
    padding: 40px 30px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: inset 0px 10px 10px 0px #ffffff;
    border-radius: 20px;
    .logo-img {
      margin-bottom: 30px;
    }
    .login-title {
      padding-bottom: 24px;
      text-align: center;
      font-size: 20px;
      font-weight: 600;
      color: #333333;
    }
    .login-form-card {
      width: 360px;
      padding: 0 45px;
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 20px;
        }
        .el-input {
          --el-input-inner-height: 50px;
          --el-border-radius-base: 10px;
          --el-border-color: #dddddd;
          --el-text-color-placeholder: #888888;
        }
        .el-checkbox {
          --el-checkbox-border-radius: 2px;
          --el-fill-color-blank: #dfebf8;
          --el-border: 1px solid #666666;
        }
      }
      .input-pre {
        font-size: 17px;
      }
      .login-button {
        width: 100%;
        height: 50px;
      }
    }
  }
}
</style>
