<template>
  <div class="evaluation_items_wrap">
    <div class="title_wrap flex_row_betweens">
      <div class="page_second_title">评价项目</div>
      <button v-if="!editSign" @click="addDict" type="button" class="el-button page_add_btn el-button--primary">
        <span>新增</span>
      </button>
    </div>
    <div v-if="!editSign">
      <div class="training_activities_item flex_row_between" v-for="item in listData" :key="item.id">
        <div class="item_content_wrap">
          <div class="item_content flex_row_betweens">
            <div class="item_content_list item_info">
              <div class="list_title">{{ item.modelName }}</div>
              <div class="list_text">
                <p class="item_pro_name overflow_elps" :title="item.modelDesc">
                  {{ item.modelDesc }}
                </p>
              </div>
            </div>
            <div class="item_content_list range_date_wrap">
              <div class="list_title">选项</div>
              <div class="list_text">{{ item.optionNum }}</div>
            </div>
          </div>
        </div>
        <div class="item_oper flex_row_start">
          <div class="item_oper_list" @click="operationModel('E', item.modelCode)">
            <!-- <i class="icon el-icon-edit"></i> -->
            <div class="text">编辑</div>
          </div>
          <div
            :class="{
              item_oper_list: true,
              item_oper_list_dist: item.rstatus == 'N'
            }"
            @click="operationModel('S', item.modelCode)"
          >
            <!-- <i class="icon el-icon-document-delete"></i> -->
            <div class="text" v-if="item.rstatus == 'Y'">停用</div>
            <div class="text" v-else>已停用</div>
          </div>
          <div
            :class="{
              item_oper_list: true,
              item_oper_list_dist: item.delFlag == 1
            }"
            @click="operationModel('D', item.modelCode)"
          >
            <!-- <i class="icon el-icon-delete-solid"></i> -->
            <div class="text" v-if="item.delFlag == 0">删除</div>
          </div>
        </div>
      </div>
    </div>
    <div class="edit_item_wrap page_section_main page_shadow" v-if="editSign">
      <el-form :model="ruleForm" :rules="rules" ref="formName" label-width="80px">
        <el-form-item label="词典名称" prop="name">
          <el-input v-model="ruleForm.name"></el-input>
        </el-form-item>
        <el-form-item label="词典描述" prop="desc">
          <el-input type="textarea" v-model="ruleForm.desc"></el-input>
        </el-form-item>
      </el-form>
      <div class="type_item_wrap">
        <div class="edu_info_header">
          <div class="item num">选项</div>
          <div class="item">选项描述</div>
          <div class="item num">选项分值</div>
          <div class="item num">操作</div>
        </div>
        <div class="edu_info_main">
          <div v-if="itemData.length > 0">
            <div class="edu_info_item" v-for="(item, index) in itemData" :key="item.id">
              <span class="item num">选项{{ index + 1 }}</span>
              <el-input class="item" v-model="item.optionContent" placeholder></el-input>
              <el-input class="item num" type="number" v-model="item.optionScore"></el-input>
              <div class="item num">
                <el-button
                  class="color_danger icon_del"
                  @click.native.prevent="deleteItem(item, index)"
                  link
                  icon="el-icon-delete"
                ></el-button>
              </div>
            </div>
          </div>
          <div class="no_data_tip" v-else>暂无数据</div>
          <div class="align_center padd_TB_30">
            <el-button class="page_confirm_btn" type="primary" @click="addItem">新增选项</el-button>
            <el-button class="page_confirm_btn" type="primary" @click="submit">确认</el-button>
            <el-button class="page_confirm_btn" type="primary" @click="cancelItem">取消</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getModelList,
  getCurModelInfo,
  editModel,
  stopModuleDictionary,
  deleteModuleDictionary
} from '../../../request/api'
export default {
  name: 'evaluationItems',
  props: ['tabsPanesign', 'tabsPaneInfo'],
  data() {
    return {
      modelId: '',
      listData: [],
      editSign: false,
      infoData: {
        itemDesc: '',
        itemId: '',
        moduleCode: '',
        modelId: ''
      },
      ruleForm: {
        name: '',
        desc: ''
      },
      rules: {
        name: [{ required: true, message: '请输入词典名称', trigger: 'blur' }]
      },
      itemData: [
        // {
        //   optionContent: "",
        //   optionDesc: "",
        //   optionNbr: "",
        //   optionScore: "",
        // },
      ]
    }
  },
  components: {},
  watch: {
    tabsPanesign: {
      deep: true,
      handler: function (val) {
        if (val) {
          console.log(val)
          this.editSign = false
        }
      }
    },
    tabsPaneInfo: {
      deep: true,
      handler: function (val) {
        if (val) {
          console.log(val)
          this.init(val)
        }
      }
    }
  },
  mounted() {
    console.log(this.tabsPanesign)
    console.log(this.tabsPaneInfo)
    this.init(this.tabsPaneInfo)
  },
  methods: {
    init(val) {
      this.infoData.moduleCode = this.tabsPaneInfo[0].moduleCode
      this.infoData.modelId = this.tabsPaneInfo[0].modelId
      this.modelId = val[0].modelId
      this.getModelListFun()
    },
    getModelListFun() {
      getModelList({
        modelId: this.modelId
      }).then(res => {
        this.listData = []
        if (res.code == 200) {
          this.listData = res.data
        }
      })
    },
    addDict() {
      this.ruleForm = {
        name: '',
        desc: ''
      }
      this.itemData = [
        // {
        //   optionContent: "",
        //   optionDesc: "",
        //   optionNbr: "",
        //   optionScore: "",
        // },
      ]
      console.log(this.ruleForm.name)
      this.editSign = true
    },
    // 操作
    operationModel(type, code) {
      if (type == 'E') {
        // 编辑
        this.getCurModelInfoFun(code)
        this.editSign = true
      } else if (type == 'S') {
        // 停用
        this.stopModuleDictionaryFun(code)
      } else if (type == 'D') {
        // 删除
        this.$confirm('确认删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.deleteModuleDictionaryFun(code)
          })
          .catch(() => {})
      }
    },
    deleteModuleDictionaryFun(code) {
      deleteModuleDictionary({
        modelId: this.modelId,
        moduleCode: code
      }).then(res => {
        if (res.code == 200) {
          this.getModelListFun()
          this.$msg.success(res.msg)
        }
      })
    },
    stopModuleDictionaryFun(code) {
      stopModuleDictionary({
        modelId: this.modelId,
        moduleCode: code
      }).then(res => {
        if (res.code == 200) {
          this.getModelListFun()
          this.$msg.success(res.msg)
        }
      })
    },
    //获取模型信息
    getCurModelInfoFun(code) {
      getCurModelInfo({
        modelId: this.modelId,
        modelCode: code
      }).then(res => {
        if (res.code == 200) {
          this.ruleForm = {
            name: res.data[0].moduleName,
            desc: res.data[0].itemName
          }
          this.itemData = res.data[0].optionList
          this.infoData = res.data[0]
        }
      })
    },
    // --------page2---------
    editModelFun() {
      if (this.ruleForm.name == '') {
        this.$msg.warning('请填写词典名称！')
        return
      }
      for (let i = 0; i < this.itemData.length; i++) {
        if (
          this.itemData[i].optionContent == '' &&
          (this.itemData[i].optionScore == null || this.itemData[i].optionScore == '')
        ) {
          this.$msg.warning('请填写选项信息！')
          return
        }
      }
      let modelItemRequest = {
        moduleName: this.ruleForm.name,
        itemName: this.ruleForm.desc,
        itemDesc: this.infoData.itemDesc,
        itemId: this.infoData.itemId,
        moduleCode: this.infoData.moduleCode ? this.infoData.moduleCode : this.tabsPaneInfo[0].moduleCode,
        moduleId: this.infoData.modelId ? this.infoData.modelId : this.tabsPaneInfo[0].modelId,
        optionList: this.itemData
      }
      editModel(modelItemRequest).then(res => {
        if (res.code == 200) {
          this.getModelListFun()
          this.$msg.success(res.msg)
          this.editSign = false
        } else {
          this.$msg.error(res.msg)
        }
      })
    },

    deleteItem(item, index) {
      if (item.optionNbr == 0) {
        this.$confirm('确认删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.itemData.splice(index, 1)
          })
          .catch(() => {})
      } else {
        // 调用删除接口
        console.log('不调用删除接口，直接删除')
        this.itemData.splice(index, 1)
      }
    },
    submit() {
      this.editModelFun()
    },
    cancelItem() {
      this.editSign = false
    },
    addItem() {
      let creatType = {
        optionContent: '',
        optionDesc: '',
        optionNbr: '',
        optionScore: ''
      }
      this.itemData.push(creatType)
    }
  }
}
</script>

<style scoped lang="scss">
.evaluation_items_wrap {
  .title_wrap {
    height: 40px;
  }
  .training_activities_item {
    position: relative;
    padding: 12px 30px;
    margin-bottom: 8px;
    border: 1px solid #e5e5e5;
    overflow: hidden;
    .progress_state {
      width: 100px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      position: absolute;
      background: #90d99e;
      transform: rotate(-45deg);
      transform-origin: center;
      font-size: 10px;
      color: #fff;
      left: 0;
      left: -28px;
      top: 7px;
    }
    .item_index {
      width: 50px;
      text-align: center;
      font-weight: bold;
      font-size: 20px;
      color: #0099ff;
    }
    .item_content_wrap {
      width: 65%;
      padding: 0 8px;
      // background: pink;
      .item_content {
        padding-right: 10px;
        .item_content_list {
          color: #525e6c;
          .list_title {
            font-weight: bolder;
            line-height: 38px;
          }
          .list_text {
            .item_pro_name {
              width: 600px;
              .check_pro {
                line-height: 19px;
                margin: 0 5px 0 0;
                span {
                  display: inline-block;
                  font-weight: bold;
                  color: #0099ff;
                  font-size: 16px;
                  padding-right: 5px;
                }
              }
            }
          }
          .progress_details_item {
            text-align: center;
            // height: 38px;
            line-height: 38px;
            span {
              width: 23px;
              text-align: right;
              font-weight: bold;
              color: #0099ff;
              font-size: 16px;
            }
            p {
            }
          }
          .list_num {
            // font-size: 16px;
            font-style: normal;
          }
        }
        .check_progress_wrap {
          // width: 220px;
          // background: pink;
        }
        .enq_status_wrap {
          width: 90px;
        }
        .item_info {
          // background: green;
          width: 240px;
        }
        .range_date_wrap {
          .list_text {
            // line-height: 38px;
            line-height: 19px;
          }
        }
      }
    }
    .progress_details {
      padding: 14px 0 0 0;
      width: 30%;
      background: #ebf4ff;
      height: 96px;
      border-radius: 5px;
    }
    .item_oper {
      width: 250px;
      text-align: center;
      // background: pink;
      .item_oper_list {
        color: #0099ff;
      }
      .item_oper_list_dist {
        color: #b8b9bb;
        cursor: default;
      }
      .icon {
        display: inline-block;
        font-size: 28px;
      }
      &_list {
        width: 100%;
        cursor: pointer;
      }
    }
  }
  .edit_item_wrap {
    .el-form-item {
      .el-form-item__label {
        // width: 100px;
        // background: pink;
        text-align: left;
      }
      .el-form-item__content {
        width: 700px;
        .el-input {
          .el-input__inner {
            // height: 35px;
          }
        }
        .el-textarea {
          .el-textarea__inner {
            resize: none;
          }
        }
      }
    }
    .type_item_wrap {
      .edu_info_header {
        .item {
          width: 55%;
          text-align: center;
          &.num {
            width: 15%;
          }
        }
      }
      .edu_info_item {
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        .item {
          width: 55%;
          text-align: center;
          &.num {
            width: 15%;
            padding: 0;
            button {
              padding: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
