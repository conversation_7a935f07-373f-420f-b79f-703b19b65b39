<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <slot></slot>
        <div class="page_second_title">素质评价</div>
        <el-row :gutter="16">
            <el-col :span="6">
                <div class="item_title">素质评价得分</div>
                <div class="text_center score">
                    <customProcess :size="250" :num="qualityScore"/>
                    <!-- <el-progress
                        type="circle"
                        :stroke-width="20"
                        stroke-linecap="butt"
                        :percentage="qualityScore"
                        :format="progressFromat"
                    ></el-progress> -->
                </div>
            </el-col>
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24">
                <div class="item_title">人员综合素质评分各维度表现</div>
                <div
                    class="chart_box"
                    id="comprehensiveQualityForLeaving"
                ></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                    :showAllText="list.showAllText"
                />
            </el-col>
            <el-col :span="24">
                <div class="item_title">综合素质评价人员定位分布</div>
                <div class="distribution_box">
                    <div class="box_title">评价内容</div>
                    <div class="box_item" v-for="item in moduleList">
                        <div class="item_list">
                            <div class="item_text head_text">
                                {{ item.disctionary.name }}
                            </div>
                            <div
                                class="item_text"
                                v-for="list in item.modelOption"
                            >
                                {{ list.optionContent }}
                            </div>
                        </div>
                        <div class="item_num">
                            <div class="num head_num">
                                {{ item.disctionary.score }}分
                            </div>
                            <div class="num" v-for="list in item.modelOption">
                                {{ list.userNum }}人
                            </div>
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        getQualityEval,
        getQualityEvalOverallScore,
        getQualityEvalWhole,
        getQualityEvalClass,
        getQualityEvalUserScore,
        getQualityEvalUserWhole,
        getQualityEvalUserComment,
        getUserdistribution,
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import listComp from "./components/listComp.vue";
    import customProcess from "@/components/talent/common/customProcess.vue";

    export default {
        name: "orgRQualityEval",
        props: {
            enqId: String,
            orgCode: String,
            isPdf: {
                type: Boolean,
                default: false,
            },
        },
        components: { tableComps, listComp ,customProcess},
        data() {
            return {
                qualityScore: 0,
                moduleList: [],
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "素质评价分级",
                        elSpan: 18,
                        chartHeight: "200",
                        chartType: "XBar",
                        dataKey: "grade",
                    },
                    // {
                    //     chartDomId: this.$util.createRandomId(),
                    //     title: "人员综合素质明细维度",
                    //     elSpan: 24,
                    //     chartType: "XBar",
                    //     dataKey: "comprehensiveQualityForLeaving",
                    // },
                ],
                listArr: [
                    {
                        title: "本组织及直接下级组织素质评价各维度得分",
                        ajaxUrl: getQualityEvalOverallScore,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "组织",
                                prop: "orgName",
                            },
                        ],
                    },
                    {
                        title: "本组织及直接下级组织素质评价分级",
                        ajaxUrl: getQualityEvalClass,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "组织",
                                prop: "orgName",
                            },
                        ],
                    },
                    {
                        title: "本组织及直接下级组织素质评价360°",
                        ajaxUrl: getQualityEvalWhole,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "组织",
                                prop: "orgName",
                            },
                        ],
                    },

                    {
                        title: "人员素质评价得分详情",
                        ajaxUrl: getQualityEvalUserScore,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                        ],
                    },
                    {
                        title: "人员素质评价360°",
                        ajaxUrl: getQualityEvalUserWhole,
                        isAsyncColumns: true,
                        afterColumns: [
                            {
                                label: "综合得分",
                                prop: "qualityOverallScore",
                            },
                            {
                                label: "系统评价等级",
                                prop: "sysGrade",
                            },
                            {
                                label: "最终评价等级",
                                prop: "grade",
                            }

                        ],
                        columns: [
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                        ],
                    },
                    {
                        title: "人员素质评价评语",
                        ajaxUrl: getQualityEvalUserComment,
                        isAsyncColumns: false,
                        showAllText: true,
                        columns: [
                            {
                                label: "被评人姓名",
                                prop: "objectName",
                                width: 200,
                            },
                            {
                                label: "评价人姓名",
                                prop: "userName",
                                width: 200,
                            },
                            // {
                            //     label: "评语来源",
                            //     prop: "relationType",
                            //     width: 200,
                            // },
                            {
                                label: "评语说明",
                                prop: "comment",
                            },
                        ],
                    },
                ],
            };
        },
        created() {
            this.getData();
        },
        mounted() {},
        methods: {
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                        padding: 115
                        
                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getQualityEval(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        let data = res.data;
                        this.qualityScore = data.qualityScore;
                        this.initChart(data);

                        let chartData = {
                            data: data.comprehensiveQualityForLeaving.chartData,
                            legend: data.comprehensiveQualityForLeaving.legend,
                        };
                        echartsRenderPage(
                            "comprehensiveQualityForLeaving",
                            "XBar",
                            null,
                            null,
                            chartData
                        );
                    }
                });
                getUserdistribution(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.moduleList = res.data;
                    }
                });
            },
            progressFromat(val) {
                console.log(val);
                return val;
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .score {
        margin-top: 30px;
    }
    .distribution_box {
        .box_title {
            background-color: #00b0f0;
            color: #fff;
            line-height: 28px;
            padding-left: 16px;
        }
        .box_item {
            border-bottom: 1px solid #00b0f0;
            border-left: 1px solid #00b0f0;
            color: #00b0f0;
            .item_text {
                flex: 1;
                padding: 8px 5px;
                text-align: center;
                &.head_text {
                    flex: 0 0 120px;
                    text-align: center;
                    color: #333;
                    border-right: 1px solid #00b0f0;
                }
            }
            .item_num {
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                justify-content: flex-start;
                line-height: 28px;
                text-align: center;
                .num {
                    flex: 1;
                    &.head_num {
                        flex: 0 0 120px;
                        border-right: 1px solid #00b0f0;
                    }
                }
            }
        }
        .item_list {
            display: flex;
            flex-flow: row nowrap;
            align-items: stretch;
            justify-content: flex-start;
            border-bottom: 1px solid #00b0f0;
        }
    }
</style>