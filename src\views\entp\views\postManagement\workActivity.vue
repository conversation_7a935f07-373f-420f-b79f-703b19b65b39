<template>
    <div class="work_activity_wrap bg_write">
        <div class="page_main_title">工作活动</div>
        <div class="page_section">
            <div class="work_activity_center clearfix">
                <div class="page_section_aside">
                    <div class="aside_tree_title flex_row_between">
                        <div>族群分类</div>
                    </div>
                    <div class="aside_tree_list">
                        <tree-comp-radio :treeData="treeData" :needCheckedFirstNode="false" :canCancel="true" :defaultCheckedKeys="defaultCheckedKeys" @clickCallback="clickCallback"></tree-comp-radio>
                    </div>
                </div>
                <div class="page_section_main page_shadow">
                    <div class="oper_btn_wrap">
                        <el-button class="page_add_btn" type="primary" @click="addItem">新增</el-button>
                    </div>
                    <div class="edu_info_header">
                        <div class="item num">序号</div>
                        <div class="item">工作活动类型</div>
                        <div class="item">工作任务名称</div>
                        <div class="item">对应岗位类型</div>
                        <div class="item">状态</div>
<!--                        <div class="item">排序值</div>-->
                        <div class="item control">操作</div>
                    </div>
                    <div class="edu_info_mmain">
                        <work-activities-item
                                v-if="workActivityItemData.length > 0"
                                :itemData="workActivityItemData"
                                :pageBean="pageBean"
                                @deleteItem="deleteItem"
                                @handleSizeChange="handleSizeChange"
                                @handleCurrentChange="handleCurrentChange"
                        ></work-activities-item>
                        <div class="no_data_tip" v-else>暂无数据</div>
                        <div class="align_center padd_TB_30" v-if="workActivityItemData.length > 0">
                            <el-button class="page_confirm_btn" type="primary" @click="submit">确认</el-button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</template>
 
<script>
import treeCompRadio from "@/components/talent/treeComps/treeCompRadio";
import workActivitiesItem from "./components/workActivitiesItem";

import {getJobClassTree,getJobActivity,addJobActivity,delJobActivity,getJobActivityPage,getDict} from "../../request/api"
export default {
    name: "workActivity",
    components: {
        treeCompRadio,
        workActivitiesItem
    },
    data() {
        return {
            checkedJobClassCode:"",
            checkedJobClassName:"",
            defaultCheckedKeys:[],
            workActivityItemData: [
                // {
                //     id: "1",
                //     postSequence: "市场销售",
                //     activityType: "业务类",
                //     taskName: "客户开发",
                //     state: "启用",
                //     sortIndex: "0"
                // }
            ],
            dataLength:0,
            treeData: [],
            pageBean:{
                current:1,
                size:10,
                total:0
            }
        };
    },
    mounted(){
        this.getJobClassTreeFun();
    },
    methods: {
        clickCallback(code,lastNode,data) {
            console.log(code);
            this.checkedJobClassCode = code;
            this.checkedJobClassName=data.value;
            this.pageBean.current = 1;
            this.getJobActivityFun();
        },
        //删除按钮
        deleteItem(item, index) {
            console.log(item);
            if (!item.jobActivityCode) {
                // 以当前行数据是否包含 jobActivityCode 字段判断是否是入库的数据
                // 未入库的数据直接删除，不调用接口
                console.log("不调用删除接口，直接删除");
                this.workActivityItemData.splice(index, 1);
            } else {
                // 调用删除接口
                let jobActivityCode = item.jobActivityCode;
                this.$confirm('确认删除吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.delJobActivityFun(jobActivityCode);
                }).catch(() => {

                });

            }
        },
        //新增按钮
        addItem() {
            if(!this.checkedJobClassCode){
                this.$msg.warning("请勾选岗位类型！")
                return;
            }

            let obj = this.workActivityItemData[
                this.workActivityItemData.length - 1
            ];
            let addObj = {
                jobActivityCode:"",
                jobActivityName:"",
                jobActivityType:"",
                jobClassCode:this.checkedJobClassCode,
                jobClassName:this.checkedJobClassName,
                // sortNbr:"",
                status:"Y",
            };
            if (!obj) {
                this.workActivityItemData.push(addObj);
                return;
            }
            for(let i =0; i<this.workActivityItemData.length; i++){
                let obj =this.workActivityItemData[i];
                if(!obj.jobActivityName || !obj.jobActivityType || !obj.jobClassCode ){
                    this.$msg.warning("请完善数据后再新增！");
                    return;
                }
            }
            this.workActivityItemData.push(addObj);
            console.log(this.workActivityItemData);
        },
        //提交
        submit() {
            for(let i =0; i<this.workActivityItemData.length; i++){
                let obj =this.workActivityItemData[i];
                if(!obj.jobActivityName || !obj.jobActivityType || !obj.jobClassCode ){
                    this.$msg.warning("请完善数据后提交！");
                    return;
                }
            }
            // console.log(this.workActivityItemData);
            this.addJobActivityFun()
        },

        //获取工作活动列表
        //不分页
        // getJobActivityFun(){
        //     let params={
        //         jobClassCode:this.checkedJobClassCode
        //     }
        //     getJobActivity(params).then(res=>{
        //         console.log(res)
        //         if(res.code == 200){
        //             this.workActivityItemData = res.data;
        //             this.workActivityItemData.forEach(item=>{
        //                 item.status = item.rstatus;
        //             })
        //         }else{
        //             this.$msg.warning(res.msg);
        //         }
        //     })
        // },

        //pageSize 改变时会触发
        handleSizeChange(size){
            console.log(size)
            this.pageBean.current=1;
            this.pageBean.size=size;
            this.getJobActivityFun();
        },

        //currpage 改变时会触发
        handleCurrentChange(page){
            this.pageBean.current = page;
            this.getJobActivityFun();
        },

        //分页
        getJobActivityFun(){
            let params={
                jobClassCode:this.checkedJobClassCode,
                current:this.pageBean.current,
                size:this.pageBean.size

            }
            getJobActivityPage(params).then(res=>{
                console.log(res)
                if(res.code == 200){
                    this.workActivityItemData = res.data;
                    this.workActivityItemData.forEach(item=>{
                        item.status = item.rstatus;
                    })
                    this.pageBean=res.page;
                    this.dataLength=this.workActivityItemData.length;
                }else{
                    this.$msg.warning(res.msg);
                }
            })
        },

        //新增、修改
        addJobActivityFun(){
            let params = this.workActivityItemData;
            addJobActivity(params).then(res=>{
                console.log(res);
                if(res.code == 200){
                    this.$msg.success("更新成功！");
                    this.getJobActivityFun();
                }
            })
        },
        //删除
        delJobActivityFun(code){
            let params ={
                jobActivityCode:code
            }
            delJobActivity(params).then(res=>{
                // console.log(res);
                if(res.code == 200){
                    this.$msg.success("删除成功！")
                    if(this.dataLength == 1){
                        this.pageBean.current > 1 && this.pageBean.current--;
                    }
                    this.getJobActivityFun();
                }else{
                    this.$msg.warning(res.msg);
                }
            })
        },
        //获取族群树
        getJobClassTreeFun(){
            getJobClassTree({}).then(res=>{
                this.treeData = res;
                this.getJobActivityFun();
            })
        },
    }
};
</script>
 
<style scoped lang="scss">
    .aside_tree_title {
        margin-top: 0;
    }
    .edu_info_header {
         .item{
             width: 30%;
             text-align: center;
             &.num,&.control{
                 width: 10%;
             }
         }
    }
</style>