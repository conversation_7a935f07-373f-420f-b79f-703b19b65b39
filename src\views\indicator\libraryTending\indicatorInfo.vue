<script setup>
import Table from '../components/table.vue'
import { libraryTending } from '@/assets/data/data9.js'
defineOptions({ name: 'indicatorInfo' })
const data = ref(libraryTending.kpiList)
const columns = ref([
  {
    prop: 'kpiCode',
    label: '指标编码'
  },
  {
    prop: 'kpiName',
    label: '指标名称',
    width: 250
  },
  {
    prop: 'kpiClassCode',
    label: '指标类型'
  },
  {
    prop: 'kpiType',
    label: '指标类别'
  },
  {
    prop: 'kpiNature',
    label: '指标性质'
  },
  {
    prop: 'kpiExpression',
    label: '计算公式',
    width: 320,
    showOverflowTooltip: true
  },
  {
    prop: 'kpiUnit',
    label: '单位'
  },
  {
    prop: 'dataSource',
    label: '数据来源'
  },
  {
    prop: 'kpiCycle',
    label: '指标周期'
  },
  {
    prop: 'kpiPolarity',
    label: '指标极性'
  },
  {
    prop: 'parentKpiCode',
    label: '上级指标'
  },
  {
    prop: 'effectKpi',
    label: '影响指标',
    align: 'center'
  }
])
onMounted(() => {})
</script>
<template>
  <div class="indicatorInfo_wrap">
    <div class="second_wrap justify-between">
      <div class="page-title-line">指标信息</div>
      <div class="second_r justify-between">
        <div class="add_btn"><span class="icon"></span> 新增指标</div>
        <div class="operate_data justify-between">
          <div class="l_btn operate_btn"><span class="icon icon_import"></span> 导入</div>
          <div class="r_btn operate_btn"><span class="icon icon_export"></span> 导出</div>
        </div>
      </div>
    </div>
    <Table :data="data" :columns="columns" :showIndex="true"></Table>
  </div>
</template>
<style lang="scss" scoped>
.indicatorInfo_wrap {
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
  font-size: 14px;
  .justify-between {
    display: flex;
    justify-content: space-between;
  }
  .second_wrap {
    margin-bottom: 10px;
    height: 36px;
    align-items: center;
    .page-title-line {
      margin-bottom: -5px;
    }
    .second_r {
      .add_btn {
        width: 100px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        color: #fff;
        background: #40a0ff;
        border-radius: 6px 6px 6px 6px;
        cursor: pointer;
        .icon {
          display: inline-block;
          margin-bottom: -2px;
          width: 16px;
          height: 16px;
          background: url('@/assets/imgs/indicator/icon_01.png') no-repeat center;
        }
      }
      .operate_data {
        margin-left: 10px;
        height: 36px;
        line-height: 36px;
        .operate_btn {
          width: 68px;
          text-align: center;
          border: 1px solid #666666;
          cursor: pointer;
          .icon {
            display: inline-block;
            margin-bottom: -2px;
            width: 16px;
            height: 16px;
          }
        }
        .l_btn {
          border-radius: 6px 0 0 6px;
          border-right: 1px solid transparent;
          .icon {
            background: url('@/assets/imgs/indicator/icon_02.png') no-repeat center center;
          }
        }
        .l_btn:hover {
          color: #40a0ff;
          border: 1px solid #40a0ff;
          .icon {
            background: url('@/assets/imgs/indicator/icon_04.png') no-repeat center center;
          }
          + .r_btn {
            border-left: 1px solid transparent;
          }
        }
        .r_btn {
          border-radius: 0 6px 6px 0;
          .icon {
            background: url('@/assets/imgs/indicator/icon_03.png') no-repeat center center;
          }
        }
        .r_btn:hover {
          color: #40a0ff;
          border: 1px solid #40a0ff;
          .icon {
            background: url('@/assets/imgs/indicator/icon_05.png') no-repeat center center;
          }
        }
      }
    }
  }
}
</style>
