<template>
    <div class="creat_model_wrap bg_write">
        <div class="page_main_title clearfix">
            模型管理
            <div class="goback_geader" @click="$util.goback()">
                <i class="el-icon-arrow-left"></i>返回
            </div>
        </div>
        <div class="page_section">
            <div class="talent_raview_main padd_TB_16">
                <step-bar :stepData="stepData" :currentIndex="currentIndex" @stepClick="stepClick" :needClick="needClick" ></step-bar>
                <component :buildStatus="buildStatus" :is="compArr[currentIndex]" :modelId="modelId" @prevStep="prev" @nextStep="next"></component>
<!--                <div class="talent_raview_btn_wrap">-->
<!--                    <el-button class="page_confirm_btn" type="primary" @click="prev()" v-if="this.currentIndex > 0">上一步</el-button>-->
<!--                    <el-button class="page_confirm_btn" type="primary" @click="affirm()" v-if="this.currentIndex == this.stepData.length-1">确认</el-button>-->
<!--                    <el-button class="page_confirm_btn" type="primary" @click="next()" v-else>下一步</el-button>-->
<!--                </div>-->
            </div>
        </div>
    </div>
</template>
 
<script>
import stepBar from "@/components/talent/stepsComps/stepBar";
import modelBasicInfo from "./creatModelComponents/modelBasicInfo";
import abilityClassify from "./creatModelComponents/abilityClassify";
import abilityDict from "./creatModelComponents/abilityDict";
import hierarchicNorm from "./creatModelComponents/hierarchicNorm";
import previewComfirm from "./creatModelComponents/previewComfirm";
export default {
    name: "creatModel",
    components: {
        stepBar
    },
    data() {
        return {
            needClick:false,
            modelId:this.$route.query.modelId,
            buildStatus:this.$route.query.buildStatus,
            compArr: [
                modelBasicInfo,
                abilityClassify,
                abilityDict,
                hierarchicNorm,
                previewComfirm,
            ],
            currentIndex: "0",
            stepData: [
                {
                    name: "模型基本信息",
                    state: "inComplete",
                    code:0

                },
                {
                    name: "设置能力分类",
                    state: "inComplete",
                    code:1
                },
                {
                    name: "设置能力词典",
                    state: "inComplete",
                    code:2
                },
                {
                    name: "设置分级标准",
                    state: "inComplete",
                    code:3
                },
                {
                    name: "预览确认",
                    state: "inComplete",
                    code:4
                },
            ]
        };
    },
    created() {
        if(this.modelId){
            console.log(this.modelId)
            this.needClick=true;
            this.stepData.forEach(item=>{
                item.enqProgress ="Y"
            })
        }
        if(this.buildStatus == '4'){

        }
    },
    methods: {
        next(modelId) {
            if(modelId){
                this.modelId = modelId;
            }
            if (this.currentIndex == this.stepData.length - 1) {
                return false;
            }
            this.stepData[this.currentIndex].enqProgress = "Y";
            this.currentIndex++;
            this.stepData[this.currentIndex].state = "inProgress";
            // console.log(this.currentIndex)
        },
        affirm(){
            // this.stepData[this.currentIndex].state = ''
            // this.stepData[this.currentIndex].state = "completed";
        },
        prev() {
            if (this.currentIndex == 0) {
                return false;
            }
            this.currentIndex--;
            // console.log(this.currentIndex)
        },
        stepClick(code ,index){
            console.log(index)
            this.currentIndex =index;
        }


    }
};
</script>
 
<style scoped lang="scss">
.creat_model_wrap{
    .page_section{
        .talent_raview_btn_wrap{
            text-align: center;
        }
    }
}

</style>