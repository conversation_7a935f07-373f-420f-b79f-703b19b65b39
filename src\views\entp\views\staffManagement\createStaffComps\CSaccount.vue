<template>
  <div class="account_management_wrap">
    <div class="page_second_title">账号密码</div>
    <div class="account_management_center clearfix">
      <div class="info_display_wrap">
        <div class="info_display_item_box">
          <div class="info_display_item">
            <div class="info_display_item_label">账号</div>
            <div class="info_display_item_value">***********</div>
          </div>
          <div class="info_display_item">
            <div class="info_display_item_label">手机</div>
            <div class="info_display_item_value">
              <span class="modify fr" @click="showChangePhoneNum = true"></span>
            </div>
          </div>
          <div class="info_display_item">
            <div class="info_display_item_label">邮箱</div>
            <div class="info_display_item_value">
              <span class="modify fr">修改</span>
            </div>
          </div>
          <div class="info_display_item">
            <div class="info_display_item_label">密码</div>
            <div class="info_display_item_value">
              <span class="modify fr" @click="showChangePassword = true">修改</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="修改手机号" v-model:visible="showChangePhoneNum">
      <el-form>
        <el-form-item class="form_item" label="新手机号" :label-width="formLabelWidth">
          <el-input v-model="phoneNum" maxlength="11"></el-input>
        </el-form-item>
        <el-form-item class="form_item" :label-width="formLabelWidth">
          <el-input v-model="VerificationCode">
            <template v-slot:append>
              <el-button>获取验证码</el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="showChangePhoneNum = false">取 消</el-button>
          <el-button type="primary" @click="confirmPhoneNum">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog title="修改密码" v-model:visible="showChangePassword">
      <el-form>
        <el-form-item class="form_item" label="原密码" :label-width="formLabelWidth">
          <el-input v-model="password" placeholder="请输入原密码"></el-input>
        </el-form-item>
        <el-form-item class="form_item" label="新密码" :label-width="formLabelWidth">
          <el-input v-model="newPassword" placeholder="数字、字母、符号两种及以上的组合,6-20个字符"></el-input>
        </el-form-item>
        <el-form-item class="form_item" label="新密码确认" :label-width="formLabelWidth">
          <el-input v-model="newPasswordConfirm" placeholder="数字、字母、符号两种及以上的组合,6-20个字符"></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="showChangePassword = false">取 消</el-button>
          <el-button type="primary" @click="confirmPassword">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CSaccount',
  data() {
    return {
      showChangePhoneNum: false,
      showChangePassword: false,
      formLabelWidth: '120px',
      phoneNum: '',
      VerificationCode: '',
      password: '123456',
      newPassword: '',
      newPasswordConfirm: ''
    }
  },
  methods: {
    modify: function (type) {},
    confirmPhoneNum: function () {
      this.showChangePhoneNum = false
    },
    confirmPassword: function () {
      this.showChangePassword = false
    }
  }
}
</script>

<style scoped lang="scss">
.account_management_center {
  padding-left: 16px;
  min-height: 500px;
  .info_display_item {
    margin-bottom: 20px;
    .info_display_item_value {
      width: 280px;
      padding: 0 16px;
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      .modify {
        font-size: 14px;
        font-weight: bold;
        color: #0099fd;
        cursor: pointer;
      }
    }
  }
}
.form_item {
  width: 480px;
}
</style>
