import qs from 'qs'
import service from '@/api/request'

// 并发请求
export const requestAll = arr => Promise.all(arr)

// 通用action
export const setActionType = data =>
  service({
    url: `/enq/${data.url}`,
    method: 'post',
    data: qs.stringify(data.data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 示例
export const getData = data => service({ url: `/enq/XXX`, method: 'get', params: data })
export const postData = data =>
  service({
    url: `/enq/XXX`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 下载
export const fileDownload = data =>
  service({ url: `/entp/attach/download`, method: 'get', params: data, responseType: 'blob' })

// 获取当前登录人的基本信息
export const getUserInfo = data => service({ url: `/enq/enqUser/getUserInfo`, method: 'get', params: data })
// 获取当前登录人企业信息
export const getCompanyInfo = data => service({ url: `/entp/company`, method: 'get', params: data })

// 获取数据字典列表
export const getDictList = data => service({ url: `/enq/enqInfo/getDictList`, method: 'get', params: data })

// 岗位信息盘点相关
export const getCmpyBizDomain = data => service({ url: `/enq/enqOrg/getCmpyBizDomain`, method: 'get', params: data })
export const getCmpyJobClass = data => service({ url: `/enq/enqOrg/getCmpyJobClass`, method: 'get', params: data })
export const getCmpyJobLevel = data => service({ url: `/enq/enqOrg/getCmpyJobLevel`, method: 'get', params: data })
export const getPostData = data => service({ url: `/entp/jobClass/tree`, method: 'get', params: data })

// 盘点信息接口
export const getEnqList = data => service({ url: `/enq/enqInfo/page`, method: 'get', params: data })
export const personalComplete = data =>
  service({
    url: `/enq/enqInfo/personalComplete`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const deptEnqComplete = data =>
  service({
    url: `/enq/enqInfo/deptEnqComplete`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const complete = data =>
  service({
    url: `/enq/enqInfo/enqComplete`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const getAction = data => service({ url: `/enq/enqInfo/getAction`, method: 'get', params: data })

// 发起盘点
export const getEnqInfo = data => service({ url: `/enq/enqInfo/getEnqInfo`, method: 'get', params: data })
export const getCeEnqModuleById = data => service({ url: `/enq/enqInfo/getEnqModule`, method: 'get', params: data })
export const getLoginPersonDeptEnq = data =>
  service({ url: `/enq/enqInfo/getLoginPersonDeptEnq`, method: 'get', params: data })
export const getCeEnqModule = data => service({ url: `/enq/enqInfo/getCeEnqModule`, method: 'get', params: data })
export const createReview = data => service({ url: `/enq/enqInfo/create`, method: 'post', data })
export const getAllUser = data => service({ url: `/enq/enqInfo/getAllUser`, method: 'get', params: data })
export const getModelInfo = data => service({ url: `/enq/enqInfo/getModelInfo`, method: 'get', params: data })

// 盘点范围
export const getFilter = data => service({ url: `/enq/enqInfo/getFilter`, method: 'get', params: data })
export const getActivePost = data =>
  service({
    url: `/enq/enqInfo/getActivePost`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const getUserListByOrgCodes = data =>
  service({ url: `/enq/enqInfo/getUserInfoByOrgCodes`, method: 'post', data })
export const saveEnqRange = data => service({ url: `/enq/enqInfo/saveEnqRange`, method: 'post', data })
export const getEnqPost = data => service({ url: `/enq/enqInfo/getEnqPost`, method: 'get', params: data })

// 盘点模型
export const selectEnqModel = data => service({ url: `/enq/enqInfo/selectEnqModel`, method: 'get', params: data })
export const getEnqPointPost = data => service({ url: `/enq/enqInfo/getUserInfoBef`, method: 'post', data })
export const exportDownload = data =>
  service({
    url: `/enq/enqInfo/exportUserInfoData`,
    method: 'get',
    params: data,
    responseType: 'blob'
  })
export const readUserInfoData = data =>
  service({
    url: `/enq/enqInfo/readUserInfoData`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
export const saveEnqScopeUser = data => service({ url: `/enq/enqInfo/saveEnqScopeUser`, method: 'post', data })
export const checkEnqUser = data =>
  service({
    url: `/enq/enqInfo/checkEnqUser`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const checkEnqScope = data => service({ url: `/enq/enqInfo/checkEnqScope`, method: 'post', data })
export const checkEnqLoser = data =>
  service({
    url: `/enq/enqInfo/checkEnqLoser`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const exportEnqSubUserInfoData = data =>
  service({
    url: `/enq/enqInfo/exportEnqSubUserInfoData`,
    method: 'get',
    params: data,
    responseType: 'blob'
    // method: 'post',
    // data: qs.stringify(data),
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
export const getEnqRelationshipList = data =>
  service({ url: `/enq/enqInfo/getEnqRelationshipList`, method: 'get', params: data })
export const saveEnqRelation = data => service({ url: `/enq/enqInfo/saveEnqRelation`, method: 'post', data })
export const getUserInfoByName = data => service({ url: `/enq/enqInfo/getUserInfoByName`, method: 'get', params: data })
export const enqRelationNext = data => service({ url: `/enq/enqInfo/enqRelationNext`, method: 'get', params: data })
export const getEnqRelation = data => service({ url: `/enq/enqInfo/getEnqRelation`, method: 'get', params: data })
export const importRelation = data =>
  service({
    url: `/enq/enqInfo/importRelation`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
export const exportRelation = data =>
  service({ url: `/enq/enqInfo/exportRelation`, method: 'get', params: data, responseType: 'blob' })
export const getEnqCountUsing = data => service({ url: `/enq/enqInfo`, method: 'get', params: data })

// **************查看报告人员
//  更新报告范围
export const updateReportScope = data =>
  service({
    url: `/enq/enqInfo/updateReportScope`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 查询下级报告范围列表数据
export const getReportScopeList = data =>
  service({ url: `/enq/enqUser/getAllEnqUserInfo`, method: 'get', params: data })
// 启动盘点
export const startEnq = data =>
  service({
    url: `/enq/enqInfo/start`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// **********************************************个人盘点********************************
// 个人盘点信息点击下一步
export const personalNextStep = data =>
  service({
    url: `/enq/enqUser/personalNextStep`,
    method: 'put',
    data
  })

// 获取个人盘点要素
export const getPersonModule = data => service({ url: `/enq/enqInfo/getPersonModule`, method: 'get', params: data })

// 获取个人盘点信息
export const getEnqUserInfo = data => service({ url: `/enq/enqUser/getEnqUserInfo`, method: 'get', params: data })
// 修改个人盘点信息
export const updateEnqUserInfo = data =>
  service({
    url: `/enq/enqUser/updateEnqUserInfo`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 获取当前登录人的盘点岗位
export const getEnqUserPost = data => service({ url: `/enq/enqUser/getEnqUserPost`, method: 'get', params: data })

// 获取个人工作活动信息
export const getEnqUserActivity = data =>
  service({ url: `/enq/enqUser/getEnqUserActivity`, method: 'get', params: data })
// 修改个人工作活动信息
export const updateEnqUserActivity = data =>
  service({
    url: `/enq/enqUser/updateEnqUserActivity`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 根据盘点id组织树
export const getAllOrgInfo = data => service({ url: `/enq/enqUser/getAllOrgInfo`, method: 'get', params: data })
// 根据组织code查询岗位
export const getEnqPostInfo = data => service({ url: `/enq/enqUser/getEnqPostInfo`, method: 'get', params: data })

// 获取教育信息
export const getEnqUserEducation = data =>
  service({ url: `/enq/enqUser/getEnqUserEducation`, method: 'get', params: data })
// 更新教育信息
export const updateEnqUserEducation = data =>
  service({
    url: `/enq/enqUser/updateEnqUserEducation`,
    method: 'post',
    data
  })
// 删除教育信息
export const delEnqUserEducation = data =>
  service({ url: `/enq/enqUser/delEnqUserEducation`, method: 'delete', params: data })

// 获取工作履历
export const getEnqUserExperience = data =>
  service({ url: `/enq/enqUser/getEnqUserExperience`, method: 'get', params: data })
// 删除工作履历
export const delEnqUserExperience = data =>
  service({ url: `/enq/enqUser/delEnqUserExperience`, method: 'delete', params: data })
// 更新工作履历
export const updateEnqUserExperience = data =>
  service({
    url: `/enq/enqUser/updateEnqUserExperience`,
    method: 'post',
    data
  })

// 获取绩效考核类型
export const getKpiCycle = data => service({ url: `/enq/enqUser/getKpiCycle`, method: 'get', params: data })
// 获取绩效信息
export const getEnqUserKpi = data => service({ url: `/enq/enqUser/getEnqUserKpi`, method: 'get', params: data })
// 更新绩效信息
export const updateEnqUserKpi = data =>
  service({
    url: `/enq/enqUser/updateEnqUserKpi`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 获取培训信息
export const getEnqUserTraining = data =>
  service({ url: `/enq/enqUser/getEnqUserTraining`, method: 'get', params: data })
// 修改培训信息
export const updateEnqUserTraining = data =>
  service({
    url: `/enq/enqUser/updateEnqUserTraining`,
    method: 'post',
    data
  })
// 删除培训信息
export const delEnqUserTraining = data =>
  service({ url: `/enq/enqUser/delEnqUserTraining`, method: 'delete', params: data })

// 获取获奖信息
export const getEnqUserAward = data => service({ url: `/enq/enqUser/getEnqUserAward`, method: 'get', params: data })
// 修改获奖信息
export const updateEnqUserAward = data =>
  service({
    url: `/enq/enqUser/updateEnqUserAward`,
    method: 'post',
    data
  })
// 删除获奖信息
export const delEnqUserAward = data => service({ url: `/enq/enqUser/delEnqUserAward`, method: 'delete', params: data })

//删除个人工作
export const delEnqUserWorking = data =>
  service({ url: `/enq/enqUser/delEnqUserWorking`, method: 'delete', params: data })

// 个人规划
export const getEnqUserDevelopment = data =>
  service({ url: `/enq/enqUser/getEnqUserDevelopment`, method: 'get', params: data })
// 修改个人规划
export const updateEnqUserDevelopment = data =>
  service({
    url: `/enq/enqUser/updateEnqUserDevelopment`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 获取当前岗位
export const getCcurrentPosition = data =>
  service({ url: `/enq/enqUser/getCcurrentPosition`, method: 'get', params: data })
// 获取晋升岗位
export const getIdealPost = data => service({ url: `/enq/enqUser/getIdealPost`, method: 'get', params: data })
export const getIdealPosts = data => service({ url: `/enq/enqUser/getIdealPosts`, method: 'get', params: data })
// 个人盘点提交
export const submitPersonnel = data =>
  service({
    url: `/enq/enqUser/syncUserInfo`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 参与调研
export const getEnqUserSurveyById = data =>
  service({ url: `/enq/enqUser/getEnqUserSurveyById`, method: 'get', params: data })
// 保存调研
export const saveEnqUserSurvey = data =>
  service({
    url: `/enq/enqUser/saveEnqUserSurvey`,
    method: 'post',
    data
  })

//个人盘点2021/12/2
export const getWorkContentList = data =>
  service({ url: `/enq/enqUser/getWorkContentList`, method: 'get', params: data })
export const saveWorkContent = data =>
  service({
    url: `/enq/enqUser/saveWorkContent`,
    method: 'post',
    data
  })
export const getQualityEvaluation = data =>
  service({ url: `/enq/enqUser/getQualityEvaluation`, method: 'get', params: data }) //素质评价侧边栏
// 核心素质
export const getCoreQualityEvaluation = p =>
  service({
    url: `/enq/enqUser/getCoreQualityEvaluation`,
    method: 'get',
    params: p
  }) //素质评价侧边栏

export const getQualityEvaluationItem = data =>
  service({ url: `/enq/enqUser/getQualityEvaluationItem`, method: 'get', params: data }) //素质评价查询题目
export const evaluationCommentsList = data =>
  service({ url: `/enq/enqUser/evaluationCommentsList`, method: 'get', params: data }) //素质评价查询点评
export const saveEvaluationComment = data =>
  service({
    url: `/enq/enqUser/saveEvaluationComment`,
    method: 'post',
    data
  }) //素质评价查询点评保存
export const saveSurveySubmit = data =>
  service({
    url: `/enq/enqUser/saveSurveySubmit`,
    method: 'post',
    data
  }) //保存评价
export const saveDurveyUserResult = data =>
  service({ url: `/enq/enqUser/saveDurveyUserResult`, method: 'get', params: data }) //评价下一步判断
export const getKarmaEvaluation = data =>
  service({ url: `/enq/enqUser/getKarmaEvaluation`, method: 'get', params: data }) //业绩评价侧边栏
export const getTopicList = data => service({ url: `/enq/enqUser/getTopicList`, method: 'get', params: data }) //工作感受、满意度、工作驱动题目
export const saveWorkSurveySubmit = data =>
  service({
    url: `/enq/enqUser/saveWorkSurveySubmit`,
    method: 'post',
    data
  }) //工作感受、满意度、工作驱动保存
export const getJobInfoList = data => service({ url: `/enq/enqUser/getJobInfoList`, method: 'get', params: data }) //人员招募查询
export const saveJobInfo = data =>
  service({
    url: `/enq/enqUser/saveJobInfo`,
    method: 'post',
    data
  }) //人员招募保存
export const getTarget = data => service({ url: `/enq/enqUser/getTarget`, method: 'get', params: data }) //目标与结果查询
export const saveTarget = data =>
  service({
    url: `/enq/enqUser/saveTarget`,
    method: 'post',
    data
  }) //目标与结果保存
export const deleteTargetResult = data =>
  service({ url: `/enq/enqUser/deleteTargetResult`, method: 'delete', params: data }) //结果删除
export const deleteTarget = data => service({ url: `/enq/enqUser/deleteTarget`, method: 'delete', params: data }) //目标与结果删除
export const targetNextStep = data => service({ url: `/enq/enqUser/targetNextStep`, method: 'get', params: data }) //下一步再次校验
export const getPerformancData = data => service({ url: `/enq/enqUser/getPerformancData`, method: 'get', params: data }) //KPI数据上传查询
export const exportPerformancData = data =>
  service({
    url: `/enq/enqUser/exportPerformancData`,
    method: 'get',
    params: data,
    responseType: 'blob'
    // method: 'post',
    // data: qs.stringify(data),
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }) //KPI数据上传导出
export const readPerformancData = data =>
  service({
    url: `/enq/enqUser/readPerformancData`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  }) //KPI数据上传导入
export const updateEnqStatus = data =>
  service({
    url: `/enq/enqUser/updateEnqStatus`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }) //KPI数据上传导入
export const getObjectivesResults = data =>
  service({ url: `/enq/enqUser/getObjectivesResults`, method: 'get', params: data }) //目标结果数据上传查询
export const exportEnqUserInfo = data =>
  service({ url: `/enq/enqUser/exportEnqUserInfo`, method: 'get', params: data, responseType: 'blob' }) //导出参与个人盘点人员
export const exportObjectivesResults = data =>
  service({ url: `/enq/enqUser/exportObjectivesResults`, method: 'get', params: data, responseType: 'blob' }) //导出目标与结果列表
export const importObjectivesResults = data =>
  service({
    url: `/enq/enqUser/importObjectivesResults`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  }) //导入数据

// 协同网络
// 获取协同人员列表
export const getCoordinationUser = data =>
  service({ url: `/enq/enqUser/getCoordinationUser`, method: 'get', params: data })
export const deleteCoordinationUser = data =>
  service({
    url: `/enq/enqUser/deleteCoordinationUser`,
    method: 'post',
    data
  })
export const getCoordinationModeList = data =>
  service({ url: `/enq/enqUser/getCoordinationModeList`, method: 'get', params: data })
export const addUserCoordination = data =>
  service({
    url: `/enq/enqUser/addUserCoordination`,
    method: 'post',
    data
  })
export const coordinationType = data =>
  service({ url: `/enq/enqUser/getCoordinationType`, method: 'get', params: data })
export const addCoordType = data =>
  service({
    url: `/enq/enqUser/addCoordinationMode`,
    method: 'post',
    data
  })

// 个人盘点kpi评价
export const getUserKpiComment = data => service({ url: `/enq/enqUser/getUserKpiComment`, method: 'get', params: data }) //个人盘点-获取个人kpi评价
export const saveUserKpiComment = data =>
  service({
    url: `/enq/enqUser/saveUserKpiComment`,
    method: 'post',
    data
  }) //个人盘点-保存个人kpi评价

// *******************************************部门盘点****************
// 获取部门盘点信息
export const getOrgInfo = data => service({ url: `/enq/enqOrg/getOrgInfo`, method: 'get', params: data })

// 部门盘点点击下一步
export const departNextStep = data =>
  service({
    url: `/enq/enqOrg/departNextStep`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 获取部门盘点模块
export const getDeptEnqModule = data => service({ url: `/enq/enqOrg/getDeptEnqModule`, method: 'get', params: data })
// 部门职责
export const getOrgResp = data => service({ url: `/enq/enqOrg/getOrgResp`, method: 'get', params: data })
// 保存部门职责
export const saveOrgResp = data =>
  service({
    url: `/enq/enqOrg/saveOrgResp`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 获取部门职责

// 获取流程树
export const getProcessTree = data =>
  service({ url: `/enq/enqOrg/querynqOrgBizProcessTree`, method: 'get', params: data })
// 根据流程编码获取流程列表
export const getBizProcessByCode = data =>
  service({ url: `/enq/enqOrg/getBizProcessByCode`, method: 'get', params: data })
//  保存部门主要流程
export const saveEnqOrgBizProcess = data =>
  service({
    url: `/enq/enqOrg/saveEnqOrgBizProcess`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 获取部门岗位信息&&岗位编制与需求
export const getPostInfo = data =>
  service({ url: `/enq/enqOrg/${data.orgCode}/queryAllEnqPostInfoAndPost`, method: 'get', params: data })
// 修改部门岗位信息
export const updateEnqPostInfo = data =>
  service({
    url: `/enq/enqOrg/updateEnqPostInfo`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 获取岗位参与流程
export const getPostProcess = data => service({ url: `/enq/enqOrg/queryPostProcess`, method: 'get', params: data })
// 保存岗位参与流程
export const saveEnqBizProcess = data =>
  service({
    url: `/enq/enqOrg/saveEnqBizProcess`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

export const getenqOrgKpiCycle = data => service({ url: `/enq/enqOrg/getKpiCycle`, method: 'get', params: data })
export const queryAllEnqOrgKpi = data => service({ url: `/enq/enqOrg/queryAllEnqOrgKpi`, method: 'get', params: data })
export const updateEnqOrgKpi = data =>
  service({
    url: `/enq/enqOrg/updateEnqOrgKpi`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 修改岗位编制与需求
export const updateEnqPostDemand = data =>
  service({
    url: `/enq/enqOrg/updateEnqPostDemand`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 查询员工
export const getDeptUserPost = data => service({ url: `/enq/enqOrg/queryDeptUserPost`, method: 'get', params: data })
// 查询员工工作活动
export const getEnqOrgUserActivity = data =>
  service({ url: `/enq/enqUser/enqOrg/getEnqUserActivity`, method: 'get', params: data })
// 保存工作活动
export const confirmEnqUserActivity = data =>
  service({
    url: `/enq/enqUser/confirmEnqUserActivity`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 人员评价

// 查询个人评价
export const getUserEval = data =>
  service({ url: `/enq/enqOrg/queryEnqUserEvaluationByUserId`, method: 'get', params: data })
// 保存个人评价
export const saveUserEval = data =>
  service({
    url: `/enq/enqOrg/saveOneOrAllEnqUserEvaluation`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 查询所有岗位
export const getPostByLikeName = data => service({ url: `/enq/enqUser/getPostByLikeName`, method: 'get', params: data })
// 核心能力&业绩表现人员
export const kpiCapablityInterval = data =>
  service({ url: `/enq/enqOrg/kpiCapablityInterval`, method: 'get', params: data })
// 离职风险
export const retentionInterval = data => service({ url: `/enq/enqOrg/retentionInterval`, method: 'get', params: data })
// 发展潜力
export const developmentCapabilityInterval = data =>
  service({ url: `/enq/enqOrg/developmentCapabilityInterval`, method: 'get', params: data })

// 人员校准
export const queryPerCorrect = data =>
  service({ url: `/enq/enqOrg/queryKpiDevelopmentPotential`, method: 'get', params: data })
// 修改人员校准
export const updateCapaKpiDevelopment = data =>
  service({
    url: `/enq/enqOrg/updateCapaKpiDevelopment`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 查询部门培训计划
export const getAllEnqOrgTraining = data =>
  service({ url: `/enq/enqOrg/queryAllEnqOrgTraining`, method: 'get', params: data })
// 保存部门培训计划
export const saveOrgTrainging = data =>
  service({
    url: `/enq/enqOrg/saveOrgTrainging`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 删除培训计划
export const deleteTraining = data => service({ url: `/enq/enqOrg/deleteTraining`, method: 'delete', params: data })

// 部门盘点提交
export const submitOrgInfo = data =>
  service({
    url: `/enq/enqOrg/syncOrgInfo`,
    method: 'post',
    data
  })
// 部门盘点提交12.2新增
export const syncSupInfo = data =>
  service({
    url: `/enq/enqOrg/syncSupInfo`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//-------盘点进度与质量------
// 盘点进度管理列表
export const getScheduleList = data => service({ url: `/enq/schedule/list`, method: 'get', params: data })
// 盘点部门进度-已完成/未完成
export const queryTimeSpent = data => service({ url: `/enq/schedule/queryTimeSpent`, method: 'get', params: data })
// 盘点部门进度-未开始
export const queryNotStarted = data => service({ url: `/enq/schedule/queryNotStarted`, method: 'get', params: data })
// 部门整体进度
export const getOrgSchedule = data => service({ url: `/enq/schedule/orgSchedule`, method: 'get', params: data })
// 进度导出
export const orgExportData = data =>
  service({
    url: `/enq/schedule/exportData`,
    method: 'get',
    params: data,
    responseType: 'blob'
    // method: 'post',
    // data: qs.stringify(data),
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 部门人员评价
export const orgUserEvaluate = data => service({ url: `/enq/schedule/userEvaluate`, method: 'get', params: data })
// 上级盘点进度-查询
export const superiorSchedule = data => service({ url: `/enq/schedule/superiorSchedule`, method: 'get', params: data })

// 未登录人员
export const getUnLoginSchedule = data => service({ url: `/enq/schedule/unLoginSchedule`, method: 'get', params: data })
// 已完成人员/未完成人员
export const getUserSchedule = data => service({ url: `/enq/schedule/userSchedule`, method: 'get', params: data })
// 获取当前盘点下的部门级别
export const getOrgLayNo = data => service({ url: `/enq/enqOrg/getOrgLayNo`, method: 'get', params: data })

//部门整体质量
export const getOrgQuality = data => service({ url: `/quality/orgQuality`, method: 'get', params: data })
//部门质量明细
export const getOrgDetails = data => service({ url: `/quality/orgDetails`, method: 'get', params: data })
// 个人质量明细
export const getUserDetails = data => service({ url: `/quality/userDetails`, method: 'get', params: data })

//部门盘点2021/12/2
export const requirementsModuleList = data =>
  service({ url: `/enq/enqOrg/requirementsModuleList`, method: 'get', params: data }) //职位要求
export const jobValueModuleList = data =>
  service({ url: `/enq/enqOrg/jobValueModuleList`, method: 'get', params: data }) //职位价值
export const potentialEvalModuleList = data =>
  service({ url: `/enq/enqOrg/potentialEvalModuleList`, method: 'get', params: data }) //潜力评价
export const jobRequirementsList = data =>
  service({ url: `/enq/enqOrg/jobRequirementsList`, method: 'get', params: data }) //职位要求、职位价值查题目
export const potentialEvalList = data => service({ url: `/enq/enqOrg/potentialEvalList`, method: 'get', params: data }) //潜力评价查题目
export const saveJobRequirement = data =>
  service({
    url: `/enq/enqOrg/saveJobRequirement`,
    method: 'post',
    data
  }) //保存题目
export const jobNext = data => service({ url: `/enq/enqOrg/jobNext`, method: 'get', params: data }) //职位要求、职位价值下一步判断
export const qualityEvalList = data => service({ url: `/enq/enqOrg/qualityEvalList`, method: 'get', params: data }) //素质评价查询
export const getComment = data => service({ url: `/enq/enqOrg/getComment`, method: 'get', params: data }) //素质评价查询他人评语
//核心素质
export const coreQualityEvalList = data =>
  service({
    url: `/enq/enqOrg/coreQualityEvalList`,
    method: 'get',
    params: data
  })
export const exportQualityEvalList = data =>
  service({ url: `/enq/enqOrg/exportQualityEvalList`, method: 'get', params: data, responseType: 'blob' }) //素质评价列表导出
export const performanceEvalList = data =>
  service({ url: `/enq/enqOrg/performanceEvalList`, method: 'get', params: data }) //业绩评价查询
export const saveQualityEval = data =>
  service({
    url: `/enq/enqOrg/saveQualityEval`,
    method: 'post',
    data
  }) //素质评价、业绩评价、潜力模块保存
export const potentialList = data => service({ url: `/enq/enqOrg/potentialList`, method: 'get', params: data }) //潜力评价下的潜力模块查询
export const potentialLevelScore = data =>
  service({
    url: `/enq/enqOrg/potentialLevelScore`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  }) //潜力评价下的综合得分计算
export const potentialNext = data => service({ url: `/enq/enqOrg/potentialNext`, method: 'get', params: data }) //潜力评价下一步
export const retentionRiskList = data => service({ url: `/enq/enqOrg/retentionRiskList`, method: 'get', params: data }) //离职风险查询
export const retentionRiskConfirm = data =>
  service({
    url: `/enq/enqOrg/retentionRiskConfirm`,
    method: 'post',
    data
  }) //离职风险保存
export const postJobEvalList = data => service({ url: `/enq/enqOrg/postJobEvalList`, method: 'get', params: data }) //岗位评价查询
export const postJobEvalConfirm = data =>
  service({
    url: `/enq/enqOrg/postJobEvalConfirm`,
    method: 'post',
    data
  }) //岗位评价保存
export const getDirectSubordinates = data =>
  service({ url: `/enq/enqOrg/getDirectSubordinates`, method: 'get', params: data }) //个人工作侧边栏
export const personalJobEval = data => service({ url: `/enq/enqOrg/personalJobEval`, method: 'get', params: data }) //个人工作查询
export const postJobNext = data => service({ url: `/enq/enqOrg/postJobNext`, method: 'get', params: data }) //工作饱和度下一步
export const getPersonnelDevelopment = data =>
  service({ url: `/enq/enqOrg/getPersonnelDevelopment`, method: 'get', params: data }) //人员发展信息查询
export const getIdealJob = data => service({ url: `/enq/enqOrg/getIdealJob`, method: 'get', params: data }) //查职位
export const getJobList = data => service({ url: `/enq/enqOrg/getJobList`, method: 'get', params: data }) //查职位名称
export const trainingPlanList = data => service({ url: `/enq/enqOrg/trainingPlanList`, method: 'get', params: data }) //培训计划查询
export const delTraining = data => service({ url: `/enq/enqOrg/delTraining`, method: 'delete', params: data }) //培训计划删除
export const trainingPlanAdd = data =>
  service({
    url: `/enq/enqOrg/trainingPlanAdd`,
    method: 'post',
    data
  }) //培训计划保存
export const personnelDevelopmentConfirm = data =>
  service({
    url: `/enq/enqOrg/personnelDevelopmentConfirm`,
    method: 'post',
    data
  }) //保存人员发展
export const personnelDevNext = data => service({ url: `/enq/enqOrg/personnelDevNext`, method: 'get', params: data }) //人员发展下一步
export const nextStep = data =>
  service({
    url: `/enq/enqOrg/nextStep`,
    method: 'put',
    data
  }) //部门盘点全局下一步
export const getObjectiveResult = data =>
  service({ url: `/enq/enqOrg/getObjectiveResult`, method: 'get', params: data }) //部门盘点目标与结果
export const saveObjectiveResult = data =>
  service({
    url: `/enq/enqOrg/saveObjectiveResult`,
    method: 'post',
    data
  }) //部门盘点目标与结果保存
export const nextObjectiveResult = data =>
  service({ url: `/enq/enqOrg/nextObjectiveResult`, method: 'get', params: data }) //部门盘点目标与结果下一步
export const getObjectiveKpi = data => service({ url: `/enq/enqOrg/getObjectiveKpi`, method: 'get', params: data }) //部门盘点-kpi评价-kpl展示
export const saveObjectiveKpi = data =>
  service({
    url: `/enq/enqOrg/saveObjectiveKpi`,
    method: 'post',
    data
  }) //部门盘点-kpi评价-保存kpi

// ***********************人才盘点报告
// 获取项目视图列表
export const pageProject = data => service({ url: `/orgReport/pageProject`, method: 'get', params: data })
// 部门报告列表
export const queryOrgReportList = data => service({ url: `/orgReport/queryOrgReportList`, method: 'get', params: data })
// 个人报告列表
export const queryUserReportList = data =>
  service({ url: `/orgReport/queryUserReportList`, method: 'get', params: data })

// ****************个人报告***********
export const getPersonalModule = data => service({ url: `/userReport/getPersonalModule`, method: 'get', params: data })
// 盘点报告tab导航栏
export const getPersonalModuleInfo = data =>
  service({ url: `/enq/enqInfo/getEnqModuleInfo`, method: 'get', params: data })

// 个人组织及岗位
export const getUserReportUserInfo = data =>
  service({ url: `/userReport/queryOrgOfPostAndUserNumber`, method: 'get', params: data })
// 基本信息
export const getUserReportBasicInfo = data => service({ url: `/userReport/basicInfo`, method: 'get', params: data })
// 承接职责
export const getUserReportRespList = data => service({ url: `/userReport/queryRespList`, method: 'get', params: data })
// 工作活动
export const getUserActivitys = data => service({ url: `/userReport/queryUserActivitys`, method: 'get', params: data })
// 教育信息
export const getUserEducations = data =>
  service({ url: `/userReport/queryUserEducations`, method: 'get', params: data })
// 工作履历
export const getUserExperiences = data =>
  service({ url: `/userReport/queryUserExperiences`, method: 'get', params: data })
// 人才评价
export const getTalentEvaluation = data =>
  service({ url: `/userReport/queryTalentEvaluation`, method: 'get', params: data })
// 个人规划
export const getPersonalPlanning = data =>
  service({ url: `/userReport/queryPersonalPlanning`, method: 'get', params: data })
// 绩效信息
export const getUserCompreKpiReport = data =>
  service({ url: `/userReport/getUserCompreKpiReport`, method: 'get', params: data })
// 指标信息
export const getUserCompreKpiChart = data =>
  service({ url: `/userReport/getUserCompreKpiChart`, method: 'get', params: data })

// 素质评价getOrgReportOrgInfo
// 个人报告-个人规划
export const getPersonnelPlan = data =>
  service({ url: `/enq/enqReport/getPersonnelDevelopment`, method: 'get', params: data })
// 素质评价图
export const getQualityEvalData = data =>
  service({ url: `/enq/enqReport/getUserQualityEval`, method: 'get', params: data })
// 个人报告-业绩评价
export const getUserAchievementEval = data =>
  service({ url: `/enq/enqReport/getUserAchievementEval`, method: 'get', params: data })
// 个人报告-目标结果
export const getKeyResults = data => service({ url: `/enq/enqReport/getKeyResults`, method: 'get', params: data })
// 个人报告-KPI评价
export const getKpiEvalData = data => service({ url: `/enq/enqReport/getKpiEval`, method: 'get', params: data })
// 个人报告-离职风险
export const getRetentionRisk = data => service({ url: `/enq/enqReport/getRetentionRisk`, method: 'get', params: data })
// 个人报告-工作驱动
export const getWorkDrive = data => service({ url: `/enq/enqReport/getWorkDrive`, method: 'get', params: data })
// 个人激励因素
export const getJobIncentives = data => service({ url: `/enq/enqReport/getJobIncentives`, method: 'get', params: data })

// 个人报告-人才分类
export const getTalentClassification = data =>
  service({ url: `/enq/enqReport/getTalentClassification`, method: 'get', params: data })
// 个人报告-敬业度
export const getEngagement = data => service({ url: `/enq/enqReport/getEngagement`, method: 'get', params: data })

// ****************组织报告***********
export const getOrgModule = data => service({ url: `/orgReport/getOrgModule`, method: 'get', params: data })
// 获取部门报告module
export const getEnqModuleInfo = data => service({ url: `/enq/enqInfo/getEnqModuleInfo`, method: 'get', params: data })
// 部门岗位及人员数量
export const getOrgReportOrgInfo = data =>
  service({ url: `/orgReport/queryOrgOfPostAndUserNumber`, method: 'get', params: data })
// 基本信息
export const getOrgReportBasicInfo = data => service({ url: `/orgReport/queryBasicInfo`, method: 'get', params: data })
// 负责人信息
export const getEnqOrgLeaderInfo = data =>
  service({ url: `/orgReport/getEnqOrgLeaderInfo`, method: 'get', params: data })

// 职责分配
export const getOrgReportResp = data => service({ url: `/orgReport/queryOrgResp`, method: 'get', params: data })
// 参与流程
export const getPartakeProcess = data => service({ url: `/orgReport/queryPartakeProcess`, method: 'get', params: data })
// 部门指标
export const queryOrgKpi = data => service({ url: `/orgReport/queryOrgKpi`, method: 'get', params: data })
export const queryOrgUserKpi = data => service({ url: `/orgReport/queryOrgUserKpi`, method: 'get', params: data })

// 人员评价
export const queryUserEvaluate = data => service({ url: `/orgReport/queryUserEvaluate`, method: 'get', params: data })
// 晋升可能
export const queryUserDevelopment = data =>
  service({ url: `/orgReport/queryUserDevelopment`, method: 'get', params: data })

//*** 敬业分析
export const jfAnalysisPage = data => service({ url: `/orgReport/jfAnalysisPage`, method: 'get', params: data })
export const getDeptEngagement = data =>
  service({ url: `/enq/enqReport/getDeptEngagement`, method: 'get', params: data })

// 部门报告新增

//***** 素质评价
export const getQualityEval = data => service({ url: `/enq/enqReport/getQualityEval`, method: 'get', params: data })
// 综合得分
export const getQualityEvalOverallScore = data =>
  service({ url: `/enq/enqReport/getQualityEvalOverallScore`, method: 'get', params: data })
// 360评价
export const getQualityEvalWhole = data =>
  service({ url: `/enq/enqReport/getQualityEvalWhole`, method: 'get', params: data })
// 分级情况
export const getQualityEvalClass = data =>
  service({ url: `/enq/enqReport/getQualityEvalClass`, method: 'get', params: data })
// 得分与评级
export const getQualityEvalUserScore = data =>
  service({ url: `/enq/enqReport/getQualityEvalUserScore`, method: 'get', params: data })
// 360得分
export const getQualityEvalUserWhole = data =>
  service({ url: `/enq/enqReport/getQualityEvalUserWhole`, method: 'get', params: data })
// 人员素质评价评语
export const getQualityEvalUserComment = data =>
  service({ url: `/enq/enqReport/getQualityEvalUserComment`, method: 'get', params: data })
// 素质评价人员定位
export const getUserdistribution = data =>
  service({ url: `/enq/enqReport/getQualityEvalUserdistribution`, method: 'get', params: data })

// ***** 业绩评价
export const getAchievementEval = data =>
  service({ url: `/enq/enqReport/getAchievementEval`, method: 'get', params: data })
// 详情列表
export const getAchievementUser = data =>
  service({ url: `/enq/enqReport/getAchievementUser`, method: 'get', params: data })
// ***** 目标与结果
export const getObjectivesResultsList = data =>
  service({ url: `/enq/enqReport/getObjectivesResults`, method: 'get', params: data })
// 目标详情
export const getEnqObjective = data => service({ url: `/enq/enqReport/getEnqObjective`, method: 'get', params: data })
// 关键结果
export const getEnqObjectiveResult = data =>
  service({ url: `/enq/enqReport/getEnqObjectiveResult`, method: 'get', params: data })
// ***** KPI评价
export const getKPIEvaluate = data => service({ url: `/enq/enqReport/getKPIEvaluate`, method: 'get', params: data })
// KPI指标信息
export const getKPIIndicatorInformation = data =>
  service({ url: `/enq/enqReport/getKPIIndicatorInformation`, method: 'get', params: data })
// 组织KPI指标详情
export const getKPIIndicatorDetails = data =>
  service({ url: `/enq/enqReport/getKPIIndicatorDetails`, method: 'get', params: data })
// 组织业绩评价各维度得分
export const getAchievementOverallScore = data =>
  service({ url: `/enq/enqReport/getAchievementOverallScore`, method: 'get', params: data })
// 组织业绩评价评价分级情况
export const getAchievementClass = data =>
  service({ url: `/enq/enqReport/getAchievementClass`, method: 'get', params: data })
// 组织业绩评价360
export const getAchievementWhole = data =>
  service({ url: `/enq/enqReport/getAchievementWhole`, method: 'get', params: data })
// 业绩评价人员定位分布
export const getAchievementUserdistribution = data =>
  service({ url: `/enq/enqReport/getAchievementUserdistribution`, method: 'get', params: data })
// 人员业绩评价得分详情
export const getAchievementUserScore = data =>
  service({ url: `/enq/enqReport/getAchievementUserScore`, method: 'get', params: data })
// 业绩评价人员评价360°得分
export const getAchievementUserWhole = data =>
  service({ url: `/enq/enqReport/getAchievementUserWhole`, method: 'get', params: data })

// 人员潜力-人员潜力图
export const getPersonnelPotential = data =>
  service({ url: `/enq/enqReport/getPersonnelPotential`, method: 'get', params: data })
// 人员潜力-人员潜力详情
export const getPotentialDetails = data =>
  service({ url: `/enq/enqReport/getPotentialDetails`, method: 'get', params: data })
// 人员潜力-潜力评价的整体表现
export const getPotentialUserdist = data =>
  service({ url: `/enq/enqReport/getPotentialUserdistribution`, method: 'get', params: data })

// 人员晋升结构
// export const expectationCycle = p => $http.get(`${apiServerUrl}/enqReport/expectationCycle`, p);
// 人才区分-人才区分图
export const getTalentDifferentiation = data =>
  service({ url: `/enq/enqReport/getTalentDifferentiation`, method: 'get', params: data })
// 人才区分-直接下级组织人才分类
export const getSubOrgTalentType = data =>
  service({ url: `/enq/enqReport/getSubOrgTalentType`, method: 'get', params: data })
// 人才区分-人才分类详情
export const talentClassificationDetails = data =>
  service({ url: `/enq/enqReport/talentClassificationDetails`, method: 'get', params: data })

// 离职风险-离职风险图
export const getOrgRetentionRisk = data =>
  service({ url: `/enq/enqReport/getOrgRetentionRisk`, method: 'get', params: data })
// 离职风险-各组织人员潜在离职原因
export const getOrgRetentionRiskList = data =>
  service({ url: `/enq/enqReport/getOrgRetentionRiskList`, method: 'get', params: data })
// 离职风险-各职位人员潜在离职原因
export const getJobRetentionRiskList = data =>
  service({ url: `/enq/enqReport/getJobRetentionRiskList`, method: 'get', params: data })
// 离职风险-人才离职潜在原因
export const getUserRetentionRiskList = data =>
  service({ url: `/enq/enqReport/getUserRetentionRiskList`, method: 'get', params: data })
// 离职风险-人才离职风险详情
export const getRetentionRiskList = data =>
  service({ url: `/enq/enqReport/retentionRiskList`, method: 'get', params: data })

// 人才继任-人才继任图
export const getTalentSuccession = data =>
  service({ url: `/enq/enqReport/getTalentSuccession`, method: 'get', params: data })
// 人才继任-岗位继任详情
export const getPostSuccessionList = data =>
  service({ url: `/enq/enqReport/getPostSuccessionList`, method: 'get', params: data })
// 人才继任-人才继任详情
export const getTalentSuccessionList = data =>
  service({ url: `/enq/enqReport/getTalentSuccessionList`, method: 'get', params: data })
// 组织继任详情
export const getOrgSuccessionList = data =>
  service({ url: `/enq/enqReport/getOrgSuccessionList`, method: 'get', params: data })

// 部门盘点-人才发展
export const orgTalentDevelopment = data =>
  service({ url: `/enq/enqReport/orgTalentDevelopment`, method: 'get', params: data })
// 人才发展-个人发展个人总结
export const userDevelopment = data => service({ url: `/enq/enqReport/userDevelopment`, method: 'get', params: data })
// 人才发展-个人发展上级评价
export const supDevelopment = data => service({ url: `/enq/enqReport/supDevelopment`, method: 'get', params: data })

//部门盘点-职位招募
export const jobRecruitment = data => service({ url: `/enq/enqReport/jobRecruitment`, method: 'get', params: data })
// 职位需求详情
export const jobRequirementsDetails = data =>
  service({ url: `/enq/enqReport/jobRequirementsDetails`, method: 'get', params: data })

//部门报告-岗位编制列表
// export const establishmentDetails = p => $http.get(`${apiServerUrl}/enqReport/establishmentDetails`, p);
//部门报告-敬业度
export const getOrgEngagement = data => service({ url: `/enq/enqReport/getOrgEngagement`, method: 'get', params: data })
// 部门报告-敬业度-组织与人员敬业度分析
export const getOrgUserEngagement = data =>
  service({ url: `/enq/enqReport/getOrgUserEngagement`, method: 'get', params: data })
// 工作驱动-工作驱动图
export const getWorkDriven = data => service({ url: `/enq/enqReport/getWorkDriven`, method: 'get', params: data })
// 工作驱动-个人激励因素
export const getUserIncentiveFactors = data =>
  service({ url: `/enq/enqReport/getUserIncentiveFactors`, method: 'get', params: data })
// 工作驱动 组织激励因素
export const getOrgIncentiveFactors = data =>
  service({ url: `/enq/enqReport/getOrgIncentiveFactors`, method: 'get', params: data })

// 部门报告-敬业度
// 各类人才满意度
export const getOrgTalentDrivers = data =>
  service({ url: `/enq/enqReport/getOrgTalentDrivers`, method: 'get', params: data })
// 各类人才满意度详情
export const getTalentDrivers = data => service({ url: `/enq/enqReport/getTalentDrivers`, method: 'get', params: data })
// 敬业度提升改善建议
export const getSuggestionsImprovement = data =>
  service({ url: `/enq/enqReport/getSuggestionsImprovement`, method: 'get', params: data })
// 人才满意度详情
export const getPersonnelTalentDrivers = data =>
  service({ url: `/enq/enqReport/getPersonnelTalentDrivers`, method: 'get', params: data })

// 盘点数据-------
// 盘点数据列表
export const getReviewDataList = data => service({ url: `/enq/enqInfo/page`, method: 'get', params: data })
// 数据确认-获取数据查看要素
// export const getReviewDataModule = p => $http.get(`${apiServerUrl}/schedule/getPersonModule`, p);
export const getReviewDataModule = data => service({ url: `/schedule/getEnqModule`, method: 'get', params: data })
// 数据确认-个人盘点数据-目标与结果
export const getPersonalObjective = data =>
  service({ url: `/schedule/getPersonalObjective`, method: 'get', params: data })
// 数据确认-部门盘点数据-目标与结果
export const getSuperiorObjective = data =>
  service({ url: `/schedule/getSuperiorObjective`, method: 'get', params: data })
// 数据确认-导出
export const exportDataConfirm = data =>
  service({
    url: `/schedule/exportDataConfirm`,
    method: 'get',
    params: data,
    responseType: 'blob'
    // method: 'post',
    // data: qs.stringify(data),
    // headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
//导出
export const exportListDownload = data =>
  service({ url: `/entp/attach/export/download`, method: 'get', params: data, responseType: 'blob' })
// 数据确认-个人盘点数据-KPI数据
export const getPersonalKpi = data => service({ url: `/schedule/getPersonalKpi`, method: 'get', params: data })
// 数据确认-部门盘点数据-KPI数据
export const getSuperiorKpi = data => service({ url: `/schedule/getSuperiorKpi`, method: 'get', params: data })

// 数据确认-素质评价、业绩评价查询接口
export const getUserObjectiveList = data =>
  service({ url: `/schedule/getUserObjectiveList`, method: 'get', params: data })
// 数据确认-素质评价、业绩评价-作废
export const toVoid = data =>
  service({
    url: `/schedule/toVoid`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//*************************************************************** 人才盘点分析
// 盘点列表
export const enqAnalysisList = data => service({ url: `/enq/enqInfo`, method: 'get', params: data })
// 组织树
export const queryOrgWithSubChildren = data =>
  service({ url: `/enq/enqOrg/queryOrgWithSubChildren`, method: 'get', params: data })
// 职务类型
export const queryJobClassByOrg = data =>
  service({ url: `/enq/enqOrg/queryJobClassByOrg`, method: 'get', params: data })

// ********* 部门盘点报告更新版****
// 人员数量与结构
export const orgPersonnelNum = data => service({ url: `/enq/enqReport/personnelNum`, method: 'get', params: data })
// 新进人员&流失人员
export const orgPersonnelChanges = data =>
  service({ url: `/enq/enqReport/personnelChanges`, method: 'get', params: data })
// 人员晋升结构
export const orgExpectationCycle = data =>
  service({ url: `/enq/enqReport/expectationCycle`, method: 'get', params: data })
// 晋升期望详情
export const orgExpectedDetails = data =>
  service({ url: `/enq/enqReport/expectedDetails`, method: 'get', params: data })
// 人才区分
export const orgTalentDiff = data =>
  service({ url: `/enq/enqReport/talentDifferentiation`, method: 'get', params: data })
// 人才区分矩阵
export const orgTalentDiffMatrix = data => service({ url: `/enq/enqReport/getMatrix`, method: 'get', params: data })
// 人才分类详情
export const orgTalentDiffDetail = data => service({ url: `/enq/enqReport/talentDiff`, method: 'get', params: data })
// 人才风险
export const orgRetentionRisk = data => service({ url: `/enq/enqReport/retentionRisk`, method: 'get', params: data })
// 离职风险矩阵
export const orgRetentionMatrix = data =>
  service({ url: `/enq/enqReport/retentionMatrix`, method: 'get', params: data })
// 离职风险详情
export const orgRiskDetails = data => service({ url: `/enq/enqReport/riskDetails`, method: 'get', params: data })
// 离职人员可替代性
export const orgSubstitutability = data =>
  service({ url: `/enq/enqReport/substitutability`, method: 'get', params: data })
// 人才发展
export const orgTalentDeve = data => service({ url: `/enq/enqReport/talentDevelopment`, method: 'get', params: data })
// 列表
export const orgPersonalDeve = data =>
  service({ url: `/enq/enqReport/personalDevelopment`, method: 'get', params: data })
// 人才发展矩阵
export const performanceMatrix = data =>
  service({ url: `/enq/enqReport/performanceMatrix`, method: 'get', params: data })
export const potentialMatrix = data => service({ url: `/enq/enqReport/potentialMatrix`, method: 'get', params: data })
// 人才编制
export const orgStaffEstab = data => service({ url: `/enq/enqReport/staffEstablishing`, method: 'get', params: data })
// 列表
export const orgStaffEstabList = data =>
  service({ url: `/enq/enqReport/establishmentDetails`, method: 'get', params: data })
// 培训计划
export const orgTrain = data => service({ url: `/enq/enqReport/plan`, method: 'get', params: data })
// 培训计划列表
export const orgTrainList = data => service({ url: `/enq/enqReport/planDetails`, method: 'get', params: data })
// 敬业度分析
export const engagementAnalysis = data => service({ url: `/orgReport/engagementAnalysis`, method: 'get', params: data })

// 人才盘点分析列表数据导出
export const exportData = data =>
  service({
    url: `/enqAnalysis/quantity/exportData`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// ******************人才数量相关接口
// 人员特征
export const characteristics = data =>
  service({ url: `/enqAnalysis/quantity/characteristics`, method: 'get', params: data })
// 组织分布
export const orgDistribution = data => service({ url: `/enqAnalysis/quantity/org`, method: 'get', params: data })
// 组织岗位分布明细
export const queryOrgList = data => service({ url: `/enqAnalysis/quantity/queryOrgList`, method: 'get', params: data })
// 岗位分布
export const postDistribution = data => service({ url: `/enqAnalysis/quantity/post`, method: 'get', params: data })
// 岗位分布明细
export const queryPostList = data =>
  service({ url: `/enqAnalysis/quantity/queryPostList`, method: 'get', params: data })
// 岗位工作
export const postWork = data => service({ url: `/enqAnalysis/quantity/postWork`, method: 'get', params: data })
// 岗位工作明细
export const queryPostWorkList = data =>
  service({ url: `/enqAnalysis/quantity/queryPostWorkList`, method: 'get', params: data })
// 教育经历
export const education = data => service({ url: `/enqAnalysis/quantity/education`, method: 'get', params: data })
// 教育经历明细
export const queryEduList = data =>
  service({ url: `/enqAnalysis/quantity/queryEducationalList`, method: 'get', params: data })

// 工作经历
export const workExperience = data => service({ url: `/enqAnalysis/quantity/experience`, method: 'get', params: data })
// 工作经历分布明细
export const queryWorkExpList = data =>
  service({ url: `/enqAnalysis/quantity/queryWorkExperienceList`, method: 'get', params: data })

// 地理分布
export const regionDistribution = data => service({ url: `/enqAnalysis/quantity/region`, method: 'get', params: data })

// *******************人才结构相关接口
// 岗位结构
export const postStructure = data => service({ url: `/enqAnalysis/structure/post`, method: 'get', params: data })
export const queryPostIcon = data =>
  service({ url: `/enqAnalysis/structure/queryPostIcon`, method: 'get', params: data })
// 岗位结构列表
export const postList = data => service({ url: `/enqAnalysis/structure/PostList`, method: 'get', params: data })
// 工作年限结构
export const workAgeStructure = data => service({ url: `/enqAnalysis/structure/workAge`, method: 'get', params: data })
// 工作年限结构列表
export const workingYearsList = data =>
  service({ url: `/enqAnalysis/structure/WorkingYearsList`, method: 'get', params: data })
// 晋升结构
export const promotionStructure = data =>
  service({ url: `/enqAnalysis/structure/promotion`, method: 'get', params: data })
export const PromotionCycle = data =>
  service({ url: `/enqAnalysis/structure/PromotionCycle`, method: 'get', params: data })
// 岗位重要性
export const postImportance = data => service({ url: `/enqAnalysis/structure/importance`, method: 'get', params: data })
// 任职岗位-工作经验
export const getExperience = data => service({ url: `/enqAnalysis/structure/experience`, method: 'get', params: data })
// 工作经验-岗位列表
export const experiencePost = data =>
  service({ url: `/enqAnalysis/structure/experiencePost`, method: 'get', params: data })
export const queryJobQualificationsList = data =>
  service({ url: `/enqAnalysis/quantity/queryJobQualificationsList`, method: 'get', params: data })
// 沟通
export const communication = data =>
  service({ url: `/enqAnalysis/structure/communication`, method: 'get', params: data })
// 沟通-岗位列表
export const communicationPost = data =>
  service({ url: `/enqAnalysis/structure/communicationPost`, method: 'get', params: data })
export const queryCommunicateList = data =>
  service({ url: `/enqAnalysis/quantity/queryCommunicateList`, method: 'get', params: data })
// 工作环境
export const workAmbient = data => service({ url: `/enqAnalysis/structure/environment`, method: 'get', params: data })
// 工作环境-岗位列表
export const environmentPost = data =>
  service({ url: `/enqAnalysis/structure/environmentPost`, method: 'get', params: data })
export const queryEnvironmentList = data =>
  service({ url: `/enqAnalysis/quantity/queryEnvironmentList`, method: 'get', params: data })

// *********************人才质量相关接口
// 能力业绩矩阵
export const competence = data => service({ url: `/enqAnalysis/quality/competence`, method: 'get', params: data })
// 能力潜力矩阵
export const potential = data => service({ url: `/enqAnalysis/quality/potential`, method: 'get', params: data })
// 共通部门分布接口
export const qualityAllOrgDist = data => service({ url: `/enqAnalysis/views`, method: 'get', params: data })
// 能力潜力矩阵人员弹窗
export const potentialMembers = data =>
  service({ url: `/enqAnalysis/quality/potentialMembers`, method: 'get', params: data })
// 人才质量列表
export const queryCompetenceList = data =>
  service({ url: `/enqAnalysis/quantity/queryCompetenceList`, method: 'get', params: data })

// **********************人才编制相关接口
// 人才编制
export const establish = data => service({ url: `/enqAnalysis/establish`, method: 'get', params: data })
// 人才编制列表
export const establishList = data =>
  service({ url: `/enqAnalysis/quantity/queryEstablishmentList`, method: 'get', params: data })
// 人才缺口
export const talentGap = data => service({ url: `/enqAnalysis/establish/gap`, method: 'get', params: data })
// 人才缺口列表
export const talentGapList = data =>
  service({ url: `/enqAnalysis/quantity/queryTalentGapList`, method: 'get', params: data })
// 人才流动
// export const talentFlow = p => $http.get(`${apiServerUrl}/enqAnalysis/establish/flow`, p);
export const talentFlow = data => service({ url: `/enqAnalysis/quantity/talentFlow`, method: 'get', params: data })
// 人才流动列表
export const talentFlowList = data =>
  service({ url: `/enqAnalysis/quantity/queryTalentFlowList`, method: 'get', params: data })
// 人才离职
export const talentQuit = data => service({ url: `/enqAnalysis/establish/quit`, method: 'get', params: data })

// ***********************人才效率
// 工作活动整体分析 - 活动类型
export const efficiencyActivity = data =>
  service({ url: `/enqAnalysis/efficiency/activity`, method: 'get', params: data })
// 部门活动对比
export const orgActivityCompare = data =>
  service({ url: `/enqAnalysis/efficiency/orgActivityCompare`, method: 'get', params: data })
// 岗位活动对比
export const postActivityCompare = data =>
  service({ url: `/enqAnalysis/efficiency/postActivityCompare`, method: 'get', params: data })
// 流程岗位分布
export const processDistribute = data =>
  service({ url: `/enqAnalysis/efficiency/processDistribute`, method: 'get', params: data })

// ***********************人才风险
export const retentionRiskChart = data =>
  service({ url: `/enqAnalysis/quantity/retentionRisk`, method: 'get', params: data })
// 离职风险矩阵
export const retentionMatrix = data =>
  service({ url: `/enqAnalysis/risk/retentionMatrix`, method: 'get', params: data })
// 离职风险列表
export const retentionMatrixList = data =>
  service({ url: `/enqAnalysis/quantity/queryRetentionRiskList`, method: 'get', params: data })

// ************************人员优化
// 部门岗位分布列表
export const queryPostDistributionList = data =>
  service({ url: `/enqAnalysis/quantity/queryPostDistributionList`, method: 'get', params: data })
// 人员优化建议
export const staffDistribute = data =>
  service({ url: `/enqAnalysis/optimize/staffDistribute`, method: 'get', params: data })
// 人员补充建议
export const staffSupply = data => service({ url: `/enqAnalysis/optimize/staffSupply`, method: 'get', params: data })
// 人员补充建议（表格数据）

export const postDistribute = data =>
  service({ url: `/enqAnalysis/optimize/postDistribute`, method: 'get', params: data })
// -------人才盘点-盘点模型----
// 创建模型
export const creatModel = data =>
  service({
    url: `/enq/model/creatModel`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 查询模型
export const selectModel = data => service({ url: `/model/selectModel`, method: 'get', params: data })
// 获取模型列表
export const getModelList = data => service({ url: `/model/getModelList`, method: 'get', params: data })
// 编辑模型
export const editModel = data =>
  service({
    url: `/model/editModel`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 获取模型信息
export const getCurModelInfo = data => service({ url: `/model/getModelInfo`, method: 'get', params: data })
// 删除模型
export const deleteModel = data =>
  service({
    url: `/model/deleteModel`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

// 删除模块词典
export const deleteModuleDictionary = data =>
  service({
    url: `/model/deleteModuleDictionary`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 停用模块词典
export const stopModuleDictionary = data =>
  service({
    url: `/model/stopModuleDictionary`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
// 人员匹配列表
export const personnelMatchingList = data =>
  service({ url: `/enq/enqInfo/personnelMatchingList`, method: 'get', params: data })
// 导出人员匹配模板
export const exportperModuleDictionary = data =>
  service({ url: `/enq/enqInfo/exportPerModuleDictionary`, method: 'get', params: data, responseType: 'blob' })
// 导入人员匹配模板
export const importPerModuleDictionary = data =>
  service({
    url: `/enq/enqInfo/importPerModuleDictionary`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
// 导出职位匹配模板
export const exportPosModuleDictionary = data =>
  service({ url: `/enq/enqInfo/exportPosModuleDictionary`, method: 'get', params: data, responseType: 'blob' })
// 导入职位匹配模板
export const importPorModuleDictionary = data =>
  service({
    url: `/enq/enqInfo/importPosModuleDictionary`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
