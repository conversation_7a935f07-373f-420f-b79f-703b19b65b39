<script setup>
defineOptions({ name: 'AppDetail' })
const route = useRoute()
const id = route.query.id
console.log('id', id)
const detailMap = ref({
  // 战略指标洞察
  zlzbdc: {
    comp: defineAsyncComponent(() => import('./components/zlzbdc.vue'))
  },
  // 战略能力评估
  zlnlpg: {
    comp: defineAsyncComponent(() => import('./components/zlnlpg.vue'))
  }
})
</script>
<template>
  <div>
    <component :is="detailMap[id].comp" />
  </div>
</template>
<style lang="scss" scoped></style>
