<template>
    <div class="quality_wrap">
        <div class="quality flex_row_wrap_start">
            <div class="quality_item">
                <div class="quality_item_title">学历</div>
                <div class="quality_item_content">
                    <el-select v-model="educationalBackgroundOpt" size="mini" placeholder="请选择学历" clearable>
                        <el-option
                            v-for="opt in educationalBackgroundOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
            <!-- <div class="quality_item">
                <div class="quality_item_title">计算机水平</div>
                <div class="quality_item_content">
                    <el-select v-model="computerSkill" size="mini" placeholder="请选择计算机水平" clearable>
                        <el-option
                            v-for="opt in computerSkillOptions"
                            :key="opt.codeName"
                            :label="opt.codeName"
                            :value="opt.dictCode"
                        ></el-option>
                    </el-select>
                </div>
            </div> -->
            <div class="quality_item">
                <div class="quality_item_title">外语水平</div>
                <div class="quality_item_content">
                    <el-select v-model="languageProficiencyOpt" size="mini" placeholder="请选择外语水平" clearable>
                        <el-option
                            v-for="opt in languageProficiencyOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
            <div class="quality_item">
                <div class="quality_item_title">工作经验</div>
                <div class="quality_item_content">
                    <el-select v-model="workExperienceOpt" size="mini" placeholder="请选择工作经验" clearable>
                        <el-option
                            v-for="opt in workExperienceOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
            <div class="quality_item">
                <div class="quality_item_title">从事本岗位职责相同的工作年限</div>
                <div class="quality_item_content">
                    <el-select v-model="postExperienceOpt" size="mini" placeholder="请选择岗位经验" clearable>
                        <el-option
                            v-for="opt in postExperienceOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
            <div class="quality_item">
                <div class="quality_item_title">与本公司同行业的工作年限</div>
                <div class="quality_item_content">
                    <el-select v-model="industryExperienceOpt" size="mini" placeholder="请选择行业经验" clearable>
                        <el-option
                            v-for="opt in industryExperienceOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
            <!-- <div class="quality_item">
                <div class="quality_item_title">沟通频率</div>
                <div class="quality_item_content">
                    <el-select v-model="communicationFrequency" size="mini" placeholder="请选择沟通频率" clearable>
                        <el-option
                            v-for="opt in communicationFrequencyOptions"
                            :key="opt.codeName"
                            :label="opt.codeName"
                            :value="opt.dictCode"
                        ></el-option>
                    </el-select>
                </div>
            </div>
            <div class="quality_item">
                <div class="quality_item_title">沟通技巧</div>
                <div class="quality_item_content">
                    <el-select v-model="communicationSkill" size="mini" placeholder="请选择沟通技巧" clearable>
                        <el-option
                            v-for="opt in communicationSkillOptions"
                            :key="opt.codeName"
                            :label="opt.codeName"
                            :value="opt.dictCode"
                        ></el-option>
                    </el-select>
                </div>
            </div>
            <div class="quality_item">
                <div class="quality_item_title">沟通范围</div>
                <div class="quality_item_content">
                    <el-select v-model="communicationScope" size="mini" placeholder="请选择沟通范围" clearable>
                        <el-option
                            v-for="opt in communicationScopeOptions"
                            :key="opt.codeName"
                            :label="opt.codeName"
                            :value="opt.dictCode"
                        ></el-option>
                    </el-select>
                </div>
            </div> -->
                  <!-- 新增 -->
            <div class="quality_item">
                <div class="quality_item_title">知识水平</div>
                <div class="quality_item_content">
                    <el-select v-model="knowledgeLevelOpt" size="mini" placeholder="请选择知识水平" clearable>
                        <el-option
                            v-for="opt in knowledgeLevelOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
             <div class="quality_item">
                <div class="quality_item_title">专业能力</div>
                <div class="quality_item_content">
                    <el-select v-model="professionalCompetenceOpt" size="mini" placeholder="请选择专业能力" clearable>
                        <el-option
                            v-for="opt in professionalCompetenceOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
             <div class="quality_item">
                <div class="quality_item_title">主动性</div>
                <div class="quality_item_content">
                    <el-select v-model="initiativeOpt" size="mini" placeholder="请选择主动性" clearable>
                        <el-option
                            v-for="opt in initiativeOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
             <div class="quality_item">
                <div class="quality_item_title">责任心</div>
                <div class="quality_item_content">
                    <el-select v-model="responsibilityOpt" size="mini" placeholder="请选择责任心" clearable>
                        <el-option
                            v-for="opt in responsibilityOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
             <div class="quality_item">
                <div class="quality_item_title">管理能力</div>
                <div class="quality_item_content">
                    
                    <el-select v-model="managementAbilityOpt" size="mini" placeholder="请选择管理能力" clearable>
                        <el-option
                            v-for="opt in managementAbilityOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
             <div class="quality_item">
                <div class="quality_item_title">执行力</div>
                <div class="quality_item_content">
                    <el-select v-model="executionOpt" size="mini" placeholder="请选择执行力" clearable>
                        <el-option
                            v-for="opt in executionOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
             <div class="quality_item">
                <div class="quality_item_title">团队协作</div>
                <div class="quality_item_content">
                    <el-select v-model="teamCooperationOpt" size="mini" placeholder="请选择团队协作" clearable>
                        <el-option
                            v-for="opt in teamCooperationOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>
             <div class="quality_item">
                <div class="quality_item_title">创新能力</div>
                <div class="quality_item_content">
                    <el-select v-model="creativityOpt" size="mini" placeholder="请选择创新能力" clearable>
                        <el-option
                            v-for="opt in creativityOptions"
                            :key="opt.optionContent"
                            :label="opt.optionContent"
                            :value="opt.code"
                        ></el-option>
                    </el-select>
                </div>
            </div>


        </div>
        <div class="btn_wrap align_center">
            <el-button class="page_confirm_btn" type="primary" @click='submitBtn'>保 存</el-button>
        </div>
    </div>
</template>
 
<script>
import {jobInfoByCode,updateJobInfo,jobInfoQualityRequirement} from '../../../../request/api'
export default {
    name: "quality",
    props:{
        jobCode:String,
    },
    data() {
        return {
            // qualification: "",
            // qualificationOptions:[],
            // computerSkill:'',
            // computerSkillOptions:[],
            // languageLevel:'',
            // languageLevelOptions:[],
            educationalBackgroundOpt:'',
            educationalBackgroundOptions:[],
            languageProficiencyOpt:'',
            languageProficiencyOptions:[],
            workExperienceOpt:'',
            workExperienceOptions:[],
            postExperienceOpt:'',
            postExperienceOptions:[],
            industryExperienceOpt:'',
            industryExperienceOptions:[],
            knowledgeLevelOpt:'',
            knowledgeLevelOptions:[],
            professionalCompetenceOpt:'',
            professionalCompetenceOptions:[],
            initiativeOpt:'',
            initiativeOptions:[],
            responsibilityOpt:'',
            responsibilityOptions:[],
            managementAbilityOpt:'',
            managementAbilityOptions:[],
            executionOpt:'',
            executionOptions:[],
            teamCooperationOpt:'',
            teamCooperationOptions:[],
            creativityOpt:'',
            creativityOptions:[],
        };
    },
    created(){
        this.jobInfoByCodeFun()
    },
    mounted(){
        // this.getDocList()
        this.jobInfoQualityRequirementFun()
    },
    methods:{
        getDocList(){
            let params = ['QUALIFICATION','COMPUTER_SKILL','LANGUAGE_LEVEL','EXPERIENCE_AGE','COMMUNICATION_FREQUENCY','COMMUNICATION_SCOPE','COMMUNICATION_SKILL'];
            this.$getDocList(params).then(res=>{
                // this.qualificationOptions = res.QUALIFICATION
                // this.computerSkillOptions = res.COMPUTER_SKILL
                // this.languageLevelOptions = res.LANGUAGE_LEVEL
                // this.workExperienceOptions = res.EXPERIENCE_AGE,
                // this.postWorkYearsOptions = res.experience
                // this.peerWorkYearsOptions = res.experience
                // this.communicationFrequencyOptions = res.COMMUNICATION_FREQUENCY,
                // this.communicationSkillOptions = res.COMMUNICATION_SKILL,
                // this.communicationScopeOptions = res.COMMUNICATION_SCOPE
            })
        },

        jobInfoQualityRequirementFun(){
            // ZWY.BGW	本岗位经验
            // ZWY.CXN	创新能力
            // ZWY.GLN	管理能力
            // ZWY.GZJ	工作经验
            // ZWY.HYJ	行业经验
            // ZWY.TDX	团队协作
            // ZWY.WYS	外语水平
            // ZWY.XLY	学历要求
            // ZWY.ZDX	主动性
            // ZWY.ZRX	责任心
            // ZWY.ZSS	知识水平
            // ZWY.ZXL	执行力
            // ZWY.ZYN	专业能力
            let paramsArr = ['ZWY.XLY','ZWY.GZJ','ZWY.BGW','ZWY.HYJ','ZWY.ZSS','ZWY.ZYN','ZWY.ZDX','ZWY.ZRX','ZWY.GLN','ZWY.ZXL','ZWY.TDX','ZWY.CXN','ZWY.WYS']
            jobInfoQualityRequirement({
                modelName:'职位要求调研模型',
                moduleCodes:paramsArr.join(',')
            }).then(res=>{
                console.log(res)
                // 学历
                this.educationalBackgroundOptions = res.data['ZWY.XLY']
                // 外语水平
                this.languageProficiencyOptions = res.data['ZWY.WYS']
                //   工作经验
                this.workExperienceOptions = res.data['ZWY.GZJ']
                console.log(this.workExperienceOptions)
                //岗位经验
                this.postExperienceOptions = res.data['ZWY.BGW']
                // 行业经验
                this.industryExperienceOptions = res.data['ZWY.HYJ']
                // 知识水平
                this.knowledgeLevelOptions = res.data['ZWY.ZSS']
                // 专业能力
                this.professionalCompetenceOptions = res.data['ZWY.ZYN']
                // 主动性
                this.initiativeOptions = res.data['ZWY.ZDX']
                // 责任心
                this.responsibilityOptions = res.data['ZWY.ZRX']
                // 管理能力
                this.managementAbilityOptions = res.data['ZWY.GLN']
                // 执行力
                this.executionOptions = res.data['ZWY.ZXL']
                // 团队协作
                this.teamCooperationOptions = res.data['ZWY.TDX']
                // 创新能力
                this.creativityOptions = res.data['ZWY.CXN']
            })
        },

        submitBtn(){
            this.updateJobInfoFun()
        },
        updateJobInfoFun(){
            let params = {
                jobCode:this.jobCode,
                educationalBackgroundOpt:this.educationalBackgroundOpt,
                languageProficiencyOpt:this.languageProficiencyOpt,
                workExperienceOpt:this.workExperienceOpt,
                postExperienceOpt:this.postExperienceOpt,
                industryExperienceOpt:this.industryExperienceOpt,
                knowledgeLevelOpt:this.knowledgeLevelOpt,
                professionalCompetenceOpt:this.professionalCompetenceOpt,
                initiativeOpt:this.initiativeOpt,
                responsibilityOpt:this.responsibilityOpt,
                managementAbilityOpt:this.managementAbilityOpt,
                executionOpt:this.executionOpt,
                teamCooperationOpt:this.teamCooperationOpt,
                creativityOpt:this.creativityOpt,
            }
            updateJobInfo(
                params
            ).then(res=>{
                console.log(res)
                if(res.code == 200){
                    this.$emit('submitSuccessTab','quality')
                    this.$msg.success(res.msg)
                }else{
                    this.$msg.error(res.msg)
                }
            })
        },
        // 数据回显
        jobInfoByCodeFun(){
            console.log(this.jobCode)
            if(this.jobCode){
                jobInfoByCode({
                    jobCode:this.jobCode
                }).then(res=>{
                    console.log(res)
                    if(res.code == 200){
                        this.educationalBackgroundOpt = res.data.educationalBackgroundOpt,
                        this.languageProficiencyOpt = res.data.languageProficiencyOpt,
                        this.workExperienceOpt = res.data.workExperienceOpt,
                        this.postExperienceOpt = res.data.postExperienceOpt,
                        this.industryExperienceOpt = res.data.industryExperienceOpt,
                        this.knowledgeLevelOpt = res.data.knowledgeLevelOpt,
                        this.professionalCompetenceOpt = res.data.professionalCompetenceOpt,
                        this.initiativeOpt = res.data.initiativeOpt,
                        this.responsibilityOpt = res.data.responsibilityOpt,
                        this.managementAbilityOpt = res.data.managementAbilityOpt,
                        this.executionOpt = res.data.executionOpt,
                        this.teamCooperationOpt = res.data.teamCooperationOpt,
                        this.creativityOpt = res.data.creativityOpt
                        // this.qualification = res.data.qualification
                        // this.computerSkill = res.data.computerSkill
                        // this.languageLevel = res.data.languageLevel
                        // this.workExperienceOpt = res.data.workExperience
                        // this.postWorkYears = res.data.postExperience
                        // this.peerWorkYears = res.data.industryExperience
                        // this.communicationFrequency = res.data.communicationFrequency
                        // this.communicationSkill = res.data.communicationSkill
                        // this.communicationScope = res.data.communicationScope
                    }
                })
            }
        }
        
    }
};
</script>
 
<style scoped lang="scss">
.quality {
    margin-bottom: 16px;
    .quality_item {
        width: 30%;
        margin-right: 16px;
        margin-bottom: 16px;
        background: #F8FBFD;
        border-radius: 4px;
        border:1px solid #E8F1FA;
        padding:8px;
        &_title{
            line-height: 24px;
            border-bottom:1px solid #EBF4FF;
            color: #0099FF;
            margin-bottom: 8px;
            padding-left: 14px;
        }
        &_content{
            /*color: #5CB87A;*/
        }
        .el-select {
            width: 100%;
        }
        .el-input__inner {
            background: transparent;
            border:none;
        }
    }
}
</style>