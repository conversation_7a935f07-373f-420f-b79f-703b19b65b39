<script setup>
import { Search } from "@element-plus/icons-vue";
import SectionTab from "../../components/sectionTab.vue";
import Tree from "@/components/tree/index.vue";
import Table from "../../components/table.vue";
const key = ref("");

const columns = ref([
  {
    label: "能力模块",
    prop: "a",
  },
  {
    label: "能力得分",
    prop: "b",
    slot: "bSlot",
    width: 130,
  },
  {
    label: "流程赋能",
    prop: "c",
    slot: "cSlot",
    width: 130,
  },
  {
    label: "组织赋能",
    prop: "d",
    slot: "dSlot",
    width: 130,
  },
  {
    label: "人岗赋能",
    prop: "e",
    slot: "eSlot",
    width: 130,
  },
  {
    label: "数字化赋能",
    prop: "f",
    slot: "fSlot",
    width: 130,
  },
  {
    label: "AI赋能",
    prop: "g",
    slot: "gSlot",
    width: 130,
  },
  // {
  //   label: "达成率",
  //   prop: "h",
  // },
  // {
  //   label: "差距",
  //   prop: "i",
  // },
  {
    label: "",
    prop: "j",
    slot: "jSlot",
    width: 130,
  },
]);
const data = ref([
  {
    a: "物流成本占比",
    b: 43,
    c: 54,
    d: 63,
    e: 74,
    f: 83,
    g: 93,
    h: ">98%",
    i: ">98%",
    j: 3,
  },
]);

const numberArea = ref([
  {
    num: "0~59",
  },
  {
    num: "60~69",
  },
  {
    num: "70~79",
  },
  {
    num: "80~89",
  },
  {
    num: "90~100",
  },
]);

const columns2 = ref([
  {
    label: "根因类型",
    prop: "a",
  },
  {
    label: "根因定义",
    prop: "b",
  },
  {
    label: "占比",
    prop: "c",
  },
  {
    label: "影响系数",
    prop: "d",
  },
  {
    label: "影响分析",
    prop: "e",
    width: 420,
  },
]);
const data2 = ref([
  {
    a: "缺工具",
    b: "无明确执行规范或质量要求",
    c: "20%",
    d: "高",
    e: "库存动态分析能力通过实时监控库存状态（数量、库龄、周转率），快速识别积压风险并触发调整动作。实时数据反馈机制可缩短信息滞后周期，减少因数据失真导致的过量备货，直接降低库存持有天数。",
    j: 66,
  },
]);

const columns3 = ref([
  {
    label: "根因类型",
    prop: "a",
    width: 100,
  },
  {
    label: "影响类型说明",
    prop: "b",
  },
  {
    label: "占比",
    prop: "c",
    width: 100,
  },
  {
    label: "影响系数",
    prop: "d",
    width: 100,
  },
  {
    label: "影响路径",
    prop: "e",
    // width: 420,
  },
]);
const data3 = ref([
  {
    a: "核心指标影响",
    b: "导致商机转化率、成单周期、客户满意度等核心指标恶化",
    c: "25%",
    d: "高",
    e: "商机转化率下降→成单周期延长→客户满意度降低→销售业绩下滑",
  },
]);

const circleColor = (v) => {
  if (v < 59) {
    return "bg1_b";
  } else if (v > 59 && v < 69) {
    return "bg2_b";
  } else if (v > 69 && v < 79) {
    return "bg3_b";
  } else if (v > 79 && v < 89) {
    return "bg4_b";
  } else if (v > 89 && v <= 100) {
    return "bg5_b";
  }
};
</script>
<template>
  <div class="indicator_main">
    <div class="page-title-line">能力一览</div>
    <div class="number_area_list justify-start">
      分值：
      <div class="item_wrap" v-for="(item, index) in numberArea">
        <span
          class="icon"
          :class="{
            act: index == 0,
            act1: index == 1,
            act2: index == 2,
            act3: index == 3,
            act4: index == 4,
          }"
        ></span
        >{{ item.num }}
      </div>
    </div>
    <Table
      :roundBorder="false"
      :columns="columns"
      :data="data"
      headerColor
      showIndex
    >
      <template v-slot:bSlot="scope">
        <span class="circle" :class="circleColor(scope.row.b)">{{
          scope.row.b
        }}</span>
      </template>
      <template v-slot:cSlot="scope">
        <span class="circle" :class="circleColor(scope.row.c)">{{
          scope.row.c
        }}</span>
      </template>
      <template v-slot:dSlot="scope">
        <span class="circle" :class="circleColor(scope.row.d)">{{
          scope.row.d
        }}</span>
      </template>
      <template v-slot:eSlot="scope">
        <span class="circle" :class="circleColor(scope.row.e)">{{
          scope.row.e
        }}</span>
      </template>
      <template v-slot:fSlot="scope">
        <span class="circle" :class="circleColor(scope.row.f)">{{
          scope.row.f
        }}</span>
      </template>
      <template v-slot:gSlot="scope">
        <span class="circle" :class="circleColor(scope.row.g)">{{
          scope.row.g
        }}</span>
      </template>
      <template v-slot:hSlot="scope">
        <span class="circle" :class="circleColor(scope.row.h)">{{
          scope.row.h
        }}</span>
      </template>
      <template v-slot:jSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>AI解读</el-button>
      </template>
    </Table>
    <div class="tips">已选能力：<span>库存动态分析</span></div>
    <div class="page-title-line">能力DNA解码</div>
    <div class="chart_list_wrap justify-between">
      <div class="item_wrap">
        <div class="chart_t">流程赋能</div>
        <div class="chart_box"></div>
      </div>
      <div class="item_wrap">
        <div class="chart_t">组织赋能</div>
        <div class="chart_box"></div>
      </div>
      <div class="item_wrap">
        <div class="chart_t">人岗赋能</div>
        <div class="chart_box"></div>
      </div>
      <div class="item_wrap">
        <div class="chart_t">数字化赋能</div>
        <div class="chart_box"></div>
      </div>
      <div class="item_wrap">
        <div class="chart_t">AI赋能</div>
        <div class="chart_box"></div>
      </div>
    </div>
    <div class="tips">已选能力：<span>库存动态分析</span></div>

    <div class="page-title-line">根因分析</div>
    <Table
      :roundBorder="false"
      :columns="columns2"
      :data="data2"
      headerColor
      showIndex
    >
    </Table>

    <div class="page-title-line marginT20">影响分析</div>
    <Table
      :roundBorder="false"
      :columns="columns3"
      :data="data3"
      headerColor
      showIndex
    >
    </Table>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
@import "../common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
