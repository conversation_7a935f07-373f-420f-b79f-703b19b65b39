<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <slot></slot>
        <div class="report_section">
            <div class="page_second_title">组织信息</div>
            <div class="basic_info_wrap flex_row_start">
                <div class="basic_info_item">
                    <div class="title">组织名称</div>
                    <div class="text">{{ basicInfo.orgName }}</div>
                </div>
                <div class="basic_info_item">
                    <div class="title">上级组织</div>
                    <div class="text">
                        {{ basicInfo.parentOrgName || "--" }}
                    </div>
                </div>
                <div class="basic_info_item">
                    <div class="title">所属公司</div>
                    <div class="text">{{ basicInfo.companyName }}</div>
                </div>
                <div class="basic_info_item">
                    <div class="title">组织负责人</div>
                    <div class="text">{{ basicInfo.orgLeaderName }}</div>
                </div>
                <div class="basic_info_item">
                    <div class="title">岗位数量</div>
                    <div class="text">{{ basicInfo.postCount }}</div>
                </div>
                <div class="basic_info_item">
                    <div class="title">员工数量</div>
                    <div class="text">{{ basicInfo.userCount }}</div>
                </div>
                <div class="basic_info_item">
                    <div class="title">近一年入职人员</div>
                    <div class="text">
                        {{ basicInfo.oneYearUserCount || "--" }}
                    </div>
                </div>

                <!-- <div class="basic_info_item">
                <div class="title">组织层级</div>
                <div class="text">{{basicInfo.orgLevelName}}</div>
            </div> -->
            </div>
        </div>
        <div class="report_section">
            <div class="page_second_title">组织负责人信息</div>
            <div class="basic_info_wrap flex_row_start">
                <div class="basic_info_item">
                    <div class="title">姓名</div>
                    <div class="text">{{ leaderInfo.userName }}</div>
                </div>
                <div class="basic_info_item">
                    <div class="title">岗位名称</div>
                    <div class="text">
                        {{ leaderInfo.postName}}
                    </div>
                </div>
                <div class="basic_info_item">
                    <div class="title">职层</div>
                    <div class="text">{{ leaderInfo.jobLevelName }}</div>
                </div>
                <div class="basic_info_item">
                    <div class="title">职等</div>
                    <div class="text">{{ leaderInfo.jobGradeName }}</div>
                </div>
                <div class="basic_info_item">
                    <div class="title">上级岗位</div>
                    <div class="text">{{ leaderInfo.superiorPostName }}</div>
                </div>
                <div class="basic_info_item">
                    <div class="title">直接上级</div>
                    <div class="text">{{ leaderInfo.superiorName }}</div>
                </div>
                <div class="basic_info_item">
                    <div class="title">参加工作日期</div>
                    <div class="text">
                        {{ leaderInfo.workBeginDate || "--" }}
                    </div>
                </div>
                <div class="basic_info_item">
                    <div class="title">入司时间</div>
                    <div class="text">
                        {{ leaderInfo.currentEmpDate || "--" }}
                    </div>
                </div>
                <div class="basic_info_item">
                    <div class="title">工龄</div>
                    <div class="text">
                        {{ leaderInfo.workExperience || "--" }}
                    </div>
                </div>
                <div class="basic_info_item">
                    <div class="title">本岗位工作时长</div>
                    <div class="text">
                        {{ leaderInfo.postExperience}}
                    </div>
                </div>
                <div class="basic_info_item">
                    <div class="title">最近晋升日期</div>
                    <div class="text">
                        {{ leaderInfo.lastPromotionDate || "--" }}
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="page_second_title">职责模块</div>
        <div class="duty_wrap">
            <tableComps :needPagination="false" :overflowTooltip="false" :tableData="tableData"></tableComps>
        </div> -->
    </div>
</template>
 
<script>
    import {
        getOrgReportBasicInfo,
        getEnqOrgLeaderInfo,
    } from "../../../../request/api";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "orgRBasicInfo",
        props: ["enqId", "orgCode", "isPdf"],
        components: { tableComps },
        data() {
            return {
                basicInfo: {},
                leaderInfo:{},
                tableData: {
                    columns: [
                        {
                            label: "职责模块",
                            prop: "respName",
                            width: 200,
                        },
                        {
                            label: "职责描述",
                            prop: "respDesc",
                        },
                    ],
                    data: [],
                },
            };
        },
        created() {
            this.init();
        },
        mounted() {},
        methods: {
            init() {
                this.getOrgReportBasicInfoFun();
                this.getEnqOrgLeaderInfoFun();
            },
            getOrgReportBasicInfoFun() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getOrgReportBasicInfo(params).then((res) => {
                    if (res.code == "200") {
                        this.basicInfo = res.data;
                        this.$set(this.tableData, "data", res.data.respInfoList);
                    }
                });
            },
            getEnqOrgLeaderInfoFun() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getEnqOrgLeaderInfo(params).then((res) => {
                    if (res.code == "200") {
                        console.log(res);
                        this.leaderInfo = res.data;
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .org_report_main {
        display: flex;
        flex-flow: row wrap;
        .report_section{
            flex: 1;
        }
    }
    .basic_info_wrap {
        padding-left: 16px;
        margin-bottom: 32px;
        flex-flow: row wrap;
        .basic_info_item {
            width: 50%;
            margin-bottom: 32px;
            .title {
                font-size: 14px;
                color: #0099fd;
                font-weight: bold;
                margin-bottom: 8px;
            }
            .text {
                font-size: 12px;
                color: #212121;
            }
        }
    }
    .duty_wrap {
        .duty_name {
            flex: 1;
            width: 200px;
            margin-right: 20px;
        }
        .duty_desc {
            flex: 10;
        }
        .duty_head {
        }
    }
</style>