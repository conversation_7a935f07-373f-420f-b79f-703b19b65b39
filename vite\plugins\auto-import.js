// Vue函数的自动导入
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import IconsResolver from 'unplugin-icons/resolver'
export default (path, env) => {
  return AutoImport({
    imports: [
      // presets
      'vue',
      'vue-router',
      'pinia'
    ],
    // 自动导入 Element Plus 相关函数ElMessage, ElMessageBox... (带样式)
    resolvers: [
      ElementPlusResolver({
        importStyle: true
        // 开发环境不需要按需加载样式
        // importStyle: env.VITE_APP_ENV == 'development' ? false : 'sass'
      }),
      // 自动导入图标组件
      IconsResolver({
        prefix: 'Icon'
      })
    ],
    dts: false,
    eslintrc: {
      // 1、true时生成eslint配置文件，2、生成后改为false，避免重复消耗
      enabled: false,
      filepath: './.eslintrc-auto-import.json',
      globalsPropValue: true
    },
    vueTemplate: true, // 是否在 vue 模板中自动导入
    globalsPropValue: true
  })
}
