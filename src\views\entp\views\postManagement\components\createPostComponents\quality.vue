<template>
  <div class="quality_wrap">
    <div class="quality flex_row_wrap_start">
      <div class="quality_item">
        <div class="quality_item_title">学历</div>
        <div class="quality_item_content">{{ educationalBackgroundOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">外语水平</div>
        <div class="quality_item_content">{{ languageProficiencyOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">工作经验</div>
        <div class="quality_item_content">{{ workExperienceOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">从事本岗位职责相同的工作年限</div>
        <div class="quality_item_content">{{ postExperienceOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">与本公司同行业的工作年限</div>
        <div class="quality_item_content">{{ industryExperienceOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">知识水平</div>
        <div class="quality_item_content">{{ knowledgeLevelOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">专业能力</div>
        <div class="quality_item_content">{{ professionalCompetenceOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">主动性</div>
        <div class="quality_item_content">{{ initiativeOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">责任心</div>
        <div class="quality_item_content">{{ responsibilityOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">管理能力</div>
        <div class="quality_item_content">{{ managementAbilityOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">执行力</div>
        <div class="quality_item_content">{{ executionOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">团队协作</div>
        <div class="quality_item_content">{{ teamCooperationOpt }}</div>
      </div>
      <div class="quality_item">
        <div class="quality_item_title">创新能力</div>
        <div class="quality_item_content">{{ creativityOpt }}</div>
      </div>
    </div>
    <div class="btn_wrap align_center">
      <el-button class="page_confirm_btn" type="primary" @click="submitBtn"> 保存 </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { jobQualityRequirements } from '../../../../request/api'

// Props
const props = defineProps({
  curPostCodeCopy: String
})

// Emits
const emit = defineEmits(['submitSuccessTab'])

// Reactive State
const postCode = ref(props.curPostCodeCopy || '')
const educationalBackgroundOpt = ref('')
const languageProficiencyOpt = ref('')
const workExperienceOpt = ref('')
const postExperienceOpt = ref('')
const industryExperienceOpt = ref('')
const knowledgeLevelOpt = ref('')
const professionalCompetenceOpt = ref('')
const initiativeOpt = ref('')
const responsibilityOpt = ref('')
const managementAbilityOpt = ref('')
const executionOpt = ref('')
const teamCooperationOpt = ref('')
const creativityOpt = ref('')

// Methods
const submitBtn = () => {
  emit('submitSuccessTab', 'quality')
}

const jobQualityRequirementsFun = async () => {
  try {
    const res = await jobQualityRequirements({
      postCode: props.curPostCodeCopy
    })

    if (res.code == 200) {
      const {
        educationalBackgroundOpt: eduOpt,
        languageProficiencyOpt: langOpt,
        workExperienceOpt: workOpt,
        postExperienceOpt: postOpt,
        industryExperienceOpt: indOpt,
        knowledgeLevelOpt: knowOpt,
        professionalCompetenceOpt: profOpt,
        initiativeOpt: initOpt,
        responsibilityOpt: respOpt,
        managementAbilityOpt: mngOpt,
        executionOpt: execOpt,
        teamCooperationOpt: teamOpt,
        creativityOpt: creatOpt
      } = res.data

      educationalBackgroundOpt.value = eduOpt
      languageProficiencyOpt.value = langOpt
      workExperienceOpt.value = workOpt
      postExperienceOpt.value = postOpt
      industryExperienceOpt.value = indOpt
      knowledgeLevelOpt.value = knowOpt
      professionalCompetenceOpt.value = profOpt
      initiativeOpt.value = initOpt
      responsibilityOpt.value = respOpt
      managementAbilityOpt.value = mngOpt
      executionOpt.value = execOpt
      teamCooperationOpt.value = teamOpt
      creativityOpt.value = creatOpt
    }
  } catch (error) {
    console.error('获取岗位素质要求失败:', error)
    ElMessage.error('获取岗位素质要求失败')
  }
}

// Lifecycle Hooks
onMounted(() => {
  jobQualityRequirementsFun()
})
</script>

<style lang="scss" scoped>
.quality {
  margin-bottom: 16px;

  .quality_item {
    width: 30%;
    margin-right: 16px;
    margin-bottom: 16px;
    background: #f8fbfd;
    border-radius: 4px;
    border: 1px solid #e8f1fa;
    padding: 8px;

    &_title {
      line-height: 24px;
      border-bottom: 1px solid var(--el-color-info-light-8);
      color: var(--el-color-primary);
      margin-bottom: 8px;
      padding-left: 14px;
    }

    &_content {
      padding: 0 13px;
      height: 30px;
      line-height: 30px;
    }

    :deep(.el-select) {
      width: 100%;
    }

    :deep(.el-input__inner) {
      background: transparent;
      border: none;
    }
  }
}
</style>
