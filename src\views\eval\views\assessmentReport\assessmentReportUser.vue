<template>
    <div class="assessment_report_org_wrap">
        <div class="page_main_title flex_row_start">
            评估报告
            <el-breadcrumb separator="/">
                <el-breadcrumb-item>国机精工销售与市场能力测评</el-breadcrumb-item>
                <el-breadcrumb-item>个人报告</el-breadcrumb-item>
                <el-breadcrumb-item>销售部</el-breadcrumb-item>
            </el-breadcrumb>
            <div class="back_btn fr">
                <el-button type="primary" plain size="mini" @click ="$util.goback()">返回</el-button>
            </div>
        </div>
        <div class="page_section">
            <div class="page_section_title">员工发展计划建议</div>
            <div class="page_section assessment_report_org_center clearfix">
                <table-component :tableData="tableData" :needIndex=true :setCellStyle="setCellStyle"></table-component>
            </div>
        </div>
    </div>
</template>
 
<script>
import tableComponent from "@/components/talent/tableComps/tableComponent"
export default {
    name: "assessmentReportOrg",
    components: {
        tableComponent
    },
    data() {
        return {
            tableData:{
                columns: [
                    {
                        label: "部门",
                        prop: "department"
                    },
                    {
                        label: "姓名",
                        prop: "name",
                        width:'80'
                    },
                    {
                        label: "当前岗位",
                        prop: "post"
                    },
                    {
                        label: "能力目标",
                        prop: "target"
                    },
                    {
                        label: "实际",
                        prop: "score",
                        width:'70'
                    },
                    {
                        label: "匹配度",
                        prop: "matching"
                    },
                    {
                        label: "偏离度",
                        prop: "deviation",
                        className:'deviation'
                    },
                    {
                        label: "绩效",
                        prop: "achievements"
                    },
                    {
                        label: "发展建议",
                        prop: "suggest"
                    },
                    {
                        label: "推荐目标岗位",
                        prop: "targetPost"
                    },
                ],
                data: [
                    {
                        id: "1asdgasfaf",
                        department:'销售一部',
                        name: "王伟",
                        post:'销售经理',
                        target:'76',
                        score:'78',
                        matching:'100%',
                        deviation:'+15%',
                        achievements:'前5%',
                        suggest:'巩固现有级别',
                        targetPost:'销售总监'
                    },
                    {
                        id: "1asfadsffaf",
                        department:'销售一部',
                        name: "王伟",
                        post:'销售经理',
                        target:'76',
                        score:'78',
                        matching:'100%',
                        deviation:'+15%',
                        achievements:'前5%',
                        suggest:'向上级别发展',
                        targetPost:'销售总监'
                    },
                    {
                        id: "1asffasaf",
                        department:'销售一部',
                        name: "王伟",
                        post:'销售经理',
                        target:'76',
                        score:'78',
                        matching:'100%',
                        deviation:'+15%',
                        achievements:'前5%',
                        suggest:'跨通道发展',
                        targetPost:'销售总监'
                    },
                    {
                        id: "1asdgasfaf",
                        department:'销售一部',
                        name: "王伟",
                        post:'销售经理',
                        target:'76',
                        score:'78',
                        matching:'100%',
                        deviation:'-15%',
                        achievements:'82',
                        suggest:'巩固现有级别',
                        targetPost:'销售总监'
                    },
                    {
                        id: "1asfadsffaf",
                        department:'销售一部',
                        name: "王伟",
                        post:'销售经理',
                        target:'76',
                        score:'78',
                        matching:'100%',
                        deviation:'-15%',
                        achievements:'82',
                        suggest:'向上级别发展',
                        targetPost:'销售总监'
                    },
                    {
                        id: "1asffasaf",
                        department:'销售一部',
                        name: "王伟",
                        post:'销售经理',
                        target:'76',
                        score:'78',
                        matching:'100%',
                        deviation:'0%',
                        achievements:'82',
                        suggest:'跨通道发展',
                        targetPost:'销售总监'
                    },
                ]
            }
        };
    },
    methods: {
        setCellStyle({row,column,rowIndex,columnIndex}){
            if(column.property == 'deviation'){
                let val = row['deviation'];
                // 判断是不是负偏离
                let state = val.indexOf('-');

                if(state == '0'){
                    return {
                        color:'#FF8989'
                    }
                }else{
                    return {
                        color:'#0099fd'
                    }
                }
                return {}
            }
            
        }
    }
};
</script>
 
<style scoped lang="scss">
.assessment_report_org_wrap{
    background:#fff;
}
.el-breadcrumb{
    line-height: 70px;
    font-weight: normal;
    margin-left: 16px;
    font-size: inherit;
}
</style>