<template>
  <div class="role_permission_wrap bg_write">
    <div class="page_main_title">角色权限</div>
    <div class="page_section">
      <div class="page_section_main page_shadow">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title">角色名称</div>
            <div class="btn_wrap">
              <el-button link size="mini" class="oper_btn" @click="addRole">新增</el-button>
              <el-button link size="mini" class="oper_btn" @click="editRole">修改</el-button>
              <el-button link size="mini" class="oper_btn" @click="deleteRole">删除</el-button>
            </div>
          </div>
          <ul class="aside_tree_list role_wrap">
            <tree-comp-radio
              :treeData="roleTreeData"
              :canCancel="true"
              :needCheckedFirstNode="false"
              @clickCallback="roleTreeClickCallback"
            ></tree-comp-radio>
            <!-- <el-checkbox-group v-model="checkedRole" @change="handleChange">
                            <el-checkbox v-for="item in roleList" :label="item" :title='item.roleName' :key="item.roleId">{{item.roleName}}</el-checkbox>
                        </el-checkbox-group> -->
          </ul>
        </div>
        <div class="page_section_aside power_set_wrap">
          <div class="aside_tree_title flex_row_between">
            <div class="tree_title">
              <span class="power_title">功能权限</span>
              <span>操作权限</span>
            </div>
          </div>
          <div class="aside_tree_list">
            <powerTreeComp-checkbox
              :treeData="copyTreeData"
              :formSign="rolePermission"
              :defaultCheckedKeys="defaultCheckedKeys"
              @node-click-callback="nodeClickCallback"
              @menuActionCheck="menuActionCheck"
            >
            </powerTreeComp-checkbox>
          </div>
        </div>
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title">数据权限</div>
          </div>
          <!-- <div class="aside_tree_list related_staff_wrap data_power_wrap"> -->
          <div class="aside_tree_list related_staff_wrap role_wrap">
            <!-- <el-radio-group v-model="checkedDataPower"  @change="handleDPChange">
                            <el-radio class="clearfix" v-for="item in dataPowerList" :label="item.dictCode" :title='item.codeName' :key="item.dictCode" border>{{item.codeName}}</el-radio>
                        </el-radio-group> -->
            <el-checkbox-group v-model="checkedDataPower" @change="handleDPChange">
              <el-checkbox
                v-for="item in dataPowerList"
                :label="item.dictCode"
                :title="item.codeName"
                :key="item.dictCode"
                >{{ item.codeName }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </div>
    <el-dialog v-model:visible="dialogVisible" :title="popupTitleSign ? '新增角色' : '修改角色'" width="40%" center>
      <div class="line_wrap flex_row_betweens">
        <span>角色名称：</span>
        <div>
          <el-input v-model="roleName"></el-input>
        </div>
      </div>
      <template v-slot:footer>
        <div class="dialog-footer text_right">
          <el-button class="page_clear_btn" @click="popUpCancal">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="popUpSubmitBtn">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  getcompanyRoleList,
  createdCompanyRole,
  getRelationMenuTree,
  updateCompanyRole,
  deleteCompanyRole,
  getMenuTreeAndUser,
  getLoginUserMenuTree,
  saveRoleMenuAndUser,
  editUserRole,
  saveRoleAuthority,
  deleteRoleAuthority
} from '../../request/api'
import powerTreeCompCheckbox from '@/components/talent/treeComps/powerTreeCompCheckbox'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
export default {
  name: 'rolePermission',
  components: {
    treeCompRadio,
    powerTreeCompCheckbox
  },
  data() {
    return {
      checkedRole: [],
      roleList: [],
      // 角色弹层
      roleTreeData: [],
      popupTitleSign: true,
      dialogVisible: false,
      roleName: '',

      // 功能权限
      treeData: [],
      copyTreeData: [],
      checkedId: '',
      prevCheckCodes: [],
      // 数据权限
      dataPowerList: [],
      checkedDataPower: '',
      copyCheckedDataPower: '',
      operateType: '',

      checkedRelatedStaff: [],
      relatedStaff: [],
      rolePermission: 'rolePermission',
      defaultCheckedKeys: [],
      disabledCheckSign: false,
      operatePDefaultCheckedKeys: []
    }
  },
  created() {
    this.getcompanyRoleListFun()
    this.getMenuTreeAndUserFun() //功能权限 关联人员
    // this.getLoginUserMenuTreeFun()
    // this.getRelationMenuTreeFun()
  },
  mounted() {},
  // roleType为1 角色不可修改 对应的功能权限不可修改
  methods: {
    // 角色名称
    getcompanyRoleListFun() {
      getcompanyRoleList({}).then(res => {
        // console.log(res)
        this.roleTreeData = []
        if (res.code == 200) {
          this.roleTreeData = res.data.map(item => {
            return {
              code: item.roleId,
              value: item.roleName,
              roleType: item.roleType
            }
          })
        }
      })
    },
    roleTreeClickCallback(val, lastNodeSign, node) {
      this.copyTreeData = []
      this.checkedRole = []

      this.getMenuTreeAndUserFun()
      // console.log(this.checkedRole)
      if (val) {
        this.checkedRole.push({
          roleType: node.roleType,
          roleName: node.value,
          roleId: node.code
        })
        // 选中角色
        this.getRelationMenuTreeFun()
        if (this.checkedRole[0].roleType == '1') {
          this.disabledCheckSign = true
        } else {
          this.disabledCheckSign = false
        }
      } else {
        // 取消选中
        this.checkedRelatedStaff = []
        this.defaultCheckedKeys = []
        this.checkedDataPower = ''
      }
    },
    // handleChange(val){
    //     this.copyTreeData = []
    //     this.getMenuTreeAndUserFun()
    //     if(val.length > 1){
    //         val.shift();
    //     }
    //     this.checkedRole = val
    //     if(val.length > 0){
    //         // 选中角色
    //         this.getRelationMenuTreeFun()
    //         if(this.checkedRole[0].roleType == '1'){
    //             this.disabledCheckSign = true
    //         }else{
    //             this.disabledCheckSign = false
    //         }
    //     }else{
    //         // 取消选中
    //         this.checkedRelatedStaff = []
    //         this.defaultCheckedKeys = []
    //         this.checkedDataPower = ''
    //     }
    // },
    // 新增角色
    addRole() {
      this.roleName = ''
      this.dialogVisible = true
      this.popupTitleSign = true
    },
    createdCompanyRoleFun() {
      if (!this.roleName) {
        this.$msg.warning('请填写角色名称！')
        return
      }
      createdCompanyRole({
        roleName: this.roleName
      }).then(res => {
        if (res.code == 200) {
          this.getcompanyRoleListFun()
          this.dialogVisible = false
          this.$msg.success(res.msg)
        } else {
          this.$msg.error(res.msg)
        }
      })
    },
    // 修改角色
    editRole() {
      if (this.checkedRole.length == 0) {
        this.$msg.warning('请选择角色！')
      } else if (this.checkedRole[0].roleType == '1') {
        this.$msg.warning('该角色不可修改！')
      } else {
        this.roleName = ''
        this.dialogVisible = true
        this.popupTitleSign = false
        this.roleName = this.checkedRole[0].roleName
      }
    },
    updateCompanyRoleFun() {
      if (!this.roleName) {
        this.$msg.warning('请填写角色名称！')
        return
      }
      updateCompanyRole({
        roleName: this.roleName,
        roleId: this.checkedRole[0].roleId
      }).then(res => {
        // console.log(res)
        if (res.code == 200) {
          this.getcompanyRoleListFun()
          this.dialogVisible = false
          this.$msg.success(res.msg)
        } else {
          this.$msg.error(res.msg)
        }
      })
    },
    // 删除角色
    deleteRole() {
      if (this.checkedRole.length == 0) {
        this.$msg.warning('请选择角色！')
      } else {
        this.$confirm('确认删除？', '提示', {
          type: 'warning'
        })
          .then(() => {
            this.deleteCompanyRoleFun()
          })
          .catch(() => {})
      }
    },
    deleteCompanyRoleFun() {
      deleteCompanyRole({
        roleId: this.checkedRole[0].roleId
      }).then(res => {
        if (res.code == 200) {
          this.checkedRole = []
          this.defaultCheckedKeys = []
          this.getcompanyRoleListFun()
          this.$msg.success(res.msg)
        } else {
          this.$msg.error(res.msg)
        }
      })
    },
    popUpCancal() {
      this.dialogVisible = false
    },
    // 弹窗提交btn
    popUpSubmitBtn() {
      if (this.popupTitleSign) {
        // 新增
        this.createdCompanyRoleFun()
      } else {
        // 修改
        this.updateCompanyRoleFun()
      }
    },
    // 功能权限树 和 关联人员列表
    getMenuTreeAndUserFun() {
      getMenuTreeAndUser({}).then(res => {
        // console.log(res)
        this.dataPowerList = []
        this.treeData = []
        if (res.code == 200) {
          this.treeData = res.data.menuTrees
          // 操作权限初始为 未勾选
          this.setChildNodesChecked(this.treeData, false, true)
          this.dataPowerList = res.data.roleAuthority
        }
        // console.log(this.treeData)
        if (this.checkedRole.length == 0) {
          this.copyTreeData = this.treeData
        }
      })
    },
    // 操作权限添加勾选标记
    setChildNodesChecked(node, checked, disabled) {
      node.forEach(item => {
        this.$set(item, 'disabled', disabled)
        if (item.menuActions.length > 0) {
          item.menuActions.forEach(item1 => {
            // item1.checked = checked;
            this.$set(item1, 'checked', checked)
            this.$set(item1, 'disabled', disabled)
          })
        } else {
          this.setChildNodesChecked(item.children, checked, disabled)
        }
      })
    },
    // 禁止勾选
    disabledChildNodesChecked(node, nodeCode, checked) {
      node.forEach(item => {
        if (item.menuActions.length > 0) {
          item.menuActions.forEach(item1 => {
            if (item1.actionCode == nodeCode) {
              item1.checked = checked
            }
            return
          })
        } else {
          this.disabledChildNodesChecked(item.children, nodeCode, checked)
        }
      })
    },
    // 角色 关联菜单和数据权限
    getRelationMenuTreeFun() {
      getRelationMenuTree({
        roleId: this.checkedRole[0].roleId
      }).then(res => {
        this.defaultCheckedKeys = []
        this.checkedRelatedStaff = []
        this.operatePDefaultCheckedKeys = []
        this.checkedDataPower = []
        if (res.code == 200) {
          res.data.roleMenus.forEach(item => {
            this.defaultCheckedKeys.push(item.menuCode)
          })
          this.checkedId = this.defaultCheckedKeys
          this.prevCheckCodes = this.defaultCheckedKeys
          // this.checkedDataPower = res.data.roleInfo.roleAuthority
          this.checkedDataPower.push(res.data.roleInfo.roleAuthority)
          this.copyCheckedDataPower = this.checkedDataPower

          res.data.roleMenuActions.forEach(item => {
            this.operatePDefaultCheckedKeys.push({
              actionCode: item.actionCode,
              menuCode: item.menuCode
            })
          })
          if (this.checkedRole[0].roleType == '1') {
            // this.disabledCheckSign = true
            this.setChildNodesChecked(this.treeData, false, true)
          } else {
            // this.disabledCheckSign = false
            this.setChildNodesChecked(this.treeData, false, false)
          }
          if (this.operatePDefaultCheckedKeys.length > 0) {
            for (let i = 0; i < this.operatePDefaultCheckedKeys.length; i++) {
              this.setNodesMenuActionCheck(
                this.treeData,
                this.operatePDefaultCheckedKeys[i].menuCode,
                this.operatePDefaultCheckedKeys[i].actionCode
              )
            }
          }
          this.copyTreeData = this.treeData
        }
      })
    },
    // 操作权限数据回显
    setNodesMenuActionCheck(node, menuCode, menuActionCode) {
      if (node.length == 0) {
        return
      }
      node.forEach(item => {
        // console.log(item)
        if (item.menuCode == menuCode) {
          if (item.menuActions.length > 0) {
            item.menuActions.forEach(item1 => {
              if (item1.code == menuActionCode) {
                if (item1.checked == true) {
                  item1.checked = false
                } else if (item1.checked == false) {
                  item1.checked = true
                }
                return
              }
            })
          }
        }
        this.setNodesMenuActionCheck(item.children, menuCode, menuActionCode)
      })
    },
    // 勾选功能权限树
    nodeClickCallback(val, checkSign) {
      // console.log('------功能权限-------')
      if (this.checkedRole.length == 0) {
        this.defaultCheckedKeys = []
        this.$msg.warning('请先选择角色！')
        return
      }
      this.checkedId = val
      if (this.checkedRole[0].roleType == '1') {
        this.$msg.warning('该角色不可修改功能权限！')
        return
      }
      let curCheckCodes = []
      if (checkSign) {
        // 勾选
        val.filter(item => {
          if (this.prevCheckCodes.indexOf(item) == -1) {
            curCheckCodes.push(item)
          }
        })
        this.saveRoleAuthorityFun('M', curCheckCodes)
      } else {
        // 取消勾选
        this.prevCheckCodes.filter(item => {
          if (val.indexOf(item) == -1) {
            curCheckCodes.push(item)
          }
        })
        this.deleteRoleAuthorityFun('M', curCheckCodes)
      }
      this.prevCheckCodes = val
    },
    //配置操作权限
    menuActionCheck(menuActionType, menuCode, menuActionCode, checkSign) {
      // console.log('操作权限')
      //勾选 还是 取消勾选
      if (this.checkedRole.length == 0) {
        this.$msg.warning('请先选择角色！')
        return
      }
      if (this.checkedRole[0].roleType == '1') {
        this.$msg.warning('该角色不可修改功能权限！')
        return
      }
      // console.log('操作权限配置',checkSign)
      if (checkSign) {
        // 勾选
        this.saveRoleAuthorityFun('A', menuCode, menuActionType, menuActionCode)
      } else {
        // 取消勾选
        this.deleteRoleAuthorityFun('A', menuCode, menuActionType, menuActionCode)
      }
    },
    //配置数据权限
    handleDPChange(val) {
      if (this.checkedRole.length == 0) {
        this.checkedDataPower = ''
        this.$msg.warning('请先选择角色！')
        return
      }
      if (val.length > 1) {
        val.shift()
        this.checkedDataPower = val
        this.copyCheckedDataPower = val
      }
      if (val.length == 0) {
        this.checkedDataPower = this.copyCheckedDataPower
        return
      }
      this.saveRoleAuthorityFun('R', val[0])
    },
    saveRoleAuthorityFun(type, code, menuActionType, menuActionCode) {
      let curArguments
      if (type == 'R') {
        //数据权限
        curArguments = {
          roleAuthority: code,
          roleId: this.checkedRole[0].roleId,
          type: type
        }
      } else if (type == 'A') {
        // 操作权限
        curArguments = {
          actionCode: menuActionCode,
          actionType: menuActionType,
          menuCodes: code,
          roleId: this.checkedRole[0].roleId,
          type: type
        }
      } else if (type == 'M') {
        // 功能权限
        curArguments = {
          menuCodes: code.join(','),
          roleId: this.checkedRole[0].roleId,
          type: type
        }
      }
      saveRoleAuthority(curArguments).then(res => {
        // console.log(res)
        if (res.code == 200) {
          // this.getRelationMenuTreeFun()
          this.$msg.success(res.msg)
        } else {
          if (res.msg) {
            this.$msg.error(res.msg)
          }
        }
      })
    },
    // 取消勾选 功能权限 操作权限
    deleteRoleAuthorityFun(type, code, menuActionType, menuActionCode) {
      let curArguments
      if (type == 'A') {
        // 操作权限
        curArguments = {
          actionCode: menuActionCode,
          actionType: menuActionType,
          menuCodes: code,
          roleId: this.checkedRole[0].roleId,
          type: type
        }
      } else if (type == 'M') {
        // 功能权限
        curArguments = {
          menuCodes: code.join(','),
          roleId: this.checkedRole[0].roleId,
          type: type
        }
      }
      deleteRoleAuthority(curArguments).then(res => {
        if (res.code == 200) {
          // this.getRelationMenuTreeFun()
          this.$msg.success(res.msg)
        } else {
          if (res.msg) {
            this.$msg.error(res.msg)
          }
        }
      })
    },
    saveRoleMenuAndUserFun(val) {
      saveRoleMenuAndUser({
        roleId: this.checkedRole[0].roleId,
        menuCodeList: val == 2 ? this.checkedId.join(',') : '',
        userIdList: this.checkedRelatedStaff.join(',')
      }).then(res => {
        // console.log(res)
        if (res.code == 200) {
          this.$msg.success(res.msg)
        } else {
          this.$msg.error(res.msg)
        }
      })
    }
  },
  watch: {
    treeData: {
      handler(val) {
        // console.log(val)
      },
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">
.role_permission_wrap {
  .role_wrap {
    .el-checkbox-group {
      label {
        width: 100%;
        height: 35px;
        border: 1px solid #e5e5e5;
        line-height: 35px;
        font-size: 12px;
        margin: 5px 0;
        .el-checkbox__label {
          width: 80%;
          height: 35px;
          line-height: 35px;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: left;
        }
        &.is-checked {
          border-color: #449cff;
        }
        .el-checkbox__input {
          float: right;
          width: 33px;
          height: 32px;
          &.is-checked {
            .el-checkbox__inner {
              background: none;
            }
            & + .el-checkbox__label {
              color: #449cff;
            }
          }
          .el-checkbox__inner {
            width: 100%;
            height: 100%;
            border: none;
            &::after {
              top: 4px;
              left: 12px;
              width: 10px;
              height: 16px;
              font-size: 30px;
              border-width: 2px;
              border-color: #449cff;
            }
          }
        }
      }
    }
    .select_post {
      width: 80px;
      padding: 5px 20px;
      text-align: center;
      line-height: 20px;
      color: #fff;
      background: #66b1ff;
      margin-right: 5px;
    }
  }
  .line_wrap {
    margin: 0 0 10px 0;
    line-height: 40px;
    div {
      flex: 1;
    }
  }
  .related_staff_wrap {
    .el-radio-group {
      width: 100%;
      .el-radio {
        margin: 5px 0;
        width: 100%;
        height: 35px;
        border: 1px solid #e5e5e5;
        line-height: 35px;
        font-size: 12px;
        border-radius: 0;
        .el-radio__input {
          float: right;
          margin: -8px 0 0;
          height: 35px;
          line-height: 35px;
        }
        .el-radio__label {
          float: left;
          margin: -8px 0 0;
          padding: 0;
          width: 85%;
          height: 35px;
          line-height: 35px;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: left;
          font-size: 14px;
        }
      }
    }
  }
  .page_section_main {
    .page_section_aside {
      margin: 0;
      width: 235px;
      .aside_tree_list {
        height: 600px;
      }
    }
    .power_set_wrap {
      margin: 0 16px;
      min-width: 630px;
      .aside_tree_title {
        width: 100%;
        .tree_title {
          width: 100%;
        }
        .power_title {
          display: inline-block;
          margin: 0 200px 0 0;
        }
      }
      .aside_tree_list {
        overflow: auto;
      }
    }
  }
  .align_center {
    margin: 15px 0;
  }
}
</style>
