<template>
    <div class="report_wrap">
        <div class="page_main_title">
            <!-- 人才盘点报告(个人盘点) -->
            {{ enqName }}--盘点报告(个人报告) {{ beginDate | removeTime }}

            <div class="goback_geader" @click="goback">
                <i class="el-icon-arrow-left"></i>返回
            </div>
            <el-button
                type="primary"
                class="marginL_16"
                size="mini"
                @click="downloadPdf"
                >导出PDF</el-button
            >
        </div>
        <div class="page_section">
            <div class="page_section_center clearfix">
                <div class="report_header_info">
                    <span class="name">{{ userInfo.userName }}</span>
                    <span class>{{ userInfo.orgName }}</span>
                    <span class="marginR_16 marginL_16">|</span>
                    <span class>{{ userInfo.postName }}</span>
                </div>
                <div class="step_bar">
                    <step-bar
                        :stepData="stepData"
                        :needClick="true"
                        @stepClick="stepClick"
                        :currentIndex="currentModuleCode"
                    ></step-bar>
                </div>
                <keep-alive>
                    <component
                        :is="moduleObj[currentModuleCode]"
                        :enqId="enqId"
                        :userId="userId"
                        :postCode="postCode"
                        :nextBtnText="nextBtnText"
                    ></component>
                </keep-alive>
                <div class="oper_btn_wrap">
                    <el-button
                        class="page_confirm_btn"
                        type="primary"
                        @click="prevStep()"
                        v-if="this.currentIndex > 0"
                        >上一页</el-button
                    >
                    <el-button
                        class="page_confirm_btn"
                        type="primary"
                        @click="goback"
                        v-if="this.currentIndex == this.stepData.length - 1"
                        >返回报告列表</el-button
                    >
                    <el-button
                        class="page_confirm_btn"
                        type="primary"
                        @click="nextStep()"
                        v-else
                        >下一页</el-button
                    >
                </div>
            </div>
        </div>
        <user-report-to-pdf
            class="report_pdf_dom"
            :class="{ show: showPdf }"
            :moduleCodeArr="moduleArr"
        />
    </div>
</template>
 
<script>
    import {
        getUserReportUserInfo,
        getPersonalModule,
        getPersonalModuleInfo,
        getEnqInfo
    } from "../../../request/api";
    import stepBar from "@/components/talent/stepsComps/stepBar";
    import userReportToPdf from "./userReportPdf.vue";

    import userRBasicInfo from "./userReportComps/userRBasicInfo";
    import userRDuty from "./userReportComps/userRDuty";
    import userRActivities from "./userReportComps/userRActivities";
    import userREduInfo from "./userReportComps/userREduInfo";
    import userRWorkExperience from "./userReportComps/userRWorkExperience";
    import userRKpi from "./userReportComps/userRKpi";
    import userRTalentEval from "./userReportComps/userRTalentEval";
    import userRTrainInfo from "./userReportComps/userRTrainInfo";
    import userRAwardInfo from "./userReportComps/userRAwardInfo";
    import userRPersonalPlan from "./userReportComps/userRPersonalPlan";
    import userRSelfPlan from "./userReportComps/userRSelfPlan"; //个人规划
    import userRMatche from "./userReportComps/userRMatche.vue"; //任职匹配
    import userRQualityEval from "./userReportComps/userRQualityEval.vue"; //素质评价
    import userRPerformanceEval from "./userReportComps/userRPerformanceEval.vue"; //业绩评价
    import userRTargetResult from "./userReportComps/userRTargetResult.vue"; //目标结果
    import userRKPICur from "./userReportComps/userRKPICur.vue"; //kpi评价
    import userRRisk from "./userReportComps/userRRisk"; // 离职风险
    import userREngagement from "./userReportComps/userREngagement"; //敬业度
    import userRWorkDrive from "./userReportComps/userRWorkDrive"; //工作驱动
    import userRSynergy from "./userReportComps/userRSynergy"; //协同网络
    import userRTalent from "./userReportComps/userRTalent"; //人才分类

    const moduleObj = {
        PN01: userRBasicInfo,
        // P01: userRQualityEval,
        // PN02: userRActivities,
        PN02: userREduInfo,
        PN03: userRWorkExperience,
        // PN04: userRKpi,
        PN04: userRTrainInfo,
        PN05: userRAwardInfo,
        PN06: userRSelfPlan,
        PN07: userRMatche,
        PN08: userRQualityEval,
        // PN10: userRDuty,
        PN09: userRPerformanceEval,
        PN10: userRTargetResult,
        PN11: userRKPICur,
        PN12: userRRisk,
        PN13: userREngagement,
        PN14: userRWorkDrive,
        PN15: userRSynergy,
        PN16: userRTalent,
    };
    export default {
        name: "userReport",
        components: { stepBar, userReportToPdf },
        data() {
            return {
                showPdf: false,
                pdfDomIdArr:['pdf_cover'],
                userInfo: {
                    userName: "",
                    orgName: "",
                    postName: "",
                },
                enqId: null,
                enqName: "",
                beginDate:null,
                postCode: null,
                queryUserId: null,
                viewType: "p",
                // userId: null,
                moduleObj: moduleObj,
                moduleArr: [], //按顺序储存盘点模块的code
                currentModuleCode: null, //当前显示的模块code
                currentIndex: 0, //用来改变当前显示的模块code，从moduleArr中获取
                nextBtnText: "下一步",
                stepData: [],
            };
        },
        created() {
            let query = this.$route.query;
            this.enqId = query.enqId;
            this.postCode = query.postCode;
            // console.log(query)
            this.queryUserId = query.userId;
            this.viewType = query.viewType;
            console.log(this.postCode);
            this.getEnqModuleInfoFun();
            this.getUserReportUserInfoFun();
            this.getEnqInfoFn();
        },
        computed: {
            userId() {
                if (this.queryUserId) {
                    console.log("带有参数userId");
                    return this.queryUserId;
                } else {
                    console.log("没有有参数userId，取vuex");
                    return this.$store.state.userInfo.userId;
                }
            },
        },
        methods: {
            downloadPdf() {
                let that = this;
                this.showPdf = true;
                this.$loading({ fullscreen: true });
                setTimeout(() => {
                    
                    // 多页 && 按模块分页
                    // this.toPdf(
                    //     this.pdfDomIdArr,
                    //     "人才盘点报告(个人盘点)",
                    //     true,
                    //     function () {
                    //         that.showPdf = false;
                    //         that.$loading().close();
                    //     }
                    // );
                    // 单页、多页pdf生成
                    // this.getPDF({
                    //     domId:'downloadPdf',
                    //     title:this.enqName +
                    //         "--个人盘点报告(" +
                    //         this.userInfo.userName +
                    //         ")",
                    //     singlepage: true,
                    //     callback:function () {
                    //         that.showPdf = false;
                    //         that.$loading().close();
                    //     },
                    //     err:(err)=>{
                    //         console.log(err);
                    //     }
                    // });

                    this.getPDF({
                        domId:this.pdfDomIdArr,
                        title:this.enqName +
                            "--个人盘点报告(" +
                            this.userInfo.userName +
                            ")",
                        singlepage: false,
                        moduleCut: true,
                        hasCover:true,
                        callback:function () {
                            that.showPdf = false;
                            that.$loading().close();
                        },
                        err:(err)=>{
                            console.log(err);
                        }
                    });
                });
            },
            goback() {
                if (this.viewType == "p") {
                    this.$router.go(-1);
                } else {
                    this.$router.push({
                        path: "/talentReviewHome/talentReviewAnalysis/TRreport/listView",
                        query: {
                            useCache: true,
                        },
                    });
                }
            },
            stepClick(stepCode, index) {
                this.currentIndex = index;
                this.currentModuleCode = stepCode;
            },
            getUserReportUserInfoFun() {
                let params = {
                    enqId: this.enqId,
                    userId: this.userId,
                    postCode: this.postCode,
                };
                getUserReportUserInfo(params).then((res) => {
                    if (res.code == "200") {
                        this.userInfo = res.data;
                        // this.postCode = res.data.postCode;
                    }
                });
            },
            // 获取顶部tab
            getEnqModuleInfoFun() {
                getPersonalModuleInfo({
                    enqId: this.enqId,
                    type: "P",
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        res.data.forEach((item) => {
                            item["name"] = item.reportModuleName;
                            item["code"] = item.reportModuleCode;
                            item["enqProgress"] = "Y";
                            this.moduleArr.push(item.reportModuleCode);
                            this.pdfDomIdArr.push("pdf_module_"+item.reportModuleCode)
                        });
                        this.currentModuleCode = this.moduleArr[this.currentIndex];
                        this.stepData = res.data;
                    }
                });
            },
            getEnqInfoFn() {
                getEnqInfo({ id: this.enqId }).then((res) => {
                    this.enqName = res.data.enqName;
                    this.beginDate = res.data.beginDate;
                });
            },
            nextStep: function () {
                if (this.currentIndex == this.stepData.length - 1) {
                    // 最后一步提交
                    return false;
                }
                this.stepData[this.currentIndex].enqProgress = "Y";
                this.currentIndex++;
                // 设置按钮文本 “下一步” or 返回报告列表
                if (this.currentIndex == this.stepData.length - 1) {
                    this.nextBtnText = "返回报告列表";
                } else {
                    this.nextBtnText = "下一步";
                }
                this.currentModuleCode = this.moduleArr[this.currentIndex];
            },
            prevStep: function () {
                if (this.currentIndex == 0) {
                    return false;
                }
                this.currentIndex--;
                this.currentModuleCode = this.moduleArr[this.currentIndex];
            },
            // goback(){
            //     this.$router.go(-1)
            // }
        },
    };
</script>
 
<style scoped lang="scss">
    .report_header_info {
        color: #212121;
        font-size: 14px;
        margin-bottom: 16px;
        .name {
            color: #0099ff;
            font-size: 16px;
            margin-right: 14px;
            font-weight: bold;
        }
    }
    .page_second_title {
        position: relative;
        margin-bottom: 20px;
        line-height: 34px;
        color: #0099ff;
        z-index: 2;
        font-weight: bold;
        &::after{
            content:'';
            position: absolute;
            left: 0;
            top:0;
            height: 100%;
            width: 200px;
            background: #e5f7fd;
            z-index: -1;
        }

    }
    .oper_btn_wrap {
        text-align: center;
        padding-top: 16px;
    }
    // 去除select下拉箭头
    .el-input__suffix {
        display: none;
    }
    .report_pdf_dom {
        position: fixed;
        top: 0;
        left: 0;
        z-index: -1;
        opacity: 0;
        background: #fff;
        &.show {
            opacity: 1;
        }
    }
    .report_section{
        padding: 40px 0;
    }
</style>