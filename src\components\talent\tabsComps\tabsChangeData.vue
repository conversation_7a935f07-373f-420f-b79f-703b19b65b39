// tab切换更改数据，相同的tab-pane内容

<template>
  <div class="tabs_wrap">
    <el-tabs :value="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="tab in tabsData" :key="tab.label" :label="tab.label" :name="tab.name"></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
const props = defineProps({
  tabsData: Array,
  activeName: String,
  handleClick: Function
})
</script>

<style scoped lang="scss">
.tabs_wrap {
  width: 100%;
}
</style>
