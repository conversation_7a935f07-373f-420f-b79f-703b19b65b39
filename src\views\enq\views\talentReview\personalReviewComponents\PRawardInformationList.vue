<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in performanceInfoData" :key="item.id">
      <el-input class="item" v-model="item.awardName" placeholder="填写获奖名称"></el-input>
      <el-input class="item" v-model="item.awardIssuer" placeholder="填写颁发单位"></el-input>
      <el-date-picker
        class="item"
        value-format="YYYY-MM-DD"
        v-model="item.awardDate"
        type="date"
        placeholder="选择日期"
      ></el-date-picker>
      <el-select class="item" v-model="item.awardType">
        <el-option
          v-for="opt in awardTypeOptions"
          :key="opt.dictCode"
          :label="opt.codeName"
          :value="opt.dictCode"
        ></el-option>
      </el-select>
      <el-select class="item" v-model="item.currentCompany">
        <el-option v-for="opt in yesOrNo" :key="opt.dictCode" :label="opt.codeName" :value="opt.dictCode"></el-option>
      </el-select>
      <div class="item item_icon_wrap">
        <el-icon class="item_icon" @click="() => deleteItem(item, index)"><Delete /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { Delete } from '@element-plus/icons-vue'
const userStore = useUserStore()

const props = defineProps({
  performanceInfoData: {
    type: Array,
    default: () => [
      {
        id: '1',
        schoolName: '',
        graduationDate: '',
        education: '',
        post: '',
        industry: ''
      }
    ]
  }
})
const emit = defineEmits(['deleteItem'])

const awardTypeOptions = ref([])
const yesOrNo = ref([])

onMounted(async () => {
  // 你需要根据实际情况引入或获取$getDocList
  // 这里假设window.$getDocList可用，否则请import
  const res = await userStore.getDocList(['AWARD_TYPE', 'YES_NO'])
  awardTypeOptions.value = res.AWARD_TYPE
  yesOrNo.value = res.YES_NO
})

function deleteItem(item, index) {
  emit('deleteItem', item, index)
}
</script>

<style scoped lang="scss">
.edu_info_item {
  .item {
    // flex: 1;
    width: 20%;
  }
  .item_icon_wrap {
    color: #0099fd;
    font-size: 20px;
    text-align: center;
    width: 10%;
    padding-top: 2px;
  }
}
</style>
