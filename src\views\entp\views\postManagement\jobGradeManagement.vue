<template>
  <div class="job_grade_wrap bg_write">
    <div class="page_main_title">职等管理</div>
    <div class="page_section">
      <div class="page_section job_grade_center clearfix">
        <div class="filter_bar_wrap">
          <div class="flex_row_start">
            <div class="filter_item title">筛选</div>
            <div class="filter_item">
              <el-select v-model="state" placeholder="选择状态" clearable>
                <el-option label="启用" value="Y"></el-option>
                <el-option label="未启用" value="N"></el-option>
              </el-select>
            </div>
            <div class="filter_item">
              <el-input v-model="filterName" placeholder="按名称模糊查询" suffix-icon="el-icon-search"></el-input>
            </div>
            <div class="filter_item">
              <el-button class="page_add_btn" type="primary" @click="getJobGradePageFun">查询</el-button>
            </div>
          </div>
          <div class="filter_item">
            <el-button class="page_add_btn" type="primary" @click="tableAdd">新增</el-button>
          </div>
        </div>
        <table-component
          :tableData="tableData"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        >
          <template #oper>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button @click.prevent="tableEdit(scope)" link icon="el-icon-edit" class="icon_edit"></el-button>
                <el-button
                  class="color_danger icon_del"
                  @click.prevent="tableDeleteRow(scope)"
                  link
                  icon="el-icon-delete"
                ></el-button>
              </template>
            </el-table-column>
          </template>
        </table-component>
      </div>
    </div>
    <div class="dialog">
      <el-dialog :title="dialogTitle" v-model="dialogVisible" width="40%">
        <div class="dialog_form">
          <el-form ref="formRef" :model="form" label-width="100px">
            <el-form-item label="职等名称">
              <el-input v-model="form.jobGradeName"></el-input>
            </el-form-item>
            <el-form-item label="职等描述">
              <el-input v-model="form.jobGradeDesc"></el-input>
            </el-form-item>
            <el-form-item label="是否启用" v-if="dialogType == 'update'">
              <el-select v-model="form.status">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="排序号">
              <el-input
                type="number"
                v-model="form.sortNbr"
                oninput="if(value.length>4) value=value.slice(0,4)"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <el-button class="page_clear_btn" @click="cancelSequence">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="confirmAddGrade">确 定</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import { addJobGrade, updateJobGrade, delJobGrade, getJobGradePage } from '../../request/api'

const dialogVisible = ref(false)
const dialogTitle = ref('新增职等')
const dialogType = ref('add')
const state = ref('')
const filterName = ref('')
const formRef = ref(null)
const form = reactive({
  jobGradeCode: '',
  jobGradeName: '',
  jobGradeDesc: '',
  status: 'Y',
  sortNbr: ''
})
const tableData = reactive({
  columns: [
    {
      label: '序号',
      prop: 'index',
      width: '50'
    },
    {
      label: '职等编码',
      prop: 'jobGradeCode'
    },
    {
      label: '职等名称',
      prop: 'jobGradeName'
    },
    {
      label: '职等描述',
      prop: 'jobGradeDesc',
      width: '300'
    },
    {
      label: '状态',
      prop: 'statusName',
      width: '80'
    },
    {
      label: '创建人',
      prop: 'createUserName'
    },
    {
      label: '创建时间',
      prop: 'rcreateTime'
    }
  ],
  data: [],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})
const page = reactive({
  current: 1,
  size: 10,
  total: 0
})
const dataLength = ref(0)

onMounted(() => {
  getJobGradePageFun()
})

function tableAdd() {
  dialogVisible.value = true
  dialogType.value = 'add'
  dialogTitle.value = '新增职等'
  form.jobGradeCode = ''
  clearForm()
}
function tableEdit(scope) {
  dialogType.value = 'update'
  dialogTitle.value = '修改职等'
  dialogVisible.value = true
  form.jobGradeCode = scope.row.jobGradeCode
  form.jobGradeName = scope.row.jobGradeName
  form.jobGradeDesc = scope.row.jobGradeDesc
  form.status = scope.row.rstatus
  form.sortNbr = scope.row.sortNbr
}
function tableDeleteRow(scope) {
  ElMessageBox.confirm('确认删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      delJobGradeFun(scope.row.jobGradeCode)
    })
    .catch(() => {})
}
function confirmAddGrade() {
  if (!form.jobGradeName) {
    ElMessage.warning('请输入职等名称')
    return
  }
  if (!form.jobGradeDesc) {
    ElMessage.warning('请输入职等描述')
    return
  }
  if (!form.sortNbr) {
    ElMessage.warning('请输入排序号')
    return
  }
  let params = JSON.parse(JSON.stringify(form))
  if (dialogType.value == 'add') {
    addJobGradeFun(params)
  } else {
    updateJobGradeFun(params)
  }
}
function cancelSequence() {
  dialogVisible.value = false
  clearForm()
}
function clearForm() {
  form.jobGradeCode = ''
  form.jobGradeName = ''
  form.jobGradeDesc = ''
  form.status = 'Y'
  form.sortNbr = ''
}
function handleSizeChange(size) {
  page.current = 1
  page.size = size
  getJobGradePageFun()
}
function handleCurrentChange(p) {
  page.current = p
  getJobGradePageFun()
}
function getJobGradePageFun() {
  let params = {
    jobGradeName: filterName.value,
    status: state.value,
    current: page.current,
    size: page.size
  }
  getJobGradePage(params).then(res => {
    if (res.code == 200 && res.data) {
      if (res.data) {
        let newList = []
        res.data.map((item, index) => {
          newList.push(
            Object.assign({}, item, {
              index: index + 1,
              statusName: item.rstatus == 'Y' ? '启用' : '未启用'
            })
          )
        })
        tableData.data = newList
        tableData.page.total = res.total
        dataLength.value = res.data.length
      } else {
        tableData.data = []
        tableData.page.total = res.total
        dataLength.value = 0
      }
    } else {
      ElMessage.warning(res.msg)
    }
  })
}
function addJobGradeFun(params) {
  addJobGrade(params).then(res => {
    if (res.code == 200) {
      dialogVisible.value = false
      ElMessage.success('新增成功！')
      clearForm()
      getJobGradePageFun()
    } else {
      ElMessage.warning(res.msg)
    }
  })
}
function updateJobGradeFun(params) {
  updateJobGrade(params).then(res => {
    if (res.code == 200) {
      dialogVisible.value = false
      ElMessage.success('修改成功！')
      clearForm()
      getJobGradePageFun()
    } else {
      ElMessage.warning(res.msg)
    }
  })
}
function delJobGradeFun(code) {
  let params = {
    jobGradeCode: code
  }
  delJobGrade(params).then(res => {
    if (res.code == 200) {
      ElMessage.success('删除成功！')
      if (dataLength.value == 1) {
        page.current > 1 && page.current--
      }
      getJobGradePageFun()
    } else {
      ElMessage.warning(res.msg)
    }
  })
}
</script>

<style scoped lang="scss">
.job_grade_wrap {
  .dialog {
    .el-select {
      width: 100%;
    }
  }
}
</style>
