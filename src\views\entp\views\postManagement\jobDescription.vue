<template>
  <div class="post_description_wrap bg_write">
    <div class="page_main_title">职位说明书</div>
    <div class="page_section">
      <div class="page_section_aside fl">
        <div class="aside_tree_title">
          <div>筛选</div>
        </div>
        <div class="aside_tree_list">
          <tree-comp-radio
            :treeData="treeData"
            :needCheckedFirstNode="false"
            :canCancel="true"
            @clickCallback="clickCallback"
          ></tree-comp-radio>
        </div>
      </div>
      <div class="page_section_main padd_TB_16 clearfix">
        <div class="flex_row_wrap_start">
          <div
            class="post_description_item"
            v-for="item in postDescData"
            :key="item.postCode"
            @click="postInfo(item.postCode)"
          >
            <div class="title">{{ item.postName }}</div>
            <div class="title org_name">{{ item.orgName }}</div>
            <div class="text">职位说明书</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useStore } from 'vue-router'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import { getOrgDeptTree, getPostList } from '../../request/api'

const treeData = ref([])
const orgCode = ref('')
const postDescData = ref([])
const store = useStore ? useStore() : null
const router = useRouter()

const companyId = computed(() => store?.state.userInfo.companyId)

function getOrgTreeFun() {
  getOrgDeptTree({
    companyId: companyId.value
  }).then(res => {
    if (res.code == 200) {
      treeData.value = res.data.length > 0 ? res.data : []
    } else {
      treeData.value = []
    }
  })
}
function clickCallback(val, isLastNode) {
  orgCode.value = val
  getPostListFun()
}
function getPostListFun() {
  getPostList({
    orgCode: orgCode.value
  }).then(res => {
    postDescData.value = []
    if (res.code == 200) {
      if (res.data.length > 0) {
        postDescData.value = res.data.map(item => {
          return {
            postName: item.postName,
            postCode: item.postCode,
            orgName: item.orgName
          }
        })
      }
    }
  })
}
function postInfo(code) {
  router.push({
    path: '/basicSettingHome/postManagement/postDescription/postDescriptionDetailed',
    query: { postCode: code }
  })
}

watch(
  companyId,
  val => {
    if (val) {
      getOrgTreeFun()
    }
  },
  { immediate: true }
)

onMounted(() => {
  getPostListFun()
})
</script>

<style scoped lang="scss">
.post_description_item {
  position: relative;
  width: 120px;
  height: 150px;
  padding: 15px 0;
  box-shadow: 2px 2px 8px #ccc;
  margin: 0 15px 10px;
  cursor: pointer;
  /*background: #f8f8f8;*/
  .title {
    /*height: 40px;*/
    font-size: 12px;
    color: #666;
    line-height: 20px;
    padding: 0 8px;
  }
  .org_name {
    color: #515e6c;
    font-weight: bold;
  }
  .text {
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 20px;
    font-size: 12px;
    height: 30px;
    line-height: 30px;
    color: #0099ff;
    background: #ebf4ff;
    padding: 0 5px;
    text-align: center;
  }
}
</style>
