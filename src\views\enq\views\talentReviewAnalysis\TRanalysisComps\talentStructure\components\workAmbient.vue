<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">加班频率</div>
          <div class="content_item_content" id="overtime"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">出差频率</div>
          <div class="content_item_content" id="travel"></div>
        </div>
      </div>
      <!-- <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">具体岗位</div>
                    <div class="content_item_content">
                        <div class="post_list_wrap">
                            <div class="post_list" v-for="list in postList" :key="list.code">{{list.name}}</div>
                        </div>
                    </div>
                </div>
            </div> -->
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            工作环境分析
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { workAmbient, environmentPost, queryEnvironmentList, exportData } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref([])
const postList = ref([])
const wenType = ref('')
const dictCode = ref('')
const page = ref(1)
const size = ref(10)

const overtime = reactive({
  addEvent: true,
  defaultChecked: true,
  data: []
})

const travel = reactive({
  addEvent: true,
  data: []
})

const tableData = reactive({
  columns: [
    {
      label: '职位名称',
      prop: 'job_name'
    },
    {
      label: '所属组织',
      prop: 'org_name'
    },
    {
      label: '职位族群',
      prop: 'parent_job_class_name'
    },
    {
      label: '职位序列',
      prop: 'job_class_name'
    },
    {
      label: '职层',
      prop: 'job_level_name'
    },
    {
      label: '职等',
      prop: 'job_grade_name'
    },
    {
      label: '加班频率',
      prop: 'overtimeCode'
    },
    {
      label: '出差频率',
      prop: 'travelCode'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('overtime', 'YBar', '350', '250', overtime, chartCallbackOvertime)
  echartsRenderPage('travel', 'YBar', '350', '250', travel, chartCallbackTravel)
}

const workAmbientFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await workAmbient(params)
    if (res.code == 200) {
      const data = res.data
      overtime.data = window.$util.addPercentSign(data.overtime, 'value')
      wenType.value = 'O'
      if (overtime.data.length > 0) {
        dictCode.value = overtime.data[0].code
      }
      await environmentPostFun()
      travel.data = window.$util.addPercentSign(data.travel, 'value')
      initChart()
    }
  } catch (error) {
    console.error(error)
  }
}

const chartCallbackOvertime = data => {
  wenType.value = 'O'
  dictCode.value = overtime.data[data.dataIndex].code
  echartsRenderPage('travel', 'YBar', '350', '250', travel, chartCallbackTravel)
  environmentPostFun()
}

const chartCallbackTravel = data => {
  wenType.value = 'T'
  dictCode.value = travel.data[data.dataIndex].code
  echartsRenderPage('overtime', 'YBar', '350', '250', overtime, chartCallbackOvertime)
  environmentPostFun()
}

const environmentPostFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      wenType: wenType.value,
      dictCode: dictCode.value
    }
    const res = await environmentPost(params)
    if (res.code == 200) {
      postList.value = res.data.post
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  page.value = 1
  workAmbientFun()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryEnvironmentList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error(error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'm'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '工作环境分析')
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  workAmbientFun()
  getTableData()
})
</script>

<style scoped lang="scss"></style>
