<template>
    <div class="">
        <div class="page_third_title">
            评价关系详情
            <div class="fr">
                <el-button type="primary" @click="exportExcel" size="mini"
                    >导出</el-button>
            </div>
        </div>
        <table-component
            :tableData="tableData"
            :needIndex="true"
            :loading="loading"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
        ></table-component>
    </div>
</template>
 
<script>
    import { relationshipDetails, allExportData } from "../../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "evalRelationshipAnalysisDetails",
        props: ["orgCode", "evalId"],
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading: false,
                tableData: {
                    columns: [
                        {
                            label: "姓名",
                            prop: "userName",
                        },
                        {
                            label: "岗位",
                            prop: "userPostName",
                        },
                        {
                            label: "被评人员",
                            prop: "objectName",
                        },
                        {
                            label: "被评人员岗位",
                            prop: "objectPostName",
                        },
                        {
                            label: "能力模块",
                            prop: "moduleName",
                        },
                        {
                            label: "得分",
                            prop: "actualScore",
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        components: {
            tableComponent,
        },
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
        },

        methods: {
            getData() {
                this.loading = true;
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                relationshipDetails(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                        this.loading = false;
                    } else {
                        this.loading = false;
                    }
                });
            },
            handleSizeChange(size) {
                this.pageSize = size;
                this.getData();
            },
            handleCurrentChange(current) {
                this.currPage = current;
                this.getData();
            },
            exportExcel() {
                let params = {
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                    type: "relationship",
                };
                allExportData(params).then((res) => {
                    console.log(res);
                    this.$exportDownload(res.data, "评价关系详情");
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>