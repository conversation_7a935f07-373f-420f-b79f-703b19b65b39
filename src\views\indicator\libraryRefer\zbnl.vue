<script setup>
import TabLeft from '../components/tabLeft.vue'
import ChatInterpret from '@/components/AI/chatInterpret.vue'
import { cancelRequest } from '@/api/request.js'
import { indicatorCategoryList, indicatorAbilityList } from '@/api/modules/indicator.js'
import { useDialogueStore } from '@/stores'
defineOptions({ name: 'indicatorInfo' })

const tabList = ref([])
const activeTab = ref('')
const handleTab = data => {
  activeTab.value = data.key
  currentPage.value = 1
  getTableList()
  currentChat.value = {}
  // 结束请求
  cancelRequest(`post/agent/aiDiagnosis${useDialogueStore().lastData}`)
}
const tableData = ref([])

const getTabList = async () => {
  indicatorCategoryList().then(res => {
    if (res.code == 200) {
      tabList.value = res.data
      activeTab.value = res.data[0].children.length ? res.data[0].children[0].code : res.data[0].code
      getTableList()
    } else {
      tabList.value = []
      activeTab.value = ''
    }
  })
}

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0) // 总页数
const handleSizeChange = val => {
  currentPage.value = 1
  pageSize.value = val
  getTableList()
}
const handleCurrentChange = val => {
  currentPage.value = val
  getTableList()
}
const loading = ref(false)
const getTableList = () => {
  loading.value = true
  indicatorAbilityList({ kpiCode: activeTab.value, pageNum: currentPage.value, pageSize: pageSize.value })
    .then(res => {
      if (res.code == 200) {
        tableData.value = res.data
        total.value = res.total
      } else {
        tableData.value = []
        total.value = 0
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const currentChat = ref({})
const chatRef = ref()
const getChat = row => {
  currentChat.value = row
  nextTick(() => {
    currentChat.value = row
    setTimeout(() => {
      chatRef.value.isNewConversation(row.id)
    }, 0)
  })
}
onMounted(() => {
  getTabList()
})
</script>
<template>
  <div class="page-container">
    <div class="left">
      <div class="page-title-line">查看指标类别</div>
      <TabLeft :data="tabList" v-model:active="activeTab" @tab-click="handleTab"></TabLeft>
    </div>
    <div class="right">
      <div class="page-title-line">查看指标</div>
      <div class="app-table">
        <el-table :data="tableData" v-loading="loading">
          <el-table-column type="index" label="序号" width="80" align="center" />
          <el-table-column prop="abilityName" label="重点关联的能力" width="200"></el-table-column>
          <el-table-column prop="abilityImprovePath" label="能力改善路径"></el-table-column>
          <el-table-column width="190">
            <template #default="scope">
              <el-button class="operate-btn" @click="getChat(scope.row)">AI解读</el-button>
              <el-button class="operate-btn">能力评估</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-card">
        <el-pagination
          class="ai-pagination"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :background="false"
          layout="sizes, prev, pager, next, total"
          :total="tableData.length"
          :hide-on-single-page="false"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <div class="ai-title" v-if="currentChat.id">
        已选指标：<span>{{ currentChat.abilityName }}</span>
      </div>
      <div class="ai-content p-[18px]" v-show="currentChat.id">
        <ChatInterpret ref="chatRef" type="C"></ChatInterpret>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.page-container {
  @include flex-center(row, flex-start, flex-start);
  gap: 20px;
  .page-title-line {
    margin-bottom: 12px;
  }
  .left {
    width: 180px;
    flex-shrink: 0;
  }
  .right {
    width: calc(100% - 200px);
    .app-table {
      border-radius: 10px;
      border: 1px solid #c6dbf3;
      overflow: hidden;

      :deep(.el-table) {
        width: 100%;
        --el-font-size-base: 14px;
        --el-table-header-text-color: #93abcb;
        --el-table-border-color: #c6dbf3;
        --el-table-tr-bg-color: transparent;
        --el-table-row-hover-bg-color: #c6dbf3;
        --el-table-header-bg-color: transparent;
        background-color: transparent;
        // tr {
        //   height: 52px;
        // }
        .el-table__inner-wrapper:before {
          // 隐藏table底边框
          background-color: transparent;
        }
        tr:last-of-type {
          td {
            // 隐藏table底边框
            border-bottom: none;
          }
        }
      }
      .operate-btn {
        width: 68px;
        height: 24px;
        border-radius: 12px;
        border: 1px solid #40a0ff;
        color: #40a0ff;
        background-color: transparent;
        &:hover {
          background-color: #40a0ff;
          color: #fff;
        }
      }
    }
    .pagination-card {
      @include flex-center(row, flex-end, center);
      margin-top: 10px;
    }
    .ai-pagination {
      --el-pagination-bg-color: transparent;
      --el-pagination-button-disabled-bg-color: transparent;
      :deep(.el-select__wrapper) {
        background-color: transparent;
      }
      :deep(.el-select) {
        width: 100px;
      }
    }
    .ai-title {
      padding: 20px 0 9px;
      font-size: 14px;
      color: #94a1af;
      span {
        font-weight: 600;
        color: #3d3d3d;
      }
    }
    .ai-content {
      width: 100%;
      background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
      border-radius: 8px;
      border: 1px solid #c6dbf3;
    }
  }
}
</style>
