<template>
    <div class="work_activity_wrap">
        <div class="edu_info_header">
            <div class="item">工作活动类型</div>
            <div class="item">对应职位类别</div>
            <div class="item">工作任务名称</div>
            <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
            <div
                class="edu_info_item"
                v-if="workActivityList.length > 0"
                v-for="(item, index) in workActivityList"
                :key="item.id"
            >
                <el-select
                    class="item"
                    v-model="item.jobActivityType"
                    disabled
                    placeholder
                >
                    <el-option
                        v-for="activity in jobActivityList"
                        :label="activity.codeName"
                        :value="activity.dictCode"
                    ></el-option>
                </el-select>
                <el-input
                    class="item"
                    v-model="item.jobTypeName"
                    disabled
                    placeholder
                ></el-input>
                <el-input
                    class="item"
                    v-model="item.jobActivityName"
                    disabled
                    placeholder
                ></el-input>
                <div class="item item_icon_wrap">
                    <i
                        class="item_icon el-icon-delete icon_del"
                        @click="deleteItem(item, index)"
                    ></i>
                </div>
            </div>

            <div class="no_data_tip" v-if="workActivityList.length == 0">
                暂无数据
            </div>
            <div class="align_center paddT_12">
                <el-button
                    class="page_confirm_btn"
                    type="primary"
                    @click="addPostActivity"
                    >修改</el-button
                >
            </div>
        </div>

        <el-dialog
            title="选择工作活动"
            :visible.sync="dialogVisible"
            width="60%"
            center
        >
            <div class="page_section">
                <div class="page_section_aside">
                    <div class="aside_tree_title flex_row_between">
                        <div class="overflow_elps tree_title">岗位族群</div>
                    </div>
                    <div class="aside_tree_list">
                        <tree-comp-checkbox
                            :defaultCheckedKeys="defaultCheckedKeys"
                            :treeData="treeData"
                            @node-click-callback="nodeClickCallback"
                        >
                        </tree-comp-checkbox>
                    </div>
                </div>
                <div class="page_section_main page_shadow">
                    <table-component
                        :tableData="tableData"
                        :needIndex="false"
                        :needPagination="false"
                        :checkSelection="checkSelection"
                        :selectionStatus="true"
                        @selectionChange="selectionChange"
                        @curSelectInfo="curSelectInfo"
                    >
                    </table-component>
                </div>
            </div>
            <div slot="footer" class="dialog-footer align_right">
                <el-button class="page_clear_btn" @click="cancal"
                    >取 消</el-button
                >
                <el-button
                    class="page_add_btn"
                    type="primary"
                    @click="popUpSubmitBtn"
                    >保 存</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>
<script>
    import {
        jobActivityList,
        createJobActivity,
        delTitleActivity,
        getJobActivity,
        getDict,
        getJobClassTree,
    } from "../../../../request/api";
    import treeCompCheckbox from "@/components/talent/treeComps/treeCompCheckbox";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "workActivity",
        props: {
            jobCode: String,
        },
        components: {
            treeCompCheckbox,
            tableComponent,
        },
        data() {
            return {
                jobActivityList: [],
                workActivityList: [],
                // 弹窗数据
                dialogVisible: false,
                defaultCheckedKeys: [],
                treeData: [],
                tableData: {
                    columns: [
                        {
                            label: "岗位序列",
                            prop: "jobClassName",
                        },
                        {
                            label: "活动类型",
                            prop: "jobActivityTypeName",
                        },
                        {
                            label: "工作任务名称",
                            prop: "jobActivityName",
                        },
                    ],
                    data: [],
                },
                checkSelection: [],
                checkWorkActivityList: [],
                defaultCheckWorkActivityList: [],
            };
        },
        created() {
            this.getJobClassTreeFun();
            this.getJobActivityList();
        },
        mounted() {
            this.getDictFun();
        },
        methods: {
            // 工作活动类型字典表
            getDictFun() {
                let params = {
                    dictId: "JOB_ACTIVITY_TYPE",
                };
                getDict(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.jobActivityList = res.data;
                    }
                });
            },
            // 获取已配置工作活动列表 数据回显
            getJobActivityList() {
                this.workActivityList = [];
                jobActivityList({
                    jobCode: this.jobCode,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        if (res.data.length > 0) {
                            this.workActivityList = res.data.map((item) => {
                                return {
                                    jobActivityName: item.jobActivityName,
                                    jobActivityCode: item.jobActivityCode,
                                    jobActivityType: item.jobActivityType,
                                    jobClassName: item.jobClassName,
                                    jobClassCode: item.jobClassCode,
                                    jobTypeName: item.jobTypeName
                                };
                            });
                        }
                    }
                });
            },
            //新增岗位工作活动
            addPostActivity() {
                this.dialogVisible = true;
            },
            //获取族群树
            getJobClassTreeFun() {
                getJobClassTree({}).then((res) => {
                    // console.log(res);
                    if (res.length > 0) {
                        this.treeData = res;
                    } else {
                        this.treeData = [];
                    }
                });
            },
            nodeClickCallback(val, curCheckCode) {
                this.addDefaultCheckAct(val, curCheckCode);
                let jobClassCodes = val.join(",");
                this.getJobActivityFun(jobClassCodes);
            },
            // 如果当前为操作为勾选 则需将默认勾选活动再次进行勾选 否则不做操作
            addDefaultCheckAct(val, curCheckCode) {
                if (val.indexOf(curCheckCode) == -1) {
                    // 取消勾选
                } else {
                    //勾选
                    // console.log('默认勾选的族群',this.defaultCheckedKeys)
                    // console.log('当前勾选的所有族群',val)
                    // console.log('当前勾选的族群',curCheckCode)
                    // console.log('默认勾选的工作活动',this.defaultCheckWorkActivityList)
                    // console.log('当前勾选的工作活动',this.checkWorkActivityList)
                    for (let k = 0; k < val.length; k++) {
                        for (let j = 0; j < this.defaultCheckedKeys.length; j++) {
                            if (val[k] == this.defaultCheckedKeys[j]) {
                                for (
                                    let i = 0;
                                    i < this.defaultCheckWorkActivityList.length;
                                    i++
                                ) {
                                    if (
                                        val[k] ==
                                        this.defaultCheckWorkActivityList[i]
                                            .jobClassCode
                                    ) {
                                        console.log(val[k]);
                                        let hasSign = this.checkWorkActivityList.some(
                                            (item) => {
                                                if (
                                                    item.jobActivityCode ==
                                                    this
                                                        .defaultCheckWorkActivityList[
                                                        i
                                                    ].jobActivityCode
                                                ) {
                                                    return true;
                                                }
                                            }
                                        );
                                        if (!hasSign) {
                                            this.checkWorkActivityList.push(
                                                this.defaultCheckWorkActivityList[i]
                                            );
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // console.log('当前勾选的工作活动', this.checkWorkActivityList)
                }
            },
            // 获取企业工作活动列表
            getJobActivityFun(val) {
                getJobActivity({
                    jobClassCodes: val,
                }).then((res) => {
                    // console.log(res)
                    if (res.code == 200) {
                        this.tableData.data = res.data.map((item) => {
                            return {
                                jobActivityName: item.jobActivityName,
                                jobActivityCode: item.jobActivityCode,
                                jobActivityTypeName: item.jobActivityTypeName,
                                jobClassName: item.jobClassName,
                                jobClassCode: item.jobClassCode,
                            };
                        });
                    }
                    this.curCheckPosts();
                });
            },
            selectionChange(val) {
                // console.log(val)
                this.checkWorkActivityList = val;
            },
            // 当前勾选row
            curSelectInfo(selection, row) {
                let checkSign = selection.some((item) => {
                    if (item.jobActivityCode == row.jobActivityCode) {
                        return true;
                    }
                });
                if (checkSign) {
                    //  勾选
                    this.checkWorkActivityList.push(row);
                } else {
                    // 取消勾选
                    this.checkWorkActivityList.some((item, index) => {
                        if (item.jobActivityCode == row.jobActivityCode) {
                            this.checkWorkActivityList.splice(index, 1);
                            return;
                        }
                    });
                }
            },
            // 当前勾选岗位
            curCheckPosts() {
                let curCheckSelection = [];
                for (let i = 0; i < this.checkWorkActivityList.length; i++) {
                    for (let j = 0; j < this.tableData.data.length; j++) {
                        if (
                            this.checkWorkActivityList[i].jobActivityCode ==
                            this.tableData.data[j].jobActivityCode
                        ) {
                            curCheckSelection.push(this.tableData.data[j]);
                            break;
                        }
                    }
                }
                this.$nextTick(() => {
                    this.checkSelection = curCheckSelection;
                });
            },
            // 默认要勾选弹窗组织 岗位数据
            defaultCheckedTreeAndTable() {
                if (this.workActivityList.length == 0) {
                    this.defaultCheckedKeys = [];
                    this.checkSelection = [];
                    this.checkWorkActivityList = [];
                    this.tableData.data = [];
                    return;
                }
                let defaultJobClassCodes = [];
                if (this.workActivityList.length > 0) {
                    this.checkWorkActivityList = this.workActivityList;
                    this.defaultCheckWorkActivityList = this.checkWorkActivityList;
                    for (let i = 0; i < this.workActivityList.length; i++) {
                        if (
                            defaultJobClassCodes.indexOf(
                                this.workActivityList[i].jobClassCode
                            ) == -1
                        ) {
                            defaultJobClassCodes.push(
                                this.workActivityList[i].jobClassCode
                            );
                        }
                    }
                    this.getJobActivityFun(defaultJobClassCodes.join(","));
                    this.defaultCheckedKeys = defaultJobClassCodes;
                }
            },
            cancal() {
                this.dialogVisible = false;
            },
            popUpSubmitBtn() {
                this.createJobActivityFun();
            },
            // 新增、编辑工作活动列表
            createJobActivityFun() {
                let postActivityRequestList;
                if (this.checkWorkActivityList.length == 0) {
                    postActivityRequestList = [
                        {
                            jobActivityCode: "",
                            // jobActivityName:'',
                            // jobActivityType:'',
                            jobCode: this.jobCode,
                            // sortNbr:'',
                        },
                    ];
                } else {
                    postActivityRequestList = this.checkWorkActivityList.map(
                        (item) => {
                            return {
                                jobActivityCode: item.jobActivityCode,
                                // jobActivityName:item.jobActivityName,
                                // jobActivityType:item.jobActivityType,
                                jobCode: this.jobCode,
                                // sortNbr:item.sortNbr,
                                // jobClassName:item.jobClassName,
                            };
                        }
                    );
                }
                createJobActivity(postActivityRequestList).then((res) => {
                    if (res.code == 200) {
                        this.dialogVisible = false;
                        this.getJobActivityList();
                        this.$msg.success(res.msg);
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
            // 删除工作活动
            deleteItem(item, index) {
                // console.log(item,index)
                this.$confirm("确定删除?", "确定", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.deleteJobActivityFun(item.jobActivityCode);
                    })
                    .catch(() => {});
            },
            // 删除工作活动接口
            deleteJobActivityFun(val) {
                delTitleActivity({
                    jobActivityCode: val,
                    jobCode: this.jobCode,
                }).then((res) => {
                    // console.log(res)
                    if (res.code == 200) {
                        this.$msg.success(res.msg);
                        this.getJobActivityList();
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
        },
        watch: {
            dialogVisible(val) {
                if (!val) {
                    // this.coopPostCode = ''
                } else {
                    // 打开弹窗
                    this.defaultCheckedTreeAndTable();
                }
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .aside_tree_title {
        margin-top: 0;
    }
    .edu_info_header {
        .item {
            width: 32%;
            padding: 0 8px;
        }
        .item_icon_wrap {
            text-align: center;
            width: 8%;
        }
    }

    .work_activity_wrap {
        .edu_info_item {
            display: flex;
            flex-flow: row nowrap;
            justify-content: space-between;
            .item {
                width: 32%;
            }
            .item_icon_wrap {
                text-align: center;
                width: 8%;
                .item_icon {
                    margin-top: 5px;
                }
            }
        }
    }

    .el-dialog {
        .el-dialog__body {
            padding: 0 5px;
            .page_section {
                .page_section_aside {
                    width: 25%;
                    .aside_tree_title {
                        .tree_title {
                            width: 100%;
                        }
                    }
                    .aside_tree_list {
                        height: 350px !important;
                        padding: 10px 5px;
                    }
                }
                .page_section_main {
                    height: 380px;
                    min-height: 386px;
                    flex: 1;
                }
            }
        }
    }
</style>