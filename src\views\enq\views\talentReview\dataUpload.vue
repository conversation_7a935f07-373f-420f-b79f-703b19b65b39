<template>
  <div class="launch_review_wrap bg_write">
    <div class="page_main_title">
      数据上传
      <div class="goback_geader" @click="goback"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section">
      <div class="launch_review_center clearfix">
        <step-bar
          :stepData="stepData"
          :needClick="!isEdit"
          @stepClick="stepClick"
          :currentIndex="currentModuleCode"
        ></step-bar>
        <component
          :isEdit="isEdit"
          :getEnqId="getEnqId"
          :is="moduleObj[currentModuleCode]"
          @nextStep="nextStep"
          @prevStep="prevStep"
        ></component>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getEnqInfo } from '../../request/api'
import stepBar from '@/components/talent/stepsComps/stepBar'
import dataUploadKPI from './dataUploadComponents/dataUploadKPI'
import dataUploadResult from './dataUploadComponents/dataUploadResult'

const route = useRoute()
const isEdit = ref(true)
const moduleArr = ref(['L01', 'L02'])
const moduleObj = {
  L01: dataUploadResult,
  L02: dataUploadKPI
}
const currentModuleCode = ref('L01')
const currentIndex = ref(0)
const stepData = ref([
  {
    code: 'L01',
    name: '目标与关键结果',
    enqProgress: 'Y'
  },
  {
    code: 'L02',
    name: 'KPI数据',
    enqProgress: 'N'
  }
])

const router = useRouter()
const goback = () => {
  router.back()
}

const getEnqId = () => {
  return route.query.enqId
}

const getEnqInfoFun = async () => {
  const enqId = getEnqId()
  if (enqId) {
    const res = await getEnqInfo({ id: enqId })
    if (res.code == 200) {
      isEdit.value = res.data.enqStatus == 1
      stepData.value.forEach(item => {
        item.enqProgress = res.data.enqStatus == 1 ? 'N' : 'Y'
      })
    }
  }
}

const stepClick = (code, index) => {
  currentIndex.value = index
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

const nextStep = () => {
  stepData.value[currentIndex.value].enqProgress = 'Y'
  if (currentIndex.value == stepData.value.length - 1) {
    return false
  }
  currentIndex.value++
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

const prevStep = () => {
  if (currentIndex.value == 0) {
    return false
  }
  currentIndex.value--
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

onMounted(() => {
  // getEnqInfoFun()
})
</script>

<style scoped lang="scss">
.step_btn_wrap {
  padding-top: 16px;
}
</style>
