<template>
  <div class="common-layout">
    <el-container class="">
      <el-aside width="220px">
        <Menu></Menu>
      </el-aside>
      <el-container>
        <el-header>
          <Header></Header>
        </el-header>
        <el-main class="page_container">
          <RouterView />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>
<script setup>
import Menu from './components/menu.vue'
import Header from './components/header.vue'
</script>
<style lang="scss" scoped>
.common-layout {
  height: 100vh;
}
.el-container {
  height: 100vh;
  .el-aside {
    box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  }
  &:nth-child(2) {
    background: linear-gradient(180deg, #e8f4ff 0%, #ffffff 100%);
  }
}
.el-header {
  padding: 0;
}
.page_container {
  padding-top: 0;
}
</style>
