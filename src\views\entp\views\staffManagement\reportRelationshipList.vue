<template>
  <div class="report_relationship_list_wrap">
    <div class="page_main_title">汇报关系管理</div>
    <div class="page_section">
      <div class="report_relationship_list_center clearfix">
        <div class="page_section_aside org_chart_aside">
          <div class="aside_tree_title">
            <div>组织架构</div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio
              :treeData="treeData"
              :needCheckedFirstNode="needCheckedFirstNode"
              :defaultCheckedKeys="defaultCheckedKeys"
              @clickCallback="clickCallback"
            />
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <div class="filter_bar_wrap">
            <div class="flex_row_start">
              <div class="filter_item title">筛选</div>
              <div class="filter_item">
                <el-input v-model="staffName" placeholder="按员工姓名进行检索">
                  <template #suffix>
                    <i class="el-input__icon el-icon-search"></i>
                  </template>
                </el-input>
              </div>
              <div class="filter_item">
                <el-button class="page_add_btn" type="primary" @click="searchStaff">查询</el-button>
              </div>
            </div>
            <div class="flex_row_start">
              <div class="filter_item">
                <el-button class="page_add_btn" type="primary" @click="importReport">导入</el-button>
              </div>
              <div class="filter_item">
                <el-button class="page_add_btn" type="primary" @click="exportReport">导出</el-button>
              </div>
            </div>
          </div>
          <div>
            <table-component
              :tableData="tableData"
              :needIndex="true"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
            >
              <template #oper>
                <el-table-column label="操作" width="80">
                  <template #default="scope">
                    <el-button
                      @click.prevent="tableEdit(tableData.data[scope.$index])"
                      icon="el-icon-edit"
                      link
                      class="icon_edit"
                    />
                  </template>
                </el-table-column>
              </template>
            </table-component>
          </div>
        </div>
      </div>
    </div>
    <!-- <report-import-popUp v-model:show="showPopUp" @importSign="importSign" /> -->
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getOrgDeptTree, exportDownload } from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio.vue'
import tableComponent from '@/components/talent/tableComps/tableComponent.vue'
// import reportImportPopUp from '../tPopUpComps/reportImportPopUp.vue'

const router = useRouter()

const treeData = ref([])
const orgCode = ref('')
const staffName = ref('')
const showPopUp = ref(false)
const needCheckedFirstNode = ref(true)
const defaultCheckedKeys = ref([])
const currentPage = ref(1)
const pageSize = ref(10)

const tableData = reactive({
  columns: [
    { label: '员工编码', prop: 'employeeCode' },
    { label: '员工姓名', prop: 'userName' },
    { label: '所属部门', prop: 'orgName' },
    { label: '岗位', prop: 'postName' },
    { label: '直接上级', prop: 'supUserName' }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})

function getOrgTreeFun() {
  getOrgDeptTree().then(res => {
    if (res.code == 200) {
      treeData.value = res.data.length > 0 ? res.data : []
    } else {
      treeData.value = []
      orgCode.value = ''
    }
  })
}

function clickCallback(val, isLastNode) {
  orgCode.value = val
  currentPage.value = 1
  getReportPageListFun()
}

function searchStaff() {
  currentPage.value = 1
  getReportPageListFun()
}

function handleSizeChange(size) {
  pageSize.value = size
  getReportPageListFun()
}

function handleCurrentChange(current) {
  currentPage.value = current
  getReportPageListFun()
}

function tableEdit(row) {
  router.push({
    path: '/basicSettingHome/reportRelationship/editReport',
    query: {
      staffId: row.staffId
    }
  })
}

function importReport() {
  showPopUp.value = true
}

function exportReport() {
  // exportReportData({
  //   orgCode: orgCode.value || '',
  //   staffName: staffName.value || ''
  // }).then(res => {
  //   if (res.code == 200) {
  //     exportDownloadFun(res.data)
  //   } else {
  //     ElMessage.warning(res.msg)
  //   }
  // })
}

function exportDownloadFun(val) {
  exportDownload({
    fileName: val
  }).then(res => {
    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '汇报关系列表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  })
}

function getReportPageListFun() {
  // getReportPageList({
  //   orgCode: orgCode.value,
  //   staffName: staffName.value,
  //   current: currentPage.value,
  //   size: pageSize.value
  // }).then(res => {
  //   if (res.code == 200) {
  //     tableData.page.total = res.total
  //     tableData.data = res.data
  //   } else {
  //     tableData.data = []
  //     tableData.page = {
  //       total: 0,
  //       current: 1,
  //       size: 10
  //     }
  //   }
  // })
}

function importSign(sign) {
  if (sign) {
    getReportPageListFun()
  }
}

onMounted(() => {
  getOrgTreeFun()
})
</script>

<style scoped lang="scss">
.report_relationship_list_wrap {
  .filter_item {
    .el-button {
      margin: 0 10px 0 0;
    }
  }
  .flex_row_start {
    .el-input__inner {
      width: 180px;
    }
  }
  .aside_tree_list {
    height: 662px !important;
  }
}
</style>
