<template>
  <div class="aside_filter">
    <!--        <div class="page_second_title">筛选</div>-->
    <div class="aside_filter_bar">
      <div class="filter_bar_classify">
        <div class="classify_title" @click="toggleShow('showOrgContent')">按组织架构</div>
        <div class="classify_content tree_dom" :class="{ show: showOrgContent }">
          <!-- <treeCompRadio :expandedLevel="2" :defaultExpandAll="false" @clickCallback="clickCallback" :treeData="filterData.orgData"></treeCompRadio> -->
          <treeCompCheckbox
            @node-click-callback="checkboxCallback"
            :expandedLevel="2"
            :expandAll="false"
            :checkChildNode="false"
            :isLateralCheck="true"
            :treeData="filterData.orgData"
          ></treeCompCheckbox>
        </div>
      </div>
      <div class="filter_bar_classify">
        <div class="classify_title" @click="toggleShow('showJobContent')">按职务类型</div>
        <div class="classify_content" :class="{ show: showJobContent }">
          <treeCompRadio
            @clickCallback="jobClassCallback"
            :needCheckedFirstNode="false"
            :canCancel="true"
            :labelKey="'jobClassName'"
            :nodeKey="'jobClassCode'"
            :treeData="filterData.jobClass"
          ></treeCompRadio>

          <!-- <div
                        class="item"
                        :class="{'active':item.jobClassCode == jobClassCode}"
                        v-for="(item,index) in filterData.jobClass"
                        :key="index"
                        @click="selectedItem(item,item.jobClassCode)"
                    >
                        <i class="item_icon el-icon-caret-right"></i>
                        {{item.jobClassName}}
                    </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import treeCompCheckbox from '@/components/talent/treeComps/treeCompCheckbox'

const props = defineProps({
  filterData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['getCode'])

const showOrgContent = ref(true)
const showJobContent = ref(true)
const jobClassCode = ref('')
const jobClassName = ref('')
const treeCheckedId = ref('')
const treeNodeName = ref('')

const toggleShow = type => {
  if (type == 'showOrgContent') {
    showOrgContent.value = !showOrgContent.value
  } else if (type == 'showJobContent') {
    showJobContent.value = !showJobContent.value
  }
}

const selectedItem = (item, code) => {
  if (code == jobClassCode.value) {
    jobClassCode.value = ''
    jobClassName.value = ''
  } else {
    jobClassCode.value = code
    jobClassName.value = item.jobClassName
  }
  emit('getCode', treeCheckedId.value, jobClassCode.value, jobClassName.value, treeNodeName.value)
}

const clickCallback = (checkedId, lastNode, data) => {
  treeCheckedId.value = checkedId
  treeNodeName.value = data.label
  emit('getCode', treeCheckedId.value, jobClassCode.value, jobClassName.value, treeNodeName.value)
}

const checkboxCallback = (checkedCodes, curCheckCode) => {
  treeCheckedId.value = checkedCodes.toString()
  treeNodeName.value = null
  emit('getCode', treeCheckedId.value, treeNodeName.value, jobClassCode.value, jobClassName.value)
}

const jobClassCallback = (checkedId, lastNode, data) => {
  jobClassCode.value = checkedId
  jobClassName.value = data.jobClassName
  emit('getCode', treeCheckedId.value, jobClassCode.value, jobClassName.value, treeNodeName.value)
}
</script>

<style scoped lang="scss">
.aside_filter {
  /*padding-top: 20px;*/
}
.aside_filter_bar {
  border: 1px solid #e5e5e5;
  .filter_bar_classify {
    color: #212121;
    font-size: 14px;
    .classify_title {
      line-height: 34px;
      background-color: #ebf4ff;
      // text-align: center;
      padding-left: 30px;
      cursor: pointer;
      border-bottom: 1px solid #e5e5e5;
      &:last-of-type {
        border: none;
      }
    }
    .classify_content {
      padding-left: 12px;
      height: 0;
      max-height: 600px;
      overflow: auto;
      &.show {
        height: auto;
      }
      &.tree_dom {
        padding-left: 8px;
      }
      .item {
        margin: 10px 0;
        cursor: pointer;
        &.active {
          color: #0099ff;
          .item_icon {
            visibility: visible;
          }
        }
        .item_icon {
          visibility: hidden;
        }
      }
    }
  }
}
</style>
