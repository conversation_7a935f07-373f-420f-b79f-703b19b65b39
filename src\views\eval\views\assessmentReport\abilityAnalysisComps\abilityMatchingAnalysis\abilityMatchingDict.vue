<template>
    <div class="ability_matching_dict_wrap">
        <div class="staff_relevancy_main page_section">
            <div class="page_third_title">部门匹配度</div>
            <el-table
                class="table_wrap"
                :data="tableData.data"
                v-loading="loading"
                border
            >
                <el-table-column type="index" align="center"></el-table-column>
                <el-table-column
                    v-for="(item, index) in tableData.tableTitle"
                    :label="item.label"
                    :prop="item.prop"
                    align="center"
                >
                    <el-table-column
                        v-for="item1 in item.childrenLabels"
                        :key="item.prop"
                        :label="item1.label"
                        :prop="item1.prop"
                        align="center"
                        :formatter="item1.formatterFun"
                    >
                    </el-table-column>
                </el-table-column>
            </el-table>
            <coustomPagination :total="total" @pageChange="pageChange"></coustomPagination>
        </div>
    </div>
</template>
 
<script>
    import coustomPagination from "@/components/talent/paginationComps/coustomPagination";
    import { departmentMatchingDegree } from "../../../../request/api";
    export default {
        name: "abilityMatchingDict",
        props: ["orgCode","evalId"],
        components: {coustomPagination},
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading:false,
                tableData: {
                    tableTitle: [
                        {
                            label: "部门",
                            prop: "orgName",
                        },
                        {
                            label: "岗能匹配达标",
                            childrenLabels: [
                                {
                                    label: "人数",
                                    prop: "achieveCount",
                                },
                                {
                                    label: "占比",
                                    prop: "achieveProportion",
                                    formatterFun(data) {
                                        return data['achieveProportion']+"%";
                                    },
                                },
                            ],
                        },
                        {
                            label: "最佳匹配",
                            childrenLabels: [
                                {
                                    label: "人数",
                                    prop: "matchCount",
                                },
                                {
                                    label: "占比",
                                    prop: "matchProportion",
                                    formatterFun(data) {
                                        return data['matchProportion']+"%";
                                    },
                                },
                            ],
                        },
                        {
                            label: "正向高偏离",
                            childrenLabels: [
                                {
                                    label: "人数",
                                    prop: "posDevCount",
                                },
                                {
                                    label: "占比",
                                    prop: "posDevProportion",
                                    formatterFun(data) {
                                        return data['posDevProportion']+"%";
                                    },
                                },
                            ],
                        },
                        {
                            label: "负向高偏离",
                            childrenLabels: [
                                {
                                    label: "人数",
                                    prop: "negDevCount",
                                },
                                {
                                    label: "占比",
                                    prop: "negDevProportion",
                                    formatterFun(data) {
                                        return data['negDevProportion']+"%";
                                    },
                                },
                            ],
                        },
                    ],
                    data: [
                        {
                            index: 1,
                            dict: "销售一部",
                            standardPerson: 56,
                            standardDuty: "72%",
                            bestPerson: 56,
                            bestDuty: "72%",
                            forwardPerson: 56,
                            forwardDuty: "72%",
                            negativePerson: 56,
                            negativeDuty: "72%",
                        },
                        {
                            index: 2,
                            dict: "销售一部",
                            standardPerson: 56,
                            standardDuty: "72%",
                            bestPerson: 56,
                            bestDuty: "72%",
                            forwardPerson: 56,
                            forwardDuty: "72%",
                            negativePerson: 56,
                            negativeDuty: "72%",
                        },
                    ],
                },
            };
        },
        created() {},
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
        },
        methods: {
            getData() {
                this.loading = true;
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                departmentMatchingDegree(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.tableData, "data", res.data);
                        this.total = res.total;
                        this.loading = false;
                    }else{
                        this.loading = false;
                    }
                });
            },
            pageChange(size,currPage){
                this.pageSize = size;
                this.currPage = currPage;
                this.getData();
            },  
        },
    };
</script>
 
<style scoped lang="scss">
    .ability_matching_dict_wrap {
        .staff_relevancy_main {
            .table_wrap tbody td {
                padding: 0 !important;
            }
            .table_wrap tbody .cell {
                padding: 12px 0;
                height: 100%;
                cursor: pointer;
            }
            .table_wrap .check_box_wrap {
                color: #0099fd;
                font-weight: 700;
            }
        }
    }
</style>