<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">一级组织分布</div>
          <div class="content_item_content" id="on_level"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">二级组织分布</div>
          <div class="content_item_content" id="two_level"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">三级组织分布</div>
          <div class="content_item_content" id="three_level"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">四级组织分布</div>
          <div class="content_item_content" id="four_level"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">五级组织分布</div>
          <div class="content_item_content" id="five_level"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">六级组织分布</div>
          <div class="content_item_content" id="six_level"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            组织分布明细
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { orgDistribution, queryOrgList, exportData } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')

const onLevelData = reactive({
  data: []
})

const twoLevelData = reactive({
  data: []
})

const threeLevelData = reactive({
  data: []
})

const fourLevelData = reactive({
  data: []
})

const fiveLevelData = reactive({
  data: []
})

const sixLevelData = reactive({
  data: []
})

const filterData = ref([])
const page = ref(1)
const size = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '一级组织',
      prop: 'onLevelName'
    },
    {
      label: '二级组织',
      prop: 'twoLevelName'
    },
    {
      label: '三级组织',
      prop: 'threeLevelName'
    },
    {
      label: '四级组织',
      prop: 'fourLevelName'
    },
    {
      label: '五级组织',
      prop: 'fiveLevelName'
    },
    {
      label: '六级组织',
      prop: 'sixLevelName'
    },
    {
      label: '姓名',
      prop: 'userName'
    },
    {
      label: '岗位',
      prop: 'postName'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('on_level', 'YBar', '230', '200', onLevelData)
  echartsRenderPage('two_level', 'YBar', '230', '200', twoLevelData)
  echartsRenderPage('three_level', 'YBar', '230', '200', threeLevelData)
  echartsRenderPage('four_level', 'YBar', '230', '200', fourLevelData)
  echartsRenderPage('five_level', 'YBar', '230', '200', fiveLevelData)
  echartsRenderPage('six_level', 'YBar', '230', '200', sixLevelData)
}

const orgDistributionFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await orgDistribution(params)
    if (res.code == 200) {
      const data = res.data
      onLevelData.data = window.$util.addPercentSign(data.onLevel, 'value')
      twoLevelData.data = window.$util.addPercentSign(data.twoLevel, 'value')
      threeLevelData.data = window.$util.addPercentSign(data.threeLevel, 'value')
      fourLevelData.data = window.$util.addPercentSign(data.fourLevel, 'value')
      fiveLevelData.data = window.$util.addPercentSign(data.fiveLevel, 'value')
      sixLevelData.data = window.$util.addPercentSign(data.sixLevel, 'value')
      initChart()
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  page.value = 1
  orgDistributionFun()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryOrgList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error(error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'f'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '组织分布明细')
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  orgDistributionFun()
  getTableData()
})
</script>

<style scoped lang="scss"></style>
