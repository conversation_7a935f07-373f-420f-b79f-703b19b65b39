<script setup>
import Table from '@/components/table/simplenessTable.vue'
// 11

const columns = ref([
  {
    label: '二级能力',
    prop: 'a'
  },
  {
    label: '能力得分',
    prop: 'b'
  },
  {
    label: '高层',
    prop: 'c'
  },
  {
    label: '业务管理层',
    prop: 'd'
  },
  {
    label: '部门管理层',
    prop: 'e'
  },
  {
    label: '基层主管',
    prop: 'f'
  },
  {
    label: '一线执行层',
    prop: 'g'
  }
])
const data = ref([
  {
    a: '经验依赖型',
    b: '12',
    c: '36',
    d: '36',
    e: '36',
    f: '36',
    g: '0',
    h: '2024-09-26 09:59:59',
    k: '100'
  }
])

const columns2 = ref([
  {
    label: '二级能力',
    prop: 'a'
  },
  {
    label: '认知能力',
    prop: 'b'
  },
  {
    label: '分析能力',
    prop: 'c'
  },
  {
    label: '计划能力',
    prop: 'd'
  },
  {
    label: '评审能力',
    prop: 'e'
  },
  {
    label: '规制能力',
    prop: 'f'
  },
  {
    label: '决策能力',
    prop: 'g'
  },
  {
    label: '落实能力',
    prop: 'h'
  }
])
const data2 = ref([
  {
    a: '市场分析与战略规划',
    b: '36',
    c: '36',
    d: '36',
    e: '36',
    f: '36',
    g: '36',
    h: '36'
  }
])

const columns3 = ref([
  {
    label: '能力短板类型',
    prop: 'a',
    width: 150
  },
  {
    label: '训练模块',
    prop: 'b'
  },
  {
    label: '实战场景设计',
    prop: 'c'
  }
])
const data3 = ref([
  {
    a: '缺乏认知能力',
    b: '《战略地图绘制与行业趋势洞察》课程 + 《PESTEL 模型实战应用》',
    c: '针对智能家电行业，运用 PESTEL 模型分析东南亚市场政策（如能效标准）、技术（如 AI 烹饪）趋势，输出《2025 年区域战略认知手册》'
  }
])

onMounted(() => {})
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">能力提升管理建议</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        基于能力测评数据，精准识别团队成员能力与目标岗位胜任力要求的匹配程度，通过岗位调整实现
        “人岗适配”，最大化释放人才效能。
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="t_w justify-between">
        <div class="page-title-line">核心能力表现</div>
        <div class="btn_wrap justify-between marginT-10">
          <div class="btn marginR20">能力得分</div>
          <div class="btn">能力匹配度</div>
        </div>
      </div>
      <Table :roundBorder="false" :columns="columns" :data="data" headerColor showIndex> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">专业能力提升工程</div>
      <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor showIndex> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">专业能力提升工程（战略解码与目标分解）</div>
      <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor showIndex> </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../../../../style/common.scss';
@import './common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
