<script setup>
import { Search } from '@element-plus/icons-vue'
import EChartsBar from '@/components/EChartsBar.vue'
import { formatter } from 'element-plus'
import { improveTaskList } from '@/assets/data/data9.js'
import Tree from '@/components/tree/index.vue'

defineOptions({ name: 'improveTasks' })
const openAi = inject('openAi')

const menuList = ref(['组织指标'])
const active = ref(0)
const treeRef = ref()
const filterText = ref('')
const props = {
  value: 'id',
  label: 'label',
  children: 'children'
}
const data = ref([
  {
    id: 1,
    label: 'H公司',
    children: [
      {
        id: 4,
        label: '三级组织',
        children: [
          {
            id: 5,
            label: '采购部'
          },
          {
            id: 6,
            label: '供应链计划管理部'
          },
          {
            id: 7,
            label: '电子商务部'
          },
          {
            id: 8,
            label: '欧盟区产品部'
          },
          {
            id: 9,
            label: '工艺部'
          },
          {
            id: 10,
            label: '供应链计划管理部'
          },
          {
            id: 11,
            label: 'GTM部'
          },
          {
            id: 12,
            label: '结构研发部'
          },
          {
            id: 13,
            label: '经营与财务管理部'
          },
          {
            id: 14,
            label: '冷柜研发部'
          },
          {
            id: 15,
            label: '零售与用户运营部'
          },
          {
            id: 16,
            label: '品牌与产品营销部'
          }
        ]
      }
    ]
  }
])
const treeActive = ref(6)
const filterNode = (value, data) => {
  if (!value) return true
  return data.label.includes(value)
}

watch(filterText, val => {
  !treeRef.value.filter(val)
})

const type = ref('列表')
const options = ['列表', '统计图']

const taskData = ref(improveTaskList)
const warnData = ref([
  {
    id: 1,
    target: '库存周转天数',
    level: '关注级',
    trigger: '偏离基准10%-15%',
    baseline: '对比同期下降10%',
    whether: '动态基线触发',
    risk: '库存周转天数↑15% → 仓储成本占比↑2% → 现金流周转率↓0.5次 → 紧急采购频次↑3次/月 → 客户订单交付准时率↓5%',
    improve: '3项',
    status: '处理中'
  },
  {
    id: 2,
    target: '替代供应商储备率',
    level: '警示级',
    trigger: '偏离基准15%-25%',
    baseline: '对比同期下降20%',
    whether: '阈值规则触发',
    risk: '库存周转天数↑15% → 仓储成本占比↑2% → 现金流周转率↓0.5次 → 紧急采购频次↑3次/月 → 客户订单交付准时率↓5%',
    improve: '3项',
    status: '已关闭'
  },
  {
    id: 3,
    target: '物流成本占比',
    level: '严重级',
    trigger: '偏离>25%',
    baseline: '对比同期下降30%',
    whether: '阈值规则触发',
    risk: '库存周转天数↑35% → 仓储爆仓率↑40% → 现金流断裂风险↑橙色 → 生产停线频次↑2次/周 → 核心客户流失率↑15% → 银行授信额度下调20%',
    improve: '3项',
    status: '已关闭'
  }
])
const improveData = ref([
  {
    id: 1,
    taskName: '库存结构优化',
    riskEvents: '仓储成本超支风险',
    related: '库存结构优化',
    countermeasures: '库存结构优化',
    actions:
      '1. 实施呆滞库存三级分类清理（红 / 黄 / 蓝），90 天以上库龄物料 30 日内通过促销、调拨、报废处理，压减占比至 15% 以下； 2. 部署 AI 动态安全库存模型，A 类物料周转压缩至 15 天，ERP 系统自动预警补货； 3. 引入自动化仓储系统，60 日内提升存储密度 30%，优化库位动线； 4. 重谈仓储合同，40% 租金转为按周转量计费，设置达标折扣条款。',
    person: '王伟（供应链总监）',
    level: '高',
    result: '《库存结构优化报告》',
    status: '进行中'
  },
  {
    id: 2,
    taskName: '供应商任务',
    riskEvents: '紧急采购成本攀升',
    related: '采购与供应商管理',
    countermeasures: '供应弹性增强',
    actions:
      '1. 认证 3 家以上 72 小时应急供应商，签订加急订单协议，2 周内录入系统； 2. 与 TOP5 供应商试点 VMI，转移安全库存至前置仓，30 日内完成数据对接； 3. 部署智能补货算法，按小时扫描库存自动生成订单，采购每日审核，压缩补货周期； 4. 按销售额 2% 计提战略储备金，制定管理办法，1 周内完成建账。',
    person: '采购总监',
    level: '中',
    result: '《供应商应急管理手册》',
    status: '进行中'
  },
  {
    id: 3,
    taskName: '供应商任务',
    riskEvents: '紧急采购成本攀升',
    related: '采购与供应商管理',
    countermeasures: '供应弹性增强',
    actions:
      '1. 认证 3 家以上 72 小时应急供应商，签订加急订单协议，2 周内录入系统； 2. 与 TOP5 供应商试点 VMI，转移安全库存至前置仓，30 日内完成数据对接； 3. 部署智能补货算法，按小时扫描库存自动生成订单，采购每日审核，压缩补货周期； 4. 按销售额 2% 计提战略储备金，制定管理办法，1 周内完成建账。',
    person: '采购总监',
    level: '低',
    result: '《供应商应急管理手册》',
    status: '进行中'
  }
])

const list = ref([
  {
    title: '部门任务总量',
    data: [
      {
        name: '采购部',
        value: '12'
      },
      {
        name: '供应链计划管理部',
        value: '8'
      },
      {
        name: '电子商务部',
        value: '16'
      },
      {
        name: '欧盟区产品部',
        value: '15'
      }
    ]
  },
  {
    title: '部门任务年度达成率',
    symbol: '%',
    data: [
      {
        name: '采购部',
        value: '85%'
      },
      {
        name: '供应链计划管理部',
        value: '72%'
      },
      {
        name: '电子商务部',
        value: '63%'
      },
      {
        name: '欧盟区产品部',
        value: '92%'
      }
    ]
  },
  {
    title: '本月新增部门任务',
    data: [
      {
        name: '采购部',
        value: '2'
      },
      {
        name: '供应链计划管理部',
        value: '3'
      },
      {
        name: '电子商务部',
        value: '4'
      },
      {
        name: '欧盟区产品部',
        value: '2'
      }
    ]
  },
  {
    title: '本月完成的任务数',
    data: [
      {
        name: '采购部',
        value: '2'
      },
      {
        name: '供应链计划管理部',
        value: '3'
      },
      {
        name: '电子商务部',
        value: '4'
      },
      {
        name: '欧盟区产品部',
        value: '2'
      }
    ]
  }
])

const getOptions = item => {
  let symbol = item.symbol
  return {
    xAxisData: item.data.map(item => item.name).reverse(),
    xAxis: {
      show: false
    },
    grid: {
      top: 0,
      left: '3%',
      right: 0,
      bottom: '3%',
      containLabel: true
    },
    series: [
      {
        type: 'bar',
        data: item.data
          .map(item => {
            if (symbol) {
              return parseInt(item.value)
            } else {
              return parseFloat(item.value)
            }
          })
          .reverse(),
        itemStyle: {
          color: '#85E5FF'
        },
        showBackground: true,
        label: {
          show: true,
          formatter: function (params) {
            if (symbol) {
              return params.value + symbol
            } else {
              return parseFloat(params.value)
            }
          }
        },
        formatter: function (params) {
          console.log(params)
          if (symbol) {
            return params.value + symbol
          } else {
            return parseFloat(params.value).toFixed(2)
          }
        }
      }
    ]
  }
}
// ['未开始', '进行中', '已完成', '已逾期'],
const list2 = ref([
  {
    title: '任务状态分布',
    color: ['#85E5FF', '#41AAF4', '#8FDB6C', '#847AFC'],
    legend: [
      {
        name: '未开始',
        code: 'A'
      },
      {
        name: '进行中',
        code: 'B'
      },
      {
        name: '已完成',
        code: 'C'
      },
      {
        name: '已逾期',
        code: 'D'
      }
    ],
    data: [
      {
        name: '采购部',
        A: '2',
        B: '2',
        C: '1',
        D: '1'
      },
      {
        name: '供应链计划管理部',
        A: '1',
        B: '2',
        C: '2',
        D: '2'
      },
      {
        name: '电子商务部',
        A: '5',
        B: '3',
        C: '1',
        D: '1'
      },
      {
        name: '欧盟区产品部',
        A: '4',
        B: '2',
        C: '0',
        D: '2'
      }
    ]
  },
  {
    title: '未完成任务优先级分布',
    color: ['#85E5FF', '#41AAF4', '#847AFC'],
    legend: [
      {
        name: '低',
        code: 'L'
      },
      {
        name: '中',
        code: 'M'
      },
      {
        name: '高',
        code: 'H'
      }
    ],
    data: [
      {
        name: '采购部',
        L: '2',
        M: '2',
        H: '1'
      },
      {
        name: '供应链计划管理部',
        L: '1',
        M: '2',
        H: '2'
      },
      {
        name: '电子商务部',
        L: '5',
        M: '3',
        H: '1'
      },
      {
        name: '欧盟区产品部',
        L: '4',
        M: '2',
        H: '0'
      }
    ]
  },
  {
    title: '未完成任务对应风险分布',
    color: ['#85E5FF', '#41AAF4', '#847AFC'],
    legend: [
      {
        name: '低',
        code: 'L'
      },
      {
        name: '中',
        code: 'M'
      },
      {
        name: '高',
        code: 'H'
      }
    ],
    data: [
      {
        name: '采购部',
        L: '2',
        M: '2',
        H: '1'
      },
      {
        name: '供应链计划管理部',
        L: '1',
        M: '2',
        H: '2'
      },
      {
        name: '电子商务部',
        L: '5',
        M: '3',
        H: '1'
      },
      {
        name: '欧盟区产品部',
        L: '4',
        M: '2',
        H: '0'
      }
    ]
  },
  {
    title: '人员任务年度累计状态',
    color: ['#85E5FF', '#41AAF4', '#8FDB6C', '#847AFC'],
    legend: [
      {
        name: '未开始',
        code: 'A'
      },
      {
        name: '进行中',
        code: 'B'
      },
      {
        name: '已完成',
        code: 'C'
      },
      {
        name: '已逾期',
        code: 'D'
      }
    ],
    data: [
      {
        name: '王伟',
        A: '2',
        B: '2',
        C: '1',
        D: '1'
      },
      {
        name: '李虎',
        A: '1',
        B: '2',
        C: '2',
        D: '2'
      },
      {
        name: '王勇',
        A: '5',
        B: '3',
        C: '1',
        D: '1'
      },
      {
        name: '刘民',
        A: '4',
        B: '2',
        C: '0',
        D: '2'
      }
    ]
  },
  {
    title: '未完成任务人员优先级分布',
    color: ['#85E5FF', '#41AAF4', '#847AFC'],
    legend: [
      {
        name: '高',
        code: 'H'
      },

      {
        name: '中',
        code: 'M'
      },
      {
        name: '低',
        code: 'L'
      }
    ],
    data: [
      {
        name: '王伟',
        H: '2',
        M: '2',
        L: '1'
      },
      {
        name: '李虎',
        H: '1',
        M: '2',
        L: '2'
      },
      {
        name: '王勇',
        H: '5',
        M: '3',
        L: '1'
      },
      {
        name: '刘民',
        H: '4',
        M: '2',
        L: '0'
      }
    ]
  },
  {
    title: '未完成任务对应风险分布',
    color: ['#85E5FF', '#41AAF4', '#847AFC'],
    legend: [
      {
        name: '高',
        code: 'H'
      },

      {
        name: '中',
        code: 'M'
      },
      {
        name: '低',
        code: 'L'
      }
    ],
    data: [
      {
        name: '王伟',
        H: '2',
        M: '2',
        L: '1'
      },
      {
        name: '李虎',
        H: '1',
        M: '2',
        L: '2'
      },
      {
        name: '王勇',
        H: '5',
        M: '3',
        L: '1'
      },
      {
        name: '刘民',
        H: '4',
        M: '2',
        L: '0'
      }
    ]
  }
])
const getData = (item, data) => {
  let arr = []

  data.map(i => {
    arr.push(i[item.code])
  })
  return arr.reverse() // 反转数组，确保数据顺序与xAxisData对应
}
const getOptions2 = item => {
  let legend = item.legend
  return {
    xAxisData: item.data.map(list => list.name).reverse(),
    xAxis: {
      show: false
    },
    legend: {
      data: legend.map(item => item.name)
    }, // 自定义 lege
    grid: {
      left: '3%',
      right: '4%',
      bottom: 0,
      containLabel: true
    },
    color: item.color,
    series: legend.map((list, index) => {
      return {
        name: list.name,
        type: 'bar',
        stack: '总量',
        data: getData(list, item.data)
      }
    })

    // item.data.map((item, index) => {
    //   return {
    //     name: legend[index],
    //     type: 'bar',
    //     data: [item.A, item.B, item.C, item.D].reverse()
    //   }
    // })
  }
}
</script>
<template>
  <div class="page-container">
    <div class="menu">
      <span>指标类别</span>
      <div class="item" :class="{ active: active == index }" v-for="(item, index) in menuList" :key="item">
        {{ item }}
      </div>
    </div>
    <div class="page-main p-[20px]">
      <div class="left p-[8px]">
        <el-input v-model="filterText" :suffix-icon="Search" class="search-input" placeholder="按组织名称检索" />
        <!-- <el-tree
          :data="data"
          :props="defaultProps"
          node-key="id"
          default-expand-all
          :current-node-key="treeActive"
          highlight-current
        /> -->
        <Tree></Tree>
      </div>
      <div class="right">
        <div class="tab-card">
          <el-segmented v-model="type" :options="options" />
        </div>
        <div class="list-card" v-if="type == '列表'">
          <div class="title-card">
            <div class="page-title-line">指标改善任务</div>
            <el-button class="export-btn">
              <img src="@/assets/imgs/indicator/icon_03.png" alt="" class="export-icon" />导出
            </el-button>
          </div>
          <div class="app-table">
            <el-table :data="taskData" border>
              <el-table-column type="index" label="序号" width="80" align="center" />
              <el-table-column prop="orgName" label="组织" width="130" align="center"> </el-table-column>
              <el-table-column label="全部" align="center">
                <el-table-column prop="improveKpi" label="改善指标" align="center"> </el-table-column>
                <el-table-column prop="totalTask" label="总任务" align="center"> </el-table-column>
                <el-table-column prop="noStart" label="未开始" align="center"> </el-table-column>
                <el-table-column prop="inProgress" label="进行中" align="center"> </el-table-column>
                <el-table-column prop="completed" label="已完成" align="center"> </el-table-column>
                <el-table-column prop="beOverdue" label="已逾期" align="center"> </el-table-column>
              </el-table-column>
              <el-table-column label="新增" align="center">
                <el-table-column prop="addImproveKpi" label="改善指标" align="center"> </el-table-column>
                <el-table-column prop="addTask" label="新增任务" align="center"> </el-table-column>
                <el-table-column prop="addNoStart" label="未开始" align="center"> </el-table-column>
                <el-table-column prop="addInProgress" label="进行中" align="center"> </el-table-column>
                <el-table-column prop="addCompleted" label="已完成" align="center"> </el-table-column>
                <el-table-column prop="addBeOverdue" label="已逾期" align="center"> </el-table-column>
              </el-table-column>
              <el-table-column prop="taskProgress" label="任务进度" align="center"></el-table-column>
              <el-table-column width="175">
                <template #default="scope">
                  <el-button class="operate-btn">详情</el-button>
                  <el-button
                    class="operate-btn"
                    @click="openAi(`${scope.row.orgName}指标改善任务进度${scope.row.taskProgress}`)"
                    >AI简报</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="title-card">
            <div class="page-title-line">任务关联的指标预警信息</div>
            <el-button class="export-btn">
              <img src="@/assets/imgs/indicator/icon_03.png" alt="" class="export-icon" />导出
            </el-button>
          </div>
          <div class="page-table">
            <el-table :data="warnData">
              <el-table-column type="index" label="序号" width="80" align="center" />
              <el-table-column prop="target" label="指标" width="150"> </el-table-column>
              <el-table-column prop="level" label="预警等级"> </el-table-column>
              <el-table-column prop="trigger" label="触发规制（阈值规则）" width="170"></el-table-column>
              <el-table-column prop="baseline" label="触发规制（动态基线）" width="170"></el-table-column>
              <el-table-column prop="whether" label="是否触发"></el-table-column>
              <el-table-column prop="risk" label="风险传导模拟" width="300"></el-table-column>
              <el-table-column prop="improve" label="改善任务" align="center"> </el-table-column>
              <el-table-column prop="status" label="状态" align="center"></el-table-column>
            </el-table>
          </div>
          <div class="title-card">
            <div class="page-title-line">改善任务</div>
            <el-button class="export-btn">
              <img src="@/assets/imgs/indicator/icon_03.png" alt="" class="export-icon" />导出
            </el-button>
          </div>
          <div class="page-table">
            <el-table :data="improveData">
              <el-table-column type="index" label="序号" width="80" align="center" />
              <el-table-column prop="taskName" label="任务名称" width="150"> </el-table-column>
              <el-table-column prop="riskEvents" label="对应风险事件" width="150"> </el-table-column>
              <el-table-column prop="related" label="关联能力"></el-table-column>
              <el-table-column prop="countermeasures" label="应对举措"></el-table-column>
              <el-table-column prop="actions" label="关键行动" width="500"></el-table-column>
              <el-table-column prop="person" label="责任人"></el-table-column>
              <el-table-column prop="level" label="优先级" align="center"> </el-table-column>
              <el-table-column prop="result" label="输出成果"></el-table-column>
              <el-table-column prop="status" label="状态" align="center"></el-table-column>
            </el-table>
          </div>
        </div>
        <div class="charts-card" :class="{ show: type == '统计图' }">
          <div class="title-card">
            <div class="page-title-line">指标改善任务</div>
          </div>
          <div class="app-echarts">
            <div class="col col-4">
              <div class="charts-item location border p-[16px] text-[14px]" v-for="item in list">
                <div class="charts-title">{{ item.title }}</div>
                <div class="charts-main">
                  <EChartsBar type="horizontal" :options="getOptions(item)" />
                </div>
              </div>
            </div>
            <div class="col col-3">
              <div class="charts-item location border p-[16px] text-[14px]" v-for="iten in list2">
                <div class="charts-title">{{ iten.title }}</div>
                <div class="charts-main">
                  <EChartsBar type="horizontal" :stacked="true" :options="getOptions2(iten)" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.menu {
  @include flex-center(row, flex-start, center);
  margin-bottom: 26px;
  span {
    margin-right: 18px;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
  }
  .item {
    width: 311px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    margin-right: 12px;
    background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
    border-radius: 5px;
    border: 1px solid #c6dbf3;
    font-size: 14px;
    color: #333333;
    cursor: pointer;
    font-weight: 500;
    &.active {
      color: #40a0ff;
    }
  }
}
.location {
  background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), rgba(255, 255, 255, 0.5);
}
.border {
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #c6dbf3;
}
.page-main {
  @include flex-center(row, flex-start, flex-start);
  gap: 20px;
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px;
  .left {
    width: 206px;
    flex-shrink: 0;
    border-radius: 8px;
    border: 1px solid #c6dbf3;
  }
  .title-card {
    @include flex-center(row, space-between, center);
    padding-bottom: 10px;
    .page-title-line {
      margin-bottom: 0;
      font-size: 16px;
      font-weight: 600;
    }
    .export-btn {
      height: 36px;
      border-radius: 6px;
      border-color: #666666;
      .export-icon {
        margin-right: 6px;
        vertical-align: middle;
      }
    }
  }
  .right {
    width: calc(100% - 226px);
    .tab-card {
      @include flex-center(row, center, center);
      height: 36px;
      :deep(.el-segmented) {
        height: 100%;
        font-size: 16px;
        font-weight: 500;
        --el-segmented-item-selected-color: #fff;
        --el-border-radius-base: 18px;
        --el-segmented-bg-color: #d8ebff;
        --el-segmented-color: #76b2ed;
        --el-segmented-item-hover-color: #76b2ed;
        --el-segmented-item-hover-bg-color: transparent;
        --el-segmented-item-active-bg-color: transparent;
        .el-segmented__item {
          padding: 0 34px;
        }
      }
    }
    .list-card {
      width: 100%;
      margin-bottom: 38px;
      .app-table {
        width: 100%;
        margin-bottom: 38px;
        :deep(.el-table) {
          --el-table-border-color: #b7cce2;
          --el-table-row-hover-bg-color: #c6dbf3;
          th.el-table__cell {
            background-color: #eaf4ff;
            .cell {
              font-size: 12px;
              color: #86add3;
            }
          }
          .el-table__cell {
            .cell {
              font-size: 12px;
            }
          }
        }
        .operate-btn {
          width: 68px;
          height: 24px;
          border-radius: 12px;
          border: 1px solid #40a0ff;
          color: #40a0ff;
          background-color: transparent;
          &:hover {
            background-color: #40a0ff;
            color: #fff;
          }
        }
      }
      .page-table {
        margin-bottom: 38px;
        :deep(.el-table) {
          th.el-table__cell {
            background-color: #eaf4ff;
            .cell {
              font-size: 14px;
              color: #93abcb;
            }
          }
        }
      }
    }
    .app-echarts {
      width: 100%;
      .col {
        @include flex-center(row, flex-start, flex-start);
        width: 100%;
        margin-bottom: 30px;
        flex-wrap: wrap;
        gap: 20px;
        .charts-title {
          padding-bottom: 10px;
          color: #333333;
          font-weight: bold;
        }
        .charts-main {
          height: calc(100% - 30px);
        }
      }
      .col-4 {
        .charts-item {
          width: calc((100% - 60px) / 4);
          height: 236px;
        }
      }
      .col-3 {
        .charts-item {
          width: calc((100% - 40px) / 3);
          height: 265px;
        }
      }
    }
  }
}
.charts-card {
  position: fixed;
  z-index: -1;
  opacity: 0;
  &.show {
    position: relative;
    opacity: 1;
    z-index: initial;
  }
}
</style>
