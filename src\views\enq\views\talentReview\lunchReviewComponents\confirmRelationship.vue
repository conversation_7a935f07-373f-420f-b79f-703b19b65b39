<template>
  <div class="relationship_wrap">
    <div class="level_wrap">
      <div v-show="showPage">
        <div class="flex_row_between marginT_20">
          <div class="page_second_title">评价关系</div>
          <div class="flex_row_start">
            <el-button class="page_add_btn" type="primary" :disabled="!isEdit" @click="importStaffDialog = true"
              >导入数据</el-button
            >
          </div>
        </div>
        <div
          class="loading_style"
          v-loading.fullscreen.lock="loading"
          element-loading-text="上传中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.5)"
        ></div>
        <div class="relationship_main flex_row_start">
          <tableComponent
            :loading="loadingStatus"
            :tableData="tableData"
            :needIndex="true"
            height="400"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          ></tableComponent>
        </div>
      </div>
      <div class="talent_raview_btn_wrap align_center marginT_30" v-if="isEdit">
        <el-button class="page_confirm_btn" type="primary" @click="prev">上一步</el-button>
        <el-button class="page_confirm_btn" type="primary" @click="next">下一步</el-button>
      </div>
      <!-- 导入弹窗 -->
      <el-dialog
        v-model="importStaffDialog"
        title="开始导入"
        :width="'50%'"
        :before-close="importStaffDialogBeforeClose"
      >
        <div class="import_staff_wrap">
          <div class="import_staff_title">操作步骤:</div>
          <div class="oper_step">
            <p>1、下载《参与人员评价关系导入模板》</p>
            <p>2、打开下载表，标记需要参与本次测评的人员。</p>
            <p>3、信息输入完毕，点击"选择文件"按钮，选择excel文档。</p>
            <p>4、点击"开始导入",导入中如有任何疑问，请致电010-86482868。</p>
          </div>
          <div class="import_staff_title">填写须知:</div>
          <div class="oper_step">
            <p>1、不能在该Excel表中对员工信息类别进行增加、删除或修改；</p>
            <p>2、Excel中红色字段为必填字段，黑色字段为选填字段；</p>
            <p>3、如需取消部分特殊的评价关系，请删除相关评价记录即可;</p>
          </div>
          <div class="fs16 main_color pointer download_file" @click="exportTemplate">
            立即下载《参与人员评价关系导入模板》
          </div>
          <div class="upload_file_wrap">
            <el-input placeholder="请输入内容" v-model="fileName" readonly>
              <template #append>
                <label for="up" class="upload_label">
                  选择文件
                  <input
                    id="up"
                    style="display: none"
                    ref="fileRef"
                    type="file"
                    accept=".xlsx"
                    class="form-control page_clear_btn"
                    @change="fileChange"
                  />
                </label>
              </template>
            </el-input>
          </div>
        </div>
        <template #footer>
          <el-button class="page_clear_btn" @click="importStaffDialog = false">取 消</el-button>
          <el-button type="primary" class="page_add_btn" @click="importExcel">开始导入</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getEnqRelation, importRelation, exportRelation } from '../../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const props = defineProps({
  getEnqId: {
    type: Function,
    required: true
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['prevStep', 'nextStep'])

const showPage = ref(false)
const importStaffDialog = ref(false)
const fileName = ref(null)
const uploadFile = ref(null)
const loadingStatus = ref(false)
const loading = ref(false)
const fileRef = ref(null)
const enqId = ref(null)

const tableData = ref({
  columns: [
    { label: '一级组织', prop: 'oneLevelName' },
    { label: '二级组织', prop: 'twoLevelName' },
    { label: '三级组织', prop: 'threeLevelName' },
    { label: '四级组织', prop: 'fourLevelName' },
    { label: '五级组织', prop: 'fiveLevelName' },
    { label: '评价项目', prop: 'enqModuleName' },
    { label: '被评价人', prop: 'objectName' },
    { label: '评价关系', prop: 'relationType' },
    { label: '评价人', prop: 'userName' },
    { label: '评价人岗位', prop: 'userPostName' }
  ],
  data: [],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})

const handleSizeChange = async size => {
  tableData.value.page.size = size
  await getList()
}

const handleCurrentChange = async current => {
  tableData.value.page.current = current
  await getList()
}

const getList = async () => {
  loadingStatus.value = true
  try {
    const params = {
      enqId: enqId.value,
      size: tableData.value.page.size,
      current: tableData.value.page.current
    }
    const res = await getEnqRelation(params)

    if (res.code == 200) {
      if (typeof res.data == 'boolean') {
        showPage.value = false
      } else {
        showPage.value = true
        tableData.value.data = res.data
        tableData.value.page.total = res.total
      }
    }
  } catch (error) {
    ElMessage.error(error.message)
  } finally {
    loadingStatus.value = false
  }
}

const exportTemplate = async () => {
  try {
    const res = await exportRelation({
      enqId: enqId.value
    })

    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '参与盘点员工评价关系表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const importStaffDialogBeforeClose = done => {
  fileName.value = ''
  if (document.getElementById('up')) {
    document.getElementById('up').value = null
  }
  done()
}

const fileChange = e => {
  const formData = new FormData()
  const file = e.target.files[0]
  formData.append('file', file)
  formData.append('enqId', enqId.value)

  if (fileRef.value) {
    fileRef.value.value = ''
  }

  fileName.value = file.name
  uploadFile.value = formData
}

const importExcel = async () => {
  loading.value = true
  try {
    const res = await importRelation(uploadFile.value)
    if (res.code == 200) {
      ElMessage.success('上传成功')
      importStaffDialog.value = false
      await getList()
    } else {
      ElMessage.warning(res.msg)
    }
  } catch (error) {
    ElMessage.error(error.message)
  } finally {
    loading.value = false
  }
}

const prev = async () => {
  try {
    await ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '保存',
      cancelButtonText: '放弃修改'
    })
    emit('prevStep')
  } catch (action) {
    if (action == 'cancel') {
      ElMessage.info('已放弃修改并返回上一步')
      emit('prevStep')
    } else {
      ElMessage.info('取消返回上一步')
    }
  }
}

const next = () => {
  emit('nextStep')
}

onMounted(() => {
  enqId.value = props.getEnqId()
  getList()
})
</script>

<style scoped lang="scss">
.level_count {
  color: #f00;
}

.relationship_wrap {
  width: 100%;

  .level_wrap {
    .level_wrap_ul {
      width: 900px;
      height: 70px;

      li {
        height: 62px;
        margin-right: 10px;
        border: 1px solid #e5e5e5;
        cursor: pointer;

        span {
          display: block;
          text-align: center;
          line-height: 30px;
          background: #ebf4ff;
        }

        .border_blue {
          .el-input__inner {
            border: 1px solid #0099ff;
          }
        }

        .el-input__inner {
          border-radius: 0;
        }

        .el-input-group__append {
          border-radius: 0;
          margin: 0;
        }

        .el-input.is-disabled {
          .el-input__inner {
            color: #212121;
            background: #fff;
          }
        }

        &.active {
          border: 1px solid #0099ff;

          span {
            background: #0099ff;
            color: #fff;
          }
        }
      }
    }
  }
}

.check_box_wrap {
  color: #0099fd;
  font-weight: 700;
  font-size: 24px;
}

.upload_wrap {
  position: relative;
  width: 80px;
  height: 30px;
  margin-left: 10px;
  cursor: pointer;
}

.import_btn {
  font-size: 0;
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.import_staff_wrap {
  .import_staff_title {
    color: #515c71;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .oper_step {
    line-height: 28px;
    color: #515c71;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .download_file {
    display: block;
    margin-bottom: 16px;
  }

  .upload_file_wrap {
    margin-bottom: 16px;

    .upload_label {
      display: block;
      height: 28px;
      line-height: 28px;
      width: 100%;
      cursor: pointer;
    }
  }
}

.el-icon-loading:before {
  font-size: 24px;
}
</style>
