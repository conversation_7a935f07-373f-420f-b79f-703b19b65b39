<template>
  <div class="department_process_main">
    <div class="page_second_title">流程分类</div>
    <div class="department_main">
      <div class="aside_wrap">
        <div class="aside_tree_title">一/二级流程</div>
        <div class="aside_tree_list">
          <tree-comp-radio
            :treeData="treeData"
            :needCheckedFirstNode="false"
            :canCancel="true"
            @clickCallback="clickCallback"
          />
        </div>
      </div>
      <div class="process_table">
        <table-component
          :tableData="tableData"
          :checkSelection="checkSelection"
          :selectionStatus="true"
          @selectionChange="selectionChange"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
          
        />
        <div class="align_center paddT_12 marginT_16">
          <el-button class="page_confirm_btn" type="primary" @click="submit"> 确认 </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useStore } from 'vuex'
import { getProcessTree, getProcessList, createPostProcess, getPostProcess } from '../../../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import tableComponent from '@/components/talent/tableComps/tableComponent'

// Props
const props = defineProps({
  curPostCodeCopy: String
})

// Emits
const emit = defineEmits(['submitSuccessTab'])

// Store
const store = useStore()

// Computed
const companyId = computed(() => store.state.userInfo.companyId)

// Reactive State
const treeData = ref([])
const tableData = ref({
  columns: [
    {
      label: '流程编码',
      prop: 'bizProcessCode',
      width: 100
    },
    {
      label: '流程名称',
      prop: 'bizProcessName',
      width: 120
    },
    {
      label: '流程描述',
      prop: 'bizProcessDesc'
    },
    {
      label: '层级',
      prop: 'layerNo',
      width: 60
    },
    {
      label: '前置流程',
      prop: 'prevProcessName',
      width: 120
    }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})
const checkBizProcessCode = ref('')
const postBizProcessList = ref([])
const checkSelection = ref([])

// Methods
const getProcessTreeFun = async () => {
  try {
    const res = await getProcessTree({
      bizProcessCode: '',
      companyId: companyId.value,
      layerNo: ''
    })

    if (res.code == 200) {
      treeData.value = res.data
    } else {
      treeData.value = []
    }
  } catch (error) {
    console.error('获取流程树失败:', error)
    ElMessage.error('获取流程树失败')
  }
}

const clickCallback = val => {
  checkBizProcessCode.value = val
  tableData.value.page.current = 1
  getProcessListFun()
}

const getProcessListFun = async () => {
  try {
    const res = await getProcessList({
      bizProcessCode: checkBizProcessCode.value,
      companyId: companyId.value,
      current: tableData.value.page.current,
      size: tableData.value.page.size
    })

    if (res.code == 200) {
      if (res.data.length > 0) {
        tableData.value.data = res.data.map(item => ({
          bizProcessCode: item.bizProcessCode,
          bizProcessName: item.bizProcessName,
          bizProcessDesc: item.bizProcessDesc,
          layerNo: item.layerNo,
          parentProcessName: item.parentProcessName,
          prevProcessName: item.prevProcessName
        }))
        await getPostProcessFun()
      } else {
        tableData.value.data = []
      }
      tableData.value.page = res.page
    } else {
      tableData.value.data = []
      tableData.value.page = {
        total: 0,
        current: 1,
        size: 10
      }
    }
  } catch (error) {
    console.error('获取流程列表失败:', error)
    ElMessage.error('获取流程列表失败')
  }
}

const handleSizeChange = size => {
  tableData.value.page.size = size
  getProcessListFun()
}

const handleCurrentChange = current => {
  tableData.value.page.current = current
  getProcessListFun()
}

const getPostProcessFun = async () => {
  checkSelection.value = []
  try {
    const res = await getPostProcess({
      postCode: props.curPostCodeCopy
    })

    if (res.code == 200 && res.data.length > 0) {
      const selectedItems = []
      res.data.forEach(processCode => {
        const matchingItem = tableData.value.data.find(item => item.bizProcessCode == processCode)
        if (matchingItem) {
          selectedItems.push(matchingItem)
        }
      })
      checkSelection.value = selectedItems
    }
  } catch (error) {
    console.error('获取已配置流程失败:', error)
    ElMessage.error('获取已配置流程失败')
  }
}

const selectionChange = selection => {
  postBizProcessList.value = selection.map(item => ({
    bizProcessCode: item.bizProcessCode,
    postCode: props.curPostCodeCopy
  }))
}

const createPostProcessFun = async () => {
  try {
    const res = await createPostProcess(postBizProcessList.value)
    if (res.code == 200) {
      emit('submitSuccessTab', 'process')
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('配置流程失败:', error)
    ElMessage.error('配置流程失败')
  }
}

const submit = () => {
  if (postBizProcessList.value.length > 0) {
    createPostProcessFun()
  } else {
    ElMessage.warning('请选择配置流程！')
  }
}

// Watchers
watch(
  () => companyId.value,
  val => {
    if (val) {
      getProcessTreeFun()
      getProcessListFun()
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.department_main {
  margin-top: 20px;
  display: flex;
}

.aside_wrap {
  width: 200px;
  min-height: 365px;
  border: 1px solid #e5e5e5;

  .aside_tree_title {
    padding: 5px;
    background: var(--el-color-info-light-8);
    font-size: 14px;
  }

  .aside_tree_list {
    padding: 5px;
  }
}

.process_table {
  flex: 1;
  margin-left: 16px;
}

:deep(.el-table__header tr th) {
  line-height: 45px;
  font-size: 14px;
  padding: 0;
}

:deep(.el-table th > .cell) {
  .el-checkbox__input {
    padding-left: 4px;
  }
}

:deep(.el-table .cell) {
  line-height: 45px;
}
</style>
