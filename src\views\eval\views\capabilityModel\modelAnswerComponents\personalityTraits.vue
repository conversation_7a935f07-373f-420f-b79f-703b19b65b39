<template>
    <div class="leader_ship_wrap bg_write">
        <div class="describe_wrap">
            <div class="describe_title">{{ itemName }}</div>
            <div class="behavior_describe flex_row_betweens">
                <div class="title">行为描述：</div>
                <ul v-html="itemDesc">
                </ul>
            </div>
        </div>
        <div class="question_wrap">
            <!-- <div class="describe_title">请结合以下人员在工作中的表现，拖动或点击进度条，定位每个人的最匹配的能力阶段。</div> -->
            <div class="question_main">
                <table v-if="tableData.length > 0">
                    <thead>
                        <tr>
                            <th class="serial" rowspan="2">序号</th>
                            <th class="post_name" rowspan="2">岗位名称</th>
                            <th
                                class="post_name"
                                v-if="type == 'eval'"
                                rowspan="2"
                            >
                                姓名
                            </th>
                            <th
                                class="importance"
                                v-if="type == ''"
                                rowspan="2"
                                colspan="3"
                            >
                                该能力对岗位的重要度
                            </th>
                            <th
                                class="set"
                                v-if="tableData.length > 0 && setAreaNum > 0"
                                :colspan="setAreaNum"
                            >
                                您认为相关人员在此能力项上处于什么阶段
                            </th>
                            <th class="post_dict" v-if="type == ''" colspan="3">
                                岗位词典重要度累计
                            </th>
                        </tr>
                        <tr>
                            <!-- <th class="btn" v-if="type == ''" v-for="(item,index) in importanceTitleList" v-model="item.dictCode">{{item.codeName}}</th> -->
                            <th
                                class="set_list"
                                v-if="setTitleList.length > 0"
                                v-for="(item, index) in setTitleList"
                                v-model="item.optionNbr"
                            >
                                {{ item.optionContent }}
                            </th>
                            <th
                                class="btn"
                                v-if="type == ''"
                                v-for="(item, index) in importanceTitleList"
                                v-model="item.dictCode"
                            >
                                {{ item.codeName }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, index) in tableData">
                            <td>{{ index + 1 }}</td>
                            <td>{{ item.postName }}</td>
                            <td v-if="type == 'eval'">{{ item.objectName }}</td>
                            <td
                                v-if="type == ''"
                                v-for="(item1, index1) in item.importanceList"
                                @click="
                                    checkImportant(
                                        tableData,
                                        index,
                                        'importance',
                                        index1,
                                        item.postCode
                                    )
                                "
                                :class="{
                                    importance_item1:
                                        item.importance == item1.dictCode &&
                                        index1 == 0,
                                    importance_item2:
                                        item.importance == item1.dictCode &&
                                        index1 == 1,
                                    importance_item3:
                                        item.importance == item1.dictCode &&
                                        index1 == 2,
                                }"
                                class="btn"
                            >
                                <span>{{ item1.codeName }}</span>
                            </td>

                            <td
                                v-if="setAreaNum > 0"
                                :colspan="setAreaNum"
                                class="progress"
                            >
                                <el-slider
                                    v-model="item.progress.progressValue"
                                    :max="item.progress.progressMaxValue"
                                    :step="0.5"
                                ></el-slider>
                            </td>
                            <td
                                v-if="type == ''"
                                v-for="(item1, index1) in item.postDictIt"
                                class=""
                            >
                                <span>{{ item1.value }}</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="no_data" v-if="tableData.length == 0">
                    暂无数据！
                </div>
            </div>
        </div>
        <div class="align_center marginT_30" v-if="tableData.length > 0">
            <el-button
                v-if="prevBtnShowSign"
                class="page_confirm_btn"
                type="primary"
                @click="prev()"
                >上一题</el-button
            >
            <el-button
                v-if="
                    nextBtnShowSign &&
                    !(
                        unAnswerItemIds.length == 1 &&
                        unAnswerItemIds[0] == itemId
                    )
                "
                class="page_confirm_btn"
                type="primary"
                @click="next()"
                >下一题</el-button
            >
            <el-button
                class="page_confirm_btn"
                type="primary"
                @click="submit()"
                v-if="
                    (unAnswerItemIds.length == 1 &&
                        unAnswerItemIds[0] == itemId) ||
                    !nextBtnShowSign
                "
                >提交</el-button
            >
        </div>
    </div>
</template>

<script>
    import {
        getItemInfo,
        saveEvalSubmit,
        getDictItem,
        answerSubmit,
    } from "../../../request/api";
    export default {
        name: "personalityTraits",
        props: {
            currentItemId: Number,
            lastBuildId: "",
            switchAnswer: Boolean,
            type: String, //从测评项目来答题
            questionInfo: Object,
        },
        data() {
            return {
                itemDesc: "",
                itemName: "",
                itemId: "",
                importanceTitleList: [],
                setTitleList: [],
                tableData: [],
                setAreaNum: 5,
                nextBtnShowSign: true,
                prevBtnShowSign: true,
                btnSign: "",
                unAnswerItemIds: [],
                nextItemId: "",
                startTime: "",
            };
        },
        components: {},
        created() {
            this.getDictItemFun();
        },
        mounted() {
            // this.getItemInfoFun(this.questionInfo)
        },
        methods: {
            checkImportant(tableData, tDindex, source, sourceIndex, postCode) {
                // console.log(tableData);
                // console.log('第几行',tDindex)
                // console.log('第几列',sourceIndex)
                this.tableData[tDindex].importanceList.forEach((item, index) => {
                    if (index == sourceIndex) {
                        this.tableData[tDindex].importance = item.dictCode;
                        let importanceCopy = this.tableData[tDindex].importance;
                        if (importanceCopy == 2) {
                            this.tableData[tDindex].progress.progressValue = 3;
                        } else if (importanceCopy == 3) {
                            this.tableData[tDindex].progress.progressValue = 4;
                        } else {
                            this.tableData[tDindex].progress.progressValue = 0;
                        }

                        if (this.tableData[tDindex].importance == item.dictCode) {
                            item.checkSign = true;
                        } else {
                            item.checkSign = false;
                        }
                    } else {
                        item.checkSign = false;
                    }
                });
            },
            prev() {
                this.btnSign = "prev";
                this.answerFun();
            },
            next() {
                this.btnSign = "next";
                this.answerFun();
            },
            submit() {
                this.btnSign = "submit";
                this.answerFun();
            },
            answerFun() {
                let evalSubmitRequestList = [];
                for (let i = 0; i < this.tableData.length; i++) {
                    if (this.type == "eval") {
                        if (!this.tableData[i].progress.progressValue) {
                            this.$msg.warning("请确认答完当前题目！");
                            return;
                        }
                        evalSubmitRequestList.push({
                            evalId: this.lastBuildId,
                            itemId: this.itemId,
                            startTime: this.startTime,
                            objectId: this.tableData[i].objectId,
                            optionNbr: this.tableData[i].progress.progressValue,
                            postCode: this.tableData[i].postCode,
                        });
                    } else {
                        if (!this.tableData[i].importance) {
                            this.$msg.warning("请确认答完当前题目！");
                            return;
                        }
                        // if (
                        //     this.setAreaNum > 0 &&
                        //     !this.tableData[i].progress.progressValue
                        // ) {
                        //     this.$msg.warning("请确认答完当前题目！");
                        //     return;
                        // }
                        if (
                            this.tableData[i].importance > 1 &&
                            !this.tableData[i].progress.progressValue
                        ) {
                            this.$msg.warning("请确认答完当前题目！");
                            return;
                        }

                        evalSubmitRequestList.push({
                            evalId: this.lastBuildId,
                            importance: this.tableData[i].importance,
                            itemId: this.itemId,
                            startTime: this.startTime,
                            objectId: this.tableData[i].objectId,
                            optionNbr: this.tableData[i].progress.progressValue,
                            postCode: this.tableData[i].postCode,
                        });
                    }
                }
                // console.log('提交列表',evalSubmitRequestList)
                this.saveEvalSubmitFun(evalSubmitRequestList);
            },

            //数据字典
            getDictItemFun() {
                getDictItem({
                    dictId: "IMPORTANCE",
                }).then((res) => {
                    this.importanceTitleList = [];
                    if (res.code == 200) {
                        this.importanceTitleList = res.data.map((item) => {
                            return {
                                codeName: item.codeName,
                                dictCode: item.dictCode,
                                checkSign: false,
                            };
                        });
                    }
                });
            },
            // 获取答题信息
            getItemInfoFun(res) {
                // val 题目接口返回所有数据
                this.setAreaNum = res.optionList ? res.optionList.length : 0;
                let progressMaxNum = 0;
                if (res.optionList && res.optionList.length > 0) {
                    progressMaxNum =
                        res.optionList[res.optionList.length - 1].optionNbr;
                }
                this.itemDesc = res.itemDesc;
                this.itemName = res.itemName;
                this.itemId = res.itemId;
                this.setTitleList = res.optionList ? res.optionList : [];
                this.unAnswerItemIds = res.unAnswerItemIds;
                this.nextItemId = res.nextItemId;
                this.startTime = res.startTime;
                if (res.nextItemId == 0) {
                    this.nextBtnShowSign = false;
                } else {
                    this.nextBtnShowSign = true;
                }
                if (res.preItemId == 0) {
                    this.prevBtnShowSign = false;
                } else {
                    this.prevBtnShowSign = true;
                }
                if (this.type == "eval") {
                    // 测评项目
                    this.tableData = res.objectList
                        ? res.objectList.map((item) => {
                              return {
                                  postName: item.postName,
                                  postCode: item.postCode,
                                  progress: {
                                      progressValue: item.optionNbr
                                          ? item.optionNbr
                                          : 0,
                                      progressMaxValue: progressMaxNum,
                                  },
                                  importance: item.importance,
                                  objectId: item.objectId,
                                  optionNbr: item.optionNbr ? item.optionNbr : 0,
                                  objectName: item.objectName,
                              };
                          })
                        : [];
                } else {
                    this.tableData = res.objectList
                        ? res.objectList.map((item) => {
                              return {
                                  postName: item.postName,
                                  postCode: item.postCode,
                                  importanceList: this.defaultCheckImportance(
                                      item.importance
                                  ),
                                  postDictIt: this.dictCountComputed(item.maps),
                                  progress: {
                                      progressValue: item.optionNbr
                                          ? item.optionNbr
                                          : 0,
                                      progressMaxValue: progressMaxNum,
                                  },
                                  importance: item.importance,
                                  objectId: item.objectId,
                                  optionNbr: item.optionNbr,
                              };
                          })
                        : [];
                }
                // console.log(this.tableData)
            },
            dictCountComputed(val) {
                let curDictCount = [];
                for (let i = 0; i < this.importanceTitleList.length; i++) {
                    for (let j = 0; j < val.length; j++) {
                        if (
                            this.importanceTitleList[i].dictCode == val[j].dict_code
                        ) {
                            curDictCount.push({
                                value: val[j].count,
                            });
                        }
                    }
                }
                return curDictCount;
            },
            defaultCheckImportance(val) {
                if (val) {
                    for (let i = 0; i < this.importanceTitleList.length; i++) {
                        if (this.importanceTitleList[i].dictCode == val) {
                            this.importanceTitleList[i].checkSign = true;
                        }
                    }
                }
                return this.importanceTitleList;
            },
            // 保存答题
            saveEvalSubmitFun(evalSubmitRequestList) {
                saveEvalSubmit(evalSubmitRequestList).then((res) => {
                    // console.log(res)
                    if (res.code == 200) {
                        this.$msg.success(res.msg);
                        if (this.btnSign == "prev") {
                            this.$emit("prevStep");
                        } else if (this.btnSign == "next") {
                            this.$emit("nextStep");
                        } else if (this.btnSign == "submit") {
                            if (
                                (this.unAnswerItemIds.length == 1 &&
                                    this.unAnswerItemIds[0] == this.itemId) ||
                                this.unAnswerItemIds.length == 0
                            ) {
                                // 答最后一道未答题目
                                this.answerSubmitFun();
                            } else {
                                // 答所有题目的最后一道
                                this.$emit("submit");
                            }
                        }
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
            // 提交答题
            answerSubmitFun() {
                answerSubmit({
                    evalId: this.lastBuildId,
                }).then((res) => {
                    if (res.code == 200) {
                        this.$emit("submit");
                    }
                });
            },
        },
        watch: {
            questionInfo: {
                handler: function (val) {
                    this.tableData = [];
                    this.getItemInfoFun(val);
                },
                deep: true,
            },
        },
    };
</script>

<style scoped lang='less'>
    .leader_ship_wrap {
        .describe_title {
            color: #222222;
            font-weight: 700;
            // height: 40px;
            line-height: 40px;
        }
        .describe_wrap {
            .behavior_describe {
                .title {
                    width: 80px;
                }
                ul {
                    flex: 1;
                    white-space: pre-wrap;
                    li {
                        margin: 0 0 10px 0;
                        i {
                            margin: 0 3px 0 0;
                            line-height: 40px;
                            vertical-align: middle;
                            display: inline-block;
                            width: 6px;
                            height: 6px;
                            border-radius: 50%;
                            background: #222222;
                        }
                    }
                }
            }
        }
        .question_wrap {
            .question_main {
                table {
                    width: 100%;
                    // border-collapse:0;
                    border-spacing: 1px;
                }
                thead {
                    tr {
                        background: #f4f4f4;

                        th {
                            height: 40px;
                            color: #212121;
                            font-weight: normal;
                        }
                        .serial {
                        }
                        .post_name {
                        }
                        .importance {
                        }
                        .set {
                            // max-width: 20px;
                            // background: pink;
                        }
                        .post_dict {
                        }
                        .btn {
                        }
                        .set_list {
                            width: 110px;
                        }
                    }
                }
                tbody {
                    tr {
                        height: 40px;
                        text-align: center;
                        background: #ebf4ff;
                        .btn {
                            width: 80px;
                            span {
                                width: 70px;
                                display: inline-block;
                                height: 33px;
                                line-height: 33px;
                                background: #d4e5fa;
                                cursor: pointer;
                            }
                        }
                        .importance_item1 {
                            span {
                                background: #f9926f;
                                color: #fff;
                            }
                        }
                        .importance_item2 {
                            span {
                                background: #449cff;
                                color: #fff;
                            }
                        }
                        .importance_item3 {
                            span {
                                background: #fbb72c;
                                color: #fff;
                            }
                        }
                        .progress {
                            padding: 0 0px;
                        }
                    }
                    tr:nth-child(2n) {
                        // background: #F4F4F4;
                        // background: #EBF4FF;
                    }
                    .el-slider__runway {
                        background: #d4e5fa;
                    }
                }
                .no_data {
                    text-align: center;
                    line-height: 60px;
                    color: #909399;
                }
            }
        }
    }
</style>