<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
defineOptions({ name: 'model' })
const activeIndex = ref('3')
const coreList = ref([
  {
    title: '【GTMB】销售营销2B'
  },
  {
    title: '【GTMC】销售营销2C'
  },
  {
    title: '【PLM】产品全生命周期管理'
  },
  {
    title: '【SCM-P&O】供应链 计划订单'
  },
  {
    title: '【SCM-P&S】供应链采购与供应商管理'
  },
  {
    title: '【SCM-M&PL】供应链制造与厂内物流'
  },
  {
    title: '【FPD】成套设备完美交付'
  },
  {
    title: '【E2E-QM】端到端质量管理'
  },
  {
    title: '【ABC】全业务链成本优化'
  },
  {
    title: '【DGA】目标落地论证'
  },
  {
    title: '【S&OP】销售与业务协同'
  },
  {
    title: '【O&PM】组织与人才管理'
  },
  {
    title: '【B&FM】预算与财务管理'
  },
  {
    title: '【PS&D】流程系统与数字化'
  }
])
const modelList = ref([
  {
    id: 1,
    name: '管理计划策略',
    desc: '管理计划策略中的市场洞察与趋势预判能力，是指企业通过建立系统化的数据收集体系（涵盖行业动态、政策法规、技术创新、消费者行为、竞品动态等多维度数据），运用数据分析工具（如 SWOT 分析、PESTEL 模型）与场景模拟技术，精准识别市场增长机会（如智能家居细分领域、节能家电政策红利）与潜在风险（如原材料价格波动、贸易壁垒升级），并将分析结果转化为需求预测调整、产能布局优化、产品研发方向等战略输入的能力。该能力要求跨部门协同（市场调研、供应链计划、产品规划），通过定期召开战略研讨会，将市场信号转化为可执行的供应链策略，确保企业资源投入与市场趋势同步。',
    affect:
      '①战略短视，错过新兴市场机会；②资源错配，跟风竞争陷入红海；③风险响应滞后，导致库存积压或产能闲置；④部门数据割裂，需求传导失真；⑤客户需求变化应对被动，成本激增。',
    manage:
      '①精准布局细分市场，指导产能储备；②提前应对政策技术变化，降低风险；③跨部门数据协同，形成共识计划；④优化资源分配，聚焦高增长品类；⑤快速响应市场变化，缩短决策周期。',
    chartData: [
      {
        name: '流程端到端',
        value: '0'
      },
      {
        name: '输入输出文档',
        value: '0'
      },
      {
        name: '业务规则',
        value: '9'
      },
      {
        name: '业务 KPI',
        value: '5'
      },
      {
        name: '组织设置',
        value: '9'
      },
      {
        name: '岗位角色职责',
        value: '9'
      },
      {
        name: '岗位协同 RACI',
        value: '5'
      },
      {
        name: '组织岗位 KPI',
        value: '9'
      },
      {
        name: '人员动力',
        value: '5'
      },
      {
        name: '人员能力要求',
        value: '9'
      },
      {
        name: '人员能力评估',
        value: '9'
      },
      {
        name: '能力培训',
        value: '9'
      },
      {
        name: '系统赋能',
        value: '5'
      },
      {
        name: '数据治理',
        value: '9'
      },
      {
        name: '系统改善规划',
        value: '9'
      },
      {
        name: '自动化与智能化设备',
        value: '4'
      },
      {
        name: '算法赋能',
        value: '4'
      },
      {
        name: '业务模型优化',
        value: '4'
      },
      {
        name: '数据驱动决策',
        value: '4'
      },
      {
        name: '自动化流程优化',
        value: '4'
      },
      {
        name: '智能风险预警',
        value: '0'
      }
    ]
  },
  {
    id: 2,
    name: '管理需求计划',
    desc: '管理需求计划能力是指针对家电行业内销 TOB（大宗客户 / 工程订单）、内销 TOC（零售渠道 / 电商平台）、外销（海外市场 / 区域定制）等不同渠道的需求特性，整合历史销售数据、客户订单明细、促销活动排期、库存水位、市场调研结果等多维度输入，运用时间序列分析、机器学习算法（如随机森林模型）建立分层需求预测模型（如 TOB 客户专属模型纳入项目进度与付款节点，TOC 模型联动天气数据与促销日历，外销模型包含本地化政策与物流周期），并通过月度预测评审会动态校准预测参数，确保预测结果与实际需求偏差率控制在合理区间（战略客户≤5%、普通客户≤10%），为采购计划、生产排程提供精准输入的能力。',
    affect:
      '①预测粗放导致交付延误或库存积压；②外销需求忽视本地化，退货率上升；③模型僵化引发物料过剩；④产销脱节，订单履约率下降；⑤需求变更处理慢，客户投诉增加。',
    manage:
      '①分层预测提升精准度，优化库存结构；②降低产成品周转天数，释放资金；③产销协同增强，资源匹配度提升；④提前应对需求波动，订单满足率提高；⑤数据闭环反推策略调整，减少滞销。',
    chartData: [
      {
        name: '流程端到端',
        value: '38'
      },
      {
        name: '输入输出文档',
        value: '4'
      },
      {
        name: '业务规则',
        value: '48'
      },
      {
        name: '业务 KPI',
        value: '43'
      },
      {
        name: '组织设置',
        value: '43'
      },
      {
        name: '岗位角色职责',
        value: '43'
      },
      {
        name: '岗位协同 RACI',
        value: '28'
      },
      {
        name: '组织岗位 KPI',
        value: '28'
      },
      {
        name: '人员动力',
        value: '28'
      },
      {
        name: '人员能力要求',
        value: '28'
      },
      {
        name: '人员能力评估',
        value: '28'
      },
      {
        name: '能力培训',
        value: '28'
      },
      {
        name: '系统赋能',
        value: '50'
      },
      {
        name: '数据治理',
        value: '50'
      },
      {
        name: '系统改善规划',
        value: '50'
      },
      {
        name: '自动化与智能化设备',
        value: '31'
      },
      {
        name: '算法赋能',
        value: '42'
      },
      {
        name: '业务模型优化',
        value: '42'
      },
      {
        name: '数据驱动决策',
        value: '42'
      },
      {
        name: '自动化流程优化',
        value: '42'
      },
      {
        name: '智能风险预警',
        value: '48'
      }
    ]
  },
  {
    id: 3,
    name: '管理主计划',
    desc: '管理主计划能力是指整合销售预测、自有工厂产能（设备利用率、人员配置、模具可用性）、外协工厂产能（产能波动系数、质量稳定性）、长周期物料供应周期（如压缩机 LT=8 周、PCB 板 LT=6 周）等关键资源数据，制定月度 / 周度主生产计划（MPS），并通过召开月度供应评审会（S&OP 会议）动态调整计划参数（如旺季产能爬坡方案、淡季设备检修计划），确保生产计划与销售需求、物料齐套性、产能负荷相平衡的能力。该能力要求运用 APS（高级计划排程系统）进行产能模拟，识别瓶颈环节（如注塑机产能不足），制定跨工厂产能调配策略，最终实现产供销平衡，降低紧急插单率与库存周转率目标达成。',
    affect:
      '①产能错配导致交付延迟；②物料脱节引发停产；③多工厂负荷不均，成本增加；④计划变更频繁，产线效率下降；⑤协同低效，库存周转率降低。',
    manage:
      '①均衡产能分配，降低外协成本；②物料齐套率提升，减少停产风险；③控制计划调整，提升产线效率；④优化库存策略，提高周转速度；⑤快速响应紧急订单，缩短履约周期。',
    chartData: [
      {
        name: '流程端到端',
        value: '61'
      },
      {
        name: '输入输出文档',
        value: '38'
      },
      {
        name: '业务规则',
        value: '66'
      },
      {
        name: '业务 KPI',
        value: '64'
      },
      {
        name: '组织设置',
        value: '62'
      },
      {
        name: '岗位角色职责',
        value: '64'
      },
      {
        name: '岗位协同 RACI',
        value: '46'
      },
      {
        name: '组织岗位 KPI',
        value: '29'
      },
      {
        name: '人员动力',
        value: '29'
      },
      {
        name: '人员能力要求',
        value: '45'
      },
      {
        name: '人员能力评估',
        value: '45'
      },
      {
        name: '能力培训',
        value: '45'
      },
      {
        name: '系统赋能',
        value: '63'
      },
      {
        name: '数据治理',
        value: '63'
      },
      {
        name: '系统改善规划',
        value: '54'
      },
      {
        name: '自动化与智能化设备',
        value: '28'
      },
      {
        name: '算法赋能',
        value: '43'
      },
      {
        name: '业务模型优化',
        value: '43'
      },
      {
        name: '数据驱动决策',
        value: '43'
      },
      {
        name: '自动化流程优化',
        value: '26'
      },
      {
        name: '智能风险预警',
        value: '43'
      }
    ]
  },
  {
    id: 4,
    name: '管理工序计划',
    desc: '管理工序计划能力是指将主生产计划分解为日生产计划，结合车间设备状态（OEE 指标）、人员技能矩阵、模具更换时间（如冰箱门体模具切换需 90 分钟）、工艺路径约束（如喷涂线固化时间）等现场数据，运用 MES 系统进行工序排程（优先排序规则：交期优先、产能瓶颈优先），实时监控工单执行进度（完工数量、良品率、设备停机时间），并通过安灯系统（Andon）快速响应工序异常（如缺料、设备故障、质量缺陷），实现从计划到执行的闭环管控能力。该能力要求建立异常处理 SOP（如 30 分钟响应、2 小时解决），确保日计划达成率≥95%，降低产线等待时间与返工成本。',
    affect:
      '①人工排程效率低，产线等待时间长；②异常处理滞后，引发批量返工；③数据脱节导致计划调整慢；④多品种排程低效，换线成本高；⑤质量波动大，生产成本上升。',
    manage:
      '①智能排程提升产线效率；②实时监控缩短异常响应时间；③支持混线生产，降低换线成本；④工序标准化提高良品率；⑤数据透明化提升计划达成率。',
    chartData: [
      {
        name: '流程端到端',
        value: '17'
      },
      {
        name: '输入输出文档',
        value: '12'
      },
      {
        name: '业务规则',
        value: '22'
      },
      {
        name: '业务 KPI',
        value: '22'
      },
      {
        name: '组织设置',
        value: '20'
      },
      {
        name: '岗位角色职责',
        value: '22'
      },
      {
        name: '岗位协同 RACI',
        value: '22'
      },
      {
        name: '组织岗位 KPI',
        value: '21'
      },
      {
        name: '人员动力',
        value: '20'
      },
      {
        name: '人员能力要求',
        value: '22'
      },
      {
        name: '人员能力评估',
        value: '22'
      },
      {
        name: '能力培训',
        value: '22'
      },
      {
        name: '系统赋能',
        value: '19'
      },
      {
        name: '数据治理',
        value: '22'
      },
      {
        name: '系统改善规划',
        value: '11'
      },
      {
        name: '自动化与智能化设备',
        value: '7'
      },
      {
        name: '算法赋能',
        value: '9'
      },
      {
        name: '业务模型优化',
        value: '4'
      },
      {
        name: '数据驱动决策',
        value: '6'
      },
      {
        name: '自动化流程优化',
        value: '4'
      },
      {
        name: '智能风险预警',
        value: '6'
      }
    ]
  },
  {
    id: 5,
    name: '管理物料需求计划',
    desc: '管理物料需求计划能力是指依据主生产计划、BOM 清单（含损耗率、替代料规则）、库存状态（安全库存、在途量、呆滞库存），运用 MRPⅡ 系统分解自有工厂与外协工厂的物料需求，制定采购计划（含长周期物料安全库存策略）与委外生产计划，跟踪物料到货进度（通过 EDI 系统与供应商直连），确保物料齐套率≥95% 的能力。该能力要求区分关键物料（如芯片、压缩机）与非关键物料（如包装件），实施双源采购（关键物料≥2 家供应商）与 VMI（供应商管理库存）模式，动态调整采购批量（经济订货批量 EOQ 模型），避免缺料停产与库存积压，实现物料需求与生产计划的精准匹配。',
    affect:
      '①需求计算粗放，缺料率高；②外协备料失控，交付率低；③库存冗余，呆滞物料占比高；④流程割裂，计划匹配度低；⑤单一来源风险，断供损失大。',
    manage:
      '①精准计算需求，降低缺料风险；②协同外协提升交付率；③动态优化库存，减少呆滞物料；④数据贯通提高计划匹配度；⑤关键物料双源，降低断供风险。',
    chartData: [
      {
        name: '流程端到端',
        value: '10'
      },
      {
        name: '输入输出文档',
        value: '0'
      },
      {
        name: '业务规则',
        value: '10'
      },
      {
        name: '业务 KPI',
        value: '8'
      },
      {
        name: '组织设置',
        value: '10'
      },
      {
        name: '岗位角色职责',
        value: '10'
      },
      {
        name: '岗位协同 RACI',
        value: '10'
      },
      {
        name: '组织岗位 KPI',
        value: '8'
      },
      {
        name: '人员动力',
        value: '8'
      },
      {
        name: '人员能力要求',
        value: '0'
      },
      {
        name: '人员能力评估',
        value: '5'
      },
      {
        name: '能力培训',
        value: '5'
      },
      {
        name: '系统赋能',
        value: '0'
      },
      {
        name: '数据治理',
        value: '10'
      },
      {
        name: '系统改善规划',
        value: '10'
      },
      {
        name: '自动化与智能化设备',
        value: '7'
      },
      {
        name: '算法赋能',
        value: '5'
      },
      {
        name: '业务模型优化',
        value: '2'
      },
      {
        name: '数据驱动决策',
        value: '4'
      },
      {
        name: '自动化流程优化',
        value: '2'
      },
      {
        name: '智能风险预警',
        value: '1'
      }
    ]
  },
  {
    id: 6,
    name: '管理库存',
    desc: '管理库存能力是指针对家电行业成品（整机）、在制品（半成品）、原材料（零部件）三类库存，制定差异化库存策略的能力：成品库存采用 ABC 分类法（A 类畅销品安全库存 15 天、C 类滞销品 45 天），结合渠道库存共享数据（经销商库存 T+1 同步）实施动态补货；在制品库存通过 MES 系统实时监控工序间流转，设定合理缓冲量（如焊接工序后缓冲 2 小时产能），避免堆积与短缺；原材料库存运用 JIT（准时制）与 VMI（供应商管理库存）模式，关键物料安全库存覆盖 4 周需求，非关键物料采用批量采购降低物流成本。同时，建立滞销品淘汰机制（如库龄超 60 天触发促销）与呆滞物料处理流程（每月盘点 + 折价处理），平衡库存周转效率（目标≥6 次 / 年）与供应安全（断供率≤3%）。',
    affect:
      '①策略粗放导致断供与积压并存；②渠道库存割裂，畅销品缺货；③周转低下，资金占用高；④在制品失控，质量追溯难；⑤数据不共享，紧急采购成本高。',
    manage:
      '①分层管控提升周转效率；②渠道协同降低缺货率；③在制品监控减少返工；④建立预警机制处理呆滞物料；⑤VMI/JIT 模式节约库存成本。',
    chartData: [
      {
        name: '流程端到端',
        value: '15'
      },
      {
        name: '输入输出文档',
        value: '8'
      },
      {
        name: '业务规则',
        value: '12'
      },
      {
        name: '业务 KPI',
        value: '5'
      },
      {
        name: '组织设置',
        value: '13'
      },
      {
        name: '岗位角色职责',
        value: '16'
      },
      {
        name: '岗位协同 RACI',
        value: '0'
      },
      {
        name: '组织岗位 KPI',
        value: '0'
      },
      {
        name: '人员动力',
        value: '0'
      },
      {
        name: '人员能力要求',
        value: '0'
      },
      {
        name: '人员能力评估',
        value: '0'
      },
      {
        name: '能力培训',
        value: '0'
      },
      {
        name: '系统赋能',
        value: '15'
      },
      {
        name: '数据治理',
        value: '15'
      },
      {
        name: '系统改善规划',
        value: '14'
      },
      {
        name: '自动化与智能化设备',
        value: '5'
      },
      {
        name: '算法赋能',
        value: '5'
      },
      {
        name: '业务模型优化',
        value: '5'
      },
      {
        name: '数据驱动决策',
        value: '5'
      },
      {
        name: '自动化流程优化',
        value: '4'
      },
      {
        name: '智能风险预警',
        value: '5'
      }
    ]
  },
  {
    id: 7,
    name: '管理订单策略',
    desc: '管理订单策略能力是指根据订单类型（MTS 备货生产、MTO 按单生产、ETO 工程定制）、客户等级（战略客户 / 普通客户）、订单利润贡献，制定差异化履约规则的能力：MTS 订单按历史销量预测备货，周转天数控制在 30 天内；MTO 订单（如定制化冰箱）采用拉动式生产，交付周期≤45 天；ETO 订单（如商用空调项目）实施项目制管理，交期承诺精准到工序节点。同时，建立订单优先级规则（战略客户订单优先排产、高毛利订单产能倾斜）与交期承诺机制（通过 ATP 可承诺量系统自动校验库存 / 产能），确保紧急订单（72 小时急单）响应时效≤2 小时，常规订单评审周期≤24 小时，实现资源向高价值订单集中，提升订单履约效率与客户满意度。',
    affect:
      '①模式错配导致库存积压；②优先级混乱影响核心客户；③交期盲目承诺，爽约率高；④分类粗放增加换线成本；⑤人工评审效率低，错失订单。',
    manage:
      '①精准履约提升库存与交付效率；②资源倾斜增强客户粘性；③智能承诺提高交期准确率；④绿色通道提升急单响应速度；⑤数据驱动优化资源分配。',
    chartData: [
      {
        name: '流程端到端',
        value: '1'
      },
      {
        name: '输入输出文档',
        value: '5'
      },
      {
        name: '业务规则',
        value: '4'
      },
      {
        name: '业务 KPI',
        value: '0'
      },
      {
        name: '组织设置',
        value: '5'
      },
      {
        name: '岗位角色职责',
        value: '5'
      },
      {
        name: '岗位协同 RACI',
        value: '0'
      },
      {
        name: '组织岗位 KPI',
        value: '0'
      },
      {
        name: '人员动力',
        value: '0'
      },
      {
        name: '人员能力要求',
        value: '0'
      },
      {
        name: '人员能力评估',
        value: '0'
      },
      {
        name: '能力培训',
        value: '0'
      },
      {
        name: '系统赋能',
        value: '3'
      },
      {
        name: '数据治理',
        value: '5'
      },
      {
        name: '系统改善规划',
        value: '3'
      },
      {
        name: '自动化与智能化设备',
        value: '0'
      },
      {
        name: '算法赋能',
        value: '0'
      },
      {
        name: '业务模型优化',
        value: '0'
      },
      {
        name: '数据驱动决策',
        value: '0'
      },
      {
        name: '自动化流程优化',
        value: '0'
      },
      {
        name: '智能风险预警',
        value: '0'
      }
    ]
  },
  {
    id: 8,
    name: '管理订单执行',
    desc: '管理订单执行能力是指按照订单策略高效完成 MTS/MTO/ETO 订单交付的能力：MTS 订单通过自动化生产线批量生产，实时监控成品入库进度（每小时更新库存数据）；MTO 订单触发柔性生产流程，支持多品种混线生产（如空调室内机与室外机共线），换线时间≤30 分钟；ETO 订单组建专项团队，全程跟踪定制化部件采购、工序加工、质量检验进度。同时，通过 WMS 系统监控物流状态（仓储货位、运输轨迹），快速处理订单变更（如数量 ±10%、交期调整 ±3 天），建立三级异常处理机制（轻微异常 30 分钟响应、重大异常 2 小时解决），确保订单执行过程透明可控，最终实现交付准时率≥98%、订单变更处理时效≤4 小时的管理目标。',
    affect:
      '①突发订单应对差，缺货率高；②变更处理慢，调配成本高；③信息滞后导致客户满意度低；④异常失控增加交付延迟；⑤流程不规范引发合规风险。',
    manage:
      '①动态插单提升大促订单满足率；②系统自动调配降低变更成本；③全程可视提高客户响应速度；④分级处理减少交付延迟；⑤流程标准化降低合规风险。',
    chartData: [
      {
        name: '流程端到端',
        value: '41'
      },
      {
        name: '输入输出文档',
        value: '20'
      },
      {
        name: '业务规则',
        value: '33'
      },
      {
        name: '业务 KPI',
        value: '20'
      },
      {
        name: '组织设置',
        value: '32'
      },
      {
        name: '岗位角色职责',
        value: '36'
      },
      {
        name: '岗位协同 RACI',
        value: '22'
      },
      {
        name: '组织岗位 KPI',
        value: '18'
      },
      {
        name: '人员动力',
        value: '18'
      },
      {
        name: '人员能力要求',
        value: '18'
      },
      {
        name: '人员能力评估',
        value: '18'
      },
      {
        name: '能力培训',
        value: '18'
      },
      {
        name: '系统赋能',
        value: '42'
      },
      {
        name: '数据治理',
        value: '41'
      },
      {
        name: '系统改善规划',
        value: '37'
      },
      {
        name: '自动化与智能化设备',
        value: '26'
      },
      {
        name: '算法赋能',
        value: '26'
      },
      {
        name: '业务模型优化',
        value: '26'
      },
      {
        name: '数据驱动决策',
        value: '26'
      },
      {
        name: '自动化流程优化',
        value: '22'
      },
      {
        name: '智能风险预警',
        value: '21'
      }
    ]
  },
  {
    id: 9,
    name: '管理订单全链路',
    desc: '管理订单全链路能力是指通过数字化平台（如 SAP IBP、MES、LES）实现订单从接收（CRM 系统对接）、计划（主计划分解）、生产（工序工单执行）、物流（仓储配送）到交付（客户签收）的全流程可视化，实时采集各环节数据（如订单状态、生产进度、物流位置），建立异常事件预警机制（如交付延迟超 24 小时触发红色预警、质量缺陷率超 2% 自动停线），并通过数据看板（Dashboard）提供决策支持（如履约周期分析、异常事件热力图）的能力。该能力要求打通系统数据壁垒（如 ERP 与 WMS 数据实时同步），沉淀异常处理经验（建立案例库），实现订单执行过程的透明化、智能化，为供应链优化提供数据支撑。',
    affect:
      '①信息断层导致协作低效；②预警缺失引发客户投诉；③响应迟缓增加退换货率；④数据缺失导致决策盲目；⑤经验未沉淀，问题重复发生。',
    manage:
      '①数据同步提升协作效率；②主动预警降低客户投诉；③知识沉淀减少问题复发；④数据驱动优化瓶颈环节；⑤开放查询提升客户满意度与复购率。',
    chartData: [
      {
        name: '流程端到端',
        value: '1'
      },
      {
        name: '输入输出文档',
        value: '3'
      },
      {
        name: '业务规则',
        value: '3'
      },
      {
        name: '业务 KPI',
        value: '0'
      },
      {
        name: '组织设置',
        value: '3'
      },
      {
        name: '岗位角色职责',
        value: '3'
      },
      {
        name: '岗位协同 RACI',
        value: '3'
      },
      {
        name: '组织岗位 KPI',
        value: '0'
      },
      {
        name: '人员动力',
        value: '0'
      },
      {
        name: '人员能力要求',
        value: '0'
      },
      {
        name: '人员能力评估',
        value: '0'
      },
      {
        name: '能力培训',
        value: '0'
      },
      {
        name: '系统赋能',
        value: '2'
      },
      {
        name: '数据治理',
        value: '1'
      },
      {
        name: '系统改善规划',
        value: '2'
      },
      {
        name: '自动化与智能化设备',
        value: '1'
      },
      {
        name: '算法赋能',
        value: '1'
      },
      {
        name: '业务模型优化',
        value: '1'
      },
      {
        name: '数据驱动决策',
        value: '1'
      },
      {
        name: '自动化流程优化',
        value: '1'
      },
      {
        name: '智能风险预警',
        value: '1'
      }
    ]
  }
])

const activeId = ref(1)
const changeModel = id => {
  activeId.value = id
}
const activeModel = computed(() => modelList.value.find(item => item.id == activeId.value))
// const chartData = ref(modelList.value[0].chartData)

const setOptions = () => {
  return {
    xAxisData: activeModel.value.chartData.map(item => item.name),
    xAxis: {
      axisLabel: {
        interval: 0, // 强制显示所有标签
        rotate: 45, // 标签倾斜度 -90 至 90 默认为0
        margin: 10, // 刻度标签与轴线之间的距离
        fontSize: 12 // 刻度标签的文字大小
      }
    },
    series: [
      {
        name: '销量',
        type: 'bar',
        showBackground: true,
        label: {
          show: true
        },
        itemStyle: {
          color: '#53B8FF'
        },
        data: activeModel.value.chartData.map(item => item.value)
      }
    ]
  }
}
</script>
<template>
  <div class="core-list">
    <div
      class="list"
      :class="{ active: activeIndex == index }"
      v-for="(list, index) in coreList"
      @click="activeIndex = index"
    >
      {{ list.title }}
    </div>
  </div>
  <div class="title">查看能力能力结构</div>
  <div class="model-content">
    <div class="model-list-wrap">
      <div
        class="model-list"
        :class="{ active: activeId == list.id }"
        v-for="list in modelList"
        :key="list.id"
        @click="changeModel(list.id)"
      >
        {{ list.name }}
      </div>
    </div>
    <div class="model-info">
      <div class="page-title-line">能力简介</div>
      <div class="desc">{{ activeModel.desc }}</div>
      <div class="page-title-line">能力DNA分布</div>
      <div class="desc" style="height: 300px">
        <EChartsBar :options="setOptions()" />
      </div>
      <div class="flex-box">
        <div>
          <div class="page-title-line">缺乏该项能力，对管理的影响</div>
          <div class="list-content">
            {{ activeModel.affect }}
            <!-- <div class="list-item" v-for="item in activeModel.affect" :key="item.label">
              <div class="label">{{ item.label }}：</div>
              <div class="value">{{ item.value }}</div>
            </div> -->
          </div>
        </div>
        <div>
          <div class="page-title-line">提升该能力，对管理的价值</div>
          <div class="list-content">
            {{ activeModel.affect }}
            <!-- <div class="list-item" v-for="item in activeModel.manage" :key="item.label">
              {{ item }}
              <div class="label">{{ item.label }}：</div>
              <div class="value">{{ item.value }}</div>
            </div> -->
          </div>
        </div>
      </div>
      <div class="btn-wrap">
        <div class="btn">取消关注</div>
        <div class="btn">加入评估模块</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.title {
  font-size: 16px;
  color: #333333;
  line-height: 32px;
  margin-bottom: 11px;
}
.core-list {
  display: flex;
  flex-flow: row wrap;
  gap: 10px;
  margin-bottom: 30px;
  .list {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333333;
    width: calc((100% - 60px) / 7);
    height: 35px;
    padding: 0px 8px;
    border-radius: 5px 5px 5px 5px;
    line-height: 18px;
    background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #c6dbf3;
    text-align: center;
    cursor: pointer;
    &:hover,
    &.active {
      border: 1px solid #53acff;
      color: #40a0ff;
      box-shadow: 0px 0px 10px 0px rgba(124, 182, 237, 0.5);
    }
  }
}
.model-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-start;
  .model-list-wrap {
    flex: 0 0 200px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px 8px 8px 8px;
    padding: 16px 13px;
    margin-right: 17px;
    .model-list {
      font-size: 14px;
      color: #6c757e;
      line-height: 35px;
      background: #f0f9ff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #a5c1dc;
      text-align: center;
      margin-bottom: 10px;
      cursor: pointer;
      &.active {
        background: #83c1ff;
        border-color: #83c1ff;
        color: #fff;
      }
    }
  }
  .model-info {
    .desc {
      padding: 18px 25px;
      font-weight: 400;
      font-size: 14px;
      color: #3d3d3d;
      line-height: 28px;
      background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      margin-bottom: 25px;
    }
    .list-content {
      background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      padding: 20px 10px 20px 26px;
      margin-bottom: 25px;
      .list-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 4px;
        line-height: 28px;
        font-size: 14px;
        .label {
          font-weight: 600;
          font-size: 14px;
          color: #3d3d3d;
          white-space: nowrap;
        }
      }
    }
    .btn-wrap {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .btn {
        width: 200px;
        line-height: 40px;
        text-align: center;
        color: #fff;
        background: #53a9f9;
        border-radius: 42px;
        cursor: pointer;
        & + .btn {
          margin-left: 20px;
        }
      }
    }
  }
}
</style>
