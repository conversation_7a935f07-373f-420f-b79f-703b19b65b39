<template>
  <div class="work_activities_main">
    <div class="marginT_30"></div>
    <div class="page_second_title">本岗位工作活动</div>
    <div class="marginT_20"></div>
    <div class="change_post flex_row_end">
      <div class="post_title bold">岗位 :</div>
      <div class="flex_row_start">
        <div class="post_list_wrap flex_row_start">
          <div
            class="post_list"
            :class="{ active: postCode == item.postCode }"
            @click="postChange(item.postCode)"
            v-for="item in postList"
            :key="item.postCode"
          >
            {{ item.postName }}
          </div>
        </div>
      </div>
    </div>
    <div class="marginT_16"></div>
    <div class="work_activities_content">
      <el-table :data="activityList" style="width: 100%">
        <el-table-column prop="jobActivityType" label="活动类型" width="120">
          <template #default="scope">
            <el-select class="select_style" :model-value="scope.row.jobActivityType" disabled>
              <el-option
                v-for="item in jobActivityOptions"
                :label="item.codeName"
                :value="item.dictCode"
                :key="item.dictCode"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="jobActivityName" label="工作任务名称"></el-table-column>
        <el-table-column prop="workType" label="在司/出差" width="120">
          <template #default="scope">
            <el-select
              :model-value="scope.row.workType"
              @update:model-value="val => (scope.row.workType = val)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in workTypeoptions"
                :label="item.codeName"
                :value="item.dictCode"
                :key="item.dictCode"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="timePercentage" label="时间占比" width="150">
          <template #default="scope">
            <el-input
              :model-value="scope.row.timePercentage"
              @update:model-value="val => (scope.row.timePercentage = val)"
              min="0"
              maxlength="3"
              type="number"
              placeholder
            >
              <template #append>%</template>
            </el-input>
          </template>
        </el-table-column>
        <el-table-column prop="overtimeFlag" label="需要加班" width="120">
          <template #default="scope">
            <el-select
              :model-value="scope.row.overtimeFlag"
              @update:model-value="val => (scope.row.overtimeFlag = val)"
              placeholder="请选择"
            >
              <el-option
                :label="item.codeName"
                :value="item.dictCode"
                v-for="item in yesOrNoOptions"
                :key="item.dictCode"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="overtimeFrequency" label="加班频率" width="180">
          <template #default="scope">
            <el-select
              :model-value="scope.row.overtimeFlag == 'N' ? '0' : scope.row.overtimeFrequency"
              @update:model-value="val => (scope.row.overtimeFrequency = val)"
              :disabled="scope.row.overtimeFlag == 'N'"
              placeholder="请选择"
            >
              <el-option
                v-for="item in overTimeOptions"
                :label="item.codeName"
                :value="item.dictCode"
                :key="item.dictCode"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page_second_title marginT_30">本岗主要对内的协同岗位（与哪些岗位有工作协同）</div>
    <div class="collaboration_post_content">
      <div class="post_item_wrap flex_row_wrap_start">
        <div class="flex_row_betweens xt_wrap">
          <div class="icon"></div>
        </div>
        <div class="post_item_main">
          <div class="post_item" v-for="(item, index) in teamPost" :key="item.coopPostCode">
            <div class="name">{{ item.coopPostName }}</div>
            <div class="name_icon" @click="deletePost(index)">
              <el-icon><Minus /></el-icon>
            </div>
          </div>
          <div class="work_add_btn" v-show="teamPost.length < 10">
            <el-button link @click="showDialog">
              <el-icon><Plus /></el-icon>添加
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="page_second_title marginT_30">本岗主要对外的协同角色（与哪些外部角色打交道）</div>
    <div class="collaborative_role flex_row_start">
      <div class="icon"></div>
      <el-checkbox-group v-model="coopRoleList">
        <el-checkbox v-for="item in coopRole" :label="item.dictCode" :key="item.dictCode">{{
          item.codeName
        }}</el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="text_center marginT_30">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
    <!-- 对内协同岗位选择弹窗 -->
    <el-dialog v-model="dialogShow" title="选择岗位">
      <div class="dialog_center clearfix">
        <div class="org_tree">
          <div class="dialog_title">组织</div>
          <div class="tree_list">
            <tree-comp-checkbox
              :defaultCheckedKeys="checkedOrgCode"
              :treeData="orgTreeData"
              @node-click-callback="getOrgCode"
            ></tree-comp-checkbox>
          </div>
        </div>
        <div class="post_list_wrap">
          <div class="dialog_title">
            选择岗位
            <span class="fr color_base">已选 {{ checkedPostList.length }}</span>
          </div>
          <tableComponent
            :selectionStatus="true"
            :checkSelection="checkSelection"
            @selectionChange="selectionChange"
            :size="'small'"
            :height="'300'"
            :needIndex="true"
            :needPagination="false"
            :tableData="checkedPostData"
          >
          </tableComponent>
        </div>
      </div>
      <div class="marginT_20 align_right">
        <el-button class="page_clear_btn" @click="dialogShow = false">取消</el-button>
        <el-button class="page_add_btn" @click="dialogSubmit">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Minus } from '@element-plus/icons-vue'
import {
  getEnqUserPost,
  getEnqUserActivity,
  getAllOrgInfo,
  getEnqPostInfo,
  updateEnqUserActivity
} from '../../../request/api.js'
import treeCompCheckbox from '@/components/talent/treeComps/treeCompCheckbox'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const props = defineProps({
  nextBtnText: String,
  enqId: String
})

const emit = defineEmits(['prevStep', 'nextStep'])

const userStore = useUserStore()

// 响应式状态
const postType = ref('Y')
const postCode = ref('')
const postName = ref('')
const postList = ref([])
const coopRoleList = ref([])
const jobActivityOptions = ref([])
const yesOrNoOptions = ref([])
const overTimeOptions = ref([])
const workTypeoptions = ref([])
const coopRole = ref([])
const backupData = reactive({})
const dialogShow = ref(false)
const checkedOrgCode = ref([])
const checkedPostCode = ref([])
const orgTreeData = ref([])
const orgCode = ref('')
const checkedPostList = ref([])
const checkSelection = ref([])
const checkedPostData = reactive({
  columns: [
    {
      label: '所属部门',
      prop: 'orgName'
    },
    {
      label: '岗位名称',
      prop: 'postName'
    }
  ],
  data: [],
  page: {}
})
const teamPost = ref([])
const activityList = ref([])

// 计算属性
const userId = computed(() => userStore.userInfo.userId)

// 方法
const deletePost = index => {
  teamPost.value.splice(index, 1)
}

const getEnqUserPostFun = async () => {
  const params = {
    enqId: props.enqId
  }
  try {
    const res = await getEnqUserPost(params)
    if (res.code == '200') {
      postList.value = res.data
      postCode.value = res.data[0].postCode
      postName.value = res.data[0].postName
      postType.value = res.data[0].isPrimary
      res.data.forEach(item => {
        getEnqUserActivityFun(item.postCode)
      })
    } else {
      ElMessage.error('获取岗位信息出错！')
    }
  } catch (error) {
    console.error(error)
  }
}

const postChange = val => {
  postList.value.forEach(item => {
    if (item.postCode == val) {
      postType.value = item.isPrimary
      postName.value = item.postName
    }
  })
  postCode.value = val
  if (backupData[val]) {
    activityList.value = backupData[val].activityList
    coopRoleList.value = backupData[val].coopRoleList
    teamPost.value = backupData[val].teamPost
  } else {
    getEnqUserActivityFun()
  }
}

const getEnqUserActivityFun = async postCode => {
  const params = {
    enqId: props.enqId,
    postCode: postCode
  }
  backupData[postCode] = {}
  try {
    const res = await getEnqUserActivity(params)
    if (res.code == '200') {
      const data = res.data
      let activityData = []
      let obj = {
        enqId: props.enqId,
        jobActivityCode: 'AA-01',
        overtimeFlag: 'Y',
        overtimeFrequency: '0',
        postCode: postCode,
        timePercentage: '1',
        userId: userId.value,
        jobActivityType: 'a',
        workType: '1',
        jobActivityName: 'AAA'
      }
      data.activityList.forEach(item => {
        for (const key in obj) {
          obj[key] = item[key]
          if (key == 'overtimeFrequency') {
            obj[key] = item[key] ? item[key] : '0'
          }
          if (key == 'timePercentage') {
            obj[key] = item[key] == null ? '100' : item[key]
          }
        }
        activityData.push({ ...obj })
        obj = {
          enqId: props.enqId,
          jobActivityCode: '',
          overtimeFlag: '',
          overtimeFrequency: '0',
          postCode: postCode,
          timePercentage: '',
          userId: userId.value,
          jobActivityType: '',
          workType: '',
          jobActivityName: ''
        }
      })

      backupData[postCode].activityList = activityData
      backupData[postCode].teamPost = data.postCoopListI
      backupData[postCode].coopRoleList = data.postCoopListO

      activityList.value = backupData[postCode.value].activityList
      teamPost.value = backupData[postCode.value].teamPost
      coopRoleList.value = backupData[postCode.value].coopRoleList
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
  }
}

const updateEnqUserActivityFun = async stepType => {
  const params = {
    enqId: props.enqId,
    enqPostCoopRequestList: [],
    enqUserActivityRequestList: []
  }
  const formattedData = formatterDataFun()
  params.enqPostCoopRequestList = formattedData.coopList
  params.enqUserActivityRequestList = formattedData.activityList

  try {
    const res = await updateEnqUserActivity(params)
    if (res.code == '200') {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
  }
}

const coopRoleListFormatter = (val, postCode) => {
  return val.map(item => ({
    coopPostCode: item,
    coopType: 'O',
    enqId: props.enqId,
    postCode: postCode,
    userId: userId.value
  }))
}

const formatterDataFun = () => {
  let activityList = []
  let coopList = []
  const c = JSON.parse(JSON.stringify(backupData))

  for (const key in c) {
    if (Object.prototype.hasOwnProperty.call(c, key)) {
      const list = c[key].activityList
      let teamList = c[key].teamPost
      const coopRoleList = coopRoleListFormatter(c[key].coopRoleList, key)

      teamList = teamList.concat(coopRoleList)
      activityList = activityList.concat(list)
      coopList = coopList.concat(teamList)
    }
  }

  return {
    activityList,
    coopList
  }
}

const getOrgCode = code => {
  if (code.length == 0) {
    checkedPostData.data = []
    return
  }
  const orgCodes = code.join(',')
  getEnqPostInfoFun(orgCodes)
}

const getEnqPostInfoFun = async orgCodes => {
  try {
    const res = await getEnqPostInfo({
      enqId: props.enqId,
      orgCodes: orgCodes
    })
    if (res.code == '200') {
      const checkDataArr = []
      checkedPostData.data = res.data.filter(item => {
        if (checkedPostCode.value.indexOf(item.postCode) >= 0) {
          checkDataArr.push(item)
        }
        return item.postCode != postCode.value
      })
      checkSelection.value = checkDataArr
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
  }
}

const selectionChange = selected => {
  checkedPostList.value = selected
}

const showDialog = () => {
  checkedOrgCode.value = []
  checkedPostCode.value = []
  if (teamPost.value.length == 0) {
    checkedPostData.data = []
  } else {
    const arr = []
    teamPost.value.forEach(item => {
      arr.push(item.orgCode)
      checkedPostCode.value.push(item.coopPostCode)
    })
    checkedOrgCode.value = [...new Set(arr)]
  }
  dialogShow.value = true
}

const dialogSubmit = () => {
  if (checkedPostList.value.length > 10) {
    ElMessage.warning('对内协同岗位最多10个！')
    return
  }
  teamPost.value = []
  checkedPostCode.value = []

  checkedPostList.value.forEach(item => {
    checkedPostCode.value.push(item.postCode)
    teamPost.value.push({
      coopPostCode: item.postCode,
      coopPostName: item.postName,
      orgName: item.orgName,
      orgCode: item.orgCode,
      coopType: 'I',
      enqId: props.enqId,
      postCode: postCode.value,
      userId: userId.value
    })
  })
  dialogShow.value = false
}

const getAllOrgInfoFun = async () => {
  try {
    const res = await getAllOrgInfo({
      enqId: props.enqId
    })
    if (res.code == '200') {
      orgTreeData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
  }
}

const checkData = data => {
  const arr = data
  const len = arr.length
  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    if (
      Object.keys(obj).some(
        key => ['workType', 'timePercentage', 'overtimeFlag', 'overtimeFrequency'].includes(key) && !obj[key]
      )
    ) {
      return true
    }
  }
  return false
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      updateEnqUserActivityFun('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') {
        emit('prevStep')
      }
    })
}

const nextBtn = () => {
  let len = 0
  for (const key in backupData) {
    len++
    if (Object.prototype.hasOwnProperty.call(backupData, key)) {
      const list = backupData[key].activityList
      if (checkData(list)) {
        ElMessage.warning('请完善所有岗位工作活动数据后再次提交')
        return
      } else {
        const count = list.reduce((sum, item) => sum + parseInt(item.timePercentage), 0)
        if (count > 100) {
          ElMessage.warning(`岗位：${postName.value} 工作活动时间占比总和不能大于100`)
          return
        }
      }
    }
  }
  if (len < postList.value.length) {
    ElMessage.error(`请完善其他岗位的相关信息后再次提交 `)
    return
  }
  updateEnqUserActivityFun('nextStep')
}

// 监听器
watch(postCode, (val, oldVal) => {})

watch(
  activityList,
  val => {
    backupData[postCode.value].activityList = val
  },
  { deep: true }
)

watch(
  teamPost,
  val => {
    backupData[postCode.value].teamPost = val
  },
  { deep: true }
)

watch(
  coopRoleList,
  val => {
    backupData[postCode.value].coopRoleList = val
  },
  { deep: true }
)

// 生命周期钩子
onMounted(async () => {
  const docList = await userStore.getDocList([
    'OVERTIME_FREQUENCY',
    'YES_NO',
    'WORK_TYPE',
    'COOP_ROLE',
    'JOB_ACTIVITY_TYPE'
  ])
  overTimeOptions.value = docList.OVERTIME_FREQUENCY
  yesOrNoOptions.value = docList.YES_NO
  workTypeoptions.value = docList.WORK_TYPE
  coopRole.value = docList.COOP_ROLE
  jobActivityOptions.value = docList.JOB_ACTIVITY_TYPE

  await getEnqUserPostFun()
  await getAllOrgInfoFun()
})
</script>

<style scoped lang="scss">
:deep(.el-table__row) {
  height: 45px;
}
.change_post {
  .post_title {
    display: inline-block;
    font-size: 14px;
    margin-right: 16px;
    line-height: 30px;
  }
  .post_list_wrap {
    .post_list {
      cursor: pointer;
      margin-right: 16px;
      line-height: 30px;
      &.active {
        color: #ffc000;
        color: #0099ff;
      }
    }
  }
}

.work_activities_content {
  margin-bottom: 16px;
}

.collaboration_post_content {
  margin-bottom: 16px;
  .select_post {
    width: 80px;
    height: 80px;
    padding: 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    background: #5cb87a;
    margin-right: 5px;
  }
  .post_item_wrap {
    color: #525e6c;
    margin: 20px 0;
    .post_item_main {
      flex: 1;
      margin-left: 10px;
      display: flex;
      flex-wrap: wrap;
      .post_item {
        width: 18%;
        height: 35px;
        display: flex;
        padding: 0 10px;
        justify-content: space-between;
        line-height: 35px;
        margin: 0 5px 8px 5px;
        border: 1px solid #e4e7ed;
        .name_icon {
          width: 20px;
          height: 20px;
          background: #d2d2d2;
          border-radius: 50%;
          margin-top: 6px;
          text-align: center;
          line-height: 22px;
          font-size: 16px;
          color: #fff;
          cursor: pointer;
          &:hover {
            opacity: 0.6;
          }
        }
      }
      .work_add_btn {
        width: 18%;
        height: 35px;
        margin: 0 5px;
        button {
          width: 100%;
          height: 100%;
          border: 1px dashed #0099ff;
          line-height: 35px;
          padding: 0;
        }
      }
    }

    .xt_wrap {
      .icon {
        width: 85px;
        height: 85px;
        background: url('../../../../../../public/icons/icon_xt.png') no-repeat center;
      }
    }
  }
}
.collaborative_role {
  margin: 20px 0;
  .icon {
    width: 85px;
    height: 35px;
    background: url('../../../../../../public/icons/icon_xt2.png') no-repeat center;
  }
  .el-checkbox-group {
    flex: 1;
    margin-left: 10px;
    label {
      width: 18%;
      height: 35px;
      border: 1px solid #e5e5e5;
      line-height: 35px;
      font-size: 12px;
      margin: 0 5px;
      &.is-checked {
        border-color: #449cff;
      }
      :deep(.el-checkbox__input) {
        float: right;
        width: 30px;
        height: 30px;
        margin: 2px 2px;
        &.is-checked {
          .el-checkbox__inner {
            background: none;
          }
          & + .el-checkbox__label {
            color: #449cff;
          }
        }
        .el-checkbox__inner {
          width: 100%;
          height: 100%;
          border: none;
          &::after {
            top: 4px;
            left: 12px;
            width: 10px;
            height: 16px;
            font-size: 30px;
            border-width: 2px;
            border-color: #449cff;
          }
        }
        .el-checkbox__original {
          width: 33px;
          height: 33px;
        }
      }
    }
  }
  .select_post {
    width: 80px;
    padding: 5px 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    background: #66b1ff;
    margin-right: 5px;
  }
}

.dialog_center {
  padding-bottom: 16px;

  .org_tree {
    float: left;
    margin-right: 16px;
    border: 1px solid #e5e5e5;
    .tree_list {
      padding: 16px;
      height: 300px;
      overflow-y: auto;
    }
  }

  .post_list_wrap {
    overflow-x: hidden;
    overflow-y: auto;
    border: 1px solid #e5e5e5;

    .post_list {
      color: #0099fd;
      margin-bottom: 6px;

      .delete_icon {
        cursor: pointer;
      }

      padding-right: 16px;
    }
  }

  .dialog_title {
    color: #212121;
    font-size: 16px;
    padding: 5px 10px;
    background: #ebf4ff;
    border-bottom: 1px solid #e5e5e5;
  }
}

:deep(.el-dialog__header) {
  padding: 11px 20px;
  background-color: #ebf4ff;
  .el-dialog__headerbtn {
    top: 14px;
    font-size: 20px;
  }
}

// 去除input number类型 加减箭头
:deep(input::-webkit-outer-spin-button),
:deep(input::-webkit-inner-spin-button) {
  -webkit-appearance: none;
}

:deep(input[type='number']) {
  -moz-appearance: textfield;
}

:deep(.select_style .el-input.is-disabled .el-input__inner) {
  background: transparent;
  color: #606266;
  border: none;
}

// 去除select下拉箭头
:deep(.select_style .el-input__suffix) {
  display: none;
}
</style>
