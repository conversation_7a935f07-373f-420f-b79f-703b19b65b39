<template>
    <div class="staff_relevancy_wrap bg_write">
        <div class="staff_relevancy_main page_section">
            <el-table class="table_wrap" :data="tableData.data" border @cell-click="cellClick">
                <el-table-column
                    v-for="(item,index) in tableData.tableTitle"
                    :label="item.label"
                    align="center"
                >
                    <el-table-column
                        v-for="(item1,index1) in item.childrenLabels"
                        v-if="!item1.canCheck"
                        :label="item1.label"
                        :prop="item1.prop"
                        :width="item1.width"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        v-for="(item1,index) in item.childrenLabels"
                        v-if="item1.canCheck"
                        :label="item1.label"
                        :prop="item1.userId"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <span :class="{ 'el-icon-check':scope.row.checkList[index].checked,'check_box_wrap':true}"></span>
                        </template>
                    </el-table-column>

                </el-table-column>
            </el-table>
        </div>
        <div class="align_center marginT_20">
            <el-button class="page_confirm_btn" type="primary" @click="prev()">上一步</el-button>
            <el-button class="page_confirm_btn" type="primary" @click="affirm()">确认</el-button>
        </div>
    </div>
</template>

<script>
    import {gangnengModelin} from "../../../request/api"
    export default {
        name: "modelMateInfoRelation",
        components: {},
        props:["modelId"],
        data() {
            return {
                tableData: {
                    props:["index","postName"],
                    tableTitle: [
                        {
                            label: '',
                            childrenLabels: [
                                {
                                    label: "序号",
                                    prop: "index",
                                    canCheck: false,
                                    width:50
                                },
                                {
                                    label: "岗位",
                                    prop: "postName",
                                    canCheck: false,

                                },
                            ]
                        },
                        {
                            label: '建模人员',
                            childrenLabels: []
                        },
                    ],
                    data: []
                },
                postList: JSON.parse(window.sessionStorage.checkedPostList),
                personList: JSON.parse(window.sessionStorage.checkedPersonList),
                maps:[],
            };
        },
        created() {
            this.tableData.data=this.postList.map((item,index)=>{
                return {
                    index:index+1,
                    postName:item.postName,
                    postCode:item.postCode,
                    checkList:this.personList.map(item=>{
                        return {
                            userName:item.userName,
                            userId:item.userId,
                            checked:false,
                        }
                    })
                }
            })
            this.tableData.tableTitle[1].childrenLabels=this.personList.map((item,index)=>{
              return {
                  label:item.userName,
                  userId:item.userId+"",
                  canCheck:true,
              }
            })
        },
        methods: {
            cellClick(row, column, cell, event) {
                if(this.tableData.props.indexOf(column.property) > -1){
                    return;
                }
                let userId=column.property;
                let rowData = this.tableData.data[row.index-1];
                rowData.checkList.some(item=>{
                    if(item.userId == userId){
                        item.checked = !item.checked
                    }
                })
            },
            prev() {
                this.$emit("prevStep")
            },
            affirm() {
                this.gangnengModelinFun();
            },
            gangnengModelinFun(){
                let evalPostList = this.postList.map(item=>{
                    return item.postCode;
                })
                let evaluateUserList = this.personList.map(item=>{
                    return item.userId;
                })
                let maps=this.tableData.data.map(item=>{
                    let userIds=[];
                    item.checkList.forEach(obj=>{
                        if(obj.checked){
                            userIds.push(obj.userId)
                        }
                    })
                    return {
                        postCode:item.postCode,
                        userIds:userIds.toString()
                    }
                })
                let flag=true;
                maps.some(item=>{
                    if(!item.userIds){
                        this.$msg.warning("有岗位未设置建模人员！");
                        flag=false
                        return;
                    }
                })
                if(!flag){
                    return;
                }
                let params={
                    modelId:this.modelId,
                    evalPostList,
                    evaluateUserList,
                    maps
                };
                console.log(params);
                gangnengModelin(params).then(res=>{
                    if(res.code ==200){
                        this.$msg.success(res.msg);
                        window.sessionStorage.removeItem("checkedJobList");
                        window.sessionStorage.removeItem("checkedPostList");
                        window.sessionStorage.removeItem("checkedPersonList");
                        this.$util.goback()
                    }else{
                        this.$msg.warning(res.msg)
                    }
                })
            }
        }
    };
</script>

<style scoped lang="scss">
    .staff_relevancy_wrap {
        .staff_relevancy_main {

             .table_wrap tbody td {
                padding: 0 !important;
            }

             .table_wrap tbody .cell {
                padding: 0;
                height: 100%;
                cursor: pointer;
            }

             .table_wrap .check_box_wrap {
                color: #0099fd;
                font-weight: 700;
                font-size: 24px;
            }

        }
    }

</style>