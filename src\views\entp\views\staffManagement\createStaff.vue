<template>
  <div class="create_staff_wrap">
    <div class="page_main_title">
      员工管理
      <div class="goback_geader" @click="goBack"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_second_title">{{ staffId ? '修改员工' : '新增员工' }}</div>
    <div class="page_section create_staff_center clearfix">
      <CSbasicInfo :type="staffId ? 'edit' : 'create'" :staffId="staffId" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import CSbasicInfo from './createStaffComps/CSbasicInfo.vue'

const props = defineProps({
  staffId: {
    type: String,
    default: ''
  }
})

const router = useRouter()

function goBack() {
  router.back()
}
</script>

<style scoped lang="scss">
.create_staff_wrap {
  .page_main_title {
    position: relative;
    .goback_geader {
      position: absolute;
      left: 0;
      top: 0;
      cursor: pointer;
      i {
        margin-right: 5px;
      }
    }
  }
}
</style>
