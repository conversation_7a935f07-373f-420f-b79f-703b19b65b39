<template>
  <div class="evaluation_item_list_wrap">
    <div class="page_main_title">评估项目管理</div>
    <div class="oper_btn marginR_16 marginT_20 align_right">
      <el-button
        class="page_add_btn"
        type="primary"
        size="mini"
        v-if="showBtn"
        v-link="
          '/talentAssessment/talentAssessmentManagement/InitiatesProjects'
        "
        >发起项目</el-button
      >
    </div>
    <div class="page_section">
      <div class="evaluation_item_list_center clearfix">
        <div
          class="evaluation_item_wrap"
          v-for="(item, index) in reviewListData.data"
          :key="item.id"
        >
          <p class="classify">{{ item.evalTypeName }}</p>
          <div class="index">{{ index + 1 }}</div>
          <div class="evaluation_item_info">
            <div class="evaluation_item_name flex_row_between">
              <div class="name overflow_elps" :title="item.reviewName">
                {{ item.evalName }}
              </div>
            </div>
            <div class="evaluation_item_date" v-if="item.beginTime">
              {{ item.beginTime.split(" ")[0] }} -
              {{ item.endTime.split(" ")[0] }}
            </div>
            <div class="evaluation_item_admin_info flex_row_between">
              <div class="item_admin_info_left flex_row_between">
                <!--                                <div class="company_name overflow_elps">{{item.companyName}}</div>-->
                <div class="company_admin">
                  {{ item.userName }}
                </div>
                <div>{{ item.phoneNumber }}</div>
              </div>
              <div class="flex_row_between">
                <div class="num">{{ item.submitCount }}</div>
                <div class>提交</div>
                <div class="num">/</div>
                <div class="num">{{ item.sumCount }}</div>
                <div class>总数</div>
              </div>
            </div>
          </div>
          <div class="evaluation_item_progress_wrap">
            <simple-step-component
              :stepsData="stepsData"
              :completedStep="getEvalStep(item.evalStatus)"
            ></simple-step-component>
            <div class="steps_item_children flex_row_between">
              <div class="steps_item_children_item clearfix">
                <div
                  class="item fl"
                  :class="{ event_none: !list.enable }"
                  v-for="list in item.phaseOneActionList"
                  :key="list.actionCode"
                  @click="
                    actionClick(
                      list.enable,
                      list.actionCode,
                      list.actionUrl,
                      list.paramKey,
                      item[list.paramKey]
                    )
                  "
                >
                  {{ list.actionName }}
                </div>
                <!-- <div class="item fl">设置职层</div>
                                <div class="item fl">组织层级</div>
                                <div class="item fl">部门信息</div>
                                <div class="item fl">岗位信息</div>
                                <div class="item fl">人员信息</div>
                                <div class="item fl">准备完成</div>-->
              </div>
              <div class="steps_item_children_item clearfix">
                <div
                  class="item fl"
                  :class="{ event_none: !list.enable }"
                  v-for="list in item.phaseTwoActionList"
                  :key="list.actionCode"
                  @click="
                    actionClick(
                      list.enable,
                      list.actionCode,
                      list.actionUrl,
                      list.paramKey,
                      item[list.paramKey]
                    )
                  "
                >
                  {{ list.actionName }}
                </div>
                <!-- <div class="item fl">
                                    <router-link
                                        :to="{path:'/talentAssessment/talentAssessmentManagement/InitiatesProjects',query:{evalId:item.evalId}}"
                                    >测评设置</router-link>
                                </div>
                                <div class="item fl">
                                    <router-link :to="{path:'/talentAssessment/talentAssessmentManagement/startPrepare',query:{evalId:item.evalId}}">
                                        开始测评
                                    </router-link>
                                </div>
                                <div class="item fl">答卷完成</div>
                                <div class="item fl">
                                    <router-link :to="{path:'/talentAssessment/talentAssessmentManagement/commentLower',query:{evalId:item.evalId}}">
                                        点评下级
                                    </router-link>
                                </div>
                                <div class="item fl">作废项目</div> -->
              </div>
              <div class="steps_item_children_item clearfix">
                <div
                  class="item fl"
                  :class="{ event_none: !list.enable }"
                  v-for="list in item.phaseThreeActionList"
                  :key="list.actionCode"
                  @click="
                    actionClick(
                      list.enable,
                      list.actionCode,
                      list.actionUrl,
                      list.paramKey,
                      item[list.paramKey]
                    )
                  "
                >
                  {{ list.actionName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="pagination_wrap">
        <el-pagination
          :page-sizes="[5, 10, 20, 50]"
          @size-change="handleSizeChange"
          :current-page="reviewListData.page.current"
          :page-size="reviewListData.page.size"
          @current-change="handleCurrentChange"
          layout="total, sizes, prev, pager, next, jumper"
          :total="reviewListData.page.total"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getEvalList, setActionType, getAction } from "../../request/api";
import simpleStepComponent from "@/components/talent/stepsComps/simpleStepComponent";

export default {
  name: "talentReview",
  components: {
    simpleStepComponent,
  },
  data() {
    return {
      showBtn: false,
      completedStep: 1,
      actionClickFlag: true,
      stepsData: [
        {
          name: "测评准备",
        },
        {
          name: "参与测评",
        },
        {
          name: "查看报告",
        },
      ],
      reviewListData: {
        data: [],
        page: {
          current: 1,
          size: 5,
          total: 0,
        },
      },
    };
  },
  created() {
    this.getEvalListFun();
    this.getActionFun();
  },
  mounted() {},
  methods: {
    isAsyncAction(type) {
      const typeKey = type.split("")[type.length - 1];
      const apiArr = ["C", "U", "R", "D"];
      return apiArr.includes(typeKey);
    },
    getEvalListFun() {
      getEvalList({
        current: this.reviewListData.page.current,
        size: this.reviewListData.page.size,
      }).then((res) => {
        console.log(res);
        if (res.code == 200 || res.code == 0) {
          this.actionClickFlag = true;
          this.reviewListData.data = res.data;
          // this.reviewListData.page = res.page
          this.reviewListData.page.total = res.total;
        }
      });
    },
    actionClick(
      actionEnable,
      actionType,
      actionUrl,
      actionParamKey,
      actionParam
    ) {
      if (!actionEnable) {
        return;
      }
      let data = {};
      data[actionParamKey] = actionParam;
      if (this.isAsyncAction(actionType)) {
        if (this.actionClickFlag) {
          this.actionClickFlag = false;
          setActionType({ url: actionUrl, data: data }).then((res) => {
            console.log(res);
            if (res.code == "200") {
              ElMessage.success(res.msg);
              this.getEvalListFun();
            } else {
              ElMessage.warning(res.msg);
            }
          });
        }
      } else {
        this.$router.push(actionUrl + actionParamKey + "=" + actionParam);
      }
    },
    getEvalStep(evalStatus) {
      let enqStep = 1;
      if (evalStatus == "X") {
        // enqStep = 0;
        return 0;
      }
      if (0 <= evalStatus && evalStatus <= 1) {
        return 1;
      } else if (evalStatus == 9) {
        return 3;
      } else {
        return 2;
      }
    },
    // 获取发起测评项目按钮状态
    getActionFun() {
      getAction({ actionCode: "D0201.01H" }).then((res) => {
        console.log(res);
        if (res.code == "200" && res.data) {
          this.showBtn = true;
        }
      });
    },
    //pageSize 改变时会触发
    handleSizeChange(size) {
      console.log(size);
      this.reviewListData.page.current = 1;
      this.reviewListData.page.size = size;
      this.getEvalListFun();
    },

    //currpage 改变时会触发
    handleCurrentChange(page) {
      this.reviewListData.page.current = page;
      this.getEvalListFun();
    },
  },
};
</script>

<style scoped lang="scss">
.evaluation_item_list_wrap {
  .evaluation_item_list_center {
    .evaluation_item_wrap {
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      border: 1px solid #e5e5e5;
      line-height: 20px;
      margin-bottom: 20px;
      position: relative;

      .classify {
        position: absolute;
        top: -2px;
        left: -5px;
        padding: 5px;
        background: #0099ff;
        color: #fff;
        font-size: 14px;
        transform: scale(0.9);
      }

      .index {
        font-size: 20px;
        font-weight: bold;
        width: 80px;
        color: #409eff;
        text-align: center;
      }

      .evaluation_item_info {
        width: 35%;
        padding-right: 15px;

        .num {
          font-size: 16px;
          color: #0099ff;
          font-weight: bold;
          padding: 0 2px;
        }

        .evaluation_item_name {
          .name {
            font-weight: bold;
            cursor: pointer;
          }
        }

        .evaluation_item_date {
          margin-top: 10px;
        }

        .evaluation_item_admin_info {
          margin-top: 20px;

          .item_admin_info_left {
            div {
              margin: 0 8px 0 0;
            }
          }

          .company_name {
            width: 140px;
            cursor: pointer;
          }
        }
      }

      .evaluation_item_progress_wrap {
        width: 65%;
        margin-left: 15px;
        background: #ebf4ff;
        padding: 5px 0;

        .steps_item_children {
          align-items: flex-start;

          .steps_item_children_item {
            flex: 1;
            padding: 0 0 0 10px;
          }

          .item {
            float: left;
            font-size: 12px;
            background: #0099ff;
            border-radius: 2px;
            margin-right: 3px;
            margin-bottom: 3px;
            text-align: center;
            line-height: 18px;
            padding: 3px 10px;
            cursor: pointer;
            color: #fff;
            &.event_none {
              background-color: #c3c3c3;
            }
            a {
              color: #fff;
            }
          }
        }
      }
    }
  }
}
</style>
