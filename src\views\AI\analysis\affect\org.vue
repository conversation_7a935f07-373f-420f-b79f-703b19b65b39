<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import SimplenessTable from '@/components/table/simplenessTable.vue'

defineOptions({ name: 'org' })
const activeType = ref('整体')
const typeList = ref(['整体', '详情'])
const changeType = item => {
  activeType.value = item
}
const orgTable = ref(null)
const overviewColumns = ref([
  { prop: 'core_metric_score', label: '核心指标影响', width: 70 },
  { prop: 'process_execution_score', label: '流程执行影响', width: 70 },
  { prop: 'decision_efficiency_score', label: '决策效率影响', width: 70 },
  { prop: 'regulatory_constraint_score', label: '制度约束影响', width: 70 },
  { prop: 'data_quality_score', label: '数据质量影响', width: 70 },
  { prop: 'system_support_score', label: '系统支撑影响', width: 70 },
  { prop: 'role_performance_score', label: '岗位履职影响', width: 70 },
  { prop: 'collaboration_efficiency_score', label: '协同效率影响', width: 70 },
  { prop: 'personnel_efficiency_score', label: '人员效能影响', width: 70 },
  { prop: 'goal_driven_score', label: '目标驱动影响', width: 70 }
])
const overviewData = ref([
  {
    id: '1',
    organization: '采购部',
    core_metric_score: '49',
    process_execution_score: '70',
    decision_efficiency_score: '67',
    regulatory_constraint_score: '58',
    data_quality_score: '58',
    system_support_score: '65',
    role_performance_score: '46',
    collaboration_efficiency_score: '65',
    personnel_efficiency_score: '45',
    goal_driven_score: '49'
  },
  {
    id: '2',
    organization: '重庆冰箱工厂',
    core_metric_score: '63',
    process_execution_score: '67',
    decision_efficiency_score: '47',
    regulatory_constraint_score: '50',
    data_quality_score: '68',
    system_support_score: '58',
    role_performance_score: '61',
    collaboration_efficiency_score: '49',
    personnel_efficiency_score: '56',
    goal_driven_score: '65'
  },
  {
    id: '3',
    organization: '电子商务部',
    core_metric_score: '66',
    process_execution_score: '48',
    decision_efficiency_score: '69',
    regulatory_constraint_score: '61',
    data_quality_score: '69',
    system_support_score: '64',
    role_performance_score: '61',
    collaboration_efficiency_score: '61',
    personnel_efficiency_score: '66',
    goal_driven_score: '54'
  },
  {
    id: '4',
    organization: '欧盟区产品部',
    core_metric_score: '50',
    process_execution_score: '45',
    decision_efficiency_score: '63',
    regulatory_constraint_score: '53',
    data_quality_score: '65',
    system_support_score: '52',
    role_performance_score: '65',
    collaboration_efficiency_score: '49',
    personnel_efficiency_score: '58',
    goal_driven_score: '47'
  },
  {
    id: '5',
    organization: '工艺部',
    core_metric_score: '52',
    process_execution_score: '59',
    decision_efficiency_score: '60',
    regulatory_constraint_score: '67',
    data_quality_score: '70',
    system_support_score: '46',
    role_performance_score: '53',
    collaboration_efficiency_score: '67',
    personnel_efficiency_score: '64',
    goal_driven_score: '62'
  },
  {
    id: '6',
    organization: '供应链计划管理部',
    core_metric_score: '60',
    process_execution_score: '58',
    decision_efficiency_score: '48',
    regulatory_constraint_score: '63',
    data_quality_score: '57',
    system_support_score: '52',
    role_performance_score: '68',
    collaboration_efficiency_score: '67',
    personnel_efficiency_score: '50',
    goal_driven_score: '68'
  },
  {
    id: '7',
    organization: 'GTM部',
    core_metric_score: '67',
    process_execution_score: '67',
    decision_efficiency_score: '52',
    regulatory_constraint_score: '47',
    data_quality_score: '54',
    system_support_score: '57',
    role_performance_score: '54',
    collaboration_efficiency_score: '58',
    personnel_efficiency_score: '61',
    goal_driven_score: '69'
  },
  {
    id: '8',
    organization: '结构研发部',
    core_metric_score: '70',
    process_execution_score: '52',
    decision_efficiency_score: '68',
    regulatory_constraint_score: '53',
    data_quality_score: '54',
    system_support_score: '68',
    role_performance_score: '61',
    collaboration_efficiency_score: '45',
    personnel_efficiency_score: '53',
    goal_driven_score: '65'
  },
  {
    id: '9',
    organization: '经营管理部',
    core_metric_score: '66',
    process_execution_score: '45',
    decision_efficiency_score: '49',
    regulatory_constraint_score: '57',
    data_quality_score: '57',
    system_support_score: '48',
    role_performance_score: '64',
    collaboration_efficiency_score: '58',
    personnel_efficiency_score: '61',
    goal_driven_score: '58'
  },
  {
    id: '10',
    organization: '冷柜研发部',
    core_metric_score: '54',
    process_execution_score: '56',
    decision_efficiency_score: '65',
    regulatory_constraint_score: '67',
    data_quality_score: '49',
    system_support_score: '52',
    role_performance_score: '66',
    collaboration_efficiency_score: '57',
    personnel_efficiency_score: '69',
    goal_driven_score: '62'
  },
  {
    id: '11',
    organization: '零售与用户运营部',
    core_metric_score: '67',
    process_execution_score: '66',
    decision_efficiency_score: '54',
    regulatory_constraint_score: '50',
    data_quality_score: '62',
    system_support_score: '46',
    role_performance_score: '50',
    collaboration_efficiency_score: '47',
    personnel_efficiency_score: '50',
    goal_driven_score: '57'
  },
  {
    id: '12',
    organization: '品牌与产品营销部',
    core_metric_score: '极简',
    process_execution_score: '58',
    decision_efficiency_score: '47',
    regulatory_constraint_score: '46',
    data_quality_score: '69',
    system_support_score: '64',
    role_performance_score: '64',
    collaboration_efficiency_score: '64',
    personnel_efficiency_score: '62',
    goal_driven_score: '46'
  },
  {
    id: '13',
    organization: '威海冰冷工厂',
    core_metric_score: '58',
    process_execution_score: '64',
    decision_efficiency_score: '极简',
    regulatory_constraint_score: '61',
    data_quality_score: '68',
    system_support_score: '51',
    role_performance_score: '64',
    collaboration_efficiency_score: '50',
    personnel_efficiency_score: '51',
    goal_driven_score: '70'
  },
  {
    id: '14',
    organization: '渠道运营部',
    core_metric_score: '51',
    process_execution_score: '45',
    decision_efficiency_score: '69',
    regulatory_constraint_score: '58',
    data_quality_score: '61',
    system_support_score: '58',
    role_performance_score: '46',
    collaboration_efficiency_score: '56',
    personnel_efficiency_score: '67',
    goal_driven_score: '67'
  },
  {
    id: '15',
    organization: '全球产品经理部',
    core_metric_score: '49',
    process_execution_score: '69',
    decision_efficiency_score: '56',
    regulatory_constraint_score: '56',
    data_quality_score: '69',
    system_support_score: '62',
    role_performance_score: '45',
    collaboration_efficiency_score: '57',
    personnel_efficiency_score: '65',
    goal_driven_score: '68'
  },
  {
    id: '16',
    organization: '格米冰冷GTM部',
    core_metric_score: '53',
    process_execution_score: '45',
    decision_efficiency_score: '66',
    regulatory_constraint_score: '65',
    data_quality_score: '50',
    system_support_score: '57',
    role_performance_score: '46',
    collaboration_efficiency_score: '57',
    personnel_efficiency_score: '49',
    goal_driven_score: '47'
  },
  {
    id: '17',
    organization: '东莞冰冷工厂',
    core_metric_score: '49',
    process_execution_score: '61',
    decision_efficiency_score: '54',
    regulatory_constraint_score: '52',
    data_quality_score: '62',
    system_support_score: '46',
    role_performance_score: '45',
    collaboration_efficiency_score: '60',
    personnel_efficiency_score: '66',
    goal_driven_score: '70'
  },
  {
    id: '18',
    organization: '东莞研发部',
    core_metric_score: '60',
    process_execution_score: '46',
    decision_efficiency_score: '57',
    regulatory_constraint_score: '51',
    data_quality_score: '70',
    system_support_score: '46',
    role_performance_score: '68',
    collaboration_efficiency_score: '57',
    personnel_efficiency_score: '56',
    goal_driven_score: '56'
  },
  {
    id: '19',
    organization: '苏州冰箱工厂',
    core_metric_score: '59',
    process_execution_score: '56',
    decision_efficiency_score: '50',
    regulatory_constraint_score: '67',
    data_quality_score: '67',
    system_support_score: '59',
    role_performance_score: '58',
    collaboration_efficiency_score: '56',
    personnel_efficiency_score: '66',
    goal_driven_score: '65'
  },
  {
    id: '20',
    organization: '营销管理部',
    core_metric_score: '46',
    process_execution_score: '53',
    decision_efficiency_score: '56',
    regulatory_constraint_score: '65',
    data_quality_score: '68',
    system_support_score: '67',
    role_performance_score: '47',
    collaboration_efficiency_score: '59',
    personnel_efficiency_score: '54',
    goal_driven_score: '52'
  },
  {
    id: '21',
    organization: '用户服务部',
    core_metric_score: '48',
    process_execution_score: '67',
    decision_efficiency_score: '65',
    regulatory_constraint_score: '49',
    data_quality_score: '47',
    system_support_score: '53',
    role_performance_score: '46',
    collaboration_efficiency_score: '58',
    personnel_efficiency_score: '58',
    goal_driven_score: '64'
  },
  {
    id: '22',
    organization: '战略与变革管理部',
    core_metric_score: '52',
    process_execution_score: '52',
    decision_efficiency_score: '51',
    regulatory_constraint_score: '66',
    data_quality_score: '70',
    system_support_score: '53',
    role_performance_score: '47',
    collaboration_efficiency_score: '54',
    personnel_efficiency_score: '51',
    goal_driven_score: '45'
  },
  {
    id: '23',
    organization: '制造中心领导',
    core_metric_score: '62',
    process_execution_score: '46',
    decision_efficiency_score: '63',
    regulatory_constraint_score: '53',
    data_quality_score: '57',
    system_support_score: '56',
    role_performance_score: '54',
    collaboration_efficiency_score: '51',
    personnel_efficiency_score: '49',
    goal_driven_score: '69'
  },
  {
    id: '24',
    organization: '质量部',
    core_metric_score: '62',
    process_execution_score: '60',
    decision_efficiency_score: '68',
    regulatory_constraint_score: '59',
    data_quality_score: '57',
    system_support_score: '51',
    role_performance_score: '49',
    collaboration_efficiency_score: '52',
    personnel_efficiency_score: '53',
    goal_driven_score: '45'
  },
  {
    id: '25',
    organization: '智能制造推进部',
    core_metric_score: '66',
    process_execution_score: '48',
    decision_efficiency_score: '45',
    regulatory_constraint_score: '48',
    data_quality_score: '54',
    system_support_score: '62',
    role_performance_score: '47',
    collaboration_efficiency_score: '64',
    personnel_efficiency_score: '49',
    goal_driven_score: '61'
  }
])

onMounted(() => {
  orgTable.value.setCurrentRow(overviewData.value[5])
})
const setColor = value => {
  if (value <= 40) {
    return '#83ECF7'
  } else if (value <= 50) {
    return '#A0F0F6'
  } else {
    return '#6ACEFC'
  }
}

const taskCol = ref([
  {
    label: '序号',
    type: 'index'
  },
  {
    label: '组织名称',
    prop: 'orgName',
    width: '90px'
  },
  {
    label: '举措',
    prop: 'measure',
    width: '110px'
  },
  {
    label: '关键行动',
    prop: 'keyAction'
  },
  {
    label: '建议责任人',
    prop: 'suggestedOwner',
    width: '110px'
  },
  {
    label: '输出成果',
    prop: 'output',
    width: '150px'
  },
  {
    label: '优先级',
    prop: 'priority',
    width: '60px'
  }
])
const taskData = ref([
  {
    id: '1',
    orgName: '供应链计划管理部',
    measure: '构建需求计划偏差分析机制',
    keyAction:
      '①开发《需求计划偏差分析模板》，包含数据偏差率、影响范围、责任环节诊断；②建立月度偏差复盘会议机制，重大偏差（超 20%）触发专项整改；③将偏差分析结果纳入需求计划模型优化输入参数。',
    suggestedOwner: '李娜（营销流程专员）',
    output: '《偏差分析报告模板》',
    priority: '中'
  },
  {
    id: '2',
    orgName: '供应链计划管理部',
    measure: '建立岗位专业能力培训体系',
    keyAction:
      '①开发《需求计划核心方法论课程包》，包含预测模型应用、数据驱动决策等模块；②每季度组织外部专家培训（如邀请 APICS 认证讲师授课）；③实施 “轮岗 + 项目制” 培养，安排分析师参与重大促销需求计划实战。',
    suggestedOwner: '郑浩（IT 开发经理）',
    output: '《培训课程体系及实施计划》',
    priority: '中'
  },
  {
    id: '3',
    orgName: '供应链计划管理部',
    measure: '开发需求决策模拟沙盘工具',
    keyAction:
      '①构建需求计划决策模拟场景（如旺季需求激增、供应链中断等）；②设置不同决策方案的指标模拟测算（如库存周转率、交付准时率变化）；③提供决策方案对比分析，辅助管理层选择最优策略。',
    suggestedOwner: '陈宇（人力资源经理）',
    output: '《模拟沙盘使用指南》',
    priority: '低'
  },
  {
    id: '4',
    orgName: '供应链计划管理部',
    measure: '实施流程节点可视化监控',
    keyAction:
      '①在项目管理系统中搭建需求计划流程看板，实时显示各环节进度（收集→分析→评审→发布）；②设置节点超时预警（黄色预警：超时 12 小时，红色预警：超时 24 小时）；③生成流程效率分析报告，统计各环节平均耗时及瓶颈点。',
    suggestedOwner: '赵阳（数据中台工程师）',
    output: '《流程监控看板使用说明》',
    priority: '低'
  },
  {
    id: '5',
    orgName: '供应链计划管理部',
    measure: '完善目标达成激励机制',
    keyAction:
      '①设立专项奖金（年度目标达成奖、季度创新奖）；②对超额完成核心指标的团队 / 个人给予额外奖励（如准确率超 95% 时奖励团队 10 万元）；③将目标达成情况纳入员工晋升评估体系（连续两年达标者优先晋升）。',
    suggestedOwner: '周明（营销主管）',
    output: '《激励机制方案及实施流程》',
    priority: '低'
  },
  {
    id: '6',
    orgName: '供应链计划管理部',
    measure: '建立 “师徒制” 人才培养机制',
    keyAction:
      '①选拔资深计划员作为导师（需具备 5 年以上经验，核心指标达标率连续 3 年超 90%）；②制定师徒带教计划（3 个月掌握基础预测模型，6 个月独立完成月度计划）；③设立带教奖励（徒弟达标率每超 10%，导师获 2000 元奖金）。',
    suggestedOwner: '赵阳（数据中台工程师）',
    output: '《师徒制实施细则及台账》',
    priority: '中'
  },
  {
    id: '7',
    orgName: '供应链计划管理部',
    measure: '实施数据质量提升专项行动',
    keyAction:
      '①成立数据质量小组（含数据专员、系统工程师、业务骨干）；②制定数据清洗计划（季度清洗历史异常数据，每月校验新录入数据）；③建立数据质量责任矩阵（明确销售部、市场部、计划部数据录入及审核责任）。',
    suggestedOwner: '陈宇（人力资源经理）',
    output: '《数据质量提升行动计划》',
    priority: '高'
  },
  {
    id: '8',
    orgName: '供应链计划管理部',
    measure: '升级供应链计划管理系统',
    keyAction:
      '①新增需求计划对象管理模块，实现准入审核、数据维护、跨部门协同功能集成；②打通与 ERP、CRM、WMS 系统数据接口，实现需求数据实时同步；③优化系统用户界面，提升操作便捷性（如批量导入、智能搜索、自定义报表）。',
    suggestedOwner: '赵阳（数据中台工程师）',
    output: '《系统升级方案及操作手册》',
    priority: '高'
  },
  {
    id: '9',
    orgName: '供应链计划管理部',
    measure: '开展跨部门协作能力提升工作坊',
    keyAction:
      '①设计《跨部门协作实战课程》，包含沟通技巧、冲突解决、目标对齐等内容；②组织部门结对共建活动（如计划部与销售部组队解决历史需求冲突问题）；③邀请外部专家分享跨部门协作成功案例（如标杆企业需求协同机制解析）。',
    suggestedOwner: '赵阳（数据中台工程师）',
    output: '《工作坊方案及案例集》',
    priority: '中'
  },
  {
    id: '10',
    orgName: '供应链计划管理部',
    measure: '开展制度解读与培训',
    keyAction:
      '①组织制度发布会（邀请各部门负责人及核心员工参与，详细解读制度条款）；②开发制度培训微课程（每个流程节点制作 3-5 分钟动画视频，便于理解）；③设置制度考核（新员工入职必须通过制度考试，老员工每年复考一次）。',
    suggestedOwner: '王强（系统实施工程师）',
    output: '《制度培训课程及考核题》',
    priority: '低'
  }
])

const affectCol = ref([
  {
    type: 'index',
    label: '序号'
  },
  {
    label: '组织名称',
    prop: 'orgName',
    width: 90
  },
  {
    label: '短板类型',
    prop: 'weaknessType',
    width: 90
  },
  {
    label: '得分',
    prop: 'score',
    width: 50
  },
  {
    label: '主要管理影响',
    prop: 'mainMgmtImpact'
  },
  {
    label: '主要指标影响',
    prop: 'mainKpiImpact'
  },
  {
    label: '影响程度',
    prop: 'impactLevel',
    width: 50
  },
  {
    label: '传导路径',
    prop: 'transmissionPath'
  },
  {
    label: '关键任务',
    prop: 'keyTask',
    width: 50,
    align: 'center'
  }
])

const affectData = ref([
  {
    id: '1',
    orgName: '供应链计划管理部',
    weaknessType: '核心指标影响',
    score: '60',
    mainMgmtImpact:
      '战略库存配置失衡，紧急订单响应机制失效，导致供应链成本持续攀升；需求计划与产能规划错配，引发生产资源浪费或短缺',
    mainKpiImpact: '库存周转率下降，订单交付准时率降低，供应链总成本上升',
    impactLevel: '高',
    transmissionPath:
      '需求计划偏差→安全库存设定失准→库存积压 / 短缺→周转率下降→订单交付能力受损→紧急采购 / 调拨成本增加→总成本上升',
    keyTask: '3'
  },
  {
    id: '2',
    orgName: '供应链计划管理部',
    weaknessType: '流程执行影响',
    score: '58',
    mainMgmtImpact:
      '需求计划流程节点混乱，跨业务单元执行标准不统一，导致月度计划整合耗时增加；需求收集、分析、评审环节无标准化模板，引发多版本冲突与反复返工',
    mainKpiImpact: '流程各环节操作差异大，计划版本混乱，流程返工率升高，计划时效性滞后',
    impactLevel: '极高',
    transmissionPath:
      '无明确执行规范→各环节操作无统一指引→执行差异大→计划版本混乱→流程节点衔接失效→返工率升高→计划发布延期',
    keyTask: '3'
  },
  {
    id: '3',
    orgName: '供应链计划管理部',
    weaknessType: '决策效率影响',
    score: '48',
    mainMgmtImpact:
      '市场趋势响应滞后，产能与需求错配，导致旺季断货率上升和淡季库存积压；管理层依赖经验决策，重大促销活动需求响应延迟，错失黄金销售窗口',
    mainKpiImpact: '新品上市首单缺货率上升，滞销库存占比增加，首触需求响应时效延长，竞品截单率上升，战略客户满意度下降',
    impactLevel: '极高',
    transmissionPath:
      '市场 / 产能数据缺失→管理层经验决策→需求计划偏离实际→生产排程与采购计划错配→旺季产能不足 / 淡季库存积压→客户流失 / 成本增加',
    keyTask: '3'
  },
  {
    id: '4',
    orgName: '供应链计划管理部',
    weaknessType: '制度约束影响',
    score: '63',
    mainMgmtImpact:
      '需求计划变更无管控，历史版本不可追溯，导致审计风险积累和部门间责任推诿；制度条款模糊，执行标准不统一，引发跨部门协作争议与流程执行随意性',
    mainKpiImpact: '计划变更无记录导致库存异常损失，合规审计通过率下降，部门间责任纠纷案件增加，制度执行偏差率上升',
    impactLevel: '高',
    transmissionPath:
      '无管理制度约束→关键行为无规范→执行随意性→责任界定模糊→历史版本不可追溯→审计风险积累→部门协作信任度下降',
    keyTask: '2'
  },
  {
    id: '5',
    orgName: '供应链计划管理部',
    weaknessType: '数据质量影响',
    score: '57',
    mainMgmtImpact:
      '客户需求数据碎片化，市场趋势分析失真，导致产品分群策略失效；历史订单、促销活动等关键数据缺失 / 错误，引发需求预测模型失效与资源配置错配',
    mainKpiImpact:
      '需求预测误差率上升，采购计划错配率增加，原材料呆滞库存金额上升，产品分群准确率下降，定制化供应链策略失效',
    impactLevel: '中',
    transmissionPath:
      '关键数据缺失 / 失真→需求分析维度不全→预测模型失效→产品分群失准→采购 / 生产计划错配→库存冗余或短缺→供应链效率下降',
    keyTask: '2'
  },
  {
    id: '6',
    orgName: '供应链计划管理部',
    weaknessType: '系统支撑影响',
    score: '52',
    mainMgmtImpact:
      '多系统数据孤岛，人工跨系统操作导致数据一致性差，流程自动化水平受限；需求计划系统与 ERP/WMS 接口断层，引发生产排程与库存数据脱节',
    mainKpiImpact:
      '系统间数据同步延迟，人工跨系统录入错误率上升，月度系统对账耗时增加，数据不一致导致生产排程失误，流程自动化覆盖率低',
    impactLevel: '极高',
    transmissionPath:
      '系统功能不支撑→人工跨系统操作→数据不一致→操作断层→生产 / 采购计划偏差→库存与产能错配→交付延迟 / 成本增加',
    keyTask: '3'
  },
  {
    id: '7',
    orgName: '供应链计划管理部',
    weaknessType: '岗位履职影响',
    score: '68',
    mainMgmtImpact:
      '核心环节由兼职人员承担，专业深度不足导致需求计划颗粒度粗糙；职责分散化引发多岗位交叉负责，关键任务优先级冲突与输出延迟',
    mainKpiImpact: '需求计划颗粒度不足，历史数据利用率低，关键需求维度漏判率上升，计划输出延迟率增加',
    impactLevel: '中',
    transmissionPath:
      '无专职岗位→核心环节兼职处理→精力分散→专业方法论缺失→需求分析粗放→计划颗粒度不足→资源配置精度下降',
    keyTask: '3'
  },
  {
    id: '8',
    orgName: '供应链计划管理部',
    weaknessType: '协同效率影响',
    score: '67',
    mainMgmtImpact:
      '跨部门需求对接规则模糊，信息传递偏差导致需求评审效率低下；销售 / 市场 / 供应链目标冲突，引发需求数据博弈与计划反复调整',
    mainKpiImpact:
      '跨部门需求评审一次性通过率低，单次评审耗时增加，年度沟通成本上升，需求计划调整频率高，紧急需求响应周期延长',
    impactLevel: '高',
    transmissionPath: '协作接口模糊→职责边界不清→信息不对称→跨部门博弈→评审低效→计划调整频繁→供应链响应速度下降',
    keyTask: '3'
  },
  {
    id: '9',
    orgName: '供应链计划管理部',
    weaknessType: '人员效能影响',
    score: '50',
    mainMgmtImpact:
      '团队技能断层，高级分析工具应用不足导致复杂需求处理低效；新人培养体系缺失，关键岗位依赖少数骨干，引发人才断层风险',
    mainKpiImpact: '预测模型应用率低，复杂需求处理耗时增加，新员工独立完成计划率低，骨干离职导致计划准确率下降',
    impactLevel: '中',
    transmissionPath: '人员技能不匹配→高级工具应用少→复杂需求处理慢→新人成长慢→骨干依赖度高→团队效能波动',
    keyTask: '3'
  },
  {
    id: '10',
    orgName: '供应链计划管理部',
    weaknessType: '目标驱动影响',
    score: '68',
    mainMgmtImpact:
      '绩效考核指标缺失，执行动力不足导致战略需求漏判率上升；长期目标分解模糊，资源分配聚焦短期任务，忽视能力建设',
    mainKpiImpact: '战略客户需求漏判率上升，长期目标达成率低，团队精力聚焦日常任务，新技术 / 方法论引入停滞',
    impactLevel: '低',
    transmissionPath: '无绩效考核驱动→目标感模糊→执行动力不足→战略需求忽视→资源分配短视→能力建设滞后→竞争力下降',
    keyTask: '3'
  }
])
</script>
<template>
  <div class="org-detail">
    <div class="page-title-line">能力短板影响分析组织分布</div>
    <el-table ref="orgTable" highlight-current-row class="distribute" stripe :data="overviewData">
      <el-table-column label="序号" type="index" width="55" align="center"></el-table-column>
      <el-table-column label="组织" prop="organization" align="center"></el-table-column>
      <el-table-column
        v-for="col in overviewColumns"
        :key="col.prop"
        align="center"
        :label="col.label"
        :prop="col.prop"
        :width="col.width"
      >
        <template #default="{ row }">
          <div class="table-score">
            <div class="score" :style="{ background: setColor(row[col.prop]) }">{{ row[col.prop] }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="80" fixed="right">
        <template #default="{}">
          <el-button size="small" type="primary" plain>详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-title-line mt-7">能力短板影响分析(供应链计划管理部)</div>
    <SimplenessTable :columns="affectCol" :data="affectData"></SimplenessTable>
    <div class="page-title-line mt-7">
      <div class="page-title">建议改善关键任务(供应链计划管理部)</div>
    </div>
    <SimplenessTable highlight-current-row :columns="taskCol" :data="taskData"></SimplenessTable>
  </div>
</template>
<style lang="scss" scoped>
.page-title-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .choose-dna {
    font-size: 16px;
    color: #40a0ff;
  }
}

.table-score {
  display: flex;
  align-items: center;
  justify-content: center;
  .score {
    width: 65px;
    line-height: 24px;
    background: #6acefc;
    border-radius: 20px;
    color: #fff;
    font-size: 14px;
  }
}
:deep(.el-table.distribute .cell) {
  padding: 0 2px;
}
</style>
