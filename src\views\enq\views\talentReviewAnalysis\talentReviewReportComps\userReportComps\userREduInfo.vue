<template>
    <div class="report_section edu_info_wrap">
        <div class="clearfix">
            <div class="page_second_title">
                <span>教育信息</span>
            </div>
            <div class="edu_info_center marginT_16">
                <div class="edu_info_header">
                    <div class="item">毕业院校</div>
                    <div class="item">毕业日期</div>
                    <div class="item">学历</div>
                    <div class="item">与当前岗位相关</div>
                    <div class="item">与当前行业相关</div>
                </div>
                <div class="edu_info_mmain">
                    <div
                        class="edu_info_item"
                        v-for="item in eduInfoData"
                        :key="item.id"
                    >
                        <div class="item">{{ item.graduateSchool }}</div>
                        <div class="item">{{ item.graduateDate|removeTime }}</div>
                        <div class="item">
                            {{ item.qualification }}
                        </div>
                        <div class="item">
                            {{ item.postRelated }}
                        </div>
                        <div class="item">
                            {{ item.industryRelated }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { getUserEducations } from "../../../../request/api.js";
    export default {
        name: "userREduInfo",
        props: ["nextBtnText", "enqId", "userId"],
        components: {},
        data() {
            return {
                eduInfoData: [],
            };
        },
        created() {
            this.getEducationData();
        },
        filters: {
            removeTime: function (val) {
                return val ? val.split(" ")[0] : " ";
            },
        },
        methods: {
            getEducationData() {
                getUserEducations({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.eduInfoData = res.data;
                    } else {
                        this.$message.error("获取数据失败!");
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .edu_info_wrap {
        // margin-bottom: 16px;
    }
    .edu_info_header,
    .edu_info_mmain {
        .item {
            width: 23%;
            padding-left: 8px;
        }
        .item_icon_wrap {
            text-align: center;
            width: 8%;
        }
    }
</style>
