<template>
  <div class="org_chart_wrap bg_write">
    <div class="page_main_title">组织结构图</div>
    <div class="page_section">
      <div class="org_chart_center clearfix">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title" :title="treeTitle">
              {{ treeTitle }}
            </div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio :treeData="treeData" @clickCallback="clickCallback" />
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <div class="filter_bar_wrap">
            <div class="flex_row_start">
              <div class="filter_item title">筛选</div>
              <div class="filter_item">
                <el-select v-model="orgLevelCode" clearable placeholder="请选择组织层级">
                  <el-option
                    v-for="item in orgLevelOptions"
                    :key="item.dictCode"
                    :label="item.codeName"
                    :value="item.dictCode"
                  />
                </el-select>
              </div>
              <div class="filter_item">
                <el-select v-model="bizDomainCode" clearable placeholder="请选择业务领域">
                  <el-option
                    v-for="item in bizDomainOptions"
                    :key="item.dictCode"
                    :label="item.codeName"
                    :value="item.dictCode"
                  />
                </el-select>
              </div>
              <div class="filter_item">
                <el-select v-model="isEntity" clearable placeholder="请选择是否实体">
                  <el-option label="是" value="Y" />
                  <el-option label="否" value="N" />
                </el-select>
              </div>
              <div class="filter_item">
                <el-button class="page_add_btn" type="primary" @click="keyWordSearch"> 查询 </el-button>
              </div>
            </div>
          </div>
          <div class="chart_wrap" v-if="orgChartData && loadEndSign">
            <orgTree
              name="test"
              :data="orgChartData"
              :horizontal="true"
              :collapsable="false"
              :render-content="renderContent"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, h } from 'vue'
import { getOrgDeptTree, getOrgStructure, getDomainList, getOrgLevelList } from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
// import orgTree from '@/components/talent/orgTree/orgTree'
import orgTree from '@/components/talent/components/org-tree/org-tree'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)
const treeTitle = computed(() => userStore.companyInfo?.companyName || '')

// 响应式状态
const rStatus = ref('')
const orgLevelOptions = ref([])
const bizDomainOptions = ref([])
const bizDomainCode = ref('')
const layerNo = ref('')
const isEntity = ref('')
const orgCode = ref('')
const orgLevelCode = ref('')
const treeData = ref([])
const orgChartData = ref({})
const loadEndSign = ref(false)

// 组织树配置
const orgChartOption = {
  direction: 'l2r'
}

// 方法
const getOrgDeptTreeFun = async () => {
  try {
    getOrgDeptTree({
      companyId: companyId.value
    }).then(res => {
      if (res.code == 200) {
        treeData.value = res.data.length > 0 ? res.data : []
      } else {
        treeData.value = []
      }
    })
  } catch (error) {
    console.error('获取组织树失败:', error)
    treeData.value = []
  }
}

const clickCallback = (val, isLastNode, item) => {
  orgCode.value = val
  layerNo.value = val ? item.layerNo : ''
  getOrgStructureFun()
}

const getOrgLevelListFun = async () => {
  try {
    const res = await getOrgLevelList({
      companyId: companyId.value,
      rStatus: rStatus.value
    })

    if (res.data.length > 0) {
      orgLevelOptions.value = res.data.map(item => ({
        dictCode: item.orgLevelCode,
        codeName: item.orgLevelName
      }))
    } else {
      orgLevelOptions.value = []
    }
  } catch (error) {
    console.error('获取组织层级列表失败:', error)
    orgLevelOptions.value = []
  }
}

const getDomainListFun = async () => {
  try {
    const res = await getDomainList({
      companyId: companyId.value,
      rStatus: rStatus.value
    })

    if (res.code == 200) {
      bizDomainOptions.value =
        res.data.length > 0
          ? res.data.map(item => ({
              dictCode: item.bizDomainCode,
              codeName: item.bizDomainName
            }))
          : []
    } else {
      bizDomainOptions.value = []
    }
  } catch (error) {
    console.error('获取业务领域列表失败:', error)
    bizDomainOptions.value = []
  }
}

const getOrgStructureFun = async () => {
  loadEndSign.value = false

  try {
    const res = await getOrgStructure({
      companyId: companyId.value,
      bizDomainCode: bizDomainCode.value,
      layerNo: layerNo.value,
      isEntity: isEntity.value,
      orgCode: orgCode.value,
      orgLevelCode: orgLevelCode.value
    })

    if (res.code == 200) {
      orgChartData.value = res.data.length > 0 ? res.data[0] : ''
    } else {
      orgChartData.value = ''
    }

    loadEndSign.value = true
  } catch (error) {
    console.error('获取组织架构失败:', error)
    orgChartData.value = ''
    loadEndSign.value = true
  }
}

const keyWordSearch = () => {
  getOrgStructureFun()
}

const renderContent = (h, data) => {
  console.log(data)

  if (loadEndSign.value) {
    return h('div', { class: 'label_wrap' }, [
      h(
        'div',
        {
          class: 'label_title',
          title: data.orgName // 直接写 title
        },
        data.orgName
      ),
      h('p', { class: 'label_text' }, [
        h('span', null, `${data.countPost}岗位`),
        h('span', null, `${data.countPostUser}人`)
      ])
    ])
  }
}

// 监听公司ID变化
watch(
  () => companyId.value,
  val => {
    if (val) {
      getOrgDeptTreeFun()
      getDomainListFun()
      getOrgLevelListFun()
    }
  },
  { immediate: true }
)
</script>

<style lang="scss">
.org-tree-container {
  display: inline-block;
  padding: 15px;
  background-color: #fff;
}

.org-tree {
  display: table;
  text-align: center;

  &:before,
  &:after {
    content: '';
    display: table;
  }

  &:after {
    clear: both;
  }
}

.org-tree-node,
.org-tree-node-children {
  position: relative;
  margin: 0;
  padding: 0;
  list-style-type: none;

  &:before,
  &:after {
    transition: all 0.35s;
  }
}

.org-tree-node-label {
  position: relative;
  display: inline-block;

  .org-tree-node-label-inner {
    //padding: 10px 15px;
    padding: 0;
    text-align: center;
    border-radius: 3px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.15);

    .label_wrap {
      width: 150px;
      line-height: 35px;
      border: 1px solid #0099ff;
      cursor: pointer;

      .label_title {
        height: 35px;
        padding: 0 5px;
        color: #fff;
        background: #0099ff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .label_text {
        line-height: 35px;
        padding: 0 10px;
        color: #333;
        overflow: hidden;
        border-top: 1px solid #0099ff;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: flex;
        span {
          flex: 1;
        }
      }
    }
  }
}

.org-tree-node-btn {
  position: absolute;
  top: 100%;
  left: 50%;
  width: 20px;
  height: 20px;
  z-index: 10;
  margin-left: -11px;
  margin-top: 9px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 50%;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.35s ease;

  &:hover {
    background-color: #e7e8e9;
    transform: scale(1.15);
  }

  &:before,
  &:after {
    content: '';
    position: absolute;
  }

  &:before {
    top: 50%;
    left: 4px;
    right: 4px;
    height: 0;
    border-top: 1px solid #ccc;
  }

  &:after {
    top: 4px;
    left: 50%;
    bottom: 4px;
    width: 0;
    border-left: 1px solid #ccc;
  }

  &.expanded:after {
    border: none;
  }
}

.org-tree-node {
  padding-top: 20px;
  display: table-cell;
  vertical-align: top;

  &.is-leaf,
  &.collapsed {
    padding-left: 10px;
    padding-right: 10px;
  }

  &:before,
  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 19px;
  }

  &:after {
    left: 50%;
    border-left: 1px solid #91d3ff;
  }

  &:not(:first-child):before,
  &:not(:last-child):after {
    border-top: 1px solid #91d3ff;
  }
}

.collapsable .org-tree-node.collapsed {
  padding-bottom: 30px;

  .org-tree-node-label:after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    width: 50%;
    height: 20px;
    border-right: 1px solid #91d3ff;
  }
}

.org-tree > .org-tree-node {
  padding-top: 0;

  &:after {
    border-left: 0;
  }
}

.org-tree-node-children {
  padding-top: 20px;
  display: table;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 20px;
    border-left: 1px solid #91d3ff;
  }

  &:after {
    content: '';
    display: table;
    clear: both;
  }
}

.horizontal {
  .org-tree-node {
    // display: flex;
    // flex-direction: row;
    // justify-content: flex-start;
    // align-items: center;
    display: table-cell;
    float: none;
    padding-top: 0;
    padding-left: 20px;

    &.is-leaf,
    &.collapsed {
      padding-top: 10px;
      padding-bottom: 10px;
    }

    &:before,
    &:after {
      width: 19px;
      height: 50%;
    }

    &:after {
      top: 50%;
      left: 0;
      border-left: 0;
    }

    &:only-child:before {
      border-bottom: 1px solid #91d3ff;
    }

    &:not(:first-child):before,
    &:not(:last-child):after {
      border-top: 0;
      border-left: 1px solid #91d3ff;
    }

    &:not(:only-child):after {
      border-top: 1px solid #91d3ff;
    }

    .org-tree-node-inner {
      display: table;
    }
  }

  .org-tree-node-label {
    display: table-cell;
    vertical-align: middle;
  }

  &.collapsable .org-tree-node.collapsed {
    padding-right: 30px;

    .org-tree-node-label:after {
      top: 0;
      left: 100%;
      width: 20px;
      height: 50%;
      border-right: 0;
      border-bottom: 1px solid #91d3ff;
    }
  }

  .org-tree-node-btn {
    top: 50%;
    left: 100%;
    margin-top: -11px;
    margin-left: 9px;
  }

  & > .org-tree-node:only-child:before {
    border-bottom: 0;
  }

  .org-tree-node-children {
    // display: flex;
    // flex-direction: column;
    // justify-content: center;
    // align-items: flex-start;
    display: table-cell;
    padding-top: 0;
    padding-left: 20px;

    &:before {
      top: 50%;
      left: 0;
      width: 20px;
      height: 0;
      border-left: 0;
      border-top: 1px solid #91d3ff;
    }

    &:after {
      display: none;
    }

    & > .org-tree-node {
      display: block;
    }
  }
}

.chart_wrap {
  width: 100%;
  height: 580px;
  margin-top: 20px;
  overflow: auto;
}

.filter_item {
  line-height: 30px;
  margin-right: 10px;
}

.no_data {
  line-height: 80px;
  color: var(--el-text-color-placeholder);
  text-align: center;
}
</style>
