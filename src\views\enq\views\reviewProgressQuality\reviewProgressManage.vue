<template>
  <div class="review_progress_manage_wrap">
    <div class="page_main_title">盘点进度</div>
    <div class="page_section review_progress_manage_center clearfix">
      <div class="training_activities_center clearfix">
        <div class="training_activities_item flex_row_between" v-for="(item, index) in listData" :key="item.id">
          <!-- <div class="progress_state">进行中</div> -->
          <!--                    <div class="item_index" v-if="(index < 8 || index == 8) && page.current == 1  ">0{{index+1}}</div> -->
          <!--                    <div class="item_index" v-if="index > 8 && page.current == 1  ">{{index+1}}</div>-->
          <div class="item_index">
            {{ index + 1 + (page.current - 1) * page.size }}
          </div>
          <div class="item_content_wrap">
            <div class="item_content flex_row_betweens">
              <div class="item_content_list item_info">
                <div class="list_title">项目名称</div>
                <div class="list_text">
                  <p class="item_pro_name overflow_elps" :title="item.projectName">
                    {{ item.enqName }}
                  </p>
                </div>
              </div>
              <div class="item_content_list range_date_wrap">
                <div class="list_title">起止日期</div>
                <div class="list_text">
                  {{ item.beginDate }}
                  <br />
                  {{ item.endDate }}
                </div>
              </div>
              <div class="item_content_list check_progress_wrap">
                <div class="list_title">个人盘点</div>
                <div class="list_text">
                  <div class="item_pro_name">
                    <div class="check_pro flex_row_between">
                      <span class="overflow_elps" :title="item.completed">{{ item.completed }}</span>
                      <p>提交</p>
                    </div>
                    <!-- <div class="check_pro">/</div> -->
                    <div class="check_pro flex_row_between">
                      <span class="overflow_elps" :title="item.total">{{ item.total }}</span>
                      <p>总数</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="item_content_list check_progress_wrap">
                <div class="list_title">部门盘点</div>
                <div class="list_text">
                  <div class="item_pro_name">
                    <div class="check_pro flex_row_between">
                      <span class="overflow_elps" :title="item.superiorCompleted">{{ item.superiorCompleted }}</span>
                      <p>提交</p>
                    </div>
                    <!-- <div class="check_pro">/</div> -->
                    <div class="check_pro flex_row_between">
                      <span class="overflow_elps" :title="item.superiorTotal">{{ item.superiorTotal }}</span>
                      <p>总数</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="item_content_list enq_status_wrap">
                <div class="list_title">状态</div>
                <div class="list_text">
                  {{ item.enqStatusName }}
                </div>
              </div>
            </div>
          </div>
          <div class="item_oper flex_row_start">
            <div class="item_oper_list" @click="() => progressInfo(item.enqId, item.enqName, 'P')">
              <!-- <i class="icon el-icon-user-solid"></i> -->
              <el-icon class="icon"><UserFilled /></el-icon>
              <div class="text">个人盘点进度</div>
            </div>
            <div class="item_oper_list" @click="() => progressInfo(item.enqId, item.enqName, 'D')">
              <!-- <i class="icon el-icon-s-data"></i> -->
              <el-icon class="icon"><Histogram /></el-icon>
              <div class="text">部门盘点进度</div>
            </div>
          </div>
        </div>
        <div class="pagination_wrap">
          <el-pagination
            :page-sizes="[10, 20, 50, 100]"
            @size-change="handleSizeChange"
            :current-page="page.current"
            :page-size="page.size"
            @current-change="handleCurrentChange"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getScheduleList } from '../../request/api'
import { useUserStore } from '@/stores/modules/user.js'
import { Histogram, UserFilled } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

const listData = ref([])
const page = reactive({
  current: 1,
  size: 10,
  total: 0
})

const companyId = computed(() => userStore.userInfo.companyId)

const progressInfo = (val, name, type) => {
  if (type == 'D') {
    // 部门进度
    router.push({
      path: '/talentReviewHome/reviewProgressQuality/RPmanage/RPDeptdetails',
      query: {
        enqId: val,
        enqName: name
      }
    })
  } else if (type == 'P') {
    // 个人进度
    router.push({
      path: '/talentReviewHome/reviewProgressQuality/RPmanage/RPdetails',
      query: {
        enqId: val,
        enqName: name
      }
    })
  } else if (type == 'Q') {
    // 盘点质量
    router.push({
      path: '/talentReviewHome/reviewProgressQuality/RQmanage/RQdetails',
      query: {
        enqId: val,
        enqName: name
      }
    })
  } else if (type == 'E') {
    // 评价进度
    router.push({
      path: '/talentReviewHome/reviewProgressQuality/RPmanage/REdetails',
      query: {
        enqId: val,
        enqName: name
      }
    })
  }
}

const handleSizeChange = val => {
  page.size = val
  getScheduleListFun()
}

const handleCurrentChange = val => {
  page.current = val
  getScheduleListFun()
}

const getScheduleListFun = async () => {
  const res = await getScheduleList({
    companyId: companyId.value,
    current: page.current,
    size: page.size
  })
  page.total = res.total
  listData.value = res.data.map(item => ({
    enqName: item.enqName,
    beginDate: item.beginDate ? item.beginDate.split(' ')[0] : '',
    endDate: item.endDate ? item.endDate.split(' ')[0] : '',
    completed: item.completed,
    total: item.total,
    completionRate: item.completionRate > 0 ? item.completionRate * 100 : item.completionRate,
    perfect: item.perfect,
    notStart: item.notStart,
    unLogin: item.unLogin,
    enqId: item.enqId,
    enqStatusName: item.enqStatusName,
    superiorCompleted: item.superiorCompleted,
    superiorTotal: item.superiorTotal
  }))
}

watch(
  companyId,
  val => {
    if (val) {
      getScheduleListFun()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.review_progress_manage_wrap {
  .review_progress_manage_center {
    .training_activities_center {
      .training_activities_item {
        position: relative;
        // height: 120px;
        padding: 12px 30px;
        margin-bottom: 8px;
        border: 1px solid #e5e5e5;
        overflow: hidden;
        .progress_state {
          width: 100px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          position: absolute;
          background: #90d99e;
          transform: rotate(-45deg);
          transform-origin: center;
          font-size: 10px;
          color: #fff;
          left: 0;
          left: -28px;
          top: 7px;
        }
        .item_index {
          width: 50px;
          text-align: center;
          font-weight: bold;
          font-size: 20px;
          color: #0099ff;
        }
        .item_content_wrap {
          width: 65%;
          padding: 0 8px;
          // background: pink;
          .item_content {
            padding-right: 10px;
            .item_content_list {
              color: #525e6c;
              .list_title {
                font-weight: bolder;
                line-height: 38px;
              }
              .list_text {
                // height: 38px;
                // line-height: 38px;
                .item_pro_name {
                  // line-height: 38px;
                  .check_pro {
                    // height: 38px;
                    line-height: 19px;
                    margin: 0 5px 0 0;
                    span {
                      display: inline-block;
                      font-weight: bold;
                      color: #0099ff;
                      font-size: 16px;
                      padding-right: 5px;
                    }
                  }
                }
              }
              .progress_details_item {
                text-align: center;
                // height: 38px;
                line-height: 38px;
                span {
                  width: 23px;
                  text-align: right;
                  font-weight: bold;
                  color: #0099ff;
                  font-size: 16px;
                }
                p {
                }
              }
              .list_num {
                // font-size: 16px;
                font-style: normal;
              }
            }
            .check_progress_wrap {
              // width: 220px;
              // background: pink;
            }
            .enq_status_wrap {
              width: 90px;
            }
            .item_info {
              // background: green;
              width: 240px;
            }
            .range_date_wrap {
              .list_text {
                // line-height: 38px;
                line-height: 19px;
              }
            }
          }
        }
        .progress_details {
          padding: 14px 0 0 0;
          width: 30%;
          background: #ebf4ff;
          height: 96px;
          border-radius: 5px;
        }
        .item_oper {
          width: 250px;
          text-align: center;
          // background: pink;
          .icon {
            display: inline-block;
            font-size: 28px;
            color: #0099ff;
            // width: 30px;
            // height: 33px;
            // background: url('../../../../../public/images/pro_mang_icon.png') no-repeat center;
            // background-size: 100% 100%;
          }
          &_list {
            width: 100%;
            cursor: pointer;
          }
        }
      }
      .training_activities_item:hover {
        // background: #EBF4FF;
        // cursor: pointer;
      }
    }
  }
}
</style>
