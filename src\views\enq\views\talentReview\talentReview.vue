<template>
  <div class="talent_review_wrap">
    <div class="page_main_title">人才盘点</div>
    <div class="oper_btn marginR_16 text_right" v-if="launchReviewBtn">
      <el-button type="primary" class="page_add_btn" v-link="'/talentReviewHome/talentReview/launchReview'"
        >发起盘点</el-button
      >
    </div>
    <div class="page_section marginT_8">
      <div class="talent_review_center clearfix">
        <div class="review_item" v-for="(item, index) in reviewListData" :key="item.enqId">
          <div class="index">
            {{ index + 1 + (currentPage - 1) * pageSize }}
          </div>
          <div class="item_content_wrap">
            <div class="item_content flex_row_betweens">
              <div class="item_content_list item_info">
                <div class="list_title">项目名称</div>
                <div class="list_text">
                  <p class="item_pro_name two_overflow_elps" :title="item.enqName">
                    {{ item.enqName }}
                  </p>
                </div>
              </div>
              <div class="item_content_list range_date_wrap">
                <div class="list_title">起止日期</div>
                <div class="list_text">
                  {{ removeTime(item.beginDate) }}
                </div>
                <div class="list_text" v-if="item.endDate">
                  {{ removeTime(item.endDate) }}
                </div>
              </div>

              <div class="item_content_list check_progress_wrap">
                <div class="list_title">个人盘点</div>
                <div class="list_text">
                  <div class="item_pro_name">
                    <div class="check_pro flex_row_betweens">
                      <span class="overflow_elps" :title="item.enqSubmitTotal">{{ item.enqSubmitTotal }}</span>
                      <p>提交</p>
                    </div>
                    <div class="check_pro flex_row_between">
                      <span class="overflow_elps" :title="item.enqTotal">{{ item.enqTotal }}</span>
                      <p>总数</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="item_content_list check_progress_wrap">
                <div class="list_title">部门盘点</div>
                <div class="list_text">
                  <div class="item_pro_name">
                    <div class="check_pro flex_row_between">
                      <span class="overflow_elps" :title="item.deptEnqSubmitTotal">{{ item.deptEnqSubmitTotal }}</span>
                      <p>提交</p>
                    </div>
                    <div class="check_pro flex_row_between">
                      <span class="overflow_elps" :title="item.deptEnqTotal">{{ item.deptEnqTotal }}</span>
                      <p>总数</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="review_progress_wrap">
            <simple-step-component
              :stepsData="stepsData"
              :completedStep="getEnqStep(item.enqStatus)"
            ></simple-step-component>
            <div class="steps_item_children flex_row_between">
              <div class="steps_item_children_item" v-for="(items, indexs) in item.menuActionVOList" :key="indexs">
                <div
                  v-for="list in items.actionList"
                  :key="list.actionCode"
                  class="item"
                  :class="{ event_none: !list.enable }"
                  @click="
                    actionClick(
                      list.enable,
                      list.actionCode,
                      list.actionUrl,
                      list.paramKey,
                      list.actionName,
                      item[list.paramKey],
                      item
                    )
                  "
                >
                  {{ list.actionName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <coustomPagination :total="total" :pageSizes="pageSizes" @pageChange="pageChange"></coustomPagination>
    </div>
    <el-dialog v-model="showOrgListDialog" title="盘点部门">
      <el-table :data="enqOrgList">
        <el-table-column property="org_name" label="部门名称"></el-table-column>
        <el-table-column property="enq_org_status" label="盘点状态" :formatter="formatterFun"></el-table-column>
        <el-table-column property="address" label="">
          <template #default="scope">
            <el-button @click="handleClick(scope.row)" link>{{
              scope.row.enq_org_status == 'E' ? '盘点' : '修改'
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { useEnqStore } from '../../store'
import { useRouter } from 'vue-router'
//import { ElMessage, ElMessageBox } from 'element-plus'
import {
  creatModel,
  getEnqList,
  personalComplete,
  deptEnqComplete,
  complete,
  setActionType,
  getAction,
  getLoginPersonDeptEnq
} from '../../request/api'
import simpleStepComponent from '@/components/talent/stepsComps/simpleStepComponent'
import coustomPagination from '@/components/talent/paginationComps/coustomPagination'
import { isAsyncAction } from '@/utils/utils'

const userStore = useUserStore()
const enqStore = useEnqStore()
const router = useRouter()

// 响应式状态
const currentEnqId = ref(null)
const showOrgListDialog = ref(false)
const enqOrgList = ref([])
const launchReviewBtn = ref(false)
const actionClickFlag = ref(true)
const currentPage = ref(1)
const pageSize = ref(5)
const total = ref(0)
const pageSizes = ref([5, 20, 50, 100, 200])
const completedStep = ref(1)
const stepsData = ref([])
const reviewListData = ref([])
const tabsPanesign = ref('')
const tabsData = ref([])
const tabsPaneInfo = ref([])

// 计算属性
const userInfo = computed(() => userStore.userInfo)

// 方法
const removeTime = val => {
  return val ? val.split(' ')[0] : ' '
}

const actionClick = async (actionEnable, actionType, actionUrl, actionParamKey, actionName, actionParam, enqData) => {
  if (!actionEnable) return

  const data = {}
  data[actionParamKey] = actionParam

  if (isAsyncAction(actionType)) {
    try {
      await ElMessageBox.confirm(`确定进行 ${actionName} 操作？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      if (actionClickFlag.value) {
        actionClickFlag.value = false
        const res = await setActionType({ url: actionUrl, data })
        if (res.code == '200') {
          ElMessage.success(res.msg)
          getEnqListFun()
        } else {
          ElMessage.warning(res.msg)
        }
      }
    } catch {
      ElMessage.info('已取消')
    }
  } else {
    if (actionType == 'C0101.24H') {
      const res = await getLoginPersonDeptEnq({ enqId: enqData.enqId })
      if (res.code == '200') {
        const data = res.data
        if (data.length > 1) {
          currentEnqId.value = enqData.enqId
          enqOrgList.value = data
          showOrgListDialog.value = true
        } else {
          router.push(actionUrl + actionParamKey + '=' + actionParam + '&orgCode=' + data[0]['org_code'])
        }
      }
    } else {
      router.push(actionUrl + actionParamKey + '=' + actionParam)
    }
  }
}

const formatterFun = (row, column, cellValue) => {
  if (cellValue == 'E') return '未提交'
  else if (cellValue == 'P') return '已提交'
}

const handleClick = row => {
  router.push(
    '/talentReviewHome/talentReview/departmentReview?enqId=' + currentEnqId.value + '&orgCode=' + row['org_code']
  )
}

const getEnqListFun = async () => {
  const params = {
    current: currentPage.value,
    size: pageSize.value
  }
  const res = await getEnqList(params)
  if (res.code == '200') {
    if (res.data && res.data.length != 0) {
      stepsData.value = res.data[0].menuMap
    }
    reviewListData.value = res.data
    total.value = res.total
    actionClickFlag.value = true
  }
}

const personalCompleteFun = async enqId => {
  const res = await personalComplete({ id: enqId })
  if (res.code == '200') {
    ElMessage.success(res.msg)
    getEnqListFun()
  } else {
    ElMessage.warning(res.msg)
  }
}

const deptEnqCompleteFun = async enqId => {
  const res = await deptEnqComplete({ id: enqId })
  if (res.code == '200') {
    ElMessage.success(res.msg)
    getEnqListFun()
  } else {
    ElMessage.warning(res.msg)
  }
}

const enqCompleteFun = async enqId => {
  const res = await complete({ id: enqId })
  if (res.code == '200') {
    ElMessage.success(res.msg)
    getEnqListFun()
  } else {
    ElMessage.warning(res.msg)
  }
}

const getActionFun = async () => {
  const res = await getAction({ actionCode: 'C0101.01H' })
  if (res.code == '200') {
    launchReviewBtn.value = res.data == null ? false : true
  }
}

const getEnqStep = enqStatus => {
  let enqStep = 1
  switch (enqStatus) {
    case '1':
      enqStep = 1
      break
    case '2':
    case '3':
    case '4':
    case '5':
      enqStep = 2
      break
    case '6':
      enqStep = 3
      break
    default:
      enqStep = 1
      break
  }
  return enqStep
}

const pageChange = (size, current) => {
  pageSize.value = size
  currentPage.value = current
  getEnqListFun()
}

const creatModelFun = async () => {
  const res = await creatModel({})
  if (res.code == 200) {
    tabsPanesign.value = res.data[0].moduleCode
    tabsData.value = res.data.map(item => ({
      label: item.modelName,
      name: item.modelId,
      moduleCode: item.moduleCode,
      modelId: item.modelId
    }))
    tabsPaneInfo.value = [tabsData.value[0]]
  }
}

// 生命周期钩子
onMounted(() => {
  enqStore.setCreateEnqId(null)
  getEnqListFun()
  getActionFun()
  // creatModelFun()
})
</script>

<style scoped lang="scss">
.talent_review_center {
  .review_item {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5px;
    border: 1px solid #e5e5e5;
    line-height: 20px;
    margin-bottom: 8px;
    &:hover {
      background: #ebf4ff;
    }
    .index {
      font-size: 20px;
      font-weight: bold;
      width: 60px;
      color: #0099ff;
      text-align: center;
    }
    .review_info {
      width: 50%;
      /*border-right: 1px solid #e5e5e5;*/
      padding-right: 25px;
      .num {
        font-size: 16px;
        color: #0099ff;
        font-weight: bold;
        padding: 0 2px;
      }
      .review_info_name {
        margin-bottom: 8px;
        .name {
          font-size: 14px;
          font-weight: bold;
        }
      }
      .review_date {
        margin-bottom: 5px;
        font-size: 12px;
      }
      .review_admin_info {
        margin-top: 20px;
        font-size: 12px;
        .company_name {
          width: 200px;
        }
        .company_admin {
        }
      }
    }
    .review_progress_wrap {
      width: 50%;
      margin-left: 25px;
      padding: 5px;
      background: #ebf4ff;
      .steps_item_children {
        align-items: flex-start;
        .steps_item_children_item {
          width: 210px;
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          padding: 0 10px;
          &.item_one,
          &.item_three {
            flex: 1;
          }
        }
        .item {
          float: left;
          font-size: 12px;
          background: #0099ff;
          border-radius: 2px;
          margin-right: 3px;
          margin-bottom: 3px;
          text-align: center;
          line-height: 18px;
          padding: 3px 7px;
          cursor: pointer;
          color: #fff;
          &.event_none {
            background: #c3c3c3;
          }
        }
      }
    }
  }
  .item_content_wrap {
    width: 65%;
    padding: 0 8px;
    // background: green;
    .item_content {
      padding-right: 10px;
      .item_content_list {
        color: #525e6c;
        .list_title {
          font-weight: bolder;
          line-height: 38px;
        }
        .list_text {
          //   height: 40px;
          //   line-height: 20px;
          // background: pink;
          .item_pro_name {
            // line-height: 20px;

            .check_pro {
              //   height: 38px;
              //   line-height: 38px;
              //   margin: 0 5px 0 0;
              // background: yellow;
              span {
                display: inline-block;
                font-weight: bold;
                color: #0099ff;
                font-size: 16px;
                padding-right: 5px;
              }
            }
          }
        }
        .progress_details_item {
          text-align: center;
          // height: 38px;
          //   line-height: 38px;
          span {
            width: 23px;
            text-align: right;
            font-weight: bold;
            color: #0099ff;
            font-size: 16px;
          }
          p {
          }
        }
        .list_num {
          // font-size: 16px;
          font-style: normal;
        }
      }
      .check_progress_wrap {
        .list_text {
          //   height: 40px !important;
          //   line-height: 20px !important;
          //   background: green;
          .check_pro {
            // background: green !important;
            line-height: 20px !important;
          }
        }
      }
      .enq_status_wrap {
        width: 90px;
      }
      .item_info {
        // background: green;
        width: 180px;
      }
      .range_date_wrap {
        .list_text {
          //   line-height: 38px;
        }
      }
    }
  }
}
.talent_review_wrap {
  .el-table__body tr td {
    font-size: 16px;
  }
}
</style>
