<template>
  <div class="job_grade_wrap rDataPer_quality_evaluation_wrap bg_write">
    <div class="page_section">
      <div class="page_section job_grade_center clearfix">
        <div class="filter_bar_wrap">
          <div class="flex_row_start">
            <div class="filter_item title">筛选</div>
            <div class="filter_item">
              <el-input
                v-model="objectName"
                placeholder="按被评人员姓名模糊查询"
                clearable
                
              ></el-input>
            </div>
            <div class="filter_item">
              <el-input
                v-model="userName"
                placeholder="按评价人员名称模糊查询"
                clearable
                
              ></el-input>
            </div>
            <div class="filter_item area_filter_item">
              <span class="select_title">分值区间：</span>
              <el-input
                class="filter_ipt_mini"
                
                type="number"
                v-model="minScore"
                placeholder=""
              ></el-input>
              <span class="sign">~</span>
              <el-input
                class="filter_ipt_mini"
                
                type="number"
                v-model="maxScore"
                placeholder=""
              ></el-input>
            </div>
            <div class="filter_item">
              <span class="select_title">差异区间：</span>
              <el-select
                class="filter_item marginR_16"
                v-model="dictCode"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in differenceOption"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </div>
            <div class="filter_item">
              <el-button
                class="page_add_btn"
                type="primary"
                
                @click="getTargetDataFun"
                >查询</el-button
              >
            </div>
          </div>
        </div>
        <table-component
          :needIndex="true"
          :tableData="tableData"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        >
          <template v-slot:oper>
            <el-table-column label="操作" width="80" align="center">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.rstatus == 'Y'"
                  @click.native.prevent="tableEdit(scope)"
                  link
                  class="icon_edit"
                  >作废</el-button
                >
                <el-button v-if="scope.row.rstatus == 'N'" link class=""
                  >已作废</el-button
                >
              </template>
            </el-table-column>
          </template>
        </table-component>
      </div>
    </div>
  </div>
</template>
 
<script>
import tableComponent from "@/components/talent/tableComps/tableComponent";
import {
  getDictList,
  getUserObjectiveList,
  toVoid,
} from "../../../request/api";
export default {
  name: "rDataQualityEvaluation",
  components: {
    tableComponent,
  },
  props: ["enqId"],
  data() {
    return {
      userName: "",
      objectName: "",
      minScore: "",
      maxScore: "",
      dictCode: "",
      differenceOption: [{}],
      tableData: {
        columns: [
          {
            label: "一级组织",
            prop: "oneLevelName",
          },
          {
            label: "二级组织",
            prop: "twoLevelName",
          },
          {
            label: "三级组织",
            prop: "threeLevelName",
          },
          {
            label: "被评人姓名",
            prop: "objectName",
          },
          {
            label: "被评人岗位",
            prop: "postName",
          },
          {
            label: "评价关系",
            prop: "relationTypeName",
          },
          {
            label: "模块",
            prop: "moduleType",
          },
          {
            label: "词典",
            prop: "moduleName",
          },
          {
            label: "评价分值",
            prop: "actualScore",
          },
          {
            label: "综合差异",
            prop: "difference",
          },
        ],
        data: [],
        page: {
          current: 1,
          size: 10,
          total: 0,
        },
      },
    };
  },
  mounted() {
    this.getDictListFun();
    this.getUserObjectiveListFun();
  },
  methods: {
    getDictListFun() {
      getDictList({
        dictIds: "DIFFERENCE",
      }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.differenceOption = res.data.DIFFERENCE;
        }
      });
    },
    getUserObjectiveListFun() {
      getUserObjectiveList({
        enqId: this.enqId,
        current: this.tableData.page.current,
        size: this.tableData.page.size,
        type: "quality",
        objectName: this.objectName,
        userName: this.userName,
        minScore: this.minScore,
        maxScore: this.maxScore,
        dictCode: this.dictCode,
      }).then((res) => {
        this.tableData.data = [];
        this.tableData.page = {
          current: 1,
          size: 10,
          total: 0,
        };
        if (res.code == 200) {
          this.tableData.data = res.data;
          this.tableData.page = res.page;
        }
      });
    },
    //pageSize 改变时会触发
    handleSizeChange(size) {
      console.log(size);
      this.tableData.page.current = 1;
      this.tableData.page.size = size;
      this.getUserObjectiveListFun();
    },
    //currpage 改变时会触发
    handleCurrentChange(page) {
      this.tableData.page.current = page;
      this.getUserObjectiveListFun();
    },
    getTargetDataFun() {
      if (this.minScore > this.maxScore) {
        this.$msg.warning("分值区间应从小到大！");
        return;
      }

      this.tableData.page.current = 1;
      this.getUserObjectiveListFun();
    },
    tableEdit(scope) {
      console.log(scope.row);
      this.$confirm("确定删除?", "确定", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(() => {
          this.toVoidFun(scope.row.objectId, scope.row.userId);
        })
        .catch(() => {});
    },
    toVoidFun(objectId, userId) {
      toVoid({
        enqId: this.enqId,
        objectId: objectId,
        type: "quality",
        userId: userId,
      }).then((res) => {
        if (res.code == 200) {
          this.$msg.success(res.msg);
          this.tableData.page.current = 1;
          this.getUserObjectiveListFun();
        }
      });
    },
  },
};
</script>
 
<style scoped lang="scss">
.rDataPer_quality_evaluation_wrap {
  .area_filter_item {
    .select_title {
      white-space: nowrap;
    }
    .el-input__inner {
      width: 70px;
    }
  }
  .icon_edit {
    width: 60px;
    height: 28px;
    background: #0099ff;
    color: #fff;
    padding: 0;
    margin: 0 auto;
    span {
      display: inline-block;
      width: 60px;
      height: 25px;
      line-height: 25px;
      font-size: 16px;
    }
  }
}
</style>