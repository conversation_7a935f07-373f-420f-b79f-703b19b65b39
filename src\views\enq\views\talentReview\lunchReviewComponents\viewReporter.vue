<template>
  <div class="clearfix marginT_30" :class="{ event_none: !isEdit }">
    <div class="clearfix">
      <div class="clearfix report_view_wrap fl">
        <div class="selection_range_left">
          <div class="page_second_title">报告生成范围</div>
          <div class="report_desc marginT_20">
            组织报告：按照组织维度生成盘点报告，涉及的组织层级为：集团、公司与部门且仅组织负责人查看；个人报告盘点人员及其上级查看）
          </div>
          <div class="select_report_range">
            <el-checkbox v-model="reportOrgFlag" label="组织报告">组织报告</el-checkbox>
            <el-checkbox v-model="reportUserFlag" label="个人报告">个人报告</el-checkbox>
          </div>
        </div>
        <div class="selection_range_left">
          <div class="page_second_title">可查看下级报告的范围</div>
          <div class="report_desc marginT_20">
            直接下级：汇报关系中直接上级是自己的所有人的报告；
            所有下级：直接下级以及所有下级的下级（所有下级看到的报告较多）
          </div>
          <div class="select_report_range">
            <el-radio-group v-model="level" @change="levelChange">
              <el-radio :label="1">直接下级</el-radio>
              <el-radio :label="99">所有下级</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="selection_range_select_post marginT_30">
          <div class="page_second_title">可查看报告统计</div>
          <div class="table_wrap marginT_20">
            <table-component :tableData="tableData" :needPagination="false" />
          </div>
        </div>
      </div>
      <div class="center_right">
        <div class="review_content">
          <div class="item">
            <div class="title">有权限查看报告人数</div>
            <div class="content">
              <span class="text">{{ tableData.data.length }}</span>
              <span>项</span>
            </div>
          </div>
          <div class="item">
            <div class="title">有权限查看个人报告</div>
            <div class="content">
              <span class="text">28</span>
              <span>分钟</span>
            </div>
          </div>
          <div class="item">
            <div class="title">有权限查看组织报告</div>
            <div class="content">
              <span class="text">30</span>
              <span>分钟</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="step_btn_wrap align_center" v-if="isEdit">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="submit">下一步</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { updateReportScope, getReportScopeList, getEnqInfo } from '../../../request/api'
import TableComponent from '@/components/talent/tableComps/tableComponent'

const props = defineProps({
  getEnqId: {
    type: Function,
    required: true
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['prevStep', 'nextStep'])

const enqId = ref(null)
const reportOrgFlag = ref(false)
const reportUserFlag = ref(false)
const level = ref(1)
const tableData = ref({
  columns: [
    { label: '公司', prop: 'companyName' },
    { label: '部门', prop: 'orgName' },
    { label: '姓名', prop: 'userName' },
    { label: '组织报告', prop: 'orgReportCount' },
    { label: '个人报告', prop: 'userReportCount' }
  ],
  data: []
})

const getEnqInfoFun = async () => {
  try {
    const res = await getEnqInfo({ id: enqId.value })
    if (res.code == 200) {
      const data = res.data
      level.value = data.reportLayerDepth || 1
      reportOrgFlag.value = data.reportOrgFlag == 'Y'
      reportUserFlag.value = data.reportUserFlag == 'Y'
      await getReportScopeListFun()
    } else {
      ElMessage.error('获取盘点基本信息失败！')
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const levelChange = async () => {
  await getReportScopeListFun()
}

const getReportScopeListFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      level: level.value
    }
    const res = await getReportScopeList(params)
    if (res.code == 200) {
      tableData.value.data = res.data
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const saveReportScope = async type => {
  try {
    const params = {
      id: enqId.value,
      reportLayerDepth: level.value,
      reportOrgFlag: reportOrgFlag.value ? 'Y' : 'N',
      reportUserFlag: reportUserFlag.value ? 'Y' : 'N'
    }
    const res = await updateReportScope(params)
    if (res.code == 200) {
      ElMessage.success(res.msg)
      emit(type)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const prevBtn = async () => {
  try {
    await ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '保存',
      cancelButtonText: '放弃修改'
    })
    await saveReportScope('prevStep')
  } catch (action) {
    if (action == 'cancel') {
      ElMessage.info('已放弃修改并返回上一步')
      emit('prevStep')
    } else {
      ElMessage.info('取消返回上一步')
    }
  }
}

const submit = async () => {
  if (!level.value) {
    ElMessage.warning('请选择可查看报告人员的范围')
    return
  }
  if (!reportOrgFlag.value && !reportUserFlag.value) {
    ElMessage.warning('请选择报告生成范围！')
    return
  }
  await saveReportScope('nextStep')
}

onMounted(() => {
  enqId.value = props.getEnqId()
  getEnqInfoFun()
})
</script>

<style scoped lang="scss">
.report_view_wrap {
  width: 850px;
}

.selection_range_left {
  float: left;
  width: 400px;
  margin-right: 16px;
  margin-bottom: 16px;

  .report_desc {
    margin-bottom: 16px;
    height: 60px;
  }
}

.selection_range_select_post {
  float: left;
  width: 800px;
}

.center_right {
  float: right;
  width: 260px;
  margin-top: 40px;

  .review_content {
    .item {
      width: 230px;
      height: 80px;
      border: 1px solid #e5e5e5;
      text-align: center;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 2px;
      color: #555;

      .title {
        margin-bottom: 5px;
        font-size: 14px;
      }

      .text {
        font-size: 30px;
        color: #0099fd;
        font-weight: bold;
      }
    }
  }
}
</style>
