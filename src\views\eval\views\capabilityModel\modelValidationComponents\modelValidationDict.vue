<template>
    <div class="model_validation_dict_wrap marginT_20">
        <div class="model_validation_dict_main flex_row_betweens">
            <div class="main_left_wrap page_section">
                <p class="page_second_title">确认能力词典</p>
                <div class="model_validation_dict_left_main marginT_16">
                    <table-component
                        :tableData="tableData"
                        :height="'500'"
                        :needIndex="needIndex"
                        :needPagination="needPagination"
                    >
                        <template v-slot:oper2>
                            <el-table-column label="综合得分" width="250">
                                <template slot-scope="scope">
                                    <div
                                        class="completion_rate flex_row_between"
                                    >
                                        <div class="bar_wrap">
                                            <div
                                                class="bar_progress"
                                                :class="
                                                    scope.row.overallScore < 50
                                                        ? 'bg_low'
                                                        : scope.row
                                                              .overallScore < 70
                                                        ? 'bg_normal'
                                                        : scope.row
                                                              .overallScore < 90
                                                        ? 'bg_middle'
                                                        : 'bg_high'
                                                "
                                                :style="{
                                                    width:
                                                        scope.row.overallScore +
                                                        '%',
                                                }"
                                            ></div>
                                        </div>
                                        <div
                                            class="completion_rate_num"
                                            :class="
                                                scope.row.overallScore < 50
                                                    ? 'color_low'
                                                    : scope.row.overallScore <
                                                      70
                                                    ? 'color_normal'
                                                    : scope.row.overallScore <
                                                      90
                                                    ? 'color_middle'
                                                    : 'color_high'
                                            "
                                        >
                                            {{ scope.row.overallScore }}
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="状态" width="80">
                                <template slot-scope="scope">
                                    <div v-if="type == 'show'">
                                        <span
                                            class="btn_confirm"
                                            link
                                            v-if="
                                                scope.row.mouduleStatus == 'Y'
                                            "
                                            >已确认</span
                                        >
                                        <span
                                            class="btn_no_confirm"
                                            link
                                            v-else
                                            >未确认</span
                                        >
                                    </div>
                                    <div v-else>
                                        <el-button
                                            class="btn_confirm"
                                            link
                                            v-if="
                                                scope.row.mouduleStatus == 'Y'
                                            "
                                            @click="
                                                confirmModule(
                                                    scope.row.moduleCode,
                                                    scope.row.mouduleStatus
                                                )
                                            "
                                            >已确认</el-button
                                        >
                                        <el-button
                                            class="btn_no_confirm"
                                            link
                                            v-else
                                            @click="
                                                confirmModule(
                                                    scope.row.moduleCode,
                                                    scope.row.mouduleStatus
                                                )
                                            "
                                            >未确认</el-button
                                        >
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                    </table-component>
                </div>
            </div>
            <div class="main_right_wrap page_section">
                <p class="page_second_title">
                    词典分类数量预览<span class="tips"></span>
                </p>
                <div id="chartWrap" class="marginT_16"></div>
            </div>
        </div>
        <div class="align_center marginT_30" v-if="type != 'show'">
            <el-button class="page_confirm_btn" type="primary" @click="next()"
                >下一步</el-button
            >
        </div>
    </div>
</template>

<script>
    import { echartsRenderPage } from "../../../../../../public/js/echartsimg/echartsToImg";
    import {
        getConfirmModuleDict,
        confirmModuleBuildModule,
        confirmModuleBuildModuleNextStep,
    } from "../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";

    export default {
        name: "modelValidationDict",
        components: {
            tableComponent,
        },
        props: ["modelId", "buildId", "type"],
        data() {
            return {
                needPagination: false,
                needIndex: true,
                tableData: {
                    columns: [
                        {
                            label: "能力分类",
                            prop: "parentModuleName",
                        },
                        {
                            label: "能力词典",
                            prop: "moduleName",
                        },
                        {
                            label: "比较重要",
                            prop: "importance3Count",
                        },
                        {
                            label: "一般",
                            prop: "importance2Count",
                            width: 60,
                        },
                        {
                            label: "不重要",
                            prop: "importance1Count",
                            width: 80,
                        },
                        // {
                        //     label: "综合得分",
                        //     prop: "score",
                        //     // width: 200
                        // },
                        // {
                        //     label: "",
                        //     prop: "affirm",
                        //     // width: 200
                        // },
                    ],
                    data: [],
                },
                chartData: {
                    data: [],
                },
            };
        },
        created() {},
        mounted() {
            this.getConfirmModuleDictFun();
        },
        methods: {
            getConfirmModuleDictFun() {
                getConfirmModuleDict({
                    buildId: this.buildId,
                    modelId: this.modelId,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.tableData.data = res.data.list;
                        this.chartData.data = res.data.preview.map((item) => {
                            return {
                                name: item.module_name,
                                value: item.number,
                            };
                        });
                        this.echartsRenderPageFun(
                            "chartWrap",
                            "YBar",
                            250,
                            360,
                            this.chartData
                        );
                    }
                });
            },
            echartsRenderPageFun(id, type, width, height, chartData) {
                echartsRenderPage(id, type, width, height, chartData);
            },
            confirmModule(moduleCode, status) {
                if (status == "Y") {
                    this.$confirm("取消确认此条词典？", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                        .then(() => {
                            this.confirmModuleBuildModuleFun(moduleCode, status);
                        })
                        .catch(() => {});
                } else {
                    this.$confirm("确认此条词典？", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    })
                        .then(() => {
                            this.confirmModuleBuildModuleFun(moduleCode, status);
                        })
                        .catch(() => {});
                }
            },
            confirmModuleBuildModuleFun(code, status) {
                confirmModuleBuildModule({
                    buildId: this.buildId,
                    moduleCode: code,
                    status: status == "Y" ? "N" : "Y",
                }).then((res) => {
                    if (res.code == 200) {
                        this.$msg.success(res.msg);
                        this.getConfirmModuleDictFun();
                    }
                });
            },
            confirmModuleBuildModuleNextStepFun() {
                confirmModuleBuildModuleNextStep({
                    buildId: this.buildId,
                }).then((res) => {
                    // console.log(res)
                    if (res.code == 200) {
                        this.$emit("nextStep");
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            next() {
                // 判断有没有被确认的能力词典
                let arr = [];
                this.tableData.data.forEach((element) => {
                    let status = element.mouduleStatus;
                    arr.push(status);
                });
                console.log(arr);
                if (arr.includes("Y")) {
                    this.confirmModuleBuildModuleNextStepFun();
                }else{
                    this.$msg.warning('请至少确认一项能力词典')
                }
            },
        },
    };
</script>

<style scoped lang="scss">
    .model_validation_dict_wrap {
        .model_validation_dict_title {
            padding: 3px 8px;
            font-size: 16px;
            line-height: 28px;
            color: #0099fd;
            font-weight: bold;
            background-color: #f2f8ff;
            border-radius: 3px;
        }

        .model_validation_dict_main {
            .main_left_wrap {
                width: 75%;
            }

            .main_right_wrap {
                width: 25%;
            }
        }

        .completion_rate {
            .bar_wrap {
                width: calc(100% - 45px);
                height: 18px;
                background: #EBF4FF;
                position: relative;
                padding-top: 5px;

                .bar_progress {
                    background: #00b050;
                    height: 8px;
                    width: 0%;
                    &.bg_high {
                        background: #00b050;
                    }

                    &.bg_middle {
                        background: #00b0f0;
                    }

                    &.bg_normal {
                        background: #ffc000;
                    }

                    &.bg_low {
                        background: #ff8181;
                    }
                }
            }

            .completion_rate_num {
                font-weight: bold;

                &.not_login {
                    color: #ff6d6d;
                }
            }
        }
    }
    .btn_confirm {
        color: #0099FF;
        padding: 3px 5px;
        border: 1px solid #0099FF;
        border-radius: 2px;
        background: #EBF4FF;
    }
    .btn_no_confirm {
        color: #f00;
        padding: 3px 5px;
        border: 1px solid #f00;
        border-radius: 2px;
        background: #f5dbdb;
        &:hover {
            color: #f00;
        }
    }
</style>