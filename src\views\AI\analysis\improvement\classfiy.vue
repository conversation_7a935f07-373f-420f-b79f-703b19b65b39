<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import SimplenessTable from '@/components/table/simplenessTable.vue'
defineOptions({ name: 'affect' })
const chartData = ref([
  {
    name: '流程端到端闭环',
    value: '6 '
  },
  {
    name: '输出文档',
    value: '3 '
  },
  {
    name: '业务规则',
    value: '4 '
  },
  {
    name: '业务KPI',
    value: '5 '
  },
  {
    name: '组织设置',
    value: '3 '
  },
  {
    name: '岗位角色职责',
    value: '3 '
  },
  {
    name: '岗位协同RACI',
    value: '6 '
  },
  {
    name: '组织岗位KPI',
    value: '6 '
  },
  {
    name: '人员动力',
    value: '5 '
  },
  {
    name: '人员能力要求',
    value: '2 '
  },
  {
    name: '人员能力评估',
    value: '2 '
  },
  {
    name: '能力培训',
    value: '3 '
  },
  {
    name: '系统赋能',
    value: '4 '
  },
  {
    name: '数据治理',
    value: '1 '
  },
  {
    name: '系统集成',
    value: '2 '
  },
  {
    name: '自动化与智能化设备',
    value: '3'
  }
])
const chartOpt = ref({
  xAxisData: chartData.value.map(i => i.name),
  xAxis: {
    show: true,
    axisLabel: {
      interval: 0,
      rotate: 45
    }
  },
  series: [
    {
      data: chartData.value.map(i => i.value),
      type: 'bar',
      showBackground: true,
      itemStyle: {
        color: '#40a0ff'
      },
      label: {
        show: true
      }
    }
  ]
})

const tacticsCol = ref([
  {
    label: '序号',
    type: 'index'
  },
  {
    label: '一级策略',
    prop: 'name',
    width: '110px'
  },
  {
    label: '二级策略',
    prop: 'name2',
    width: '110px'
  },
  {
    label: '策略名称',
    prop: 'name3',
    width: '110px'
  },
  {
    label: '概要目标',
    prop: 'target'
  }
])
const tacticsData = ref([
  {
    name: '流程赋能',
    name2: '流程端到端',
    name3: '需求计划全流程端到端贯通',
    target: '建立覆盖市场调研、数据整合、计划评审的全流程体系，实现需求计划全链路可视化与闭环管理。'
  },
  {
    name: '流程赋能',
    name2: '输入输出文档',
    name3: '需求计划标准化文档体系建设',
    target: '统一需求计划输入模板（含历史销量、市场趋势、客户订单）与输出规范，确保跨部门数据交互效率提升 30%。'
  },
  {
    name: '流程赋能',
    name2: '业务规则',
    name3: '需求计划动态调整规则库构建',
    target: '建立基于市场波动、产能约束、库存水平的需求计划弹性调整规则，实现需求响应周期缩短 20%。'
  },
  {
    name: '流程赋能',
    name2: '业务 KPI',
    name3: '需求计划精准度 KPI 体系优化',
    target: '设定需求预测准确率（目标≥90%）、计划调整及时率（目标≥95%）等核心 KPI，配套季度考核机制。'
  },
  {
    name: '组织赋能',
    name2: '组织设置',
    name3: '跨部门需求管理委员会组建',
    target: '设立由市场、销售、计划、生产、采购组成的专项委员会，每月召开需求对齐会议，强化端到端协同。'
  },
  {
    name: '组织赋能',
    name2: '岗位角色职责',
    name3: '需求计划岗位权责清单化管理',
    target: '明确需求分析师、计划协调员等岗位核心职责，建立岗位说明书 2.0 版，消除职责交叉与管理盲区。'
  },
  {
    name: '组织赋能',
    name2: '岗位协同 RACI',
    name3: '需求管理流程 RACI 矩阵优化',
    target:
      '构建跨岗位 RACI 责任矩阵（明确需求制定 R - 责任方、A - 审批方、C - 咨询方、I - 告知方），提升流程协作效率。'
  },
  {
    name: '组织赋能',
    name2: '组织岗位 KPI',
    name3: '需求管理团队 KPI 分级考核体系',
    target: '建立管理层（战略匹配度）、执行层（计划准确率）、操作层（数据及时率）三级 KPI，实施差异化考核。'
  },
  {
    name: '人岗赋能',
    name2: '人员动力',
    name3: '计划团队激励机制建设',
    target: '设立需求计划创新奖、精准度奖金池，配套职业发展双通道，提升核心人才留存率至 95% 以上。'
  },
  {
    name: '人岗赋能',
    name2: '人员能力要求',
    name3: '需求管理岗位能力模型升级',
    target: '构建包含数据分析（30%）、跨部门沟通（25%）、系统应用（20%）、行业洞察（25%）的四维能力模型。'
  },
  {
    name: '人岗赋能',
    name2: '人员能力评估',
    name3: '计划人才能力 360 度评估体系',
    target: '引入上级评价、同事互评、自我评估、系统数据四维评估，每年 Q2/Q4 实施能力等级认证。'
  },
  {
    name: '人岗赋能',
    name2: '能力培训',
    name3: '供应链计划专项人才培养计划',
    target: '联合顶尖咨询机构开发定制化课程，年度人均培训时长≥40 课时，重点提升 AI 预测工具应用能力。'
  },
  {
    name: '数字化赋能',
    name2: '系统赋能',
    name3: '智能需求计划系统升级项目',
    target: '引入 AI 驱动的需求预测系统（如 Oracle Demand Planning），实现历史数据自动清洗、多场景模拟预测。'
  },
  {
    name: '数字化赋能',
    name2: '数据治理',
    name3: '需求数据主数据管理体系建设',
    target: '统一客户、产品、渠道主数据标准，建立数据质量监控仪表盘，确保需求数据完整性≥99%、准确性≥98%。'
  },
  {
    name: '数字化赋能',
    name2: '系统改善规划',
    name3: '计划系统功能迭代路线图',
    target: '规划未来 3 年系统升级路径（2025 年实现移动端审批、2026 年集成 IoT 设备数据、2027 年部署区块链存证）。'
  },
  {
    name: '数字化赋能',
    name2: '自动化与智能化设备',
    name3: '智能数据采集终端部署',
    target: '在销售终端部署 IoT 数据采集设备，实现订单数据实时同步率 100%，减少人工录入误差与延迟。'
  },
  {
    name: 'AI 赋能',
    name2: '算法赋能',
    name3: '机器学习需求预测模型开发',
    target: '基于历史销量、促销活动、天气数据训练 LSTM 预测模型，目标将预测误差率降低至 8% 以内。'
  },
  {
    name: 'AI 赋能',
    name2: '业务模型优化',
    name3: '动态需求分配模型构建',
    target: '开发基于产能、库存、运输成本的多目标优化模型，实现需求与供应资源的智能匹配。'
  },
  {
    name: 'AI 赋能',
    name2: '数据驱动决策',
    name3: '需求管理驾驶舱可视化升级',
    target: '构建实时数据驾驶舱，集成需求波动预警、库存健康度分析等模块，支撑管理层 10 分钟级决策响应。'
  },
  {
    name: 'AI 赋能',
    name2: '自动化流程优化',
    name3: '需求计划审批流程自动化改造',
    target: '部署 RPA 机器人处理常规计划审批，例外事项自动触发人工审核，实现流程处理效率提升 60%。'
  },
  {
    name: 'AI 赋能',
    name2: '智能风险预警',
    name3: '需求异常智能监测系统上线',
    target:
      '设定需求突增 / 骤降、预测偏差超阈值等 10 + 预警规则，实现 7×24 小时实时监测与多渠道通知（邮件 / 短信 / 系统弹窗）。'
  }
])
const tacticsList = ref([
  {
    name: '策略简介',
    id: 'desc',
    comp: defineAsyncComponent(() => import('./classfiy/desc.vue'))
  },
  {
    name: '核心框架',
    id: 'core',
    comp: defineAsyncComponent(() => import('./classfiy/core.vue'))
  },
  {
    name: '实施步骤与资源保障',
    id: 'process',
    comp: defineAsyncComponent(() => import('./classfiy/process.vue'))
  },
  {
    name: '策略评估指标',
    id: 'indicator',
    comp: defineAsyncComponent(() => import('./classfiy/indicator.vue'))
  },
  {
    name: '策略应用组织建议',
    id: 'suggestion',
    comp: defineAsyncComponent(() => import('./classfiy/suggestion.vue'))
  }
])

const activeType = ref(tacticsList.value[0])
const changeType = item => {
  activeType.value = item
}
const tableRef = ref(null)
onMounted(() => {
  tableRef.value?.simplenessTableRef.setCurrentRow(tacticsData.value[0])
})
</script>
<template>
  <div class="affect-page">
    <div class="page-title-line">能力改善策略库内容分布</div>
    <div class="h-[300px]">
      <EChartsBar :options="chartOpt"></EChartsBar>
    </div>
    <div class="page-title-line mt-7">能力改善策略库信息</div>
    <SimplenessTable ref="tableRef" height="400px" highlight-current-row :columns="tacticsCol" :data="tacticsData">
      <template #oper="">
        <el-table-column label="" width="100">
          <template #default="">
            <el-button size="small" type="primary" plain>详情</el-button>
          </template>
        </el-table-column>
      </template>
    </SimplenessTable>
    <div class="page-title-line mt-7">能力改善策略详情(商机推进全流程管理闭环策略)</div>
    <div class="tactics-type">
      <div
        class="type-list"
        :class="{ active: activeType.id == item.id }"
        @click="changeType(item)"
        v-for="item in tacticsList"
      >
        {{ item.name }}
      </div>
    </div>
    <component :is="activeType.comp"></component>
  </div>
</template>
<style lang="scss" scoped>
.tactics-type {
  display: flex;
  align-items: center;
  gap: 13px;
  margin-bottom: 23px;
  .type-list {
    min-width: 134px;
    background: #ffffff;
    border-radius: 141px 141px 141px 141px;
    border: 1px solid #dee6ee;
    text-align: center;
    line-height: 30px;
    padding: 0 5px;
    cursor: pointer;
    &.active {
      background: #40a0ff;
      border-color: #40a0ff;
      color: #fff;
    }
  }
}
</style>
