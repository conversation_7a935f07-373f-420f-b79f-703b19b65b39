<template>
  <div class="post_description_wrap bg_write">
    <div class="page_main_title">职位说明书</div>
    <div class="page_section">
      <div class="page_section_aside fl">
        <div class="aside_tree_title">
          <div>筛选</div>
        </div>
        <div class="aside_tree_list">
          <tree-comp-radio
            :treeData="treeData"
            :needCheckedFirstNode="false"
            :canCancel="true"
            @clickCallback="clickCallback"
          ></tree-comp-radio>
        </div>
      </div>
      <div class="page_section_main padd_TB_16 clearfix">
        <div class="flex_row_wrap_start">
          <div
            class="post_description_item"
            v-for="item in postDescData"
            :key="item.jobCode"
            @click="postInfo(item.jobCode)"
          >
            <div class="title">{{ item.jobName }}</div>
            <!-- <div class="title org_name">{{ item.orgName }}</div> -->
            <div class="text">职位说明书</div>
          </div>
        </div>
        <coustomPagination class="pagination_dom" :total="total" @pageChange="pageChange"></coustomPagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getJobClassTree, queryJobDescription } from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import coustomPagination from '@/components/talent/paginationComps/coustomPagination'

const treeData = ref([])
const jobClassCode = ref('')
const postDescData = ref([])
const current = ref(1)
const size = ref(10)
const total = ref(0)
const router = useRouter()

function getJobClassTreeFun() {
  getJobClassTree({}).then(res => {
    if (res.length > 0) {
      treeData.value = res
    } else {
      treeData.value = []
    }
  })
}
function clickCallback(val, isLastNode) {
  jobClassCode.value = val
  getJobDescListFun()
}
function getJobDescListFun() {
  queryJobDescription({
    current: current.value,
    size: size.value,
    jobClassCode: jobClassCode.value
  }).then(res => {
    postDescData.value = []
    if (res.code == 200) {
      if (res.data.length > 0) {
        postDescData.value = res.data.map(item => {
          return {
            jobName: item.job_name,
            jobCode: item.job_code
            // orgName: item.orgName,
          }
        })
        total.value = res.total
      }
    }
  })
}
function postInfo(code) {
  router.push({
    path: '/basicSettingHome/postManagement/postDescription/postDescriptionDetailed',
    query: { jobCode: code }
  })
}
function pageChange(newSize, newCurrent) {
  size.value = newSize
  current.value = newCurrent
  getJobDescListFun()
}
onMounted(() => {
  getJobClassTreeFun()
  getJobDescListFun()
})
</script>

<style scoped lang="scss">
.page_section_main {
  position: relative;
  padding-bottom: 60px;
  .pagination_dom {
    position: absolute;
    bottom: 10px;
    right: 10px;
  }
}
.post_description_item {
  position: relative;
  width: 120px;
  height: 150px;
  padding: 15px 0;
  box-shadow: 2px 2px 8px #ccc;
  margin: 0 15px 10px;
  cursor: pointer;
  /*background: #f8f8f8;*/
  .title {
    /*height: 40px;*/
    font-size: 12px;
    color: #666;
    line-height: 20px;
    padding: 0 8px;
  }
  .org_name {
    color: #515e6c;
    font-weight: bold;
  }
  .text {
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 20px;
    font-size: 12px;
    height: 30px;
    line-height: 30px;
    color: #0099ff;
    background: #ebf4ff;
    padding: 0 5px;
    text-align: center;
  }
}
</style>
