<template>
    <div class="report_wrap">
        <div class="page_main_title">
            {{ enqName }}--盘点报告(部门报告) {{ beginDate | removeTime }}
            <div class="goback_geader" @click="goback">
                <i class="el-icon-arrow-left"></i>返回
            </div>
            <el-button
                type="primary"
                class="marginL_16"
                size="mini"
                @click="downloadPdf"
                >导出PDF</el-button
            >
        </div>
        <div class="page_section">
            <div class="page_section_center clearfix">
                <div class="report_header_info">
                    <span class="name">{{ orgInfo.orgName }}</span>
                    <span class>
                        岗位:
                        <span>{{ orgInfo.postCount }}</span>
                    </span>
                    <span class="marginR_16 marginL_16">|</span>
                    <span class>
                        人员:
                        {{ orgInfo.userCount }}
                    </span>
                </div>
                <div class="step_bar">
                    <step-bar
                        :stepData="stepData"
                        :needClick="true"
                        @stepClick="stepClick"
                        :currentIndex="currentModuleCode"
                    ></step-bar>
                </div>
                <keep-alive>
                    <component
                        class
                        :is="moduleObj[currentModuleCode]"
                        :enqId="enqId"
                        :orgCode="orgCode"
                    ></component>
                </keep-alive>
                <div class="oper_btn_wrap">
                    <el-button
                        class="page_confirm_btn"
                        type="primary"
                        @click="prevStep()"
                        v-if="this.currentIndex > 0"
                        >上一页</el-button
                    >
                    <el-button
                        class="page_confirm_btn"
                        type="primary"
                        @click="goback"
                        v-if="this.currentIndex == this.stepData.length - 1"
                        >返回报告列表</el-button
                    >
                    <el-button
                        class="page_confirm_btn"
                        type="primary"
                        @click="nextStep()"
                        v-else
                        >下一页</el-button
                    >
                </div>
            </div>
        </div>
        <org-report-to-pdf
            class="report_pdf_dom"
            :class="{ show: showPdf }"
            :moduleCodeArr="moduleArr"
        ></org-report-to-pdf>
    </div>
</template>
 
<script>
    // 获取部门报告模块文件
    const files = require.context("./orgReportComps", false, /\.vue$/);
    let compMap = {
        // DN01(对应模块code):files(报告模块对应的前端文件)
    };
    for (const fileName of files.keys()) {
        let keyStr = fileName.split("/")[1].split(".")[0].split("_")[0];
        compMap[keyStr] = files(fileName).default;
    }
    import {
        getOrgReportOrgInfo,
        getEnqModuleInfo,
        getEnqInfo,
    } from "../../../request/api";
    import stepBar from "@/components/talent/stepsComps/stepBar";
    import OrgReportToPdf from "./orgReportToPdf.vue";

    export default {
        name: "orgReport",
        components: { stepBar, OrgReportToPdf },
        data() {
            return {
                showPdf: false,
                pdfDomIdArr:['pdf_cover'],
                enqName: "",
                beginDate: "",
                orgInfo: {
                    orgName: "",
                    postNum: "",
                    staffNum: "",
                },
                enqId: null,
                orgCode: null,
                viewType: "p",
                moduleObj: compMap,
                moduleArr: [], //按顺序储存盘点模块的code
                currentModuleCode: null, //当前显示的模块code
                currentIndex: 0, //用来改变当前显示的模块code，从moduleArr中获取
                nextBtnText: "下一步",
                stepData: [
                    // {
                    //     name: "基本信息",
                    //     code: "D01_basicInfo",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "数量与结构",
                    //     code: "orgRStructure",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "任职匹配",
                    //     code: "orgRJobMatch",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "工作网络",
                    //     code: "orgRWorkNet",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "素质评价",
                    //     code: "orgRQualityEval",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "业绩评价",
                    //     code: "orgRPerformanceEval",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "目标与结果",
                    //     code: "orgRTargetRes",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "KPI评价",
                    //     code: "orgRKPIEval",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "人员潜力",
                    //     code: "orgRPotentials",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "人员晋升",
                    //     code: "orgRPromotion",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "人才区分",
                    //     code: "orgRDistinction",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "离职风险",
                    //     code: "orgRRisk",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "人才继任",
                    //     code: "orgRSuccession",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "人才发展",
                    //     code: "orgRDeve",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "培训计划",
                    //     code: "orgRTrain",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "职位招募",
                    //     code: "orgROrg",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "敬业度",
                    //     code: "orgRDedicat",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "工作驱动",
                    //     code: "workDriven",
                    //     enqProgress: "Y",
                    // },
                    // old
                    // {
                    //     name: "人员晋升",
                    //     code: "orgRPromotion",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "人才区分",
                    //     code: "orgRDistinction",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "人才风险",
                    //     code: "orgRRisk",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "人才发展",
                    //     code: "orgRDeve",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "人才编制",
                    //     code: "orgROrg",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "培训计划",
                    //     code: "orgRTrain",
                    //     enqProgress: "Y",
                    // },
                    // {
                    //     name: "敬业度分析",
                    //     code: "orgRDedicat",
                    //     enqProgress: "Y",
                    // },
                ],
            };
        },
        created() {
            this.init();
        },
        methods: {
            init() {
                let query = this.$route.query;
                this.enqId = query.enqId;
                this.orgCode = query.orgCode;
                this.viewType = query.viewType;
                this.getOrgModuleFun();
                this.getOrgReportOrgInfoFun();
                this.getEnqInfoFn();
            },
            goback() {
                if (this.viewType == "p") {
                    this.$router.go(-1);
                } else {
                    this.$router.push({
                        path: "/talentReviewHome/talentReviewAnalysis/TRreport/listView",
                        query: {
                            useCache: true,
                        },
                    });
                }
            },
            stepClick(stepCode, index) {
                this.currentIndex = index;
                this.currentModuleCode = stepCode;
            },
            getOrgReportOrgInfoFun() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getOrgReportOrgInfo(params).then((res) => {
                    if (res.code == "200") {
                        this.orgInfo = res.data;
                    }
                });
            },
            getOrgModuleFun() {
                getEnqModuleInfo({ enqId: this.enqId, type: "D" }).then((res) => {
                    if (res.code == "200") {
                        let arr = [];
                        res.data.forEach((item) => {
                            arr.push({
                                name: item.reportModuleName,
                                code: item.reportModuleCode,
                                enqProgress: "Y",
                            });
                            this.moduleArr.push(item.reportModuleCode);
                            this.pdfDomIdArr.push("pdf_module_"+item.reportModuleCode)
                        });
                        this.currentModuleCode = this.moduleArr[this.currentIndex];
                        this.stepData = arr;
                        // this.currentModuleCode = this.stepData[0].code;
                    }
                });
            },
            getEnqInfoFn() {
                getEnqInfo({ id: this.enqId }).then((res) => {
                    this.enqName = res.data.enqName;
                    this.beginDate = res.data.beginDate;
                });
            },
            nextStep() {
                if (this.currentIndex == this.stepData.length - 1) {
                    // 最后一步提交
                    return false;
                }
                this.stepData[this.currentIndex].enqProgress = "Y";
                this.currentIndex++;
                // 设置按钮文本 “下一步” or "返回报告列表"
                if (this.currentIndex == this.stepData.length - 1) {
                    this.nextBtnText = "返回报告列表";
                } else {
                    this.nextBtnText = "下一步";
                }
                this.currentModuleCode = this.stepData[this.currentIndex].code;
            },
            prevStep() {
                if (this.currentIndex == 0) {
                    return false;
                }
                this.currentIndex--;
                this.currentModuleCode = this.stepData[this.currentIndex].code;
            },
            downloadPdf() {
                let that = this;
                this.showPdf = true;
                this.$loading({ fullscreen: true });
                setTimeout(() => {
                    // 多页 && 按模块分页
                    // this.toPdf(
                    //     this.pdfDomIdArr,
                    //     this.enqName +
                    //         "--部门盘点报告(" +
                    //         this.orgInfo.orgName +
                    //         ")",
                    //     true,
                    //     function () {
                    //         that.showPdf = false;
                    //         that.$loading().close();
                    //     }
                    // );


                    // 单页、多页pdf生成
                    // this.getPDF({
                    //     domId:'testPdf',
                    //     title:this.enqName +
                    //         "--部门盘点报告(" +
                    //         this.orgInfo.orgName +
                    //         ")",
                    //     singlepage: false,
                    //     callback:function () {
                    //         that.showPdf = false;
                    //         that.$loading().close();
                    //     },
                    //     err:(err)=>{
                    //         console.log(err);
                    //     }
                    // });
                    this.getPDF({
                        domId:this.pdfDomIdArr,
                        title:this.enqName +
                            "--部门盘点报告(" +
                            this.orgInfo.orgName +
                            ")",
                        singlepage: false,
                        moduleCut: true,
                        hasCover:true,
                        callback:function () {
                            that.showPdf = false;
                            that.$loading().close();
                        },
                        err:(err)=>{
                            console.log(err);
                        }
                    });
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .report_wrap {
        position: relative;
    }
    .page_second_title {
        position: relative;
        margin-bottom: 20px;
        line-height: 34px;
        color: #0099ff;
        z-index: 2;
        font-weight: bold;
        &::after{
            content:'';
            position: absolute;
            left: 0;
            top:0;
            height: 100%;
            width: 500px;
             background: #e5f7fd;
            z-index: -1;

        }

    }
    .report_header_info {
        color: #212121;
        font-size: 14px;
        margin-bottom: 30px;
        .name {
            color: #0099FF;
            font-size: 16px;
            margin-right: 14px;
            font-weight: bold;
        }
    }
    .step_bar {
        margin-bottom: 24px;
    }
    .oper_btn_wrap {
        text-align: center;
        padding-top: 16px;
    }
    // 去除select下拉箭头
    .el-input__suffix {
        display: none;
    }
    .el-col {
        margin-bottom: 20px;
    }
    .item_title {
        line-height: 24px;
        color: #0099ff;
        background-color: #e5f7fd;
        font-size: 14px;
        padding-left: 16px;
        margin-bottom: 5px;
    }
    .chart_box {
        min-height: 300px;
        // height: 300px;
        border: 1px solid #dcdfe6;
        text-align: center;
    }
    .report_pdf_dom {
        width: 100vw;
        position: fixed;
        top: 0;
        left: 0;
        z-index: -1;
        opacity: 0;
        background: #fff;
        &.show {
            opacity: 1;
        }
    }
</style>