<template>
    <div class="project_view_main">
        <div class="talent_review_report">
            <reportProjectViewList :listData="listData"></reportProjectViewList>
        </div>
        <coustomPagination :total="total" @pageChange="pageChange"></coustomPagination>
    </div>
</template>
 
<script>
import {pageProject} from "../../../request/api"
import reportProjectViewList from './reportProjectViewList'
import coustomPagination from '@/components/talent/paginationComps/coustomPagination'
export default {
    name: "reportProjectView",
    props: [],
    components: {
        reportProjectViewList,
        coustomPagination
    },
    data() {
        return {
            total:0,
            currentPage:1,
            pageSize:10,
            listData: []
        };
    },
    created(){
        this.pageProjectFun();
    },
    mounted() {},
    methods: {
        pageProjectFun(){
            let params = {
                current:this.currentPage,
                size:this.pageSize
            };
            pageProject(params).then(res => {
                console.log(res);
                if(res.code == "200"){
                    this.listData = res.data;
                    this.total = res.total;
                }
            })
        },
        pageChange(size,currentPage){
            this.pageSize = size;
            this.currentPage = currentPage;
            this.pageProjectFun();
        }
    }
};
</script>
 
<style scoped lang="scss">
.project_view_main{
    padding-top:32px;
}
</style>