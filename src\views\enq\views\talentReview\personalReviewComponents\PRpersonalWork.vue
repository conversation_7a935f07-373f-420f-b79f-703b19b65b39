<template>
  <div class="edu_info_wrap performance_info_main">
    <div class="clearfix">
      <div class="page_second_title marginT_16">
        <span>个人工作</span>
      </div>
      <!-- <div class="align_right">
        <el-button class="page_add_btn" type="primary" @click="addItem">新增</el-button>
      </div>-->
      <div class="edu_info_center marginT_16">
        <div class="edu_info_header bgc">
          <div class="item">
            <div class="total">
              <div class="item_divs">月度工时预估</div>
              <div class="item_divs time_total">{{ monthTime }}H</div>
            </div>
          </div>
          <div class="item"></div>
          <div class="item"></div>
          <div class="item"></div>
          <div class="item item_icon_wrap">
            <el-button class="page_add_btn" type="primary" @click="addItem">新增</el-button>
          </div>
        </div>
        <div class="edu_info_header">
          <div class="item">工作内容</div>
          <div class="item">
            <div class="item_div">工作频率</div>
            <div class="item_div time">
              <div>日</div>
              <div>周</div>
              <div>月</div>
            </div>
          </div>
          <div class="item">工作次数</div>
          <div class="item">单次工作时长(小时)</div>
          <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
          <PRpersonalWorkList :trainData="trainData" v-on:deleteItem="deleteItem"></PRpersonalWorkList>
          <div class="align_right" style="margin-top: 10px">
            <el-button class="page_add_btn" type="primary" @click="saveData()">保存</el-button>
          </div>
          <div class="align_center marginT_30">
            <el-button
              class="page_confirm_btn"
              type="primary"
              @click="prevStep"
              v-show="currentIndex != currentFirstCode"
              >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="submit('nextStep')">{{ nextBtnText }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { getWorkContentList, saveWorkContent, delEnqUserWorking } from '../../../request/api'
import PRpersonalWorkList from './PRpersonalWorkList.vue'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])
const userStore = useUserStore()

const submitFlag = ref(true)
const trainData = ref([])
const monthTime = ref('')
const flag = ref(true)

const userId = computed(() => userStore.userInfo.userId)

onMounted(() => {
  getWorkContentListFun()
})

function saveData(stepType) {
  if (!submitFlag.value) return
  if (checkData(trainData.value)) {
    ElMessage.warning('请完善数据后提交！')
    return
  }
  submitFlag.value = false
  let params = {
    enqId: props.enqId,
    userWorkingList: trainData.value
  }
  saveWorkContent(params).then(res => {
    if (res.code == '200') {
      ElMessage.success('保存成功!')
      if (stepType) {
        emit(stepType)
      } else {
        getWorkContentListFun()
      }
      submitFlag.value = true
    } else {
      submitFlag.value = true
      ElMessage.error(res.msg)
    }
  })
}

function deleteItem(item, index) {
  if (!Object.prototype.hasOwnProperty.call(item, 'workingId')) {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      type: 'warning'
    })
      .then(() => {
        trainData.value.splice(index, 1)
        ElMessage.success('删除成功!')
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  } else {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      type: 'warning'
    })
      .then(() => {
        delEnqUserWorking({
          workingId: item.workingId,
          enqId: props.enqId
        }).then(res => {
          if (res.code == '200') {
            ElMessage.success('删除成功!')
            trainData.value.splice(index, 1)
            getWorkContentListFun()
          } else {
            ElMessage.error('删除失败！')
          }
        })
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  }
}

function addItem() {
  let obj = trainData.value[trainData.value.length - 1]
  let addObj = {
    workingContent: '',
    workingFreq: 'D',
    workingCount: '',
    workingDuration: ''
  }
  if (!obj) {
    trainData.value.push(addObj)
    return
  }
  if (checkData(trainData.value)) {
    ElMessage.warning('请完善当前数据后新增！')
    return
  }
  trainData.value.push(addObj)
}

function submit(stepType) {
  if (trainData.value && trainData.value.length == 0) {
    ElMessage.error('请先填写个人工作内容!')
    return
  } else {
    if (checkData(trainData.value)) {
      ElMessage.warning('请完善数据后提交！')
      return
    } else {
      let hasSaved = false
      for (const item of trainData.value) {
        if (Object.prototype.hasOwnProperty.call(item, 'workingId')) {
          hasSaved = true
          emit(stepType)
          flag.value = false
          break
        }
      }
      if (!hasSaved) {
        ElMessage.error('请先保存个人工作内容!')
      }
    }
  }
}

function getWorkContentListFun() {
  getWorkContentList({
    enqId: props.enqId
  }).then(res => {
    if (res.code == '200') {
      trainData.value = res.data.enqUserWorkingList
      monthTime.value = res.data.workingHoursM
    } else {
      ElMessage.error('获取数据失败!')
    }
  })
}

function checkData(data) {
  for (let index = 0; index < data.length; index++) {
    const obj = data[index]
    if (!obj.workingContent || !obj.workingFreq || !obj.workingCount || !obj.workingDuration) {
      return true
    }
  }
  return false
}

function prevStep() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      saveData('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}
</script>

<style scoped lang="scss">
.performance_info_main {
  /*width: 60%;*/
}

.edu_info_wrap {
  margin-bottom: 16px;
}
.bgc {
  background-color: #fff;
  // height: 60px;
  margin-bottom: 10px;
  padding: 0;
}
.align_right {
  padding: 0 15px;
}
.edu_info_header {
  margin-bottom: 10px;
  .item {
    // flex: 1;
    width: 25%;
    padding: 0;
    .total {
      border: 1px solid #ccc;
      padding: 0 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .item_div {
        // height: 50%;
        // height: 30px;
        // line-height: 30px;
      }
      .time_total {
        color: #449cff;
        font-weight: bold;
      }
    }
    .item_div {
      height: 50%;
      height: 22.5px;
      line-height: 22.5px;
    }
    .time {
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
  }
  .item_icon_wrap {
    text-align: center;
    width: 10%;
  }
}
</style>
