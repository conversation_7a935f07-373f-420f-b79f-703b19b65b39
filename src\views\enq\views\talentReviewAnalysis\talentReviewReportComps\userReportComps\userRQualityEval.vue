<template>
    <div class="report_section userR_quality_eval_wrap">
        <div class="page_second_title">综合素质表现</div>
        <div class="flex_row_around marginB_32">
            <li class="annulus_item">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveQualityScore.qualityOverallScore"
                />
                <p>综合得分</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveQualityScore.S != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveQualityScore.S"
                />
                <p>自评</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveQualityScore.P != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveQualityScore.P"
                />
                <p>同级</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveQualityScore.U != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveQualityScore.U"
                />
                <p>上级</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveQualityScore.B != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveQualityScore.B"
                />
                <p>下级</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveQualityScore.O != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveQualityScore.O"
                />
                <p>协同</p>
            </li>
            <li class="annulus_item" v-if="comprehensiveQualityScore.L != null">
                <customProcess
                    :size="100"
                    :fontSize="20"
                    :strokeWidth="15"
                    :num="comprehensiveQualityScore.L"
                />
                <p>分管领导</p>
            </li>
            <li class="last_annulus_item">
                <span>
                    {{ comprehensiveQualityScore.actualQualityGrade }}
                </span>
            </li>
        </div>
        <div class="flex_row_start marginB_32">
            <div class="ranking_wrap">
                <div class="page_third_title">
                    <span>综合素质排名</span>
                </div>
                <ul class="ranking_main">
                    <li class="item_wrap">
                        <p class="title">整体排名</p>
                        <p class="number">
                            <span class="weight">{{
                                comprehensiveQualityRanking.wholeRanking
                            }}</span
                            >/{{ comprehensiveQualityRanking.whole }}
                        </p>
                    </li>
                    <li class="item_wrap">
                        <p class="title">本部门排名</p>
                        <p class="number">
                            <span class="weight">{{
                                comprehensiveQualityRanking.orgRanking
                            }}</span
                            >/{{ comprehensiveQualityRanking.org }}
                        </p>
                    </li>
                    <li class="item_wrap">
                        <p class="title">本职位排名</p>
                        <p class="number">
                            <span class="weight">{{
                                comprehensiveQualityRanking.jobRanking
                            }}</span
                            >/{{ comprehensiveQualityRanking.job }}
                        </p>
                    </li>
                </ul>
            </div>
            <div class="job_score">
                <div class="page_third_title">
                    <span>同职位360°评分对比</span>
                </div>
                <div class="chart_box factor" :id="jobScoreComparisonId"></div>
            </div>
        </div>
        <div class="marginB_32">
            <div class="page_third_title">
                <span>综合素质得分对比</span>
            </div>
            <div class="report_section_content">
                <div
                    class="chart_box factor"
                    :id="qualityScoreComparisonId"
                ></div>
            </div>
        </div>
        <!-- <div class="bottom_wrap marginB_32">
            <div class="page_third_title">
                <span>素质评价上级综合评语</span>
            </div>
            <div
                class="superior_comments"
                v-for="(item, index) in comprehensiveComments.u"
                :key="index"
            >
                {{ item }}
            </div>
        </div> -->
        <!-- <div class="bottom_wrap">
            <div class="page_third_title">
                <span>其它评语</span>
            </div>
            <div class="remark_info">
                <ul>
                    <li class="flex_row_start">
                        <p class="name">同级</p>
                        <div class="right">
                            <p
                                class="info overflow_elps"
                                v-for="(item, index) in comprehensiveComments.p"
                                :key="index"
                            >
                                {{ item }}
                            </p>
                        </div>
                    </li>
                    <li class="flex_row_start">
                        <p class="name">下级</p>
                        <div class="right">
                            <p
                                class="info overflow_elps"
                                v-for="(item, index) in comprehensiveComments.b"
                                :key="index"
                            >
                                {{ item }}
                            </p>
                        </div>
                    </li>
                    <li class="flex_row_start">
                        <p class="name">分管领导</p>
                        <div class="right">
                            <p
                                class="info overflow_elps"
                                v-for="(item, index) in comprehensiveComments.l"
                                :key="index"
                            >
                                {{ item }}
                            </p>
                        </div>
                    </li>
                    <li class="flex_row_start">
                        <p class="name">协同</p>
                        <div class="right">
                            <p
                                class="info overflow_elps"
                                v-for="(item, index) in comprehensiveComments.o"
                                :key="index"
                            >
                                {{ item }}
                            </p>
                        </div>
                    </li>
                </ul>
            </div>
        </div> -->
        <div class="bottom_wrap">
            <div class="page_third_title">
                <span>综合素质具体表现</span>
            </div>
            <div>
                <tableComponent
                    :tableData="tableData"
                    :border="true"
                    :needPagination="false"
                    :needIndex="false"
                    :overflowTooltip="!isPdf"
                ></tableComponent>
            </div>
        </div>
    </div>
</template>

<script>
    // 素质评价
    import customProcess from "@/components/talent/common/customProcess.vue";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import { getQualityEvalData } from "../../../../request/api.js";
    export default {
        name: "userRQualityEval",
        props: ["nextBtnText", "enqId", "userId", "postCode", "orgCode","isPdf"],
        components: {
            tableComponent,
            customProcess,
        },
        data() {
            return {
                comprehensiveQualityScore: "",
                comprehensiveQualityRanking: "",
                jobScoreComparisonId: this.$util.createRandomId(),
                jobScoreComparison: {
                    data: [],
                    legend: [],
                },
                qualityScoreComparisonId: this.$util.createRandomId(),
                qualityScoreComparison: {
                    data: [],
                    legend: [],
                },
                comprehensiveComments: "",

                tableData: {
                    columns: [
                        {
                            label: "评价维度",
                            prop: "name",
                            width: "200",
                        },
                        {
                            label: "评估内容",
                            prop: "content",
                            width: "200",
                        },
                        {
                            label: "综合得分",
                            prop: "actualScore",
                            width: "100",
                        },
                        {
                            label: "综合表现",
                            prop: "performance",
                            width: "",
                        },
                    ],
                    data: [],
                },
            };
        },
        created() {},
        computed: {},
        mounted() {
            this.getQualityEvalDataFun();
        },
        methods: {
            setItemText(value) {
                return () => {
                    return value;
                };
            },
            getQualityEvalDataFun() {
                getQualityEvalData({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    if (res.code == 200) {
                        // 综合素质得分
                        this.comprehensiveQualityScore =
                            res.data.comprehensiveQualityScore;
                        // 同职位360°评分对比
                        this.jobScoreComparison.data =
                            res.data.jobScoreComparison.chartData;
                        this.jobScoreComparison.legend =
                            res.data.jobScoreComparison.legend;
                        echartsRenderPage(
                            this.jobScoreComparisonId, //id
                            "XBar", // 图表类型
                            null, //宽
                            "400", //高
                            this.jobScoreComparison //图表数据
                        );
                        // 综合素质排名
                        this.comprehensiveQualityRanking =
                            res.data.comprehensiveQualityRanking;
                        // 综合素质得分对比
                        this.qualityScoreComparison.data =
                            res.data.qualityScoreComparison.chartData;
                        this.qualityScoreComparison.legend =
                            res.data.qualityScoreComparison.legend;
                        echartsRenderPage(
                            this.qualityScoreComparisonId, //id
                            "XBar", // 图表类型
                            null, //宽
                            "400", //高
                            this.qualityScoreComparison //图表数据
                        );
                        //综合素质具体表现
                        this.tableData.data = res.data.qualitySpecificPerformance;
                        // 上级综合评语
                        // 其它评语
                        this.comprehensiveComments = res.data.comprehensiveComments;
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .score_classfiy {
        margin-bottom: 32px;
    }
    .annulus_item {
        text-align: center;
        p {
            color: #0099ff;
            font-weight: 600;
        }
    }
    .last_annulus_item {
		display: flex;
		align-items: center;
        height: 100px;
        width: 100px;
        text-align: center;
        color: #008fff;
        background: #dae8fd;
        border-radius: 50%;
        padding: 0 10px;
    }
    .ranking_wrap {
        width: 400px;
        .ranking_main {
            padding-left: 16px;
            .item_wrap {
                padding: 0 20px 0 10px;
                line-height: 32px;
                background: #dae8fd;
                text-align: center;
                color: #008fff;
                margin-bottom: 10px;
                display: flex;
                justify-content: space-between;
                .title {
                    font-weight: 600;
                }
                .number {
                    font-size: 12px;
                    .weight {
                        font-size: 14px;
                        font-weight: 600;
                    }
                }
            }
        }
    }
    .job_score {
        width: 1100px;
        margin-left: 30px;
    }
    .superior_comments {
        line-height: 25px;
        padding-left: 16px;
    }
    .remark_info {
        ul {
            li {
                line-height: 25px;
                margin: 0 0 15px 0;
                .name {
                    width: 80px;
                    height: 25px;
                    text-align: center;
                    border: 1px solid #0099ff;
                    color: #0099ff;
                    font-weight: 600;
                    margin: 0 15px 0 0;
                }
                .info {
                    flex: 1;
                    max-width: 1040px;
                }
            }
        }
    }
    // .userR_quality_eval_wrap {
    //     padding: 0 10px;
    //     height: 480px;
    //     overflow: auto;
    //     pointer-events: auto;
    //     .userR_quality_eval_main {
    //         .top_wrap {
    //             .top_left_wrap {
    //                 // width: 590px;
    //                 .top_left_main {
    //                     padding: 25px 20px 0 0px;
    //                     .annulus_item {
    //                         text-align: center;
    //                         p {
    //                             margin: 10px 0 0 0;
    //                         }
    //                         .el-progress {
    //                             .el-progress__text {
    //                                 margin: 0 0 0 15%;
    //                                 width: 42px;
    //                                 height: 42px;
    //                                 line-height: 45px;
    //                                 background: #dae8fd;
    //                                 border-radius: 50%;
    //                             }
    //                         }
    //                     }
    //                     .last_annulus_item {
    //                         width: 100px;
    //                         height: 80px;
    //                         line-height: 80px;
    //                         text-align: center;
    //                         color: #008fff;
    //                         background: #dae8fd;
    //                         border-radius: 8%;
    //                     }
    //                 }
    //             }
    //             .top_right_wrap {
    //                 flex: 1;
    //                 .report_section_content {
    //                     padding: 10px 0 0 0;
    //                     height: 300px;
    //                     border: 1px solid #dcdfe6;
    //                     .chart_box {
    //                         width: 100%;
    //                     }
    //                 }
    //             }
    //         }
    //         .score_chart_wrap {
    //             .page_second_title {
    //                 margin: 15px 0;
    //             }
    //             .report_section_content {
    //                 border: 1px solid #dcdfe6;
    //                 width: 100%;
    //                 height: 300px;
    //             }
    //         }
    //         .bottom_wrap {
    //             .page_second_title {
    //                 margin: 15px 0;
    //             }
    //             .remark_info {
    //                 ul {
    //                     li {
    //                         line-height: 25px;
    //                         margin: 0 0 15px 0;
    //                         .name {
    //                             width: 80px;
    //                             height: 25px;
    //                             text-align: center;
    //                             border: 1px solid #0099ff;
    //                             color: #0099ff;
    //                             font-weight: 600;
    //                             margin: 0 15px 0 0;
    //                         }
    //                         .info {
    //                             flex: 1;
    //                             max-width: 1040px;
    //                         }
    //                     }
    //                 }
    //             }
    //             .superior_comments {
    //                 line-height: 25px;
    //             }
    //         }
    //     }
    // }
</style>
