<template>
  <div class="basic_info_main">
    <div class="page_second_title">基本信息</div>
    <div class="basic_info_content">
      <div class="basic_info_item">
        <div class="title">职位名称</div>
        <div class="text">{{ jobName }}</div>
      </div>
      <div class="basic_info_item">
        <div class="title">职位族群</div>
        <div class="text">{{ parentJobClassName }}</div>
      </div>
      <div class="basic_info_item">
        <div class="title">职位序列</div>
        <div class="text">{{ jobClassName }}</div>
      </div>
      <div class="basic_info_item">
        <div class="title">编制人数</div>
        <div class="text">{{ budgetedCount }}</div>
      </div>
      <div class="basic_info_item">
        <div class="title">职位描述</div>
        <div class="text">{{ jobDesc }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { jobInfoByCode } from '../../../../request/api'

const props = defineProps({
  jobCode: String
})

const jobName = ref('')
const parentJobClassName = ref('')
const jobClassName = ref('')
const budgetedCount = ref('')
const jobDesc = ref('')

async function jobInfoByCodeFun() {
  if (!props.jobCode) return

  const res = await jobInfoByCode({
    jobCode: props.jobCode
  })

  if (res.code == 200) {
    jobName.value = res.data.jobName
    parentJobClassName.value = res.data.parentJobClassName
    jobClassName.value = res.data.jobClassName
    budgetedCount.value = res.data.budgetedCount
    jobDesc.value = res.data.jobDesc
  }
}

onMounted(jobInfoByCodeFun)
watch(() => props.jobCode, jobInfoByCodeFun)
</script>

<style scoped lang="scss">
.page_second_title {
  margin: 10px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
}
.basic_info_content {
  .basic_info_item {
    padding: 0 16px;
    margin-bottom: 10px;
    .title {
      line-height: 30px;
      color: #0099ff;
    }
    .text {
      line-height: 1.5;
      color: #777;
    }
  }
}
</style>
