<template>
	<div class="report_section userR_talent_wrap">
		<div class="userR_talent_main">
			<div class="content_item">
				<div class="page_second_title">
					<span>能力业绩分类</span>
				</div>
				<div class="top_wrap flex_row_betweens">
					<div class="type_wrap flex_row_start">
						<div class="sign">能力</div>
						<ul class="ordinate_wrap">
							<li>高</li>
							<li>中</li>
							<li>低</li>
							<li></li>
						</ul>
						<div class="content">
							<ul class="line_item_wrap flex_row_start">
								<li class="gray">待发展人才</li>
								<li class="cyan">明日之星</li>
								<li class="black_blue">明星人才</li>
							</ul>
							<ul class="flex_row_start">
								<li class="yellow">差距员工</li>
								<li class="blue">中坚力量</li>
								<li class="dark_blue">绩效之星</li>
							</ul>
							<ul class="flex_row_start">
								<li class="orange">待优化人才</li>
								<li class="indigo">稳定人才</li>
								<li class="azure">专业人才</li>
							</ul>
							<ul class="abscissa_wrap flex_row_start">
								<li class="">低</li>
								<li>中</li>
								<li>高</li>
							</ul>
							<div class="abscissa_sign">业绩</div>
						</div>
					</div>
					<div class="desc_info_wrap">
						<p class="title">{{capabilityPerformance.codeName}} </p>
						<div class="desc">
							{{capabilityPerformance.codeDesc}}
						</div>
					</div>
				</div>
			</div>
			<div class="content_item">
				<div class="page_second_title">
					<span>能力潜力分类</span>
				</div>
				<div class="top_wrap flex_row_betweens">
					<div class="type_wrap flex_row_start">
						<div class="sign">能力</div>
						<ul class="ordinate_wrap">
							<li>高</li>
							<li>中</li>
							<li>低</li>
							<li></li>
						</ul>
						<div class="content">
							<ul class="line_item_wrap flex_row_start">
								<li class="gray">有较大的潜力的人才</li>
								<li class="cyan">有巨大潜力的人才</li>
								<li class="black_blue">潜力绩效俱佳的优秀员工</li>
							</ul>
							<ul class="flex_row_start">
								<li class="yellow">有一定潜力的绩差员工</li>
								<li class="blue">有一定潜力的普通员工</li>
								<li class="dark_blue">有一定潜力的优秀员工</li>
							</ul>
							<ul class="flex_row_start">
								<li class="orange">潜力绩效皆差的员工</li>
								<li class="indigo">潜力较差的员工</li>
								<li class="azure">潜力用尽的优秀员工</li>
							</ul>
							<ul class="abscissa_wrap flex_row_start">
								<li class="">低</li>
								<li>中</li>
								<li>高</li>
							</ul>
							<div class="abscissa_sign">业绩</div>
						</div>
					</div>
					<div class="desc_info_wrap">
						<p class="title">{{capabilityPotential.codeName}}</p>
						<div class="desc">
							{{capabilityPotential.codeDesc}}
						</div>
					</div>
				</div>
			</div>
			<div class="content_item">
				<div class="page_second_title">
					<span>个人优势</span>
				</div>
				<div class="desc_info">
					{{strength}}
				</div>
			</div>
			<div class="content_item">
				<div class="page_second_title">
					<span>待提升的能力</span>
				</div>
				<div class="desc_info">
					{{weakness}}
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	// 人才分类
	import {getTalentClassification} from "../../../../request/api"
    import tableComponent from "@/components/talent/tableComps/tableComponent";
	import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
	export default {
		name: "userRTalent",
		props: ["nextBtnText", "enqId", "userId", "postCode"],
		components: {
			tableComponent
		},
		data() {
			return {
				capabilityPerformance:'',
				capabilityPotential:'',
				strength:'',
				weakness:'',
			};
		},
		created() {
			
		},
		computed:{
			
		},
		mounted(){
			this.getTalentClassificationFun()
		},
		methods: {
			setItemText(value){
				return ()=>{
					return value
				}
			},
			getTalentClassificationFun(){
				getTalentClassification({
					enqId:this.enqId,
					userId:this.userId
				}).then(res=>{
					if(res.code == 200){
						this.capabilityPerformance = res.data.capabilityPerformance
						this.capabilityPotential = res.data.capabilityPotential
						this.strength = res.data.strength
						this.weakness = res.data.weakness
					}
				})
			}
		}
	};
</script>
<style scoped lang="scss">
.userR_talent_wrap {
	padding: 0 10px;
	height: 480px;
	overflow:auto ;	
	pointer-events: auto;
	.userR_talent_main{
		.page_second_title{
			margin: 20px 0 15px;
		}
		.top_wrap{
			.type_wrap{
				width: 49%;
				.sign{
					margin: 0 10px;
					padding: 45px 0 0 0;
					width: 20px;
					height: 100%;
					color: #008fff;
				}
				ul{
					li{
						height: 40px;
						line-height: 40px;
						text-align: center
					}
				}
				.ordinate_wrap{
					li{
						width: 40px;
						background: #dae8fd;
					}
				}
				.content{
					li{
						width: 150px;
						color: #fff;
					}
					.gray{
						background:#85868a;
					}
					.blue{
						background: #a3d0f3;
					}
					.cyan{
						background: #557db1;
					}
					.black_blue{
						background: #082953;
					}
					.dark_blue{
						background: #4182d8;
					}
					.indigo{
						background: #346892;
					}
					.orange{
						background: #eb8373;
					}
					.azure{
						background: #0f3970;
					}
					.yellow{
						background: #e6a23c;
					}
					.abscissa_wrap{
						li{
							color: #008fff;
							background: #dae8fd;
						}
					}
					.abscissa_sign{
						width: 100%;
						height: 40px;
						line-height: 40px;
						color: #008fff;
						text-align: center;
					}
				}
			}
		}
		.desc_info_wrap{
			width: 49%;
			.title{
				height: 40px;
				line-height: 40px;
				color: #008fff;
			}
			.desc{
				line-height: 25px;
			}
		}
		.content_item{
			.desc_info{
				line-height: 25px;
			}
		}
	}
}
</style>