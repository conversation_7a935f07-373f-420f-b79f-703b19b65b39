<template>
    <div class="report_section userR_self_plan_wrap">
        <div class="page_second_title">
            <span>个人规划</span>
        </div>
        <div class="page_third_title">人员综合表现</div>
        <div class="annulus_wrap flex_row_around">
            <li class="annulus_item" v-if="infoData.qualityOverallScore != null">
                <p class="info">
                    <customProcess
                        :size="150"
                        :strokeWidth="20"
                        :num="infoData.qualityOverallScore"
                    />
                </p>
                <p class="type">核心素质</p>
            </li>
            <li class="annulus_item" v-if="infoData.performOverallScore!=null">
                <p class="info">
                    <customProcess
                        :size="150"
                        :strokeWidth="20"
                        :num="infoData.performOverallScore"
                    />
                </p>
                <p class="type">业绩表现</p>
            </li>
            <li class="annulus_item" v-if="infoData.objectiveScore!=null">
                <p class="info">
                    <customProcess
                        :size="150"
                        :strokeWidth="20"
                        :num="infoData.objectiveScore"
                    />
                </p>
                <p class="type">目标与结果</p>
            </li>
            <li class="annulus_item" v-if="infoData.kpiScore!=null">
                <p class="info">
                    <customProcess
                        :size="150"
                        :strokeWidth="20"
                        :num="infoData.kpiScore"
                    />
                </p>
                <p class="type">KPI评价</p>
            </li>
            <li class="annulus_item" v-if="infoData.potentialOverallScore!=null">
                <p class="info">
                    <customProcess
                        :size="150"
                        :strokeWidth="20"
                        :num="infoData.potentialOverallScore"
                    />
                </p>
                <p class="type">离职风险</p>
            </li>
            <li class="annulus_item" v-if="infoData.engagementScore!=null">
                <p class="info">
                    <customProcess
                        :size="150"
                        :strokeWidth="20"
                        :num="infoData.engagementScore"
                    />
                </p>
                <p class="type">敬业度</p>
            </li>
        </div>
        <div class="page_third_title">个人发展期望</div>
        <div class="develop_expect flex_row_betweens">
            <li class="annulus_item">
                <p class="type">个人期望发展类型</p>
                <p class="info">{{ infoData.developmentType }}</p>
            </li>
            <li class="annulus_item">
                <p class="type">个人期望晋升岗位</p>
                <p class="info">{{ infoData.jobName }}</p>
            </li>
            <li class="annulus_item">
                <p class="type">个人期望晋升周期</p>
                <p class="info">
                    {{ infoData.expectationCycle }}
                </p>
            </li>
        </div>
        <div class="page_third_title">个人发展期望</div>
        <div class="parag">{{ infoData.analyzeSummarize }}</div>
        <div class="page_third_title">个人发展计划</div>
        <div class="parag">{{ infoData.developmentPlan }}</div>
    </div>
</template>

<script>
    // 个人规划
    import { getPersonnelPlan } from "../../../../request/api";
    import customProcess from "@/components/talent/common/customProcess.vue";
    export default {
        name: "userRSelfPlan",
        props: ["nextBtnText", "enqId", "userId", "postCode"],
        components: { customProcess },
        data() {
            return {
                infoData: {},
            };
        },
        created() {},
        computed: {},
        mounted() {
            this.getPersonnelPlanFun();
        },
        methods: {
            getPersonnelPlanFun() {
                getPersonnelPlan({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200 && res.data) {
                        this.infoData = res.data;
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .annulus_wrap {
        margin-bottom: 16px;
        .annulus_item {
            text-align: center;
            .type {
                color: #0099ff;
                font-weight: 600;
            }
            .info {
                line-height: 25px;
            }
        }
    }
    .develop_expect {
		padding:0 16px;
		margin-bottom: 26px;
        .annulus_item {
            .type {
                color: #0099ff;
                font-weight: 600;
				margin-bottom: 16px;
            }
			.info{

			}
        }
    }
</style>
