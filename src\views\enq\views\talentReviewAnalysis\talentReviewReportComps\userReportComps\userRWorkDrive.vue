<template>
    <div class="report_section userR_work_drive_wrap">
        <div class="page_second_title">工作驱动因素</div>
        <div class="userR_work_drive_main">
            <div class="bottom_wrap">
                <div class="page_third_title">
                    <span>驱动因素（哪些因素能够有效的驱动个人发展）</span>
                </div>
                <div class="report_section_content">
                    <div class="chart_box factor" :id="driveFactorChartId"></div>
                </div>
            </div>
            <div class="bottom_wrap">
                <div class="page_third_title">
                    <span>激励因素（对其产生激励效果的因素）</span>
                </div>
                <div class="incentive_wrap flex_row_betweens">
                    <div class="incentive_item">
                        <p class="title">强激励因素</p>
                        <ul class="desc_wrap">
                            <li
                                class="desc_item"
                                v-for="(item, index) in incentiveFactors.Strong"
                                :key="index"
                            >
                                {{ item }}
                            </li>
                        </ul>
                    </div>
                    <div class="incentive_item">
                        <p class="title">一般激励因素</p>
                        <ul class="desc_wrap">
                            <li
                                class="desc_item"
                                v-for="(
                                    item, index
                                ) in incentiveFactors.General"
                                :key="index"
                            >
                                {{ item }}
                            </li>
                        </ul>
                    </div>
                    <div class="incentive_item">
                        <p class="title">一般负激励因素</p>
                        <ul class="desc_wrap">
                            <li
                                class="desc_item"
                                v-for="(
                                    item, index
                                ) in incentiveFactors.GeneralNegative"
                                :key="index"
                            >
                                {{ item }}
                            </li>
                        </ul>
                    </div>
                    <div class="incentive_item">
                        <p class="title">强负激励因素</p>
                        <ul class="desc_wrap">
                            <li
                                class="desc_item"
                                v-for="(
                                    item, index
                                ) in incentiveFactors.StrongNegative"
                                :key="index"
                            >
                                {{ item }}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="page_third_title">
            <span>个人激励因素</span>
            <tableComponent
                :tableData="tableData"
                :border="true"
                :needPagination="false"
                :needIndex="false"
                :overflowTooltip="!isPdf"
            ></tableComponent>
        </div>
    </div>
</template>

<script>
    // 工作驱动
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import { getWorkDrive, getJobIncentives } from "../../../../request/api";
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    export default {
        name: "userRWorkDrive",
        props: ["nextBtnText", "enqId", "userId", "postCode","isPdf"],
        components: {tableComponent},
        data() {
            return {
                driveFactorChartId:this.$util.createRandomId(),
                driveFactorChartData: {
                    data: [],
                    legend: [],
                },
				tableData:{
					columns:[
						{
							label:'评估维度',
							prop:'name',
							width:160
						},
						{
							label:'评估内容',
							prop:'content'
						},
						{
							label:'表现',
							prop:'actualScore'
						},
					],
					data:[]
				},
                incentiveFactors: [],
            };
        },
        created() {},
        computed: {},
        mounted() {
            this.getList();
            this.getWorkDriveFun();
        },
        methods: {
            getWorkDriveFun() {
                getWorkDrive({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    if (res.code == 200) {
                        this.driveFactorChartData.data =
                            res.data.turnoverRiskFactors.chartData;
                        this.driveFactorChartData.legend =
                            res.data.turnoverRiskFactors.legend;
                        echartsRenderPage(
                            this.driveFactorChartId, //id
                            "XBar", // 图表类型
                            null, //宽
                            "300", //高
                            this.driveFactorChartData //图表数据
                        );
                        this.incentiveFactors = res.data.incentiveFactors;
                    }
                });
            },
            getList() {
                getJobIncentives({ enqId: this.enqId, userId: this.userId }).then(
                    (res) => {
                        console.log(res);
						if(res.code == 200){
							this.tableData.data = res.data;
						}
                    }
                );
            },
        },
    };
</script>
<style scoped lang="scss">
    .userR_work_drive_wrap {
        padding: 0 10px;
        height: 480px;
        overflow: auto;
        pointer-events: auto;
        .userR_work_drive_main {
            .bottom_wrap {
                .page_third_title {
                    margin: 15px 0;
                }
                .report_section_content {
                    border: 1px solid #dcdfe6;
                    width: 100%;
                    height: 300px;
                }
                .incentive_wrap {
                    .incentive_item {
                        padding: 0 10px 10px;
                        width: 24%;
                        color: #008fff;
                        background: #dae8fd;
                        .title {
                            height: 35px;
                            line-height: 35px;
                            font-weight: 700;
                        }
                        .desc_wrap {
                            line-height: 20px;
                        }
                    }
                }
            }
        }
    }
</style>