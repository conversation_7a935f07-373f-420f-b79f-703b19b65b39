<template>
    <div class="report_section userR_KPI_cur_wrap">
        <div class="page_second_title">KPI评价</div>
        <div class="userR_KPI_cur_main">
            <div class="top_wrap">
                <div class="top_left_wrap marginB_32">
                    <div class="page_third_title">
                        <span>KPI评价指标综合评价结果</span>
                    </div>
                    <div class="top_left_main flex_row_around">
                        <li class="annulus_item">
                            <customProcess
                                :size="100"
                                :fontSize="20"
                                :strokeWidth="15"
                                :num="kpiScoreResult.kpiScore"
                            />
                            <p>综合得分</p>
                        </li>
                        <li class="annulus_item">
                            <customProcess
                                :size="100"
                                :fontSize="20"
                                :strokeWidth="15"
                                :num="kpiScoreResult.kpiSubScore"
                            />
                            <p>上级</p>
                        </li>
                        <li class="annulus_item" v-if="kpiScoreResult.kpiLeaderScore != null">
                            <customProcess
                                :size="100"
                                :fontSize="20"
                                :strokeWidth="15"
                                :num="kpiScoreResult.kpiLeaderScore"
                            />
                            <p>领导</p>
                        </li>
                        <li class="last_annulus_item">
                            {{ kpiScoreResult.kpiOverallMerit }}
                        </li>
                    </div>
                </div>
                <div class="top_right_wrap marginB_32">
                    <div class="ranking_wrap">
                        <div class="page_third_title">
                            <span>绩效评价综合排名</span>
                        </div>
                        <ul class="ranking_main flex_row_betweens">
                            <li class="item_wrap">
                                <p class="title">全司排名</p>
                                <p class="number">
                                    <span class="weight">{{
                                        kpiRanking.wholeRanking
                                    }}</span
                                    >/{{ kpiRanking.whole }}
                                </p>
                            </li>
                            <li class="item_wrap">
                                <p class="title">本部门排名</p>
                                <p class="number">
                                    <span class="weight">{{
                                        kpiRanking.orgRanking
                                    }}</span
                                    >/{{ kpiRanking.org }}
                                </p>
                            </li>
                            <li class="item_wrap">
                                <p class="title">本职位排名</p>
                                <p class="number">
                                    <span class="weight">{{
                                        kpiRanking.jobRanking
                                    }}</span
                                    >/{{ kpiRanking.job }}
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="bottom_wrap marginB_32">
                <div class="page_third_title">
                    <span>绩效指标</span>
                </div>
                <div class="reault_expression_item">
                    <tableComponent
                        :tableData="tableData"
                        :border="true"
                        :needPagination="false"
                        :needIndex="false"
                    ></tableComponent>
                </div>
            </div>
            <div class="result_eval">
                <div class="item">
                    <div class="item_label">个人KPI综合得分:</div>
                    <div class="item_value">{{kpiScoreResult.kpiScore}}</div>
                </div>
                <div class="item">
                    <div class="item_label">KPI系统评级:</div>
                    <div class="item_value">{{ kpiScoreResult.kpiOverallMerit  || '无' }}</div>
                </div>
                <div class="item">
                    <div class="item_label">KPI综合评级:</div>
                    <div class="item_value"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    // kpi评价
    import { getKpiEvalData } from "../../../../request/api";
    import customProcess from "@/components/talent/common/customProcess.vue";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "userRKPICur",
        props: ["nextBtnText", "enqId", "userId", "postCode"],
        components: {
            tableComponent,
            customProcess,
        },
        data() {
            return {
                kpiScoreResult: "",
                kpiRanking: "",
                tableData: {
                    columns: [
                        {
                            label: "指标名称",
                            prop: "kpiName",
                            width: "400",
                        },
                        {
                            label: "目标",
                            prop: "kpiObjective",
                            width: "",
                        },
                        {
                            label: "实际表现",
                            prop: "kpiActual",
                            width: "",
                        },
                        {
                            label: "本组织排名",
                            prop: "orgRanking",
                        },
                        {
                            label: "全公司排名",
                            prop: "wholeRanking",
                        },
                        {
                            label: "权重",
                            prop: "weight",
                        },
                        {
                            label: "上级评价",
                            prop: "supScore",
                        },
                        {
                            label: "分管领导",
                            prop: "leaderScore",
                        },
                        {
                            label: "综合得分",
                            prop: "overallScore",
                        },
                    ],
                    data: [],
                },
            };
        },
        created() {},
        computed: {},
        mounted() {
            this.getKpiEvalDataFun();
        },
        methods: {
            setItemText(value) {
                return () => {
                    return value;
                };
            },
            getKpiEvalDataFun() {
                getKpiEvalData({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    // console.log(res)
                    if (res.code == 200) {
                        this.kpiScoreResult = res.data.kpiScore;
                        this.kpiRanking = res.data.kpiRanking;
                        this.tableData.data = res.data.enqObjectiveKpis;
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .userR_KPI_cur_wrap {
        padding: 0 10px;
        height: 480px;
        overflow: auto;
        pointer-events: auto;
        .userR_KPI_cur_main {
            .top_wrap {
                .top_left_wrap {
                    .top_left_main {
                        padding: 25px 20px 0 0px;
                        .annulus_item {
                            text-align: center;
                            p {
                                margin: 10px 0 0 0;
                            }
                            .el-progress {
                                .el-progress__text {
                                    margin: 0 0 0 15%;
                                    width: 42px;
                                    height: 42px;
                                    line-height: 45px;
                                    background: #dae8fd;
                                    border-radius: 50%;
                                }
                            }
                        }
                        .last_annulus_item {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 100px;
                            height: 100px;
                            // line-height: 80px;
                            text-align: center;
                            color: #008fff;
                            background: #dae8fd;
                            border-radius: 50%;
                        }
                    }
                }
                .top_right_wrap {
                    flex: 1;
                    .ranking_wrap {
                        .ranking_main {
                            padding-left: 16px;
                            .item_wrap {
                                flex:1;
                                padding: 20px 0 0 16px;
                                height: 90px;
                                background: #dae8fd;
                                // text-align: center;
                                color: #008fff;
                                margin-right: 16px;
                                .title {
                                    font-weight: 600;
                                }
                                .number {
                                    margin: 16px 0 0 0;
                                    font-size: 14px;
                                    .weight {
                                        font-size: 18px;
                                        font-weight: 600;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .bottom_wrap {
                .page_second_title {
                    margin: 15px 0;
                }
            }
        }
    }
    .result_eval{
        display: flex;
        justify-content: space-between;
        align-items: center;
        .item{
            display: flex;
            font-size: 16px;
            line-height: 24px;
            .item_label{
                margin-right: 15px;
            }
            .item_value{
                color: #0099fd;
            }
        }
    }
</style>
