<template>
  <div class="org_management_dialog">
    <el-dialog :title="title[type]" v-model:visible="dialogVisible" width="800px" :before-close="handleClose">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="dialog_form" :inline="true" label-position="top">
        <el-form-item label="组织编码" prop="orgCode">
          <el-input v-model="ruleForm.orgCode" disabled></el-input>
        </el-form-item>
        <el-form-item label="组织名称" prop="orgName">
          <el-input v-model="ruleForm.orgName"></el-input>
        </el-form-item>
        <el-form-item label="组织简称" prop="orgShortName">
          <el-input v-model="ruleForm.orgShortName"></el-input>
        </el-form-item>
        <el-form-item label="上级组织" prop="orgSup">
          <el-input v-model="supOrgData['label']" disabled></el-input>
        </el-form-item>
        <el-form-item label="组织层级" prop="orgLevel">
          <el-select v-model="ruleForm.orgLevel" placeholder="请选择组织层级">
            <el-option label="JT/集团" value="JT"></el-option>
            <el-option label="GS/公司" value="GS"></el-option>
            <el-option label="BM/部门" value="BM"></el-option>
            <el-option label="ZU/小组" value="ZU"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属公司" prop="company">
          <el-input v-model="ruleForm.company"></el-input>
        </el-form-item>
        <el-form-item label="业务领域" prop="businessArea">
          <el-input v-model="ruleForm.businessArea"></el-input>
        </el-form-item>
        <el-form-item label="是否实体组织" prop="isEntityOrg">
          <el-select v-model="ruleForm.isEntityOrg" placeholder="是否实体组织">
            <el-option label="是" value="是"></el-option>
            <el-option label="否" value="否"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="confirmSubmit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'orgManagementDialog',
  props: {
    type: String,
    dialogVisible: Boolean,
    supOrgData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    let validateOrgName = (rule, value, callback) => {
      if (value == '') {
        callback(new Error('请输入组织名称'))
      } else {
        if (this.ruleForm.orgName !== '') {
          // 这里进行获取组织编码的操作
          this.ruleForm.orgCode = value
        }
        callback()
      }
    }
    return {
      title: {
        add: '新增组织',
        edit: '编辑组织'
      },
      ruleForm: {
        orgCode: '',
        orgName: '',
        orgShortName: '',
        orgSup: '',
        orgLevel: '',
        isEntityOrg: '是'
      },
      rules: {
        orgName: {
          required: true,
          validator: validateOrgName,
          trigger: 'blur'
        }
        // orgSup:{required: true,message:'请选择上级组织',trigger:'change'}
      }
    }
  },
  methods: {
    confirmSubmit() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          if (this.type == 'add') {
            this.addOrg()
          } else if (this.type == 'edit') {
            this.editOrg()
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
      if (this.type == 'add') {
        this.addOrg()
      } else if (this.type == 'edit') {
        this.editOrg()
      }
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          this.$refs['ruleForm'].resetFields()
          this.$emit('dialogHidden')
        })
        .catch(_ => {})
    },
    editOrg() {
      let that = this
      console.log('编辑')
      setTimeout(function () {
        that.$emit('dialogHidden')
        that.$refs['ruleForm'].resetFields()
      }, 3000)
    },
    addOrg() {
      let that = this

      console.log('新增')
      setTimeout(function () {
        that.$emit('dialogHidden')
        that.$refs['ruleForm'].resetFields()
      }, 3000)
    }
  }
}
</script>

<style scoped lang="scss">
.el-form-item__label {
  line-height: 20px;
}
.el-form--inline .el-form-item {
  margin-right: 0px;
  width: 32%;
}
.el-form--inline .el-form-item .el-select {
  width: 100%;
}
</style>
