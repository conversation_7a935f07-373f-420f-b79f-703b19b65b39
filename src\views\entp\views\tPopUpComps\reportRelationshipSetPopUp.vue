<template>
  <el-dialog :title="popupTitle" v-model="dialogVisible" @close="$emit('update:show', false)" width="80%" center>
    <div class="report_relationship_set_popUp_wrap">
      <div class="choose_post_main flex_row_betweens">
        <div class="main_left">
          <p class="page_second_title">选择人员</p>
          <div class="flex_row_start">
            <div class="main_left_tree">
              <p class="choose_post_title">部门</p>
              <div class="tree_main">
                <tree-comp-radio
                  :defaultCheckedKeys="defaultCheckedKeysPop"
                  :treeData="treeData"
                  @clickCallback="clickCallback"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="main_left">
          <div class="search_staff_wrap filter_item">
            <el-input placeholder="按姓名查询" suffix-icon="el-icon-search" v-model="likeName" @change="searchStaff" />
          </div>
          <div class="flex_row_start">
            <div class="main_left_choose">
              <div class="choose_post_title">人员</div>
              <ul class="second_level_post_wrap">
                <li
                  v-for="item in staffOptions"
                  :key="item.dictCode"
                  @click="chooseSecondLevelPost(item)"
                  class="flex_row_betweens"
                  :class="{ active: item.dictCode == checkOption?.dictCode }"
                >
                  <span>{{ item.codeName }}</span>
                  <span v-if="item.dictCode == checkOption?.dictCode" class="el-icon-check" />
                  <span v-else class="icon_check" />
                </li>
                <div class="no_data_row" v-if="staffOptions.length == 0">暂无数据</div>
              </ul>
            </div>
          </div>
        </div>
        <div class="main_right">
          <p class="page_second_title">已选人员</p>
          <div class="select_right">
            <p class="choose_post_title" />
            <ul class="second_level_post_wrap">
              <li class="flex_row_betweens hover_style" v-if="checkOption">
                <span>{{ checkOption.codeName }}</span>
                <span class="el_del_bg el-icon-minus" @click="removePost" />
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button class="page_clear_btn" @click="cancel">取 消</el-button>
      <el-button class="page_add_btn" type="primary" @click="submitBtn">确认</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getOrgDeptTree, getOrgStaffList, setSuperiorId } from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import { useUserStore } from '@/stores/modules/user'

// Props定义
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  setStaffSign: {
    type: String,
    default: ''
  },
  setSatffUserId: null,
  defaultCheckOption: null
})

// Emits定义
const emit = defineEmits(['update:show', 'setStaffSign'])

// Store
const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

// 响应式状态
const dialogVisible = computed({
  get: () => props.show,
  set: value => emit('update:show', value)
})
const popupTitle = ref('选择上级')
const treeData = ref([])
const defaultCheckedKeysPop = ref([])
const orgCode = ref('')
const likeName = ref('')
const staffOptions = ref([])
const checkOption = ref(null)
const leaderId = ref('')
const superiorId = ref('')

// 方法定义
const getOrgDeptTreeFun = async () => {
  try {
    const res = await getOrgDeptTree({
      companyId: companyId.value
    })
    if (res.code == 200) {
      treeData.value = res.data.length > 0 ? res.data : []
    } else {
      treeData.value = []
    }
  } catch (error) {
    console.error('获取组织树失败:', error)
    ElMessage.error('获取组织树失败')
  }
}

const clickCallback = (val, isLastNode) => {
  orgCode.value = val
  getOrgStaffListFun()
}

const getOrgStaffListFun = async () => {
  try {
    const res = await getOrgStaffList({
      companyId: companyId.value,
      likeName: likeName.value,
      orgCode: orgCode.value
    })

    if (res.code == 200) {
      staffOptions.value = res.data.map(item => ({
        codeName: item.userName,
        dictCode: item.userId
      }))
    } else {
      staffOptions.value = []
    }
  } catch (error) {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员列表失败')
  }
}

const searchStaff = () => {
  getOrgStaffListFun()
}

const chooseSecondLevelPost = item => {
  checkOption.value = item
}

const removePost = () => {
  checkOption.value = null
}

const cancel = () => {
  dialogVisible.value = false
  orgCode.value = ''
}

const submitBtn = async () => {
  superiorId.value = ''
  leaderId.value = ''

  if (props.setStaffSign == 'S') {
    superiorId.value = checkOption.value ? checkOption.value.dictCode : ''
  } else if (props.setStaffSign == 'L') {
    leaderId.value = checkOption.value ? checkOption.value.dictCode : ''
  }

  try {
    const res = await setSuperiorId({
      companyId: companyId.value,
      userId: props.setSatffUserId,
      superiorId: superiorId.value,
      leaderId: leaderId.value
    })

    if (res.code == 200) {
      ElMessage.success(res.msg)
      dialogVisible.value = false
      emit('setStaffSign', true)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('设置失败:', error)
    ElMessage.error('设置失败，请重试')
  }
}

// 生命周期钩子
onMounted(() => {
  getOrgDeptTreeFun()
})
</script>

<style scoped>
.report_relationship_set_popUp_wrap {
  padding: 0 20px;
}

.choose_post_main {
  display: flex;
  justify-content: space-between;
}

.main_left {
  width: 30%;
}

.main_right {
  width: 30%;
}

.page_second_title {
  font-size: 16px;
  font-weight: bold;
  color: #515c71;
  margin-bottom: 16px;
}

.choose_post_title {
  font-size: 14px;
  color: #515c71;
  margin-bottom: 8px;
}

.tree_main {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.second_level_post_wrap {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.second_level_post_wrap li {
  padding: 8px 16px;
  cursor: pointer;
}

.second_level_post_wrap li:hover {
  background-color: #f5f7fa;
}

.second_level_post_wrap li.active {
  background-color: #ecf5ff;
  color: #409eff;
}

.icon_check {
  width: 16px;
  height: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
}

.el_del_bg {
  cursor: pointer;
  color: #f56c6c;
}

.no_data_row {
  text-align: center;
  color: #909399;
  padding: 16px;
}

.search_staff_wrap {
  margin-bottom: 16px;
}

:deep(.el-dialog__header) {
  background-color: var(--color-tagbg);
  padding: 15px 20px;
}
</style>
