<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'

defineOptions({ name: 'indicator' })

const columns = ref([
  {
    label: '维度',
    prop: 'name'
  },
  {
    label: '核心指标',
    prop: 'indicator'
  },
  {
    label: '基线值',
    prop: 'base'
  },
  {
    label: '目标值',
    prop: 'target'
  },
  {
    label: '数据来源',
    prop: 'data'
  },
  {
    label: '评估频率',
    prop: 'eval'
  }
])
const tableData = ref([
  {
    strategy: '需求计划全流程端到端贯通',
    name: '流程完整性',
    indicator: '端到端流程完整率',
    base: '65%',
    target: '≥90%',
    data: '流程监控系统',
    eval: '每日统计'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '过程可控性',
    indicator: '流程断点发生率',
    base: '30 次 / 月',
    target: '≤8 次 / 月',
    data: '流程诊断报告',
    eval: '月度汇总'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '执行效率',
    indicator: '需求分析周期',
    base: '10 天',
    target: '≤7 天',
    data: 'ERP 系统日志',
    eval: '项目追踪'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '数据质量',
    indicator: '跨系统数据准确率',
    base: '82%',
    target: '≥95%',
    data: '数据对账平台',
    eval: '每日校验'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '持续优化性',
    indicator: '计划调整响应时效',
    base: '48 小时',
    target: '≤12 小时',
    data: '异常处理记录',
    eval: '实时监控'
  }
])
</script>
<template>
  <div class="core">
    <div class="core-title">
      <div class="text">策略评估指标</div>
    </div>
    <SimplenessTable :columns="columns" :data="tableData"></SimplenessTable>
  </div>
</template>
<style lang="scss" scoped>
.core {
  .core-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
    color: #3d3d3d;
    margin-bottom: 13px;
    .index {
      width: 16px;
      height: 16px;
      background: #40a0ff;
      border-radius: 50%;
      text-align: center;
      line-height: 16px;
      color: #fff;
      font-weight: normal;
      margin-right: 7px;
    }
  }
  .border {
    background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), rgba(255, 255, 255, 0.5);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 16px 20px;
  }
}
</style>
