<template>
  <div class="report_relationship_error_data_wrap">
    <div class="page_main_title">
      <div class="goback_geader" @click="goBack"><i class="el-icon-arrow-left"></i>返回</div>
      汇报关系管理
    </div>
    <div class="page_second_title">汇报关系导入信息--错误一览</div>
    <div class="page_section report_relationship_error_data_center clearfix">
      <el-table :data="tableData" stripe ref="tableRef" v-if="flag">
        <el-table-column type="index" width="50" />
        <el-table-column v-for="col in columns" :prop="col.prop" :key="col.prop" :label="col.label" :width="col.width">
          <template #default="scope">
            <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row[col.prop].msg"
              placement="top"
              :disabled="scope.row[col.prop].accept"
            >
              <el-input
                size="small"
                :class="{ error: !scope.row[col.prop].accept }"
                v-model="scope.row[col.prop].val"
                :disabled="scope.row[col.prop].accept"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <coustomPagination @pageChange="pageChange" :total="tableDataCopy.length" />
      <div class="btn_wrap align_center">
        <el-button type="primary" @click="saveReport">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
// import { importReportData } from '../../request/api'
import coustomPagination from '@/components/talent/paginationComps/coustomPagination.vue'

const router = useRouter()
const route = useRoute()

const flag = ref(true)
const tableData = ref([])
const tableDataCopy = ref([]) // 备份
const columns = [
  { label: '员工编码', prop: 'employeeCode' },
  { label: '员工姓名', prop: 'userName' },
  { label: '直接上级编码', prop: 'supEmployeeCode' },
  { label: '直接上级姓名', prop: 'supUserName' }
]

function goBack() {
  router.back()
}

function pageChange(pageSize, currentPage) {
  getPageData(pageSize, currentPage)
}

function getPageData(pageSize, currentPage) {
  const offset = (currentPage - 1) * pageSize
  tableData.value =
    offset + pageSize >= tableDataCopy.value.length
      ? tableDataCopy.value.slice(offset, tableDataCopy.value.length)
      : tableDataCopy.value.slice(offset, offset + pageSize)
}

function saveReport() {
  const reportExcelRequests = tableDataCopy.value.map(item => {
    const obj = {}
    for (const key in item) {
      obj[key] = item[key].val
    }
    return obj
  })

  // importReportData(reportExcelRequests).then(res => {
  //   if (res.code == 200) {
  //     tableData.value = []
  //     tableDataCopy.value = []
  //     tableData.value = res.data.obj
  //     tableDataCopy.value = res.data.obj
  //     flag.value = false
  //     if (res.data.errorCount == 0) {
  //       goBack()
  //       return
  //     }
  //     getPageData(10, 1)
  //     nextTick(() => {
  //       flag.value = true
  //     })
  //   } else {
  //     ElMessage.error(res.msg)
  //   }
  // })
}

onMounted(() => {
  tableData.value = route.query.data
  tableDataCopy.value = route.query.data
  getPageData(10, 1)
})
</script>

<style scoped lang="scss">
.el-input.error .el-input__inner {
  border-color: red;
}
.el-input--mini .el-input__inner {
  margin: 5px 0;
}
</style>
