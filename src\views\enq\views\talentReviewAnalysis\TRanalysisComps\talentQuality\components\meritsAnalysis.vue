
<template>
    <div class="talent_main">
        <div class="aside_filter_wrap">
            <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
        </div>
        <div class="talent_number_content page_section flex_row_wrap_start">
            <div class="content_item el-col-8">
                <div class="content_item_main">
                    <div class="content_item_title">绩效表现</div>
                    <div class="content_item_content" id="view"></div>
                </div>
            </div>
            <div class="content_item el-col-16">
                <div class="content_item_main">
                    <div class="content_item_title">部门分布</div>
                    <div class="content_item_content" id="orgView"></div>
                </div>
            </div>
            <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">岗位分布</div>
                    <div class="content_item_content" id="postView"></div>
                </div>
            </div>
            <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">详情列表
                        <el-button class="fr" type="primary" size="mini" @click="exportDataFn">导出</el-button>
                    </div>
                    <div class="content_item_content">
                        <tableComponet
                            @handleSizeChange="handleSizeChange"
                            @handleCurrentChange="handleCurrentChange"
                            :tableData="tableData"
                            :needIndex="true"
                        ></tableComponet>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js";
    import { qualityAllOrgDist,queryCompetenceList,exportData } from "../../../../../request/api.js";
    import asideFilterBar from "../../asideFilterBar.vue";
    import tableComponet from "@/components/talent/tableComps/tableComponent.vue";

    export default {
        name: "meritsAnalysis",
        props: [],
        components: {
            asideFilterBar,
            tableComponet
        },
        data() {
            return {
                enqId: "",
                jobClassCode: "",
                orgCode: "",
                filterData: {},
                view: {
                    data: [],
                },
                orgView: {
                    data: [],
                },
                postView: {
                    data: [],
                },
                page: 1,
                size: 10,
                tableData: {
                    columns: [
                        {
                            label: "员工编码",
                            prop: "employee_code",
                        },
                        {
                            label: "员工姓名",
                            prop: "user_name",
                        },
                        {
                            label: "所属组织",
                            prop: "org_name",
                        },
                        {
                            label: "任职岗位",
                            prop: "post_name",
                        },
                        {
                            label: "职层",
                            prop: "job_level_name",
                        },
                        {
                            label: "绩效表现",
                            prop: "kpi",
                        },
                        {
                            label: "评价人",
                            prop: "superior",
                        },
                        {
                            label: "评价日期",
                            prop: "evaluationTime",
                        },
                        
                    ],
                    data: [],
                    page: {
                        current: 1,
                        total: 0,
                        size: 10,
                    },
                },
            };
        },
        created() {
            this.enqId = this.$route.query.enqId;
            this.qualityAllOrgDist();
            this.filterData = this.$attrs.filterData;
            this.getTableData();
        },
        mounted() {},
        methods: {
            initChart() {
                echartsRenderPage("view", "YBar", "230", "220", this.view);
                echartsRenderPage("orgView", "YStack", "470", "220", this.orgView);
                echartsRenderPage("postView", "XBar", "700", "220", this.postView);
            },
            qualityAllOrgDist() {
                let params = {
                    enqId: this.enqId,
                    jobClassCode: this.jobClassCode,
                    orgCode: this.orgCode,
                    dictCode: "KPI_RANK",
                };
                qualityAllOrgDist(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        let data = res.data;
                        this.$set(
                            this.view,
                            "data",
                            this.$util.addPercentSign(data.view, "value")
                        );
                        // this.$set(this.orgView, "data", data.orgView);
                        this.orgView = data.distributionView;
                        this.$set(this.postView, "data", data.postView);
                        this.initChart();
                    }
                });
            },
            getCode(orgCode, jobClassCode) {
                this.jobClassCode = jobClassCode;
                this.orgCode = orgCode;
                this.page = 1;
                this.qualityAllOrgDist();
                this.getTableData();
            },
            handleCurrentChange(page) {
                this.page = page;
                this.getTableData();
            },
            handleSizeChange(size) {
                this.size = size;
                this.getTableData();
            },
            getTableData() {
                let params = {
                    enqId: this.enqId,
                    jobClassCode: this.jobClassCode,
                    orgCode: this.orgCode,
                    current: this.page,
                    size: this.size,
                };
                queryCompetenceList(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
            exportDataFn(){
                let params = {
                    enqId:this.enqId,
                    orgCode:this.orgCode,
                    type:'n'
                }
                exportData(params).then(res => {
                    this.$exportDownloadFile(res.data,'绩效分析详情列表');
                })
            }
        },
    };
</script>
 
<style scoped lang="scss">
</style>