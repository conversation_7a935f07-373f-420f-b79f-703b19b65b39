<template>
    <div class="assess_progress_manage_wrap bg_write">
        <div class="page_main_title">测评进度管理</div>
        <div class="page_section assess_progress_manage_main">
            <div
                    class="assess_progress_item flex_row_between"
                    v-for="(item,index) in listData"
                    :key="item.id">
                <div class="item_index">{{index+1}}</div>
                <div class="item_content_wrap">
                    <div class="item_content">
                        <p class="project_name overflow_elps" :title='item.projectName'>{{item.evalName}}</p>
                        <p class="time" v-if="item.beginTime">{{item.beginTime.split(' ')[0]}} - {{item.endTime.split(' ')[0]}}</p>
                        <div class="last_line_descript_wrap flex_row_betweens">
                            <div class="left flex_row_betweens">
                                <span class="company_name overflow_elps"
                                      :title='item.companyName'>{{item.companyName}}</span>
                                <span>{{item.userName}}</span>
                                <span>{{item.phoneNumber}}</span>
                            </div>
                            <div class="right">
                                <span class="num">{{item.submitNumber}}</span>提交
                                <span></span>/
                                <span class="num">{{item.totalNumber}}</span>总数
                            </div>
                        </div>
                    </div>
                </div>
                <div class="item_content_wrap progress_details">
                    <div class="item_content flex_row_around">
                        <div class="item_content_list">
                            <div class="list_title">完成度</div>
                            <div class="list_text">
                                <span class="list_num">{{item.degreeOfCompletion}}</span>%
                            </div>
                        </div>
                        <div class="item_content_list">
                            <div class="list_title">测评中</div>
                            <div class="list_text">
                                <span class="list_num">{{item.underEvaluation}}</span>人
                            </div>
                        </div>
                        <div class="item_content_list">
                            <div class="list_title">未开始</div>
                            <div class="list_text">
                                <span class="list_num">{{item.notStarted}}</span>人
                            </div>
                        </div>
<!--                        <div class="item_content_list">-->
<!--                            <div class="list_title">未登录</div>-->
<!--                            <div class="list_text">-->
<!--                                <span class="list_num">{{item.notStarted}}</span>人-->
<!--                            </div>-->
<!--                        </div>-->
                    </div>
                </div>
                <div class="item_oper flex_row_start">
                    <!-- <div
                            class="item_oper_list"
                            v-link="'/talentAssessment/talentAssessmentManagement/assessStaffManage?evalId='+item.evalId"
                    >
                        <i class="icon el-icon-s-data"></i>
                        <div class="text">参与人员</div>
                    </div> -->
                    <div
                            class="item_oper_list"
                            v-link="'/talentAssessment/talentAssessmentManagement/assessProgressManageInfo?evalId='+item.evalId"
                    >
                        <i class="icon el-icon-s-data"></i>
                        <div class="text">参评进度</div>
                    </div>
                    <div
                            class="item_oper_list"
                            v-link="'/talentAssessment/talentAssessmentManagement/assessCommentManageInfo?evalId='+item.evalId"
                    >
                        <i class="icon el-icon-s-data"></i>
                        <div class="text">点评进度</div>
                    </div>
                    <!-- <div
                            class="item_oper_list"
                            v-link="'/talentAssessment/talentAssessmentManagement/evaluateProgressManage?evalId='+item.evalId"
                    >
                        <i class="icon el-icon-s-data"></i>
                        <div class="text">评价进度</div>
                    </div> -->
                    <div
                            class="item_oper_list"
                            v-link="'/talentAssessment/talentAssessmentManagement/dataConfirmDetail?evalId='+item.evalId"
                    >
                        <i class="icon el-icon-s-data"></i>
                        <div class="text">数据确认</div>
                    </div>
                </div>
            </div>
            <div class="pagination_wrap">
                <el-pagination
                        :page-sizes="[5, 10, 20, 50]"
                        @size-change="handleSizeChange"
                        :current-page="page.current"
                        :page-size="page.size"
                        @current-change="handleCurrentChange"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="page.total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
    import {getEvalScheduleList} from "../../request/api"

    export default {
        name: "assessProgressManage",
        components: {},
        data() {
            return {
                listData: [],
                page:{
                    current:1,
                    size:5,
                    evalName:"",
                    total:0
                },

            };
        },
        created(){
            this.getEvalScheduleListFun()
        },
        methods: {
            getEvalScheduleListFun(){
                getEvalScheduleList({
                    current:this.page.current,
                    size:this.page.size,
                    evalName:this.evalName
                }).then(res=>{
                    console.log(res)
                    if(res.code ==200){
                        this.listData = res.data;
                        this.page = res.page;
                    }
                })
            },

            // 翻页
            handleCurrentChange(curPage) {
                this.page.current = curPage;
                this.getEvalScheduleListFun();
            },
            handleSizeChange(curSize) {
                this.page.size = curSize;
                this.getEvalScheduleListFun();
            },
        }
    };
</script>

<style scoped lang="scss">
    .assess_progress_manage_wrap {
        .assess_progress_manage_main {
            .assess_progress_item {
                position: relative;
                padding: 16px 8px 16px;
                margin-bottom: 8px;
                border: 1px solid #d9d9d9;
                overflow: hidden;

                .item_index {
                    width: 30px;
                    font-weight: bold;
                    font-size: 20px;
                    color: #409EFF;
                    text-align: center;
                }

                .item_content_wrap {
                    width: 50%;
                    padding: 0 8px;

                    .item_content {
                        padding-right: 10px;

                        .item_content_list {
                            color: #525e6c;

                            .list_title {
                                font-weight: bold;
                                margin-bottom: 8px;
                            }

                            .list_num {
                                color: #0099FF;
                                font-size: 16px;
                                font-weight: bold;
                            }
                        }

                        .project_name, .time {
                            line-height: 28px;
                        }

                        .project_name {
                            font-weight: 700;
                            cursor: pointer;
                        }

                        .time {

                        }

                        .last_line_descript_wrap {
                            height: 28px;
                            line-height: 28px;
                            white-space: nowrap;

                            .left {
                                span {
                                    margin: 0 8px 0 0;
                                }

                                .company_name {
                                    width: 220px;
                                    overflow: hidden;
                                    // cursor: pointer;
                                }
                            }

                            .right {

                                span {
                                    margin: 0 4px;
                                    display: inline-block;
                                }

                                .num {
                                    color: #0099FF;
                                    font-weight: bold;
                                    font-size: 16px;
                                }
                            }
                        }
                    }
                }

                .progress_details {
                    width: 25%;
                    border-left: 1px solid #d9d9d9;
                }

                .item_oper {
                    width: 260px;
                    padding: 0 8px;
                    border-left: 1px solid #d9d9d9;
                    align-items: center;
                    color: #0099fd;
                    text-align: center;

                    .icon {
                        font-size: 30px;
                    }

                    &_list {
                        width: 100%;
                        cursor: pointer;
                    }
                }
            }
        }
    }
</style>