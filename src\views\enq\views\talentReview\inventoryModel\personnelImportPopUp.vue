<template>
  <el-dialog
    title="开始导入"
    :visible.sync="dialogVisible"
    @close="$emit('update:show', false)"
    :show="show"
    width="50%"
    center
  >
    <div class="import_post_wrap">
      <div class="import_post_title">操作步骤:</div>
      <div class="oper_step">
        <p>1、下载《导入模板》</p>
        <p>
          2、打开下载表，将对应字段信息输入或粘贴进本表，为了保障粘贴信息被有效导入，请使用纯文本或者数字。
        </p>
        <p>3、信息输入完毕，点击“选择文件”按钮，选择excel文档。</p>
        <p>4、点击"开始导入",导入中如有任何疑问，请致电000000000。</p>
      </div>
      <a
        class="fs16 main_color download_file"
        href="/edp/api/entp/staffReportTemplate.xlsx"
        download="汇报关系批量导入模板.xlsx"
        >立即下载《批量导入模板》</a
      >
      <div class="upload_file_wrap">
        <el-input placeholder="请输入内容" v-model="fileName" readonly>
          <template slot="append">
            <label for="up" class="upload_label">
              选择文件
              <input
                id="up"
                style="display: none"
                ref="file"
                type="file"
                class="form-control page_clear_btn"
                @change="fileChange"
              />
            </label>
          </template>
        </el-input>
      </div>
    </div>
    <div slot="footer">
      <el-button class="page_clear_btn" @click="cancal">取 消</el-button>
      <el-button class="page_add_btn" type="primary" @click="submitBtn"
        >开始导入</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
// import {readReportRelationshipExcelData} from '../../request/api'
export default {
  name: "postImportPopUp",
  props: {
    show: {
      type: Boolean,
      dafault: false,
    },
  },
  data() {
    return {
      dialogVisible: this.show,
      fileName: "",
      uploadFile: null,
    };
  },
  created() {},
  mounted() {},
  methods: {
    fileChange(e) {
      if (e.target.files.length == 0) {
        return;
      }
      let formData = new FormData();
      //把文件信息放入对象中
      let file = e.target.files[0];
      //把文件信息放入对象中
      formData.append("file", file);
      this.fileName = file.name;
      this.uploadFile = formData;
    },
    readReportRelationshipExcelDataFun() {
      this.$emit("importSign", false);
      readReportRelationshipExcelData(this.uploadFile).then((res) => {
        if (res.code == 200) {
          this.uploadFile = null;
          if (res.data.errorCount > 0) {
            this.$router.push({
              path: "/basicSettingHome/reportRelationshipHome/reportRelationshipErrorData",
              query: {
                data: res.data.obj,
              },
            });
          } else {
            this.$msg.success(res.msg);
            this.fileName = "";
            document.getElementById("up").value = null;
            this.dialogVisible = false;
            this.$emit("importSign", true);
          }
        } else {
          this.$msg.error(res.msg);
          // this.$msg.error("模板文件格式不正确，请重新下载模板文件");
        }
        this.$refs.file.value = "";
      });
    },
    cancal() {
      this.dialogVisible = false;
    },
    submitBtn() {
      if (!this.fileName) {
        this.$msg.warning("请选择导入文件!");
        return;
      }
      this.readReportRelationshipExcelDataFun();
    },
  },
  watch: {
    show(val) {
      this.dialogVisible = this.show;
    },
  },
};
</script>
<style scoped lang="scss">
.import_post_wrap {
  .import_post_title {
    color: #515c71;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .oper_step {
    line-height: 28px;
    color: #515c71;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .download_file {
    display: block;
    margin-bottom: 16px;
  }

  .upload_file_wrap {
    margin-bottom: 16px;

    .upload_label {
      display: block;
      height: 28px;
      line-height: 28px;
      width: 100%;
      cursor: pointer;
    }
  }
}
.el-dialog__header {
  background-color: #EBF4FF;
  padding: 15px 20px;
}
</style>
