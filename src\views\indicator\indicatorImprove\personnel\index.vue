<template>
  <div class="personnel">
    <div class="tree">
      <el-input class="w-60 mb-[8px]" placeholder="按组织名称检索" />
      <!-- <el-tree :data="data" :props="defaultProps" default-expand-all /> -->
      <Tree></Tree>
    </div>
    <div class="main">
      <div class="key-index">
        <div class="title">关键指标（供应链计划管理部）</div>
        <div class="menu">
          <div
            class="menu-item"
            v-for="item in menuList"
            :class="{ active: item.id == activeStepId }"
            :key="item.id"
            @click="changeStep(item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <component :is="activeStep.comp" />
    </div>
  </div>
</template>
<script setup>
import Tree from '@/components/tree/index.vue'

const menuList = ref([
  { id: 1, name: '当期表现', comp: defineAsyncComponent(() => import('./present.vue')) },
  { id: 2, name: '上期表现', comp: defineAsyncComponent(() => import('./prior.vue')) },
  { id: 3, name: '指标变化', comp: defineAsyncComponent(() => import('./variation.vue')) }
])
const router = useRouter()
const route = useRoute()
const activeStepId = ref(route.query.stepId || menuList.value[0].id)
const activeStep = computed(() => {
  return menuList.value.find(item => item.id == activeStepId.value)
})
router.push({ path: route.path, query: { ...route.query, stepId: activeStepId.value } })
const changeStep = item => {
  activeStepId.value = item.id
  router.push({ path: route.path, query: { ...route.query, stepId: item.id } })
}
const defaultProps = {
  children: 'children',
  label: 'label'
}
const data = ref([
  {
    id: 1,
    label: '二级组织名称',
    children: [
      {
        id: 4,
        label: '三级组织名称1',
        children: [
          {
            id: 41,
            label: '人员名称1'
          }
        ]
      },
      {
        id: 5,
        label: '三级组织名称2'
      },
      {
        id: 6,
        label: '三级组织名称3'
      },
      {
        id: 7,
        label: '三级组织名称4'
      }
    ]
  }
])
</script>
<style lang="scss" scoped>
.personnel {
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  .tree {
    width: 206px;
    border-radius: 8px;
    border: 1px solid #c6dbf3;
    padding: 8px;
    margin-right: 20px;
    flex-shrink: 0;
  }
  .main {
    width: calc(100% - 226px);
    .key-index {
      .title {
        font-size: 16px;
        color: #333333;
        font-weight: 600;
        margin-bottom: 12px;
      }
      .menu {
        display: flex;
        margin-bottom: 20px;
        .menu-item {
          width: 193px;
          height: 35px;
          background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
          border-radius: 5px 5px 5px 5px;
          border: 1px solid #c6dbf3;
          color: #333333;
          font-size: 14px;
          font-weight: 500;
          line-height: 35px;
          text-align: center;
          margin-right: 9px;
          cursor: pointer;
          &.active {
            color: #ffffff;
            background: linear-gradient(90deg, #40a0ff 0%, #8cc6fe 50%, #90c8fe 100%);
          }
        }
      }
    }
  }
}
</style>
