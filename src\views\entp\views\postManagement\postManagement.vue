# Updating to Vue 3 with Composition API
<template>
  <div class="post_management_wrap bg_write">
    <div class="page_main_title">岗位管理</div>
    <div class="page_section">
      <div class="post_management_center clearfix">
        <div class="page_section_aside org_chart_aside">
          <div class="aside_tree_title">
            <div>组织分类</div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio
              :treeData="treeData"
              :defaultExpandAll="false"
              :expandedLevel="2"
              :defaultCheckedKeys="defaultCheckedKeys"
              @clickCallback="treeClick"
              :needCheckedFirstNode="true"
            ></tree-comp-radio>
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <div class="filter_bar_wrap">
            <div class="flex_row_start">
              <div class="filter_item title">筛选</div>
              <div class="filter_item">
                <el-cascader
                  v-model="jobLevelCode"
                  :options="jobLevelTreeData"
                  placeholder="职层"
                  :props="{
                    label: 'value',
                    value: 'code',
                    expandTrigger: 'hover',
                    checkStrictly: true
                  }"
                  @change="val => handleItemChange(val, 'jobLevelTree')"
                  clearable
                />
              </div>
              <div class="filter_item">
                <el-input v-model="jobName" clearable placeholder="按职位名称检索">
                  <template #suffix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              <div class="filter_item">
                <el-input v-model="postName" clearable placeholder="按岗位名称进行检索">
                  <template #suffix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              <div class="filter_item">
                <el-button class="page_add_btn" type="primary" @click="searchPost">查询</el-button>
              </div>
            </div>
          </div>
          <div class="filter_bar_wrap">
            <div class="flex_row_start">
              <div class="filter_item title">操作</div>
              <el-button class="page_add_btn" type="primary" @click="createPost">新增</el-button>
              <el-button class="page_add_btn" type="primary" @click="postImport">导入</el-button>
              <el-button class="page_add_btn" type="primary" @click="postExport">导出</el-button>
            </div>
          </div>
          <div>
            <table-component
              :tableData="tableData"
              :needIndex="true"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
            >
              <template #oper>
                <el-table-column label="协同岗位" width="100">
                  <template #default="scope">
                    <div class="flex_row_start">
                      <span class="span_col">{{ scope.row.collPost }}</span>
                      <el-button
                        @click="allocation(tableData.data[scope.$index])"
                        type="primary"
                        link
                        :icon="Setting"
                        class="color_base el_setting"
                      />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button
                      @click="tableEdit(tableData.data[scope.$index])"
                      :icon="Edit"
                      type="primary"
                      link
                      class="icon_edit"
                    />
                    <el-button
                      class="color_danger icon_del"
                      @click="tableDeleteRow(scope.$index, tableData.data)"
                      type="danger"
                      link
                      :icon="Delete"
                    />
                  </template>
                </el-table-column>
              </template>
            </table-component>
          </div>
        </div>
      </div>
    </div>
    <post-import-pop-up v-model:show="showPopUp" @importSign="importSign" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Setting, Edit, Delete } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/modules/user'
import {
  getOrgDeptTree,
  getPostPageList,
  jobLevelTree,
  deletePost,
  getPostTree,
  exportPostData,
  exportDownload
} from '../../request/api'
import TreeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import TableComponent from '@/components/talent/tableComps/tableComponent'
import PostImportPopUp from '../tPopUpComps/postImportPopUp'
import { formatterData } from '@/utils/utils'

defineOptions({ name: 'PostManagement' })

// Store and Router
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

// Reactive State
const useCache = ref(route.params.useCache || false)
const treeData = ref([])
const jobLevelTreeData = ref([])
const jobLevelCode = ref('')
const defaultCheckedKeys = ref([])
const orgCode = ref('')
const jobName = ref('')
const parentPostCode = ref('')
const postName = ref('')
const parentPostTreeData = ref([])
const showFictitiousOrg = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showPopUp = ref(false)
const hasPostSign = ref(false)

// Table Data
const tableData = ref({
  columns: [
    { label: '岗位外部编码', prop: 'postCodeExtn' },
    { label: '岗位名称', prop: 'postName' },
    { label: '所属组织', prop: 'org' },
    { label: '上级岗位', prop: 'supPost' },
    { label: '职位族群', prop: 'jobGroup' },
    { label: '岗位序列', prop: 'postSequence' },
    { label: '职位', prop: 'jobName' },
    { label: '职层', prop: 'jobLevel' },
    { label: '职等', prop: 'jobGrade' }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})

// Computed
const companyId = computed(() => userStore.userInfo.companyId)

// Methods
const init = async () => {
  if (useCache.value) {
    readCacheParams()
  }

  // 职层树
  const jobLevelResult = await jobLevelTree()
  jobLevelTreeData.value = jobLevelResult.length > 0 ? formatterData(jobLevelResult) : []

  // 组织树
  getOrgDeptTree({ companyId: companyId.value }).then(res => {
    if (res.code == 200) {
      treeData.value = res.data.length > 0 ? res.data : []
    } else {
      treeData.value = []
    }
  })
}

const readCacheParams = () => {
  const cacheParams = userStore.postMParams
  jobLevelCode.value = cacheParams.jobLevelCode
  orgCode.value = cacheParams.orgCode
  defaultCheckedKeys.value = [orgCode.value]
  parentPostCode.value = cacheParams.parentPostCode
  postName.value = cacheParams.postName
  jobName.value = cacheParams.jobName
  currentPage.value = cacheParams.current
  pageSize.value = cacheParams.size
}

const treeClick = val => {
  orgCode.value = val
  if (!useCache.value) {
    currentPage.value = 1
  }
  getPostPageListFun()
}

const handleItemChange = (val, treeType) => {
  if (val) {
    if (treeType == 'jobLevelTree') {
      jobLevelCode.value = val.length > 0 ? val[val.length - 1] : ''
    }
    if (treeType == 'postTree') {
      parentPostCode.value = val.length > 0 ? val[val.length - 1] : ''
    }
  }
}

const getPostTreeFun = async () => {
  try {
    const res = await getPostTree({ orgCode: '' })
    parentPostTreeData.value = res.length > 0 ? formatterData(res) : []
  } catch (error) {
    console.error('Error fetching post tree:', error)
  }
}

const searchPost = () => {
  currentPage.value = 1
  tableData.value.page.current = 1
  getPostPageListFun()
}

const createPost = () => {
  if (!orgCode.value) {
    ElMessage.warning('请选择组织！')
    return
  }
  router.push({
    path: '/basicSettingHome/postManagement/createPost',
    query: { orgCode: orgCode.value }
  })
}

const getPostPageListFun = async () => {
  const data = {
    jobLevelCode: jobLevelCode.value,
    orgCode: orgCode.value,
    parentPostCode: parentPostCode.value,
    postName: postName.value,
    jobName: jobName.value,
    current: currentPage.value,
    size: pageSize.value
  }

  const cacheParams = userStore.getParams('postMParams')
  const params = useCache.value ? { ...data, ...cacheParams } : { ...cacheParams, ...data }

  userStore.setParams({
    params,
    stateKey: 'postMParams'
  })

  try {
    const res = await getPostPageList(params)
    if (res.code == 200 && res.data) {
      tableData.value.page.total = res.total
      tableData.value.data = res.data.map(item => ({
        postCodeExtn: item.postCodeExtn,
        postName: item.postName,
        org: item.orgName,
        orgCode: item.orgCode,
        supPost: item.parentPostName,
        jobGroup: item.parentJobClassName,
        postSequence: item.jobClassName,
        jobName: item.jobName,
        jobLevel: item.jobLevelName,
        jobGrade: item.jobGradeName,
        collPost: item.postCoopNum,
        sort: item.sortNbr,
        postCode: item.postCode
      }))
    } else {
      tableData.value.data = []
      tableData.value.page.total = res.total
    }
    useCache.value = false
  } catch (error) {
    console.error('Error fetching post list:', error)
    useCache.value = false
  }
}

const handleSizeChange = size => {
  pageSize.value = size
  getPostPageListFun()
}

const handleCurrentChange = current => {
  currentPage.value = current
  getPostPageListFun()
}

const allocation = row => {
  router.push({
    path: '/basicSettingHome/postManagement/createPost',
    query: {
      orgCode: row.orgCode,
      postCode: row.postCode,
      isaAllocationSign: true,
      pageType: 'edit'
    }
  })
}

const tableEdit = row => {
  router.push({
    path: '/basicSettingHome/postManagement/createPost',
    query: {
      orgCode: row.orgCode,
      postCode: row.postCode,
      pageType: 'edit'
    }
  })
}

const tableDeleteRow = async (index, rows) => {
  try {
    await ElMessageBox.confirm('确认删除?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await deletePostFun(rows[index].postCode)
  } catch (error) {
    // User canceled or error occurred
  }
}

const deletePostFun = async postCode => {
  try {
    const res = await deletePost({ postCode })
    if (res.code == 200) {
      ElMessage.success(res.msg)
      tableData.value.page.current = 1
      getPostPageListFun()
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('Error deleting post:', error)
  }
}

const postImport = () => {
  showPopUp.value = true
}

const postExport = () => {
  exportPostDataFun()
}

const exportPostDataFun = async () => {
  try {
    const res = await exportPostData()
    if (res.code == 200) {
      const downloadRes = await exportDownload({ fileCode: res.data })
      const blob = new Blob([downloadRes])
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = '岗位信息.xlsx'
      link.click()
      window.URL.revokeObjectURL(link.href)
    }
  } catch (error) {
    console.error('Error exporting data:', error)
  }
}

// Watchers
watch(
  () => companyId.value,
  val => {
    if (val) {
      init()
    }
  },
  { immediate: true }
)

// Initialize
onMounted(() => {
  // Additional initialization if needed
})
</script>

<style lang="scss" scoped>
.post_management_wrap {
  .page_section_aside {
    width: 260px;
    float: left;
    background: #fff;
    margin-right: 20px;
    .aside_tree_title {
      height: 50px;
      line-height: 50px;
      padding: 0 20px;
      border-bottom: 1px solid #eee;
      div {
        font-size: 16px;
        font-weight: bold;
      }
    }
    .aside_tree_list {
      padding: 20px;
    }
  }
  .page_section_main {
    overflow: hidden;
    background: #fff;
    padding: 20px;
    .filter_bar_wrap {
      margin-bottom: 20px;
      .filter_item {
        margin-right: 10px;
        &.title {
          line-height: 32px;
        }
      }
    }
  }
  .el_setting {
    margin-left: 10px;
  }
  .span_col {
    display: inline-block;
    width: 30px;
    text-align: center;
  }
}
</style>
