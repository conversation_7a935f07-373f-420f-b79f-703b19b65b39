// 使用 Vite 的 import.meta.glob 替代 webpack 的 require.context
const talentNumberModules = import.meta.glob('./talentNumber/**/*.vue')
const talentStructureModules = import.meta.glob('./talentStructure/**/*.vue')
const talentQualityModules = import.meta.glob('./talentQuality/**/*.vue')
const talentOrgModules = import.meta.glob('./talentOrg/**/*.vue')
const talentEfficiencyModules = import.meta.glob('./talentEfficiency/**/*.vue')
const talentRiskModules = import.meta.glob('./talentRisk/**/*.vue')
const talentOptimizeModules = import.meta.glob('./talentOptimize/**/*.vue')

const routerBasic = '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/'

function createRouter(modules, routeKey) {
  const resRoute = []

  Object.keys(modules).forEach(path => {
    // 提取文件名作为组件名
    const fileName = path.split('/').pop().replace('.vue', '')

    // 跳过包含 'Home' 的文件
    if (fileName.includes('Home')) {
      return
    }

    const routePath = `${routerBasic}${routeKey}/${fileName}`

    resRoute.push({
      path: routePath,
      name: fileName,
      meta: {
        hidd: true,
        name: fileName
      },
      component: modules[path] // Vite 会自动处理动态导入
    })
  })

  return resRoute
}

export const talentNumberRouter = createRouter(talentNumberModules, 'talentNumber')
export const talentStructureRouter = createRouter(talentStructureModules, 'talentStructure')
export const talentQualityRouter = createRouter(talentQualityModules, 'talentQuality')
export const talentOrgRouter = createRouter(talentOrgModules, 'talentOrg')
export const talentEfficiencyRouter = createRouter(talentEfficiencyModules, 'talentEfficiency')
export const talentRiskRouter = createRouter(talentRiskModules, 'talentRisk')
export const talentOptimizeRouter = createRouter(talentOptimizeModules, 'talentOptimize')
