<template>
    <div class="org_report_main" :class="{'marginB_16':isPdf}">
        <div class="page_second_title">工作网络</div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import { orgTalentDiff, orgTalentDiffDetail,orgTalentDiffMatrix } from "../../../../request/api.js";
    export default {
        name: "orgRWorkNet",
        props: ["enqId", "orgCode","isPdf"],
        data() {
            return {
                kpiRankOption: [],
                kpiCapablity:{},
                developmentCapability:[],
                chartDom: [
                    {
                        chartDomId: "TALEsssNT",
                        title: "",
                        elSpan: 8,
                        height:198,
                        chartType: "XBar",
                        dataKey:'TALENT_CLASS'
                    },
                ],
            };
        },
        created() {
            this.getData();
        },
        mounted() {},
        methods: {
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                    };
                    if(chart.chartDomId == 'TALENT_CLASS'){
                        console.log('人才区分-人才分类');
                        console.log(chartData);
                    }
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTalentDiff(params).then((res) => {
                    // console.log(res);
                    if (res.code == "200") {
                        this.initChart(res.data);
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>