<template>
  <div class="detail_main">
    <div class="detail_main_aside">
      <div class="page_third_title">分析主题</div>
      <tabsLink :tabsData="tabsLinkData" :isVertical="true"></tabsLink>
    </div>
    <div class="detail_main_content">
      <div class="page_third_title">分析视图</div>
      <router-view :filterData="filterData"></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { queryOrgWithSubChildren, queryJobClassByOrg } from '../../../../request/api'
import tabsLink from '@/components/talent/tabsComps/tabsLink'

const route = useRoute()
const enqId = ref(route.query.enqId)

const tabsLinkData = ref([
  {
    id: 'asdfsdfqteqa',
    name: '工作活动整体分析',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentEfficiency/activityAnalysis'
  },
  {
    id: 'asdqrefafasdfa',
    name: '部门活动对比',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentEfficiency/departmentContrast'
  },
  {
    id: 'asasdfdqrefasffa',
    name: '岗位活动对比',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentEfficiency/postContrast'
  },
  {
    id: 'asdqrefaasfagdasdfa',
    name: '流程岗位分布分析',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentEfficiency/ProcessPostDistribute'
  }
])

const filterData = reactive({
  orgData: [],
  jobClass: []
})

const queryOrgWithSubChildrenFun = async enqId => {
  try {
    const res = await queryOrgWithSubChildren({ enqId })
    if (res.code == 200) {
      filterData.orgData = res.data
    }
  } catch (error) {
    console.error(error)
  }
}

const queryJobClassByOrgFun = async () => {
  try {
    const res = await queryJobClassByOrg()
    if (res.code == 200) {
      filterData.jobClass = res.data
    }
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  tabsLinkData.value.forEach(item => {
    item.path = item.path + '?enqId=' + enqId.value
  })
  queryOrgWithSubChildrenFun(enqId.value)
  queryJobClassByOrgFun()
})
</script>

<style lang="scss"></style>
