<template>
  <div class="report_page" id="page1">
    <div class="report_section">
      <div class="page_second_title">各部门敬业度和驱动因素</div>
      <div class="report_section_content clearfix">
        <el-table ref="multipleTable" :data="mydOrgList">
          <el-table-column
            prop="name"
            label="驱动因素"
            width="300"
          ></el-table-column>
          <el-table-column
            v-for="col in enqOrgInfos"
            :prop="col.orgCode"
            :key="col.orgCode"
            :label="col.orgName"
          ></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">部门得分差异的可能性</div>
      <div class="report_section_content clearfix">
        <div class="explain_text">
          -
          <span>资源投入有效性：</span
          >内部资源配置并非完全根据工作内容、工作量、工作压力及创造价值的差异进行分配，可能会产生敬业度的分化。
        </div>
        <div class="explain_text">
          -
          <span>管理者风格差异：</span
          >管理者风格（高层领导及部门领导）的差异使得各部门形成不同的工作氛围，这成为影响员工敬业水平的重要因素。
        </div>
        <div class="explain_text">
          -
          <span>人员结构差异：</span
          >员工的结构差异（年龄、司龄、学历等）导致员工期望和需求的差异，也是影响敬业水平的重要因素。
        </div>
        <div class="explain_text">
          -
          <span>敬业行为传导：</span
          >直接上级、管理团队的风格及行为表现成为影响员工敬业水平的重要因素。
        </div>
        <div class="explain_text">
          -
          <span>回报体系的差异：</span
          >回报体系会因地点、工作内容、职位等的差异而产生差别，这对敬业度水平产生重要的作用。
        </div>
        <div class="explain_text">
          -
          <span>工作职责差异：</span
          >不同部门的员工工作职责有很大差异，不同的工作压力、工作内容、工作考评体系会影响到敬业度水平。
        </div>
        <div class="explain_text">
          -
          <span>工作压力的不同：</span
          >员工工作内容及面临的压力、工作氛围（如硬件设施，上级及同事软环境等）的差异性也是导致敬业度差异的原因。
        </div>
        <div class="explain_text">
          -
          <span>授权程度：</span
          >在公司中的授权程度往往会影响员工可支配的资源、行动的自由度等，这些都会影响整个员工的敬业度水平。
        </div>
      </div>
    </div>
    <div class="report_section">
      <!-- <div class="page_second_title">各部门敬业度各驱动因素</div> -->
      <div class="report_section_content clearfix">
        <div class="chart_wrap">
          <div class="page_third_title">按职层分析</div>
          <div id="jydJobLevelChart" class="chart_box"></div>
        </div>
      </div>
    </div>
    <div class="report_section">
      <div class="report_section_content clearfix">
        <el-table ref="multipleTable" :data="jyJobLevelList">
          <el-table-column
            prop="name"
            label="驱动因素"
            width="300"
          ></el-table-column>
          <el-table-column
            v-for="col in jobLevels"
            :prop="col.jobLevelCode"
            :key="col.jobLevelCode"
            :label="col.jobLevelName"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
 
<script>
import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js";
import { jfAnalysisPage } from "../../../../../request/api.js";
import tableComp from "@/components/talent/tableComps/tableComponent";
export default {
  name: "",
  props: ["enqId", "orgCode", "isPdf", "userId"],
  components: { tableComp },
  data() {
    return {
      enqOrgInfos: [], //各部门敬业度各驱动因素columns
      mydOrgList: [], //各部门敬业度各驱动因素 data
      jydJobLevelChart: {
        data: [],
      },
      jobLevels: [],
      jyJobLevelList: [],
      tableData: {
        columns: [
          {
            label: "驱动因素",
            prop: "orgCode",
          },
          {
            label: "部门一",
            prop: "count",
          },
          {
            label: "部门二",
            prop: "type1",
          },
          {
            label: "部门三",
            prop: "type2",
          },
          {
            label: "部门四",
            prop: "type3",
          },
          {
            label: "部门五",
            prop: "type4",
          },
          {
            label: "部门六",
            prop: "type5",
          },
        ],
        data: [],
      },
      tableData2: {
        columns: [
          {
            label: "",
            prop: "orgCode",
          },
          {
            label: "总经理",
            prop: "count",
          },
          {
            label: "副总",
            prop: "type1",
          },
          {
            label: "总监",
            prop: "type2",
          },
          {
            label: "经理",
            prop: "type3",
          },
          {
            label: "主管",
            prop: "type4",
          },
          {
            label: "员工",
            prop: "type5",
          },
        ],
        data: [],
      },
    };
  },
  created() {
    this.jfAnalysisPageFun();
  },
  mounted() {},
  methods: {
    initChart() {
      echartsRenderPage(
        "jydJobLevelChart",
        "XBar",
        null,
        "280",
        this.jydJobLevelChart
      );
    },
    jfAnalysisPageFun() {
      let params = {
        enqId: this.enqId,
        orgCode: this.orgCode,
        userId: this.userId,
        number: "5",
      };
      jfAnalysisPage(params).then((res) => {
        console.log(res);
        if (res.code == "200") {
          let data = res.data.jfAnalysisPage5;
          this.enqOrgInfos = this.dotToline(
            data.enqOrgInfos,
            "value",
            "orgCode"
          );
          this.mydOrgList = this.dotToline(data.mydOrgList, "key");
          data.jydJobLevelChart.map((item) => {
            item["value"] = item.val;
          });
          this.jobLevels = this.dotToline(
            data.jobLevels,
            "value",
            "jobLevelCode"
          );
          this.jyJobLevelList = this.dotToline(data.jyJobLevelList, "key");
          this.$set(this.jydJobLevelChart, "data", data.jydJobLevelChart);
          this.initChart();
        }
      });
    },
    dotToline(param, type, valueKey) {
      if (Array.isArray(param)) {
        if (param.length == 0) {
          return;
        }
        param.forEach((item) => {
          if (typeof item == "object") {
            for (const key in item) {
              if (item.hasOwnProperty(key)) {
                if (type == "key") {
                  let newKey = key.split(".").join("-");
                  item[newKey] = item[key];
                } else if (type == "value") {
                  let val = item[valueKey];
                  item[valueKey] = val.split(".").join("-");
                }
                // delete item[key];
              }
            }
          }
        });
        return param;
      }
    },
  },
};
</script>
 
<style scoped lang="scss">
.dedicated_main {
  height: 420px;
  overflow-y: auto;
}
.report_section {
  margin-bottom: 32px;
}
.chart_wrap {
  width: 100%;
  height: auto;
}
.explain_text {
  overflow: hidden;
  color: #212121;
  line-height: 24px;
}
</style>