<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title">流动类型</div>
          <div class="content_item_content" id="flow_type"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">入职人数</div>
          <div class="content_item_content" id="entrantCount"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">离职人数</div>
          <div class="content_item_content" id="resignationCount"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">调入人数</div>
          <div class="content_item_content" id="transInCount"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">调出人数</div>
          <div class="content_item_content" id="transOutCount"></div>
        </div>
      </div>
      <!-- <div class="content_item el-col-12">
                <div class="content_item_main">
                    <div class="content_item_title">流动部门</div>
                    <div class="content_item_content" id="flow_org"></div>
                </div>
            </div>
            <div class="content_item el-col-12">
                <div class="content_item_main">
                    <div class="content_item_title">流动岗位及人数</div>
                    <div class="content_item_content" id="flow_post"></div>
                </div>
            </div> -->
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            详情列表
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>

          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { talentFlow, talentFlowList, exportData } from '../../../../../request/api.js'
import asideFilterBar from '../../asideFilterBar.vue'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'

const route = useRoute()
const enqId = ref(route.query.enqId)
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref({})

const flowType = reactive({
  data: []
})

const entrantCount = reactive({
  data: []
})

const resignationCount = reactive({
  data: []
})

const transInCount = reactive({
  data: []
})

const transOntCount = reactive({
  data: []
})

const page = ref(1)
const size = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '一级组织',
      prop: 'onLevelName'
    },
    {
      label: '二级组织',
      prop: 'twoLevelName'
    },
    {
      label: '三级组织',
      prop: 'threeLevelName'
    },
    {
      label: '四级组织',
      prop: 'fourLevelName'
    },
    {
      label: '当前部门',
      prop: 'orgName'
    },
    {
      label: '是否末级部门',
      prop: 'lastStage'
    },
    {
      label: '部门负责人',
      prop: 'orgLeaderName'
    },
    {
      label: '编制',
      prop: 'budgetedCount',
      width: 50
    },
    {
      label: '期初',
      prop: 'initialCount',
      width: 50
    },
    {
      label: '入职',
      prop: 'entrantCount',
      width: 50
    },
    {
      label: '离职',
      prop: 'resignationCount',
      width: 50
    },
    {
      label: '调入',
      prop: 'transInCount',
      width: 50
    },
    {
      label: '调出',
      prop: 'transOntCount',
      width: 50
    },
    {
      label: '期末',
      prop: 'finalCount',
      width: 50
    },
    {
      label: '缺口',
      prop: 'shortageCount',
      width: 50
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('flow_type', 'XBar', '700', '220', flowType)
  echartsRenderPage('entrantCount', 'YBar', '340', '220', entrantCount)
  echartsRenderPage('resignationCount', 'YBar', '340', '220', resignationCount)
  echartsRenderPage('transInCount', 'YBar', '340', '220', transInCount)
  echartsRenderPage('transOutCount', 'YBar', '340', '220', transOntCount)
}

const talentFlowFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await talentFlow(params)
    if (res.code == 200) {
      const data = res.data
      flowType.data = data.flowType
      entrantCount.data = window.$util.addPercentSign(data.entrant, 'value')
      resignationCount.data = window.$util.addPercentSign(data.resignation, 'value')
      transInCount.data = window.$util.addPercentSign(data.transIn, 'value')
      transOntCount.data = window.$util.addPercentSign(data.transOut, 'value')
      initChart()
    }
  } catch (error) {
    console.error('获取流动数据失败:', error)
  }
}

const getCode = (orgCode, jobClassCode) => {
  jobClassCode.value = jobClassCode
  orgCode.value = orgCode
  page.value = 1
  talentFlowFun()
  getTableData()
}

const handleCurrentChange = page => {
  page.value = page
  getTableData()
}

const handleSizeChange = size => {
  size.value = size
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await talentFlowList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error('获取表格数据失败:', error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'f'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '人才流动详情列表')
  } catch (error) {
    console.error('导出数据失败:', error)
  }
}

onMounted(() => {
  talentFlowFun()
  filterData.value = route.attrs.filterData
  getTableData()
})
</script>

<style scoped lang="scss"></style>
