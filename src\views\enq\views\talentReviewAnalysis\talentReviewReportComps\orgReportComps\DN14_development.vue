<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <slot></slot>
        <div class="page_second_title">人才发展</div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24">
                <div class="item_title text_center">
                    一年内向上发展的人员类型分布
                </div>
                <el-col :span="24">
                    <talent-classify-matrix
                        :kpiRankOption="kpiRankOption"
                        :competenceRankOptions="competenceRankOptions"
                        :needCodeDesc="false"
                        :matrixHeight="260"
                        :unit="'位'"
                        :talentData="kpiCapablity"
                    ></talent-classify-matrix>
                </el-col>
                <el-col :span="24">
                    <talent-matrix
                        :headTitle="'潜力'"
                        :asideTitle="'能力'"
                        :matrixHeight="260"
                        :matrixData="developmentCapability"
                    ></talent-matrix>
                </el-col>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
            <!-- <el-col :span="24">
                <div class="item_title">人才发展个人总结</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentDevChange"
                    @handleSizeChange="handleSizeDevChange"
                    :tableData="tableDevData"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf">
                    更多数据请查看网页版报告
                </div>
            </el-col>
            <el-col :span="24">
                <div class="item_title">人才发展上级评价</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentEvaChange"
                    @handleSizeChange="handleSizeEvaChange"
                    :tableData="tableEvaData"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf">
                    更多数据请查看网页版报告
                </div>
            </el-col> -->
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    // import {
    //     orgPersonalDeve,
    //     orgTalentDeve,
    //     performanceMatrix,
    //     potentialMatrix,
    // } from "../../../../request/api";
    import {
        orgTalentDevelopment,
        userDevelopment,
        supDevelopment,
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import talentClassifyMatrix from "@/components/talent/common/talentClassifyMatrix";
    import talentMatrix from "@/components/talent/common/talentMatrix";
    import listComp from "./components/listComp.vue";

    export default {
        name: "orgRDeve",
        props: ["enqId", "orgCode", "isPdf"],
        components: { tableComps, talentClassifyMatrix, talentMatrix,listComp },
        data() {
            return {
                size: 50,
                current: 1,
                kpiRankOption: [],
                competenceRankOptions: [],
                developmentOptions: [],
                developmentCapability: [],
                kpiCapablity: {},
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "员工个人发展规划",
                        elSpan: 6,
                        chartHeight: "200",
                        chartType: "YBar",
                        dataKey: "developmentPlanning",
                    },
                    // {
                    //     chartDomId: this.$util.createRandomId(),
                    //     title: "预计发展周期",
                    //     elSpan: 6,
                    //     chartType: "YBar",
                    //     dataKey: "developmentCycle",
                    // },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "一年内向上发展人员组织分布",
                        elSpan: 18,
                        chartType: "XBar",
                        dataKey: "orgDistribution",
                    },
                    // {
                    //     chartDomId: this.$util.createRandomId(),
                    //     title: "预计一年内向上发展的人群职群分布",
                    //     elSpan: 6,
                    //     height: "260",
                    //     chartType: "YBar",
                    //     dataKey: "jobClassDistribution",
                    // },
                ],
                listArr: [
                    {
                        title: "个人发展总结",
                        ajaxUrl: userDevelopment,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                            {
                                label: "岗位名称",
                                prop: "postName",
                            },
                            {
                                label: "人才类别",
                                prop: "talentType",
                            },
                            {
                                label: "个人期望发展类型",
                                prop: "developmentType",
                            },
                            {
                                label: "个人期望发展岗位",
                                prop: "jobName",
                            },
                            {
                                label: "个人晋升周期期望",
                                prop: "expectationCycle",
                            },
                            {
                                label: "个人短板分析",
                                prop: "analyzeSummarize",
                                width: 200,
                            },
                            {
                                label: "个人发展计划",
                                prop: "developmentPlan",
                                width: 200,
                            },
                        ],
                    },
                    {
                        title: "人才发展上级评价",
                        ajaxUrl: supDevelopment,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                                width: 180
                            },
                            {
                                label: "姓名",
                                prop: "userName",
                                width: 130
                            },
                            {
                                label: "岗位名称",
                                prop: "postName",
                                width: 180
                            },
                            // {
                            //     label: "人才类别",
                            //     prop: "talentType",
                            //     width: 100
                            // },
                            // {
                            //     label: "发展潜力",
                            //     prop: "development_potential",
                            // },
                            {
                                label: "本岗位工作时长",
                                prop: "currentPostAge",
                            },
                            {
                                label: "最近晋升日期",
                                prop: "lastPromotionDate",
                            },
                            {
                                label: "建议个人发展类型",
                                prop: "developmentType",
                            },
                            {
                                label: "建议发展职位",
                                prop: "jobName",
                            },
                            {
                                label: "预计发展周期",
                                prop: "expectationCycle",
                            },
                        ],
                    },
                ],
                tableDevData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "orgName",
                        },
                        {
                            label: "姓名",
                            prop: "userName",
                        },
                        {
                            label: "岗位名称",
                            prop: "postName",
                        },
                        {
                            label: "人才类别",
                            prop: "talentType",
                        },
                        {
                            label: "个人期望发展类型",
                            prop: "developmentType",
                        },
                        {
                            label: "个人期望发展岗位",
                            prop: "jobName",
                        },
                        {
                            label: "个人晋升周期期望",
                            prop: "expectationCycle",
                        },
                        {
                            label: "个人短板分析",
                            prop: "analyzeSummarize",
                        },
                        {
                            label: "个人发展计划",
                            prop: "developmentPlan",
                            width: 300,
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
                tableEvaData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "orgName",
                        },
                        {
                            label: "姓名",
                            prop: "userName",
                        },
                        {
                            label: "岗位名称",
                            prop: "postName",
                        },
                        {
                            label: "人才类别",
                            prop: "talentType",
                        },
                        // {
                        //     label: "发展潜力",
                        //     prop: "development_potential",
                        // },
                        {
                            label: "本岗位工作时长",
                            prop: "currentPostAge",
                        },
                        {
                            label: "最近晋升日期",
                            prop: "lastPromotionDate",
                        },
                        {
                            label: "建议个人发展类型",
                            prop: "developmentType",
                        },
                        {
                            label: "建议发展职位",
                            prop: "jobName",
                        },
                        {
                            label: "预计发展周期",
                            prop: "expectationCycle",
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
            };
        },
        created() {
            let docList = ["KPI_RANK", "COMPETENCE_RANK", "DEVELOPMENT_POTENTIAL"];
            this.$getDocList(docList).then((res) => {
                this.kpiRankOption = this.$util.deepClone(res.KPI_RANK).reverse();
                this.competenceRankOptions = res.COMPETENCE_RANK;
                // this.developmentOptions = res.DEVELOPMENT_POTENTIAL;
            });
            // this.getData();
            // this.orgRiskDetailsFn();
            // this.performanceMatrixFn();
            // this.potentialMatrixFn();
        },
        mounted() {
            this.orgTalentDevelopmentFun();
            this.userDevelopmentFun();
            this.supDevelopmentFun();
        },
        methods: {
            orgTalentDevelopmentFun() {
                orgTalentDevelopment({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then((res) => {
                    if (res.code == 200) {
                        this.initChart(res.data);
                        this.kpiCapablity = res.data.capabilityPerformanceMatrix;
                        this.developmentCapability =
                            res.data.capabilityPotentialMatrix;
                    }
                });
            },
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                        padding:110
                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            userDevelopmentFun() {
                let params = {
                    size: this.tableDevData.page.size,
                    current: this.tableDevData.page.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                userDevelopment(params).then((res) => {
                    if (res.code == 200) {
                        this.tableDevData.data = res.data;
                        this.tableDevData.page = res.page;
                    }
                });
            },
            handleCurrentDevChange(current) {
                this.tableDevData.page.current = current;
                this.userDevelopmentFun();
            },
            handleSizeDevChange(size) {
                this.tableDevData.page.size = size;
                this.userDevelopmentFun();
            },
            supDevelopmentFun() {
                let params = {
                    size: this.tableEvaData.page.size,
                    current: this.tableEvaData.page.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                supDevelopment(params).then((res) => {
                    if (res.code == 200) {
                        this.tableEvaData.data = res.data;
                        this.tableEvaData.page = res.page;
                    }
                });
            },
            handleCurrentEvaChange(current) {
                this.tableEvaData.page.current = current;
                this.supDevelopmentFun();
            },
            handleSizeEvaChange(size) {
                this.tableEvaData.page.size = size;
                this.supDevelopmentFun();
            },
            // ---------
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTalentDeve(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.initChart(res.data);
                    }
                });
            },
            orgRiskDetailsFn() {
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgPersonalDeve(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
            // 能力绩效矩阵
            performanceMatrixFn() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                performanceMatrix(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.kpiCapablity = res.data;
                    }
                });
            },
            // 能力潜力矩阵
            potentialMatrixFn() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                potentialMatrix(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.developmentCapability = res.data;
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>