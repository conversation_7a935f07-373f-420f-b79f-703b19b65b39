<template>
    <div class="edu_info_wrap">
        <div class="page_second_title">
            教育信息
        </div>
        <div class="oper_btn_wrap">
            <el-button class="page_add_btn" type="primary" @click="addItem">新增</el-button>
        </div>
        <div class="edu_info_center">
            <div class="edu_info_header">
                <div class="item">毕业院校</div>
                <div class="item">毕业日期</div>
                <div class="item">学历</div>
                <div class="item">与当前岗位相关</div>
                <div class="item">与当前行业相关</div>
                <div class="item item_icon_wrap">操作</div>
            </div>
            <div class="edu_info_mmain">
                <edu-info-item :eduInfoData="eduInfoData" v-on:deleteItem="deleteItem"></edu-info-item>
                <div class="align_center paddT_12">
                    <el-button class="page_confirm_btn" type="primary" @click="submit">确认</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
 
<script>
import eduInfoItem from "./CSeduInfoItem";
import { addEducation, getEducation, delEducation } from "../../../request/api";

export default {
    name: "CSeduInfo",
    components: {
        eduInfoItem
    },
    data() {
        return {
            staffId:null,
            submitFlag:true,
            eduInfoData: [
                // {
                //     graduateSchool: "学校名字",
                //     graduateDate: "2019-07-01",
                //     qualification: "A",
                //     postRelated: "Y",
                //     industryRelated: "N",
                // }
            ]
        };
    },
    created() {
        // 跟据路由信息判断是编辑还是新增
        this.type = this.$route.name == "editStaff" ? "edit" : "create";
        if (this.type == "edit") {
            console.log("编辑");
            let id = this.$route.query.userId;
            this.staffId = id;
            this.getEducationData();
        }else{
            this.staffId = this.$store.state.createStaffId;
        }
        
    },
    methods: {
        deleteItem(item, index) {
            let that = this;
            if (!item.hasOwnProperty("educationId")) {
                // 以当前行数据是否包含 educationId 字段判断是否是入库的数据
                // 未入库的数据直接删除，不调用接口
                console.log("不调用删除接口，直接删除");
                this.$confirm("确认删除此条信息？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                        this.eduInfoData.splice(index, 1);
                        this.$message({
                            type: "success",
                            message: "删除成功!"
                        });
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除"
                        });
                    });
            } else {
                let educationId = item.educationId;
                // 调用删除接口
                console.log("调用删除接口");
                this.$confirm("确认删除此条信息？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                        delEducation({
                            educationId: educationId,
                            userId: this.staffId
                        }).then(res => {
                            if (res.code == "200") {
                                this.$message({
                                    type: "success",
                                    message: "删除成功!"
                                });
                                // 删除当前数据
                                this.eduInfoData.splice(index, 1);
                            } else {
                                this.$message.error("删除失败！");
                            }
                        });
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除"
                        });
                    });
            }
        },
        addItem() {
            let obj = this.eduInfoData[this.eduInfoData.length - 1];
            let addObj = {
                graduateSchool: "",
                graduateDate: "",
                qualification: "",
                postRelated: "",
                industryRelated: "",
                userId: this.staffId
            };
            if (!obj) {
                this.eduInfoData.push(addObj);
                return;
            }

            if (this.checkData(this.eduInfoData)) {
                this.$message({
                    message: "请完善当前数据后新增！",
                    type: "warning"
                });
                return;
            }
            this.eduInfoData.push(addObj);
        },
        submit() {
            let that = this;
            if (!this.submitFlag) return;
            // 校验数据中有没有空值
            if (this.checkData(this.eduInfoData)) {
                this.$message({
                    message: "请完善数据后提交！",
                    type: "warning"
                });
                return;
            }
            this.submitFlag = false;
            let params = this.eduInfoData;
            addEducation(params).then(res => {
                if (res.code == "200") {
                    this.$message({
                        type: "success",
                        message: "保存成功!"
                    });
                    this.getEducationData();
                    this.submitFlag = true;
                } else {
                    this.submitFlag = true;
                    this.$message.error("保存失败!");
                }
            });
        },
        getEducationData() {
            getEducation({
                current: "1",
                size: "10",
                userId: this.staffId
            }).then(res => {
                if (res.code == "200") {
                    this.eduInfoData = res.data;
                } else {
                    this.$message.error("获取数据失败!");
                }
            });
        },
        checkData(data) {
            // 校验数据 是否有空值
            let arr = data;
            let len = arr.length;
            let verifiKey = ['graduateSchool','graduateDate','qualification','postRelated','industryRelated'];
            // 校验数据中有没有空值
            for (let index = 0; index < len; index++) {
                const obj = arr[index];
                if (this.$util.objHasEmpty(obj,verifiKey)) {
                    // 检测到有空值跳出遍历
                    console.log("有空值");
                    return true;
                } else {
                    console.log("没有空值");
                    // return true;
                }
            }
        }
    }
};
</script>
 
<style scoped lang="scss">
    .page_second_title{
        margin: 10px 0;
        padding-bottom: 10px;
        border-bottom: 1px solid #e5e5e5;
        position: relative;
    }
    .edu_info_wrap{
        width: 100%;
        overflow: hidden;
    }
.edu_info_header {
    .item {
        width: 23%;

    }
    .item_icon_wrap {
        text-align: center;
        width: 10%;
    }
}
</style>