<script setup>
import simplenessTable from '@/components/table/simplenessTable.vue'
import { manageViewDetailAppDesc } from '@/assets/data/manageView.js'
defineOptions({ name: 'appDesc' })
const route = useRoute()
const id = route.query.id
const title = route.query.title
console.log('id', id)
const descData = manageViewDetailAppDesc[id]
console.log(descData)
const columns = ref([
  {
    prop: 'type',
    label: '痛点类型',
    width: '160'
  },
  {
    prop: 'solve',
    label: '解决痛点',
    width: '350'
  },
  {
    prop: 'value',
    label: '核心价值'
  }
])

const columns2 = ref([
  {
    prop: 'role',
    label: '用户角色',
    width: '160'
  },
  {
    prop: 'solve',
    label: '解决痛点',
    width: '350'
  },
  {
    prop: 'scene',
    label: '场景',
    width: '160'
  },
  {
    prop: 'feature',
    label: '使用功能',
    width: '160'
  },
  {
    prop: 'procedure',
    label: '操作步骤'
  }
])

const router = useRouter()
const toAppDetail = () => {
  router.push({ path: '/manageView/detail/content', query: route.query })
}
</script>
<template>
  <div class="view-detail-wrap">
    <div class="page-title-line">应用描述</div>
    <div class="detail-location">{{ descData.desc }}</div>
    <div class="page-title-line">解决痛点与核心价值</div>
    <simplenessTable class="table" :columns="columns" :data="descData.solve"></simplenessTable>
    <div class="page-title-line">典型用户与场景</div>
    <simplenessTable class="table" :columns="columns2" :data="descData.scene"></simplenessTable>
    <div class="view-btn" @click="toAppDetail">查看</div>
  </div>
</template>
<style lang="scss" scoped>
.view-detail-wrap {
  .page-title-line {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .detail-location {
    font-size: 16px;
    color: #3d3d3d;
    line-height: 24px;
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 16px;
    margin-bottom: 30px;
  }
  .table {
    margin-bottom: 30px;
  }
  .view-btn {
    width: 269px;
    line-height: 45px;
    text-align: center;
    background: #53a9f9;
    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    margin: 0 auto;
  }
}
</style>
