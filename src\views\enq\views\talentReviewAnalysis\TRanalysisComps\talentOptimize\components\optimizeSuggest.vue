<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section">
      <div class="content_item_title marginB_16 clearfix">
        人员分布
        <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
      </div>
      <tableComponent
        :needIndex="true"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
        :size="'small'"
        :tableData="tableData"
      ></tableComponent>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { staffDistribute, queryCompetenceList, exportData } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const route = useRoute()
const enqId = ref(route.query.enqId)
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref([])
const pageSize = ref(10)
const currPage = ref(1)

const tableData = reactive({
  columns: [
    {
      label: '部门名称',
      prop: 'org_name'
    },
    {
      label: '姓名',
      prop: 'user_name'
    },
    {
      label: '岗位',
      prop: 'post_name'
    },
    {
      label: '绩效',
      prop: 'kpi'
    },
    {
      label: '能力',
      prop: 'competence'
    },
    {
      label: '潜力',
      prop: 'development',
      className: 'text_center'
    },
    {
      label: '分类',
      prop: 'talent'
    },
    {
      label: '晋升',
      prop: 'promotion',
      className: 'text_center'
    },
    {
      label: '离职风险',
      prop: 'retention',
      className: 'text_center'
    },
    {
      label: '离职影响',
      prop: 'dimission',
      className: 'text_center'
    }
  ],
  data: [],
  page: {}
})

const staffDistributeFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      size: pageSize.value,
      current: currPage.value,
      type: 1
    }
    const res = await queryCompetenceList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error('获取人员分布数据失败:', error)
  }
}

const getCode = (orgCode, jobClassCode) => {
  jobClassCode.value = jobClassCode
  orgCode.value = orgCode
  currPage.value = 1
  staffDistributeFun()
}

const handleSizeChange = size => {
  pageSize.value = size
  staffDistributeFun()
}

const handleCurrentChange = currPage => {
  currPage.value = currPage
  staffDistributeFun()
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'n'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '人员优化建议详情列表')
  } catch (error) {
    console.error('导出数据失败:', error)
  }
}

onMounted(() => {
  staffDistributeFun()
  filterData.value = route.attrs.filterData
})
</script>

<style scoped lang="scss">
.text_center {
  text-align: center !important;
}
</style>
