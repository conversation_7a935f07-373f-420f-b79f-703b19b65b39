<template>
  <div class="job_grade_wrap rDataPer_kpi_wrap bg_write">
    <div class="page_main_title flex_row_betweens">
      <div class="title flex_row_start">
        <p>盘点数据查看</p>
        <div class="check_title" v-if="enqName"><span>/</span>{{ enqName }}</div>
        <div class="check_title"><span>/</span>个人盘点数据-KPI</div>
      </div>
      <div class="goback_geader" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section">
      <div class="page_section job_grade_center clearfix">
        <div class="filter_bar_wrap">
          <div class="flex_row_start">
            <div class="filter_item title">筛选</div>
            <div class="filter_item">
              <el-input v-model="userName" placeholder="按姓名模糊查询" clearable></el-input>
            </div>
            <div class="filter_item">
              <el-input v-model="kpiName" placeholder="按指标名称模糊查询" clearable></el-input>
            </div>
            <div class="filter_item">
              <el-button class="page_add_btn" type="primary" @click="getTargetDataFun">查询</el-button>
            </div>
          </div>
          <div class="filter_item">
            <el-button class="page_add_btn" type="primary" @click="exportData">导出</el-button>
          </div>
        </div>
        <table-component :needIndex="true" :tableData="tableData" :needPagination="false"> </table-component>
      </div>
    </div>
  </div>
</template>

<script>
import tableComponent from '@/components/talent/tableComps/tableComponent'
import { getPersonalKpi, exportDataConfirm, exportListDownload } from '../../../request/api'
export default {
  name: 'rDataPerKpi',
  components: {
    tableComponent
  },
  props: [''],
  data() {
    return {
      enqId: this.$route.query.enqId,
      enqName: this.$route.query.enqName,
      userName: '',
      kpiName: '',
      tableData: {
        columns: [
          {
            label: '一级组织',
            prop: 'oneLevelName'
          },
          {
            label: '二级组织',
            prop: 'twoLevelName'
          },
          {
            label: '三级组织',
            prop: 'threeLevelName'
          },
          {
            label: '四级组织',
            prop: 'fourLevelName'
          },
          {
            label: '姓名',
            prop: 'userName'
          },
          {
            label: '岗位',
            prop: 'postName'
          },
          {
            label: '指标名称',
            prop: 'kpiName'
          },
          {
            label: '目标',
            prop: 'kpiObjectiveName'
          },
          {
            label: '评价标准',
            prop: 'kpiEvaluationStandard'
          },
          {
            label: '实际表现',
            prop: 'kpiActual'
          },
          {
            label: '指标权重',
            prop: 'kpiWeight'
          }
        ],
        data: [],
        page: {
          current: 1,
          size: 10,
          total: 0
        }
      }
    }
  },
  mounted() {
    this.getPersonalKpiFun()
  },
  methods: {
    getPersonalKpiFun() {
      getPersonalKpi({
        enqId: this.enqId,
        kpiName: this.kpiName,
        userName: this.userName
      }).then(res => {
        if (res.code == 200) {
          this.tableData.data = res.data
        }
      })
    },
    getTargetDataFun() {
      this.getPersonalKpiFun()
    },
    exportData() {
      this.exportDataConfirmFun()
    },
    exportDataConfirmFun() {
      exportDataConfirm({
        enqId: this.enqId,
        type: 'c'
      }).then(res => {
        this.exportDownloadFun(res)
      })
    },
    exportDownloadFun(res) {
      const blob = new Blob([res])
      const elink = document.createElement('a')
      elink.download = this.enqName + '-个人盘点数据-KPI列表.xlsx'
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    }
  }
}
</script>

<style scoped lang="scss"></style>
