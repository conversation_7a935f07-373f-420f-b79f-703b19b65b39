<template>
  <div class="talent_review_home page_container clearfix">
    <div class="page_main bg_write">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'
import { getUserInfo } from '../request/api'
const store = useUserStore()

const getUserInfoFun = async () => {
  try {
    const res = await getUserInfo()
    if (res.code == '200') {
      store.setUserInfo(res.data)
    } else {
      ElMessage.error('获取用户信息失败！')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取用户信息失败！')
  }
}

onMounted(() => {
  getUserInfoFun()
})
</script>

<style lang="scss">
@import '@/styles/talent.scss';

.talent_review_home {
  height: 100%;
}
.page_aside_title {
  line-height: 70px;
  font-size: 20px;
  color: #333;
  font-weight: bold;
  padding-left: 16px;
}

.aside_nav_wrap {
  width: 240px;
  border: none;
}
</style>
