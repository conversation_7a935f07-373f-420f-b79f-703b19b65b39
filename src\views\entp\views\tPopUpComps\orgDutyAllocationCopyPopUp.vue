<template>
  <el-dialog
    title="从其它组织复制职责"
    v-model="dialogVisible"
    @close="$emit('update:showDAC', false)"
    width="70%"
    center
  >
    <div class="page_section">
      <div class="page_section_aside">
        <div class="aside_tree_title flex_row_between">
          <div class="overflow_elps tree_title" :title="treeTitle">{{ treeTitle }}</div>
        </div>
        <div class="aside_tree_list">
          <tree-comp-radio v-model="defaultCheckedKeys" :treeData="treeData" @clickCallback="clickCallback" />
        </div>
      </div>
      <div class="page_section_main page_shadow">
        <table-component :tableData="dutyTableData" :needIndex="false" :needPagination="false" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer align_right">
        <el-button class="page_clear_btn" @click="cancel">取 消</el-button>
        <el-button class="page_add_btn" type="primary" @click="submitBtn">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'
import { getOrgDeptTree, getOrgRespList, setOrgRespCopy } from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import tableComponent from '@/components/talent/tableComps/tableComponent'

// Props定义
const props = defineProps({
  showDAC: {
    type: Boolean,
    default: false
  },
  checkedId: {
    type: String,
    required: true
  }
})

// Emits定义
const emit = defineEmits(['update:showDAC', 'copyDutySign'])

// Store
const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)
const treeTitle = computed(() => userStore.companyInfo.companyName)

// 响应式状态
const dialogVisible = computed({
  get: () => props.showDAC,
  set: value => emit('update:showDAC', value)
})

const treeData = ref([])
const defaultCheckedKeys = ref('')
const dutyTableData = ref({
  columns: [
    {
      label: '职责名称',
      prop: 'respName'
    },
    {
      label: '职责描述',
      prop: 'respDesc'
    }
  ],
  data: []
})
const popupCheckedId = ref('')

// 方法定义
const getOrgTreeFun = async () => {
  try {
    const res = await getOrgDeptTree({
      companyId: companyId.value
    })

    if (res.code == 200) {
      treeData.value = res.data.length > 0 ? res.data : []
      disabledNodesChecked(res.data, props.checkedId)
    } else {
      treeData.value = []
    }
  } catch (error) {
    console.error('获取组织树失败:', error)
    ElMessage.error('获取组织树失败')
  }
}

const disabledNodesChecked = (nodes, disabledNode) => {
  if (nodes.length > 0) {
    nodes.forEach(item => {
      if (item.code == disabledNode) {
        item.disabled = true
        return
      }
      if (item.children?.length > 0) {
        disabledNodesChecked(item.children, disabledNode)
      }
    })
  }
}

const clickCallback = async (val, isLastNode) => {
  if (val) {
    popupCheckedId.value = val
    await getOrgRespListFun()
  } else {
    dutyTableData.value.data = []
  }
}

const getOrgRespListFun = async () => {
  try {
    const res = await getOrgRespList({
      orgCode: popupCheckedId.value
    })

    dutyTableData.value.data = []
    if (res.code == 200) {
      dutyTableData.value.data = res.data
    }
  } catch (error) {
    console.error('获取职责列表失败:', error)
    ElMessage.error('获取职责列表失败')
  }
}

const cancel = () => {
  dialogVisible.value = false
}

const submitBtn = async () => {
  try {
    emit('copyDutySign', false)
    const res = await setOrgRespCopy({
      fromOrgCode: popupCheckedId.value,
      toOrgCode: props.checkedId
    })

    if (res.code == 200) {
      emit('copyDutySign', true)
      dialogVisible.value = false
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('复制职责失败:', error)
    ElMessage.error('复制职责失败')
  }
}

// 监听
watch(
  companyId,
  val => {
    if (val) {
      getOrgTreeFun()
    }
  },
  { immediate: true }
)

watch(
  () => props.showDAC,
  val => {
    if (!val) {
      dutyTableData.value.data = []
      defaultCheckedKeys.value = ''
    }
    getOrgTreeFun()
  }
)
</script>

<style scoped>
.page_section {
  display: flex;
  height: 500px;
}

.page_section_aside {
  width: 280px;
  border-right: 1px solid #dcdfe6;
  margin-right: 20px;
}

.aside_tree_title {
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.tree_title {
  font-weight: bold;
  color: #303133;
}

.aside_tree_list {
  height: calc(100% - 41px);
  overflow-y: auto;
  padding: 10px;
}

.page_section_main {
  flex: 1;
  overflow-y: auto;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

:deep(.el-dialog__header) {
  background-color: var(--color-tagbg);
  padding: 15px 20px;
}
</style>
