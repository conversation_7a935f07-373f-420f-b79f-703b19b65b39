<template>
  <div class="business_area_center clearfix">
    <div class="oper_btn_wrap marginB_16 align_right">
      <el-button class="page_add_btn" type="primary"  @click="addItem"> 新增 </el-button>
    </div>
    <div class="edu_info_header flex_row_betweens">
      <div class="item desc">职责描述</div>
      <div class="item">时间占比(总和100%)</div>
      <div class="item item_icon_wrap">操作</div>
    </div>
    <div class="edu_info_mmain">
      <div v-if="dutyData.length > 0">
        <div class="edu_info_item flex_row_betweens" v-for="(item, index) in dutyData" :key="index">
          <el-input class="item desc" v-model="item.respDesc" placeholder="请输入职责描述" />
          <div class="time_percent_wrap">
            <el-input
              class="item"
              type="number"
              v-model.number="item.timePercentage"
              :min="0"
              placeholder="请选择时间占比"
            />
            <span class="unit">%</span>
          </div>
          <div class="item item_icon_wrap">
            <el-icon class="item_icon icon_del" @click="deleteItem(item.postCode, item.postRespId, index)">
              <Delete />
            </el-icon>
          </div>
        </div>
      </div>
      <div class="align_center paddT_12">
        <el-button class="page_confirm_btn" type="primary" @click="submit"> 确认 </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { useStore } from 'vuex'
import { createPostResp, deletePostResp, getPostRespList } from '../../../../request/api'

// Props
const props = defineProps({
  curPostCodeCopy: String
})

// Emits
const emit = defineEmits(['submitSuccessTab'])

// Store
const store = useStore()

// Computed
const companyId = computed(() => store.state.userInfo.companyId)

// Reactive State
const postCode = ref(props.curPostCodeCopy)
const dutyData = ref([])

// Methods
const getPostRespListFun = async () => {
  try {
    const res = await getPostRespList({
      postCode: postCode.value
    })

    if (res.code == 200) {
      if (res.data.length > 0) {
        dutyData.value = res.data
      } else {
        dutyData.value = [
          {
            companyId: companyId.value,
            postCode: postCode.value,
            respDesc: '',
            timePercentage: '',
            postRespId: '',
            sortNbr: ''
          }
        ]
      }
    } else {
      dutyData.value = []
    }
  } catch (error) {
    console.error('获取职责列表失败:', error)
    ElMessage.error('获取职责列表失败')
  }
}

const addItem = () => {
  const addObj = {
    companyId: companyId.value,
    postCode: postCode.value,
    respDesc: '',
    timePercentage: '',
    postRespId: '',
    sortNbr: ''
  }

  if (dutyData.value.length > 0) {
    const lastItem = dutyData.value[dutyData.value.length - 1]
    if (!lastItem.respDesc || !lastItem.timePercentage) {
      ElMessage.warning('请完善当前信息后新增！')
      return
    }
  }

  dutyData.value.push(addObj)
}

const createPostRespFun = async () => {
  try {
    const res = await createPostResp(dutyData.value)
    if (res.code == 200) {
      emit('submitSuccessTab', 'duty')
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('创建职责失败:', error)
    ElMessage.error('创建职责失败')
  }
}

const submit = () => {
  if (dutyData.value.length == 0) {
    ElMessage.warning('请新增后提交！')
    return
  }

  const lastItem = dutyData.value[dutyData.value.length - 1]
  if (!lastItem.respDesc || !lastItem.timePercentage) {
    ElMessage.warning('请完善信息后提交！')
    return
  }

  const timePercentTotal = dutyData.value.reduce((total, item) => total + (item.timePercentage || 0), 0)

  if (timePercentTotal > 100) {
    ElMessage.warning('时间占比总和不能大于100%！')
    return
  }

  createPostRespFun()
}

const deleteItem = async (postCode, postId, index) => {
  try {
    await ElMessageBox.confirm('确认删除?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    if (postId) {
      await deletePostRespFun(postCode, postId)
    } else {
      dutyData.value.splice(index, 1)
    }
  } catch (error) {
    // User cancelled the operation
  }
}

const deletePostRespFun = async (postCode, postId) => {
  try {
    const res = await deletePostResp({
      postCode: postCode,
      postRespId: postId
    })

    if (res.code == 200) {
      await getPostRespListFun()
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('删除职责失败:', error)
    ElMessage.error('删除职责失败')
  }
}

// Watchers
watch(
  () => companyId.value,
  val => {
    if (val) {
      getPostRespListFun()
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.edu_info_header {
  .item {
    width: 20%;

    &.desc {
      width: 65%;
    }
  }

  .item_icon_wrap {
    text-align: center;
    width: 5%;
  }
}

.business_area_center {
  .edu_info_item {
    .item {
      width: 20%;
    }

    .desc {
      width: 67%;
    }

    .time_percent_wrap {
      width: 23%;

      .item {
        margin: 0 5% 0 0;
        width: 80%;
      }
    }

    .item_icon_wrap {
      text-align: center;
      width: 5%;

      .item_icon {
        font-size: 20px;
        cursor: pointer;
      }
    }
  }
}
</style>
