<script setup>
defineOptions({ name: 'zlnlpg' })
const stepList = ref([
  {
    id: 'scene',
    title: '了解评估场景',
    comp: defineAsyncComponent(() => import('./zlnlpg/scene.vue'))
  },
  {
    id: 'model',
    title: '查看评估模型',
    comp: defineAsyncComponent(() => import('./zlnlpg/model.vue'))
  },
  {
    id: 'step',
    title: '了解评估步骤',
    comp: defineAsyncComponent(() => import('./zlnlpg/step.vue'))
  },
  {
    id: 'report',
    title: '了解评估报告',
    comp: defineAsyncComponent(() => import('./zlnlpg/report.vue'))
  },
  {
    id: 'evaluate',
    title: '开始能力测评',
    comp: defineAsyncComponent(() => import('./zlnlpg/evaluate.vue')),
    query: {
      processId: 'example'
    }
  },
  {
    id: 'viewReport',
    title: '查看评估报告',
    comp: defineAsyncComponent(() => import('./zlnlpg/viewReport.vue'))
  },
  {
    id: 'risk',
    title: '能力短板风险',
    comp: defineAsyncComponent(() => import('./zlnlpg/risk.vue'))
  },
  {
    id: 'measure',
    title: '能力提升举措',
    comp: defineAsyncComponent(() => import('./zlnlpg/measure.vue'))
  },
  {
    id: 'plan',
    title: '推荐行动计划',
    comp: defineAsyncComponent(() => import('./zlnlpg/plan.vue'))
  }
])

const router = useRouter()
const route = useRoute()
console.log(route)
const activeStepId = ref(route.query.stepId || stepList.value[0].id)

const activeStep = computed(() => {
  return stepList.value.find(item => item.id == activeStepId.value)
})

router.push({ path: route.path, query: { ...route.query, stepId: activeStepId.value } })
const changeStep = (item, index) => {
  activeStepId.value = item.id
  router.push({ path: route.path, query: { ...route.query, stepId: item.id, ...item.query } })
}

const next = () => {
  // activeStep.value = activeStep.value + 1
  // activeStepId.value = stepList.value[activeStep.value].id
  // router.push({ path: route.path, query: { ...route.query, stepId: activeStepId.value } })
}
</script>
<template>
  <div class="page-content">
    <div class="content-aside">
      <div class="aside-title">建议步骤 :</div>
      <div
        class="step-list"
        :class="{ active: activeStepId == step.id }"
        v-for="(step, index) in stepList"
        :key="step.id"
        @click="changeStep(step, index)"
      >
        {{ step.title }}
      </div>
    </div>
    <div class="content-mian">
      <component :is="activeStep.comp" @next="next" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.page-content {
  display: flex;
  align-items: flex-start;
  .content-aside {
    flex: 0 0 200px;
    padding: 24px 20px;
    background: linear-gradient(224deg, #d0e4f9 0%, rgba(195, 230, 255, 0.6) 100%);
    border-radius: 8px 8px 8px 8px;
    margin-right: 16px;
    .aside-title {
      font-size: 14px;
      color: #333333;
      line-height: 35px;
    }
    .step-list {
      width: 100%;
      line-height: 33px;
      background: #f0f9ff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #a5c1dc;
      text-align: center;
      cursor: pointer;
      margin-bottom: 10px;

      &.active {
        background: #83c1ff;
        border: 1px solid #83c1ff;
        color: #ffffff;
      }
    }
  }
  .content-mian {
    flex: 1;
  }
}
:deep(.next-btn) {
  width: 269px;
  line-height: 45px;
  font-size: 16px;
  color: #ffffff;
  text-align: center;
  background: #53a9f9;
  border-radius: 8px 8px 8px 8px;
  margin: 0 auto;
  cursor: pointer;
}
</style>
