export default {
	createRandomId: function() {
		return "_" + Math.random().toString(36).substr(2);
	},
	// 格式化时间
	formatter: function(thistime, fmt) {
		let $this = new Date(thistime)
		let o = {
			'M+': $this.getMonth() + 1,
			'd+': $this.getDate(),
			'h+': $this.getHours(),
			'm+': $this.getMinutes(),
			's+': $this.getSeconds(),
			'q+': Math.floor(($this.getMonth() + 3) / 3),
			'S': $this.getMilliseconds()
		}
		if (/(y+)/.test(fmt)) {
			fmt = fmt.replace(RegExp.$1, ($this.getFullYear() + '').substr(4 - RegExp.$1.length))
		}
		for (var k in o) {
			if (new RegExp('(' + k + ')').test(fmt)) {
				fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
			}
		}
		return fmt
	},
	// 浏览器回退函数
	goback: function() {
		// window.history.go(-1);  
		window.history.back();
	},
	// 判断是否是空对象
	isEmptyObj: function(obj) {
		for (var key in obj) {
			if ({}.hasOwnProperty.call(obj, key)) return false;
		}
		return true;
	},
	objHasEmpty(obj, verifiKey, ignoreKey) {
		// verifiKey : Array 校验必填字段、非空；不传默认所有必填（若后端返回的其他非展示用的字段为空时，会有影响，建议传入必填字段）
		// ignoreKey: Array 忽略校验的字段
		// 校验对象是否有空值，
		// 有 返回 ture 反之为 false(undefinde)
		ignoreKey = ignoreKey ? ignoreKey : [];
		if (!verifiKey || verifiKey.length == 0 ) {
			for (const key in obj) {
				if (ignoreKey.indexOf(key) == -1) {
					if (obj.hasOwnProperty(key)) {
						const element = obj[key];
						if ((Array.isArray(element) && element.length == 0) || (Object.prototype.toString.call(element) ==
							'[object Object]' && this.isEmptyObj(element)) || (!element && element !== 0)) {
							// 空数组   空对象  空值
							return true;
						}
						// if (Array.isArray(element) && element.length == 0) {
						//     // 空数组
						//     return true;
						// }
						// if (Object.prototype.toString.call(element) == '[object Object]' && this.isEmptyObj(element)) {
						//     //  空对象
						//     return true;
						// }
						// if (!element) {
						//     return true;
						// }
					}
				} 
			}
		} else {
			for (const key in obj) {
				if (verifiKey.indexOf(key) !== -1) {

					if (obj.hasOwnProperty(key)) {
						const element = obj[key];
						if ((Array.isArray(element) && element.length == 0) || (Object.prototype.toString.call(element) ==
							'[object Object]' && this.isEmptyObj(element)) || (!element && element !== 0)) {
							// 空数组   空对象  空值
							return true;
						}
						// if (Array.isArray(element) && element.length == 0) {
						//     // 空数组
						//     return true;
						// }
						// if (Object.prototype.toString.call(element) == '[object Object]' && this.isEmptyObj(element)) {
						//     //  空对象
						//     return true;
						// }
						// if (!element) {
						//     return true;
						// }
					}
				}
			}
		}

	},

	// 查找返回数组中的对象符合条件的一项，并返回该项
	findArrItem: function(arr, key, val) {
		var res = '';
		if (!(arr instanceof Array)) {
			console.warn('请传入数组')
			return
		}
		const findItem = function(arr) {
			for (const item of arr) {
				if (item[key] == val) {
					res = item;
					break;
				} else if (item.children && item.children.length > 0) {
					if (res == '') {
						findItem(item.children)
					}
				}
			}
		}
		findItem(arr);
		return res;
	},
	randomColor: function() {
		var r = Math.floor((Math.random() * 10) * 256);
		var g = Math.floor((Math.random() * 10) * 256);
		var b = Math.floor((Math.random() * 10) * 256);
		var color =
			"#" +
			r.toString(16) +
			g.toString(16) +
			b.toString(16);
		color = color.substring(0, 7)
		return color;

	},
	// 深拷贝对象
	deepClone: function(target) {
		// 定义一个变量
		let result;
		// 如果当前需要深拷贝的是一个对象的话
		if (typeof target === 'object') {
			// 如果是一个数组的话
			if (Array.isArray(target)) {
				result = []; // 将result赋值为一个数组，并且执行遍历
				for (let i in target) {
					// 递归克隆数组中的每一项
					result.push(this.deepClone(target[i]))
				}
				// 判断如果当前的值是null的话；直接赋值为null
			} else if (target === null) {
				result = null;
				// 判断如果当前的值是一个RegExp对象的话，直接赋值    
			} else if (target.constructor === RegExp) {
				result = target;
			} else {
				// 否则是普通对象，直接for in循环，递归赋值对象的所有值
				result = {};
				for (let i in target) {
					result[i] = this.deepClone(target[i]);
				}
			}
			// 如果不是对象的话，就是基本数据类型，那么直接赋值
		} else {
			result = target;
		}
		// 返回最终结果
		return result;
	},
    getBase64(file) {
        return new Promise(function (resolve, reject) {
            let reader = new FileReader();
            let imgResult = "";
            reader.readAsDataURL(file);
            reader.onload = function () {
                imgResult = reader.result;
            };
            reader.onerror = function (error) {
                reject(error);
            };
            reader.onloadend = function () {
                resolve(imgResult);
            };
        });
	},
	// 格式化数据，删除属性children空数组
	formatterData(arr) {
		const reduceFun = function (list) {
			list.forEach((item) => {
				if (item.children.length > 0) {
					reduceFun(item.children);
				} else {
					// item.children = null;
					delete item.children;
				}
			});
		};
		reduceFun(arr);
		return arr;
	},
	//
	/**
	 * 格式化数据，删除属性 'attrKey' 空数组
	 * 用于级联组件，空数组会显示空的面板
	 * 
	 * @param {Array} arr 需要处理的数组
	 * @param {string} [attrKey='children'] 需要处理的属性，默认为 children
	 * @return {Array} 处理后的数组
	 */
	removeEmptyChildren(arr, attrKey = 'children') {
		if (!Array.isArray(arr)) {
			console.error('The arr type must be an Array')
			return
		}
		const reduceFun = function (list) {
			list.forEach((item) => {
				if (item.children.length > 0) {
					reduceFun(item[attrKey]);
				} else {
					// item.children = null;
					delete item[attrKey];
				}
			});
		};
		reduceFun(arr);
		return arr;
	},
	// C、U、R、D为接口操作
	// 返回action 类型是否为接口操作
	isAsyncAction(type) {
		let typeKey = type.split("")[type.length-1];
		let apiArr = ['C', 'U', 'R', 'D'];
		return apiArr.includes(typeKey);
		// if (apiArr.includes(type)) {
		// 	return true
		// } else {
		// 	return false
		// }
	},
	// 处理图表value为百分数
	getPercent(arr,formatterKey,sum) {
		if (arr.length > 0) {
			arr.map(item => {
				item[formatterKey] = ((item.value / sum * 100).toFixed(2)) + "%";
			})
		}
		return arr;
	},
	// 添加百分号
	addPercentSign(arr,key) {
		if (arr.length > 0) {
			arr.map(item => {
				item[key] = item[key] + "%";
			})
		}
		return arr;
	},

    cellMerging(data){
        function getRowspan(obj){
            if (obj.children.length == 0) {
                obj.rowspan = 1;
                return 1;
            } else {
                let count = 0;
                for (let i = 0; i < obj.children.length; i++) {
                    count += getRowspan(obj.children[i]);
                }
                obj.rowspan = count;
                return count;
            }
        }

        for(let i = 0;i < data.length;i++){
            getRowspan(data[i])
        }

        return data;
	},
	compareDate(date1,date2){
		// 日期大小比较
		// 日期格式 "2007-2-2 7:30"
		return ((new Date(date1.replace(/-/g,"\/"))) > (new Date(date2.replace(/-/g,"\/"))));
	},
	debounce(fn, delay) {
		/**
		 * 防抖，在某个时间间隔内，事件处理函数只执行一次
		 * @param {*} fn 需要执行的函数
		 * @param {*} delay 时间间隔
		 */
		let timer = null;
		return function () {
			if (timer) {
				clearTimeout(timer)
			}
			timer = setTimeout(fn(),delay)
		}
	},
	//截取时间到小时
	cutTimeToHour(value){
		let arr;
		if(value){
			arr = value.split(":");
			return arr[0]+"时";
		}
	},
	// 是否是函数
	isFunction(fn) {
		return Object.prototype.toString.call(fn) === '[object Function]';
	}
}
