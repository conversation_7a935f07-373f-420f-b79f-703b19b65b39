<template>
  <div class="edu_info_wrap performance_info_main">
    <div class="clearfix">
      <div class="page_second_title marginT_30">
        个人指标完成
        <div class="change_post fr">
          <div class="kpi_list_wrap flex_row_end">
            <div class="post_title">指标类型</div>
            <div
              class="kpi_list"
              :class="{ active: kpiType == item.kpi_cycle }"
              @click="kpiTypeChange(item.kpi_cycle)"
              v-for="item in kpiTypeOptions"
              :key="item.kpi_cycle"
            >
              {{ item.kpi_codeName }}
            </div>
          </div>
          <!-- <el-select size="mini" v-model="kpiType" @change="kpiTypeChange">
						<el-option v-for="item in kpiTypeOptions" :key="item.kpi_cycle" :label="item.kpi_codeName" :value="item.kpi_cycle">
						</el-option>
					</el-select> -->
        </div>
      </div>
      <div class="kpi_header flex_row_between marginT_16">
        <div class="kpi_header_item name">指标名称</div>
        <div class="kpi_header_item unit">单位</div>
        <div class="kpi_header_item postName">岗位名称</div>
        <div class="kpi_header_item" v-for="item in columns" :key="item.assessmentDate">
          <div class="kpi_header_item_title">{{ item.assessmentDate }}</div>
          <div class="flex_row_around">
            <span>目标</span>
            <span>实际</span>
          </div>
        </div>
      </div>
      <div class="kpi_content_wrap">
        <div
          class="kpi_content_item edu_info_item flex_row_between"
          v-for="(item, index) in enqUserKpiData"
          :key="item.userId"
        >
          <div class="kpi_content name">{{ item.kpiName }}</div>
          <div class="kpi_content unit">{{ item.kpiUnit }}</div>
          <div class="kpi_content postName">{{ item.postName }}</div>
          <div class="kpi_content" v-for="(list, index) in item.enqUserKpiDate" :key="index">
            <div class="flex_row_between">
              <el-input class="kpi_ipt" type="number" v-model="list.kpiGoal"></el-input>
              <el-input class="kpi_ipt" type="number" v-model="list.kpiScore"></el-input>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="align_center marginT_30">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useStore } from 'vuex'
import { getEnqUserKpi, updateEnqUserKpi, getKpiCycle } from '../../../request/api'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])
const store = useStore()

const kpiType = ref(null)
const kpiTypeOptions = ref([])
const columns = ref([])
const enqUserKpiData = ref([])
const backupData = ref({})

const userId = computed(() => store.state.userInfo.userId)

watch(
  enqUserKpiData,
  val => {
    if (!backupData.value[kpiType.value]) backupData.value[kpiType.value] = {}
    backupData.value[kpiType.value].enqUserKpiData = val
  },
  { deep: true }
)

watch(
  columns,
  val => {
    if (!backupData.value[kpiType.value]) backupData.value[kpiType.value] = {}
    backupData.value[kpiType.value].columns = val
  },
  { deep: true }
)

onMounted(() => {
  getKpiCycleFun()
})

function getEnqUserKpiFun(kpiTypeVal) {
  let params = {
    enqId: props.enqId,
    kpiCycle: kpiTypeVal
  }
  backupData.value[kpiTypeVal] = {}
  getEnqUserKpi(params).then(res => {
    if (res.code == '200') {
      backupData.value[kpiTypeVal].enqUserKpiData = res.data.enqUserKpi
      backupData.value[kpiTypeVal].columns = res.data.enqUserKpiColumn
      enqUserKpiData.value = backupData.value[kpiType.value].enqUserKpiData
      columns.value = backupData.value[kpiType.value].columns
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function getKpiCycleFun() {
  let params = {
    enqId: props.enqId,
    userId: userId.value
  }
  getKpiCycle(params).then(res => {
    if (res.code == '200') {
      kpiTypeOptions.value = res.data
      kpiType.value = kpiTypeOptions.value[0].kpi_cycle
      kpiTypeOptions.value.forEach(item => {
        let kpiTypeVal = item.kpi_cycle
        getEnqUserKpiFun(kpiTypeVal)
      })
    }
  })
}

function kpiTypeChange(val) {
  kpiType.value = val
  if (backupData.value[val]) {
    enqUserKpiData.value = backupData.value[val].enqUserKpiData
    columns.value = backupData.value[val].columns
  } else {
    getEnqUserKpiFun(val)
  }
}

function submit(stepType) {
  let resultArr = []
  for (const key of Object.keys(backupData.value)) {
    let list = backupData.value[key].enqUserKpiData
    list.forEach(item => {
      let kpidata = item.enqUserKpiDate
      resultArr = resultArr.concat(kpidata)
    })
  }
  if (checkData(resultArr)) {
    ElMessage.warning('请完善数据后提交')
    return
  }
  updateEnqUserKpi(resultArr).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function prevBtn() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submit('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}

function nextBtn() {
  submit('nextStep')
}

function checkData(data) {
  for (let index = 0; index < data.length; index++) {
    const obj = data[index]
    if (!obj.kpiGoal || !obj.kpiScore) {
      return true
    }
  }
  return false
}
</script>

<style scoped lang="scss">
.edu_info_wrap {
  margin-bottom: 16px;
}

.change_post {
  margin-left: 16px;
  .post_title {
    // display: inline-block;
    font-size: 14px;
    margin-right: 16px;
  }
  .kpi_list_wrap {
    align-items: center;
  }
  .kpi_list {
    color: #449cff;
    border: 1px solid #449cff;
    padding: 0px 5px;
    line-height: 20px;
    height: 25px;
    border-radius: 3px;
    margin: 0 8px;
    background: #fff;
    cursor: pointer;
    &.active {
      background: #449cff;
      color: #fff;
    }
  }
}

.edu_info_center {
  width: 60%;
}

.kpi_header {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  background: #f4f4f4;
  color: #525e6c;
  font-size: 14px;
  padding: 0 16px;
  height: 56px;
  line-height: 20px;
  text-align: center;
  .kpi_header_item {
    width: 15%;
    &.name {
      text-align: left;
    }

    &.unit {
      width: 50px;
    }

    &.postName {
      width: 100px;
      text-align: left;
    }
  }

  .kpi_header_item_title {
    font-size: 14px;
    margin-bottom: 4px;
  }
}

.kpi_content_wrap {
  font-size: 12px;
  .kpi_content_item {
    .kpi_content {
      width: 15%;
      margin-left: 5px;
      .kpi_ipt:first-of-type {
        margin-right: 5px;
      }

      &.unit {
        width: 50px;
        text-align: center;
        // flex: 0;
      }

      &.postName {
        width: 100px;
      }
    }
  }
}

.column_header_title {
  margin-bottom: 8px;
}

.column_ipt:first-of-type {
  margin-right: 5px;
}

// 去除input number类型 加减箭头
// input::-webkit-outer-spin-button,
// input::-webkit-inner-spin-button {
// 	-webkit-appearance: none;
// }
</style>
