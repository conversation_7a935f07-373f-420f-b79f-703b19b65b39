<template>
  <div class="ability_analysis_wrap">
    <div class="page_main_title">人才盘点分析</div>
    <div class="page_section">
      <!--            <div class="page_section_title">人才盘点分析</div>-->
      <div class="ability_analysis_center clearfix">
        <div class="ability_analysis_aside page_section">
          <div class="page_second_title">盘点项目</div>
          <div class="project_assessment_wrap marginT_20">
            <div
              class="project_assessment_item"
              :class="{ active: item.enqId == enqId }"
              v-for="(item, index) in enqList"
              :key="item.id"
              @click="selectProject(index, item.enqId)"
            >
              <div class="project_name">{{ item.enqName }}</div>
              <div class="project_date">{{ formatDate(item.rCreateTime) }}</div>
            </div>
          </div>
        </div>
        <div class="ability_analysis_main page_section" v-if="enqList.length > 0">
          <div class="page_second_title">分析主题</div>
          <div class="analysis_theme_wrap marginT_20">
            <div class="analysis_theme_item" v-for="(item, index) in themeData" :key="item.themeName">
              <div class="icon">
                <i class="el-icon-s-data"></i>
              </div>
              <div class="theme_name">{{ item.themeName }}</div>
              <div class="introduce">{{ item.themeIntroduce }}</div>
              <div class="btn">
                <el-button class="page_add_btn" type="primary"  @click="show(item.path, index)"
                  >查看</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <coustomPagination :total="total" @pageChange="pageChange"></coustomPagination>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import coustomPagination from '@/components/talent/paginationComps/coustomPagination'
import { pageProject } from '../../request/api'

const router = useRouter()

const enqId = ref('')
const enqList = ref([])
const pageCurrent = ref(1)
const pageSize = ref(10)
const total = ref(0)

const formatDate = dateString => {
  if (!dateString) return ''
  return dateString.split(' ')[0]
}

const themeData = [
  {
    themeName: '人才数量',
    key: 'talentNumber',
    themeIntroduce: '全方位分析人才的数量信息，为人才的科学规划提供决依据',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails'
  },
  {
    themeName: '人才结构',
    key: 'talentStructure',
    themeIntroduce: '分析人才结构，为人才的结构优化以及后续的人才结构配置提供决策支持',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails'
  },
  {
    themeName: '人才质量',
    key: 'talentQuality',
    themeIntroduce: '分析员工在核心能力、业绩、潜力等方面的表现，查看人员质量',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails'
  },
  {
    themeName: '人才编制',
    key: 'talentOrganization',
    themeIntroduce: '分析各组织、岗位的人员编制情况，为后续人员规划提供支持',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails'
  },
  {
    themeName: '人才风险',
    key: 'talentRisk',
    themeIntroduce: '人才的风险因素，以及人才流失对业务的影响、以及损失',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails'
  },
  {
    themeName: '人才优化',
    key: 'talentOptimize',
    themeIntroduce: '通过盘点给出的基础的人才优化方案，详细的人才优化方案，请使用专业模型',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails'
  }
]

const enqAnalysisListFun = async () => {
  try {
    const params = {
      current: pageCurrent.value,
      size: pageSize.value
    }
    const res = await pageProject(params)
    if (res.code == 200) {
      enqList.value = res.data
      enqId.value = res.data[0].enqId
      total.value = res.total
    }
  } catch (error) {
    console.error('获取盘点项目列表失败:', error)
  }
}

const pageChange = (size, current) => {
  pageSize.value = size
  pageCurrent.value = current
  enqAnalysisListFun()
}

const selectProject = (index, id) => {
  enqId.value = id
}

const show = (path, index) => {
  router.push({
    path: path,
    query: {
      enqId: enqId.value,
      routeIndex: index
    }
  })
}

onMounted(() => {
  enqAnalysisListFun()
})
</script>

<style scoped lang="scss">
.ability_analysis_aside {
  float: left;
  width: 400px;
  margin-right: 16px;
}
.ability_analysis_main {
  overflow: hidden;
}
.project_assessment_wrap {
  overflow-y: auto;
  .project_assessment_item {
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
    line-height: 36px;
    border: 1px solid #e5e5e5;
    margin-bottom: 6px;
    cursor: pointer;
    &.active {
      background: #0099FF;
      color: #fff;
      border-color: #0099FF;
    }
  }
}
.analysis_theme_wrap {
  .analysis_theme_item {
    padding: 5px 16px;
    border: 1px solid #e5e5e5;
    display: flex;
    justify-content: flex-start;
    line-height: 40px;
    vertical-align: middle;
    align-items: center;
    margin-bottom: 6px;
    .icon {
      width: 30px;
      height: 40px;
      text-align: center;
      line-height: 38px;
      border-radius: 50%;
      color: #0099FF;
    }
    .theme_name {
      width: 150px;
      margin-right: 10px;
    }
    .introduce {
      line-height: 20px;
      width: 400px;
      margin-right: 10px;
      color: #525e6c;
    }
    .btn {
      width: 100px;
      text-align: right;
    }
  }
}
</style>
