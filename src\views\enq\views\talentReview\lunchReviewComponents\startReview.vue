<template>
  <div class="start_review_main" :class="{ event_none: !isEdit }">
    <div class="start_review_content">
      <div class="tip_wrap">
        <el-icon class="icon"><CircleCheck /></el-icon>
        <div class="text_tip">恭喜您已完成盘点的配置，请启动盘点</div>
      </div>
      <div class="review_info flex_row_between marginT_30">
        <div class="item">
          <div class="title">项目名称</div>
          <div class="text">{{ enqName }}</div>
        </div>
        <div class="item" v-if="adminUserName">
          <div class="title">盘点管理员</div>
          <div class="text">{{ adminUserName }}</div>
        </div>
        <div class="item">
          <div class="title">起止时间</div>
          <div class="text">{{ formatDate(beginDate) }}~{{ formatDate(endDate) }}</div>
        </div>
        <div class="item">
          <div class="title">盘点人员</div>
          <div class="text">
            <span class="num">{{ enqUserCount }}</span
            >人
          </div>
        </div>
        <div class="item">
          <div class="title">生成答卷</div>
          <div class="text">
            <span class="num">{{ answerNum }}</span
            >份
          </div>
        </div>
        <div class="item">
          <div class="title">预计生成报告数量</div>
          <div class="text">
            <span class="num">{{ reportNum }}</span
            >份
          </div>
        </div>
      </div>
    </div>
    <div class="marginT_30 align_center" v-if="isEdit">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="submit">启动</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useEnqStore } from '@/views/enq/store'
import { ElMessage } from 'element-plus'
import { CircleCheck } from '@element-plus/icons-vue'
import { getEnqInfo, startEnq } from '../../../request/api.js'

const props = defineProps({
  getEnqId: {
    type: Function,
    required: true
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['prevStep'])

const router = useRouter()
const enqStore = useEnqStore()

const enqId = ref(null)
const enqName = ref('')
const adminUserName = ref('')
const beginDate = ref('')
const endDate = ref('')
const enqUserCount = ref(0)
const answerNum = ref(0)
const reportNum = ref(0)

const formatDate = date => {
  if (!date) return ''
  return date.split(' ')[0]
}

const getEnqInfoFun = async () => {
  try {
    const res = await getEnqInfo({ id: enqId.value })
    if (res.code == 200) {
      const data = res.data
      enqId.value = data.enqId
      enqName.value = data.enqName
      adminUserName.value = data.adminUserName
      beginDate.value = data.beginDate
      endDate.value = data.endDate
      enqUserCount.value = data.enqUserCount
      answerNum.value = data.enqUserCount
      reportNum.value = data.enqOrgCount + data.enqUserCount
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const prevBtn = () => {
  emit('prevStep')
}

const submit = async () => {
  try {
    const res = await startEnq({ id: enqId.value })
    if (res.code == 200) {
      ElMessage.success('启动成功')
      enqStore.setCreateEnqId(null)
      router.go(-1)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

onMounted(() => {
  enqId.value = props.getEnqId()
  getEnqInfoFun()
})
</script>

<style scoped lang="scss">
.tip_wrap {
  text-align: center;
  padding: 16px 0;

  .icon {
    font-size: 100px;
    color: #0099ff;
    margin-bottom: 16px;
  }

  .text_tip {
    font-size: 18px;
    color: #525e6c;
  }
}

.review_info {
  padding: 30px 50px;
  background: #ebf4ff;
  border-radius: 3px;

  .item {
    text-align: center;
  }

  .title {
    font-size: 16px;
    margin-bottom: 16px;
  }

  .text {
    color: #525e6c;
    line-height: 22px;

    .num {
      font-size: 20px;
      color: #0099ff;
    }
  }
}
</style>
