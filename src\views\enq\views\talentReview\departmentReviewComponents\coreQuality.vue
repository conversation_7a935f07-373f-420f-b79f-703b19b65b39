<template>
  <div class="edu_info_wrap">
    <div class="clearfix">
      <div class="page_second_title marginT_8">
        <span>核心素质</span>
      </div>
      <div style="color: #0099ff; font-weight: 600">
        上级需要根据系统给出的各项维度分数，对下级人员进行等级评价，评价标准分为5档：S杰出（通常占10%），A一贯超出预期（30%），B符合预期（30%），C需要提高（20%），D不合格（10%）
      </div>
      <div class="btn_wrap align_right">
        <el-button class="page_add_btn" type="primary" @click="exportDownloadFun()">下载详细数据</el-button>
      </div>
      <!-- 系统根据规则给出默认评级，上级可进行修改，最终评价分为5档：S杰出（通常占10%），A一贯超出预期（30%），B符合预期（30%），C需要提高（20%），D不合格（10%） -->

      <div class="edu_info_center marginT_16">
        <el-table :data="eduInfoData" style="width: 100%">
          <el-table-column type="index" label="序号" width="50" align="center" fixed="left" />
          <el-table-column prop="userName" label="姓名" align="center" fixed="left" />
          <el-table-column prop="S" label="自评" align="center" fixed="right" />
          <el-table-column prop="U" label="上级" align="center" fixed="right" />
          <el-table-column prop="P" label="同级" align="center" fixed="right" />
          <el-table-column prop="B" label="下级" align="center" fixed="right" />
          <!-- <el-table-column
            min-width="100px"
            v-for="(value, key, idx) in legend"
            :label="value"
            :prop="key"
            :key="idx"
            align="center"
          >
            <template #default="scope">
              <div>{{ scope.row[key] }}</div>
               <div v-if="key!='actualQualityGrade'">{{scope.row[key]}}</div>
              <div v-else>
                <el-select class="item" v-model="scope.row[key]" placeholder="请选择">
                  <el-option
                    v-for="(item,index) in qualificationOptions"
                    :label="item.codeName"
                    :value="item.dictCode"
                    :key="index"
                  />
                </el-select>
              </div>
            </template>
          </el-table-column>-->
          <el-table-column prop="coreQualityOverallScore" label="综合得分" align="center" fixed="right" />
          <el-table-column prop="coreQualityLevelSys" label="系统评级" align="center" fixed="right" />
          <!-- <el-table-column label="他人评语" align="center">
            <template #default="scope">
              <el-button @click="handleSeeClick(scope.row)" type="text" size="small">查看</el-button>
            </template>
          </el-table-column> -->
          <el-table-column prop="actualQualityGrade" label="实际等级" width="120" align="center" fixed="right">
            <template #default="scope">
              <el-select class="item" v-model="scope.row.actualCoreQualityGrade" placeholder="请选择">
                <el-option
                  v-for="(item, index) in qualificationOptions"
                  :label="item.codeName"
                  :value="item.dictCode"
                  :key="index"
                />
              </el-select>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="qualityComments" label="素质评价上级评语" width="300" align="center">
            <template #default="scope">
              <el-input class="item" v-model="scope.row.qualityComments" placeholder="请输入" />
            </template>
          </el-table-column>-->
        </el-table>
        <div class="edu_info_mmain">
          <!-- <quality-evaluation-item :eduInfoData="eduInfoData" /> -->
          <div class="align_center marginT_30">
            <el-button
              class="page_confirm_btn"
              type="primary"
              @click="prevStep"
              v-show="currentIndex != currentFirstCode"
              >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="submit('nextStep')">{{ nextBtnText }}</el-button>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="查看他人评论" v-model="dialogVisible">
      <div class="commont_box">
        <div v-for="(item, index) in commontList" :key="index" class="commont">
          <div>评价人：{{ item.userName }}</div>
          <div>评语：{{ item.comment }}</div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" @click="dialogVisible = false">取 消</el-button>
          <el-button size="small" type="primary" @click="dialogVisible = false">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { coreQualityEvalList, saveQualityEval, getComment, exportQualityEvalList } from '../../../request/api.js'
import { objHasEmpty } from '@/utils/utils.js'
import { useUserStore } from '@/stores/modules/user.js'
const userStore = useUserStore()
const props = defineProps({
  nextBtnText: String,
  enqId: String,
  currentIndex: Number,
  currentFirstCode: Number
})

const emit = defineEmits(['nextStep', 'prevStep'])

const submitFlag = ref(true)
const qualificationOptions = ref([])
const eduInfoData = ref([])
const legend = ref({})
const dialogVisible = ref(false)
const commontList = ref([])

// 查看评语
function handleSeeClick(item) {
  getCommentData(item)
  dialogVisible.value = true
}

// 查询他人评语
function getCommentData(item) {
  getComment({
    enqId: props.enqId,
    objectId: item.userId
  }).then(res => {
    console.log(res)
    if (res.code == '200') {
      commontList.value = res.data
    } else {
      ElMessage.error('获取数据失败!')
    }
  })
}

function exportDownloadFun() {
  let params = {
    enqId: props.enqId,
    type: 'coreQuality'
  }
  exportQualityEvalList(params).then(res => {
    // console.log(res)
    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '部门核心素质列表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href) // 释放URL 对象
    document.body.removeChild(elink)
  })
}

function submit(stepType) {
  if (!submitFlag.value) return
  // 校验数据中有没有空值
  if (checkData(eduInfoData.value)) {
    ElMessage({
      message: '请完善数据后提交！',
      type: 'warning'
    })
    submitFlag.value = true
    return
  }
  submitFlag.value = false
  let arr = []
  eduInfoData.value.forEach(item => {
    arr.push({
      grade: item.actualCoreQualityGrade,
      // comments: item.coreQualityComments,
      userId: item.userId
    })
  })
  let params = {
    enqId: props.enqId,
    list: arr,
    type: 'coreQuality'
  }
  saveQualityEval(params)
    .then(res => {
      if (res.code == '200') {
        ElMessage({
          type: 'success',
          message: '保存成功!'
        })
        // getEducationData();
        submitFlag.value = true
        emit(stepType)
      } else {
        submitFlag.value = true
        ElMessage.error('保存失败!')
      }
    })
    .catch(err => {
      submitFlag.value = true
    })
}

// 查询
function getEducationData() {
  coreQualityEvalList({
    enqId: props.enqId
  }).then(res => {
    console.log(res)
    if (res.code == '200') {
      eduInfoData.value = res.data
      // eduInfoData.value = res.data.dataMap;
      // legend.value = res.data.legend;
    } else {
      ElMessage.error('获取数据失败!')
    }
  })
}

function checkData(data) {
  // 校验数据 是否有空值
  let arr = data
  let len = arr.length
  // 校验数据中有没有空值
  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    if (objHasEmpty(obj, ['actualCoreQualityGrade'])) {
      // 检测到有空值跳出遍历
      return true
    } else {
      // return true;
    }
  }
}

function prevStep() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submit('prevStep')
    })
    .catch(action => {
      ElMessage.info(action === 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action === 'cancel') {
        emit('prevStep')
      }
    })
}

onMounted(() => {
  // enqId = route.params.id;
  userStore.getDocList(['ACTUAL_GRADE']).then(res => {
    qualificationOptions.value = res.ACTUAL_GRADE
  })
  getEducationData()
})
</script>

<style scoped lang="scss">
.edu_info_wrap {
  margin-bottom: 16px;
}

.edu_info_header {
  .item {
    width: 9%;
    // padding-left: 15px;
  }

  .item_icon_wrap {
    text-align: center;
    width: 6%;
  }
}

.commont_box {
  height: 400px;
  overflow-y: auto;

  .commont {
    border-bottom: 1px solid #9bd3f6;

    div {
      padding: 10px 0;
    }
  }
}

:deep(.el-dialog__body) {
  padding: 0 30px;
}
</style>
