<template>
  <el-table
    ref="singleTable"
    :data="tableData.list"
    :highlight-current-row="hasRadio"
    @current-change="handleCurrentChange"
    border
  >
    <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
    <slot name="nameColumn">
      <el-table-column label="能力模块" prop="name" align="center" width="200">
        <template #default="scope">
          <div class="align_left">{{ scope.row['name'] }}</div>
          <div class="score_bar">
            <div
              class="content_bar"
              :style="{
                width: `${scope.row['value']}%`
                // background: `linear-gradient(-90deg,${setColor(scope.row['value'])})`
              }"
            ></div>
            <div class="text" :style="{ left: `calc(${scope.row['value']}% + 5px)` }">
              {{ scope.row['value'] }}
            </div>
          </div>
        </template>
      </el-table-column>
    </slot>
    <el-table-column v-if="hasTrend" label="变化趋势" prop="changeScore" align="center" width="100">
      <template #default="scope">
        <div class="type_trend">
          <div class="arrow_icon flex_row_center">
            <SvgIcon name="icon-arrow-up" color="#95DC6B" v-if="scope.row.changeType == 1"></SvgIcon>
            <div class="trend_line" v-if="!scope.row.changeType"></div>
            <SvgIcon name="icon-arrow-down" color="#FF7D7D" v-if="scope.row.changeType == -1"></SvgIcon>
          </div>
          <div>{{ scope.row.changeType > 0 ? '+' : '' }}{{ scope.row.changeScore * scope.row.changeType }}</div>
        </div>
      </template>
    </el-table-column>
    <el-table-column v-for="col in tableData.columns" :prop="col.code" :key="col.code" :label="col.name" align="center">
      <template #default="scope">
        <div class="score_bar can_hidden">
          <div
            class="content_bar"
            :style="{
              width: `${scope.row[col.code]}%`
              // background: `linear-gradient(-90deg,${setColor(scope.row[col.code])})`
            }"
          ></div>
          <div class="text" :style="{ left: `calc(${scope.row[col.code]}% + 5px)` }">
            {{ scope.row[col.code] }}
          </div>
        </div>
        <div class="bar_chart">
          <div class="bar_chart_item" v-for="item in scope.row[`${col.code}List`]">
            <div class="chart_item_label">{{ item.name }}</div>
            <div class="chart_item_bar">
              <div
                class="chart_item_bar_content"
                :style="{ width: `${item.value}%`, background: `${setColor(item.value)}` }"
              ></div>
              <div class="chart_item_bar_text" :style="{ left: `calc(${item.value}%)` }">{{ item.value }}</div>
            </div>
          </div>
        </div>
      </template>
    </el-table-column>
    <el-table-column fixed="right" label="操作" width="60" align="center" v-if="hasOper">
      <template #header>
        <el-button class="back_btn" link v-if="hasBack" @click="back()">返回</el-button>
        <div v-else>操作</div>
      </template>
      <template #default="scope">
        <div class="pointer detail_btn" @click="toDetail(scope.row, scope.$index)">详情</div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

const props = defineProps({
  hasBack: {
    type: Boolean,
    default: false
  },
  hasTrend: {
    type: Boolean,
    default: true
  },
  hasRadio: {
    type: Boolean,
    default: true
  },
  hasOper: {
    type: Boolean,
    default: true
  },
  tableData: {
    type: Object,
    default: () => ({ list: [], columns: [] })
  }
})

const emit = defineEmits(['detail', 'back'])
const singleTable = ref(null)

watch(
  () => props.tableData.list,
  value => {
    console.log('tableList value :>> ', value)
    if (props.hasRadio) {
      nextTick(() => {
        singleTable.value.setCurrentRow(value[0])
      })
    }
  }
)

const handleCurrentChange = val => {
  if (!props.hasRadio) return
  // currentRow.value = val
}

const toDetail = (row, index) => {
  emit('detail', row)
}

const back = () => {
  emit('back')
}

const setColor = score => {
  const scoreRanges = [
    '#B2FDFE',
    '#83FDFE',
    '#34EAFF',
    '#3ED4FF',
    '#22B5FA',
    '#2589EC',
    '#0D69C1',
    '#08549B',
    '#03396E',
    '#032B4D)'
  ]
  const index = parseInt(score / 10) == 10 ? 9 : parseInt(score / 10)
  return scoreRanges[index]
}
</script>

<style lang="scss" scoped>
.type_trend {
  font-size: 16px;
  color: #373d41;
  text-align: center;

  .text {
    margin-bottom: 10px;
    white-space: nowrap;
  }

  .trend_line_box {
    height: 16px;
  }

  .trend_line {
    width: 14px;
    height: 4px;
    background: #2ce2ff;
  }

  .arrow_icon {
  }
}

.score_bar {
  position: relative;
  width: calc(100% - 30px);
  height: 20px;
  background-color: #f5f7f7;
  margin-top: 10px;

  .content_bar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #85e5ff;
  }

  .text {
    position: absolute;
    left: 100%;
    top: -3px;
    color: #000;
    white-space: nowrap;
    font-size: 12px;
  }

  .name {
    width: 100%;
    text-align: left;
  }
}

.bar_chart {
  display: none;

  .bar_chart_item {
    display: flex;
    align-items: stretch;
    min-height: 24px;
    transition: background-color 0.3s ease;
    &:hover {
      background-color: #efefef;
    }

    .chart_item_label {
      position: relative;
      flex: 0 0 80px;
      font-size: 12px;
      color: #3d3d3d;
      border-right: 1px solid #ddd;
      text-align: right;
      padding: 3px 3px 3px 0;
      z-index: 1;
      line-height: 16px;
    }

    .chart_item_bar {
      position: relative;
      width: calc(100% - 100px);

      .chart_item_bar_content {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 14px;
        // margin-left: -2px;
        z-index: 0;
        // background-color: #2589EC;
      }

      .chart_item_bar_text {
        position: absolute;
        top: -4px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        white-space: nowrap;
      }
    }
  }
}

.detail_btn {
  pointer-events: none;
}

:deep(.el-table__row.current-row) {
  height: auto;
  height: 200px;
  background: #fff;

  .score_bar.can_hidden {
    display: none;
  }

  .bar_chart {
    display: block;
  }

  .detail_btn {
    color: #22b5fa;
    cursor: pointer;
    pointer-events: initial;
  }
}

:deep(.el-table__body tr.current-row > td) {
  background: #fff;
}

:deep(.el-table .cell) {
  padding-left: 2px;
  padding-right: 2px;
}
.align_left {
  text-align: left;
}
</style>
