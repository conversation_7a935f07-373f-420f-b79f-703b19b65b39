<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section">
      <div class="content_item_title flex_row_between">
        岗位活动对比
        <!-- <el-button type="primary" plain size="mini" @click>选择岗位</el-button> -->
      </div>
      <template>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column type="index" fixed="left"></el-table-column>
          <el-table-column prop="activityTypeName" fixed="left" label="工作类别" width="80"></el-table-column>
          <el-table-column prop="activityName" fixed="left" label="主要活动" width="100"></el-table-column>
          <el-table-column
            prop="efficiencyValue"
            fixed="left"
            label="高绩效岗位"
            :formatter="addPercentSign"
          ></el-table-column>
          <el-table-column
            v-for="list in columns"
            :formatter="addPercentSign"
            :key="list.postCode"
            :prop="list.postCode"
            :label="list.postName"
          ></el-table-column>
        </el-table>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { postActivityCompare } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const route = useRoute()
const enqId = ref(route.query.enqId)
const jobClassCode = ref('')
const orgCode = ref('')
const pageCurrent = ref(1)
const pageSize = ref(10)
const columns = ref([])
const tableData = ref([])
const filterData = ref({})

const postActivityCompareFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: pageCurrent.value,
      size: pageSize.value
    }
    const res = await postActivityCompare(params)
    if (res.code == 200) {
      columns.value = dotToline(res.data.postList, 'value', 'postCode')
      tableData.value = dotToline(res.data.records, 'key')
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  postActivityCompareFun()
}

const addPercentSign = (row, column, cellValue) => {
  return cellValue + '%'
}

const dotToline = (param, type, valueKey) => {
  if (Array.isArray(param)) {
    if (param.length == 0) {
      return
    }
    param.forEach(item => {
      if (typeof item == 'object') {
        for (const key in item) {
          if (Object.prototype.hasOwnProperty.call(item, key)) {
            if (type == 'key') {
              const newKey = key.split('.').join('-')
              item[newKey] = item[key]
            } else if (type == 'value') {
              const val = item[valueKey]
              item[valueKey] = val.split('.').join('-')
            }
          }
        }
      }
    })
    return param
  }
}

onMounted(() => {
  filterData.value = route.attrs.filterData
  postActivityCompareFun()
})
</script>

<style scoped lang="scss"></style>
