<template>
  <div class="personal_inventorytips">
    <div class="page_main_title">
      <div class="goback_geader" @click="goback()"><i class="el-icon-arrow-left"></i>返回</div>
      个人盘点
    </div>
    <div class="page_section">
      <div class="one_div">
        <div class="greetings">您好，感谢您在百忙之中参与本次盘点；</div>
        <div class="title">
          我们期待着您作出客观的评价，并表达您的期望。您的认真回答与表达，将为公司的人才管理提升，提供非常重要的参考依据。
        </div>
        <ul>
          <li>1、本次问卷为不记名问卷，我们会对您的回答内容进行保密。</li>
          <li>2、请结合日常工作中的感受及实际情况，对每个盘点项进行精准的判断。</li>
          <li>3、盘点过程中，您可以随时退出，系统会自动保存你的评估结果，继续时会自动定位到最近位置。</li>
        </ul>
      </div>
      <div class="two_div">
        <div class="title">注意事项</div>
        <ul>
          <li>
            <span></span
            >为了顺利完成测评，请在没有外界干扰的环境下进行测评，并关闭一些无关程序，如下载工具等，以确保网络连接畅通。
          </li>
          <li>
            <span></span>如遇到网速较慢、网页卡死等情况，请关闭测评页面，稍后更换较好的网络环境后重新登录，继续作答。
          </li>
          <li>
            <span></span
            >如遇突发情况，如断网、接听电话、电脑死机、断电等，请关闭浏览器或计算机，当可以作答时再次进行盘点。
          </li>
          <li><span></span>请保证您在不被打扰的状态下完成所有题目，点击最后的"提交"，提交完成后本次个人盘点结束。</li>
        </ul>
      </div>
      <div class="three_div">
        <div class="page_second_title">是否隐藏您的信息</div>
        <div class="radio_box">
          <el-radio-group v-model="radio">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="2">否</el-radio>
          </el-radio-group>
          <div v-show="radio == '1'" class="tip">
            您已选择隐藏个人信息，被评价人员无法了解您的信息，所有评价数据均以"评价人1、2、3"进行记录。
          </div>
        </div>
      </div>
    </div>
    <div class="marginT_30 align_center">
      <el-button class="page_confirm_btn" type="primary" @click="startReview">开始盘点</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const enqId = ref(null)
const radio = ref(1)

const goback = () => {
  router.push('/talentReviewHome/talentReview')
}

const startReview = () => {
  router.push('/talentReviewHome/talentReview/personalReview?enqId=' + enqId.value)
}

onMounted(() => {
  enqId.value = route.query.enqId
})
</script>

<style lang="scss" scoped>
.personal_inventorytips {
  .one_div {
    margin-bottom: 25px;
    .greetings {
      margin-bottom: 10px;
    }
    .title {
      margin-bottom: 10px;
    }
    ul {
      li {
        margin: 10px 0;
      }
    }
  }
  .two_div {
    margin-bottom: 25px;
    .greetings {
      margin-bottom: 10px;
    }
    .title {
      margin-bottom: 10px;
      font-weight: 600;
    }
    ul {
      li {
        margin: 10px 0;
        span {
          display: inline-block;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background-color: #000;
          margin: 0 5px;
        }
      }
    }
  }
  .three_div {
    .page_second_title {
      font-weight: 600;
    }
    .radio_box {
      height: 40px;
      padding: 0 5px;
      display: flex;
      align-items: center;
      .el-radio-group {
      }
      .tip {
        margin-left: 20px;
        color: #409eff;
      }
    }
  }
}
</style>
