<template>
  <div class="person all">
    <div class="tree">
      <el-input class="w-60 mb-[8px]" placeholder="按组织名称检索" />
      <Tree></Tree>
    </div>
    <div class="main">
      <div class="key-index">
        <div class="title">关键指标</div>
        <div class="table-main">
          <el-table ref="tableDataRef" :data="tableData" highlight-current-row style="width: 100%">
            <el-table-column type="index" align="center" label="序号" width="80" />
            <el-table-column prop="kpiName" label="指标名称" width="180" />
            <el-table-column prop="targetClass" label="目标类别" width="180" />
            <el-table-column prop="period" label="目标期间" />
            <el-table-column prop="kpiUnit" label="指标单位" />
            <el-table-column prop="charge" label="责任人" />
            <el-table-column prop="targetValue" label="指标目标" />
            <el-table-column prop="realityValue" label="实际表现" />
            <el-table-column prop="yieldRate" label="达成率" />
            <el-table-column prop="gap" label="差距" />
            <el-table-column>
              <template #default="scope">
                <div
                  class="btn"
                  @click="
                    openAi(
                      `${scope.row.kpiName}、${scope.row.targetClass}、${scope.row.period}、指标目标${scope.row.targetValue}、实际表现${scope.row.realityValue}`
                    )
                  "
                >
                  AI解读
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!-- 已选择 -->
      <div class="active">
        <div class="title">
          <div class="text">已选指标：</div>
          <div class="name">库存周转天数</div>
        </div>
        <div class="page-title-line">同岗位人员指标表现（库存周转天数）</div>
        <el-table ref="tableDataRef2" highlight-current-row :data="table1" :stripe="true" style="width: 100%">
          <el-table-column type="index" align="center" label="序号" width="80" />
          <el-table-column prop="kpiName" label="指标名称" />
          <el-table-column prop="staff" label="人员" />
          <el-table-column prop="depName" label="部门" />
          <el-table-column prop="postName" label="岗位" />
          <el-table-column prop="targetValue" label="指标目标" />
          <el-table-column prop="realityValue" label="实际表现" />
          <el-table-column prop="yieldRate" label="达成率" />
          <el-table-column prop="ranking" label="同组织排名" />
        </el-table>
        <div class="page-title-line">关联人员能力（周伟）</div>
        <el-table :data="table2" :stripe="true" style="width: 100%">
          <el-table-column type="index" align="center" label="序号" width="80" />
          <el-table-column prop="key1" label="人员能力" />
          <el-table-column prop="key2" align="center" label="相关性" />
          <el-table-column prop="key3" label="关联逻辑" width="600" />
          <el-table-column prop="key4" align="center" label="能力表现" width="100">
            <template #default="scope">
              <div class="num" :style="{ background: scope.row.key4 >= 60 ? '#40D476' : '#FF5E4C' }">
                {{ scope.row.key4 }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="page-title-line">人员能力提升建议</div>
        <el-table :data="table3" :stripe="true" style="width: 100%">
          <el-table-column prop="key1" label="维度" width="110" />
          <el-table-column prop="key2" label="具体内容" />
          <el-table-column prop="key3" label="实施要求" width="500" />
        </el-table>
        <div class="page-title-line">决策风格表现（供应链规划能力）</div>
        <div class="chart-main">
          <div class="item" v-for="item in list">
            <div class="title">{{ item.title }}</div>
            <EChartsBar type="horizontal" :options="getOptions(item)" />
          </div>
        </div>
        <div class="page-title-line">决策风格改善建议</div>
        <el-table :data="table4" :stripe="true" style="width: 100%">
          <el-table-column prop="key1" label="决策模式/风格" width="120" />
          <el-table-column prop="key2" label="主要表现" width="400" />
          <el-table-column prop="key3" label="改善建议" />
        </el-table>
      </div>
    </div>
  </div>
</template>
<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import { diagnosticAnalysis } from '@/assets/data/data9.js'
import Tree from '@/components/tree/index.vue'

defineOptions({ name: 'Person' })
const openAi = inject('openAi')
const list = ref([
  {
    title: '1、决策风格偏好',
    symbol: '%',
    data: [
      {
        name: '经验依赖型',
        value: '45%'
      },
      {
        name: '数据驱动型',
        value: '15%'
      },
      {
        name: '系统思考型',
        value: '15%'
      },
      {
        name: '机会主义型',
        value: '25%'
      }
    ]
  },
  {
    title: '2、决策风险偏好',
    symbol: '%',
    data: [
      {
        name: '高风险',
        value: '40%'
      },
      {
        name: '中风险',
        value: '30%'
      },
      {
        name: '低风险',
        value: '30%'
      }
    ]
  },
  {
    title: '3、团队协作偏好',
    symbol: '%',
    data: [
      {
        name: '同小组合作',
        value: '30%'
      },
      {
        name: '单部门协作',
        value: '20%'
      },
      {
        name: '跨部门协同',
        value: '20%'
      },
      {
        name: '战略级协同',
        value: '15%'
      },
      {
        name: '生态级协同',
        value: '15%'
      }
    ]
  },
  {
    title: '4、成本收益偏好',
    symbol: '%',
    data: [
      {
        name: '高投入高回报',
        value: '30%'
      },
      {
        name: '低投入高回报',
        value: '20%'
      },
      {
        name: '性价比平衡',
        value: '20%'
      },
      {
        name: '低投入低回报',
        value: '15%'
      },
      {
        name: '高投入低回报',
        value: '15%'
      }
    ]
  }
])
const getOptions = item => {
  let symbol = item.symbol
  return {
    xAxisData: item.data.map(item => item.name).reverse(),
    xAxis: {
      show: false
    },
    grid: {
      top: 0,
      left: '3%',
      right: 0,
      bottom: '3%',
      containLabel: true
    },
    series: [
      {
        type: 'bar',
        data: item.data
          .map(item => {
            if (symbol) {
              return parseInt(item.value)
            } else {
              return parseFloat(item.value)
            }
          })
          .reverse(),
        itemStyle: {
          color: '#85E5FF'
        },
        showBackground: true,
        label: {
          show: true,
          formatter: function (params) {
            if (symbol) {
              return params.value + symbol
            } else {
              return parseFloat(params.value)
            }
          }
        },
        formatter: function (params) {
          console.log(params)
          if (symbol) {
            return params.value + symbol
          } else {
            return parseFloat(params.value).toFixed(2)
          }
        }
      }
    ]
  }
}
const table4 = [
  {
    key1: '经验依赖型',
    key2: '过度依赖历史供应链规划经验，习惯沿用过往的安全库存计算公式、采购周期设定或需求预测模型，忽视市场环境变化（如消费升级、供应链重构）与新技术应用（如 AI 预测算法）。例如：面对新渠道订单波动，仍按传统模式设定缓冲库存，导致断货或积压；供应商结构调整后，未重新评估交货周期，造成供应脱节。该风格在稳定场景可维持效率，但在复杂变化中易引发策略滞后与资源错配。',
    key3: '1. 建立经验匹配清单：决策前对比当前场景与历史经验的差异点（如渠道、供应地域、需求频率），标注不匹配项并补充新数据验证（如收集新渠道数据重构预测模型）；\n2. 引入数据驱动工具：学习机器学习算法、供应链模拟软件，通过数据生成多版本方案，对比经验与数据策略的库存周转效果，逐步强化数据决策习惯；\n3. 季度知识更新机制：每季度学习 1 项供应链规划新技术 / 方法论（如 DDP 需求驱动规划），并强制融入现有策略（如替换 1 个旧预测模型）；\n4. 设立反经验试点：预留 20% 业务单元（如非核心产品线）采用新策略（如 VMI 模式、零库存试点），通过 A/B 测试对比新旧策略的库存周转效率，用数据修正经验偏差；\n5. 跨代际协作优化：与新生代组成 “经验 + 创新” 小组，融合历史案例与数字化工具（如 Python 数据分析），共同拆解历史策略的适用边界，生成适配新场景的规划方案。'
  },
  {
    key1: '高风险型',
    key2: '偏好激进策略，倾向极端压缩安全库存或押注单一爆品，忽视需求波动与供应链不确定性。例如：为提升周转率大幅削减缓冲库存，导致突发订单断货；或未充分验证需求即批量采购长周期原料，造成滞销积压。该风格可能短期提升指标，但易引发供应链断裂风险，冲击现金流与客户满意度。',
    key3: '1. 建立风险评估矩阵：决策前量化分析需求波动系数、供应周期稳定性等指标，设定安全库存与采购量的风险容忍阈值（如安全库存不低于历史最大波动量的 70%）；\n2. 最小可行性测试（MVT）：对高风险策略先进行单区域 / 单产品线试点，收集数据验证后再规模化推广（如试销 3 个月评估滞销率）；\n3. 引入风险量化工具：学习蒙特卡洛模拟等方法，通过历史数据模拟不同决策场景下的库存周转天数、缺货率波动，输出风险 - 收益平衡方案；\n4. 强化风险意识培训：吸收行业典型案例（如牛鞭效应导致的库存危机），参加供应链风险管理课程，建立 “风险 - 效率” 双维度决策思维；\n5. 季度复盘机制：对高风险策略的实际效果进行回溯，对比库存周转目标与实际表现，形成 “激进策略 + 安全冗余” 的动态调整模型（如旺季安全库存上浮 15%）。'
  },
  {
    key1: '小组合作型',
    key2: '依赖跨部门小组集体决策，过度追求共识导致效率低下，方案易陷入 “折中主义”。例如：制定库存策略时，因销售强调订单满足率、采购强调成本控制，最终安全库存高于实际需求，降低周转效率；需求预测环节因缺乏主导，多版本数据并存延误计划。该风格虽兼顾各方诉求，但易引发 “群体思维”，抑制创新策略。',
    key3: '1. 明确决策责任主体：在跨部门小组中设立 “最终责任人”（如供应链规划经理），建立 “共识讨论 + 责任人拍板” 机制，避免过度民主导致的决策拖延；\n2. 引入优先级评估工具：使用 ICE 矩阵等工具，对库存周转天数、采购成本等指标设定权重，以数据化方式辅助小组快速收敛方案（如按权重排序筛选最优解）；\n3. 跨部门决策效率培训：学习 “六顶思考帽”“德尔菲法” 等工具，引导成员从数据、创新、风险等视角高效讨论，减少无效博弈；\n4. 设定决策时效标准：常规策略需在 48 小时内完成小组评审，重大策略不超过 7 个工作日，避免因过度讨论错过市场窗口；\n5. 细化小组角色分工：明确销售提供需求数据、生产提供产能约束、采购提供成本边界等职责，提升讨论针对性（如需求组聚焦历史订单波动，供应组聚焦交期稳定性）。'
  },
  {
    key1: '高投入高回报型',
    key2: '倾向通过大规模资源投入（如新建仓储、采购高价系统、储备战略库存）追求库存周转突破，忽视投入产出比与边际效益。例如：为实现数据可视化不计成本部署顶级系统，却因适配度低导致资源浪费；或提前囤积过量库存应对旺季，最终因需求不及预期造成积压。该风格易造成资源浪费，反噬企业盈利能力。',
    key3: '1. 建立成本 - 效率评估模型：决策前测算单位库存周转天数优化的资源投入，设定 ROI 阈值（如≤6 个月回本），优先选择投入产出比高的方案（如优化现有系统功能而非采购新系统）；\n2. 推行轻资产改善策略：优先通过流程优化（如缩短采购审批链）、算法升级（如优化安全库存公式）提升效率，验证效果后再考虑硬件 / 系统投资；\n3. 聚焦瓶颈环节突破：运用 TOC 约束理论，识别供应链关键瓶颈（如滞销品处理慢、供应商交货延迟），针对性投入资源（如建立滞销品快速清仓通道），避免全面撒网；\n4. 引入外部对标机制：分析行业领先企业库存周转优化的资源投入效率（如每降低 1 天周转天数的平均成本），设定符合企业规模的投入上限（如不超过年度供应链预算的 15%）；\n5. 建立项目后评估机制：对高投入方案持续追踪 6-12 个月，若库存周转天数未达预期目标（如提升低于 10%），启动资源止损并调整方案（如停用低效模块，转为轻量化解决方案）。'
  }
]
const table3 = [
  {
    key1: '自学模块',
    key2: '1. 必读书目：《供应链规划：理论与实践》《需求驱动的供应链规划》；2. 在线课程：APICS CSCP 认证核心课程（供应链规划模块）、供应链规划与库存优化实战（慕课网）；3. 行业报告：每月精读 3 份快消 / 制造业供应链白皮书',
    key3: '每周学习≥8 小时，输出供应链规划工具应用笔记（含 EOQ 模型、MRPⅡ 算法实践心得）'
  },
  {
    key1: '课堂培训',
    key2: '1. 内部课程：《供应链需求规划与库存优化》（12 课时，含 S&OP 流程模拟）；2. 外部认证：ASCM 供应链专业认证（CSCP）；3. 案例研讨：丰田 JIT 供应链规划案例复盘、亚马逊动态库存规划策略拆解',
    key3: '培训后完成供应链规划沙盘模拟，要求库存周转率提升方案达标率≥85%'
  },
  {
    key1: '岗位带教',
    key2: '1. 导师：供应链规划总监（每周 1 次一对一辅导，聚焦需求预测偏差分析与安全库存建模）；2. 实战项目：主导季度滚动生产计划与采购协同优化项目（覆盖 3 条核心产品线）；3. 轮岗体验：在生产计划部（2 周）、采购部（2 周）进行跨职能协作实操',
    key3: '提交《供应链规划跨部门协同改进方案》，经业务部门签字确认落地可行性'
  },
  {
    key1: '学习内容',
    key2: '1. 需求预测模型：时间序列分析（ARIMA）、机器学习预测算法（随机森林）；2. 供应链规划工具：S&OP（销售与运营计划）、DRP（分销资源计划）；3. 库存优化方法：ABC 分类法、VMI（供应商管理库存）、JIT（准时制生产）；4. 数据工具：Excel 高级函数（数据透视表、VLOOKUP）、Python（Pandas 数据处理）、供应链规划软件（如 SAP IBP）',
    key3: '每月完成 1 次真实业务数据建模练习，输出《需求预测误差分析报告》'
  },
  {
    key1: '学习建议',
    key2: '1. 建立供应链规划看板：实时监控需求预测准确率、订单满足率、库存周转天数等 15 + 核心指标；2. 参加行业峰会：全球供应链大会（GSCF）、中国供应链管理年会；3. 搭建对标数据库：跟踪 TOP5 同行供应链规划 KPI（如库存周转率、订单响应周期），每季度输出对标分析报告',
    key3: '每季度在公司知识平台分享规划工具使用心得，累计下载量≥50 次'
  },
  {
    key1: '学习周期',
    key2: '1. 短期（1-2 月）：掌握需求预测基础工具与安全库存计算模型；2. 中期（3-6 月）：独立完成单产品线供应链规划方案（含采购计划、生产排期、库存策略）；3. 长期（6-12 月）：主导全品类供应链规划体系优化项目（覆盖需求、供应、库存三大模块）',
    key3: '每个阶段通过业务场景模拟考核，未达标需参加专项强化训练（72 小时集训）'
  },
  {
    key1: '考核建议',
    key2: '1. 笔试：供应链规划工具应用（权重 30%，含 EOQ 计算、S&OP 流程设计）；2. 实操：制定某事业部年度供应链规划方案（权重 50%，要求库存周转天数目标值较现状降低 20%）；3. 360 度评估：跨部门协作满意度（权重 20%，生产、采购、销售部门评分≥4.5/5）',
    key3: '综合得分≥85 分纳入供应链核心人才库，享受规划项目主导权优先资格'
  }
]
const table2 = [
  {
    key1: '供应链规划能力',
    key2: '极高',
    key3: '供应链规划决定库存结构合理性，直接影响各环节库存水位。全局性资源调配和网络设计（如仓网布局、补货周期）可系统性降低冗余库存，缩短周转周期。',
    key4: '66'
  },
  {
    key1: '需求预测分析能力',
    key2: '极高',
    key3: '精准的需求预测是库存计划的核心依据。预测偏差将导致库存积压或短缺，直接影响周转天数。动态预测能力（如滚动预测、AI模型）可显著提升供需匹配度。',
    key4: '55'
  },
  {
    key1: '库存策略制定能力',
    key2: '极高',
    key3: '制定科学的安全库存策略、ABC分类管理、呆滞品处理机制，直接影响库存周转效率。策略制定者需平衡服务水平与库存成本，避免过度囤积或频繁断货。',
    key4: '69'
  },
  {
    key1: '数据分析与建模能力',
    key2: '高',
    key3: '通过数据挖掘识别库存异常（如库龄超标SKU）、建立库存优化模型（如动态安全水位计算），为决策提供量化依据。缺乏数据驱动易导致经验主义误判。',
    key4: '69'
  },
  {
    key1: '供应链协同能力',
    key2: '高',
    key3: '跨部门/企业协同（如CPFR、VMI）可打破信息孤岛，减少牛鞭效应。协同能力不足将导致各环节局部优化，放大整体库存波动（如生产过量、采购冗余）。',
    key4: '52'
  },
  {
    key1: '采购与库存联动能力',
    key2: '高',
    key3: '采购批量、频次与库存策略的匹配度直接影响库存水位。联动能力强的采购团队能动态调整订货策略（如经济批量优化、JIT采购），避免集中到货导致的库存峰值。',
    key4: '67'
  },
  {
    key1: '生产计划协调能力',
    key2: '高',
    key3: '生产计划与库存策略的协同能力决定在制品和成品库存量。柔性生产能力（如小批量生产、快速换线）可减少生产过剩，避免因排产僵化导致的库存积压。',
    key4: '64'
  },
  {
    key1: '物流与仓储优化能力',
    key2: '中高',
    key3: '仓储作业效率（如拣货速度、库位规划）影响库存周转速度，物流时效性决定在途库存天数。但该能力更多影响操作效率，对库存策略的全局性影响弱于前几项。',
    key4: '69'
  },
  {
    key1: '跨部门沟通协调能力',
    key2: '中',
    key3: '沟通能力不足会导致信息滞后（如销售未及时共享促销计划），间接引发库存失衡。但属于支撑性能力，需通过影响其他核心能力（如预测、计划）间接作用于库存周转。',
    key4: '52'
  },
  {
    key1: '库存成本管控能力',
    key2: '中高',
    key3: '成本管控意识驱动库存精细化管理（如减少高价值物料囤积），但过度关注成本可能牺牲周转效率（如为降低采购单价而大批量订货）。需与周转目标协同优化。',
    key4: '64'
  }
]
const table1 = ref(diagnosticAnalysis.person2)

const tableData = ref(diagnosticAnalysis.person1)
const tableDataRef = ref(null)
const tableDataRef2 = ref(null)
onMounted(() => {
  tableDataRef.value.setCurrentRow(diagnosticAnalysis.person1[1])
  tableDataRef2.value.setCurrentRow(diagnosticAnalysis.person2[6])
})
const data = ref([
  {
    id: 1,
    label: 'H公司',
    children: [
      {
        id: 4,
        label: '采购部'
      },
      {
        id: 5,
        label: '重庆冰箱工厂'
      },
      {
        id: 6,
        label: '电子商务部'
      },
      {
        id: 7,
        label: '欧盟区产品部'
      },
      {
        id: 8,
        label: '供应链计划管理部',
        children: [
          {
            id: 21,
            label: '王伟'
          },
          {
            id: 22,
            label: '陈建军'
          },
          {
            id: 23,
            label: '陈丽娟'
          },
          {
            id: 24,
            label: '陈丽君'
          },
          {
            id: 25,
            label: '陈秀英'
          },
          {
            id: 26,
            label: '陈志强'
          },
          {
            id: 27,
            label: '李凤霞'
          },
          {
            id: 28,
            label: '李凤英'
          },
          {
            id: 29,
            label: '李国强'
          },
          {
            id: 30,
            label: '李建国'
          },
          {
            id: 31,
            label: '刘凤兰'
          },
          {
            id: 32,
            label: '刘凤霞'
          },
          {
            id: 33,
            label: '刘桂兰'
          },
          {
            id: 34,
            label: '刘桂珍'
          },
          {
            id: 35,
            label: '刘建军'
          },
          {
            id: 36,
            label: '刘俊芳'
          },
          {
            id: 37,
            label: '刘淑芳'
          },
          {
            id: 38,
            label: '刘淑华'
          },
          {
            id: 39,
            label: '刘淑珍'
          }
        ]
      },
      {
        id: 9,
        label: 'GTM部'
      },
      {
        id: 10,
        label: '结构研发部'
      },
      {
        id: 11,
        label: '经营与财务管理部'
      }
    ]
  }
])
const active = ref(21)
</script>
<style lang="scss" scoped>
@import './diagnosticAnalysis.scss';
.chart-main {
  display: flex;
  justify-content: space-between;
  .item {
    width: 318px;
    height: 265px;
    background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), rgba(255, 255, 255, 0.5);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 20px;
    .title {
      font-size: 14px;
      color: #333333;
      font-weight: 600;
      margin-bottom: 10px;
    }
  }
}
</style>
