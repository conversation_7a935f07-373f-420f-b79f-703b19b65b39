<script setup>
const emits = defineEmits(["sign"]);

const leftMenu2 = ref([
  {
    name: "人才基础画像",
    code: "A",
    children: [
      {
        name: "基本信息",
        code: 1,
      },
      {
        name: "职业轨迹",
        code: 2,
      },
    ],
  },
  {
    name: "任职匹配分析",
    code: "B",
    children: [
      {
        name: "硬性条件匹配",
        code: 11,
      },
      {
        name: "能力匹配雷达图",
        code: 12,
      },
    ],
  },
  {
    name: "多维评估结果",
    code: "C",
    children: [
      {
        name: "素质评价结果",
        code: 21,
      },
      {
        name: "业绩评价结果",
        code: 22,
      },
      {
        name: "潜力评价结果",
        code: 23,
      },
    ],
  },
]);

const leftMenu2CheckSign = ref(1);
const zbdb = ref([
  {
    title: "明确发展方向：",
    info: " 如果整体得分较高且处于较高能力阶段，说明企业在当前业务领域具备较强的竞争力，可考虑进行市场扩张、多元化发展等战略。若整体得分较低或处于较低能力阶段，企业则需要专注于提升核心能力，弥补短板，可能需要收缩战线，集中资源解决关键问题。 ",
  },
  {
    title: "设定战略目标：",
    info: "依据整体得分和能力阶段，制定具体的、可衡量的战略目标。如处于能力提升阶段的企业，目标可以是在一定时间内将整体得分提高到某个水平，达到下一个能力阶段。",
  },
]);

const closeInfo = (i) => {
  emits("sign", "list");
};
const leftMenu2Check = (c) => {
  leftMenu2CheckSign.value = c;
};
onMounted(() => {});
</script>
<template>
  <div class="index_wrap reportInfo_wrap">
    <div class="t_btn" @click="closeInfo"><span class="icon"></span>返回</div>
    <div class="title_main">20240201 战略管理人才盘点报告（采购部）</div>
    <div class="reportInfo_main justify-between">
      <div class="left_menu_wrap">
        <div class="title">报告目录</div>
        <div class="item_wrap" v-for="it in leftMenu2">
          <div class="name">{{ it.name }}</div>
          <div
            class="item_c_wrap"
            :class="{ item_c_act: leftMenu2CheckSign == item.code }"
            v-for="item in it.children"
            @click="leftMenu2Check(item.code)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>

      <div class="right_wrap">
        <div class="info_section_wrap">
          <div class="page-title-line">人才基本信息</div>
          <div></div>
        </div>
        <div class="info_section_wrap">
          <div class="page-title-line">岗位信息</div>
          <div></div>
        </div>
        <div class="info_section_wrap">
          <div class="page-title-line">教育背景</div>
          <div></div>
        </div>

        <div class="info_section_wrap">
          <div class="page-title-line">如何使用该项结果</div>
          <div class="section_box_wrap dot_content_wrap">
            <div class="item_wrap" v-for="item in zbdb">
              <span class="icon"></span>
              <span class="title">{{ item.title }}</span>
              <span class="info">{{ item.info }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.reportInfo_wrap {
  .t_btn {
    margin: 0 0 20px 0;
    width: 74px;
    height: 28px;
    line-height: 28px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #40a0ff;
    text-align: center;
    color: #40a0ff;
    cursor: pointer;
    .icon {
      display: inline-block;
      margin: 0px 6px -1px 0;
      width: 16px;
      height: 16px;
      background: url("@/assets/imgs/org/icon_06.png") no-repeat center;
      background-size: 100% 100%;
    }
  }
  .title_main {
    margin-bottom: 20px;
    padding: 0 22px;
    height: 82px;
    line-height: 82px;
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
  }
  .reportInfo_main {
    padding: 20px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
    border-radius: 8px 8px 8px 8px;
  }
  .left_menu_wrap {
    margin-right: 20px;
    padding: 12px 10px;
    width: 198px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #a5c1dc;
    .item_wrap {
      text-align: center;
      .name {
        height: 35px;
        line-height: 35px;
        color: #333333;
      }
      .item_c_wrap {
        margin: 0 auto;
        width: 178px;
        height: 35px;
        line-height: 35px;
        font-size: 14px;
        border-radius: 5px 5px 5px 5px;
        cursor: pointer;
        &.item_c_act {
          border: 1px solid #53a9f9;
          color: #53a9f9;
        }
      }
    }
  }
  .right_wrap {
    flex: 1;
    .info_section_wrap {
      margin-bottom: 30px;
      padding: 0 0 20px;
      border-bottom: 4px dotted #818080;
    }
  }
}
</style>
