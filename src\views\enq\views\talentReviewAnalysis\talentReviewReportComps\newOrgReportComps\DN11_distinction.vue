<template>
    <div class="org_report_main distinction_wrap" :class="{'marginB_16':isPdf}">
        <div class="page_second_title">人才区分</div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="8">
                <div class="item_title">能力绩效矩阵</div>
                <talent-classify-matrix
                    :kpiRankOption="kpiRankOption"
                    :competenceRankOptions="competenceRankOptions"
                    :needCodeDesc="false"
                    :unit="''"
                    :matrixHeight="250"
                    :talentData="kpiCapablity"
                ></talent-classify-matrix>
            </el-col>
            <el-col :span="8">
                <div class="item_title">能力潜力矩阵</div>
                <talent-matrix
                    :headTitle="'潜力'"
                    :asideTitle="'能力'"
                    :matrixHeight="250"
                    :matrixData="developmentCapability"
                ></talent-matrix>
            </el-col>
            <el-col :span="24">
                <div class="item_title">直接下级组织人才分类</div>
                <tableComps
                    :needIndex="false"
                    :needPagination="false"
                    :tableData="tableSubordinateData"
                    border
                ></tableComps>
            </el-col>
            <el-col :span="24">
                <div class="item_title">人才分类详情</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentChange"
                    @handleSizeChange="handleSizeChange"
                    :tableData="tableData"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf">更多数据请查看网页版报告</div>
            </el-col>
            <el-col :span="24">
                <div class="item_title">不同人才管理建议</div>
                <ul class="manage_advice_wrap">
                    <li class="manage_advice_item flex_row_start" v-for="(item,index) in infoDataList" :key='index'>
                        <div class="title">{{item.title}}</div>
                        <div class="item_info">{{item.info}}</div>
                    </li>
                </ul>
            </el-col>

        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    // import { orgTalentDiff, orgTalentDiffDetail,orgTalentDiffMatrix } from "../../../../request/api";
    import { getTalentDifferentiation,talentClassificationDetails,getSubOrgTalentType} from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import talentClassifyMatrix from "@/components/talent/common/talentClassifyMatrix";
    import talentMatrix from "@/components/talent/common/talentMatrix";
    export default {
        name: "orgRDistinction",
        props: ["enqId", "orgCode","isPdf"],
        components: { tableComps, talentClassifyMatrix,talentMatrix },
        data() {
            return {
                kpiRankOption: [],
                competenceRankOptions: [],
                developmentOptions: [],
                kpiCapablity:{},
                developmentCapability:[],
                size: 10,
                current: 1,
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "人才分类",
                        elSpan: 8,
                        height:198,
                        chartType: "YBar",
                        dataKey:'talentClassification'
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "素质评价",
                        elSpan: 8,
                        chartType: "YBar",
                        dataKey:'quality'
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "发展潜力",
                        elSpan: 8,
                        chartType: "YBar",
                        dataKey:'potential'
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "绩效表现",
                        elSpan: 8,
                        chartType: "YBar",
                        dataKey:'perform'
                    },
                ],
                tableSubordinateData:{
                    columns:[],
                    data:[]
                },
                tableData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "orgName",
                        },
                        {
                            label: "姓名",
                            prop: "userName",
                        },
                        {
                            label: "岗位",
                            prop: "userName",
                        },
                        {
                            label: "人才类别",
                            prop: "talentName",
                        },
                        {
                            label: "素质评价",
                            prop: "quality",
                        },
                        {
                            label: "发展潜力",
                            prop: "potential",
                        },
                        {
                            label: "绩效表现",
                            prop: "perform",
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
                infoDataList:[
                    {
                        title:'明星人才',
                        info:'业绩与潜能双优的明星员工，有能力承担更高层级的任务，也是外部挖猎的主要对象，需要重点培养发展，注重给予平台和机会：短时间内安排合适的新职务或赋予更重要的责任，使其迅速获得晋升；个性化保留策略，激励倾斜。'
                    },
                    {
                        title:'骨干人才',
                        info:'设置绩效挑战目标，让其在原来的岗位上获得更多进步；培养、提升，以更胜任现有岗位；重点保留。'
                    },
                    {
                        title:'明日之星',
                        info:'业绩合格潜能较好的员工，是企业的中坚力量，可设定更高的工作目标，进行业绩辅导，重点在于帮助他们提高绩效。经过3个月～1年的培养周期，可以提拔到更高一层级或采取其他个性化保留策略。'
                    },
                    {
                        title:'核心人才',
                        info:'业绩优秀且潜能合格的员工，这部分人员是企业的业务骨干。加强培育向上一层级发展所需的核心能力，激发其展现更多潜能，经过3个月～1年的培养周期，可以提拔到更高一层级，给予历练机会；重点保留，合理激励。'
                    },
                    {
                        title:'稳定人才',
                        info:'绩效合格，但工作行为上存在不足的员工，要给这类人员业绩压力，给予足够的培训与发展机会，以更胜任现岗。 '
                    },
                    {
                        title:'待优化人才',
                        info:'分析绩效差的原因，帮助其提升个人专业能力，以不断改善绩效水平，逐步向稳定人才、核心人才迈进'
                    },
                ]
            };
        },
        created() {
            let docList = ["KPI_RANK", "COMPETENCE_RANK", "DEVELOPMENT_POTENTIAL"];
            this.$getDocList(docList).then((res) => {
                this.kpiRankOption = this.$util.deepClone(res.KPI_RANK).reverse();
                this.competenceRankOptions = res.COMPETENCE_RANK;
                this.developmentOptions = res.DEVELOPMENT_POTENTIAL;
            });
            // this.getData();
            // this.orgTalentDiffFn();
            // this.orgTalentDiffMatrixFn();
            this.getTalentDifferentiationFun()
            this.getSubOrgTalentTypeFun()
            this.talentClassificationDetailsFun()
        },
        mounted() {},
        methods: {
            // 人才区分-人才区分图
            getTalentDifferentiationFun(){
                getTalentDifferentiation({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then(res=>{
                    if(res.code == 200){
                        this.initChart(res.data)
                        this.kpiCapablity = res.data.capabilityPerformanceMatrix
                        this.developmentCapability = res.data.capabilityPotentialMatrix
                    }

                })
            },
            getSubOrgTalentTypeFun(){
                getSubOrgTalentType({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then(res=>{
                    if(res.code == 200){
                        this.tableSubordinateData.columns = [
                            {
                                label: "分类",
                                prop: "name",
                            }
                        ]
                        let tableTitle = res.data.legend.map(item=>{
                            return{
                                label:item.orgName,
                                prop:item.orgCode.replace(/\./g,'-')
                            }
                        })
                        this.tableSubordinateData.columns = this.tableSubordinateData.columns.concat(tableTitle)
                        this.tableSubordinateData.data = this.dotToline(res.data.dataList, "key");
                    }
                    // 
                    // tableSubordinateData:{
                    //     columns:[],
                    //     data:[]
                    // },
                })
            },
            dotToline(param, type, valueKey) {
                if (Array.isArray(param)) {
                    if (param.length == 0) {
                        return;
                    }
                    param.forEach((item) => {
                        if (typeof item == "object") {
                            for (const key in item) {
                                if (item.hasOwnProperty(key)) {
                                    if (type == "key") {
                                        let newKey = key.split(".").join("-");
                                        item[newKey] = item[key];
                                    }else if(type == "value"){
                                        let val = item[valueKey];
                                        item[valueKey] = val.split(".").join("-");
                                    }
                                    // delete item[key];
                                }
                            }
                        }
                    });
                    return param;
                }
            },
            // 人才区分-人才分类详情
            talentClassificationDetailsFun(){
                let params = {
                    size: this.tableData.page.size,
                    current: this.tableData.page.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                talentClassificationDetails(params).then(res=>{
                    if(res.code == 200){
                        this.tableData.data = res.data
                        this.tableData.page = res.page
                    }
                })

            },  
            handleCurrentChange(current) {
                this.tableData.page.current = current;
                this.talentClassificationDetailsFun();
            },
            handleSizeChange(size) {
                this.tableData.page.size = size;
                this.talentClassificationDetailsFun();
            },
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                    };
                    if(chart.chartDomId == 'TALENT_CLASS'){
                        console.log('人才区分-人才分类');
                        console.log(chartData);
                    }
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },

            // ----------
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTalentDiff(params).then((res) => {
                    // console.log(res);
                    if (res.code == "200") {
                        this.initChart(res.data);
                    }
                });
            },
            orgTalentDiffFn() {
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTalentDiffDetail(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
            orgTalentDiffMatrixFn(){
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTalentDiffMatrix(params).then(res =>{
                    console.log(res);
                    if(res.code == 200){
                        this.kpiCapablity = res.data.kpiCapablity
                        this.developmentCapability = res.data.developmentCapability
                    }
                })
            },
        
        },
    };
</script>
 
<style scoped lang="scss">
.distinction_wrap{
    .manage_advice_wrap{
        .manage_advice_item{
            margin: 0 0 10px 0;
            border:1px solid #dcdfe6;
            height: 60px;
            .title{
                padding:0 0 0 15px;
                width:120px;
                line-height: 60px;
                background: #dae8fd;
                color: #008fff;
                font-weight: 600;
            }
            .item_info{
                padding: 5px 10px;
                line-height: 25px;
                flex: 1;
            }
        }
    }
}
</style>