<script setup>
import Table from '../components/table.vue'
import Tree from '@/components/tree/index.vue'
import Steps from '../components/steps.vue'
import { targetTending } from '@/assets/data/data9.js'
defineOptions({ name: 'orgIndicator' })
const stepsData = ref([
  {
    name: '目标值设定',
    finshSign: false,
    code: 1
  },
  {
    name: '智能检查',
    finshSign: false,
    code: 2
  },
  {
    name: '实际值维护',
    finshSign: false,
    startSign: false,
    code: 3
  }
])
const stepsNum = ref(1)
const dataStep1 = ref(targetTending.targetData)
const columnsStep1 = ref([
  {
    prop: 'orgName',
    label: '组织名称'
  },
  {
    prop: 'class',
    label: '目标类别',
    width: 120,
    sortable: true
  },
  {
    prop: 'period',
    label: '目标期间',
    width: 120,
    sortable: true
  },
  {
    prop: 'kpiName',
    label: '指标名称',
    width: 250,
    sortable: true
  },
  {
    prop: 'kpiUnit',
    label: '指标单位',
    width: 150,
    align: 'center'
  },
  {
    prop: 'kpiTarget',
    label: '指标目标值'
  },
  {
    prop: 'charge',
    label: '责任人'
  }
])

const data = ref(targetTending.targetStatistics)
const columns = ref([
  {
    prop: 'orgName',
    label: '组织名称',
    width: 160
  },
  {
    prop: 'orgCharge',
    label: '组织负责人',
    width: 120
  },
  {
    prop: 'targetClass',
    label: '目标类别'
  },
  {
    prop: 'period',
    label: '目标期间'
  },
  {
    prop: 'aimValue',
    label: '目标数量'
  },
  {
    prop: 'scalarType',
    label: '数量类'
  },
  {
    prop: 'economicClass',
    label: '经济类'
  },
  {
    prop: 'qualificationType',
    label: '质量类'
  },
  {
    prop: 'concurrency',
    label: '效率类'
  },
  {
    prop: 'restrain',
    label: '约束性类'
  },
  {
    prop: 'costPool',
    label: '成本类'
  }
])

const data2 = ref(targetTending.smartCheck)
const columns2 = ref([
  {
    prop: 'kpiName',
    label: '指标名称',
    width: 230
  },
  {
    prop: 'targetClass',
    label: '目标类别',
    width: 120
  },
  {
    prop: 'period',
    label: '目标期间',
    width: 120
  },
  {
    prop: 'checkResult',
    label: '智能检查结果',
    width: 380,
    showOverflowTooltip: true
  }
])

const dataStep3 = ref(targetTending.actualValue)
const columnsStep3 = ref([
  {
    prop: 'org',
    label: '组织名称',
    width: 160
  },
  {
    prop: 'targetClass',
    label: '目标类别',
    width: 120,
    sortable: true
  },
  {
    prop: 'period',
    label: '目标期间',
    sortable: true,
    width: 120
  },
  {
    prop: 'kpiName',
    label: '指标名称',
    width: 230,
    sortable: true
  },
  {
    prop: 'kpiUnit',
    label: '指标单位'
  },
  {
    prop: 'targetValue',
    label: '指标目标值'
  },
  {
    prop: 'realityValue',
    label: '指标实际值'
  },
  {
    prop: 'charge',
    label: '责任人'
  }
])

const treeData = ref([
  {
    id: 1,
    label: 'H冰箱公司',
    children: [
      {
        id: 10,
        label: '营销分公司（15）',
        children: [
          {
            id: 100,
            label: '北京分公司',
            children: [
              {
                id: 1000,
                label: 'H营销部',
                children: [
                  {
                    id: 1001,
                    label: '王伟'
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
])
const defaultChecked = ref([])
const defaultExpand = ref(true)
const stepSecond = ref([
  {
    name: '目标设定统计',
    code: 1
  },
  {
    name: '智能检查',
    code: 2
  }
])
const stepSecondCheck = ref(1)
const stepsStatus = n => {
  if (n == stepsNum.value || n < stepsNum.value) {
    return true
  }
}
const checkStepS = c => {
  stepSecondCheck.value = c
}
const operateBtn = c => {
  let maxArea = stepSecond.value.length + 1
  if (c == 'N' && stepsNum.value < maxArea) {
    stepsNum.value++
  } else if (c == 'P' && stepsNum.value > 1) {
    stepsNum.value--
  }
  console.log(stepSecondCheck.value)
  if (stepSecondCheck.value !== 1) {
    stepSecondCheck.value = 1
  }
}
const clickRow = o => {
  console.log(o)
}
onMounted(() => {})
</script>
<template>
  <div class="orgIndicator_wrap">
    <div class="steps_wap justify-start">
      <div class="title">应用步骤</div>
      <div class="steps_c">
        <Steps :stepsData="stepsData" :stepsNum="stepsNum"></Steps>
      </div>
    </div>
    <div class="" v-if="stepsNum == 1">
      <div class="orgIndicator_content justify-between">
        <div class="l_wrap">
          <div class="page-title-line">组织指标目标值设定</div>
          <div class="l">
            <div class="title">组织</div>
            <!-- <div class="second_t">未关联指标组织</div> -->
            <div class="tree_wrap">
              <Tree></Tree>
            </div>
          </div>
        </div>

        <div class="r">
          <div class="second_wrap justify-between">
            <div class="page-title-line">组织指标目标值项目</div>
            <div class="second_r justify-between">
              <div class="add_btn"><span class="icon"></span> 新增目标</div>
              <div class="operate_data justify-between">
                <div class="l_btn operate_btn"><span class="icon icon_import"></span> 导入</div>
                <div class="r_btn operate_btn"><span class="icon icon_export"></span> 导出</div>
              </div>
            </div>
          </div>
          <Table :data="dataStep1" :columns="columnsStep1" :showIndex="true">
            <template #default="scope">
              <el-table-column class="icon_wrap" min-width="90px">
                <el-icon @click.prevent="clickRow(scope.$index)"><i-ep-Edit /></el-icon>
                <el-icon @click.prevent="clickRow(scope.$index)"><i-ep-Delete /></el-icon>
              </el-table-column>
            </template>
          </Table>

          <div class="btn_wrap justify-between">
            <div class="bnt next_btn" @click="operateBtn('N')">下一步</div>
          </div>
        </div>
      </div>
    </div>
    <div class="" v-if="stepsNum == 2">
      <div class="orgIndicator_content justify-between">
        <div class="l_wrap">
          <div class="page-title-line">组织指标目标值智能检查</div>
          <div class="l">
            <div class="title">组织</div>
            <!-- <div class="second_t">未关联指标组织</div> -->
            <div class="tree_wrap">
              <Tree></Tree>
            </div>
          </div>
        </div>

        <div class="r">
          <div class="step_second_wrap justify-start">
            <div
              class="item_wrap"
              :class="{ act: stepSecondCheck == item.code }"
              v-for="item in stepSecond"
              :key="item.code"
              @click="checkStepS(item.code)"
            >
              <span></span>{{ item.name }}
            </div>
          </div>
          <div class="" v-if="stepSecondCheck == 1">
            <Table :data="data" :columns="columns" :showIndex="true">
              <template #default="scope">
                <el-table-column class="icon_wrap">
                  <div @click.prevent="clickRow(scope.$index)">
                    <el-icon><i-ep-Document /></el-icon>
                  </div>
                </el-table-column>
              </template>
            </Table>
          </div>
          <div class="" v-if="stepSecondCheck == 2">
            <Table :data="data2" :columns="columns2" :showIndex="true">
              <template #default="scope">
                <el-table-column class="icon_wrap" min-width="260px">
                  <el-button type="primary" plain round @click.prevent="clickRow(scope.$index)">关联查看</el-button>
                  <el-button type="primary" plain round @click.prevent="clickRow(scope.$index)">AI建议</el-button>
                  <el-button type="primary" plain round @click.prevent="clickRow(scope.$index)">反馈调整</el-button>
                </el-table-column>
              </template>
            </Table>
          </div>
          <div class="btn_wrap justify-between">
            <div class="bnt pre_btn" @click="operateBtn('P')">上一步</div>
            <div class="bnt next_btn" @click="operateBtn('N')">下一步</div>
          </div>
        </div>
      </div>
    </div>
    <div class="" v-if="stepsNum == 3">
      <div class="orgIndicator_content justify-between">
        <div class="l_wrap">
          <div class="page-title-line">组织指标目标值设定</div>
          <div class="l">
            <div class="title">组织</div>
            <!-- <div class="second_t">未关联指标组织</div> -->
            <div class="tree_wrap">
              <Tree></Tree>
            </div>
          </div>
        </div>

        <div class="r">
          <div class="second_wrap justify-between">
            <div class="page-title-line">组织指标目标值项目</div>
            <div class="second_r justify-between">
              <div class="add_btn"><span class="icon"></span> 新增目标</div>
              <div class="operate_data justify-between">
                <div class="l_btn operate_btn"><span class="icon icon_import"></span> 导入</div>
                <div class="r_btn operate_btn"><span class="icon icon_export"></span> 导出</div>
              </div>
            </div>
          </div>
          <Table :data="dataStep3" :columns="columnsStep3" :showIndex="true">
            <template #default="scope">
              <el-table-column class="icon_wrap" min-width="100px">
                <el-icon @click.prevent="clickRow(scope.$index)"><i-ep-Edit /></el-icon>
                <el-icon @click.prevent="clickRow(scope.$index)"><i-ep-Delete /></el-icon>
              </el-table-column>
            </template>
          </Table>
          <div class="btn_wrap justify-between">
            <div class="bnt pre_btn" @click="operateBtn('P')">上一步</div>
            <div class="bnt next_btn" @click="operateBtn('N')">确认</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.orgIndicator_wrap {
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
  font-size: 14px;
  .justify-start {
    display: flex;
    justify-content: flex-start;
  }
  .justify-between {
    display: flex;
    justify-content: space-between;
  }
  .steps_wap {
    margin: 20px auto 40px;
    .title {
      width: 80px;
      color: #53a9f9;
      font-size: 16px;
      font-weight: 600;
    }
    .steps_c {
      width: calc(100% - 50px);
      margin: 0 0 0 -80px;
      display: flex;
      justify-content: center;
    }
  }
  .second_wrap {
    margin-bottom: 4px;
    height: 36px;
    align-items: center;
    .page-title-line {
      margin-bottom: 16px;
    }
    .second_r {
      margin-top: -6px;
      .add_btn {
        width: 100px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        color: #fff;
        background: #40a0ff;
        border-radius: 6px 6px 6px 6px;
        cursor: pointer;
        .icon {
          display: inline-block;
          margin-bottom: -2px;
          width: 16px;
          height: 16px;
          background: url('@/assets/imgs/indicator/icon_01.png') no-repeat center;
        }
      }
      .operate_data {
        margin-left: 10px;
        height: 36px;
        line-height: 36px;
        .operate_btn {
          width: 68px;
          text-align: center;
          border: 1px solid #666666;
          cursor: pointer;
          .icon {
            display: inline-block;
            margin-bottom: -2px;
            width: 16px;
            height: 16px;
          }
        }
        .l_btn {
          border-radius: 6px 0 0 6px;
          border-right: 1px solid transparent;
          .icon {
            background: url('@/assets/imgs/indicator/icon_02.png') no-repeat center center;
          }
        }
        .l_btn:hover {
          color: #40a0ff;
          border: 1px solid #40a0ff;
          .icon {
            background: url('@/assets/imgs/indicator/icon_04.png') no-repeat center center;
          }
          + .r_btn {
            border-left: 1px solid transparent;
          }
        }
        .r_btn {
          border-radius: 0 6px 6px 0;
          .icon {
            background: url('@/assets/imgs/indicator/icon_03.png') no-repeat center center;
          }
        }
        .r_btn:hover {
          color: #40a0ff;
          border: 1px solid #40a0ff;
          .icon {
            background: url('@/assets/imgs/indicator/icon_05.png') no-repeat center center;
          }
        }
      }
    }
  }

  .orgIndicator_content {
    .l {
      padding: 8px;
      margin-right: 20px;
      width: 230px;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      .title {
        margin: 0 auto;
        width: 210px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
        background: #e3effa;
        border-radius: 8px 8px 8px 8px;
      }
      .second_t {
        font-size: 12px;
        height: 40px;
        line-height: 40px;
        color: #40a0ff;
      }
    }
    .r {
      width: calc(100% - 250px);
      .step_second_wrap {
        margin-bottom: 20px;
        .item_wrap {
          position: relative;
          margin-right: 18px;
          font-size: 16px;
          line-height: 20px;
          cursor: pointer;
          &.act {
            font-weight: 600;
            color: #333;
            span {
              display: inline-block;
              position: absolute;
              bottom: -8px;
              left: 0;
              right: 0;
              margin: 0 auto;
              width: 30px;
              height: 4px;
              background: #40a0ff;
              border-radius: 2px 2px 2px 2px;
            }
          }
        }
      }
      .btn_wrap {
        margin: 40px auto 0;
        width: 260px;
        .bnt {
          width: 120px;
          height: 36px;
          border-radius: 6px 6px 6px 6px;
          line-height: 36px;
          color: #40a0ff;
          text-align: center;
          border: 1px solid #40a0ff;
          cursor: pointer;
        }
        .pre_btn {
        }
        .next_btn {
          background: #40a0ff;
          color: #fff;
        }
      }
      :deep .el-table {
        .el-button {
          height: 24px;
          border: 1px solid #40a0ff;
        }
      }
    }
  }
}
:deep(.el-table) {
  .el-icon {
    margin-right: 20px;
    font-size: 18px;
    color: #40a0ff;
    cursor: pointer;
  }
  .el-icon:hover {
    color: #3778ea;
  }
}
</style>
