<script setup>
import { Search } from "@element-plus/icons-vue";
import SectionTab from "../../components/sectionTab.vue";
import Tree from "@/components/tree/index.vue";
import Table from "../../components/table.vue";

import curPerformance from "./curPerformance.vue";
import priorPerformance from "./priorPerformance.vue";
import indicatorChanges from "./indicatorChanges.vue";
import trendPrediction from "./trendPrediction.vue";

const key = ref("");

const sectionTabList = ref([
  {
    name: "当期表现",
    code: 1,
  },
  {
    name: "上期表现",
    code: 2,
  },
  {
    name: "指标变化",
    code: 3,
  },
  {
    name: "趋势预测",
    code: 4,
  },
]);
const sectionTabCheckSign = ref(1);
const checkSecTab = (c) => {
  sectionTabCheckSign.value = c;
};
const tabContentList = ref([
  curPerformance,
  priorPerformance,
  indicatorChanges,
  trendPrediction,
]);
</script>
<template>
  <div class="indicator_wrap">
    <div class="justify-start">
      <div class="left_menu_wrap marginR20">
        <el-input
          class="marginB20"
          v-model="key"
          style="width: 100%; height: 30px"
          placeholder="按组织名称检索"
          :suffix-icon="Search"
        >
        </el-input>
        <Tree></Tree>
      </div>
      <div class="right_main">
        <SectionTab
          :sectionTabList="sectionTabList"
          :sectionTabCheckSign="sectionTabCheckSign"
          @checkSecTab="checkSecTab"
          :itemWidth="'194px'"
        ></SectionTab>
        <div class="content-mian">
          <component :is="tabContentList[sectionTabCheckSign - 1]" />
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.indicator_wrap {
  .left_menu_wrap {
    width: 240px;
    padding: 8px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
  }
  .right_main {
    flex: 1;
    overflow: hidden;
    :deep .section_tab_wrap {
      .s_tab_item {
        &.act {
          background: linear-gradient(
            -20deg,
            #0594fa 0%,
            #83c8f8 39%,
            #83c8f8 59%,
            #0594fa 99%
          );
          color: #fff;
          border-color: transparent !important;
        }
      }
    }
    .content-mian {
      width: 100%;
    }
  }
}
</style>
