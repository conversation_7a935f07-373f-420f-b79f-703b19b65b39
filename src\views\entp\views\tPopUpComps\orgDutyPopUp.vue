<template>
  <el-dialog
    :title="props.popupTitleSign ? '新增职责' : '修改职责'"
    v-model="dialogVisible"
    @close="$emit('update:show', false)"
    width="40%"
    center
  >
    <div class="line_wrap flex_row_betweens">
      <span>职责名称：</span>
      <div>
        <el-input v-model="respName" />
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>业务领域：</span>
      <div>
        <el-select v-model="bizDomainCode" clearable placeholder="请选择">
          <el-option v-for="opt in bizDomainOptions" :key="opt.codeName" :label="opt.codeName" :value="opt.dictCode" />
        </el-select>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>职责描述：</span>
      <div>
        <el-input type="textarea" v-model="respDesc" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer align_right">
        <el-button class="page_clear_btn" @click="cancel">取 消</el-button>
        <el-button class="page_add_btn" type="primary" @click="submitBtn">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'
import { getDomainList, createResp, updateResp, getRespInfo } from '../../request/api'

// Props定义
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  popupTitleSign: {
    type: Boolean,
    default: false
  },
  editRespCode: {
    type: String,
    default: ''
  }
})

// Emits定义
const emit = defineEmits(['update:show', 'updateSuccessSign'])

// Store
const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

// 响应式状态
const dialogVisible = computed({
  get: () => props.show,
  set: value => emit('update:show', value)
})

const bizDomainCode = ref('')
const bizDomainOptions = ref([])
const respDesc = ref('')
const respName = ref('')

// 方法定义
const clearFormData = () => {
  respName.value = ''
  bizDomainCode.value = ''
  respDesc.value = ''
}

const getDomainListFun = async () => {
  try {
    const res = await getDomainList({
      companyId: companyId.value,
      rStatus: ''
    })

    if (res.code == 200 && res.data.length > 0) {
      bizDomainOptions.value = res.data.map(item => ({
        dictCode: item.bizDomainCode,
        codeName: item.bizDomainName
      }))
    } else {
      bizDomainOptions.value = []
    }
  } catch (error) {
    console.error('获取业务领域失败:', error)
    ElMessage.error('获取业务领域失败')
    bizDomainOptions.value = []
  }
}

const createRespFun = async () => {
  try {
    const res = await createResp({
      bizDomainCode: bizDomainCode.value,
      respDesc: respDesc.value,
      respName: respName.value
    })

    if (res.code == 200) {
      emit('updateSuccessSign', true)
      dialogVisible.value = false
      ElMessage.success(res.msg)
    } else {
      emit('updateSuccessSign', false)
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('创建职责失败:', error)
    emit('updateSuccessSign', false)
    ElMessage.error('创建职责失败')
  }
}

const updateRespFun = async () => {
  try {
    const res = await updateResp({
      bizDomainCode: bizDomainCode.value,
      respDesc: respDesc.value,
      respName: respName.value,
      respCode: props.editRespCode
    })

    if (res.code == 200) {
      emit('updateSuccessSign', true)
      dialogVisible.value = false
      ElMessage.success(res.msg)
    } else {
      emit('updateSuccessSign', false)
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('更新职责失败:', error)
    emit('updateSuccessSign', false)
    ElMessage.error('更新职责失败')
  }
}

const getRespInfoFun = async () => {
  try {
    const res = await getRespInfo({
      respCode: props.editRespCode
    })

    if (res.code == 200) {
      bizDomainCode.value = res.data.bizDomainCode
      respName.value = res.data.respName
      respDesc.value = res.data.respDesc
    }
  } catch (error) {
    console.error('获取职责信息失败:', error)
    ElMessage.error('获取职责信息失败')
  }
}

const cancel = () => {
  dialogVisible.value = false
}

const submitBtn = () => {
  if (!respName.value) {
    ElMessage.warning('请填写职责名称！')
    return
  }

  if (!bizDomainCode.value) {
    ElMessage.warning('请选择业务领域！')
    return
  }

  if (props.popupTitleSign) {
    createRespFun()
  } else {
    updateRespFun()
  }
}

// 监听
watch(
  companyId,
  val => {
    if (val) {
      getDomainListFun()
    }
  },
  { immediate: true }
)

watch(
  () => props.show,
  val => {
    if (val) {
      clearFormData()
      if (!props.popupTitleSign) {
        getRespInfoFun()
      }
    }
  }
)
</script>

<style scoped>
.line_wrap {
  margin-bottom: 20px;
}

.line_wrap span {
  width: 120px;
  text-align: right;
  margin-right: 10px;
  color: #606266;
}

.line_wrap div {
  flex: 1;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

:deep(.el-dialog__header) {
  background-color: var(--color-tagbg);
  padding: 15px 20px;
}

:deep(.el-input),
:deep(.el-select) {
  width: 100%;
}

:deep(.el-textarea__inner) {
  resize: none;
  font-family:
    PingFang SC,
    Avenir,
    Helvetica,
    Arial,
    sans-serif;
  font-size: 14px;
}
</style>
