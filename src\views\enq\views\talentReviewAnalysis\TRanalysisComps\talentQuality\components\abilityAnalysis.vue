<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">核心能力</div>
          <div class="content_item_content" id="view"></div>
        </div>
      </div>
      <div class="content_item el-col-16">
        <div class="content_item_main">
          <div class="content_item_title">部门分布</div>
          <div class="content_item_content" id="orgView"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title">岗位分布</div>
          <div class="content_item_content" id="postView"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title">
            详情列表
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import {
  qualityAllOrgDist as fetchQualityAllOrgDist,
  queryCompetenceList,
  exportData
} from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref({})
const page = ref(1)
const size = ref(10)

const view = reactive({
  data: []
})

const orgView = reactive({
  data: []
})

const postView = reactive({
  data: []
})

const tableData = reactive({
  columns: [
    {
      label: '员工编码',
      prop: 'employee_code'
    },
    {
      label: '员工姓名',
      prop: 'user_name'
    },
    {
      label: '所属组织',
      prop: 'org_name'
    },
    {
      label: '任职岗位',
      prop: 'post_name'
    },
    {
      label: '职层',
      prop: 'job_level_name'
    },
    {
      label: '核心能力',
      prop: 'competence'
    },
    {
      label: '评价人',
      prop: 'superior'
    },
    {
      label: '评价日期',
      prop: 'evaluationTime'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('view', 'YBar', '230', '220', view)
  echartsRenderPage('orgView', 'YStack', '470', '220', orgView)
  echartsRenderPage('postView', 'XBar', '700', '220', postView)
}

const qualityAllOrgDist = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      dictCode: 'COMPETENCE_RANK'
    }
    const res = await fetchQualityAllOrgDist(params)
    if (res.code == 200) {
      const data = res.data
      view.data = window.$util.addPercentSign(data.view, 'value')
      orgView.data = data.distributionView
      postView.data = data.postView
      initChart()
    }
  } catch (error) {
    console.error('获取能力分析数据失败:', error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  page.value = 1
  qualityAllOrgDist()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryCompetenceList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error('获取表格数据失败:', error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'n'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '能力分析详细列表')
  } catch (error) {
    console.error('导出数据失败:', error)
  }
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  qualityAllOrgDist()
  getTableData()
})
</script>

<style scoped lang="scss"></style>
