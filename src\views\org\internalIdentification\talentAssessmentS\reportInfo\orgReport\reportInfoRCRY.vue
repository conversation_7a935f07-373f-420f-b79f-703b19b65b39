<script setup>
import Table from "@/components/table/simplenessTable.vue";

const columns = ref([
  {
    label: "人员层级",
    prop: "a",
  },
  {
    label: "专业能力得分",
    prop: "b",
    align: "center",
  },
  {
    label: "专业能力匹配度",
    prop: "c",
    align: "center",
    children: [
      {
        label: "整体匹配",
        prop: "c1",
        align: "center",
      },
      {
        label: "匹配<60%",
        prop: "c2",
        align: "center",
      },
      {
        label: "60~80%",
        prop: "c3",
        align: "center",
      },
      {
        label: "80~100%",
        prop: "c4",
        align: "center",
      },
      {
        label: "100%及以上",
        prop: "c5",
        align: "center",
      },
    ],
  },

  {
    label: "团队协作",
    prop: "d",
    align: "center",
    children: [
      {
        label: "主要特征",
        prop: "d1",
        align: "center",
      },
      {
        label: "占比",
        prop: "d2",
        align: "center",
      },
    ],
  },
  {
    label: "决策模式",
    prop: "e",
    align: "center",
    children: [
      {
        label: "主要特征",
        prop: "e1",
        align: "center",
      },
      {
        label: "占比",
        prop: "e2",
        align: "center",
      },
    ],
  },
  {
    label: "决策风格",
    prop: "f",
    align: "center",
    children: [
      {
        label: "主要特征",
        prop: "f1",
        align: "center",
      },
      {
        label: "占比",
        prop: "f2",
        align: "center",
      },
    ],
  },
]);
const data = ref([
  {
    a: "最高决策层",
    b: "12",
    c1: "",
    c2: "",
    c3: "",
    c4: "",
    c5: "",

    d1: "36",
    d2: "36",

    e1: "36",
    e2: "36",

    f1: "36",
    f2: "36",
  },
]);

const columns2 = ref([
  {
    label: "人员层级",
    prop: "a",
    align: "center",
  },
  {
    label: "引入互补人才（管理风格补充）",
    prop: "b",
    align: "center",
    children: [
      {
        label: "团队协作",
        prop: "b1",
        align: "center",
        children: [
          {
            label: "特征",
            prop: "b11",
            align: "center",
          },
          {
            label: "人数",
            prop: "b12",
            align: "center",
          },
        ],
      },
      {
        label: "决策模式",
        prop: "b2",
        align: "center",
        children: [
          {
            label: "特征",
            prop: "b21",
            align: "center",
          },
          {
            label: "人数",
            prop: "b22",
            align: "center",
          },
        ],
      },
      {
        label: "决策风格",
        prop: "b3",
        align: "center",
        children: [
          {
            label: "特征",
            prop: "b31",
            align: "center",
          },
          {
            label: "人数",
            prop: "b32",
            align: "center",
          },
        ],
      },
    ],
  },
  {
    label: "引入互补人才（专业能力补充）",
    prop: "c",
    align: "center",
    children: [
      {
        label: "专业能力1",
        prop: "c1",
        align: "center",
        children: [
          {
            label: "专业能力名称",
            prop: "c11",
            align: "center",
          },
          {
            label: "人数",
            prop: "c12",
            align: "center",
          },
        ],
      },
      {
        label: "专业能力2",
        prop: "c2",
        align: "center",
        children: [
          {
            label: "专业能力名称",
            prop: "c21",
            align: "center",
          },
          {
            label: "人数",
            prop: "c22",
            align: "center",
          },
        ],
      },
      {
        label: "专业能力3",
        prop: "c3",
        align: "center",
        children: [
          {
            label: "专业能力名称",
            prop: "c31",
            align: "center",
          },
          {
            label: "人数",
            prop: "c32",
            align: "center",
          },
        ],
      },
    ],
  },
]);
const data2 = ref([
  {
    a: "最高决策层",
    b11: "",
    b12: "",
    b21: "",
    b22: "",
    b31: "",
    b32: "",

    c11: "",
    c12: "",
    c21: "",
    c22: "",
    c31: "",
    c32: "",

    // c: "36",

    // d: "36",
    // e: "36",
    // f: "36",
    // g: "36",
    // h: "36",
  },
]);

const columns3 = ref([
  {
    label: "人员姓名",
    prop: "a",
    width: 80,
  },
  {
    label: "当前岗位",
    prop: "b",
  },
  {
    label: "当前岗位匹配度",
    prop: "c",
  },
  {
    label: "推荐岗位",
    prop: "d",
  },
  {
    label: "推荐岗位匹配度",
    prop: "e",
  },
  {
    label: "核心匹配优势",
    prop: "f",
    width: 150,
  },
  {
    label: "关键风险提示",
    prop: "g",
    width: 150,
  },
  {
    label: "决策模式适配性",
    prop: "h",
  },
  {
    label: "任用建议",
    prop: "i",
  },
  {
    label: "实施优先级",
    prop: "j",
    slot: "jSlot",
    width: 130,
  },
]);
const data3 = ref([
  {
    a: "王伟",
    b: "市场调研专员",
    c: "62%",
    d: "海外市场战略分析师",
    e: "62%",
    f: "1. 风险识别能力突出（87分） 2. 数据驱动决策模式匹配 3. 跨文化敏感度优势",
    g: "1. 团队协作能力短板（49分） 2. 战略定力不足预警",
    h: "数据驱动型→系统思考型",
    i: "平级调岗+专项培养",
    j: 3,
  },
]);

const columns4 = ref([
  {
    label: "人员姓名",
    prop: "a",
    width: 150,
  },
  {
    label: "发展类别",
    prop: "b",
  },
  {
    label: "参考标准",
    prop: "c",
  },
  {
    label: "补充能力 / 调整管理风格",
    prop: "d",
  },
  {
    label: "发展建议",
    prop: "e",
  },
]);
const data4 = ref([
  {
    a: "王伟",
    b: "能力提升",
    c: "专业能力匹配度>80%",
    d: "市场分析与战略规划：50~60",
    e: "参与公司级流程优化项目，强制使用 ROI 模型分析方案",
  },
]);

onMounted(() => {});
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">人才任用建议</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        基于能力测评数据，精准识别团队成员能力与目标岗位胜任力要求的匹配程度，通过岗位调整实现
        “人岗适配”，最大化释放人才效能。
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">整体任用匹配</div>
      <Table
        :roundBorder="false"
        :columns="columns"
        :data="data"
        headerColor
        showIndex
      >
      </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">整体任用匹配建议</div>
      <Table
        :roundBorder="false"
        :columns="columns2"
        :data="data2"
        headerColor
        showIndex
      >
      </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">人员任用匹配</div>
      <Table
        :roundBorder="false"
        :columns="columns3"
        :data="data3"
        headerColor
        showIndex
      >
        <template v-slot:jSlot="scope">
          <el-rate v-model="scope.row.j" disabled />
        </template>
      </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">人员任用匹配建议（王伟-采购经理）</div>
      <Table
        :roundBorder="false"
        :columns="columns4"
        :data="data4"
        headerColor
        showIndex
      >
      </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
