<template>
  <svg :class="svgClass" aria-hidden="false">
    <use :xlink:href="iconName" :fill="color" />
  </svg>
</template>

<script setup>
const props = defineProps({
  name: {
    type: String,
    required: true
  },
  className: {
    type: String,
    default: ''
  },
  color: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: String,
    default: '1em'
  },
  height: {
    type: String,
    default: '1em'
  }
})
const iconName = computed(() => `#icon-${props.name}`)
const svgClass = computed(() => {
  if (props.className && !props.disabled) {
    return `svg-icon ${props.className}`
  }
  if (props.disabled) {
    return `svg-icon disabled`
  }
  return 'svg-icon'
})
</script>

<style scope lang="scss">
.sub-el-icon,
.nav-icon {
  display: inline-block;
  font-size: 15px;
  margin-right: 12px;
  position: relative;
}

.svg-icon {
  width: v-bind(width);
  height: v-bind(height);
  position: relative;
  fill: currentColor;
  color: v-bind(color);
  vertical-align: -2px;
  &.disabled {
    color: #777;
    pointer-events: none;
  }
}
</style>
