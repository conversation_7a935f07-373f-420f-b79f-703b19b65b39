<template>
  <div class="relationship_groups_wrap bg_write">
    <div class="page_main_title">组织关联</div>
    <div class="page_section">
      <div class="relationship_groups_center clearfix">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title">组织</div>
          </div>
          <div class="aside_tree_list">
            <treeCompRadio :treeData="treeData" @clickCallback="clickCallback" />
          </div>
        </div>
        <div class="page_section_main page_shadow" v-if="searchTargetSign">
          <div class="flex_row_betweens">
            <div class="page_section_aside">
              <div class="aside_tree_title flex_row_between">
                <div class="overflow_elps tree_title">指标类型</div>
              </div>
              <div class="aside_tree_list">
                <treeCompRadio
                  :treeData="kpiClassTreeData"
                  :needCheckedFirstNode="false"
                  :canCancel="true"
                  v-model="defaultCheckedKeys"
                  @clickCallback="checkKpiClass"
                />
              </div>
            </div>
            <div class="table_comp_wrap">
              <tableComponent
                :tableData="tableData"
                :needIndex="true"
                :checkSelection="checkSelection"
                :selectionStatus="true"
                @selectionChange="selectionChange"
                @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange"
              />
              <div class="align_center">
                <el-button type="primary" class="page_confirm_btn" @click="batchAssociatedBtn()">确认</el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="page_section_main page_shadow" v-if="!searchTargetSign">
          <div class="filter_bar_wrap">
            <div class="flex_row_start"></div>
            <div class="filter_item">
              <el-button
                :class="{ act_btn: searchTargetSign == false, page_add_btn: true }"
                type="primary"
                @click="searchTarget('all')"
                >全部</el-button
              >
              <el-button
                :class="{ act_btn: searchTargetSign == true, page_add_btn: true }"
                type="primary"
                @click="searchTarget('related')"
                >已关联</el-button
              >
            </div>
          </div>
          <div>
            <tableComponent
              :tableData="tableDataR"
              :needIndex="true"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { getOrgDeptTree, getKpiClassTree, getOrgKpiList, relateOrgKpi } from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio.vue'
import tableComponent from '@/components/talent/tableComps/tableComponent.vue'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

const treeData = ref([])
const checkedId = ref('')
const kpiClassTreeData = ref([])
const tableData = reactive({
  columns: [
    { label: '指标分类', prop: 'kpiClassName' },
    { label: '指标编码', prop: 'kpiCode' },
    { label: '指标名称', prop: 'kpiName' },
    { label: '指标维度', prop: 'kpiTypeName' }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})

const searchTargetSign = ref(true)
const searchTargetTypeSign = ref('all')
const tableDataR = reactive({
  columns: [
    { label: '部门名称', prop: 'orgName' },
    { label: '上级部门', prop: 'parentOrgName' },
    { label: '指标分类', prop: 'kpiClassName' },
    { label: '指标编码', prop: 'kpiCode' },
    { label: '指标名称', prop: 'kpiName' },
    { label: '指标维度', prop: 'kpiTypeName' }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})
const kpiClassCode = ref('')
const checkSelection = ref([])
const defaultCheckedKeys = ref('')
const checkTargetList = ref([])

function getOrgDeptTreeFun() {
  getOrgDeptTree({ companyId: companyId.value }).then(res => {
    treeData.value = res.code == 200 && res.data.length > 0 ? res.data : []
  })
}

function clickCallback(val, isLastNode) {
  checkedId.value = val
  tableData.page.current = 1
  tableDataR.page.current = 1
  getOrgKpiListFun(searchTargetTypeSign.value)
}

function getKpiClassTreeFun() {
  kpiClassTreeData.value = []
  getKpiClassTree({ companyId: companyId.value }).then(res => {
    if (res.code == 200 && res.data.length > 0) {
      kpiClassTreeData.value = res.data
    }
  })
}

function checkKpiClass(val, isLastNode) {
  kpiClassCode.value = val
  tableData.page.current = 1
  tableDataR.page.current = 1
  getOrgKpiListFun(searchTargetTypeSign.value)
}

function getOrgKpiListFun(val) {
  tableData.data = []
  tableDataR.data = []
  checkSelection.value = []
  getOrgKpiList({
    kpiClassCode: val == 'all' ? kpiClassCode.value : '',
    orgCode: checkedId.value,
    type: val,
    current: val == 'all' ? tableData.page.current : tableDataR.page.current,
    size: val == 'all' ? tableData.page.size : tableDataR.page.size
  }).then(res => {
    if (res.code == 200) {
      if (res.data.length > 0) {
        if (val == 'all') {
          tableData.data = res.data.map(item => ({
            kpiClassName: item.kpiClassName,
            kpiCode: item.kpiCode,
            kpiName: item.kpiName,
            kpiTypeName: item.kpiTypeName,
            isRelated: item.isRelated
          }))
          nextTick(() => {
            for (let i = 0; i < tableData.data.length; i++) {
              if (tableData.data[i].isRelated == 'Y') {
                checkSelection.value.push(tableData.data[i])
              }
            }
          })
        } else {
          tableDataR.data = res.data.map(item => ({
            orgName: item.orgName,
            parentOrgName: item.parentOrgName,
            kpiClassName: item.kpiClassName,
            kpiCode: item.kpiCode,
            kpiName: item.kpiName,
            kpiTypeName: item.kpiTypeName
          }))
        }
      }
      if (val == 'all') {
        tableData.page.total = res.total
      } else {
        tableDataR.page = res.page
      }
    }
  })
}

function handleSizeChange(val) {
  tableData.page.current = 1
  if (searchTargetTypeSign.value == 'all') {
    tableData.page.size = val
  } else {
    tableDataR.page.size = val
  }
  getOrgKpiListFun(searchTargetTypeSign.value)
}
function handleCurrentChange(val) {
  if (searchTargetTypeSign.value == 'all') {
    tableData.page.current = val
  } else {
    tableDataR.page.current = val
  }
  getOrgKpiListFun(searchTargetTypeSign.value)
}

function selectionChange(val) {
  checkTargetList.value = val
}

function batchAssociatedBtn() {
  let kpicodeArr = []
  checkTargetList.value.forEach(item => {
    kpicodeArr.push(item.kpiCode)
  })
  relateOrgKpiFun(kpicodeArr)
}

function relateOrgKpiFun(val) {
  if (!checkedId.value) {
    ElMessage.warning('请选择组织！')
    return
  }
  let pro = {
    orgCode: checkedId.value,
    kpiCodes: val,
    kpiClassCode: kpiClassCode.value
  }
  relateOrgKpi({
    json: JSON.stringify(pro)
  }).then(res => {
    if (res.code == 200) {
      getOrgKpiListFun(searchTargetTypeSign.value)
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function searchTarget(val) {
  if (val == 'all') {
    defaultCheckedKeys.value = ''
    getKpiClassTreeFun()
    kpiClassCode.value = ''
    tableData.page.current = 1
    getOrgKpiListFun(val)
    searchTargetSign.value = true
  } else {
    if (!checkedId.value) {
      ElMessage.warning('请选择组织！')
      return
    }
    tableDataR.page.current = 1
    getOrgKpiListFun(val)
    searchTargetSign.value = false
  }
  searchTargetTypeSign.value = val
}

watch(
  companyId,
  val => {
    if (val) {
      getOrgDeptTreeFun()
      getKpiClassTreeFun()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.relationship_groups_wrap {
  .page_section_main {
    .check_num {
      margin: 0 10px 0 0;
      span {
        margin: 0 5px;
        display: inline-block;
        color: #0099ff;
      }
    }
    .page_section_aside {
      height: 580px;
      .aside_tree_list {
        height: 540px;
      }
    }
    .table_comp_wrap {
      flex: 1;
      .align_center {
        margin: 40px 0 0 0;
      }
    }
    .act_btn {
      background: #fff;
      color: #449cff;
    }
    .el-table__header tr th {
      line-height: 45px;
      font-size: 14px;
      padding: 0;
    }
    .el-table th > .cell {
      .el-checkbox__input {
        padding-left: 4px;
      }
    }
    .el-table .cell {
      line-height: 45px;
    }
  }
}
</style>
