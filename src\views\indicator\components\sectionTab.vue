<script setup>
defineOptions({ name: 'sectionTab' })
const props = defineProps(['sectionTabCheckSign', 'sectionTabList'])
const emits = defineEmits(['checkSecTab'])
const checkSecTab = c => {
  emits('checkSecTab', c)
}
</script>
<template>
  <div class="section_tab_wrap">
    <div
      class="s_tab_item"
      :class="{ act: props.sectionTabCheckSign == item.code }"
      v-for="item in props.sectionTabList"
      :key="item.code"
      @click="checkSecTab(item.code)"
    >
      {{ item.name }}
    </div>
  </div>
</template>
<style lang="scss" scoped>
.section_tab_wrap {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
  .s_tab_item {
    margin-right: 12px;
    width: 310px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    color: #333;
    font-size: 14px;
    background: url('../../../assets/imgs/indicator/img_01.png') no-repeat center center;
    background-size: 100% 100%;
    cursor: pointer;
    &.act {
      color: #40a0ff;
      background: url('../../../assets/imgs/indicator/img_02.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
}
</style>
