<template>
    <div class="talent_main">
        <div class="aside_filter_wrap">
            <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
        </div>
        <div class="talent_number_content page_section flex_row_wrap_start">
            <div class="content_item_title marginB_16">流程岗位分布</div>
            <tableComponent
                :needIndex="true"
                :tableData="tableData"
                @handleCurrentChange="handleCurrentChange"
                @handleSizeChange="handleSizeChange"
            ></tableComponent>
        </div>
    </div>
</template>
 
<script>
    import asideFilterBar from "../../asideFilterBar";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import { processDistribute } from "../../../../../request/api";

    export default {
        name: "processPostDistribute",
        props: [],
        components: {
            asideFilterBar,
            tableComponent,
        },
        data() {
            return {
                enqId: "",
                jobClassCode: "",
                orgCode: "",
                pageCurrent: 1,
                pageSize: 10,
                columns: [],
                tableData: {
                    columns: [
                        {
                            label: "一级流程",
                            prop: "firstProcess",
                        },
                        {
                            label: "二级流程",
                            prop: "secondProcess",
                        },
                        {
                            label: "三级流程",
                            prop: "thirdProcess",
                        },
                        {
                            label: "参与部门",
                            prop: "orgNum",
                        },
                        {
                            label: "参与岗位",
                            prop: "postNum",
                        },
                        {
                            label: "参与人数",
                            prop: "staffNum",
                        },
                    ],
                    data: [],
                    page: {},
                },
            };
        },
        created() {
            this.enqId = this.$route.query.enqId;
            this.processDistributeFun();
            this.filterData = this.$attrs.filterData;
        },
        mounted() {},
        methods: {
            processDistributeFun() {
                let params = {
                    enqId: this.enqId,
                    jobClassCode: this.jobClassCode,
                    orgCode: this.orgCode,
                    current: this.pageCurrent,
                    size: this.pageSize,
                };
                processDistribute(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        // this.columns = this.dotToline(res.data.orgList,'value','orgCode');
                        this.tableData.data = res.data;
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
            handleSizeChange(size) {
                this.pageSize = size;
                this.processDistributeFun();
            },
            handleCurrentChange(current) {
                this.pageCurrent = current;
                this.processDistributeFun();
            },
            getCode(orgCode, jobClassCode) {
                this.jobClassCode = jobClassCode;
                this.orgCode = orgCode;
                this.processDistributeFun();
            },
            addPercentSign(row, column, cellValue, index) {
                return cellValue + "%";
            },
            dotToline(param, type, valueKey) {
                if (Array.isArray(param)) {
                    if (param.length == 0) {
                        return;
                    }
                    param.forEach((item) => {
                        if (typeof item == "object") {
                            for (const key in item) {
                                if (item.hasOwnProperty(key)) {
                                    if (type == "key") {
                                        let newKey = key.split(".").join("-");
                                        item[newKey] = item[key];
                                    } else if (type == "value") {
                                        let val = item[valueKey];
                                        item[valueKey] = val.split(".").join("-");
                                    }
                                    // delete item[key];
                                }
                            }
                        }
                    });
                    return param;
                }
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>