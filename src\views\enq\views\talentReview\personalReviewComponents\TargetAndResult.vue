<template>
  <div class="target_result_main marginT_16">
    <div class="page_second_title">目标与关键结果</div>
    <div class="btn_wrap align_right">
      <el-button class="page_add_btn" type="primary" @click="add">新增目标与关键结果</el-button>
    </div>
    <div class="result_main marginT_16" v-for="(item, indexs) in tableBoxData" :key="indexs">
      <div class="post_process_table">
        <el-table class="table_wrap" :data="item.enqObjectiveRequests">
          <el-table-column prop="objectiveName" label="目标名称">
            <template v-slot="scope">
              <el-input v-model="scope.row.objectiveName" :disabled="scope.row.selected" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="weight" label="目标权重" width="150">
            <template v-slot="scope">
              <el-input v-model="scope.row.weight" :disabled="scope.row.selected" size="mini">
                <template v-slot:append>%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="selfScore" label="目标自评分值" width="100">
            <template v-slot="scope">
              <el-input v-model="scope.row.selfScore" :disabled="true" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template v-slot="scope">
              <el-button
                class="icon_edit"
                :icon="Edit"
                @click.prevent="tableEditRow(scope.$index, scope.row, indexs, 1)"
                link
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="post_process_table">
        <el-table class="table_wrap" :data="item.enqObjectiveResultRequests">
          <el-table-column prop="resultName" label="关键结果">
            <template v-slot="scope">
              <el-input v-model="scope.row.resultName" :disabled="scope.row.selected" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="weight" label="结果权重" width="150">
            <template v-slot="scope">
              <el-input v-model="scope.row.weight" :disabled="scope.row.selected" size="mini">
                <template v-slot:append>%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="selfScore" label="结果自评分值" width="100">
            <template v-slot="scope">
              <el-input v-model="scope.row.selfScore" :disabled="scope.row.selected" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template v-slot="scope">
              <el-button
                class="icon_edit"
                :icon="Edit"
                @click.prevent="tableEditRow(scope.$index, scope.row, indexs, 2)"
                link
              ></el-button>
              <el-button
                class="icon_del"
                :icon="Delete"
                @click.prevent="tableDeleteRow(scope.$index, scope.row, indexs, 2)"
                link
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex_row_betweens marginT_16">
        <el-button class="page_add_btn" type="primary" @click="addRusult(item.enqObjectiveResultRequests)"
          >新增关键结果</el-button
        >
        <div>
          <el-button class="page_add_btn" type="primary" @click="saveRusult(item)">保存</el-button>
          <el-button class="page_add_btn" type="primary" @click="delRusult(item, indexs)" v-if="indexs != 0"
            >删除目标与关键结果</el-button
          >
        </div>
      </div>
    </div>
    <div class="align_center marginT_16">
      <el-button type="primary" class="page_confirm_btn" @click="prevBtn" v-show="currentIndex != currentFirstCode"
        >上一步</el-button
      >
      <el-button type="primary" class="page_confirm_btn" @click="submit('nextStep')">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { getTarget, saveTarget, deleteTargetResult, deleteTarget, targetNextStep } from '../../../request/api'
import { Delete, Edit } from '@element-plus/icons-vue'
// import useUtil from '@/utils/utils' // 如有深拷贝工具请引入

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  orgCode: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])
const useUser = useUserStore()

const courseOptions = ref([])
const tableBoxData = ref([
  {
    objectiveId: '',
    enqObjectiveRequests: [{ objectiveName: '', weight: '', selfScore: '', selected: false }],
    enqObjectiveResultRequests: [{ resultName: '', weight: '', selfScore: '', selected: false }]
  }
])

const userId = computed(() => useUser.userInfo.userId)

onMounted(() => {
  if (window.$getDocList) {
    window.$getDocList(['TRAINING_COURSE']).then(res => {
      courseOptions.value = res.TRAINING_COURSE
    })
  }
  getAllEnqOrgTrainingFun()
})

function submit(stepType) {
  // 目标权重校验
  let count = 0
  let enqObjectiveRequestsArr = []
  for (let i = 0; i < tableBoxData.value.length; i++) {
    for (let j = 0; j < tableBoxData.value[i].enqObjectiveRequests.length; j++) {
      enqObjectiveRequestsArr.push(tableBoxData.value[i].enqObjectiveRequests[j].weight)
    }
  }
  let flag = enqObjectiveRequestsArr.some(item => {
    if (Number(item) == 0) return true
    count += Number(item)
  })
  if (flag || count !== 100) {
    ElMessage.warning('目标权重比例输入错误！')
    return
  }
  // 关键结果权重校验
  let enqObjectiveResultRequestsArr = []
  for (let i = 0; i < tableBoxData.value.length; i++) {
    enqObjectiveResultRequestsArr.push(checkResult(tableBoxData.value[i].enqObjectiveResultRequests))
  }
  let flag1 = enqObjectiveResultRequestsArr.some(item => !item)
  if (flag1) {
    ElMessage.warning('目标权重比例输入错误！')
    return
  }
  let params = {
    enqId: props.enqId,
    userId: userId.value
  }
  targetNextStep(params).then(res => {
    if (res.code == '200') {
      ElMessage.success('保存成功！')
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function checkResult(arr) {
  let count = 0
  let flag = arr.some(item => {
    if (Number(item.weight) == 0) return true
    count += Number(item.weight)
  })
  if (flag || count !== 100) {
    return false
  } else {
    return true
  }
}

function add() {
  let obj = {
    enqObjectiveRequests: [{ objectiveName: '', weight: '', selfScore: '', selected: false }],
    enqObjectiveResultRequests: [{ resultName: '', weight: '', selfScore: '', selected: false }]
  }
  tableBoxData.value.push(obj)
}

function addRusult(data) {
  let obj = data[data.length - 1]
  let addObj = { objectiveName: '', weight: '', selfScore: '', selected: false }
  if (!obj) {
    data.push(addObj)
    return
  }
  if (checkResultData(data, ['resultName', 'weight', 'selfScore'])) {
    data.push(addObj)
  } else {
    ElMessage.warning('请完善当前信息后新增！')
  }
}

function delRusult(row, index) {
  if (tableBoxData.value.length == 1) {
    ElMessage.warning('当前数据只有一条不能删除！')
    return
  }
  if (row && row.objectiveId) {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        deleteTarget({
          enqId: props.enqId,
          objectiveId: row.objectiveId,
          userId: userId.value
        }).then(res => {
          if (res.code == '200') {
            ElMessage.success('删除成功!')
            getAllEnqOrgTrainingFun()
          } else {
            ElMessage.error(res.msg)
          }
        })
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  } else {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        tableBoxData.value.splice(index, 1)
        ElMessage.success('删除成功!')
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  }
}

function saveRusult(items) {
  if (!checkResultData(items.enqObjectiveRequests, ['objectiveName', 'weight'])) {
    ElMessage.warning('请完善目标名称数据后提交！')
    return
  }
  if (!checkResultData(items.enqObjectiveResultRequests, ['resultName', 'weight', 'selfScore'])) {
    ElMessage.warning('请完善关键结果数据后提交！')
    return
  }
  let count = 0
  let flag = items.enqObjectiveResultRequests.some(item => {
    if (Number(item.weight) == 0) return true
    count += Number(item.weight)
  })
  if (flag || count !== 100) {
    ElMessage.warning('结果权重比例输入错误！')
    return
  }
  let params = {
    enqId: props.enqId,
    userId: userId.value,
    enqObjectiveRequests: items.enqObjectiveRequests,
    enqObjectiveResultRequests: items.enqObjectiveResultRequests,
    objectiveId: items.objectiveId ? items.objectiveId : ''
  }
  saveTarget(params).then(res => {
    if (res.code == '200') {
      ElMessage.success('保存成功！')
      getAllEnqOrgTrainingFun()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function getAllEnqOrgTrainingFun() {
  let params = {
    enqId: props.enqId,
    userId: userId.value
  }
  getTarget(params).then(res => {
    if (res.code == '200') {
      let objData = res.data
      for (let i = 0; i < objData.length; i++) {
        objData[i].enqObjectiveRequests.forEach(element => {
          element.selected = true
        })
        objData[i].enqObjectiveResultRequests.forEach(element => {
          element.selected = true
        })
      }
      tableBoxData.value = objData
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function tableEditRow(index, row, indexs, type) {
  // 如有深拷贝工具请替换为深拷贝，否则直接用JSON
  let obj = JSON.parse(JSON.stringify(row))
  obj.selected = !obj.selected
  if (type == 2) {
    tableBoxData.value[indexs].enqObjectiveResultRequests[index] = obj
  } else {
    tableBoxData.value[indexs].enqObjectiveRequests[index] = obj
  }
}

function tableDeleteRow(index, row, indexs, type) {
  if (type == 2) {
    if (tableBoxData.value[indexs].enqObjectiveResultRequests.length == 1) {
      ElMessage.warning('当前数据只有一条不能删除！')
      return
    }
  }
  if (!row.resultId) {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        if (type == 1) {
          tableBoxData.value[indexs].enqObjectiveRequests.splice(index, 1)
        } else {
          tableBoxData.value[indexs].enqObjectiveResultRequests.splice(index, 1)
        }
        ElMessage.success('删除成功!')
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  } else {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        deleteTargetResult({
          enqId: props.enqId,
          resultId: row.resultId,
          objectiveId: row.objectiveId,
          userId: userId.value
        }).then(res => {
          if (res.code == '200') {
            ElMessage.success('删除成功!')
            getAllEnqOrgTrainingFun()
          } else {
            ElMessage.error(res.msg)
          }
        })
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  }
}

function checkResultData(data, checkArr) {
  return !checkData(data, checkArr)
}

function checkData(data, checkArr) {
  for (let i = 0; i < data.length; i++) {
    const obj = data[i]
    for (let j = 0; j < checkArr.length; j++) {
      if (!obj[checkArr[j]]) {
        return true
      }
    }
  }
  return false
}

function prevBtn() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submit('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}
</script>

<style scoped lang="scss">
.target_result_main {
  .result_main {
    border: 1px solid #eee;
    padding: 15px 10px;
  }
}
.page_add_btn {
  padding: 0 10px;
}
.post_column {
  margin-right: 4px;
}
.el-input-group__append,
.el-input-group__prepend {
  padding: 0 10px;
}
.table_wrap {
  margin-bottom: 16px;
}

.post_column.active {
  .post_ipt {
  }
}

.post_ipt {
  width: 30px;
  height: 30px;
  border: 1px solid #ebeef5;
  cursor: pointer;
  color: #000;
  margin-right: 3px;
  text-align: center;
  font-weight: bold;
  line-height: 28px;

  &:hover {
    background: #b3e0fd;
  }

  &.active {
    position: relative;
    background: #0099fd;
    color: #fff;

    .icon {
      display: block;
    }
  }

  .icon {
    display: none;
    width: 100%;
    line-height: 28px;
    font-weight: bold;
  }
}

.el-table th > .cell {
  padding-left: 7px;
  padding-right: 7px;
  margin-right: 4px;
}

.el-table__row .cell {
  // padding: 0 2px;
}

.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: auto;
}
.el-table__header-wrapper {
  background-color: #f4f4f4;
}
</style>
