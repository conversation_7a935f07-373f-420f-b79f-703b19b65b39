<script setup>
import Table from "@/components/table/simplenessTable.vue";

const columns = ref([
  {
    label: "二级能力",
    prop: "a",
  },
  {
    label: "能力表现 （个人得分）",
    prop: "b",
  },
  {
    label: "参评人平均表现",
    prop: "c",
  },
  {
    label: "综合能力排名 （百分位）",
    prop: "d",
  },

  {
    label: "劣势等级",
    prop: "e",
  },
  {
    label: "需提升场景",
    prop: "f",
  },
  {
    label: "实施优先级",
    prop: "j",
    slot: "jSlot",
    width: 130,
  },
]);
const data = ref([
  {
    a: "市场分析与战略规划",
    b: "15",
    c: "15",
    d: "后35%",
    e: "明显劣势（D级",
    f: "在行业趋势突变时无法及时调整战略方向 ",
    j: 3,
  },
]);

const columns2 = ref([
  {
    label: "阶段",
    prop: "a",
  },
  {
    label: "工程模块",
    prop: "b",
  },
  {
    label: "核心内容",
    prop: "c",
    width: 260,
  },
  {
    label: "劣势等级",
    prop: "d",
    width: 260,
  },
  {
    label: "战略价值",
    prop: "e",
  },
]);
const data2 = ref([
  {
    a: "诊断",
    b: "战略盲区检测",
    c: "《三维战略扫描矩阵》（行业趋势误判指数+竞争格局误判图谱+自我定位偏差分析）",
    d: "复盘近三年战略决策案例，运用战略沙盘推演验证关键决策假设，输出《战略认知偏差热力分布图》",
    e: "建立战略校准基准线 ",
  },
]);

onMounted(() => {});
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">短板提升项</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        基于能力测评数据，精准识别团队成员能力与目标岗位胜任力要求的匹配程度，通过岗位调整实现
        “人岗适配”，最大化释放人才效能。
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">核心能力表现</div>
      <Table
        :roundBorder="false"
        :columns="columns"
        :data="data"
        headerColor
        showIndex
      >
        <template v-slot:jSlot="scope">
          <el-rate v-model="scope.row.j" disabled />
        </template>
      </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">能力短板提升工程（战略解码与目标分解）</div>
      <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor>
      </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
