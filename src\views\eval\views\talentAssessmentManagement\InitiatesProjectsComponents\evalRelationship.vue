<template>
  <div class="relationship_wrap">
    <div class="level_wrap" :class="{ event_none: !isEdit }" v-if="!editStaff">
      <div class="page_second_title">
        设置评价关系权重
        <span class="level_count">(选中的评价关系比例总和需等于100)</span>
      </div>
      <div class="flex_row_start marginT_16">
        <ul
          class="level_wrap_ul flex_row_start"
          :class="{ event_none: disFlag }"
        >
          <li
            @click="checkedLevel(item, index)"
            v-for="(item, index) in weightList"
            :key="index"
            :class="{ active: item.selected }"
          >
            <span>{{ item.relationTypeName }}</span>

            <el-input
              placeholder="请输入整数"
              v-model="item.weight"
              :disabled="disFlag"
              :class="{ border_blue: !disFlag }"
              @click.stop.native
            >
              <template v-slot:append>%</template>
            </el-input>
          </li>
        </ul>
      </div>
    </div>
    <div
      class="btn_wrap marginT_16"
      :class="{ event_none: !isEdit }"
      v-if="!editStaff"
    >
      <el-button
        v-if="disFlag"
        class="page_add_btn"
        type="primary"
        @click="switchDisFlag(0)"
        >编辑</el-button
      >
      <el-button
        v-else
        class="page_add_btn"
        type="primary"
        @click="switchDisFlag(1)"
        >保存</el-button
      >
    </div>
    <div class="flex_row_between marginT_20">
      <div class="page_second_title">评价关系</div>
      <div class="flex_row_start" v-if="isEdit">
        <el-button
          class="page_add_btn"
          type="primary"
          @click="importStaffDialog = true"
          >导入数据</el-button
        >
      </div>
    </div>
    <div
      class="loading_style"
      v-loading.fullscreen.lock="loading"
      element-loading-text="上传中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.5)"
    ></div>
    <div class="relationship_main flex_row_start">
      <tableComponent
        :loading="loadingStatus"
        :tableData="tableData"
        :needIndex="true"
        height="400"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      ></tableComponent>
    </div>
    <div class="talent_raview_btn_wrap align_center marginT_30" v-if="isEdit">
      <el-button class="page_confirm_btn" type="primary" @click="prev()"
        >上一步</el-button
      >
      <el-button
        class="page_confirm_btn"
        type="primary"
        v-if="!editStaff"
        @click="next()"
        >下一步</el-button
      >
      <el-button
        class="page_confirm_btn"
        type="primary"
        v-if="editStaff"
        @click="next('submit')"
        >确认</el-button
      >
    </div>
    <!-- 导入弹窗 -->
    <el-dialog
      title="开始导入"
      v-model="importStaffDialog"
      width="800"
      :before-close="importStaffDialogBeforeClose"
    >
      <div class="import_staff_wrap">
        <div class="import_staff_title">操作步骤:</div>
        <div class="oper_step">
          <p>1、下载《参与人员评价关系导入模板》</p>
          <p>2、打开下载表，标记需要参与本次测评的人员。</p>
          <p>3、信息输入完毕，点击“选择文件”按钮，选择excel文档。</p>
          <p>4、点击"开始导入",导入中如有任何疑问，请致电010-86482868。</p>
        </div>
        <div class="import_staff_title">填写须知:</div>
        <div class="oper_step">
          <p>1、不能在该Excel表中对员工信息类别进行增加、删除或修改；</p>
          <p>2、Excel中红色字段为必填字段，黑色字段为选填字段；</p>
          <p>3、如需取消部分特殊的评价关系，请删除相关评价记录即可;</p>
          <p>
            4、如需增加特殊的评价关系，请插入相关评价数据维护即可，新增的评价关系仅可添加下级评价、同级评价与协同评价
          </p>
        </div>
        <div
          class="fs16 main_color pointer download_file"
          @click="exportTemplate"
        >
          立即下载《参与人员评价关系导入模板》
        </div>
        <div class="upload_file_wrap">
          <el-input placeholder="请输入内容" v-model="fileName" readonly>
            <template v-slot:append>
              <label for="up" class="upload_label">
                选择文件
                <!-- <el-button type="primary">选择文件</el-button> -->
                <input
                  id="up"
                  style="display: none"
                  ref="file"
                  type="file"
                  accept=".xlsx"
                  class="form-control page_clear_btn"
                  @change="fileChange"
                />
              </label>
            </template>
          </el-input>
        </div>
      </div>
      <template v-slot:footer>
        <div>
          <el-button class="page_clear_btn" @click="importStaffDialog = false"
            >取 消</el-button
          >
          <el-button type="primary" class="page_add_btn" @click="importExcel"
            >开始导入</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  getRelationInfo,
  getEvalRelation,
  saveEvalRelation,
  getPostModule,
  saveUserObjectRelation,
  exportRelation,
  importRelation,
  getEvalPostObject,
} from "../../../request/api";
import treeCompRadio from "@/components/talent/treeComps/treeCompRadio";
import tableComponent from "@/components/talent/tableComps/tableComponent";

export default {
  name: "relationshipBetween",
  props: {
    evalId: {
      type: String,
      default: 0,
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
    // 是否是修改参与人员展示页面
    editStaff: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    treeCompRadio,
    tableComponent,
  },
  data() {
    return {
      importStaffDialog: false,
      fileName: null,
      uploadFile: null,
      loadingStatus: false,
      loading: false,
      disFlag: true,
      weightList: [
        {
          name: "自评",
          value: "",
        },
        {
          name: "上级",
          value: "",
        },
        {
          name: "下级",
          value: "",
        },
        {
          name: "同级",
          value: "",
        },
        {
          name: "协同岗位",
          value: "",
        },
      ],
      sameLevelVal: "",
      upLevelVal: "",
      lowerLevelVal: "",
      searchConfirmList: [],
      defaultCheckedKeys: [],
      checkedPostCode: "",
      tableData: {
        columns: [
          {
            label: "员工姓名",
            prop: "objectName",
            fixed: true,
          },
          {
            label: "一级组织",
            prop: "oneLevelName",
          },
          {
            label: "二级组织",
            prop: "twoLevelName",
          },
          {
            label: "三级组织",
            prop: "threeLevelName",
          },
          {
            label: "四级组织",
            prop: "fourLevelName",
          },
          {
            label: "五级组织",
            prop: "fiveLevelName",
          },
          {
            label: "任职岗位",
            prop: "objPostName",
          },
          {
            label: "职层",
            prop: "jobLevelName",
          },
          {
            label: "评价关系",
            prop: "relationName",
          },
          {
            label: "评价人",
            prop: "userName",
          },
          {
            label: "评价人岗位",
            prop: "postName",
          },
        ],
        data: [],
        page: {
          current: 1,
          size: 10,
          total: 0,
        },
      },
      size: 10,
      current: 1,
      pageData: [],
    };
  },

  created() {
    // this.getPostModuleFun();
    // this.getEvalPostObjectFun();
    this.getRelationInfoFun();
  },
  methods: {
    switchDisFlag(type) {
      if (type == 0) {
        this.disFlag = !this.disFlag;
      }
      if (type == 1) {
        this.saveEvalRelationFun();
      }
    },
    checkedLevel(row, index) {
      if (!row.selected) {
        // this.$set(this.weightList[index], "selected", true);
        this.weightList[index].selected = true;
        if (this.weightList[index].relationScopeList.length > 0) {
          this.weightList[index].checkedVal =
            this.weightList[index].relationScopeList[0].relationScope;
          this.searchConfirmList.push(
            JSON.parse(JSON.stringify(this.weightList[index]))
          );
        } else {
          this.searchConfirmList.push({
            selected: true,
            checkedVal: true,
            relationScopeList: [
              {
                relationScope: true,
                relationScopeName: row.relationTypeName,
              },
            ],
            relationType: row.relationType,
            relationTypeName: row.relationTypeName,
          });
        }
      } else {
        // this.$set(this.weightList[index], "selected", false);
        this.weightList[index].selected = false;
        this.searchConfirmList.some((item, idx) => {
          if (item.relationType == row.relationType) {
            this.searchConfirmList.splice(idx, 1);
          }
        });
      }
      // console.log(this.searchConfirmList)
    },
    getRelationInfoFun() {
      getRelationInfo({
        evalId: this.evalId,
      }).then((res) => {
        console.log(res);
        this.weightList = res;
        res.forEach((item) => {
          if (item.selected) {
            if (item.relationScopeList.length > 0) {
              let obj = JSON.parse(JSON.stringify(item));
              obj.relationScopeList.some((data) => {
                if (data.selected) {
                  obj.checkedVal = data.relationScope;
                }
              });
              this.searchConfirmList.push(obj);
            } else {
              this.searchConfirmList.push({
                selected: true,
                checkedVal: true,
                relationScopeList: [
                  {
                    relationScope: true,
                    relationScopeName: item.relationTypeName,
                  },
                ],
                relationType: item.relationType,
                relationTypeName: item.relationTypeName,
              });
            }
          }
        });
        this.getList();
      });
    },
    saveEvalRelationFun() {
      if (this.searchConfirmList.length == 0) {
        ElMessage.warning("请选择评价关系！");
        return;
      }
      let params = this.searchConfirmList.map((item) => {
        let weight = 0;
        this.weightList.some((data) => {
          if (item.relationType == data.relationType) {
            weight = data.weight;
          }
        });
        return {
          evalId: this.evalId,
          relationScope: item.checkedVal == true ? "" : item.checkedVal,
          relationType: item.relationType,
          weight: weight,
        };
      });
      let count = 0;
      let flag = params.some((item) => {
        if (Number(item.weight) == 0) {
          // console.log(Number(item.weight))
          return true;
        }
        count += Number(item.weight);
      });
      // console.log(count);
      if (flag || count != 100) {
        ElMessage.warning("评价关系权重比例输入错误！");
        return;
      }
      saveEvalRelation(params).then((res) => {
        // console.log(res)
        if (res.code == 200) {
          ElMessage.success("保存成功！");
          this.disFlag = !this.disFlag;
          this.tableData.data = [];
          this.getList();
        }
      });
    },
    handleSizeChange(size) {
      this.size = size;
      this.tableData.page.size = size;
      this.getList();
    },
    handleCurrentChange(current) {
      this.current = current;
      this.tableData.page.current = current;
      this.getList();
    },
    // 获取测评对象数据
    getList(objPostCode, objectId) {
      this.loadingStatus = true;
      let params = {
        evalId: this.evalId,
        // objPostCode:this.checkedPostCode,
        size: this.size,
        current: this.current,
      };
      getEvalRelation(params).then((res) => {
        console.log(res);
        if (res.code == 200) {
          // this.$set(this.tableData, "data", res.data);
          // this.$set(this.tableData, "page", res.page);
          this.tableData.data = res.data;
          this.tableData.page.total = res.total;
        }
        this.loadingStatus = false;
      });
    },
    // 获取评价关系数据
    getEvalPostObjectFun() {
      getEvalPostObject({ evalId: this.evalId }).then((res) => {
        console.log(res);
        this.pageData = res.map((row) => {
          return {
            value: row.objectName,
            code: row.objPostCode + "," + row.objectId,
            relationPostList: null,
            flag: false,
          };
        });
        let objectId = res[0]["objectId"];
        let objPostCode = res[0]["objPostCode"];
        this.pageData[0]["flag"] = true;
        // this.getList(objPostCode, objectId);
      });
    },
    //导出模板
    exportTemplate() {
      exportRelation({
        evalId: this.evalId,
      }).then((res) => {
        // console.log(res)
        const blob = new Blob([res]);
        const elink = document.createElement("a");
        elink.download = "参与测评员工评价关系表.xlsx";
        elink.style.display = "none";
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink);
      });
    },
    //导入
    importStaffDialogBeforeClose(done) {
      this.fileName = "";
      this.uploadFile = null;
      document.getElementById("up").value = null;
      done();
    },
    //导入数据
    fileChange(e) {
      // console.log(e)
      let formData = new FormData();
      let file = e.target.files[0];
      formData.append("file", file);
      formData.append("evalId", this.evalId);
      this.$refs.file.value = "";
      //把文件信息放入对象中
      this.fileName = file.name;
      this.uploadFile = formData;
    },
    importExcel() {
      this.loading = true;
      importRelation(this.uploadFile).then((res) => {
        this.loading = false;
        console.log(res);
        if (res.code == 200) {
          ElMessage.success("上传成功");
          this.importStaffDialog = false;
          this.fileName = null;
          this.uploadFile = null;
          this.getList();
        } else {
          this.fileName = null;
          this.uploadFile = null;
          ElMessage.warning(res.msg);
        }
      });
    },

    prev: function () {
      this.$emit("prevStep");
    },
    next: function (type) {
      if (type == "submit") {
        if (this.tableData.data.length == 0) {
          ElMessage.warning("请导入评价关系");
          return;
        }
        this.$emit("nextStep", type);
        return;
      }
      this.$emit("nextStep");
    },
  },
};
</script>

<style scoped lang="scss">
.level_count {
  color: #f00;
}
.relationship_wrap {
  width: 100%;
  .level_wrap {
    .level_wrap_ul {
      width: 900px;
      height: 70px;
      li {
        height: 62px;
        margin-right: 10px;
        border: 1px solid #e5e5e5;
        cursor: pointer;
        span {
          display: block;
          text-align: center;
          line-height: 30px;
          background: #ebf4ff;
        }
        .border_blue {
          .el-input__inner {
            border: 1px solid #0099ff;
          }
        }
        .el-input__inner {
          border-radius: 0;
          /*border-left: none;*/
          /*border-bottom: none;*/
        }
        .el-input-group__append {
          border-radius: 0;
          margin: 0;
          /*border-right: none;*/
          /*border-bottom: none;*/
        }
        .el-input.is-disabled {
          .el-input__inner {
            color: #212121;
            background: #fff;
          }
        }
        &.active {
          border: 1px solid #0099ff;
          span {
            background: #0099ff;
            color: #fff;
          }
        }
      }
    }
    .control {
      /*margin-top: 30px;*/
      margin-left: 10px;
      cursor: pointer;
      .icon_check {
        font-size: 24px;
        font-weight: bold;
        color: #0099ff;
        span {
          font-size: 14px;
          font-weight: normal;
        }
      }
    }
  }
  .search_wrap {
    margin-top: 20px;
    .search_title {
      height: 30px;
      line-height: 30px;
      background: #ebf4ff;
      padding-left: 10px;
    }
    .search_main {
      padding: 10px;
      min-height: 80px;
      border: 1px solid #e5e5e5;
      .search_item {
        line-height: 34px;
        .search_label {
          width: 60px;
          text-align: center;
          height: 24px;
          line-height: 24px;
          margin-top: 5px;
          padding: 0 4px;
          margin-right: 20px;
          background: #ebf4ff;
        }
        .el-radio-group {
          span {
            line-height: 34px;
          }
        }
      }
    }
  }
  .relationship_main {
    margin-top: 20px;
    .post_left {
      width: 250px;
      .tree_box {
        border: 1px solid #e5e5e5;
        height: 300px;
        .tree_title {
          height: 30px;
          line-height: 30px;
          background: #ebf4ff;
          padding: 0 10px;
        }
        .tree_main {
          height: 270px;
          padding: 10px;
          overflow-y: auto;
        }
      }
    }
    .relationship_right {
      width: 970px;
      padding-left: 20px;
      table {
        width: 100%;
        border: 1px solid #e5e5e5;
        border-spacing: 0;
        thead {
          tr {
            height: 45px;
            background: #f4f4f4;
            text-align: center;
            td {
              border-left: 1px solid #e5e5e5;
              border-top: 1px solid #e5e5e5;
              &:first-child {
                border-left: none;
              }
            }
            &:first-child {
              td {
                border-top: none;
              }
            }
          }
        }
        tbody {
          height: 465px;
          overflow-y: auto;
          tr {
            height: 45px;
            &:nth-child(even) {
              background: #f4f4f4;
            }
            td {
              border-left: 1px solid #e5e5e5;
              border-top: 1px solid #e5e5e5;
              &.can_handler {
                cursor: pointer;
              }
              &:first-child {
                border-left: none;
              }
              .td_disabled {
                font-size: 24px;
                color: #bbb;
              }
            }
          }
        }
      }
    }
  }
}
// .el-radio__input.is-checked .el-radio__inner {
//   background: #0099FF;
//   border-color: #0099FF;
// }
//  .table_wrap tbody td {
//   padding: 0 !important;
// }

//  .table_wrap tbody .cell {
//   padding: 0;
//   height: 100%;
//   cursor: pointer;
// }

.check_box_wrap {
  color: #0099fd;
  font-weight: 700;
  font-size: 24px;
}
.upload_wrap {
  position: relative;
  width: 80px;
  height: 30px;
  margin-left: 10px;
  cursor: pointer;
}
.import_btn {
  font-size: 0;
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}
// 导入
.import_staff_wrap {
  .import_staff_title {
    color: #515c71;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .oper_step {
    line-height: 28px;
    color: #515c71;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .download_file {
    display: block;
    margin-bottom: 16px;
  }

  .upload_file_wrap {
    margin-bottom: 16px;

    .upload_label {
      display: block;
      height: 28px;
      line-height: 28px;
      width: 100%;
      cursor: pointer;
    }
  }
}
.el-icon-loading:before {
  font-size: 24px;
}
</style>
