<template>
  <div class="target_result_main marginT_8">
    <div class="page_second_title">目标与结果评价</div>
    <div class="department_main marginT_8">
      <div class="personnel_item_wrap_left">
        <div
          class="personnel_item"
          v-for="(item, index) in personnelData"
          :class="{ completed: item.type == 'Y', curr: currIndex === index }"
          :key="index"
          @click="() => selectPersonnel(item, index)"
        >
          <span>{{ item.userName }}</span>
          <i class="icon el-icon-check" v-if="item.type == 'Y'"></i>
          <i class="icon disc" v-else></i>
        </div>
      </div>
      <div class="personnel_item_wrap_right">
        <div class="project_item">
          <span class="moduleName"></span>
          <span class="item">目标与关键结果</span>
        </div>
        <div>
          <targetAndResultList
            :tableBoxData="expectationPostOptions"
            :relationCode="relationCode"
          ></targetAndResultList>
        </div>
        <div class="align_center marginT_30">
          <el-button class="page_confirm_btn" type="primary" @click="saveChildDataFn">保存</el-button>
        </div>
      </div>
    </div>
    <div class="btn_wrap align_center marginT_30">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn" v-show="currentIndex != currentFirstCode"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="submitForm()">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getDirectSubordinates,
  getObjectiveResult,
  saveObjectiveResult,
  nextObjectiveResult
} from '../../../request/api'
import { objHasEmpty } from '@/utils/utils.js'
import targetAndResultList from './targetAndResultList.vue'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const currIndex = ref(0)
const userId = ref('')
const relationCode = ref('')
const personnelData = ref([])
const expectationPostOptions = ref({})

const selectPersonnel = (item, index) => {
  currIndex.value = index
  getQualityEvaluationItemFun(item)
}

const getQualityEvaluationItemFun = item => {
  userId.value = item.userId
  relationCode.value = item.relation
  const params = {
    enqId: props.enqId,
    userId: item.userId
  }
  getObjectiveResult(params).then(res => {
    if (res.code == '200') {
      expectationPostOptions.value = res.data
    } else {
      expectationPostOptions.value = {}
    }
  })
}

const flagData = items => {
  let flag1 = true
  let flag2 = true
  if (!checkResultData(items.enqObjectiveList, ['objectiveName', 'weight'])) {
    flag1 = false
  }
  if (relationCode.value == 3) {
    if (!checkResultData(items.enqObjectiveResults, ['resultName', 'weight', 'supScore', 'leaderScore'])) {
      flag1 = false
    }
  } else if (relationCode.value == 2) {
    if (!checkResultData(items.enqObjectiveResults, ['resultName', 'weight', 'leaderScore'])) {
      flag1 = false
    }
  } else {
    if (!checkResultData(items.enqObjectiveResults, ['resultName', 'weight', 'supScore'])) {
      flag1 = false
    }
  }
  return flag1 && flag2
}

const saveChildDataFn = () => {
  // 先判断空值
  const flagArr = expectationPostOptions.value.enqObjectiveResultList.map(item => flagData(item))
  const flagNull = flagArr.some(item => !item)
  if (flagNull) {
    ElMessage.warning('请完善数据后提交！')
    return
  }
  // 判断目标与关键结果表现
  if (!expectationPostOptions.value.objectiveOverallMerit) {
    ElMessage.warning('请完善目标与关键结果表现后提交！')
    return
  }
  // 先判断目标名称百分比
  let count = 0
  let enqObjectiveRequestsArr = []
  expectationPostOptions.value.enqObjectiveResultList.forEach(result => {
    result.enqObjectiveList.forEach(obj => {
      enqObjectiveRequestsArr.push(obj.weight)
    })
  })
  const flag = enqObjectiveRequestsArr.some(item => {
    if (Number(item) == 0) return true
    count += Number(item)
  })
  if (flag || count != 100) {
    ElMessage.warning('目标权重比例输入错误！')
    return
  }
  // 判断每一个关键结果比列
  let enqObjectiveResultRequestsArr = []
  expectationPostOptions.value.enqObjectiveResultList.forEach(result => {
    enqObjectiveResultRequestsArr.push(checkResult(result.enqObjectiveResults))
  })
  const flag1 = enqObjectiveResultRequestsArr.some(item => !item)
  if (flag1) {
    ElMessage.warning('目标权重比例输入错误！')
    return
  }
  // 调接口
  const params = {
    enqId: props.enqId,
    userId: userId.value,
    relation: relationCode.value,
    objectiveOverallMerit: expectationPostOptions.value.objectiveOverallMerit,
    relationModule: props.currentIndex,
    enqObjectiveResultList: expectationPostOptions.value.enqObjectiveResultList
  }
  saveObjectiveResult(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      getDeptUserPostFun()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const checkResult = arr => {
  let count = 0
  const flag = arr.some(item => {
    if (Number(item.weight) == 0) return true
    count += Number(item.weight)
  })
  return !(flag || count != 100)
}

const checkResultData = (data, checkArr) => {
  return !checkData(data, checkArr)
}

const checkData = (data, checkArr) => {
  for (let i = 0; i < data.length; i++) {
    const obj = data[i]
    if (objHasEmpty(obj, checkArr)) {
      return true
    }
  }
  return false
}

const getDeptUserPostFun = () => {
  const params = {
    enqId: props.enqId,
    type: 'objective'
  }
  getDirectSubordinates(params).then(res => {
    if (res.code == '200') {
      if (res.data.length > 0) {
        personnelData.value = res.data
        getQualityEvaluationItemFun(res.data[currIndex.value])
      } else {
        personnelData.value = []
        // getQualityEvaluationItemFun(res.data[currIndex.value])
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const submitForm = type => {
  const params = {
    enqId: props.enqId
  }
  console.log(type)

  nextObjectiveResult(params).then(res => {
    if (res.code == '200') {
      if (type) {
        emit('prevStep')
      } else {
        nextBtn()
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submitForm('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}

const nextBtn = () => {
  emit('nextStep')
}

onMounted(() => {
  getDeptUserPostFun()
})
</script>

<style scoped lang="scss">
.department_main {
  display: flex;
  .el-input-group__append,
  .el-input-group__prepend {
    padding: 0 10px;
  }
  .personnel_item_wrap_left {
    width: 200px;
    padding-right: 15px;
    border-right: 1px solid #ccc;
    .personnel_item {
      line-height: 30px;
      padding: 0 8px;
      color: #525e6c;
      font-size: 14px;
      background: #f8f8f8;
      margin-bottom: 5px;
      font-weight: bold;
      cursor: pointer;

      &.completed {
        color: #0099fd;
        background: #eef5fb;

        .icon {
          display: block;
        }
      }

      &.curr {
        background: #0099fd;
        color: #fff;

        .icon {
          display: block;
          color: #fff;

          &.disc {
            background: #fff;
          }
        }
      }

      .icon {
        float: right;
        font-weight: bold;
        line-height: 30px;
        text-align: center;
        color: #0099fd;

        &.disc {
          width: 8px;
          height: 8px;
          margin: 10px 4px 0 auto;
          border-radius: 50%;
          background: #ffc000;
        }
      }
    }
  }
  .personnel_item_wrap_right {
    padding-left: 15px;
    width: calc(100% - 200px);
    .project_item {
      padding: 10px 0;
      font-size: 16px;
      font-weight: bold;
      .moduleName {
        color: #0099ff;
      }
      .item {
        color: #333;
      }
    }
  }
}
</style>
