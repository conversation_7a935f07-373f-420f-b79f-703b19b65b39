<template>
    <div class="create_post_wrap bg_write">
        <div class="page_main_title">
            {{pageType == 'edit' ? '编辑职位':'新增职位'}}
            <div class="goback_geader" @click="goback()">
                <i class="el-icon-arrow-left"></i>返回
            </div>
        </div>
        <div class="page_section">
            <div class="create_post_center clearfix">
                <el-tabs tab-position="left" v-model="tabsDefault"  @tab-click="tabClick" >
                    <el-tab-pane v-for="tab in tabsData" :label="tab.label" :name="tab.name" :key="tab.name" :disabled="tab.isDisabledSign">
                        <component v-if="tab.name == tabsDefault"  :is="tabsDefault"
                            :jobCode="jobCode ? jobCode : ''" 
                            :jobClassCode="jobClassCode"
                            @submitSuccessTab='submitSuccessTab' @curJobCode='curJobCode'>
                        </component>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>
</template>
 
<script>

const basicInfo = () => import('./components/createJobComponents/basicInfo')
const quality = () => import('./components/createJobComponents/quality')
const ability = () => import('./components/createJobComponents/ability')
const duty = () => import('./components/createJobComponents/duty')
const process = () => import('./components/createJobComponents/process')
const workActivity = () => import('./components/createJobComponents/workActivity')
export default {
    name: "createJob",
    components: {
        basicInfo,
        quality,
        ability,
        duty,
        process,
        workActivity,
    },
    data() {
        return {
            pageType:this.$route.query.pageType == 'edit' ? 'edit' : 'create',
            jobCode:this.$route.query.jobCode ? this.$route.query.jobCode :'' ,//用来保存新增时返回的职位编码，供其他模块使用 or 编辑时路由携带的参数
            jobClassCode:this.$route.query.jobClassCode ? this.$route.query.jobClassCode : '',//新增时携带的所属序列编码，用于基本信息展示族群序列
            tabsDefault: "",
            tabsData:[
                {
                    id:1,
                    label:"基本信息",
                    name:"basicInfo",
                    isDisabledSign:false,
                },
                {
                    id:2,
                    label:"素质要求",
                    name:"quality",
                    isDisabledSign:true,
                },
                {
                    id:3,
                    label:"能力要求",
                    name:"ability",
                    isDisabledSign:true,
                },
                {
                    id:4,
                    label:"主要职责",
                    name:"duty",
                    isDisabledSign:true,
                },
                // {
                //     id:5,
                //     label:"参与流程",
                //     name:"process",
                //     isDisabledSign:true,
                // },
                // {
                //     id:6,
                //     label:"工作活动",
                //     name:"workActivity",
                //     isDisabledSign:true,
                // },
                
            ],
           
        };
    },
    created() {
        this.tabsDefault = this.tabsData[0].name;
    },
    mounted(){
        // console.log(this.orgCode)
        // console.log(this.$route.query.postCode)
        // console.log(this.jobCode)
        this.allTabCalcelDisabled()
    },
    methods: {
        goback(){
            this.$router.push({
                name:'jobManagement',
                params:{
                    useCache:true
                }
            })
        },
        tabClick(val,event){
            console.log(val,event)
        },
        //变更切换状态
        allTabCalcelDisabled(){
            if(!this.jobCode){
                return;
            }
            for(let i=0;i < this.tabsData.length;i++){
                if(this.tabsData[i].isDisabledSign == true){
                    this.tabsData[i].isDisabledSign = false
                }
            }

        },

        //基本信息提交成功后  所有tab都可任意切换  已添加过信息的模块要数据回显
        submitSuccessTab(submitSuccessTab){
            console.log(submitSuccessTab)
            if(submitSuccessTab == 'basicInfo'){
                this.tabsDefault = 'quality'
                this.allTabCalcelDisabled()
                console.log(this.tabsData)
            }else if(submitSuccessTab == 'quality'){
                this.tabsDefault = 'ability'
            }else if(submitSuccessTab == 'ability'){
                this.tabsDefault = 'duty'
            }else if(submitSuccessTab == 'duty'){
                // this.tabsDefault = 'process'
            }
            // else if(submitSuccessTab == 'process'){
            //     this.tabsDefault = 'workActivity'
            // }else if(submitSuccessTab == 'workActivity'){
            //     // 工作活动提交成功  跳 
            // }
        },

        curJobCode(jobCode){
            this.jobCode = jobCode
        },
    },
};
</script>
 
<style scoped lang="scss">
.create_post_center {
    padding-top: 28px !important;
}

.el-input {
    width: auto;
}
.el-tabs--left .el-tabs__header.is-left {
    width: 240px;
    padding: 10px 32px 0 16px;
    border-right: 1px solid #e4e7ed;
    margin-right: 36px;
}
.el-tabs--left .el-tabs__item.is-left {
    text-align: center;
}
.el-tabs__active-bar {
    display: none;
}
.el-tabs__nav-wrap::after {
    display: none;
}
.el-tabs__item {
    text-align: center;
    background-color: #e4e7ed;
    margin-bottom: 16px;
    line-height: 40px;
    font-size: 14px;
    font-weight: 500;
    color: #525e6c;
    border-radius: 4px;
    cursor: pointer;
}
.el-tabs__item.is-active {
    background-color: #0099FF;
    color: #fff;
}
</style>