<template>
    <div class="org_report_main" :class="{'marginB_16':isPdf}">
        <div class="page_second_title">工作驱动</div>
        <el-row :gutter="16">
            <el-col :span="24">
                <div class="item_title">工作驱动因素（ 哪些因素能够有效的驱动个人更加的投入工作 ）</div>
                <div class="chart_box" id="staffEstablishingByOrg"></div>
            </el-col>
            <el-col :span="24">
                <div class="item_title">激励因素（ 对其产生激励效果的因素 ）</div>
                <div class="incentive_wrap flex_row_betweens">
					<div class="incentive_item">
						<p class="title">强激励因素</p>
						<ul class="desc_wrap">
							<li class="desc_item" v-for="(item,index) in workIncentiveFactors.Strong" :key="index">{{item}}</li>
						</ul>
					</div>
					<div class="incentive_item">
						<p class="title">一般激励因素</p>
						<ul class="desc_wrap">
							<li class="desc_item" v-for="(item,index) in workIncentiveFactors.General" :key="index">{{item}}</li>
						</ul>
					</div>
					<div class="incentive_item">
						<p class="title">一般负激励因素</p>
						<ul class="desc_wrap">
							<li class="desc_item" v-for="(item,index) in workIncentiveFactors.GeneralNegative" :key="index">{{item}}</li>
						</ul>
					</div>
					<div class="incentive_item">
						<p class="title">强负激励因素</p>
						<ul class="desc_wrap">
							<li class="desc_item" v-for="(item,index) in workIncentiveFactors.StrongNegative" :key="index">{{item}}</li>
						</ul>
					</div>
				</div>
            </el-col>   
            <el-col :span="24">
                <div class="item_title">个人激励因素</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentChange"
                    @handleSizeChange="handleSizeChange"
                    :tableData="tableData"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf">更多数据请查看网页版报告</div>
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    // import { orgStaffEstabList, orgStaffEstab } from "../../../../request/api";
    import { getWorkDriven,getUserIncentiveFactors } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "workDriven",
        props: ["enqId", "orgCode","isPdf"],
        components: { tableComps },
        data() {
            return {
                size: 10,
                current: 1,
                // chartDom: [
                //     {
                //         chartDomId: "staffEstablishingByOrg1231",
                //         title: "工作驱动因素（ 哪些因素能够有效的驱动个人更加的投入工作 ）",
                //         elSpan: 18,
                //         chartType: "XBar",
                //         dataKey:'workDriven'
                //     },
                // ],
                workDriven:{
                    data:[]
                },
                tableData: {
                    columns: [],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
                workIncentiveFactors:[],
            };
        },
        created() {
            // this.getData();
            this.getUserIncentiveFactorsFun();
            this.getWorkDrivenFun()
        },
        mounted() {},
        methods: {
            getWorkDrivenFun(){
                getWorkDriven({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then(res=>{
                    if(res.code == 200){
                        this.workDriven.data = res.data.workDriven
                        echartsRenderPage(
                            'staffEstablishingByOrg',
                            'XBar',
                            '1100',
                            '260',
                            this.workDriven
                        );
                        this.workIncentiveFactors = res.data.workIncentiveFactors
                    }
                })
            },
            getUserIncentiveFactorsFun(){
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getUserIncentiveFactors(params).then(res=>{
                    if(res.code == 200){
                        this.tableData.columns = res.data.legend.map(item=>{
                            return{
                                label:item.name,
                                prop:item.code.replace(/\./g,'-')
                            }
                        })
                        this.tableData.data = this.dotToline(res.data.dataList, "key");
                        this.tableData.page = res.page
                    }
                })
            },
            dotToline(param, type, valueKey) {
                if (Array.isArray(param)) {
                    if (param.length == 0) {
                        return;
                    }
                    param.forEach((item) => {
                        if (typeof item == "object") {
                            for (const key in item) {
                                if (item.hasOwnProperty(key)) {
                                    if (type == "key") {
                                        let newKey = key.split(".").join("-");
                                        item[newKey] = item[key];
                                    }else if(type == "value"){
                                        let val = item[valueKey];
                                        item[valueKey] = val.split(".").join("-");
                                    }
                                    // delete item[key];
                                }
                            }
                        }
                    });
                    return param;
                }
            },
            handleCurrentChange(current) {
                this.current = current;
                this.getUserIncentiveFactorsFun();
            },
            handleSizeChange(size) {
                this.size = size;
                this.getUserIncentiveFactorsFun();
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .org_report_main{
        .incentive_wrap{
            .incentive_item{
                padding: 0 10px 10px;
                width: 24%;
                color: #008fff;
                background: #dae8fd;
                .title{
                    height: 35px;
                    line-height: 35px;
                    font-weight: 700;
                }
                .desc_wrap{
                    line-height: 20px;
                }
            }
        }
    }
        

</style>