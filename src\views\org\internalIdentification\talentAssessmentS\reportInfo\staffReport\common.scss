.com_right_wrap {
  .t {
    margin-bottom: 30px;
    color: #53a9f9;
  }
  .tab_list_wrap{
    margin-bottom: 40px;
    .item{
      width: 9%;
      height: 35px;
      line-height: 35px;
      text-align: center;
      font-size: 14px;
      color: #333333;
      background: linear-gradient( 347deg, #E2F3FF 0%, rgba(237,248,255,0.1) 99%);
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #C6DBF3;
      cursor: pointer;
    }
    .item:hover, .act{
      color: #40a0ff;
      box-shadow: 0px 0px 10px 0px rgba(90, 153, 238, 0.5);
      border: 1px solid #5a99ee;
    }
  }

  .info_section_wrap {
    margin-bottom: 64px;
    .section_title {
      .line {
        flex: 1;
        margin: 10px 7px 0;
        height: 1px;
        background: #d8d8d8;
      }
      .ai {
        margin-top: -5px;
        width: 73px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #40a0ff;
        background: #e1f3ff;
        border-radius: 121px 121px 121px 121px;
      }
      &.blue_section_wrap {
      }
    }
    .tips {
      padding: 10px 20px;
      font-size: 12px;
      color: #40a0ff;
      background: #eff9ff;
      border-radius: 5px 5px 5px 5px;
    }
    .section_main {
      margin: 0 -15px;
      .i_wrap {
        margin: 0 15px;
      }
      .chart_wrap {
        border: 1px solid #f3f9fd;
        flex: 1;
        .c_title {
          height: 30px;
          line-height: 30px;
          text-align: center;
          color: #40a0ff;
          background: #eaf4ff;
          border-radius: 51px 51px 51px 51px;
        }
      }
      .desc_wrap {
        padding: 18px 20px 10px;
        width: 535px;
        background: #e3efff;
        border-radius: 6px 6px 6px 6px;
        .t {
          margin-bottom: 16px;
        }
        .desc_main {
          font-size: 14px;
          line-height: 30px;
          color: #3d3d3d;
          .bolder {
            font-weight: 600;
          }
          .icon_s {
            display: inline-block;
            margin: 0 10px 2px 0;
            width: 5px;
            height: 5px;
            background: #333333;
            border-radius: 50%;
          }
        }
      }
    }
    .section2_main {
      .i_wrap {
        flex: 1;
      }
    }
    .chart_list_wrap {
      margin: 0 -10px;
      flex-wrap: wrap;
      .chart_wrap {
        padding: 16px 20px;
        margin: 0 10px 16px;
        width: 23.2%;
        height: 280px;
        background: linear-gradient(
            228deg,
            #e6f5ff 3%,
            #ffffff 21%,
            #ffffff 82%,
            #e6f5ff 100%
          ),
          rgba(255, 255, 255, 0.5);
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #c6dbf3;

        .c_title {
        }
      }
      .chart_wrap:hover {
        background: linear-gradient(
            228deg,
            #e6f5ff 3%,
            #ffffff 21%,
            #ffffff 82%,
            #e6f5ff 100%
          ),
          rgba(255, 255, 255, 0.5);
        box-shadow: 0px 4px 10px 0px #cad5e1;
        border: 1px solid rgba(64, 160, 255, 0.7);
      }
    }
    :deep .el-slider {
      width: 90%;
      margin: 0 auto;
      .el-slider__runway {
        background: #eaf0f6;
      }
      .el-slider__runway.is-disabled .el-slider__bar {
        background: #eaf0f6;
      }
      .el-slider__button {
        background: #40a0ff;
        border: 5px solid #b9daff;
      }
      .el-slider__marks {
        margin-top: -25px;
      }
      .el-slider__marks-text {
      }
      &.red_el-slider {
        .el-slider__button {
          background: #ff5e4c;
          border: 5px solid #ffd4d0;
        }
      }
    }
    :deep(.el-table) {
      width: 100%;
      .el-icon {
        margin-right: 20px;
        font-size: 18px;
        color: #40a0ff;
        cursor: pointer;
      }
      .el-icon:hover {
        color: #3778ea;
      }
      .el-button {
        border-radius: 4px;
        height: 24px;
        border: 1px solid #40a0ff;
      }
      .circle {
        display: inline-block;
        width: 65px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        color: #fff;
        border-radius: 10px;
      }
      // .yellow_bg {
      //   background: #6acefc;
      // }
      .green_bg1 {
        background: #40d476;
      }
      .green_bg2 {
        background: #a2e8ae;
      }
      // .green_bg3 {
      //   background: #6acefc;
      // }
      .red_bg1 {
        background: #ff5e4c;
      }
      .red_bg2 {
        background: #ffa79d;
      }
      .me_icon {
        display: inline-block;
        width: 24px;
        height: 30px;
        background: url('@/assets/imgs/org/icon_11.png') no-repeat center center;
        background-size:100% 100%;
        // background: #a0f0f6;
      }
      .el-rate {
        .el-rate__item{
          .el-icon{
            margin-right: 4px;
            color: #DEE6EE;
            &.is-active{
              color: #40a0ff;
            }
          }
        }
        // background: #a0f0f6;
      }
    }
    .half_wrap {
      width: 49%;
    }
    .desc_title {
      line-height: 50px;
    }
    &.three_seven_wrap{
      .l_wrap{
        width: 30%;
      }
      .r_wrap{
        width: 68%;
      }
    }
    &.half_section_wrap{
      .l_wrap,.r_wrap{
        width: 49%;
      }
    }
  }
  
}
.right2_wrap,
.right11_wrap {
  .info_section_wrap {
    margin-bottom: 30px;
  }
}