import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import IconsResolver from 'unplugin-icons/resolver'
export default () => {
  return Components({
    dirs: ['src/components/global'],
    extensions: ['vue'],
    deep: true,
    include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
    dts: false,
    resolvers: [
      ElementPlusResolver({
        // 开发环境不需要按需加载样式
        // importStyle: env.VITE_APP_ENV == 'development' ? false : 'sass'
        importStyle: true
      }),
      // 自动注册图标组件
      IconsResolver({
        enabledCollections: ['ep']
      })
    ]
  })
}
