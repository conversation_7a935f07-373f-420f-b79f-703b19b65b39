<template>
    <div class="choose_staff_wrap">
        <div class="choose_staff_main flex_row_betweens marginT_30">
            <div class="main_left">
                <p class="page_second_title">选择建模人员</p>
                <div class="flex_row_start marginT_20">
                    <div class="main_left_tree">
                        <p class="choose_staff_title">职群</p>
                        <div class="tree_main tree_main_item">
                            <treeCompCheckbox
                                :treeData="jobClassList"
                                @node-click-callback="curChooseJobClassData"
                            ></treeCompCheckbox>
                        </div>
                        <p class="choose_staff_title">职层</p>
                        <div class="tree_main tree_main_item">
                            <treeCompCheckbox
                                :treeData="jobLevelList"
                                @node-click-callback="curChooseJobLevelData"
                            ></treeCompCheckbox>
                        </div>
                    </div>
                    <div class="main_left_tree">
                        <p class="choose_staff_title">部门</p>
                        <div class="tree_main">
                            <treeCompCheckbox
                                :expandAll="false"
                                :defaultExpandedKeys="defaultExpandedKeys"
                                :treeData="orgList"
                                @node-click-callback="curChooseOrgData"
                            ></treeCompCheckbox>
                        </div>
                    </div>
                    <div class="main_left_choose">
                        <div class="choose_staff_title">
                            <span>人员</span>
                            <span>{{ personList.length }}人员</span>
                            <span
                                class="relative pointer"
                                @click="choosePostAll"
                            >
                                <span>全选</span>
                                <i
                                    :class="{ active: !choosePostAllStatus }"
                                    class="el-icon-check icon_check_all"
                                ></i>
                            </span>
                        </div>
                        <ul class="second_level_post_wrap">
                            <li
                                v-for="(item, index) in personList"
                                @click="choosePersonItem(item, index)"
                                class="flex_row_betweens"
                                :class="{ active: item.isChoose }"
                            >
                                <span>{{ item.userName }}</span>
                                <span
                                    v-if="item.isChoose"
                                    class="el-icon-check"
                                ></span>
                                <span v-else class="icon_check"></span>
                            </li>
                            <div
                                class="no_data_row"
                                v-if="personList.length == 0"
                            >
                                暂无数据
                            </div>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="main_right">
                <div class="page_second_title">
                    <span>已选人员</span>
                    <span>{{ checkedPersonList.length }}人员</span>
                </div>
                <div class="main_right_choose marginT_20">
                    <p class="choose_staff_title">
                        {{ checkedPersonList.length }}人
                    </p>
                    <ul class="second_level_post_wrap">
                        <li
                            v-for="(item, index) in checkedPersonList"
                            class="flex_row_betweens hover_style"
                        >
                            <span>{{ item.userName }}</span>
                            <span
                                class="el_del_bg el-icon-minus"
                                @click="removePost(item.userId, index)"
                            ></span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="align_center marginT_20">
            <el-button class="page_confirm_btn" type="primary" @click="prev()"
                >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="next()"
                >下一步</el-button
            >
        </div>
    </div>
</template>

<script>
    import treeCompCheckbox from "@/components/talent/treeComps/treeCompCheckbox";
    import { getFilter, selectModelUser } from "../../../request/api";

    export default {
        name: "chooseStaff",
        components: {
            treeCompCheckbox,
        },
        data() {
            return {
                jobClassList: [],
                jobLevelList: [],
                orgList: [],
                defaultExpandedKeys: [],
                checkedJobClassList: [],
                checkedJobLevelList: [],
                checkedOrgList: [],
                personList: [],
                checkedPersonList: window.sessionStorage.checkedPersonList
                    ? JSON.parse(window.sessionStorage.checkedPersonList)
                    : [],
                checkedPersonIdList: [],
                choosePostAllStatus: true,
            };
        },
        created() {
            if (this.checkedPersonList.length > 0) {
                this.checkedPersonList.forEach((item) => {
                    this.checkedPersonIdList.push(item.userId);
                });
            }
        },
        mounted() {
            this.getFilterFun();
        },
        methods: {
            curChooseJobClassData(data) {
                this.checkedJobClassList = data;
                this.selectModelUserFun();
            },

            curChooseJobLevelData(data) {
                this.checkedJobLevelList = data;
                this.selectModelUserFun();
            },
            curChooseOrgData(data) {
                this.checkedOrgList = data;
                this.selectModelUserFun();
            },
            choosePostAll() {
                let list = this.personList;
                if (list.length == 0) {
                    return;
                }
                let chooseStatusArr = [];
                for (let i = 0, len = list.length; i < len; i++) {
                    let chooseStatus = list[i].isChoose;
                    chooseStatusArr.push(chooseStatus);
                }

                // 列表中是否有选中的项
                // 有：全部取消选中
                // 无：全部选中
                this.choosePostAllStatus = chooseStatusArr.includes(true);
                this.personList.forEach((item, index) => {
                    this.$set(
                        this.personList[index],
                        "isChoose",
                        !this.choosePostAllStatus
                    );
                });
                // 取消选中
                this.personList.forEach((item, index) => {
                    if (this.choosePostAllStatus) {
                        let clearCode = item.userId;
                        this.checkedPersonList.some((list, index) => {
                            if (list.userId == clearCode) {
                                this.checkedPersonList.splice(index, 1);
                                this.checkedPersonIdList.splice(index, 1);
                            }
                        });
                    } else {
                        this.$set(this.personList[index], "isChoose", true);
                        this.checkedPersonList.push(item);
                        this.checkedPersonIdList.push(item.userId);
                    }
                });
            },
            choosePersonItem(row, index) {
                if (!this.personList[index].isChoose) {
                    this.$set(this.personList[index], "isChoose", true);
                    this.checkedPersonList.push(row);
                    this.checkedPersonIdList.push(row.userId);
                } else {
                    this.$set(this.personList[index], "isChoose", false);
                    let clearId = row.userId;
                    this.checkedPersonList.some((item, index) => {
                        if (item.userId == clearId) {
                            this.checkedPersonList.splice(index, 1);
                            this.checkedPersonIdList.splice(index, 1);
                        }
                    });
                }
            },
            removePost(userId, index) {
                this.$confirm("确认删除吗?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.checkedPersonList.splice(index, 1);
                        this.checkedPersonIdList.splice(index, 1);
                        this.personList.some((item, i) => {
                            if (item.userId == userId) {
                                this.$set(this.personList[i], "isChoose", false);
                                this.$message({
                                    type: "success",
                                    message: "删除成功!",
                                });
                            }
                        });
                    })
                    .catch(() => {});
            },
            prev() {
                if (this.checkedPersonList.length > 0) {
                    window.sessionStorage.checkedPersonList = JSON.stringify(
                        this.checkedPersonList
                    );
                }
                this.$emit("prevStep");
            },
            next() {
                if (this.checkedPersonList.length == 0) {
                    this.$msg.warning("请选择人员！");
                    return;
                }
                window.sessionStorage.checkedPersonList = JSON.stringify(
                    this.checkedPersonList
                );
                this.$emit("nextStep");
            },

            /*api*/
            getFilterFun() {
                getFilter().then((res) => {
                    if (res.code == 200) {
                        this.jobClassList = res.data.cmpyJobClassList.map(
                            (item) => {
                                return {
                                    code: item.code,
                                    value: item.value,
                                };
                            }
                        );
                        this.jobLevelList = res.data.cmpyJobLevelList.map(
                            (item) => {
                                return {
                                    code: item.code,
                                    value: item.value,
                                };
                            }
                        );
                        this.defaultExpandedKeys.push(
                            res.data.cmpyOrgInfoList[0]["code"]
                        );
                        this.orgList = res.data.cmpyOrgInfoList;
                    }
                });
            },
            selectModelUserFun() {
                if (
                    !this.checkedJobClassList.length &&
                    !this.checkedJobLevelList.length &&
                    !this.checkedOrgList.length
                ) {
                    this.personList = [];
                    return;
                }
                selectModelUser({
                    jobClassCodes: this.checkedJobClassList.toString(),
                    jobLevelCodes: this.checkedJobLevelList.toString(),
                    orgCodes: this.checkedOrgList.toString(),
                }).then((res) => {
                    if (res.code == 200) {
                        this.personList = res.data;
                        for (let i = 0; i < this.personList.length; i++) {
                            if (
                                this.checkedPersonIdList.includes(
                                    this.personList[i].userId
                                )
                            ) {
                                this.$set(this.personList[i], "isChoose", true);
                            }
                        }
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .choose_staff_wrap {
        .choose_staff_title {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 3px 8px;
            font-size: 16px;
            line-height: 28px;
            background-color: #EBF4FF;
            border-radius: 3px;
            .icon_check_all {
                display: inline-block;
                border: 2px solid #666;
                border-radius: 50%;
                width: 24px;
                height: 24px;
                font-size: 18px;
                font-weight: bold;
                color: #e5f0f9;
                margin-left: 5px;
                line-height: 22px;
                text-align: center;
                margin-right: -5px;
                &.active {
                    color: #0099ff;
                    border-color: #0099ff;
                }
            }
        }

        .main_left_title {
            height: 40px;
            line-height: 40px;
            font-weight: 700;
        }

        .choose_staff_main {
            .page_section {
                height: 500px;
            }

            .second_level_post_wrap {
                padding: 10px;
                height: 365px;
                overflow-y: auto;
                li {
                    position: relative;
                    margin: 5px 0 0 0;
                    padding: 0 5px;
                    height: 36px;
                    line-height: 36px;
                    cursor: pointer;
                    border: 1px solid #e4e4e4;

                    .icon_check {
                        position: absolute;
                        border: 1px solid #ddd;
                        border-radius: 50%;
                        right: 5px;
                        top: 5px;
                        width: 24px;
                        height: 24px;
                    }

                    .el-icon-check {
                        height: 35px;
                        font-size: 24px;
                        line-height: 35px;
                        font-weight: bold;
                    }

                    .el_del_bg {
                        position: absolute;
                        right: 5px;
                        top: 5px;
                        width: 24px;
                        height: 24px;
                        background: #ddd;
                        border-radius: 50%;
                        line-height: 24px;
                        font-size: 20px;
                        color: #fff;
                        text-align: center;
                    }

                    .el-icon-remove-outline {
                        height: 35px;
                        font-size: 24px;
                        line-height: 35px;
                        color: #ccc;
                    }
                }

                .active {
                    border: 1px solid #0099FF;
                    color: #0099FF;
                }

                .hover_style:hover {
                    background: #EBF4FF;
                }
            }

            .main_left {
                width: 75%;

                .flex_row_start {
                    .main_left_tree {
                        width: 30%;
                        //height: 400px;
                        border: 1px solid #e5e5e5;
                        margin-right: 20px;

                        .tree_main {
                            padding: 10px 0;
                            height: 365px;
                            overflow-y: auto;
                            &.tree_main_item {
                                height: 165px;
                            }
                        }
                    }

                    .main_left_choose {
                        width: 32%;
                        height: 400px;
                        border: 1px solid #e5e5e5;
                    }
                }
            }

            .main_right {
                width: 25%;

                .main_right_choose {
                    height: 400px;
                    border: 1px solid #e5e5e5;
                }
            }
        }
    }
</style>