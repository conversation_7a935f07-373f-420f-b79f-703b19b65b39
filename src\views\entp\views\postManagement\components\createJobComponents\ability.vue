<template>
    <div class="capability_requirements_main">
<!--        <div class="page_second_title">能力要求</div>-->
        <tabs-custom :tabsData="tabsData" :activeName="'all'" @tab-click="tabClick"></tabs-custom>
        <div class="flex_row_wrap_start">
            <div class="capability_item" v-for="item in capabilityData2" :key="item.moduleCode">
                <div class="title flex_row_between">
                    <div class="text">{{item.moduleName}}</div>
                    <div class="score_bar flex_row_between">
                        <div class="bar">
                            <span class="bar_inside" :style="{'width':item.expectedScore+'%'}"></span>
                        </div>
                        <div class="score">{{item.expectedScore}}</div> 
                    </div>
                </div>
                <div class="capability_item_content clearfix">
                    <div class="item" v-for="list in item.children" :key="list.moduleCode">{{list.moduleName}}</div>
                </div>
            </div>
        </div>
        <div class="btn_wrap align_center">
            <el-button class="page_confirm_btn" type="primary" @click='submitBtn'>保 存</el-button>
        </div>
    </div>
</template>
 
<script>
import {searchPost,jobInfoCapabilityRequirements} from '../../../../request/api'
import tabsCustom from "@/components/talent/tabsComps/tabsCustom";

export default {
    name: "ability",
    props: {
       jobCode:String, 
    },
    components: {
        tabsCustom
    },
    data() {
        return {
            postCode:this.curPostCodeCopy ? this.curPostCodeCopy : '',

            tabsData: [
                {
                    name: "all",
                    label: "全部"
                }
            ],
            // capabilityData: [
            //     {
            //         id: "gxtz",
            //         name: "个性特质",
            //         score: 65,
            //         items: [
            //             {
            //                 name: "结果导向",
            //                 score: 72
            //             },
            //             {
            //                 name: "敬业投入",
            //                 score: 72
            //             },
            //             {
            //                 name: "积极主动",
            //                 score: 72
            //             },
            //             {
            //                 name: "销售素质",
            //                 score: 72
            //             },
            //             {
            //                 name: "主动学习",
            //                 score: 72
            //             }
            //         ]
            //     },
            //     {
            //         id: "tyzs",
            //         name: "通用知识",
            //         score: 75,
            //         items: [
            //             {
            //                 name: "结果导向",
            //                 score: 72
            //             },
            //             {
            //                 name: "敬业投入",
            //                 score: 72
            //             },
            //             {
            //                 name: "积极主动",
            //                 score: 72
            //             },
            //             {
            //                 name: "销售素质",
            //                 score: 72
            //             },
            //             {
            //                 name: "主动学习",
            //                 score: 72
            //             },
            //             {
            //                 name: "主动学习",
            //                 score: 72
            //             },
            //             {
            //                 name: "主动学习",
            //                 score: 72
            //             }
            //         ]
            //     },
            //     {
            //         id: "zyzs",
            //         name: "专业知识",
            //         score: 55,
            //         items: [
            //             {
            //                 name: "结果导向",
            //                 score: 72
            //             },
            //             {
            //                 name: "敬业投入",
            //                 score: 72
            //             },
            //             {
            //                 name: "积极主动",
            //                 score: 72
            //             },
            //             {
            //                 name: "销售素质",
            //                 score: 72
            //             },
            //             {
            //                 name: "主动学习",
            //                 score: 72
            //             }
            //         ]
            //     },
            //     {
            //         id: "zyjn",
            //         name: "专业技能",
            //         score: 80,
            //         items: [
            //             {
            //                 name: "结果导向",
            //                 score: 72
            //             },
            //             {
            //                 name: "敬业投入",
            //                 score: 72
            //             },
            //             {
            //                 name: "积极主动",
            //                 score: 72
            //             },
            //             {
            //                 name: "销售素质",
            //                 score: 72
            //             },
            //             {
            //                 name: "主动学习",
            //                 score: 72
            //             }
            //         ]
            //     }
            // ],
             capabilityData: [
                {
                    id: "gxtz",
                    name: "个性特质",
                    score: 65,
                    items: [
                        {
                            name: "结果导向",
                            score: 72
                        },
                        {
                            name: "积极主动 ",
                            score: 72
                        },
                        {
                            name: "主动学习",
                            score: 72
                        },
                        {
                            name: "敬业投入",
                            score: 72
                        },
                        {
                            name: "销售素质",
                            score: 72
                        }
                    ]
                },
              
                {
                    id: "zyzs",
                    name: "专业知识",
                    score: 55,
                    items: [
                        {
                            name: "公司知识",
                            score: 72
                        },
                        {
                            name: "营销知识",
                            score: 72
                        },
                        {
                            name: "产品知识",
                            score: 72
                        },
                        {
                            name: "客户关系管理知识",
                            score: 72
                        },
                        {
                            name: "战略知识",
                            score: 72
                        }
                    ]
                },
                {
                    id: "tyzs",
                    name: "团队管理",
                    score: 75,
                    items: [
                        {
                            name: "建立销售团队",
                            score: 72
                        },
                        {
                            name: "辅导销售团队",
                            score: 72
                        },
                        {
                            name: "建立高目标",
                            score: 72
                        },
                        {
                            name: "激励销售团队",
                            score: 72
                        },
                        {
                            name: "授权委责",
                            score: 72
                        },
                        {
                            name: "引领变革",
                            score: 72
                        },
                        {
                            name: "文化建设",
                            score: 72
                        }
                    ]
                },
                {
                    id: "zyjn",
                    name: "专业技能",
                    score: 80,
                    items: [
                        {
                            name: "结果导向",
                            score: 72
                        },
                        {
                            name: "敬业投入",
                            score: 72
                        },
                        {
                            name: "积极主动",
                            score: 72
                        },
                        {
                            name: "销售素质",
                            score: 72
                        },
                        {
                            name: "主动学习",
                            score: 72
                        }
                    ]
                }
            ],
            capabilityData2: []
        };
    },
    created() {
        // let that = this;
        // let obj = {};
        // this.capabilityData.forEach(function(item, index) {
        //     obj.label = item.name;
        //     obj.name = item.id;
        //     that.tabsData.push(obj);
        //     obj = {};
        // });
        // this.capabilityData2 = this.capabilityData;
        this.jobInfoCapabilityRequirementsFun()

    },
    mounted(){
        this.searchPostFun()
    },
    methods: {
        //所有tab数据获取
        //  getDocList(){
        //     let params = ['COMPETENCE_CLASS'];
        //     this.$getDocList(params).then(res=>{
        //         console.log(res)
              
        //     })
        // },
        jobInfoCapabilityRequirementsFun(){
           jobInfoCapabilityRequirements({
               competenceClass:null,
               jobCode:this.jobCode,
               postCode:this.postCode
            }).then((res)=>{
                console.log(res)
                this.capabilityData = res.data

                this.tabsData = res.data.map(item=>{
                    return{
                      name:item.moduleCode,
                      label:item.moduleName  
                    }
                })
                this.capabilityData2 = this.capabilityData;
                this.tabsData.unshift({
                    name: "all",
                    label: "全部"
                })
            })
        },


        // 数据回显
        searchPostFun(){
            if(this.curPostCodeCopy){
                searchPost({
                    postCode:this.curPostCodeCopy 
                }).then(res=>{
                    console.log(res)
                })
            }
        },

        tabClick(tabItem, index) {
            let id = tabItem.name;
            this.getCapabilityData(id);
        },
        getCapabilityData(id) {
            if (id == "all") {
                this.capabilityData2 = this.capabilityData;
            } else {
                this.capabilityData2 = this.capabilityData.filter(function(
                    item
                ) {
                    return item.moduleCode == id;
                });
            }
        },
        submitBtn(){
            this.$emit('submitSuccessTab','ability')
        }
    },
    watch:{

    }
};
</script>
 
<style scoped lang="scss">
.capability_requirements_main {
    padding: 0 0 8px 8px;
}
.capability_item {
    width: 260px;
    padding: 8px 16px;
    margin: 0 16px 16px 0;
    border-radius: 8px;
    box-shadow: 0 0px 6px rgba(0, 0, 0, 0.2);
    .title {
        line-height: 22px;
        color: #0099fd;
        padding-bottom: 8px;
        margin-bottom: 8px;
        border-bottom: 1px solid #EBF4FF;
        .score_bar {
            align-items: center;
            font-size: 14px;
            .bar {
                position: relative;
                width: 57px;
                height: 6px;
                background: #EBF4FF;
                border-radius: 3px;
                margin-right: 10px;
                .bar_inside {
                    position: absolute;
                    width: 65%;
                    height: 100%;
                    top: 0;
                    left: 0;
                    // background: #0099FF;
                    background: #87c947;
                    border-radius: inherit;
                }
            }
            .score{
                color: #87c947;
            }
        }
    }
    .capability_item_content {
        .item {
            // float: left;
            width: 110px;
            line-height: 25px;
            // background: #daefff;
            color: #0099fd;
            font-size: 12px;
            margin: 0 8px 8px 0;
            border-radius: 4px;
            padding-left: 8px;
            // &:nth-of-type(2n) {
            //     margin-right: 0;
            // }
        }
    }
}
</style>