<template>
  <div class="detail_main">
    <div class="detail_main_aside">
      <div class="page_third_title">分析主题</div>
      <tabsLink :tabsData="tabsLinkData" :isVertical="true"></tabsLink>
    </div>
    <div class="detail_main_content">
      <div class="page_third_title">分析视图</div>
      <router-view :filterData="filterData"></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { queryOrgWithSubChildren, queryJobClassByOrg } from '../../../../request/api'
import tabsLink from '@/components/talent/tabsComps/tabsLink'

const route = useRoute()
const tabsLinkData = ref([
  {
    id: 'asdfsdfqteqa',
    name: '能力分析',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentQuality/abilityAnalysis'
  },
  {
    id: 'asdqrefafasdfa',
    name: '绩效分析',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentQuality/meritsAnalysis'
  },
  {
    id: 'asasdfdqrefasffa',
    name: '潜力分析',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentQuality/potentialAnalysis'
  },
  {
    id: 'asasdfdqrefa',
    name: '能力业绩矩阵',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentQuality/achievementMatrix'
  },
  {
    id: 'asdqrefaasfagdasdfa',
    name: '能力潜力矩阵',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentQuality/potentialMatrix'
  },
  {
    id: 'aadsfsdqfasdfrefa',
    name: '人才分类',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentQuality/talentClassify'
  },
  {
    id: 'asfadqrefdasfa',
    name: '晋升可能性',
    path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentQuality/promotionPossible'
  }
])

const filterData = reactive({
  orgData: [],
  jobClass: []
})

const queryOrgWithSubChildrenFun = async enqId => {
  try {
    const res = await queryOrgWithSubChildren({ enqId })
    if (res.code == 200) {
      filterData.orgData = res.data
    }
  } catch (error) {
    console.error('获取组织数据失败:', error)
  }
}

const queryJobClassByOrgFun = async () => {
  try {
    const res = await queryJobClassByOrg()
    if (res.code == 200) {
      filterData.jobClass = res.data
    }
  } catch (error) {
    console.error('获取岗位类别数据失败:', error)
  }
}

onMounted(() => {
  const enqId = route.query.enqId
  tabsLinkData.value.forEach(item => {
    item.path = `${item.path}?enqId=${enqId}`
  })
  queryOrgWithSubChildrenFun(enqId)
  queryJobClassByOrgFun()
})
</script>

<style lang="scss"></style>
