<template>
    <div class="post_requirement_main">
        <div class="department_main clear marginT_30">
            <div class="page_second_title">岗位编制与需求</div>
            <div class="department_content marginT_20">
                <el-table :data="tableData" style="width: 100%">
                    <el-table-column
                        type="index"
                        label="序号"
                        width="40"
                    ></el-table-column>
                    <el-table-column
                        prop="postName"
                        label="岗位名称"
                    ></el-table-column>
                    <el-table-column
                        prop="postName"
                        label="编制人数"
                        width="80"
                    >
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                v-model="scope.row.budgetedCount"
                                @change="calcShortageCount(scope.row)"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="postName" label="期初" width="65">
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                v-model="scope.row.initialCount"
                                @change="calcFinaCount(scope.row)"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="postName" label="入职" width="65">
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                v-model="scope.row.entrantCount"
                                @change="calcFinaCount(scope.row)"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="postName"
                        label="离职"
                        width="80"
                    >
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                v-model="scope.row.resignationCount"
                                @change="calcFinaCount(scope.row)"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column prop="postName" label="辞退" width="65">
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                v-model="scope.row.dimissionCount"
                                @change="calcFinaCount(scope.row)"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column> -->
                    <el-table-column prop="postName" label="调入" width="65">
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                v-model="scope.row.transInCount"
                                @change="calcFinaCount(scope.row)"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="postName" label="调出" width="65">
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                v-model="scope.row.transOutCount"
                                @change="calcFinaCount(scope.row)"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column prop="postName" label="晋升" width="65">
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                v-model="scope.row.promotionCount"
                                @change="calcFinaCount(scope.row)"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="postName" label="换岗" width="65">
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                v-model="scope.row.transferCount"
                                @change="calcFinaCount(scope.row)"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column> -->
                    <el-table-column prop="postName" label="期末" width="65">
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                disabled
                                v-model="scope.row.finalCount"
                                @change="calcShortageCount(scope.row)"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="postName" label="缺口" width="65">
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                disabled
                                v-model="scope.row.shortageCount"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="postName"
                        label="需求人数"
                        width="80"
                    >
                        <template slot-scope="scope">
                            <el-input
                                type="number"
                                v-model="scope.row.recruitCount"
                                size="mini"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="postName"
                        label="紧急程度"
                        width="80"
                    >
                        <template slot-scope="scope">
                            <el-select
                                clearable
                                v-model="scope.row.recruitEmergency"
                                size="mini"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in recruitEmergencyOptions"
                                    :label="item.codeName"
                                    :value="item.dictCode"
                                ></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="postName"
                        label="期望到岗"
                        width="100"
                    >
                        <template slot-scope="scope">
                            <el-select
                                clearable
                                v-model="scope.row.recruitExpectDeadline"
                                size="mini"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in expectOptions"
                                    :label="item.codeName"
                                    :value="item.dictCode"
                                ></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column v-for="col in columns" :prop="col.prop" :label="col.label" :width="col.width">
						<template slot-scope="scope">
							<el-input v-if="col.template == 'input' && col.needCalc" type="number" v-model="getNumber" size="mini"></el-input>
							<el-select v-else-if="col.template == 'select'" clearable v-model="scope.row[col.prop]" size="mini" placeholder="请选择">
								<el-option v-for="item in col.selectOptions" :label="item.codeName" :value="item.dictCode"></el-option>
							</el-select>
							<el-input v-else-if="col.template == 'input'" type="number" v-model="scope.row[col.prop]" size="mini"></el-input>
							<div v-else>
								{{scope.row[col.prop]}}
							</div>
						</template>
					</el-table-column> -->
                </el-table>
            </div>
        </div>
        <div class="marginT_30 align_center">
            <el-button class="page_confirm_btn" type="primary" @click="prevBtn"
                >上一步</el-button
            >
            <el-button
                class="page_confirm_btn"
                type="primary"
                @click="nextBtn"
                >{{ nextBtnText }}</el-button
            >
        </div>
    </div>
</template>

<script>
    import { getPostInfo, updateEnqPostDemand } from "../../../request/api";

    export default {
        name: "postRequirement",
        props: ["nextBtnText", "enqId", "orgCode"],
        components: {},
        computed: {
            getNumber(key, row) {
                console.log(key, row);
            },
            // orgCode() {
            // 	return this.$store.state.userInfo.orgCode;
            // }
        },
        created() {
            // this.enqId = this.$route.params.id;
            this.$getDocList(["EMERGENCY", "RECRUIT_EXPECT_DEADLINE"]).then(
                (res) => {
                    this.recruitEmergencyOptions = res.EMERGENCY;
                    this.expectOptions = res.RECRUIT_EXPECT_DEADLINE;
                    this.columns[13].selectOptions = res.EMERGENCY;
                    this.columns[14].selectOptions = res.RECRUIT_EXPECT_DEADLINE;
                    this.$forceUpdate();
                }
            );

            this.getPostInfoFun();
        },
        methods: {
            getPostInfoFun() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getPostInfo(params).then((res) => {
                    if (res.code == 200) {
                        this.tableData = res.data;
                        this.$forceUpdate();
                    }
                });
            },
            calcFinaCount(row) {
                row.finalCount =
                    Number(row.initialCount) +
                    Number(row.entrantCount) -
                    Number(row.resignationCount) +
                    // Number(row.dimissionCount) +
                    Number(row.transInCount) -
                    Number(row.transOutCount);
                    // Number(row.promotionCount) -
                    // Number(row.transferCount);
                row.shortageCount =
                    Number(row.budgetedCount) - Number(row.finalCount);
            },
            calcShortageCount(row) {
                row.shortageCount =
                    Number(row.budgetedCount) - Number(row.finalCount);
            },
            updateEnqPostDemandFun(stepType) {
                let backupData = this.$util.deepClone(this.tableData);
                let params = [];
                backupData.forEach((item, index) => {
                    let obj = {
                        budgetedCount: item.budgetedCount,
                        dimissionCount: item.dimissionCount,
                        enqId: item.enqId,
                        entrantCount: item.entrantCount,
                        finalCount: item.finalCount,
                        initialCount: item.initialCount,
                        postCode: item.postCode,
                        promotionCount: item.promotionCount,
                        recruitCount: item.recruitCount,
                        recruitEmergency: item.recruitEmergency,
                        recruitExpectDeadline: item.recruitExpectDeadline,
                        resignationCount: item.resignationCount,
                        shortageCount: item.shortageCount,
                        transInCount: item.transInCount,
                        transOutCount: item.transOutCount,
                        transferCount: item.transferCount,
                    };
                    params.push(obj);
                });
                updateEnqPostDemand(params).then((res) => {
                    if (res.code == "200") {
                        this.$msg.success(res.msg);
                        this.$emit(stepType);
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
            prevBtn() {
                let that = this;

                this.$confirm("即将离开当前页面，是否保存当前页数据?", "提示", {
                    distinguishCancelAndClose: true,
                    confirmButtonText: "保存",
                    cancelButtonText: "放弃修改",
                })
                    .then(() => {
                        this.updateEnqPostDemandFun("prevStep");
                    })
                    .catch((action) => {
                        this.$msg.info({
                            message:
                                action == "cancel"
                                    ? "已放弃修改并返回上一步"
                                    : "取消返回上一步",
                        });
                        action == "cancel" ? that.$emit("prevStep") : "";
                    });
            },
            nextBtn() {
                this.updateEnqPostDemandFun("nextStep");
            },
        },
        data() {
            return {
                recruitEmergencyOptions: [],
                expectOptions: [],
                columns: [
                    {
                        label: "岗位名称",
                        prop: "postName",
                        template: null,
                    },
                    {
                        label: "编制人数",
                        prop: "budgetedCount",
                        template: "input",
                        width: "80",
                    },
                    {
                        label: "期初",
                        prop: "initialCount",
                        template: "input",
                        width: 65,
                    },
                    {
                        label: "入职",
                        prop: "entrantCount",
                        template: "input",
                        width: 65,
                    },
                    {
                        label: "主动辞职",
                        prop: "resignationCount",
                        template: "input",
                        width: "80",
                    },
                    {
                        label: "辞退",
                        prop: "dimissionCount",
                        width: 65,
                        template: "input",
                    },
                    {
                        label: "调入",
                        prop: "transInCount",
                        width: 65,
                        template: "input",
                    },
                    {
                        label: "调出",
                        prop: "transOutCount",
                        width: 65,
                        template: "input",
                    },
                    {
                        label: "晋升",
                        prop: "promotionCount",
                        width: 65,
                        template: "input",
                    },
                    {
                        label: "换岗",
                        prop: "transferCount",
                        width: 65,
                        template: "input",
                    },
                    {
                        label: "期末",
                        prop: "finalCount",
                        width: 65,
                        template: "input",
                        needCalc: true,
                    },
                    {
                        label: "缺口",
                        prop: "shortageCount",
                        width: 65,
                        template: "input",
                        needCalc: true,
                    },
                    {
                        label: "需求人数",
                        prop: "recruitCount",
                        template: "input",
                        width: "80",
                    },
                    {
                        label: "紧急程度",
                        prop: "recruitEmergency",
                        template: "select",
                        selectOptions: [],
                        width: "80",
                    },
                    {
                        label: "期望到岗",
                        prop: "recruitExpectDeadline",
                        template: "select",
                        selectOptions: [],
                        width: "100",
                    },
                ],
                tableData: [
                    {
                        id: "",
                        postName: "",
                        budgetedCount: null,
                        initialCount: null,
                        entrantCount: null,
                        resignationCount: null,
                        dimissionCount: null,
                        transInCount: null,
                        transOutCount: null,
                        promotionCount: null,
                        transferCount: null,
                        finalCount: null,
                        shortageCount: null,
                        recruitCount: null,
                        recruitCount: null,
                        recruitEmergency: "紧急",
                        recruitExpectDeadline: "1个月内",
                    },
                ],
            };
        },
    };
</script>

<style scoped lang="scss">
    .department_main {
        .right_title {
            width: 182px;
        }
    }

    .el-table .cell {
        padding: 0 5px;
    }

    .el-input__inner {
        padding: 0 5px;
    }
    .el-table__row {
        height: 45px;
    }
</style>
