<template>
  <div class="post_import_wrap bg_write">
    <div class="page_main_title">
      <div class="goback_geader" @click="goback"><i class="el-icon-arrow-left"></i>返回</div>
      岗位管理
    </div>
    <div class="page_second_title">岗位信息导入--错误一览</div>
    <div class="page_section staff_import_center clearfix">
      <el-table :data="tableData" stripe ref="tableRef" v-if="flag">
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column
          show-overflow-tooltip
          v-for="col in columns"
          :prop="col.prop"
          :key="col.prop"
          :label="col.label"
          :width="col.width"
        >
          <template #default="scope">
            <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row[col.prop].msg"
              placement="top"
              :disabled="scope.row[col.prop].accept"
            >
              <el-input
                size="small"
                :class="{ error: !scope.row[col.prop].accept }"
                v-model="scope.row[col.prop].val"
                :title="scope.row[col.prop].val"
                :type="col.prop == 'userPasswd' ? 'password' : 'text'"
                :disabled="scope.row[col.prop].accept"
              ></el-input>
              <el-select
                v-if="col.prop == 'isLeader' && scope.row[col.prop].accept == false"
                v-model="scope.row[col.prop].val"
                :class="{ error_select: !scope.row[col.prop].accept }"
                :disabled="scope.row[col.prop].accept"
                placeholder="请选择"
              >
                <el-option v-for="item in options" :key="item.dictCode" :label="item.codeName" :value="item.dictCode" />
              </el-select>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <coustomPagination @pageChange="pageChange" :total="tableDataCopy.length" />
      <div class="btn_wrap align_center">
        <el-button type="primary" @click="saveBtn">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { importPostData } from '../../request/api'
import coustomPagination from '@/components/talent/paginationComps/coustomPagination.vue'

const route = useRoute()
const router = useRouter()
const tableRef = ref(null)

const flag = ref(true)
const tableData = ref([])
const tableDataCopy = ref([])
const columns = [
  { label: '岗位系统编码', prop: 'postCodeExtn' },
  { label: '岗位名称', prop: 'postName' },
  { label: '组织系统编码', prop: 'orgCodeExtn' },
  { label: '上级岗位系统编码', prop: 'parentPostCodeExtn' },
  { label: '是否是负责人岗位', prop: 'isLeader' },
  { label: '职位系统编码', prop: 'jobCodeExtn' }
]
const options = [
  { dictCode: '是', codeName: '是' },
  { dictCode: '否', codeName: '否' }
]

function goback() {
  router.back()
}

function getPageData(pageSize, currentPage) {
  const offset = (currentPage - 1) * pageSize
  const copy = tableDataCopy.value
  tableData.value = copy.slice(offset, offset + pageSize)
}

function pageChange(pageSize, currentPage) {
  getPageData(pageSize, currentPage)
}

function saveBtn() {
  const data = tableDataCopy.value.map(item => {
    const obj = {
      postCodeExtn: '',
      postName: '',
      orgCodeExtn: '',
      parentPostCodeExtn: '',
      isLeader: '',
      jobCodeExtn: '',
      parentPostCode: '',
      postCode: ''
    }
    Object.keys(obj).forEach(key => {
      obj[key] = item[key].val
    })
    return obj
  })
  importPostData(data).then(res => {
    if (res.code == 200) {
      tableData.value = []
      tableDataCopy.value = []
      tableData.value = res.data.obj
      tableDataCopy.value = res.data.obj
      flag.value = false
      if (res.data.errorCount == 0) {
        goback()
        return
      }
      getPageData(10, 1)
      nextTick(() => {
        flag.value = true
      })
    } else {
      ElMessage.error(res.msg)
    }
  })
}

onMounted(() => {
  tableData.value = route.query.data
  tableDataCopy.value = route.query.data
  pageChange(10, 1)
})
</script>

<style scoped lang="scss">
.post_import_wrap {
  .page_second_title {
    margin: 0 0 0 15px;
  }
}
.el-input.error .el-input__inner {
  border-color: red;
}
.cell .error_select {
  .el-input {
    .el-input__inner {
      border-color: red !important;
    }
  }
}
</style>
