<template>
    <div class="report_view_list_wrap">
        <div class="page_section_aside">
            <div class="page_second_title">报告检索</div>
            <div class="report_filter_group">
                <div class="page_third_title">报告类型</div>
                <div class>
                    <div
                        class="report_filter_item"
                        :class="{ active: activeReportType == 'd' }"
                        @click="changeReportType('d')"
                    >
                        部门报告
                    </div>
                    <div
                        class="report_filter_item"
                        :class="{ active: activeReportType == 'p' }"
                        @click="changeReportType('p')"
                    >
                        个人报告
                    </div>
                </div>
            </div>
            <div class="report_filter_group">
                <div class="page_third_title">模糊检索</div>
                <div class>
                    <el-input
                        class="marginB_16"
                        v-model="searchValue"
                        placeholder
                    ></el-input>
                    <el-button type="primary"  @click="searchReport"
                        >查询</el-button
                    >
                </div>
            </div>
        </div>
        <div class="page_section_main">
            <table-component
                :tableData="tableData"
                :needIndex="true"
                @handleSizeChange="sizeChange"
                @handleCurrentChange="pageChange"
                :overflowTooltip="false"
            >
                <template v-slot:oper>
                    <el-table-column label="操作" width="100">
                        <template slot-scope="scope">
                            <el-button
                                class="page_add_btn"
                                @click="showReport(scope.row)"
                                >查看报告</el-button
                            >
                        </template>
                    </el-table-column>
                </template>
            </table-component>
        </div>
        <div class="progress_mark align_center" v-show="showProgress">
            <el-progress
                type="circle"
                :percentage="percentage"
                :color="progressColor"
                :stroke-width="10"
            ></el-progress>
        </div>
    </div>
</template>
 
<script>
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import {
        queryEvalReportList,
        queryEvalUserReportList,
        getRptChartList,
        getReportChartData,
        uploadRptChart,
        genReportPdf,
        downloadReportPdf,
    } from "../../../request/api";
    import {
        echartsRenderPage,
        echartsToImg,
        setChartData,
    } from "../../../../../../public/js/echartsimg/echartsToImg";
    export default {
        name: "reportViewList",
        components: {
            tableComponent,
        },
        data() {
            return {
                showProgress: false,
                percentage: 0,
                progressColor: "#0099ff",
                pageSize: 10,
                currPage: 1,
                activeReportType: "d",
                searchValue: "",
                tableData: {},
                tableData: {
                    columns: [],
                    data: [],
                    page: {},
                },
                tableDataPerson: {
                    columns: [
                        {
                            label: "评估项目",
                            prop: "evalName",
                            // width:160
                        },
                        {
                            label: "部门名称",
                            prop: "orgName",
                            width: 120,
                        },
                        {
                            label: "姓名",
                            prop: "userName",
                            width: 100,
                        },
                        {
                            label: "岗位",
                            prop: "postName",
                            width: 120,
                        },
                        {
                            label: "报告日期",
                            prop: "reportGenTime",
                            width: 120,
                            formatterFun: function (data) {
                                let time = data["reportGenTime"];
                                return time ? time.split(" ")[0] : " ";
                            },
                        },
                        {
                            label: "分数",
                            prop: "overallScore",
                            width: 80,
                        },
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10,
                    },
                },
                tableDataDept: {
                    columns: [
                        {
                            label: "部门名称",
                            prop: "orgName",
                            width: 120,
                        },
                        {
                            label: "评估项目",
                            prop: "evalName",
                            // width:160
                        },
                        {
                            label: "报告日期",
                            prop: "reportGenTime",
                            width: 120,
                            formatterFun: function (data) {
                                let time = data["reportGenTime"];
                                return time ? time.split(" ")[0] : " ";
                            },
                        },
                        {
                            label: "人数",
                            prop: "totalNumber",
                            width: 50,
                        },
                        {
                            label: "分数",
                            prop: "overallScore",
                            width: 80,
                        },
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        created() {
            this.getOrgReport();
        },
        methods: {
            searchReport() {
                if (this.activeReportType == "d") {
                    this.getOrgReport();
                } else if (this.activeReportType == "p") {
                    this.getUserReport();
                }
            },
            sizeChange(size) {
                this.pageSize = size;
                this.searchReport();
            },
            pageChange(page) {
                this.currPage = page;
                this.searchReport();
            },
            getOrgReport() {
                let params = {
                    size: this.pageSize,
                    current: this.currPage,
                    orgName: this.searchValue,
                };
                queryEvalReportList(params).then((res) => {
                    console.log(res);
                    if (res.code == "200" && res.data) {
                        this.$set(this.tableDataDept, "page", res.page);
                        this.$set(this.tableDataDept, "data", res.data);
                        this.tableData = this.tableDataDept;
                    } else {
                        this.$set(this.tableDataDept, "page", {
                            total: 0,
                            current: 1,
                            size: 10,
                        });
                        this.$set(this.tableDataDept, "data", []);
                        this.tableData = this.tableDataDept;
                    }
                });
            },
            getUserReport() {
                let params = {
                    size: this.pageSize,
                    current: this.currPage,
                    userName: this.searchValue,
                };
                queryEvalUserReportList(params).then((res) => {
                    if (res.code == "200" && res.data) {
                        this.$set(this.tableDataPerson, "page", res.page);
                        this.$set(this.tableDataPerson, "data", res.data);
                        this.tableData = this.tableDataPerson;
                    } else {
                        this.$set(this.tableDataPerson, "page", {
                            total: 0,
                            current: 1,
                            size: 10,
                        });
                        this.$set(this.tableDataPerson, "data", []);
                        this.tableData = this.tableDataPerson;
                    }
                });
            },
            changeReportType(type) {
                this.searchValue = "";
                this.pageSize = 10;
                this.currPage = 1;
                if (type == "d") {
                    this.activeReportType = "d";
                    this.getOrgReport();
                } else if (type == "p") {
                    this.activeReportType = "p";
                    this.getUserReport();
                }
            },
            showReport(row) {
                let evalId = row.evalId;
                console.log(row);
                if (this.activeReportType == "d") {
                    let orgCode = row.orgCode;
                    this.downloadOrgReport(row);
                } else if (this.activeReportType == "p") {
                    let uesrId = row.objectId;
                    this.downloadPersonReport(row);
                }
            },
            // 生成组织报告
            async downloadOrgReport(row) {
                //显示进度环
                this.showProgress = true;
                let params = {
                    evalId: row.evalId,
                    orgCode: row.orgCode,
                    reportType: "2",
                };
                //获取报告图表列表
                await this.getRptChartListFun(params);
                console.log("图片上传结束");
                //生成pdf
                let res = await genReportPdf(params);
                this.percentage = 90;
                if (res.code == 200) {
                    this.percentage = 100;
                    //下载pdf
                    let downParams = {
                        fileType: "",
                        evalId: row.evalId,
                        orgCode: row.orgCode,
                    };
                    let downRes = await downloadReportPdf(downParams);
                    console.log(downRes);
                    const blob = new Blob([downRes]);
                    const elink = document.createElement("a");
                    elink.download = `${row.orgName}-组织报告.pdf`;
                    elink.style.display = "none";
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    URL.revokeObjectURL(elink.href); // 释放URL 对象
                    document.body.removeChild(elink);
                } else {
                    this.$msg.warning(res.msg);
                }
                //隐藏进度环
                this.showProgress = false;
                this.percentage = 0;
            },
            //生成个人报告
            async downloadPersonReport(row) {
                //显示进度环
                console.log(row.evalId);
                this.showProgress = true;
                let params = {
                    evalId: row.evalId,
                    objectId: row.objectId,
                    orgCode: row.orgCode,
                    postCode: row.postCode,
                    reportType: "1",
                };
                //获取报告图表列表
                await this.getRptChartListFun(params);
                console.log("图片上传结束");
                //生成pdf
                let res = await genReportPdf(params);
                this.percentage = 90;
                if (res.code == 200) {
                    this.percentage = 100;
                    //下载pdf
                    let downParams = {
                        fileType: "",
                        evalId: row.evalId,
                        objectId: row.objectId,
                        orgCode: row.orgCode,
                        postCode: row.postCode,
                    };
                    let downRes = await downloadReportPdf(downParams);
                    const blob = new Blob([downRes]);
                    const elink = document.createElement("a");
                    elink.download = `${row.userName}-个人报告.pdf`;
                    elink.style.display = "none";
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    URL.revokeObjectURL(elink.href); // 释放URL 对象
                    document.body.removeChild(elink);
                } else {
                    this.$msg.warning(res.msg);
                }
                //隐藏进度环
                this.showProgress = false;
                this.percentage = 0;
            },
            //获取报告图表列表
            async getRptChartListFun(params) {
                let res = await getRptChartList(params);
                console.log(res);
                if (res.code == 200) {
                    let result = [];
                    for (let i = 0; i < res.data.length; i++) {
                        let params = {
                            url: res.data[i].dataUrl,
                            data: res.data[i].rptChartParam,
                        };
                        result.push(getReportChartData(params));
                    }
                    this.percentage = 20;
                    //获取图表数据
                    await Promise.all(result).then(async (args) => {
                        let imagesArr = [];
                        args.forEach((item, index) => {
                            if (item.code == 200) {
                                let data = item.data;
                                if (
                                    !data ||
                                    (data.chartData &&
                                        data.chartData.length == 0) ||
                                    data.length == 0 ||
                                    (data.area && data.area.length == 0)
                                ) {
                                    console.log("############################");
                                    console.log(res.data[index].chartId);
                                    return;
                                }
                                if (res.data[index].chartType == "14") {
                                    if (data.chartData[0].length == 0) {
                                        console.log("############################");
                                        console.log(res.data[index].chartId);
                                        return;
                                    }
                                }
                                data["padding"] = res.data[index]["padding"];
                                let renderTypeData = setChartData(
                                    res.data[index],
                                    data
                                );

                                if (res.data[index].chartType == "1") {
                                    renderTypeData.renderData.colorIndex = this
                                        .colorIndex++;
                                }
                                // console.log(res.data[index].chartId);
                                // if (res.data[index].chartId == "119GJ.GJ") {
                                //     console.dir(renderTypeData.renderData);
                                //     echartsRenderPage(
                                //         "test_chart_box",
                                //         "XBar",
                                //         400,
                                //         400,
                                //         renderTypeData.renderData
                                //     );
                                // }

                                //获取图表base64
                                imagesArr.push({
                                    baseImg: echartsToImg(
                                        this.$util.createRandomId(),

                                        renderTypeData.renderType,
                                        res.data[index].aspectRatio,
                                        renderTypeData.renderData
                                    ),
                                    chartId: res.data[index].chartId,
                                });
                            }
                        });
                        let result = [];
                        console.log(imagesArr);
                        imagesArr.forEach((item, index) => {
                            if (item.baseImg) {
                                let imgParams = {
                                    evalId: params.evalId,
                                    chartId: item.chartId,
                                    imgData: item.baseImg,
                                    objectId: params.objectId,
                                    orgCode: params.orgCode,
                                };
                                result.push(uploadRptChart(imgParams));
                                //上传图表图片
                            }
                        });
                        this.percentage = 50;
                        await Promise.all(result).then((args) => {
                            // console.log(args)
                            this.percentage = 80;
                        });
                    });
                } else {
                    this.$msg.warning(res.msg);
                    //隐藏进度环
                    this.showProgress = false;
                    this.percentage = 0;
                }
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .report_view_list_wrap {
        .page_section_aside {
            margin-right: 36px !important;
            border-right: 1px solid #e4e7ed;
        }
        .report_filter_group {
            padding: 0 32px 10px 16px;
            .report_filter_item {
                text-align: center;
                color: #525e6c;
                line-height: 30px;
                margin-bottom: 5px;
                background: #e4e7ed;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                &.active {
                    color: #fff;
                    background: #0099fd;
                }
            }
        }
        .el-table .has-gutter tr th {
            background: #f2f8ff;
        }
    }
    .progress_mark {
        position: fixed;
        padding-top: 300px;
        left: 0;
        top: 0;
        width: 100%;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        z-index: 99999;
        .el-progress__text {
            color: #fff;
        }
    }
</style>