<script setup>
import Table from '@/components/table/simplenessTable.vue'
// 11
const columns = ref([
  {
    label: '成本效益类型',
    prop: 'a'
  },
  {
    label: '核心特征',
    prop: 'b',
    width: 320
  },
  // {
  //   label: "风险偏好",
  //   prop: "c",
  // },
  {
    label: '占比<40%',
    prop: 'd'
  },

  {
    label: '占比40~60%',
    prop: 'e'
  },
  {
    label: '占比60~80%',
    prop: 'f'
  },
  {
    label: '占比>80%',
    prop: 'g'
  }
])
const data = ref([
  {
    a: '保守型',
    b: '风险厌恶，优先选择已知方案，注重合规性与稳定性，避免不确定性',
    c: '极低风险容忍度',
    d: '1',
    e: '2',
    f: '2',
    g: '4'
  }
])

const columns2 = ref([
  {
    label: '姓名',
    prop: 'a'
  },
  {
    label: '岗位',
    prop: 'b'
  },
  {
    label: '占比',
    prop: 'c'
  },
  {
    label: '错配等级',
    prop: 'd'
  }
])
const data2 = ref([
  {
    a: '王伟',
    b: '采购经理',
    c: '36'
  }
])

const columns3 = ref([
  {
    label: '姓名',
    prop: 'a',
    width: 80
  },

  {
    label: '岗位',
    prop: 'b'
  },
  {
    label: '二级能力',
    prop: 'c'
  },

  {
    label: '目标风格',
    prop: 'd'
  },
  {
    label: '实际风格',
    prop: 'e'
  }
])
const data3 = ref([
  {
    a: '王伟',
    b: '采购经理',
    c: '市场分析与战略规划',
    e: '92'
  }
])

const columns4 = ref([
  {
    label: '二级能力',
    prop: 'a'
  },

  {
    label: '目标风格',
    prop: 'd'
  },
  {
    label: '实际风格',
    prop: 'e'
  },
  {
    label: '干预策略',
    prop: 'b',
    width: 460
  }
])
const data4 = ref([
  {
    a: '市场分析与战略规划',
    b: '',
    c: ''
  }
])

onMounted(() => {})
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">成本效益匹配</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        基于能力测评数据，精准识别团队成员能力与目标岗位胜任力要求的匹配程度，通过岗位调整实现
        “人岗适配”，最大化释放人才效能。
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">成本效益匹配</div>
      <Table :roundBorder="false" :columns="columns" :data="data" headerColor showIndex> </Table>
    </div>
    <div class="info_section_wrap half_section_wrap justify-between">
      <div class="l_wrap">
        <div class="page-title-line">成本效益匹配（高投入高回报-占比>80%）</div>
        <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor showIndex> </Table>
      </div>
      <div class="r_wrap">
        <div class="page-title-line">成本效益二级展开（王伟-高投入高回报-占比>80% ）</div>
        <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor> </Table>
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">决策风格详情（保守型-占比>80% -王伟-采购经理）</div>
      <Table :roundBorder="false" :columns="columns4" :data="data4" headerColor showIndex> </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../../../../style/common.scss';
@import './common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
