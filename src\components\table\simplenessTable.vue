<script setup>
defineOptions({ name: 'simplenessTable' })
const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  columns: {
    type: Array,
    required: true
  },
  // 是否设置table边框圆角
  roundBorder: {
    type: Boolean,
    default: true
  }
})

const simplenessTableRef = ref(null)

defineExpose({
  simplenessTableRef: simplenessTableRef
})
</script>
<template>
  <div class="simpleness-table" :class="{ 'round-border': roundBorder }">
    <el-table ref="simplenessTableRef" :data="data" v-bind="$attrs">
      <el-table-column v-for="col in columns" v-bind="{ ...col }" :key="col.prop || col.label"> </el-table-column>
      <slot></slot>
      <slot name="oper"></slot>
    </el-table>
  </div>
</template>
<style lang="scss" scoped>
.simpleness-table {
  // 设置table边框圆角
  :deep(.el-table) {
    --el-font-size-base: 14px;
    --el-table-header-text-color: #93abcb;
    --el-table-border-color: #c6dbf3;
    --el-table-tr-bg-color: transparent;
    --el-table-header-bg-color: transparent;
    background-color: transparent;
  }
  &.round-border {
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #c6dbf3;
    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        // 隐藏table底边框
        background-color: transparent;
      }
      tr:last-of-type {
        td {
          // 隐藏table底边框
          border-bottom: none;
        }
      }
    }
  }
}
</style>
