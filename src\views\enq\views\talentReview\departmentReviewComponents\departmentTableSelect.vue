<template>
  <div class="basic_box">
    <div>
      <selectCheckData
        :tableData="tableData"
        :type="type"
        :maxHeight="430"
        :numLength="numLength"
        @tableDataFn="tableDataFun"
      ></selectCheckData>
    </div>
    <div class="btn_box">
      <el-button type="primary" class="page_confirm_btn" @click="updateTargetBtn">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import selectCheckData from '@/components/talent/selectTableCheck/selectCheckDataNew'

const props = defineProps({
  types: String,
  dataFrom: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['getChildData'])

const type = ref('radio') // 单选
const numLength = ref(1) // 固定长度
const tableData = reactive({
  tableTitle: [],
  data: []
})
const kpiCodeList = ref([])
const checkTargetSignList = ref([])
const checkTargetDisabledSignList = ref([])
const checkArr = ref([])

watch(
  () => props.dataFrom,
  newVal => {
    relatePostKpiFun()
  },
  { deep: true }
)

onMounted(() => {
  console.log(props.types, props.dataFrom)
})

const updateTargetBtn = () => {
  checkArr.value = []
  console.log(tableData.data, 11)

  for (const item of tableData.data) {
    checkArr.value.push({
      resultList: item.selectedCodes
    })
  }

  console.log(isFlag(checkArr.value))
  if (isFlag(checkArr.value)) {
    ElMessage.warning('评价内容不能为空!')
    return
  }

  const paramList = []
  if (props.types == 'postR' || props.types == 'postW') {
    for (const item of tableData.data) {
      paramList.push({
        jobCode: item.code,
        optionNbrS: item.selectedCodes
      })
    }
  } else {
    for (const item of tableData.data) {
      paramList.push({
        objectId: item.code,
        optionNbrS: item.selectedCodes
      })
    }
  }

  emit('getChildData', paramList)
}

const isFlag = arr => {
  for (const item of arr) {
    if (item.resultList.length == 0) {
      return true
    }
  }
  return false
}

const tableDataFun = data => {
  console.log(data)
  tableData.data = data
}

const relatePostKpiFun = () => {
  // 重置数据
  tableData.tableTitle = []
  tableData.data = []
  kpiCodeList.value = []
  checkTargetSignList.value = []
  checkTargetDisabledSignList.value = []

  // 初始表头固定部分及表格数据
  if (props.types == 'postR' || props.types == 'postW') {
    tableData.tableTitle = [
      {
        label: '职位',
        prop: 'name',
        canCheck: false,
        width: 145,
        fixed: 'left'
      }
    ]
  } else {
    tableData.tableTitle = [
      {
        label: '人员',
        prop: 'name',
        canCheck: false,
        width: 80,
        fixed: 'left'
      }
    ]
  }

  // 表头数据
  if (props.dataFrom.resultList) {
    for (let i = 0; i < props.dataFrom.resultList.length; i++) {
      const item = props.dataFrom.resultList[i]
      tableData.tableTitle.push({
        label: item.name,
        prop: `targetName-${i + 1}`,
        canCheck: true,
        disabledSign: `targetKpiCode-${i + 1}`,
        code: item.code,
        width: 140
      })
      checkTargetSignList.value.push(`targetName-${i + 1}`)
      checkTargetDisabledSignList.value.push(`targetKpiCode-${i + 1}`)
      kpiCodeList.value.push(item.code)
    }
  }

  // 表格数据
  if (props.dataFrom.itemList) {
    for (let j = 0; j < props.dataFrom.itemList.length; j++) {
      const item = props.dataFrom.itemList[j]
      const rowData = {
        index: j + 1,
        name: item.name,
        code: item.code,
        relation: item.relation,
        relationCode: item.relationCode,
        selectedCodes: item.selectedCodes,
        kpiCodeList: kpiCodeList.value,
        optionalCodes: item.optionalCodes
      }

      // 将所有表格初始为未被选中，初始为可勾选
      for (let i = 0; i < kpiCodeList.value.length; i++) {
        rowData[checkTargetSignList.value[i]] = false
        rowData[checkTargetDisabledSignList.value[i]] = false
      }

      // 初始化不可点击表格
      if (rowData.optionalCodes.length > 0) {
        for (const optionalCode of rowData.optionalCodes) {
          const index = kpiCodeList.value.indexOf(optionalCode)
          if (index !== -1) {
            rowData[checkTargetDisabledSignList.value[index]] = true
          }
        }
      }

      // 变更所有表格选中状态
      if (item.selectedCodes) {
        for (const selectedCode of item.selectedCodes) {
          const checkColumn = kpiCodeList.value.indexOf(selectedCode)
          if (checkColumn !== -1) {
            rowData[`targetName-${checkColumn + 1}`] = true
          }
        }
      }

      tableData.data.push(rowData)
    }
  }
}
</script>

<style scoped lang="scss">
.basic_box {
  width: 100%;
  .btn_box {
    width: 100%;
    height: 50px;
    text-align: center;
  }
}
</style>
