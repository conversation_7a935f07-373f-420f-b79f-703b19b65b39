// 对话路由
import Layout from '@/layout/index.vue'

const dialogueRoutes = [
  {
    path: '/dialogue',
    component: Layout,
    redirect: '/dialogue',
    children: [
      {
        path: '/dialogue',
        component: () => import('@/views/dialogue/index.vue')
      },
      {
        path: ':id',
        component: () => import('@/views/dialogue/detail.vue')
      }
    ]
  },
  {
    path: '/scheduling',
    component: Layout,
    redirect: '/scheduling',
    children: [
      {
        path: '/scheduling',
        meta: {
          title: '智能排产'
        },
        component: () => import('@/views/dialogue/scheduling/index.vue')
      },
      {
        path: ':id',
        meta: {
          title: '智能排产'
        },
        component: () => import('@/views/dialogue/scheduling/detail.vue')
      }
    ]
  },
  {
    path: '/question',
    component: Layout,
    redirect: '/question',
    children: [
      {
        path: '/question',
        meta: {
          title: '智能问数'
        },
        component: () => import('@/views/dialogue/question/index.vue')
      },
      {
        path: ':id',
        meta: {
          title: '智能问数'
        },
        component: () => import('@/views/dialogue/question/detail.vue')
      }
    ]
  }
]

export default dialogueRoutes
