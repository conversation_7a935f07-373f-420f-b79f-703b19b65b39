<script setup>
import SectionTabV from '../components/sectionTabVertical.vue'
import orgEffect from './orgEffect/index.vue'
import applicationScenarios from './applicationScenarios.vue'
import evalModel from './evalModel/index.vue'
import evalProcess from './evalProcess.vue'
import evalReport from './evalReport/index.vue'
const tabContentList = ref([orgEffect, applicationScenarios, evalModel, evalProcess, evalReport])
const sectionTabVList = ref([
  {
    name: '了解组织效能',
    code: 1
  },
  {
    name: '了解应用场景',
    code: 2
  },
  {
    name: '查看评估模型',
    code: 3
  },
  {
    name: '了解评估步骤',
    code: 4
  },
  {
    name: '了解评估报告',
    code: 5
  }
])
const sectionTabVCheckSign = ref(5)
const checkSecTabV = c => {
  sectionTabVCheckSign.value = c
}
</script>
<template>
  <div class="org_index_wrap">
    <div class="justify-start">
      <div class="v_tab_wrap">
        <SectionTabV
          :sectionTabVList="sectionTabVList"
          :sectionTabVCheckSign="sectionTabVCheckSign"
          :title="'快速入门：'"
          @checkSecTabV="checkSecTabV"
        ></SectionTabV>
      </div>
      <div class="content-mian">
        <component :is="tabContentList[sectionTabVCheckSign - 1]" />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.org_index_wrap {
  .v_tab_wrap {
    margin-right: 20px;
  }
  .content-mian {
    width: calc(100% - 200px);
  }
}
</style>
