import { h } from 'vue'

const EVENTS = {
  CLICK: 'on-node-click',
  MOUSEOUT: 'on-node-mouseout',
  MOUSEOVER: 'on-node-mouseover'
}

function createListener(handler, data) {
  if (typeof handler == 'function') {
    return function (e) {
      // fixed bug #48
      if (e.target.className.indexOf('org-tree-node-btn') > -1) return
      handler.apply(null, [e, data])
    }
  }
}

// 判断是否叶子节点
const isLeaf = (data, prop) => {
  return !(Array.isArray(data[prop]) && data[prop].length > 0)
}

// 创建 node 节点
function renderNode(_h, data, context) {
  const props = context
  const cls = ['org-tree-node']
  const childNodes = []
  const children = data[props.props.props.children]

  if (isLeaf(data, props.props.props.children)) {
    cls.push('is-leaf')
  } else if (props.collapsable && !data[props.props.props.expand]) {
    cls.push('collapsed')
  }

  childNodes.push(renderLabel(h, data, props))

  if (!props.collapsable || data[props.props.props.expand]) {
    childNodes.push(renderChildren(h, children, props))
  }

  return h(
    'div',
    {
      class: cls
    },
    childNodes
  )
}

// 创建展开折叠按钮
function renderBtn(_h, data, context) {
  const props = context
  const expandHandler = props.onExpand

  let cls = ['org-tree-node-btn']

  if (data[props.props.expand]) {
    cls.push('expanded')
  }

  return h('span', {
    class: cls,
    onClick: e => expandHandler && expandHandler(e, data)
  })
}

// 创建 label 节点
function renderLabel(_h, data, context) {
  const props = context
  const label = data[props.props.props.label]
  const renderContent = props.props.renderContent

  // event handlers
  const clickHandler = props.onNodeClick
  const mouseOutHandler = props.onNodeMouseout
  const mouseOverHandler = props.onNodeMouseover

  const childNodes = []
  if (typeof renderContent == 'function') {
    let vnode = renderContent(h, data)
    vnode && childNodes.push(vnode)
  } else {
    childNodes.push(label)
  }

  if (props.props.collapsable && !isLeaf(data, props.props.props.children)) {
    childNodes.push(renderBtn(h, data, props.props))
  }

  const cls = ['org-tree-node-label-inner']
  let { labelWidth, labelClassName, selectedClassName, selectedKey } = props.props

  if (typeof labelWidth == 'number') {
    labelWidth += 'px'
  }

  if (typeof labelClassName == 'function') {
    labelClassName = labelClassName(data)
  }

  labelClassName && cls.push(labelClassName)

  // add selected class and key from props
  if (typeof selectedClassName == 'function') {
    selectedClassName = selectedClassName(data)
  }

  selectedClassName && selectedKey && data[selectedKey] && cls.push(selectedClassName)

  return h(
    'div',
    {
      class: 'org-tree-node-label'
    },
    [
      h(
        'div',
        {
          class: cls,
          style: { width: labelWidth },
          onClick: createListener(clickHandler, data),
          onMouseout: createListener(mouseOutHandler, data),
          onMouseover: createListener(mouseOverHandler, data)
        },
        childNodes
      )
    ]
  )
}

// 创建 node 子节点
function renderChildren(_h, list, context) {
  const props = context
  if (Array.isArray(list) && list.length) {
    const children = list.map(item => {
      return renderNode(h, item, props)
    })

    return h(
      'div',
      {
        class: 'org-tree-node-children'
      },
      children
    )
  }
  return ''
}

export default (props, { emit }) => {
  // 事件适配，props中注入事件回调
  return renderNode(h, props.data, {
    ...props,
    onExpand: (e, data) => emit('on-expand', e, data),
    onNodeFocus: (e, data) => emit('on-node-focus', e, data),
    onNodeClick: (e, data) => emit('on-node-click', e, data),
    onNodeMouseover: (e, data) => emit('on-node-mouseover', e, data),
    onNodeMouseout: (e, data) => emit('on-node-mouseout', e, data)
  })
}
