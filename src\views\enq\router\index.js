import { createRouter, createWebHistory } from 'vue-router'

// 人才盘点分析详情  - 分析主题内路由生成
import {
  talentNumberRouter,
  talentStructureRouter,
  talentQualityRouter,
  talentOrgRouter,
  talentEfficiencyRouter,
  talentRiskRouter,
  talentOptimizeRouter
} from '../views/talentReviewAnalysis/TRanalysisComps/TRanalysisRouter'
// console.log(talentNumberRouter);

const routes = [
  {
    path: '/talentReview',
    redirect: '/talentReviewHome/talentReview',
    name: 'talentReviewHome',
    meta: {
      name: '人才盘点'
    },
    component: () => import('../views/talentReviewHome'),
    children: [
      {
        path: '/talentReviewHome/talentReviewChildrenHome',
        name: 'talentReviewChildrenHome',
        meta: {
          name: '盘点项目'
        },
        component: () => import('../views/talentReview/talentReviewChildrenHome'),
        children: [
          {
            path: '/talentReviewHome/talentReview',
            name: 'talentReview2',
            meta: {
              name: '人才盘点列表'
            },
            component: () => import('../views/talentReview/talentReview')
          },
          {
            path: '/talentReviewHome/talentReview/launchReview',
            name: 'launchReview',
            meta: {
              hidd: true,
              name: '发起盘点'
            },
            component: () => import('../views/talentReview/launchReviwe')
          },
          {
            path: '/talentReviewHome/personnelImportErrorData',
            name: 'personnelImportErrorData',
            meta: {
              hidd: true,
              name: '人员导入错误页面'
            },
            component: () => import('../views/talentReview/lunchReviewComponents/personnelImportErrorData')
          },
          {
            path: '/talentReviewHome/talentReview/PersonalInventorytips',
            name: 'talentReviewPage',
            meta: {
              hidd: true,
              name: '个人盘点引导页'
            },
            component: () => import('../views/talentReview/PersonalInventorytips')
          },
          {
            path: '/talentReviewHome/talentReview/personalReview',
            name: 'talentReview',
            meta: {
              hidd: true,
              name: '个人盘点'
            },
            component: () => import('../views/talentReview/personalReview')
          },
          {
            path: '/talentReviewHome/talentReview/departmentReview',
            name: 'departmentReview',
            meta: {
              hidd: true,
              name: '部门盘点'
            },
            component: () => import('../views/talentReview/departmentReview')
          },
          {
            path: '/talentReviewHome/talentReview/dataUpload',
            name: 'dataUpload',
            meta: {
              hidd: true,
              name: '数据上传'
            },
            component: () => import('../views/talentReview/dataUpload')
          },
          {
            path: '/talentReviewHome/talentReview/perfectReview',
            name: 'perfectReview',
            meta: {
              hidd: true,
              name: '盘点配置'
            },
            component: () => import('../views/talentReview/launchReviwe')
          },
          {
            path: '/talentReviewHome/talentReview/commentSub',
            name: 'personEval',
            meta: {
              hidd: true,
              name: '点评下级'
            },
            component: () => import('../views/talentReview/commentSubPage')
          },
          {
            path: '/talentReviewHome/talentReview/departmentReview/personCalibrate',
            name: 'personCalibrate',
            meta: {
              hidd: true,
              name: '人员校准'
            },
            component: () => import('../views/talentReview/departmentReviewComponents/personnelCalibrationPage')
          },
          {
            path: '/talentReviewHome/inventoryModelHome',
            name: 'inventoryModelHome',
            meta: {
              hidd: true,
              name: '盘点模型'
            },
            component: () => import('../views/talentReview/inventoryModel/inventoryModelHome.vue')
          },
          {
            path: '/talentReviewHome/reviewDataHome',
            redirect: '/talentReviewHome/reviewDataHome/reviewDataList',
            name: 'reviewDataHome',
            meta: {
              hidd: true,
              name: '盘点数据'
            },
            component: () => import('../views/reviewData/reviewDataHome.vue'),
            children: [
              {
                path: '/talentReviewHome/reviewDataHome/reviewDataList',
                name: 'reviewDataList',
                meta: {
                  hidd: true,
                  name: '盘点数据列表'
                },
                component: () => import('../views/reviewData/reviewDataList.vue')
              },
              {
                path: '/talentReviewHome/reviewDataHome/reviewDataView',
                name: 'reviewDataView',
                meta: {
                  hidd: true,
                  name: '盘点数据查看'
                },
                component: () => import('../views/reviewData/reviewDataView.vue')
              },
              {
                path: '/talentReviewHome/reviewDataHome/rDataPerTargetResults',
                name: 'rDataPerTargetResults',
                meta: {
                  hidd: true,
                  name: '目标与结果'
                },
                component: () => import('../views/reviewData/reviewDataClassifyPer/rDataPerTargetResults.vue')
              },
              {
                path: '/talentReviewHome/reviewDataHome/rDataPerKpi',
                name: 'rDataPerKpi',
                meta: {
                  hidd: true,
                  name: 'kpi'
                },
                component: () => import('../views/reviewData/reviewDataClassifyPer/rDataPerKpi.vue')
              },
              {
                path: '/talentReviewHome/reviewDataHome/rDataDepTargetResults',
                name: 'rDataDepTargetResults',
                meta: {
                  hidd: true,
                  name: '目标与结果'
                },
                component: () => import('../views/reviewData/reviewDataClassifyDep/rDataDepTargetResults.vue')
              },
              {
                path: '/talentReviewHome/reviewDataHome/rDataDepKpi',
                name: 'rDataDepKpi',
                meta: {
                  hidd: true,
                  name: '目标与结果'
                },
                component: () => import('../views/reviewData/reviewDataClassifyDep/rDataDepKpi.vue')
              },
              {
                path: '/talentReviewHome/reviewDataHome/reviewDataValidation',
                name: 'reviewDataValidation',
                meta: {
                  hidd: true,
                  name: '数据确认'
                },
                component: () => import('../views/reviewData/reviewDataValidation.vue')
              },
              {
                path: '/talentReviewHome/reviewDataHome/reviewDataValidation/rDataQualityEvaluation',
                name: 'rDataQualityEvaluation',
                meta: {
                  hidd: true,
                  name: '素质评价'
                },
                component: () => import('../views/reviewData/reviewDataValidationCom/rDataQualityEvaluation.vue')
              },
              {
                path: '/talentReviewHome/reviewDataHome/reviewDataValidation/rDataPerformanceEvaluation',
                name: 'rDataPerformanceEvaluation',
                meta: {
                  hidd: true,
                  name: '业绩评价'
                },
                component: () => import('../views/reviewData/reviewDataValidationCom/rDataPerformanceEvaluation.vue')
              }
            ]
          }
        ]
      },
      {
        path: '/talentReviewHome/reviewProgressQuality',
        redirect: '/talentReviewHome/reviewProgressQuality/RPmanage',
        name: 'reviewProgressQualityHome',
        meta: {
          name: '盘点进度与质量'
        },
        component: () => import('../views/reviewProgressQuality/reviewProgressQualityHome'),
        children: [
          {
            path: '/talentReviewHome/reviewProgressQuality/RPmanage',
            name: 'reviewProgressManage',
            meta: {
              name: '盘点进度管理'
            },
            component: () => import('../views/reviewProgressQuality/reviewProgressManage')
          },
          {
            path: '/talentReviewHome/reviewProgressQuality/RPmanage/RPdetails',
            name: 'reviewProgressDetails',
            meta: {
              hidd: true,
              name: '盘点进度个人详情'
            },
            component: () => import('../views/reviewProgressQuality/reviewProgressManageComps/reviewProgressDetails')
          },
          {
            path: '/talentReviewHome/reviewProgressQuality/RPmanage/RPDeptdetails',
            name: 'reviewProgressDeptDetails',
            meta: {
              hidd: true,
              name: '盘点进度部门详情'
            },
            component: () =>
              import('../views/reviewProgressQuality/reviewProgressManageComps/reviewProgressDeptDetails')
          },
          {
            path: '/talentReviewHome/reviewProgressQuality/RPmanage/REdetails',
            name: 'evaluateProgressManage',
            meta: {
              name: '盘点评价进度'
            },
            component: () => import('../views/reviewProgressQuality/evaluateProgressManage')
          },
          {
            path: '/talentReviewHome/reviewProgressQuality/RQmanage',
            name: 'reviewQualityManage',
            meta: {
              name: '盘点质量管理'
            },
            component: () => import('../views/reviewProgressQuality/reviewQualityManage')
          },
          {
            path: '/talentReviewHome/reviewProgressQuality/RQmanage/RQdetails',
            name: 'reviewQualityDetails',
            meta: {
              name: '盘点质量管理详情'
            },
            component: () => import('../views/reviewProgressQuality/reviewQualityManageComps/reviewQualityDetails')
          },
          {
            path: '/talentReviewHome/reviewProgressQuality/qualityMatrix',
            name: 'qualityProgressMatrix',
            meta: {
              name: '质量进度矩阵'
            },
            component: () => import('../views/reviewProgressQuality/qualityProgressMatrix')
          },
          {
            path: '/talentReviewHome/reviewProgressQuality/qualityMatrix/qualityMatrixDetails',
            name: 'qualityProgressMatrixDetails',
            meta: {
              name: '质量进度矩阵详情'
            },
            component: () =>
              import('../views/reviewProgressQuality/qualityProgressMatrixComps/qualityProgressMatrixDetails')
          }
        ]
      }
      // {
      //   path: '/talentReviewHome/talentReviewAnalysis',
      //   redirect: '/talentReviewHome/talentReviewAnalysis/TRreport',
      //   name: 'talentReviewAnalysisHome',
      //   meta: {
      //     name: '人才盘点分析'
      //   },
      //   component: () => import('../views/talentReviewAnalysis/talentReviewAnalysisHome'),
      //   children: [
      //     {
      //       path: '/talentReviewHome/talentReviewAnalysis/TRreport',
      //       redirect: '/talentReviewHome/talentReviewAnalysis/TRreport/projectView',
      //       name: 'talentReviewReport',
      //       meta: {
      //         name: '人才盘点报告'
      //       },
      //       component: () => import('../views/talentReviewAnalysis/talentReviewReport'),
      //       children: [
      //         {
      //           path: '/talentReviewHome/talentReviewAnalysis/TRreport/projectView',
      //           name: 'reportProjectView',
      //           meta: {
      //             name: '项目视图'
      //           },
      //           component: () => import('../views/talentReviewAnalysis/talentReviewReportComps/reportProjectView')
      //         },
      //         {
      //           path: '/talentReviewHome/talentReviewAnalysis/TRreport/listView',
      //           name: 'reportListView',
      //           meta: {
      //             name: '列表视图'
      //           },
      //           component: () => import('../views/talentReviewAnalysis/talentReviewReportComps/reportListView')
      //         },
      //         {
      //           path: '/talentReviewHome/talentReviewAnalysis/TRreport/projectView/showReport',
      //           name: 'reportProjectViewContent',
      //           meta: {
      //             name: '查看报告'
      //           },
      //           component: () =>
      //             import('../views/talentReviewAnalysis/talentReviewReportComps/reportProjectViewContent')
      //         }
      //       ]
      //     },
      //     {
      //       path: '/talentReviewHome/talentReviewAnalysis/orgReport',
      //       name: 'orgReport',
      //       meta: {
      //         name: '人才盘点报告-部门'
      //       },
      //       component: () => import('../views/talentReviewAnalysis/talentReviewReportComps/orgReport')
      //     },
      //     {
      //       path: '/talentReviewHome/talentReviewAnalysis/userReport',
      //       name: 'userReport',
      //       meta: {
      //         name: '人才盘点报告-个人'
      //       },
      //       component: () => import('../views/talentReviewAnalysis/talentReviewReportComps/userReport')
      //     },
      //     {
      //       path: '/talentReviewHome/talentReviewAnalysis/TRanalysis',
      //       name: 'talentReviewAnalysis',
      //       meta: {
      //         name: '人才盘点分析'
      //       },
      //       component: () => import('../views/talentReviewAnalysis/talentReviewAnalysis')
      //     },
      //     {
      //       path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails',
      //       name: 'talentReviewAnalysisDetails',
      //       meta: {
      //         name: '人才盘点分析详情'
      //       },
      //       component: () => import('../views/talentReviewAnalysis/TRanalysisComps/TRanalysisDetails'),
      //       children: [
      //         {
      //           path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentNumber',
      //           redirect:
      //             '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentNumber/personnelCharacter',
      //           name: 'talentNumberHome',
      //           meta: {
      //             name: '人才数量'
      //           },
      //           component: () => import('../views/talentReviewAnalysis/TRanalysisComps/talentNumber/talentNumberHome'),
      //           children: talentNumberRouter
      //         },
      //         {
      //           path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentStructure',
      //           redirect:
      //             '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentStructure/postStructure',
      //           name: 'talentStructureHome',
      //           meta: {
      //             name: '人才结构'
      //           },
      //           component: () =>
      //             import('../views/talentReviewAnalysis/TRanalysisComps/talentStructure/talentStructureHome'),
      //           children: talentStructureRouter
      //         },
      //         {
      //           path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentQuality',
      //           redirect:
      //             '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentQuality/abilityAnalysis',
      //           name: 'talentQualityHome',
      //           meta: {
      //             name: '人才质量'
      //           },
      //           component: () =>
      //             import('../views/talentReviewAnalysis/TRanalysisComps/talentQuality/talentQualityHome'),
      //           children: talentQualityRouter
      //         },
      //         {
      //           path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentOrg',
      //           redirect:
      //             '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentOrg/talentOrgChildren',
      //           name: 'talentOrgHome',
      //           meta: {
      //             name: '人才编制'
      //           },
      //           component: () => import('../views/talentReviewAnalysis/TRanalysisComps/talentOrg/talentOrgHome'),
      //           children: talentOrgRouter
      //         },
      //         {
      //           path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentEfficiency',
      //           redirect:
      //             '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentEfficiency/activityAnalysis',
      //           name: 'talentEfficiencyHome',
      //           meta: {
      //             name: '人才效率'
      //           },
      //           component: () =>
      //             import('../views/talentReviewAnalysis/TRanalysisComps/talentEfficiency/talentEfficiencyHome'),
      //           children: talentEfficiencyRouter
      //         },
      //         {
      //           path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentRisk',
      //           redirect: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentRisk/quitRisk',
      //           name: 'talentRiskHome',
      //           meta: {
      //             name: '人才风险'
      //           },
      //           component: () => import('../views/talentReviewAnalysis/TRanalysisComps/talentRisk/talentRiskHome'),
      //           children: talentRiskRouter
      //         },
      //         {
      //           path: '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentOptimize',
      //           redirect:
      //             '/talentReviewHome/talentReviewAnalysis/TRanalysis/TRanalysisDetails/talentOptimize/dpDistribution',
      //           name: 'talentOptimizeHome',
      //           meta: {
      //             name: '人才优化'
      //           },
      //           component: () =>
      //             import('../views/talentReviewAnalysis/TRanalysisComps/talentOptimize/talentOptimizeHome'),
      //           children: talentOptimizeRouter
      //         }
      //       ]
      //     }
      //   ]
      // }
    ]
  }
]

export default routes
