<script setup>
defineOptions({ name: 'diagnoseIndex' })
const router = useRouter()
const back = () => {
  router.back()
}
</script>
<template>
  <div class="diagnoseIndex">
    <div class="get-back">
      <div class="title">整体能力诊断项目</div>
      <div class="btn" @click="back()">返回</div>
    </div>
    <div class="main">
      <router-view></router-view>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.diagnoseIndex {
  width: 100%;
  .get-back {
    height: 82px;
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 29px 0 20px;
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      color: #333333;
      font-weight: bold;
    }

    .btn {
      width: 98px;
      height: 37px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #40a0ff;
      font-size: 16px;
      color: #40a0ff;
      text-align: center;
      line-height: 37px;
      cursor: pointer;
    }
  }

  .main {
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
    border-radius: 8px 8px 8px 8px;
  }
}
:deep(.page-btn-wrap) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
  .btn {
    min-width: 152px;
    line-height: 30px;
    text-align: center;
    color: #fff;
    background: #40a0ff;
    border-radius: 3px 3px 3px 3px;
    font-size: 12px;
    padding: 0 10px;
    cursor: pointer;
    &.border {
      background-color: transparent;
      border: 1px solid #40a0ff;
      color: #40a0ff;
    }
  }
}
</style>
