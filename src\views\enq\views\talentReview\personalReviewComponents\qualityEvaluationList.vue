<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { evaluationCommentsList } from '../../../request/api.js'

const props = defineProps({
  enqId: [String, Number],
  type: String
})
const emit = defineEmits(['getPotentialData'])

const eduInfoData = ref([])

function submit() {
  //直接下级不能为空
  let arr = []
  eduInfoData.value.forEach(item => {
    if (item.relationType == 'U') {
      arr.push(item)
    }
  })
  // 校验数据中有没有空值
  if (checkData(eduInfoData.value)) {
    return
  } else {
    emit('getPotentialData', eduInfoData.value)
  }
}

function getEducationData() {
  evaluationCommentsList({
    enqId: props.enqId,
    type: props.type
  }).then(res => {
    if (res.code == '200') {
      eduInfoData.value = res.data
    } else {
      ElMessage.error('获取数据失败!')
    }
  })
}

function checkData(data) {
  let arr = data
  let len = arr.length
  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    let { userName, comment } = obj
    if (!comment || comment.length < 30) {
      ElMessage.warning(`${userName} 评价评语最少30个字！`)
      return true
    }
  }
  return false
}

onMounted(() => {
  getEducationData()
})
</script>

<template>
  <div class="edu_info_wrap quality_evaluation_list_wrap" id="quality_evaluation_list_wrap">
    <div class="clearfix">
      <div class="edu_info_center">
        <el-table :data="eduInfoData" style="width: 100%" height="480">
          <el-table-column prop="userName" label="姓名" align="center" width="100"></el-table-column>
          <el-table-column prop="comment" label="综合评价评语" align="center">
            <template #default="scope">
              <el-input
                class="item"
                v-model="scope.row.comment"
                link
                clearable
                minlength="30"
                show-word-limit
                placeholder="请输入最少30个字"
              />
            </template>
          </el-table-column>
        </el-table>
        <div class="edu_info_mmain">
          <div class="align_center marginT_30">
            <el-button class="page_new_confirm_btn" type="primary" @click="submit">确定</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.edu_info_wrap {
  margin-bottom: 16px;
}

.edu_info_header {
  .item {
    width: 13%;
    // padding-left: 15px;
  }

  .item_icon_wrap {
    text-align: center;
    width: 12%;
  }
}

#quality_evaluation_list_wrap {
  height: 520px;

  .el-table {
    max-height: 450px !important;
  }
}
</style>
