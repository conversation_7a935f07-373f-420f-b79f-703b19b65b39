<script setup>
defineOptions({ name: 'step' })
const emits = defineEmits(['next'])
const modelList = ref([
  {
    id: 1,
    name: '市场洞察与趋势预测',
    desc: '市场洞察与趋势预判是指中小企业通过系统化的数据收集、行业分析、竞争观察及客户需求研究，识别市场机会与风险，并提前布局以应对未来变化的能力。'
  },
  {
    id: 2,
    name: '略意图定义与共识构建',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 3,
    name: '业务设计与商业模式创新',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 4,
    name: '创新焦点与机会管理',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 5,
    name: '战略解码与目标穿透',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 6,
    name: '关键任务与资源统筹',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 7,
    name: '组织协同与变革管理',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 8,
    name: '执行体系与流程构建',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 9,
    name: '动态校准与偏差修复',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 10,
    name: '数据驱动与智能决策',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 11,
    name: '绩效评估与激励对齐',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 12,
    name: '战略动态刷新机制',
    desc: '',
    question: [],
    manageValue: []
  }
])

const reportList = ref([
  {
    title: '整体能力表现',
    list: [
      {
        name: '整体表现'
      },
      {
        name: '能力长短板'
      },
      {
        name: '能力图谱'
      }
    ]
  },
  {
    title: '能力短板分析',
    list: [
      {
        name: '能力差因'
      },
      {
        name: '能力解码'
      },
      {
        name: '短板详解'
      }
    ]
  },
  {
    title: '各模块能力详细分析',
    list: [
      {
        name: '模块整体表现'
      },
      {
        name: '模块差因分析'
      },
      {
        name: '模块能力改善'
      }
    ]
  },
  {
    title: '能力改善总结',
    list: [
      {
        name: '能力短板风险'
      },
      {
        name: '能力提升举措'
      },
      {
        name: '短期行动计划'
      }
    ]
  }
])

const onNext = () => {
  emits('next')
}
</script>
<template>
  <div class="step-page-content">
    <div class="page-title-line">评估模式</div>
    <div class="eval-type">
      <div class="type-item">
        <div class="item-content">
          <div class="title">单人评估</div>
          <div class="desc">您一人独自完成核心能力的评估</div>
        </div>
      </div>
      <div class="type-item">
        <div class="item-content">
          <div class="title">多人评估</div>
          <div class="desc">邀请公司其他人员一起参与核心 能力的评估</div>
        </div>
      </div>
    </div>
    <div class="step-wrap">
      <div class="step-list">
        <div class="step-list-title">
          <div class="index">1</div>
          <div class="title">选择评估模型</div>
        </div>
        <div class="step-list-line"></div>
        <div class="step-list-content">
          <div class="content-title">可对整个模块下所有能力组件进行评估，也可以按需选择指定评估项，进行单独评估</div>
          <div class="model">
            <div class="model-list" v-for="item in modelList" :key="item.id">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div class="step-list">
        <div class="step-list-title">
          <div class="index">2</div>
          <div class="title">邀请测评人</div>
        </div>
        <div class="step-list-line"></div>
        <div class="step-list-content">
          <div class="content-title">可对整个模块下所有能力组件进行评估，也可以按需选择指定评估项，进行单独评估</div>
          <div class="content-main">
            <div class="content-section invitation">
              <div class="c-section-title">
                <div class="title-index">1</div>
                <div class="title-text">
                  发送自动生成的测评链接，如：<span class="href">https://ceping.xiyiqq.com/xxxxxx</span>
                </div>
              </div>
            </div>
            <div class="content-section invitation">
              <div class="c-section-title">
                <div class="title-index">2</div>
                <div class="title-text">发送自动生成的测评了解二维码给指定人员或指定微信群</div>
                <div class="qrcode">二维码</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="step-list">
        <div class="step-list-title">
          <div class="index">3</div>
          <div class="title">开始能力测评</div>
        </div>
        <div class="step-list-line"></div>
        <div class="step-list-content">
          <div class="content-title">可对整个模块下所有能力组件进行评估，也可以按需选择指定评估项，进行单独评估</div>
          <div class="content-main">
            <div class="content-section eval">
              <div class="c-section-title">
                <div class="title-index">1</div>
                <div class="title-text">阅读测评示例</div>
                <div class="eg"></div>
              </div>
            </div>
            <div class="content-section eval">
              <div class="c-section-title">
                <div class="title-index">2</div>
                <div class="title-text">正式开始测评</div>
                <div class="eg"></div>
              </div>
            </div>
            <div class="content-section eval">
              <div class="c-section-title">
                <div class="title-index">3</div>
                <div class="title-text">跟进测评进度</div>
                <div class="eg"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="step-list">
        <div class="step-list-title">
          <div class="index">4</div>
          <div class="title">查看测评报告</div>
        </div>
        <div class="step-list-line"></div>
        <div class="step-list-content">
          <div class="report">
            <div class="report-item" v-for="item in reportList" :key="item.id">
              <div class="item-title">{{ item.title }}</div>
              <div class="item-list" v-for="list in item.list" :key="list.name">{{ list.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.eval-type {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 18px;
  margin-bottom: 30px;
  .type-item {
    flex: 1;
    background-image: url('@/assets/imgs/AI/eval-bg-1.png');
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    &:nth-child(2) {
      background-image: url('@/assets/imgs/AI/eval-bg-2.png');
    }
    .item-content {
      padding: 53px 0 40px 180px;
      border-radius: 10px;
      .title {
        font-weight: 700;
        font-size: 20px;
        color: #333333;
        line-height: 32px;
      }
      .desc {
        font-size: 16px;
        color: #6da0ac;
        line-height: 29px;
      }
    }
  }
}
.step-wrap {
  .step-list {
    position: relative;
    &:last-of-type {
      .step-list-line {
        display: none;
      }
    }
    .step-list-title {
      position: absolute;
      top: -16px;
      left: 0;
      width: 166px;
      background: #dcecfc;
      border-radius: 68px;
      padding: 4px 5px;
      display: flex;
      align-items: center;
      z-index: 2;
      .index {
        width: 26px;
        height: 26px;
        background: linear-gradient(90deg, #59b9fc 5%, #4276f0 97%);
        line-height: 26px;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        border-radius: 50%;
        text-align: center;
        margin-right: 11px;
      }
      .title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
      }
    }
    .step-list-line {
      position: absolute;
      left: 14px;
      top: 0;
      width: 8px;
      background: #dcecfc;
      height: 100%;
      z-index: 1;
    }
    .step-list-content {
      padding: 27px 0 30px 40px;
      .content-title {
        font-size: 16px;
        color: #3d3d3d;
        line-height: 20px;
        margin-bottom: 17px;
      }
      .content-main {
        display: flex;
        align-items: center;
        gap: 15px;
      }
      .content-section {
        flex: 1;
        min-height: 122px;
        background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #c6dbf3;
        padding: 15px;
        &.invitation {
          display: flex;
          align-items: center;
          justify-content: center;
          .qrcode {
            width: 89px;
            height: 89px;
            background: #d8d8d8;
            font-size: 16px;
            color: #3d3d3d;
            text-align: center;
            line-height: 89px;
            margin-left: 17px;
          }
        }
        &.eval {
          display: flex;
          .c-section-title {
            align-items: flex-start;
          }
          .eg {
            width: 245px;
            height: 167px;
            background: #d8d8d8;
            margin-left: 27px;
          }
        }
        .c-section-title {
          display: flex;
          align-items: center;
          line-height: 30px;
          .title-index {
            width: 30px;
            height: 30px;
            background: #53a9f9;
            text-align: center;
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            border-radius: 50%;
            margin-right: 8px;
          }
        }
      }
    }
  }
}

.model {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  .model-list {
    width: 184px;
    line-height: 32px;
    text-align: center;
    background: #f0f9ff;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #a5c1dc;
    font-size: 14px;
    color: #6c757e;
  }
}
.report {
  display: flex;
  align-items: stretch;
  gap: 26px;
  .report-item {
    flex: 1;
    background: linear-gradient(224deg, #ddefff 0%, rgba(230, 252, 255, 0.6) 100%);
    border-radius: 8px 8px 8px 8px;
    padding: 20px;
    cursor: pointer;
    .item-title {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      margin-bottom: 20px;
    }
    .item-list {
      line-height: 38px;
      text-align: center;
      background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), rgba(255, 255, 255, 0.5);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      font-size: 14px;
      color: #333333;
      margin-bottom: 10px;
      &.active,
      &:hover {
        color: #40a0ff;
        box-shadow: 0px 0px 10px 0px rgba(83, 172, 255, 0.5);
        border: 1px solid #53acff;
      }
    }
  }
}
.href {
  color: #53a9f9;
}
</style>
