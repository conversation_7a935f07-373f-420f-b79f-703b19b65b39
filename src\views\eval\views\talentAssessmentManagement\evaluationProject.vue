<template>
    <div class="evaluation_project_wrap">
        <div class="page_main_title">评估项目管理</div>
        <div class="page_section">
            <div class="step_wrap">
                <stepBar
                    :stepData="stepData"
                    :currentIndex="currentIndex"
                    v-on:prev="prev"
                    v-on:next="next"
                    v-on:submit="submit"
                ></stepBar>
            </div>
            <div class="evaluation_project_center page_section">
                <div class="from_wrap">
                    <el-form
                        :model="ruleForm"
                        :rules="rules"
                        ref="ruleForm"
                        label-width="150px"
                        class="demo-ruleForm"
                    >
                        <el-form-item :span="12" label="测评起止日期" required>
                            <el-col :span="8">
                                <el-form-item prop="date1">
                                    <el-date-picker
                                        v-model="ruleForm.date1"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                    ></el-date-picker>
                                </el-form-item>
                            </el-col>
                        </el-form-item>
                        <el-form-item label="测评项目名称" prop="name">
                            <el-input v-model="ruleForm.name"></el-input>
                        </el-form-item>
                        <el-form-item label="测评类型" prop="region">
                            <el-select v-model="ruleForm.region" placeholder="请选择活动区域">
                                <el-option label="区域一" value="shanghai"></el-option>
                                <el-option label="区域二" value="beijing"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="选择模型" prop="type">
                            <el-checkbox-group v-model="ruleForm.type">
                                <el-checkbox label="销售与市场能力测评" name="type" border></el-checkbox>
                                <el-checkbox label="中基层管理能力测评" name="type" border></el-checkbox>
                                <el-checkbox label="中基层管理能力测评" name="type" border></el-checkbox>
                                <el-checkbox label="中基层管理能力测评" name="type" border></el-checkbox>
                                <el-checkbox label="中基层管理能力测评" name="type" border></el-checkbox>
                                <el-checkbox label="中基层管理能力测评" name="type" border></el-checkbox>
                                <el-checkbox label="中基层管理能力测评" name="type" border></el-checkbox>
                                <el-checkbox label="中基层管理能力测评" name="type" border></el-checkbox>
                                <el-checkbox label="中基层管理能力测评" name="type" border></el-checkbox>
                                <el-checkbox label="中基层管理能力测评" name="type" border></el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                        <el-form-item label="测评项目说明" prop="desc">
                            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6}" v-model="ruleForm.desc"></el-input>
                        </el-form-item>
                        <!-- <el-form-item>
                            <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
                            <el-button @click="resetForm('ruleForm')">重置</el-button>
                        </el-form-item> -->
                    </el-form>
                </div>
            </div>
            <div class="step_btn_wrap">
                <el-button type="primary" @click="prev()" v-show="this.currentIndex > 0">上一步</el-button>
                <el-button
                    type="primary"
                    @click="submit()"
                    v-if="this.currentIndex == this.stepData.length-1"
                >确认</el-button>
                <el-button type="primary" @click="next()" v-else>下一步</el-button>
            </div>
        </div>
    </div>
</template>
 
<script>
import stepBar from "@/components/common/stepBar"
export default {
    name: "evaluationProject",
    components: {
        stepBar
    },
    data() {
        return {
            value1: ["2020-01-01", "2020-02-02"],
            evalProjectName: "",
            evalProjectExplain: "",
            currentIndex: 1,
            stepData: [
                {
                    name: "项目基本信息",
                    state: "inProgress"
                },
                {
                    name: "选择测评对象",
                    state: "inComplete"
                },
                {
                    name: "设置报告范围",
                    state: "inComplete"
                },
                {
                    name: "配置与启动",
                    state: "inComplete"
                }
            ],
            ruleForm: {
                name: "",
                region: "",
                date1: [],
                delivery: false,
                type: [],
                resource: "",
                desc: ""
            },
            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入活动名称",
                        trigger: "blur"
                    },
                    {
                        min: 3,
                        max: 5,
                        message: "长度在 3 到 5 个字符",
                        trigger: "blur"
                    }
                ],
                region: [
                    {
                        required: true,
                        message: "请选择活动区域",
                        trigger: "change"
                    }
                ],
                date1: [
                    {
                        type: "array",
                        required: true,
                        message: "请选择日期",
                        trigger: "change"
                    }
                ],
                date2: [
                    {
                        type: "date",
                        required: true,
                        message: "请选择时间",
                        trigger: "change"
                    }
                ],
                type: [
                    {
                        type: "array",
                        required: true,
                        message: "请至少选择一个活动性质",
                        trigger: "change"
                    }
                ],
                resource: [
                    {
                        required: true,
                        message: "请选择活动资源",
                        trigger: "change"
                    }
                ],
                desc: [
                    {
                        required: true,
                        message: "请填写测评项目说明",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    created() {
        this.currentIndex = this.currentIndex - 1;
    },
    methods: {
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    alert("submit!");
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        prev: function() {
            if (this.currentIndex == 0) {
                return false;
            }
            // // this.stepData[this.currentIndex].state = "inComplete";
            this.currentIndex--;
            console.log("上一步");
        },
        next: function() {
            console.log("下一步");
            if (this.currentIndex == this.stepData.length - 1) {
                return false;
            }
            // this.stepData[this.currentIndex].state = "completed";
            this.currentIndex++;
            // this.stepData[this.currentIndex].state = "inProgress";
        },
        submit: function() {
            console.log("提交");
        }
    }
};
</script>
 
<style scoped lang="scss">
.step_btn_wrap {
    text-align: center;
    padding-top: 30px;
}
.el-input__inner {
    width: 280px;
}
.el-checkbox.is-bordered+.el-checkbox.is-bordered{margin-left:0;}
.evaluation_project_center .el-form-item__content .el-range-separator{
    width: 8%;
}
</style>