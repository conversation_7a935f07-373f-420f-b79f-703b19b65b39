<template>
  <div class="training_activities_center clearfix">
    <div class="training_activities_item flex_row_between" v-for="(item, index) in listData" :key="item.id">
      <div class="item_index">{{ index + 1 }}</div>
      <div class="item_content_wrap">
        <div class="item_content flex_row_between">
          <div class="item_content_list">
            <div class="list_title">项目名称</div>
            <div class="list_text">{{ item.projectName }}</div>
          </div>
          <div class="item_content_list">
            <div class="list_title">开始日期</div>
            <div class="list_text">{{ item.startDate }}</div>
          </div>
          <div class="item_content_list">
            <div class="list_title">结束日期</div>
            <div class="list_text">{{ item.endDate }}</div>
          </div>
          <div class="item_content_list">
            <div class="list_title">参与人数</div>
            <div class="list_text">
              <span class="list_num">{{ item.partakeNum }}</span
              >人
            </div>
          </div>
        </div>
      </div>
      <div class="item_oper align_right">
        <el-button type="primary" plain >查看报告</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  listData: {
    type: Array,
    required: true
  }
})
</script>

<style scoped lang="scss">
.training_activities_item {
  position: relative;
  padding: 16px 8px 16px 30px;
  margin-bottom: 8px;
  border: 1px solid #d9d9d9;
  overflow: hidden;
  .item_index {
    width: 50px;
    font-weight: bold;
    font-size: 20px;
    color: #0070c0;
  }
  .item_content_wrap {
    width: 80%;
    padding: 0 8px;
    .item_content {
      padding-right: 200px;
      .item_content_list {
        color: #525e6c;
        .list_title {
          font-weight: bold;
          margin-bottom: 8px;
        }
        .list_num {
          color: #0070c0;
          font-weight: bold;
          font-size: 16px;
        }
      }
    }
  }
  .item_oper {
    width: 200px;
    padding: 0 8px;
    align-items: center;
    color: #0099fd;
    text-align: right;
  }
}
</style>
