<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import MarkdownIt from 'markdown-it'
import markdownItContainer from 'markdown-it-container'
import * as echarts from 'echarts'
import service from '@/api/request.js'
import { historicalDetail } from '@/api/modules/project'
defineOptions({ name: 'report' })
const activeType = ref('1')
const reportTypeList = reactive([
  { iconName: 'report-type-1', type: '1', name: '整体报告' },
  { iconName: 'report-type-2', type: '2', name: '流程简报' },
  { iconName: 'report-type-3', type: '3', name: '组织简报' },
  { iconName: 'report-type-4', type: '4', name: '人岗简报' },
  { iconName: 'report-type-5', type: '5', name: '数字化简报' },
  { iconName: 'report-type-5', type: '6', name: 'AI报告' }
])
const openReport = ref(false)
const changeType = item => {
  activeType.value = item.type
  if (item.type == '6') {
    openReport.value = true
    seeReport()
  }
}
// AI报告
const dialogVisible = ref(false)
const markDownText = ref('')
const seeReport = () => {
  dialogVisible.value = true
  getHistoricalDetail()
}

const loading = ref(false)
//#region 历史
const getHistoricalDetail = () => {
  loading.value = true
  historicalDetail({ conversationId: '25234155-3101-4496-a696-9edd3bd76b88' })
    .then(res => {
      isEcharts.value = true
      let text = JSON.parse(res.data[0].outputs).answer
      markDownText.value = text
    })
    .finally(() => {
      loading.value = false
    })
}
//#endregion
//#region ai报告
const markDown = new MarkdownIt({
  html: true
})
const isEcharts = ref(false)
markDown.use(markdownItContainer, 'echarts', {
  render: function (tokens, idx) {
    const m = tokens[idx].info.trim().match(/^echarts\s+(.*)$/)
    if (tokens[idx].nesting == 1) {
      const data = JSON.parse(m[1])
      if (isEcharts.value) {
        nextTick(() => {
          setTimeout(() => {
            let myChart = echarts.init(document.getElementById('echarts_content_' + data.id))
            myChart.setOption(data.option)
          }, 1000)
          // 可以对返回数据进行渲染，也可以通过数据请求获取数据信息进行渲染
        })
        return `<div id="echarts_content_${data.id}" style="width: 100%;height: 300px;display: flex;align-items:center;justify-content:center">`
      } else {
        return `<div id="echarts_content_${data.id}" style="width: 100%;height: 300px;display: flex;align-items:center;justify-content:center;color:#87CEFA;">echarts图表加载中...`
      }
    } else {
      return `</div>\n`
    }
  }
})
const anewCreate = () => {
  markDownText.value = ''
  isEcharts.value = false
  service({
    url: '/workflowQuestion/chatMessages',
    method: 'post',
    params: { conversationId: '' },
    responseType: 'stream',
    onDownloadProgress: progressEvent => {
      let contentThink = ''
      const chunk = progressEvent.event.currentTarget.responseText
      const lines = chunk.split('\n')
      for (const line of lines) {
        if (line.startsWith('data:')) {
          const data = line.slice(5).trim()
          try {
            const jsonData = JSON.parse(data)
            if (jsonData?.event == 'message') {
              const result = jsonData.answer
              if (jsonData.answer) {
                contentThink += result
                markDownText.value = contentThink
              }
            } else if (jsonData?.event == 'message_end') {
              isEcharts.value = true
              markDownText.value = contentThink
            }
          } catch (error) {
            console.error('Failed to parse JSON data:', error)
          }
        }
      }
    }
  })
    .finally(() => {})
    .catch(err => {})
}
//#endregion

const asideList = ref([
  {
    title: '整体能力表现',
    list: [
      {
        id: 'ztbx',
        name: '整体表现'
      },
      {
        id: 'nlcdb',
        name: '能力长短板'
      },
      {
        id: 'nltp',
        name: '能力图谱'
      }
    ]
  },
  {
    title: '能力短板分析',
    list: [
      {
        id: 'nlcy',
        name: '能力差因'
      },
      {
        id: 'nljm',
        name: '能力解码'
      },
      {
        id: 'dbxj',
        name: '短板详解'
      }
    ]
  },
  {
    title: '各模块能力详细分析',
    list: [
      {
        id: 'mkztbx',
        name: '模块整体表现'
      },
      {
        id: 'mkcyfx',
        name: '模块差因分析'
      },
      {
        id: 'mknlgs',
        name: '模块能力改善'
      }
    ]
  },
  {
    title: '能力改善总结',
    list: [
      {
        id: 'nldbfx',
        name: '能力短板风险'
      },
      {
        id: 'nltsjz',
        name: '能力提升举措'
      },
      {
        id: 'dqxdhj',
        name: '短期行动计划'
      }
    ]
  }
])
const activeAside = ref('ztbx')
const activeAsideName = ref('整体表现')
const change = list => {
  activeAside.value = list.id
  activeAsideName.value = list.name
}
const chartData = ref([
  {
    name: '销售战略',
    target: '75.0 ',
    value: '53.0 '
  },
  {
    name: '市场洞察',
    target: '78.0 ',
    value: '60.0 '
  },
  {
    name: '客户洞察',
    target: '75.0 ',
    value: '59.0 '
  },
  {
    name: '市场营销',
    target: '75.0 ',
    value: '68.0 '
  },
  {
    name: '商机管理',
    target: '75.0 ',
    value: '63.0 '
  },
  {
    name: '客户管理',
    target: '82.0 ',
    value: '63.0 '
  },
  {
    name: '渠道管理',
    target: '75.0 ',
    value: '55.0 '
  },
  {
    name: '销售运营',
    target: '84.0 ',
    value: '61.0 '
  },
  {
    name: '销售团队管理',
    target: '87.0 ',
    value: '68.0 '
  },
  {
    name: '销售管理数字化',
    target: '82.0 ',
    value: '57.0'
  }
])
const setOptions = () => {
  return {
    xAxisData: chartData.value.map(item => item.name),
    xAxis: {
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    legend: {
      data: ['您的得分', '行业领先企业']
    },
    series: [
      {
        name: '您的得分',
        type: 'bar',
        data: chartData.value.map(item => item.value),
        itemStyle: {
          color: '#53B8FF'
        }
      },
      {
        name: '行业领先企业',
        type: 'line',
        data: chartData.value.map(item => item.target),
        itemStyle: {
          color: '#9BD082'
        }
      }
    ]
  }
}
</script>
<template>
  <div class="page-container">
    <div class="page-text">您可以查看如下诊断报告 ：</div>
    <div class="project-card">
      <div class="tag">整体能力诊断</div>
      <div class="info-card">
        <div class="top">
          <div class="title">供应链整体能力诊断</div>
        </div>
        <div class="bottom">
          <span class="time">2022/05/22~2022/06/22</span>
          <span class="time">王伟 13856063220</span>
        </div>
      </div>
      <div class="inject-card">
        <div class="item-tag">参与人数</div>
        <div class="number"><span>125</span>人</div>
      </div>
      <div class="module-card">
        <div class="item">
          <span class="item-tag">模块</span>
          <span class="text">12</span>
        </div>
        <div class="item">
          <span class="item-tag">组件</span>
          <span class="text">126</span>
        </div>
        <div class="item">
          <span class="item-tag">DNA</span>
          <span class="text">1221</span>
        </div>
      </div>
      <div class="report-type">
        <div
          class="type-list"
          @click="changeType(item)"
          :class="{ active: activeType == item.type }"
          v-for="item in reportTypeList"
        >
          <SvgIcon
            width="33px"
            height="33px"
            :name="item.iconName + `${activeType == item.type ? '-active' : ''}`"
          ></SvgIcon>
          <div class="name">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <div class="report-main">
      <div class="aside-wrap">
        <div class="aside-title">测评报告内容</div>
        <div class="aside-item" v-for="item in asideList" :key="item.title">
          <div class="item-title">{{ item.title }}</div>
          <div
            class="item-list"
            :class="{ active: activeAside == list.id }"
            v-for="list in item.list"
            :key="list.id"
            @click="change(list)"
          >
            {{ list.name }}
          </div>
        </div>
      </div>
      <div class="report-page-content">
        <div class="title">{{ activeAsideName }}</div>
        <div class="page-title-line">
          <span>整体得分与所处阶段</span>
          <div class="line"></div>
          <div class="ai-btn">AI解读</div>
        </div>
        <div class="flex-box">
          <div class="box-item">
            <div class="content">
              <div class="item">
                <div class="title">能力指数</div>
                <img src="@/assets/imgs/AI/report-1.png" alt="" srcset="" />
              </div>
              <div class="item">
                <div class="title">能力阶段</div>
                <img style="margin-top: 40px" src="@/assets/imgs/AI/report-2.png" alt="" srcset="" />
              </div>
            </div>
          </div>
          <div class="box-item">
            <div class="content jiedu">
              <div class="jiedu">整体得分与级别详情</div>
              <div class="jiedu-text">
                本次测评整体得分 61.8 分，处于 规范级 阶段，距离上一层级 优秀级 ，还有 8.2
                分的差距；在所有参与测评的企业中，排名 4238 名，位列全部企业的 64.9% 位置；同行业排名 182
                名，位列行业企业的 71.9% 位置；
              </div>
            </div>
          </div>
        </div>
        <div class="page-title-line">
          <span>各项能力对标</span>
          <div class="line"></div>
          <div class="ai-btn">AI解读</div>
        </div>
        <div class="flex-box">
          <div class="box-item">
            <div class="title color-[#333]">能力得分对比</div>
            <div class="content">
              <div class="item h-[300px]">
                <!-- <img style="margin-top: 9px" src="@/assets/imgs/AI/report-3.png" alt="" srcset="" /> -->
                <EChartsBar :showLine="true" :options="setOptions()"></EChartsBar>
              </div>
            </div>
          </div>
          <div class="box-item">
            <div class="content jiedu">
              <div class="jiedu">各项能力对比详情</div>
              <div class="jiedu-text">
                ‌营销与销售‌：27.0分，相对较低，需加强市场洞察、品牌建设和渠道管理能力。
                ‌产品研发‌：23.1分，同样较低，需提升产品规划、创新机制与敏捷开发能力。
                ‌采购管理‌：30.1分，处于中等水平，需进一步优化供应商管理、成本控制和风险管理。
                ‌生产管理‌：38.7分，表现相对较好，但仍需持续优化设备维护、工艺标准化和生产成本。
                ‌供应链产销协同‌：32.5分，需加强需求预测、库存优化和物流网络规划。
                ‌组织人才发展‌：21.9分，最低，需重点提升人才梯队建设、绩效与激励机制和培训发展体系。
                ‌产业分析与跟踪‌：24.8分，需加强行业趋势分析、政策法规跟踪和市场需求洞察。
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" title="AI报告" width="70%">
    <div class="project-card">
      <div class="tag">整体能力诊断</div>
      <div class="info-card">
        <div class="top">
          <div class="title">供应链整体能力诊断</div>
        </div>
        <div class="bottom">
          <span class="time">2022/05/22~2022/06/22</span>
          <span class="time">王伟 13856063220</span>
        </div>
      </div>
      <div class="inject-card">
        <div class="item-tag">参与人数</div>
        <div class="number"><span>125</span>人</div>
      </div>
      <div class="module-card">
        <div class="item">
          <span class="item-tag">模块</span>
          <span class="text">12</span>
        </div>
        <div class="item">
          <span class="item-tag">组件</span>
          <span class="text">126</span>
        </div>
        <div class="item">
          <span class="item-tag">DNA</span>
          <span class="text">1221</span>
        </div>
      </div>
      <div class="btn">
        <el-button type="primary" @click="anewCreate()">重新生成报告</el-button>
      </div>
    </div>
    <div class="markDown-main">
      <div class="reset-tailwind" v-html="markDown.render(markDownText)"></div>
    </div>
  </el-dialog>
</template>
<style lang="scss" scoped>
.page-container {
  width: 100%;
  height: 100%;
  padding-top: 12px;
  .page-text {
    margin-bottom: 9px;
    font-size: 14px;
    color: #3d3d3d;
  }

  .project-card {
    @include flex-center(row, space-around, stretch);
    width: 100%;
    height: 110px;
    margin-bottom: 24px;
    padding: 10px 15px 10px 15px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #c6dbf3;
    .tag {
      height: 23px;
      left: 23px;
      padding: 0 14px;
      margin-top: 32px;
      border-radius: 5px;
      font-size: 14px;
      color: #40a0ff;
      background-color: #e3f1ff;
    }
    .info-card {
      padding-top: 14px;
      padding-right: 20px;
      border-right: 1px solid #d8d8d8;
      .top {
        @include flex-center(row, flex-start, center);
        margin-bottom: 15px;
        .title {
          margin-right: 13px;
          font-size: 18px;
          color: #3d3d3d;
          font-weight: 500;
        }
        .progress-tag {
          padding: 3px 8px;
          font-size: 12px;
          line-height: 12px;
          color: #ffffff;
          background: #40a0ff;
          border-radius: 3px;
        }
      }
      .bottom {
        .time {
          margin-right: 14px;
          font-size: 14px;
          line-height: 12px;
          color: #acacac;
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
    .inject-card {
      padding-right: 23px;
      border-right: 1px solid #d8d8d8;
      padding-left: 10px;
      .item-tag {
        width: 80px;
        text-align: center;
        margin-bottom: 17px;
        line-height: 20px;
        font-size: 14px;
        color: #40a0ff;
        border-radius: 42px;
        background-color: #cbe4fd;
      }
      .number {
        text-align: center;
        font-size: 14px;
        color: #3d3d3d;
        span {
          font-size: 30px;
          font-weight: 500;
          color: #40a0ff;
        }
      }
    }
    .module-card {
      padding-top: 4px;
      padding-right: 25px;
      border-right: 1px solid #d8d8d8;
      padding-left: 10px;
      .item {
        margin-bottom: 5px;
        &:last-child {
          margin-bottom: 0;
        }
        .item-tag {
          padding: 1px 9px;
          margin-right: 11px;
          font-size: 12px;
          line-height: 21px;
          border-radius: 46px;
          color: #40a0ff;
          background-color: #e3f1ff;
        }
        .text {
          color: #40a0ff;
          font-size: 16px;
        }
      }
    }
    .report-type {
      @include flex-center(row, center, center);
      padding-left: 10px;
      gap: 17px;
      .type-list {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: 0 0 90px;
        text-align: center;
        color: #40a0ff;
        background: #e3f1ff;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 0;
        cursor: pointer;
        &.active {
          background: #40a0ff;
          color: #fff;
        }
        .icon {
          margin-bottom: 4px;
        }
        .name {
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
    .operate-card {
      @include flex-center(column, center, center);
      font-size: 14px;
      color: #40a0ff;
    }
  }
}
.report-main {
  display: flex;
  flex-flow: row nowrap;
  gap: 18px;
  .aside-wrap {
    flex: 0 0 198px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px 8px 8px 8px;
    padding: 14px 9px;
    .aside-title {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      margin-bottom: 26px;
    }
    .aside-item {
      text-align: center;
      .item-title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        line-height: 50px;
      }
      .item-list {
        line-height: 42px;
        border-radius: 5px 5px 5px 5px;
        border: 1px solid transparent;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        &.active {
          border-color: #53a9f9;
          color: #53a9f9;
        }
      }
    }
  }
  .report-page-content {
    flex: 1;
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
    border-radius: 8px 8px 8px 8px;
    padding: 20px 30px;
    .title {
      font-weight: 500;
      font-size: 16px;
      color: #53a9f9;
      margin-bottom: 20px;
    }
    .page-title-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 20px;
      .line {
        flex: 1;
        height: 1px;
        background: #e5e5e5;
      }
      .ai-btn {
        width: 73px;
        text-align: center;
        line-height: 30px;
        background: #e1f3ff;
        border-radius: 30px;
        font-weight: 500;
        font-size: 16px;
        color: #40a0ff;
        cursor: pointer;
      }
    }
    .value-stage {
      margin-bottom: 25px;
    }
    .flex-box {
      display: flex;
      align-items: stretch;
      gap: 28px;
      margin-bottom: 30px;
      .box-item {
        flex: 1;
        .title {
          color: #333;
        }
        .content {
          display: flex;
          gap: 22px;
          &.jiedu {
            flex-flow: column;
            height: 100%;
            padding: 18px 21px;
            background: #e3efff;
            border-radius: 6px 6px 6px 6px;
          }
          .item {
            flex: 1;
            .title {
              width: 100%;
              line-height: 30px;
              background: #eaf4ff;
              border-radius: 51px;
              font-weight: 600;
              text-align: center;
              font-size: 16px;
              color: #40a0ff;
            }
          }
          img {
            width: 100%;
            height: auto;
          }
          .jiedu-title {
            font-size: 16px;
            color: #40a0ff;
            line-height: 35px;
          }
          .jiedu-text {
            font-size: 14px;
            color: #3d3d3d;
            line-height: 31px;
          }
        }
      }
    }
  }
  .border {
    padding: 16px 25px;
    background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
  }
}

.project-card {
  @include flex-center(row, space-between, stretch);
  width: 100%;
  height: 110px;
  margin-bottom: 24px;
  padding: 10px 35px 10px 22px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #c6dbf3;
  .tag {
    height: 23px;
    left: 23px;
    padding: 0 14px;
    margin-top: 32px;
    border-radius: 5px;
    font-size: 14px;
    color: #40a0ff;
    background-color: #e3f1ff;
  }
  .info-card {
    padding-top: 14px;
    padding-right: 56px;
    // border-right: 1px solid #d8d8d8;
    .top {
      @include flex-center(row, flex-start, center);
      margin-bottom: 15px;
      .title {
        margin-right: 13px;
        font-size: 18px;
        color: #3d3d3d;
        font-weight: 500;
      }
      .progress-tag {
        padding: 3px 8px;
        font-size: 12px;
        line-height: 12px;
        color: #ffffff;
        background: #40a0ff;
        border-radius: 3px;
      }
    }
    .bottom {
      .time {
        margin-right: 44px;
        font-size: 14px;
        line-height: 12px;
        color: #acacac;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  .inject-card {
    padding: 0 100px;
    border-right: 1px solid #d8d8d8;
    border-left: 1px solid #d8d8d8;
    .item-tag {
      width: 80px;
      text-align: center;
      margin-bottom: 17px;
      line-height: 20px;
      font-size: 14px;
      color: #40a0ff;
      border-radius: 42px;
      background-color: #cbe4fd;
    }
    .number {
      text-align: center;
      font-size: 14px;
      color: #3d3d3d;
      span {
        font-size: 30px;
        font-weight: 500;
        color: #40a0ff;
      }
    }
  }
  .module-card {
    padding-top: 4px;
    .item {
      margin-bottom: 5px;
      &:last-child {
        margin-bottom: 0;
      }
      .item-tag {
        padding: 1px 9px;
        margin-right: 11px;
        font-size: 12px;
        line-height: 21px;
        border-radius: 46px;
        color: #40a0ff;
        background-color: #e3f1ff;
      }
      .text {
        color: #40a0ff;
        font-size: 16px;
      }
    }
  }
  .report-type {
    @include flex-center(row, center, center);
    gap: 17px;
    .type-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 0 0 90px;
      text-align: center;
      color: #40a0ff;
      background: #e3f1ff;
      border-radius: 8px 8px 8px 8px;
      padding: 8px 0;
      cursor: pointer;
      &.active {
        background: #40a0ff;
        color: #fff;
      }
      .icon {
        margin-bottom: 4px;
      }
      .name {
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
  .operate-card {
    @include flex-center(column, center, center);
    font-size: 14px;
    color: #40a0ff;
  }
  .btn {
    display: flex;
    align-items: center;
    border-left: 1px solid #d8d8d8;
    padding: 20px;
  }
}
.markDown-main {
  height: 500px;
  overflow-y: auto;
}
:deep(.reset-tailwind) {
  /* 基础表格样式 */
  table {
    width: 100%;
    border-collapse: collapse; /* 边框合并 */
    margin: 1rem 0;
    font-family: 'Segoe UI', sans-serif;
  }

  /* 表头样式 */
  th {
    background-color: #f5f7fa !important;
    border: 1px solid #e9ecef;
    padding: 12px;
    text-align: left; /* 配合 Markdown 对齐使用 */
    font-weight: 600;
  }

  /* 表身样式 */
  td {
    border: 1px solid #e9ecef;
    padding: 12px;
    vertical-align: top; /* 垂直对齐 */
  }

  /* 斑马线效果 */
  tbody tr:nth-child(even) {
    background-color: #f9fafb;
  }

  /* 悬停效果 */
  tbody tr:hover {
    background-color: #f0f3f5;
    cursor: pointer;
  }

  // /* 响应式适配 */
  // @media (max-width: 600px) {
  //   table {
  //     font-size: 0.9em;
  //   }
  // }
}
.reset-tailwind,
.reset-tailwind * {
  all: revert;
}
</style>
