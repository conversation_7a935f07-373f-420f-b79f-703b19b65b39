<template>
  <el-dialog
    :title="popupTitleSign ? '新增组织' : '修改组织'"
    :visible.sync="dialogVisible"
    @close="$emit('update:show', false)"
    :show="show"
    width="40%"
    center
  >
    <div class="line_wrap flex_row_betweens">
      <span>上级组织：</span>
      <div>
        <el-cascader
          :options="treeData"
          v-model="parentOrgCode"
          :placeholder="popupTitleSign ? '请选择' : parentOrgCode ? '' : '尚无上级组织'"
          :change-on-select="true"
          :props="{
            label: 'value',
            value: 'code',
            expandTrigger: 'hover'
          }"
          @change="handleItemChange"
          :key="cascaderKey"
          :disabled="popupTitleSign == false ? true : false"
          clearable
        >
        </el-cascader>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>业务领域：</span>
      <div>
        <el-select v-model="bizDomainCode" placeholder="请选择" clearable>
          <el-option
            v-for="opt in bizDomainOptions"
            :key="opt.dictCode"
            :label="opt.codeName"
            :value="opt.dictCode"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span><span class="required_fields_icon">&lowast;</span> 是否实体：</span>
      <div>
        <el-select v-model="isEntity" clearable placeholder="请选择">
          <el-option
            v-for="opt in isEntityOptions"
            :key="opt.dictCode"
            :label="opt.codeName"
            :value="opt.dictCode"
          ></el-option>
        </el-select>
      </div>
    </div>
    <!-- <div class="line_wrap flex_row_betweens" v-if="!popupTitleSign">
            <span>层级：</span>
            <div>
                <el-input type='number' v-model.number="layerNo" min='0'></el-input>
            </div>
        </div> -->
    <!-- 新增组织 不选部门负责人 修改组织才能选部门负责人 -->
    <div class="line_wrap flex_row_betweens" v-if="!popupTitleSign">
      <span>组织负责人：</span>
      <div>
        <el-select
          v-model="orgLeaderName"
          filterable
          remote
          :remote-method="getCandidateFun"
          @change="candidateChange"
          :loading="loading"
          clearable
          placeholder="请输入人员名称进行查询"
        >
          <el-option v-for="opt in staffOptions" :key="opt.dictCode" :label="opt.codeName" :value="opt.dictCode">
            <span class="options_item">{{ opt.codeName }}</span
            >-- <span class="options_item">{{ opt.orgName }}</span
            >--
            <span class="options_item">{{ opt.postName }}</span>
          </el-option>
        </el-select>
      </div>
    </div>

    <div class="line_wrap flex_row_betweens">
      <span>组织层级：</span>
      <div>
        <el-select v-model="orgLevelCode" clearable placeholder="请选择">
          <el-option
            v-for="opt in orgLevelOptions"
            :key="opt.dictCode"
            :label="opt.codeName"
            :value="opt.dictCode"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>组织名称：</span>
      <div>
        <el-input v-model="orgName"></el-input>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens" v-if="!popupTitleSign">
      <span>是否启用：</span>
      <div>
        <el-switch
          v-model="switchStatus"
          :disabled="disabledRstatus"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="是"
          inactive-text="否"
        >
        </el-switch>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>组织简称：</span>
      <div>
        <el-input v-model="orgShortName"></el-input>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span><span class="required_fields_icon">&lowast;</span> 组织外部编码：</span>
      <div>
        <el-input v-model="orgCodeExtn" maxlength="32"></el-input>
      </div>
    </div>
    <div slot="footer" class="dialog-footer align_right">
      <el-button class="page_clear_btn" @click="cancal">取 消</el-button>
      <el-button class="page_add_btn" type="primary" @click="submitBtn">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  getOrgDeptTree,
  getOrgLevelList,
  getOrgStaffList,
  principal,
  createOrgDept,
  updateOrgDept,
  getOrgDeptInfo,
  getDomainList
} from '../../request/api'

export default {
  name: 'orgDeptTreePopUp',
  props: {
    show: {
      type: Boolean,
      dafault: false
    },
    //popupTitleSign 为true时新增组织，非true时修改组织 ，新增/修改组织的唯一标识
    popupTitleSign: Boolean,
    checkedId: String, //组织树带过来的orgCode
    tebleEditId: String, //组织列表带过来的 orgCode
    isDeleteSign: Boolean
  },
  data() {
    return {
      cascaderKey: 1,
      dialogVisible: this.show,
      treeData: [],
      rStatus: '',
      switchStatus: true,
      disabledRstatus: false,
      orgLevelOptions: [],
      likeName: '',
      staffOptions: [],
      //接口所需参数
      bizDomainCode: '',
      // companyId:this.$store.state.userInfo.companyId,
      isEntity: '',
      layerNo: '',
      orgCode: '',
      orgLeaderId: '',
      orgLeaderName: '',
      orgLevelCode: '',
      orgName: '',
      orgShortName: '',
      parentOrgCode: '',
      checkParentOrgCode: '',
      orgCodeExtn: '',

      isEntityOptions: [
        {
          codeName: '是',
          dictCode: 'Y'
        },
        {
          codeName: '否',
          dictCode: 'N'
        }
      ],
      bizDomainOptions: [],
      request: {},
      loading: false
    }
  },
  created() {},
  mounted() {
    // console.log(this.popupTitleSign)
    // this.getCandidateFun()
  },
  methods: {
    // 如果是新增组织 需要数据清空
    clearFormData() {
      // console.log(isCreateOrg)
      this.bizDomainCode = ''
      this.isEntity = ''
      this.layerNo = ''
      this.orgCode = ''
      this.orgLeaderId = ''
      this.orgLeaderName = ''
      this.orgLevelCode = ''
      this.orgName = ''
      this.orgShortName = ''
      this.parentOrgCode = ''
      this.orgCodeExtn = ''
    },

    // 父级组织树
    getOrgTreeFun() {
      getOrgDeptTree({
        companyId: this.companyId
      }).then(res => {
        if (res.length > 0) {
          this.treeData = res
        } else {
          this.treeData = []
        }
      })
    },
    // 勾选父级组织树 选中节点变化时触发
    handleItemChange(val) {
      // console.log('勾选的父级组织', val)
      if (val) {
        // this.parentOrgCode = val[val.length-1]
        this.checkParentOrgCode = val[val.length - 1]
      } else {
        // this.parentOrgCode = ''
        this.checkParentOrgCode = ''
      }
    },
    // // 部门负责人下拉框
    // getOrgStaffListFun() {
    //     getOrgStaffList({
    //         companyId: this.companyId,
    //         likeName: this.likeName,
    //         orgCode: this.orgCode,
    //     }).then(res => {
    //         // console.log(res)
    //         if (res.code == 200) {
    //             if (res.data.length > 0) {
    //                 this.staffOptions = res.data.map(item => {
    //                     return {
    //                         codeName: item.userName,
    //                         dictCode: item.userId,
    //                     }
    //                 })
    //             } else {
    //                 this.staffOptions = []
    //             }
    //         }
    //     })
    // },
    getCandidateFun(val) {
      if (val !== '') {
        this.loading = true
        principal({
          userName: val
        }).then(res => {
          this.loading = false
          if (res.code == 200) {
            if (res.data && res.data.length > 0) {
              this.staffOptions = res.data.map(item => {
                return {
                  codeName: item.userName,
                  dictCode: item.userId,
                  postName: item.postName,
                  orgName: item.orgName
                }
              })
            } else {
              this.staffOptions = []
            }
          }
        })
      } else {
        this.staffOptions = []
      }
    },
    candidateChange(data) {
      this.orgLeaderId = data
    },
    // 组织职层下拉框
    getOrgLevelListFun() {
      getOrgLevelList({
        companyId: this.companyId,
        rStatus: this.rStatus
      }).then(res => {
        // console.log(res)
        if (res.data.length > 0) {
          this.orgLevelOptions = res.data.map(item => {
            return {
              dictCode: item.orgLevelCode,
              codeName: item.orgLevelName
            }
          })
        } else {
          this.orgLevelOptions = []
        }
      })
    },
    // 业务领域下拉框
    getDomainListFun() {
      getDomainList({
        companyId: this.companyId,
        rStatus: this.rStatus
      }).then(res => {
        // console.log(res)
        if (res.code == 200) {
          if (res.data.length > 0) {
            this.bizDomainOptions = res.data.map(item => {
              return {
                dictCode: item.bizDomainCode,
                codeName: item.bizDomainName
              }
            })
          } else {
            this.bizDomainOptions = []
          }
        } else {
          this.bizDomainOptions = []
        }
      })
    },
    // 弹窗取消按钮
    cancal() {
      this.dialogVisible = false
    },
    // 弹窗确认按钮  即新增/修改组织
    submitBtn() {
      if (this.popupTitleSign) {
        this.createOrgDeptFun()
      } else {
        this.updateOrgDeptFun()
      }
    },
    // 创建组织
    createOrgDeptFun() {
      if (!this.isEntity) {
        this.$msg.warning('请选择是否是实体')
        return
      } else if (!this.orgName) {
        this.$msg.warning('请填写组织名称')
        return
      } else if (!this.orgCodeExtn) {
        this.$msg.warning('组织外部编码不能为空')
        return
      }
      this.request = {
        bizDomainCode: this.bizDomainCode,
        companyId: this.companyId,
        isEntity: this.isEntity,
        orgLeaderId: this.orgLeaderId,
        orgLevelCode: this.orgLevelCode,
        orgName: this.orgName,
        orgShortName: this.orgShortName,
        parentOrgCode: this.checkParentOrgCode,
        orgCodeExtn: this.orgCodeExtn
      }
      createOrgDept(this.request).then(res => {
        // console.log(res)
        if (res.code == 200) {
          //更新成功
          //1、 需要更新弹窗父级组织树
          //2、 需要父组件更新组织树，组织列表
          //3、 需要关闭弹窗
          //4、 需要给更新成功提示
          this.getOrgTreeFun()
          this.$emit('isUpdateSign', true)
          this.dialogVisible = false
          this.$msg.success(res.msg)
        } else {
          this.$emit('isUpdateSign', false)
          this.$msg.error(res.msg)
        }
      })
    },
    // 修改组织
    updateOrgDeptFun() {
      if (!this.isEntity) {
        this.$msg.warning('请选择是否是实体')
        return
      } else if (!this.orgName) {
        this.$msg.warning('请填写组织名称')
        return
      } else if (!this.orgCodeExtn) {
        this.$msg.warning('组织外部编码不能为空')
        return
      }
      this.request = {
        bizDomainCode: this.bizDomainCode,
        companyId: this.companyId,
        isEntity: this.isEntity,
        layerNo: this.layerNo,
        orgCode: this.orgCode,
        orgLeaderId: this.orgLeaderId,
        orgLeaderName: this.orgLeaderName,
        orgLevelCode: this.orgLevelCode,
        orgName: this.orgName,
        orgShortName: this.orgShortName,
        parentOrgCode: this.checkParentOrgCode,
        orgCodeExtn: this.orgCodeExtn,
        rstatus: this.switchStatus ? 'Y' : 'N'
      }
      // console.log(this.request)
      updateOrgDept(this.request).then(res => {
        // console.log(res)
        if (res.code == 200) {
          //更新成功
          //1、 需要更新弹窗父级组织树
          //2、 需要父组件更新组织树，组织列表
          //3、 需要关闭弹窗
          //4、 需要给更新成功提示
          this.getOrgTreeFun()
          this.$emit('isUpdateSign', true)
          this.dialogVisible = false
          this.$msg.success(res.msg)
          this.staffOptions = []
        } else {
          this.$emit('isUpdateSign', false)
          this.$msg.error(res.msg)
        }
      })
    },
    // 获取组织部门详情
    getOrgDeptInfoFun() {
      getOrgDeptInfo({
        companyId: this.companyId,
        orgCode: this.orgCode
      }).then(res => {
        // console.log(res)
        if (res.code == 200) {
          this.bizDomainCode = res.data.bizDomainCode
          this.isEntity = res.data.isEntity
          this.layerNo = res.data.layerNo
          this.orgCode = res.data.orgCode
          this.orgLeaderId = res.data.orgLeaderId
          this.orgLeaderName = res.data.orgLeaderName
          this.orgLevelCode = res.data.orgLevelCode
          this.orgName = res.data.orgName
          this.orgShortName = res.data.orgShortName
          this.parentOrgCode = res.data.parentOrgCode
          this.checkParentOrgCode = res.data.parentOrgCode
          this.orgCodeExtn = res.data.orgCodeExtn
          this.disabledRstatus = res.data.disable
          this.switchStatus = res.data.rstatus == 'Y' ? true : false
          this.disabledNodesChecked(this.treeData, this.orgCode)
        } else {
          this.clearFormData()
        }
      })
    },
    disabledNodesChecked(node, disabledNode) {
      if (node.length > 0) {
        node.map(item => {
          if (item.code == disabledNode) {
            item.disabled = true
            return
          }
          this.disabledNodesChecked(item.children, disabledNode)
        })
      } else {
        return
      }
    }
  },
  watch: {
    show(val) {
      // val 为 ture 表示弹窗打开 非true表示弹窗关闭
      // popupTitleSign 为 ture 表示新增组织 非true表示修改组织
      this.dialogVisible = this.show
      // console.log('是否打开弹窗',val)
      // console.log('是否新增组织标识',this.popupTitleSign)
      // console.log('从组织树修改即勾选的组织id',this.checkedId)
      // console.log('从组织列表修改',this.tebleEditId)
      if (val) {
        if (this.popupTitleSign) {
          // 新增组织
          // 清空表单数据
          this.clearFormData()
        } else {
          // 修改组织

          if (this.tebleEditId) {
            //从组织列表修改
            this.orgCode = this.tebleEditId
          } else {
            // 从组织树修改
            this.orgCode = this.checkedId
          }
          // 获取要修改组织详情 获取部门负责人下拉框
          this.getOrgDeptInfoFun()
          // this.getOrgStaffListFun()
          // this.getCandidateFun()
        }
      }
    },

    isDeleteSign(val) {
      if (val) {
        ++this.cascaderKey
        this.getOrgTreeFun()
      }
    },
    companyId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getOrgTreeFun()
          this.getOrgLevelListFun()
          // this.getOrgStaffListFun()
          this.getDomainListFun()
        }
      }
    }
  },
  computed: {
    companyId() {
      return this.$store.state.userInfo.companyId
    }
  }
}
</script>
<style scoped lang="scss">
.dialog-footer {
  padding-bottom: 20px;
}
.el-dialog--center .el-dialog__body {
  padding: 20px 20px 0;
}
.line_wrap {
  margin: 0 0 10px 0;
  line-height: 40px;

  span {
    padding: 0 10px 0 0;
    width: 125px;
    text-align: right;

    .required_fields_icon {
      padding: 0;
      color: #f56c6c;
    }
  }

  div {
    flex: 1;
  }

  .el-cascader,
  .el-select {
    width: 100%;

    .el-input--suffix {
      width: 100%;
    }
  }
}
</style>
