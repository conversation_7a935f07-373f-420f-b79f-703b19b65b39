<template>
  <el-dialog
    :title="popupTitleSign ? '新增组织' : '修改组织'"
    v-model="dialogVisible"
    @close="emit('update:show', false)"
    width="40%"
    center
  >
    <div class="line_wrap flex_row_betweens">
      <span>上级组织：</span>
      <div>
        <el-cascader
          :options="treeData"
          v-model="parentOrgCode"
          :placeholder="popupTitleSign ? '请选择' : parentOrgCode ? '' : '尚无上级组织'"
          :props="{
            label: 'name',
            value: 'code',
            expandTrigger: 'hover',
            checkStrictly: true
          }"
          @change="handleItemChange"
          :disabled="popupTitleSign == false ? true : false"
          :key="cascaderKey"
          clearable
        >
        </el-cascader>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>业务领域：</span>
      <div>
        <el-select v-model="bizDomainCode" placeholder="请选择" clearable>
          <el-option
            v-for="opt in bizDomainOptions"
            :key="opt.dictCode"
            :label="opt.codeName"
            :value="opt.dictCode"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span><span class="required_fields_icon">&lowast;</span> 是否实体：</span>
      <div>
        <el-select v-model="isEntity" clearable placeholder="请选择">
          <el-option
            v-for="opt in isEntityOptions"
            :key="opt.dictCode"
            :label="opt.codeName"
            :value="opt.dictCode"
          ></el-option>
        </el-select>
      </div>
    </div>
    <!-- <div class="line_wrap flex_row_betweens" v-if="!popupTitleSign">
            <span>层级：</span>
            <div>
                <el-input type='number' v-model.number="layerNo" min='0'></el-input>
            </div>
        </div> -->
    <!-- 新增组织 不选部门负责人 修改组织才能选部门负责人 -->
    <div class="line_wrap flex_row_betweens" v-if="!popupTitleSign">
      <span>组织负责人：</span>
      <div>
        <el-select
          v-model="orgLeaderName"
          filterable
          remote
          :remote-method="getCandidateFun"
          @change="candidateChange"
          :loading="loading"
          clearable
          placeholder="请输入人员名称进行查询"
        >
          <el-option v-for="opt in staffOptions" :key="opt.dictCode" :label="opt.codeName" :value="opt.dictCode">
            <span class="options_item">{{ opt.codeName }}</span
            >-- <span class="options_item">{{ opt.orgName }}</span
            >--
            <span class="options_item">{{ opt.postName }}</span>
          </el-option>
        </el-select>
      </div>
    </div>

    <div class="line_wrap flex_row_betweens">
      <span>组织层级：</span>
      <div>
        <el-select v-model="orgLevelCode" clearable placeholder="请选择">
          <el-option
            v-for="opt in orgLevelOptions"
            :key="opt.dictCode"
            :label="opt.codeName"
            :value="opt.dictCode"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>组织名称：</span>
      <div>
        <el-input v-model="orgName"></el-input>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens" v-if="!popupTitleSign">
      <span>是否启用：</span>
      <div>
        <el-switch
          v-model="switchStatus"
          :disabled="disabledRstatus"
          active-color="#13ce66"
          inactive-color="#ff4949"
          active-text="是"
          inactive-text="否"
        >
        </el-switch>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>组织简称：</span>
      <div>
        <el-input v-model="orgShortName"></el-input>
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span><span class="required_fields_icon">&lowast;</span> 组织外部编码：</span>
      <div>
        <el-input v-model="orgCodeExtn" maxlength="32"></el-input>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer align_right">
        <el-button class="page_clear_btn" @click="cancal">取 消</el-button>
        <el-button class="page_add_btn" type="primary" @click="submitBtn">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import {
  getOrgDeptTree,
  getOrgLevelList,
  getOrgStaffList,
  principal,
  createOrgDept,
  updateOrgDept,
  getOrgDeptInfo,
  getDomainList
} from '../../request/api'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  //popupTitleSign 为true时新增组织，非true时修改组织 ，新增/修改组织的唯一标识
  popupTitleSign: Boolean,
  checkedId: String, //组织树带过来的orgCode
  tebleEditId: String, //组织列表带过来的 orgCode
  isDeleteSign: Boolean
})

const emit = defineEmits(['update:show', 'isUpdateSign'])

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

// 响应式数据
const cascaderKey = ref(1)
const dialogVisible = ref(props.show)
const treeData = ref([])
const rStatus = ref('')
const switchStatus = ref(true)
const disabledRstatus = ref(false)
const orgLevelOptions = ref([])
const likeName = ref('')
const staffOptions = ref([])

// 接口所需参数
const bizDomainCode = ref('')
const isEntity = ref('')
const layerNo = ref('')
const orgCode = ref('')
const orgLeaderId = ref('')
const orgLeaderName = ref('')
const orgLevelCode = ref('')
const orgName = ref('')
const orgShortName = ref('')
const parentOrgCode = ref('')
const checkParentOrgCode = ref('')
const orgCodeExtn = ref('')

const isEntityOptions = ref([
  {
    codeName: '是',
    dictCode: 'Y'
  },
  {
    codeName: '否',
    dictCode: 'N'
  }
])
const bizDomainOptions = ref([])
const request = ref({})
const loading = ref(false)

// 方法定义
// 如果是新增组织 需要数据清空
const clearFormData = () => {
  bizDomainCode.value = ''
  isEntity.value = ''
  layerNo.value = ''
  orgCode.value = ''
  orgLeaderId.value = ''
  orgLeaderName.value = ''
  orgLevelCode.value = ''
  orgName.value = ''
  orgShortName.value = ''
  parentOrgCode.value = ''
  orgCodeExtn.value = ''
}

// 父级组织树
const getOrgTreeFun = () => {
  getOrgDeptTree({
    companyId: companyId.value
  }).then(res => {
    console.log('res', res)

    if (res.data.length > 0) {
      treeData.value = res.data
    } else {
      treeData.value = []
    }
  })
}

// 勾选父级组织树 选中节点变化时触发
const handleItemChange = val => {
  // console.log('勾选的父级组织', val)
  if (val) {
    // parentOrgCode.value = val[val.length-1]
    checkParentOrgCode.value = val[val.length - 1]
  } else {
    // parentOrgCode.value = ''
    checkParentOrgCode.value = ''
  }
}

// 获取候选人
const getCandidateFun = val => {
  if (val !== '') {
    loading.value = true
    principal({
      userName: val
    }).then(res => {
      loading.value = false
      if (res.code == 200) {
        if (res.data && res.data.length > 0) {
          staffOptions.value = res.data.map(item => {
            return {
              codeName: item.userName,
              dictCode: item.userId,
              postName: item.postName,
              orgName: item.orgName
            }
          })
        } else {
          staffOptions.value = []
        }
      }
    })
  } else {
    staffOptions.value = []
  }
}

const candidateChange = data => {
  orgLeaderId.value = data
}

// 组织职层下拉框
const getOrgLevelListFun = () => {
  getOrgLevelList({
    companyId: companyId.value,
    rStatus: rStatus.value
  }).then(res => {
    // console.log(res)
    if (res.data.length > 0) {
      orgLevelOptions.value = res.data.map(item => {
        return {
          dictCode: item.orgLevelCode,
          codeName: item.orgLevelName
        }
      })
    } else {
      orgLevelOptions.value = []
    }
  })
}

// 业务领域下拉框
const getDomainListFun = () => {
  getDomainList({
    companyId: companyId.value,
    rStatus: rStatus.value
  }).then(res => {
    // console.log(res)
    if (res.code == 200) {
      if (res.data.length > 0) {
        bizDomainOptions.value = res.data.map(item => {
          return {
            dictCode: item.bizDomainCode,
            codeName: item.bizDomainName
          }
        })
      } else {
        bizDomainOptions.value = []
      }
    } else {
      bizDomainOptions.value = []
    }
  })
}

// 弹窗取消按钮
const cancal = () => {
  dialogVisible.value = false
}

// 弹窗确认按钮  即新增/修改组织
const submitBtn = () => {
  if (props.popupTitleSign) {
    createOrgDeptFun()
  } else {
    updateOrgDeptFun()
  }
}

// 创建组织
const createOrgDeptFun = () => {
  if (!isEntity.value) {
    EleMessage.warning('请选择是否是实体')
    return
  } else if (!orgName.value) {
    EleMessage.warning('请填写组织名称')
    return
  } else if (!orgCodeExtn.value) {
    EleMessage.warning('组织外部编码不能为空')
    return
  }
  request.value = {
    bizDomainCode: bizDomainCode.value,
    companyId: companyId.value,
    isEntity: isEntity.value,
    orgLeaderId: orgLeaderId.value,
    orgLevelCode: orgLevelCode.value,
    orgName: orgName.value,
    orgShortName: orgShortName.value,
    parentOrgCode: checkParentOrgCode.value,
    orgCodeExtn: orgCodeExtn.value
  }
  createOrgDept(request.value).then(res => {
    // console.log(res)
    if (res.code == 200) {
      //更新成功
      //1、 需要更新弹窗父级组织树
      //2、 需要父组件更新组织树，组织列表
      //3、 需要关闭弹窗
      //4、 需要给更新成功提示
      getOrgTreeFun()
      emit('isUpdateSign', true)
      dialogVisible.value = false
      EleMessage.success(res.msg)
    } else {
      emit('isUpdateSign', false)
      EleMessage.error(res.msg)
    }
  })
}

// 修改组织
const updateOrgDeptFun = () => {
  if (!isEntity.value) {
    EleMessage.warning('请选择是否是实体')
    return
  } else if (!orgName.value) {
    EleMessage.warning('请填写组织名称')
    return
  } else if (!orgCodeExtn.value) {
    EleMessage.warning('组织外部编码不能为空')
    return
  }
  request.value = {
    bizDomainCode: bizDomainCode.value,
    companyId: companyId.value,
    isEntity: isEntity.value,
    layerNo: layerNo.value,
    orgCode: orgCode.value,
    orgLeaderId: orgLeaderId.value,
    orgLeaderName: orgLeaderName.value,
    orgLevelCode: orgLevelCode.value,
    orgName: orgName.value,
    orgShortName: orgShortName.value,
    parentOrgCode: checkParentOrgCode.value,
    orgCodeExtn: orgCodeExtn.value,
    rstatus: switchStatus.value ? 'Y' : 'N'
  }
  // console.log(request.value)
  updateOrgDept(request.value).then(res => {
    // console.log(res)
    if (res.code == 200) {
      //更新成功
      //1、 需要更新弹窗父级组织树
      //2、 需要父组件更新组织树，组织列表
      //3、 需要关闭弹窗
      //4、 需要给更新成功提示
      getOrgTreeFun()
      emit('isUpdateSign', true)
      dialogVisible.value = false
      EleMessage.success(res.msg)
      staffOptions.value = []
    } else {
      emit('isUpdateSign', false)
      EleMessage.error(res.msg)
    }
  })
}

// 获取组织部门详情
const getOrgDeptInfoFun = () => {
  getOrgDeptInfo({
    companyId: companyId.value,
    orgCode: orgCode.value
  }).then(res => {
    // console.log(res)
    if (res.code == 200) {
      bizDomainCode.value = res.data.bizDomainCode
      isEntity.value = res.data.isEntity
      layerNo.value = res.data.layerNo
      orgCode.value = res.data.orgCode
      orgLeaderId.value = res.data.orgLeaderId
      orgLeaderName.value = res.data.orgLeaderName
      orgLevelCode.value = res.data.orgLevelCode
      orgName.value = res.data.orgName
      orgShortName.value = res.data.orgShortName
      parentOrgCode.value = res.data.parentOrgCode
      checkParentOrgCode.value = res.data.parentOrgCode
      orgCodeExtn.value = res.data.orgCodeExtn
      disabledRstatus.value = res.data.disable
      switchStatus.value = res.data.rstatus == 'Y' ? true : false
      disabledNodesChecked(treeData.value, orgCode.value)
    } else {
      clearFormData()
    }
  })
}

const disabledNodesChecked = (node, disabledNode) => {
  if (node && node.length > 0) {
    node.map(item => {
      if (item.code == disabledNode) {
        item.disabled = true
        return
      }
      disabledNodesChecked(item.children, disabledNode)
    })
  } else {
    return
  }
}

// 监听器
watch(
  () => props.show,
  val => {
    // val 为 true 表示弹窗打开 非true表示弹窗关闭
    // popupTitleSign 为 true 表示新增组织 非true表示修改组织
    dialogVisible.value = props.show
    // console.log('是否打开弹窗',val)
    // console.log('是否新增组织标识',props.popupTitleSign)
    // console.log('从组织树修改即勾选的组织id',props.checkedId)
    // console.log('从组织列表修改',props.tebleEditId)
    if (val) {
      if (props.popupTitleSign) {
        // 新增组织
        // 清空表单数据
        clearFormData()
      } else {
        // 修改组织
        if (props.tebleEditId) {
          //从组织列表修改
          orgCode.value = props.tebleEditId
        } else {
          // 从组织树修改
          orgCode.value = props.checkedId
        }
        // 获取要修改组织详情 获取部门负责人下拉框
        getOrgDeptInfoFun()
        // getOrgStaffListFun()
        // getCandidateFun()
      }
    }
  }
)

watch(
  () => props.isDeleteSign,
  val => {
    if (val) {
      ++cascaderKey.value
      getOrgTreeFun()
    }
  }
)

watch(
  companyId,
  val => {
    if (val) {
      getOrgTreeFun()
      getOrgLevelListFun()
      // getOrgStaffListFun()
      getDomainListFun()
    }
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.dialog-footer {
  padding-bottom: 20px;
}
.el-dialog--center .el-dialog__body {
  padding: 20px 20px 0;
}
.line_wrap {
  margin: 0 0 10px 0;
  line-height: 40px;

  span {
    padding: 0 10px 0 0;
    width: 125px;
    text-align: right;

    .required_fields_icon {
      padding: 0;
      color: #f56c6c;
    }
  }

  div {
    flex: 1;
  }

  :deep(.el-cascader) {
    width: 100%;
  }
  .el-select {
    width: 100%;

    .el-input--suffix {
      width: 100%;
    }
  }
}
</style>
