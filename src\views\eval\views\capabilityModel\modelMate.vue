<template>
    <div class="model_mate_wrap bg_write">
        <div class="page_main_title clearfix">
            岗能建模
        </div>
        <div class="page_section">
            <div class="model_list_wrap">
                <table-component :tableData="tableData" :needIndex="needIndex" @handleSizeChange="handleSizeChange"
                                 @handleCurrentChange="handleCurrentChange">
                    <template v-slot:oper>
                        <el-table-column label="操作" width="100">
                            <template slot-scope="scope">
                                <el-button
                                        @click.native.prevent="mate(scope.$index, tableData.data)"
                                        link
                                        icon="el-icon-setting"
                                        class="icon_detail"
                                ></el-button>
                                <el-button
                                        v-if="tableData.data[scope.$index].lastBuildId"
                                        @click.native.prevent="progress(scope.$index, tableData.data)"
                                        icon="el-icon-s-operation"
                                        link
                                        
                                        class="icon_detail"
                                ></el-button>
                                <!--                                <el-button-->
                                <!--                                    @click.native.prevent="manage(scope.$index, tableData.data)"-->
                                <!--                                    type="primary"-->
                                <!--                                    -->
                                <!--                                >管理</el-button>-->
                            </template>
                        </el-table-column>
                    </template>
                </table-component>
            </div>
        </div>
    </div>
</template>

<script>
    import {getModelPageList} from "../../request/api"
    import tableComponent from "@/components/talent/tableComps/tableComponent";

    export default {
        name: "modelManage",
        components: {
            tableComponent
        },
        data() {
            return {
                needIndex: true,
                tableData: {
                    columns: [
                        {
                            label: "模型名称",
                            prop: "modelName"
                        },
                        {
                            label: "能力分类",
                            prop: "moduleCount",
                            formatterFun: (row, column, val) => {
                                return val + "类"
                            }
                        },
                        {
                            label: "能力词典",
                            prop: "itemCount",
                            formatterFun: (row, column, val) => {
                                return val + "类"
                            }
                        },
                        {
                            label: "匹配岗位",
                            prop: "objectCount",
                            formatterFun: (row, column, val) => {
                                return val + "个"
                            }
                        },
                        {
                            label: "最新建模时间",
                            prop: "lastBuildDate",
                            formatterFun: (row, column, val) => {
                                return val ? val.split(" ")[0] : "";
                            }
                        },
                        {
                            label: "状态",
                            prop: "buildStatus",
                            formatterFun: (row, column, val) => {
                                let status = "";
                                switch (val) {
                                    case "1":
                                        status = "未建模";
                                        break;
                                    case "2":
                                        status = "建模中";
                                        break;
                                    case "3":
                                        status = "待确认";
                                        break;
                                    case "4":
                                        status = "已确认";
                                        break;
                                    default:
                                        break;
                                }
                                return status;
                            }
                        }
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10
                    }
                },
            };
        },
        created() {
            window.sessionStorage.removeItem("checkedJobList");
            window.sessionStorage.removeItem("checkedPostList");
            window.sessionStorage.removeItem("checkedPersonList");
            this.getModelPageListFun();
        },
        methods: {
            mate(index, data) {
                let row = data[index]
                this.$router.push({
                    path: '/talentAssessment/creatModelMate',
                    query: {modelId: row.modelId}
                })
            },
            progress(index, data) {
                let row = data[index]
                this.$router.push({
                    path: '/talentAssessment/modelMateInfo',
                    query: {
                        buildId: row.lastBuildId
                    }
                })
            },
            manage() {

            },
            //pageSize 改变时会触发
            handleSizeChange(size) {
                console.log(size)
                this.tableData.page.current = 1;
                this.tableData.page.size = size;
                this.getModelPageListFun();
            },

            //currpage 改变时会触发
            handleCurrentChange(page) {
                this.tableData.page.current = page;
                this.getModelPageListFun();
            },
            getModelPageListFun() {
                getModelPageList({
                    current: this.tableData.page.current,
                    size: this.tableData.page.size,
                }).then(res => {
                    console.log(res)
                    if (res.code == 200) {
                        this.tableData.data = res.data;
                        this.tableData.page = res.page;
                    }
                })
            },

        }
    };
</script>

<style scoped lang="scss">
    .model_mate_wrap {

    }

</style>