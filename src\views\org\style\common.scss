.section_box_wrap {
  margin-bottom: 30px;
  padding: 16px 20px;
  line-height: 26px;
  background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #c6dbf3;
}
.dot_content_wrap {
  .item_wrap {
    margin-bottom: 12px;
    position: relative;

    .icon {
      position: absolute;
      top: 12px;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background: #000;
    }
    .title {
      margin: 0 10px 0 21px;
      color: #000;
      white-space: nowrap;
      font-weight: 600;
    }
  }
}
.icon_content_wrap{
.item_wrap {
  margin-bottom: 12px;
  position: relative;
  .icon {
    position: absolute;
    top: 8px;
    width: 11px;
    height: 11px;
    background: url('@/assets/imgs/indicator/icon_06.png') no-repeat center center;
    background-size: 100% 100%;
  }
  .title {
    margin: 0 10px 0 21px;
    color: #40a0ff;
    white-space: nowrap;
  }
}
}
.btn {
  padding: 0 18px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #fff;
  background: #40a0ff;
  border-radius: 4px 4px 4px 4px;
  cursor: pointer;
}

.tab_bottom_long_line_wrap {
  display: flex;
  justify-content: flex-start;
  border-bottom: 1px solid #d8d8d8;
  .item_wrap {
    padding: 0 42px;
    position: relative;
    height: 40px;
    line-height: 40px;
    color: #666666;
    cursor: pointer;
    .bot_line {
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      margin: 0 auto;
      // width: 30px;
      height: 3px;
      background: transparent;
      // border-radius: 2px 2px 2px 2px;
    }
  }

  .top_tab_act {
    color: #40a0ff;
    font-weight: 600;
    .bot_line {
      background: #40a0ff;
    }
  }
}
:deep .el-pagination {
  margin: 30px auto;
  display: flex;
  justify-content: flex-end;
  .el-pagination__sizes {
    .el-select__wrapper {
      background: transparent;
    }
  }
}
.overflow-elps{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text_b{
  font-weight: 600;
}
.fontSize14{
  font-size: 14px;
}
.marginT20{
  margin-top: 20px;
}
.marginB20{
  margin-bottom: 20px;
}
.marginR20{
  margin-right: 20px;
}
.marginT10{
  margin-top: 10px;
}
.marginT-10{
  margin-top: -10px;
}
.alignCenter{
  align-items: center;
}


