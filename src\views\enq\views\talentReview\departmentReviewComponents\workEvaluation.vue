<template>
  <div class="post_requirement_main">
    <div class="department_main clear marginT_8">
      <div class="page_second_title">工作评价</div>
      <div class="department_content marginT_8">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="岗位工作评价" name="first">
            <postWorkEvaluation :enqId="enqId"></postWorkEvaluation>
          </el-tab-pane>
          <el-tab-pane label="个人工作详情" name="second">
            <personnelWorkEvaluation :enqId="enqId"></personnelWorkEvaluation>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div class="marginT_30 align_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn" v-show="currentIndex != currentFirstCode"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { postJobNext } from '../../../request/api'
import postWorkEvaluation from './postWorkEvaluation'
import personnelWorkEvaluation from './personnelWorkEvaluation'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const activeName = ref('first')

const updateEnqPostDemandFun = stepType => {
  const params = {
    enqId: props.enqId
  }
  postJobNext(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      updateEnqPostDemandFun('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}

const nextBtn = () => {
  updateEnqPostDemandFun('nextStep')
}

const handleClick = () => {}
</script>

<style scoped lang="scss">
.department_main {
  .right_title {
    width: 182px;
  }
}

.el-table .cell {
  padding: 0 5px;
}

.el-input__inner {
  padding: 0 5px;
}
.el-table__row {
  height: 45px;
}
</style>
