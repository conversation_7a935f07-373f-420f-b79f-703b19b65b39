<template>
  <div class="talent_wrap_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content">
      <mapScatterPlot :width="800" :height="450" :chartData="mapData"></mapScatterPlot>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { regionDistribution } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'
import mapScatterPlot from '@/components/talent/echartsComps/scatterPlot/mapScatterPlot'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref([])

const mapData = ref([
  { name: '洛阳', value: 9 },
  { name: '秦皇岛', value: 12 },
  { name: '石家庄', value: 14 },
  { name: '莱芜', value: 15 },
  { name: '赤峰', value: 9 },
  { name: '常德', value: 20 },
  { name: '保定', value: 18 },
  { name: '合肥', value: 9 },
  { name: '菏泽', value: 14 },
  { name: '长沙', value: 21 },
  { name: '乌鲁木齐', value: 2 },
  { name: '西宁', value: 12 },
  { name: '银川', value: 21 },
  { name: '成都', value: 9 },
  { name: '绵阳', value: 23 },
  { name: '贵阳', value: 21 },
  { name: '昆明', value: 9 },
  { name: '海口', value: 18 },
  { name: '香港', value: 7 },
  { name: '武汉', value: 9 },
  { name: '沈阳', value: 21 },
  { name: '长春', value: 9 },
  { name: '哈尔滨', value: 1 }
])

const getRegionFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await regionDistribution(params)
    if (res.code == 200 && res.data.region.length > 0) {
      mapData.value = res.data.region
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  getRegionFun()
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  getRegionFun()
})
</script>

<style scoped lang="scss"></style>
