<template>
  <div class="data_upload">
    <div class="page_second_title">目标与结果数据</div>
    <div class="table_list">
      <div class="search_box">
        <!-- <div class="form_div">
          <div class="title">筛选</div>
          <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item label>
              <el-input
                placeholder="按岗位名称进行检索"
                suffix-icon="el-icon-search"
                v-model="formInline.postName"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label>
              <el-input
                v-model="formInline.userName"
                suffix-icon="el-icon-search"
                placeholder="按姓名进行检索"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" class="page_add_btn" @click="getEnqPostFun()">查询</el-button>
            </el-form-item>
          </el-form>
        </div>-->
        <div class="btn_box">
          <!-- <el-button type="primary" class="page_add_btn btn_color" @click="exportDownloadFun()">导出</el-button> -->
          <el-button type="primary" style="width: 110px" class="page_add_btn" @click="importStaffDialog = true"
            >下载模板并导入</el-button
          >
        </div>
      </div>
      <div>
        <tableComponent
          :selectionStatus="false"
          :height="'320'"
          :needIndex="true"
          :tableData="tableData"
          :size="'mini'"
          border
          :needPagination="true"
          @pageChange="pageChange"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></tableComponent>
      </div>
      <div class="btn_wrap align_center marginT_30">
        <el-button class="page_confirm_btn btn" type="primary" @click="prevStep">下一步</el-button>
      </div>
    </div>

    <!-- 导入弹窗 -->
    <el-dialog title="开始导入" v-model="importStaffDialog" width="800" :before-close="importStaffDialogBeforeClose">
      <div class="import_staff_wrap">
        <div class="import_staff_title">操作步骤:</div>
        <div class="oper_step">
          <p>1、下载《目标与结果模板》。</p>
          <p>2、打开下载表，维护相应的数据。</p>
          <p>3、信息输入完毕，点击"选择文件"按钮，选择excel文档。</p>
          <p>4、点击"开始导入",导入中如有任何疑问，请致电010-86482868。</p>
        </div>
        <div class="download_file">
          <span class="fs16 main_color pointer" @click="exportTemplate(1)">立即下载《目标与结果模板》</span>
          <span class="fs16 main_color pointer">为对照参评人员，</span>
          <span class="fs16 main_color pointer" @click="exportTemplate(2)">请下载《本次参与个人盘点人员清单》</span>
        </div>
        <div class="upload_file_wrap">
          <el-input placeholder="请输入内容" v-model="fileName" readonly>
            <template v-slot:append>
              <label for="up" class="upload_label">
                选择文件
                <!-- <el-button type="primary">选择文件</el-button> -->
                <input
                  id="up"
                  style="display: none"
                  ref="file"
                  type="file"
                  accept=".xlsx"
                  class="form-control page_clear_btn"
                  @change="fileChange"
                />
              </label>
            </template>
          </el-input>
        </div>
      </div>
      <template v-slot:footer>
        <div>
          <el-button class="page_clear_btn" @click="importStaffDialog = false">取 消</el-button>
          <el-button type="primary" class="page_add_btn" @click="importExcel">开始导入</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  getObjectivesResults,
  exportObjectivesResults,
  exportEnqUserInfo,
  importObjectivesResults
} from '../../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'

// Props
const props = defineProps({
  getEnqId: Function,
  isEdit: Boolean
})

// Emits
const emit = defineEmits(['nextStep'])

// Router
const router = useRouter()

// 响应式数据
const submitStatus = ref(true) //防止多次调用保存接口
const enqId = ref(null)
const tableData = reactive({
  columns: [
    {
      label: '员工编码',
      prop: 'employeeCode'
    },
    {
      label: '员工姓名',
      prop: 'userName'
    },
    {
      label: '所属部门',
      prop: 'orgName'
    },
    {
      label: '目标名称',
      prop: 'objectiveName'
    },
    {
      label: '目标权重',
      prop: 'objectiveWeight'
    },
    {
      label: '关键结果名称',
      prop: 'resultName'
    },
    {
      label: '关键结果权重',
      prop: 'resultWeight'
    }
  ],
  page: {
    total: 0,
    current: 1,
    size: 10
  },
  data: []
}) //全部
const formInline = reactive({
  userName: '',
  postName: ''
})
// 导入弹窗
const importStaffDialog = ref(false)
const fileName = ref('')
const uploadFile = ref(null)
const loading = ref(false)

// 方法
//导出模板
const exportTemplate = type => {
  if (type == 1) {
    exportObjectivesResults({
      enqId: enqId.value
    }).then(res => {
      // console.log(res)
      const blob = new Blob([res])
      const elink = document.createElement('a')
      elink.download = '目标与结果模板.xlsx'
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    })
  } else {
    exportEnqUserInfo({
      enqId: enqId.value
    }).then(res => {
      // console.log(res)
      const blob = new Blob([res])
      const elink = document.createElement('a')
      elink.download = '本次参与个人盘点人员清单.xlsx'
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    })
  }
}

//导入
const importStaffDialogBeforeClose = done => {
  fileName.value = ''
  document.getElementById('up').value = null
  done()
}

//导入数据
const fileChange = e => {
  // console.log(e)
  let formData = new FormData()
  let file = e.target.files[0]
  formData.append('file', file)
  formData.append('enqId', enqId.value)
  //把文件信息放入对象中
  fileName.value = file.name
  uploadFile.value = formData
}

const importExcel = () => {
  loading.value = true
  importObjectivesResults(uploadFile.value).then(res => {
    loading.value = false
    console.log(res)
    if (res.code == 200) {
      ElMessage.success('上传成功')
      importStaffDialog.value = false
      getEnqPostFun()
    } else {
      ElMessage.warning(res.msg)
    }
  })
}

//翻页
const handleCurrentChange = currentPage => {
  tableData.page.current = currentPage
  getEnqPostFun()
}

const handleSizeChange = size => {
  tableData.page.size = size
  getEnqPostFun()
}

const pageChange = (pageSize, currentPage) => {
  tableData.page.size = pageSize
  tableData.page.current = currentPage
  getEnqPostFun()
}

const goback = () => {
  router.push('/talentReviewHome/talentReview')
}

const prevStep = () => {
  emit('nextStep')
}

//查询
const getEnqPostFun = () => {
  getObjectivesResults({
    enqId: enqId.value,
    // userName: formInline.userName,
    // postName: formInline.postName,
    current: tableData.page.current,
    size: tableData.page.size
  }).then(res => {
    console.log(res)
    if (res.code == '200') {
      tableData.data = res.data
      tableData.page.total = res.total
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 生命周期
onMounted(() => {
  // 获取盘点id
  enqId.value = props.getEnqId()
  getEnqPostFun()
})
</script>

<style scoped lang="scss">
.table_list {
  //height: 400px;
  padding: 10px;

  position: relative;
  .search_box {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .form_div {
      display: flex;
      height: 40px;
      .title {
        margin-right: 10px;
      }
    }
    .btn_box {
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 99;
      .btn_color {
        background-color: #666;
        border-color: #666;
      }
    }
  }
  .btn_wrap {
    .btn {
      width: 180px;
    }
  }
}

// 导入
.import_staff_wrap {
  .import_staff_title {
    color: #515c71;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .oper_step {
    line-height: 28px;
    color: #515c71;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .download_file {
    display: block;
    margin-bottom: 16px;
    font-size: 12px;
  }

  .downBtn {
    color: #449cff;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
  }
  .upload_file_wrap {
    margin-bottom: 16px;

    .upload_label {
      display: block;
      height: 28px;
      line-height: 28px;
      width: 100%;
      cursor: pointer;
    }
  }
}
</style>
