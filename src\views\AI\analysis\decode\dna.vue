<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import SimplenessTable from '@/components/table/simplenessTable.vue'

defineOptions({ name: 'dna' })

const dnaColumns = ref([
  {
    label: '序号',
    type: 'index',
    width: '60px',
    align: 'center'
  },
  {
    label: '一级DNA',
    prop: 'name',
    width: '100px'
  },
  {
    label: '二级DNA',
    prop: 'name2',
    width: '110px'
  },
  {
    label: '表现',
    prop: 'value',
    width: '60px'
  },
  {
    label: '能力解码结果',
    prop: 'result'
  }
])
const dnaData = ref([
  {
    name: '流程赋能',
    name2: '流程端到端',
    value: '55',
    result:
      '流程断层风险：制定需求计划对象流程缺乏端到端闭环管理，各阶段衔接松散，导致需求跟踪不连贯，资源分配效率下降，订单转化率低于行业平均水平（如需求变更时无跨阶段追溯机制，丢单率增加）'
  },
  {
    name: '流程赋能',
    name2: '输入输出文档',
    value: '53',
    result: '文档流转低效：需求计划对象相关输入输出文档标准化不足，导致跨部门信息传递误差，计划执行偏差率达 15%+'
  },
  {
    name: '流程赋能',
    name2: '业务规则',
    value: '54',
    result:
      '规则模糊风险：需求计划对象业务规则不清晰，引发计划逻辑冲突，例如多渠道需求优先级混乱，导致库存周转率降低 8%+'
  },
  {
    name: '流程赋能',
    name2: '业务 KPI',
    value: '65',
    result: 'KPI 脱节问题：业务 KPI 与需求计划对象关联性弱，考核指标未聚焦核心目标，计划达成率波动幅度超过行业均值 10%'
  }
])
const tableRef = ref(null)
onMounted(() => {
  tableRef.value?.simplenessTableRef.setCurrentRow(dnaData.value[0])
})
const affectColumns = ref([
  {
    label: '影响维度',
    prop: 'name',
    width: '150px'
  },
  {
    label: '对管理的影响',
    prop: 'affect'
  },
  {
    label: '对指标的影响',
    prop: 'affectIndicator'
  },
  {
    label: '概率',
    prop: 'value',
    width: '50px'
  }
])
const affectData = ref([
  {
    name: '需求传导断层',
    affect:
      '流程端到端不连贯会导致需求从前端销售到后端计划的传递过程中出现失真、滞后或遗漏，各环节责任划分模糊，部门间协调成本激增，管理层难以快速定位流程中的阻塞点，导致需求计划与实际市场需求脱节，无法形成闭环管理，降低整体运营效率。',
    affectIndicator:
      '需求预测准确率较行业平均水平下降 10% 以上，需求计划与实际订单的偏差率超过 15%，库存周转率因需求传导延迟出现异常波动，紧急订单响应时间平均延长 2-3 天，导致客户交付准时率下降，影响企业供应链的敏捷性和市场竞争力。',
    value: '高'
  },
  {
    name: '流程协同低效',
    affect:
      '缺乏标准化的端到端流程会使需求计划制定过程中跨部门协作缺乏明确指引，销售、市场、计划、生产等部门各自为政，信息孤岛现象严重，管理层无法通过统一流程监控需求计划的全生命周期，导致决策滞后，难以应对市场快速变化的需求。',
    affectIndicator:
      '跨部门需求确认周期平均延长 3-5 个工作日，需求变更处理效率降低 40%，因流程协同问题导致的计划返工率上升至 25% 以上，需求计划的可执行性指数下降，进而引发库存积压或短缺，库存持有成本与缺货损失成本之和占销售额比例提高 2-3 个百分点。',
    value: '高'
  },
  {
    name: '责任界定模糊',
    affect:
      '端到端流程中各节点责任不清晰会导致需求计划出现问题时各部门相互推诿，管理层难以明确责任主体，无法有效追责和改进流程，长期形成 “踢皮球” 文化，削弱团队执行力，同时导致流程优化缺乏针对性，难以建立持续改进的管理机制。',
    affectIndicator:
      '需求计划问题追溯平均耗时超过 8 小时，责任认定争议导致的处理延迟事件发生率每月超过 5 起，流程改进提案采纳率不足 30%，需求计划质量投诉率上升，客户满意度调查中供应链响应速度维度得分下降 15-20 分，影响企业整体管理效能和客户口碑。',
    value: '高'
  },
  {
    name: '资源配置失衡',
    affect:
      '端到端流程不畅会导致企业在需求计划制定过程中无法根据市场优先级动态调配资源，高价值客户需求可能因资源不足得不到及时响应，而低优先级需求却占用过多资源，管理层难以通过流程优化实现资源的最优配置，导致供应链整体竞争力下降。',
    affectIndicator:
      '高价值客户需求满足率低于 70%，低优先级需求资源占用比例超过 40%，资源利用率较行业标杆低 12-15%，单位资源产出的销售额增长率同比下降 5-8 个百分点，因资源错配导致的机会成本损失占年度利润的 3-5%，严重影响企业的经济效益和市场份额拓展。',
    value: '中'
  },
  {
    name: '决策依据缺失',
    affect:
      '端到端流程中数据传递不完整、不及时会使管理层在制定需求计划时缺乏全面的市场、库存、生产等数据支撑，只能依赖经验或局部数据决策，导致计划偏离实际需求，无法有效应对市场波动和竞争挑战，增加供应链运营风险。',
    affectIndicator:
      '需求计划调整频率每月超过 4 次，因数据缺失导致的决策失误率超过 30%，市场需求捕捉滞后率达到 20%，计划赶不上变化的情况频发，库存周转率波动幅度较稳定流程企业大 25%，订单交付周期标准差增加 5 天，供应链弹性指数下降，抗风险能力显著减弱。',
    value: '低'
  }
])

const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex == 0) {
    if (row.name == '流程断裂点：阶段模糊与责任真空') {
      if (rowIndex % 3 == 0) {
        return {
          rowspan: 3,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    } else {
      if ((rowIndex + 3) % 2 == 0) {
        return {
          rowspan: 2,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    }
  }
}

const suggestColumns = ref([
  {
    label: '序号',
    type: 'index',
    width: '50px',
    align: 'center'
  },
  {
    label: '举措',
    prop: 'name',
    width: '120px'
  },
  {
    label: '关键行动',
    prop: 'action'
  },
  {
    label: '建议责任人',
    prop: 'personal',
    width: '120px'
  },
  {
    label: '输出成果',
    prop: 'result'
  },
  {
    label: '优先级',
    prop: 'value',
    width: '60px'
  }
])
const suggestData = ref([
  {
    name: '建立需求计划对象清单',
    action:
      '①制定《需求计划对象清单模板》，明确产品（如空调、冰箱等品类及型号）、市场（区域市场、渠道类型）、客户（B 端经销商、C 端消费者）等维度的具体分类及定义标准，确保无遗漏且边界清晰；②每月组织销售、市场、生产等跨部门需求评审会，对照清单检查计划对象完整性，重点验证新品类、新渠道、新客户群体的纳入情况，形成评审记录；③利用 ERP 系统日志自动记录需求对象的新增、删除操作，生成《完整性检查报告》，对遗漏场景触发红色预警，并指定责任部门在 48 小时内补充完善。',
    personal: '张建国（供应链计划部）',
    result: '《需求计划对象完整性清单》',
    value: '极高'
  },
  {
    name: '战略匹配度提升',
    action:
      '①制定《需求计划对象 - 战略目标对照表》，明确智能家电产品线、高端市场拓展等战略目标对应的需求对象，如将 “县域经销商” 纳入客户对象；②在年度战略会议上同步需求计划对象框架，由 CEO 牵头组织高层评审，确保对象定义与企业战略目标、市场定位高度匹配；③每季度通过平衡计分卡评估对象定义与当期战略的契合度，根据战略调整动态优化，如企业启动 “全渠道融合” 战略时，新增 “O2O 渠道客户” 作为独立需求对象。',
    personal: '张建国（供应链计划部）',
    result: '《战略匹配度评估报告》',
    value: '较高'
  },
  {
    name: '闭环验证机制建设',
    action:
      '①开发《需求计划对象效果评估模型》，自动对比计划对象覆盖范围内的实际销量、库存周转率与计划数据，计算对象定义导致的偏差率，标记无效或低效对象；②每月召开闭环验证会议，运用帕累托图分析 Top3 偏差案例，如因未纳入 “直播电商客户” 导致促销订单漏判等问题，形成改进提案；③针对验证发现的问题，启动对象定义优化流程，在 2 周内完成调整并更新预测模型参数，跟踪改进后 3 个月的订单满足率、库存周转率等指标变化。',
    personal: '张建国（供应链计划部）',
    result: '《闭环验证改进方案》',
    value: '高'
  },
  {
    name: '追溯体系建设',
    action:
      '①部署需求计划对象管理系统，自动记录每次定义 / 变更的时间、操作人、修改内容及审批意见，如将 “空调柜机” 调整为 “旺季重点产品” 的原因及供应链总监审批记录；②建立电子档案库存储历史版本的对象定义文档，支持按时间轴或关键词检索，便于复盘历史决策；③在 OA 流程中设置追溯性检查节点，要求任何对象调整必须关联具体业务事件（如新品上市通知）或数据依据（如连续两季某区域销量增长超 30%），否则无法提交审批。',
    personal: '张建国（供应链计划部）',
    result: '《追溯体系操作手册》',
    value: '中'
  },
  {
    name: '反馈机制建设',
    action:
      '①设立跨部门反馈收集小组，包含区域销售经理、客服主管、生产计划员等，每月通过问卷星、座谈会等形式收集一线反馈，重点关注对象定义的模糊点、遗漏项；②建立《需求对象优化优先级矩阵》，按影响程度排序处理反馈问题，如优先解决因 “工程客户” 对象定义模糊导致的批量订单漏排问题；③每季度发布《需求对象改进报告》，公示改进成果并明确下阶段优化方向，如新增 “商用空调工程客户” 对象后订单响应速度提升 40%。',
    personal: '张建国（供应链计划部）',
    result: '《反馈改进记录手册》',
    value: '中'
  }
])
</script>
<template>
  <div class="content-main">
    <div class="page-title-line">流程赋能—DNA解码结果</div>
    <SimplenessTable ref="tableRef" highlight-current-row :columns="dnaColumns" :data="dnaData">
      <template #oper="">
        <el-table-column label="" width="100">
          <template #default="">
            <el-button size="small" type="primary" plain>详情</el-button>
          </template>
        </el-table-column>
      </template>
    </SimplenessTable>
    <div class="page-title-line mt-7">
      <span>解码详情—对核心能力及指标的影响</span>
      <div class="choose-dna">（制定需求计划对象—流程端到端闭环）</div>
    </div>
    <SimplenessTable border :roundBorder="true" :columns="affectColumns" :data="affectData"></SimplenessTable>
    <div class="page-title-line mt-7">
      <span>解码详情-建议改善措施</span>
      <div class="choose-dna">（制定需求计划对象—流程赋能）</div>
    </div>
    <SimplenessTable border :roundBorder="true" :columns="suggestColumns" :data="suggestData"></SimplenessTable>
  </div>
</template>
<style lang="scss" scoped>
.page-title-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .choose-dna {
    font-size: 16px;
    color: #40a0ff;
  }
}
.aside {
  flex: 0 0 240px;
  .aside-title {
    font-weight: 600;
    font-size: 16px;
    color: #3d3d3d;
    line-height: 16px;
    margin-bottom: 18px;
  }
  .item {
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c7e1fa;
    padding: 16px;
    margin-bottom: 13px;
    cursor: pointer;
    &.active {
      border-color: #40a0ff;
      .item-score {
        display: none;
      }
      .item-chart {
        display: block;
      }
    }
    .item-title {
      font-weight: 600;
      font-size: 14px;
      color: #3d3d3d;
      line-height: 16px;
      margin-bottom: 10px;
    }
    .item-score {
      width: calc(100% - 20px);
      position: relative;
      background: #e9edf0;
      height: 12px;
      border-radius: 12px;
      .bar {
        height: 100%;
        top: 0;
        left: 0;
        background: #79d2fb;
        border-radius: 12px;
      }
      .score {
        position: absolute;
        left: calc(100% + 10px);
        top: 0;
        font-weight: 500;
        font-size: 16px;
        color: #40a0ff;
        margin-top: -6px;
      }
    }
    .item-chart {
      display: none;
      height: 200px;
    }
  }
  .org-item {
    background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #c6dbf3;
    font-size: 14px;
    color: #40a0ff;
    line-height: 35px;
    text-align: center;
    cursor: pointer;
  }
}
.content-main {
  flex: 1;
}
</style>
