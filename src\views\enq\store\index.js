import { defineStore } from 'pinia'

export const useEnqStore = defineStore('enq', {
  state: () => ({
    pageTitle: '人才数字化管理平台',
    createEnqId: null, // 发起盘点 创建盘点项目时保存的当前创建盘点的id，在启动盘点时要重置为null；（中途退出时不使用）
    userInfo: {},
    // 盘点报告列表视图
    reportViewListParams: {}
  }),

  actions: {
    setPageTitle(str) {
      this.pageTitle = str
    },

    setCreateEnqId(id) {
      this.createEnqId = id
    },

    setUserInfo(obj) {
      this.userInfo = obj
    },

    setParams(params) {
      this.reportViewListParams = { ...this.reportViewListParams, ...params }
    }
  }
})
