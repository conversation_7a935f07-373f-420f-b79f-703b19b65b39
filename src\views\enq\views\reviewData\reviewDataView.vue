<template>
  <div class="review_data_view_wrap">
    <div class="page_main_title flex_row_betweens">
      <div class="title flex_row_start">
        <p>盘点数据查看</p>
        <div class="check_title" v-if="enqName"><span>/</span>{{ enqName }}</div>
      </div>
      <div class="goback_geader" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section review_data_list_main">
      <div class="review_main_wrap">
        <div class="review_main_select">
          <div class="title page_second_title fs14">个人盘点数据</div>
          <div class="factor_item_wrap flex_row_wrap_start">
            <div
              class="factor_item"
              @click="selectEnqModule('personModule', index, 'p')"
              :class="{ disSelect: item.status == 'N', enqSelected: item.status == 'Y' }"
              v-for="(item, index) in personModule"
              :key="item.enqModuleCode"
            >
              <span class="overflow_elps" :title="item.enqModuleName">{{ item.enqModuleName }}</span>
            </div>
          </div>
        </div>
        <div class="review_main_select marginT_20">
          <div class="title page_second_title fs14">部门盘点数据</div>
          <div class="factor_item_wrap flex_row_wrap_start">
            <div
              class="factor_item"
              @click="selectEnqModule('deptModule', index, 'd')"
              :class="{ disSelect: item.status == 'N', enqSelected: item.status == 'Y' }"
              v-for="(item, index) in deptModule"
              :key="item.enqModuleCode"
            >
              <span class="overflow_elps" :title="item.enqModuleName">{{ item.enqModuleName }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getReviewDataModule, getCeEnqModuleById } from '../../request/api'

const route = useRoute()
const router = useRouter()

const enqId = route.query.enqId
const enqName = route.query.enqName

const personModulePath = [
  {
    enqModuleCode: 'PN10',
    enqModuleName: '目标与结果',
    path: '/talentReviewHome/reviewDataHome/rDataPerTargetResults'
  },
  {
    enqModuleCode: 'PN11',
    enqModuleName: 'KPI数据',
    path: '/talentReviewHome/reviewDataHome/rDataPerKpi'
  }
]

const deptModulePath = [
  {
    enqModuleCode: 'DN08',
    enqModuleName: '目标与结果',
    path: '/talentReviewHome/reviewDataHome/rDataDepTargetResults'
  },
  {
    enqModuleCode: 'DN09',
    enqModuleName: 'KPI数据',
    path: '/talentReviewHome/reviewDataHome/rDataDepKpi'
  }
]

const personModule = ref([])
const deptModule = ref([])

const getReviewDataModuleFun = async () => {
  try {
    const res = await getReviewDataModule({ id: enqId })
    if (res.code == 200) {
      deptModule.value = res.data.departEnqModule
      personModule.value = res.data.personalEnqModule
    }
  } catch (error) {
    console.error(error)
  }
}

const selectEnqModule = (typeData, index, type) => {
  let checkEnqModuleCode =
    typeData == 'personModule' ? personModule.value[index].enqModuleCode : deptModule.value[index].enqModuleCode
  if (type == 'p') {
    findTargetPath(personModulePath, checkEnqModuleCode)
  } else if (type == 'd') {
    findTargetPath(deptModulePath, checkEnqModuleCode)
  }
}

const findTargetPath = (pathList, enqModuleCode) => {
  for (let i = 0; i < pathList.length; i++) {
    if (pathList[i].enqModuleCode == enqModuleCode) {
      let curPath = pathList[i].path
      router.push({
        path: curPath,
        query: {
          enqId: enqId,
          enqName: enqName
        }
      })
    }
  }
}

onMounted(() => {
  getReviewDataModuleFun()
})
</script>

<style scoped lang="scss">
.review_data_view_wrap {
  .review_main_wrap {
    .review_main_title {
      margin-bottom: 8px;
      color: #606266;
    }

    .review_main_select {
      margin-bottom: 16px;

      .title {
        line-height: 34px;
        margin-bottom: 8px;
        border-bottom: 1px solid #0099fd;
      }

      .factor_item {
        position: relative;
        width: 150px;
        height: 34px;
        line-height: 34px;
        margin: 0 8px 8px 0;
        border: 1px solid #e5e5e5;
        border-radius: 2px;
        color: #525e6c;
        cursor: pointer;
        span {
          display: block;
          width: 150px;
          text-align: center;
        }
        &.disSelect {
          pointer-events: none;
        }
        &.enqSelected {
          color: #0099FF;
          border-color: #0099FF;
        }
        &.enqSelected:hover {
          background: #0099FF;
          color: #fff;
        }
      }
    }
  }
  .el-date-editor .el-range-separator {
    padding: 0;
  }

  .el-form-item {
    margin-bottom: 35px;
  }
}
</style>
