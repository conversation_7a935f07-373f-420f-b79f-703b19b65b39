<script setup>
const route = useRoute()
const router = useRouter()
const projectBMlist = ref([
  {
    title: '人效对标',
    date: '2025-03-06',
    typeList: [
      '经营效益与质量',
      '盈利能力与质量',
      '人力资源效率',
      '人力成本控制',
      '组织效率与人员结构',
      '可持续发展能力'
    ]
  },
  {
    title: '人效对标',
    date: '2025-03-06',
    typeList: [
      '经营效益与质量',
      '盈利能力与质量',
      '人力资源效率',
      '人力成本控制',
      '组织效率与人员结构',
      '可持续发展能力'
    ]
  }
])
const addProject = () => {
  router.push('/org/projectBMAdd')
}
</script>
<template>
  <div class="projectBM_wrap">
    <div class="top_t_wrap justify-between">
      <div class="page-title-line">对标项目信息</div>
      <div class="btn" @click="addProject">+新增对标项目</div>
    </div>
    <div class="projectBM_main">
      <div class="it_wrap justify-between" v-for="item in projectBMlist">
        <div class="l_w">
          <div class="title">{{ item.title }} ({{ item.date }})</div>
          <div class="t_l_w justify-start">
            <div class="type_tip" v-for="(it, index) in item.typeList" :class="{ act_type_tip: index > 2 }">
              {{ it }}
            </div>
          </div>
        </div>
        <div class="c_w">对标日期: {{ item.date }}</div>
        <div class="r_w">
          <div class="btn_wrap justify-end">
            <div class="btn">维护指标信息</div>
            <div class="btn">查看对标报告</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../style/common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-end {
  display: flex;
  justify-content: flex-end;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.projectBM_wrap {
  .top_t_wrap {
    margin-bottom: 10px;
    align-items: center;
    .page-title-line {
      margin: 3px 0 0 0;
    }
  }
  .projectBM_main {
    .it_wrap {
      margin-bottom: 20px;
      padding: 24px 20px;
      width: 100%;
      background: #ffffff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #c6dbf3;
      .l_w {
        flex: 1;
        .type_tip {
          margin: 16px 10px 0 0;
          padding: 0 15px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          color: #fff;
          background: #40a0ff;
          border-radius: 4px 4px 4px 4px;
          &.act_type_tip {
            color: #40a0ff;
            background: #e3f1ff;
          }
        }
      }
      .c_w {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 220px;
        text-align: center;
        border-left: 1px solid #d8d8d8;
        border-right: 1px solid #d8d8d8;
      }
      .r_w {
        width: 320px;
        display: flex;
        align-items: center;
        justify-content: center;
        .btn_wrap {
        }
        .btn {
          margin: 0 10px;
        }
      }
    }
  }
}
</style>
