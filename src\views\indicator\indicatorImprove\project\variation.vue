<template>
  <div class="variation public">
    <div class="table-main">
      <el-table ref="tableDataRef" :data="tableData" highlight-current-row style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="key1" label="指标名称" width="180" />
        <el-table-column prop="key2" label="目标类别" />
        <el-table-column prop="key3" label="指标单位" />
        <el-table-column prop="key4" label="责任人" />
        <el-table-column prop="key5" label="当期" />
        <el-table-column prop="key6" label="上期" />
        <el-table-column prop="key7" label="当期目标" />
        <el-table-column prop="key8" label="上期目标" />
        <el-table-column prop="key9" label="目标变化">
          <template #default="scope">
            <div
              :style="{
                color: scope.row.key9[0] == '+' ? '#40D476' : scope.row.key9[0] == '-' ? '#FF5E4C' : 'rgb(96, 98, 102)'
              }"
            >
              {{ scope.row.key9 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="key10" label="当前实际" />
        <el-table-column prop="key11" label="上期实际" />
        <el-table-column prop="key12" label="实际变化">
          <template #default="scope">
            <div
              :style="{
                color:
                  scope.row.key12[0] == '+' ? '#40D476' : scope.row.key12[0] == '-' ? '#FF5E4C' : 'rgb(96, 98, 102)'
              }"
            >
              {{ scope.row.key12 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column>
          <template #default="scope">
            <div
              class="btn"
              @click="
                openAi(
                  `${scope.row.key1}、${scope.row.key2}、当期${scope.row.key5}、上期${scope.row.key6}、当前目标${scope.row.key7}、上期目标${scope.row.key8}、目标变化${scope.row.key9}、当前实际${scope.row.key10}、上期实际${scope.row.key11}、实际变化${scope.row.key12}`
                )
              "
            >
              AI解读
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 已选择 -->
    <div class="active">
      <div class="title">
        <div class="text">已选指标：</div>
        <div class="name">库存周转天数</div>
      </div>
      <div class="page-title-line">关联能力变化</div>
      <el-table ref="tableDataRef2" highlight-current-row :data="table1" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="key1" label="所属一级能力" />
        <el-table-column prop="key2" label="二级能力" />
        <el-table-column prop="key3" align="center" label="相关性" />
        <el-table-column prop="key4" align="center" label="整体能力表现">
          <template #default="scope">
            <div
              class="num"
              :style="{
                background: scope.row.key4[0] == '+' ? '#40D476' : scope.row.key4[0] == '-' ? '#FF5E4C' : '#BCBCBC'
              }"
            >
              {{ scope.row.key4 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="key5" align="center" label="流程赋能变化">
          <template #default="scope">
            <div
              class="num"
              :style="{
                background: scope.row.key5[0] == '+' ? '#40D476' : scope.row.key5[0] == '-' ? '#FF5E4C' : '#BCBCBC'
              }"
            >
              {{ scope.row.key5 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="key6" align="center" label="组织赋能变化">
          <template #default="scope">
            <div
              class="num"
              :style="{
                background: scope.row.key6[0] == '+' ? '#40D476' : scope.row.key6[0] == '-' ? '#FF5E4C' : '#BCBCBC'
              }"
            >
              {{ scope.row.key6 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="key7" align="center" label="人岗赋能变化">
          <template #default="scope">
            <div
              class="num"
              :style="{
                background: scope.row.key7[0] == '+' ? '#40D476' : scope.row.key7[0] == '-' ? '#FF5E4C' : '#BCBCBC'
              }"
            >
              {{ scope.row.key7 }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="key8" align="center" label="信息赋能变化">
          <template #default="scope">
            <div
              class="num"
              :style="{
                background: scope.row.key8[0] == '+' ? '#40D476' : scope.row.key8[0] == '-' ? '#FF5E4C' : '#BCBCBC'
              }"
            >
              {{ scope.row.key8 }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="page-title-line">能力DNA变化（库存动态分析）</div>
      <div class="chart-box h-[300px]">
        <EChartsBar :options="chartsOptions"></EChartsBar>
      </div>
      <div class="page-title-line">能力DNA变化描述</div>
      <el-table :data="table3" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="60" />
        <el-table-column prop="key1" label="指标" />
        <el-table-column prop="key2" label="预警等级" />
        <el-table-column prop="key3" label="风险传导模拟" width="400" />
        <el-table-column prop="key4" align="center" label="上次评估" />
        <el-table-column prop="key5" align="center" label="本次评估" />
        <el-table-column prop="key6" label="主要变化" width="400" />
      </el-table>
    </div>
  </div>
</template>
<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
const openAi = inject('openAi')
const table3 = [
  {
    key1: '流程端到端闭环',
    key2: '闭环验证',
    key3: '建立月度库存策略复盘机制，对比实际库存周转天数与预测值（偏差＞15% 触发专项分析），输出改进方案并跟踪落地（30 日内完成策略调整）。',
    key4: '52',
    key5: '55',
    key6: '较上次提升 3 分，月度复盘机制落地更扎实，实际库存周转天数与预测值对比分析更精准，改进方案跟踪落地效率提高，策略调整周期缩短，闭环管理效果增强。'
  },
  {
    key1: '流程端到端闭环',
    key2: '反馈改进',
    key3: '每季度根据库存数据准确率（如＜90%）、策略执行偏差率（如补货计划完成率＜85%）优化流程，新增数据校验规则或跨部门协作节点，形成《流程改进记录单》。',
    key4: '51',
    key5: '56',
    key6: '提升 5 分，季度流程优化频率增加，依据数据准确率、策略执行偏差率等指标新增更多数据校验规则与跨部门协作节点，《流程改进记录单》应用更规范，问题响应速度加快。'
  },
  {
    key1: '流程端到端闭环',
    key2: '可追溯性',
    key3: '关键决策（如安全库存阈值调整、滞销品处理方案）可追溯至原始数据（如销售波动明细、供应链延迟记录），系统自动留存变更日志≥3 年，支持全链路流程回溯。',
    key4: '48',
    key5: '52',
    key6: '提升 4 分，关键决策原始数据追溯范围扩大，系统自动留存变更日志更完整，全链路流程回溯能力增强，数据记录保留时长达标，决策依据可查性显著提升。'
  },
  {
    key1: '流程端到端闭环',
    key2: '流程完整性',
    key3: '流程覆盖库存数据采集（实时 / 日 / 周）、需求预测、策略制定、执行跟踪、效果评估全环节，各环节输入输出标准明确（如采集模板包含 SKU / 库龄 / 周转率等 20 + 字段），无断点或逻辑断层。',
    key4: '53',
    key5: '56',
    key6: '提升 3 分，流程覆盖环节更全面，库存数据采集频率、字段标准等输入输出要求更明确，断点和逻辑断层减少，各环节衔接更顺畅，流程规范化程度提高。'
  },
  {
    key1: '流程端到端闭环',
    key2: '目标一致性',
    key3: '库存动态分析流程设计目标与企业供应链战略（如库存成本降低 15%、周转效率提升 20%）直接关联，每个关键环节（如数据采集 / 策略制定）设置目标对齐验证点（如季度战略匹配度评审）。',
    key4: '55',
    key5: '61',
    key6: '提升 6 分，流程设计与企业供应链战略关联更紧密，关键环节目标对齐验证点设置更合理，季度战略匹配度评审机制有效运行，战略落地支撑作用增强。'
  },
  {
    key1: '流程端到端闭环',
    key2: '完整性',
    key3: '分析报告包含库存周转天数（同比 / 环比）、安全库存偏差率（≤10%）、呆滞库存占比（≤8%）等核心指标，策略方案明确执行路径（如 ABC 分类实施步骤）及资源配置（如促销预算分配表），无关键信息缺失。',
    key4: '40',
    key5: '56',
    key6: '提升 16 分，显著改善，分析报告核心指标更齐全，策略方案执行路径、资源配置等内容从缺失到明确，关键信息完整度大幅提升，报告实用性和指导意义增强。'
  }
]
const tableData = [
  {
    key1: '供应商交付准时率',
    key2: '年度目标',
    key3: '',
    key4: '',
    key5: '2025年',
    key6: '2024年',
    key7: '98%',
    key8: '95%',
    key9: '+3%',
    key10: '95%',
    key11: '92%',
    key12: '+3%'
  },
  {
    key1: '库存周转天数',
    key2: '年度目标',
    key3: '',
    key4: '',
    key5: '2025年',
    key6: '2024年',
    key7: '80',
    key8: '85',
    key9: '-5',
    key10: '82',
    key11: '86',
    key12: '-4%'
  },
  {
    key1: '供应链协同指数',
    key2: '年度目标',
    key3: '',
    key4: '',
    key5: '2025年',
    key6: '2024年',
    key7: '85分',
    key8: '85分',
    key9: '无变化',
    key10: '82.5分',
    key11: '无变化',
    key12: '无变化'
  },
  {
    key1: '替代供应商储备率',
    key2: '月度目标',
    key3: '',
    key4: '',
    key5: '2025-05',
    key6: '2025-04',
    key7: '85%',
    key8: '80%',
    key9: '+5%',
    key10: '75%',
    key11: '77%',
    key12: '-2%'
  },
  {
    key1: '物流成本占比',
    key2: '月度目标',
    key3: '',
    key4: '',
    key5: '2025-05',
    key6: '2025-04',
    key7: '4.5%',
    key8: '4.5%',
    key9: '无变化',
    key10: '4.8%',
    key11: '无变化',
    key12: '无变化'
  }
]
const table1 = [
  {
    key1: '仓储物流管理',
    key2: '库存动态分析',
    key3: '高',
    key4: '+4',
    key5: '+1',
    key6: '+6',
    key7: '0',
    key8: '+1'
  },
  {
    key1: '供应链产销协同',
    key2: '需求预测与精准化管理',
    key3: '极高',
    key4: '+7',
    key5: '+7',
    key6: '+3',
    key7: '+6',
    key8: '+2'
  },
  {
    key1: '供应链产销协同',
    key2: 'CPFR（协同计划、预测与补货）',
    key3: '高',
    key4: '+1',
    key5: '+5',
    key6: '0',
    key7: '+5',
    key8: '-3'
  },
  {
    key1: '数字化制造',
    key2: '生产计划与柔性执行',
    key3: '中高',
    key4: '+3',
    key5: '+2',
    key6: '+3',
    key7: '+4',
    key8: '+3'
  },
  {
    key1: '仓储物流管理',
    key2: '智能仓储技术应用',
    key3: '高',
    key4: '+4',
    key5: '-5',
    key6: '+1',
    key7: '+3',
    key8: '-3'
  },
  {
    key1: '供应链产销协同',
    key2: '供应链计划集成',
    key3: '中高',
    key4: '+2',
    key5: '+7',
    key6: '+3',
    key7: '+3',
    key8: '+6'
  },
  {
    key1: '仓储物流管理',
    key2: '物流网络设计',
    key3: '中',
    key4: '+6',
    key5: '+2',
    key6: '+6',
    key7: '+1',
    key8: '+2'
  },
  {
    key1: '采购与供应商管理',
    key2: '采购执行与合同管理',
    key3: '中',
    key4: '+1',
    key5: '+7',
    key6: '+5',
    key7: '0',
    key8: '+2'
  },
  {
    key1: '供应链产销协同',
    key2: '全链路成本优化',
    key3: '高',
    key4: '+1',
    key5: '+4',
    key6: '+7',
    key7: '0',
    key8: '+4'
  },
  {
    key1: '仓储物流管理',
    key2: '物流数据分析与决策',
    key3: '中高',
    key4: '+7',
    key5: '+1',
    key6: '0',
    key7: '+3',
    key8: '-4'
  }
]

const chartData = ref([
  {
    name: '流程端到端闭环',
    value: '7'
  },
  {
    name: '输出文档',
    value: '2'
  },
  {
    name: '业务规则',
    value: '4'
  },
  {
    name: '业务 KPI',
    value: '1'
  },
  {
    name: '组织设置',
    value: '2'
  },
  {
    name: '岗位角色职责',
    value: '1'
  },
  {
    name: '岗位协同 RACI',
    value: '5'
  },
  {
    name: '组织岗位 KPI',
    value: '3'
  },
  {
    name: '人员动力',
    value: '4'
  },
  {
    name: '人员能力要求',
    value: '3'
  },
  {
    name: '人员能力评估',
    value: '2'
  },
  {
    name: '能力培训',
    value: '1'
  },
  {
    name: '系统赋能',
    value: '5'
  },
  {
    name: '数据治理',
    value: '5'
  },
  {
    name: '系统集成',
    value: '3'
  },
  {
    name: '系统改善及规划',
    value: '1'
  }
])
const tableDataRef = ref(null)
const tableDataRef2 = ref(null)
onMounted(() => {
  tableDataRef.value.setCurrentRow(tableData[1])
  tableDataRef2.value.setCurrentRow(table1[0])
})

const chartsOptions = ref({
  xAxisData: chartData.value.map(item => item.name),
  xAxis: {
    axisLabel: {
      interval: 0, // 强制显示所有标签
      rotate: 45, // 标签倾斜度 -90 至 90 默认为0
      margin: 10, // 刻度标签与轴线之间的距离
      fontSize: 12 // 刻度标签的文字大小
    }
  },
  series: [
    {
      type: 'bar',
      data: chartData.value.map(item => item.value),
      showBackground: true,
      itemStyle: {
        color: '#53B8FF'
      },
      label: {
        show: true
      }
    }
  ]
})
</script>
<style lang="scss" scoped>
@import '../indicatorImprove.scss';
</style>
