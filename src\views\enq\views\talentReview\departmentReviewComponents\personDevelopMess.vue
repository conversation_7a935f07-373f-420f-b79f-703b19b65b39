<template>
  <div class="person_dev_mess">
    <div class="person_Mess">
      <span class="name">{{ personnelDevData.userName }}</span>
      <span class="postion"
        >{{ personnelDevData.postName }}&nbsp;&nbsp;(本岗位工作年限：{{ personnelDevData.currentPostAgeName }})</span
      >
    </div>
    <div class="person_development">
      <div class="dev_left">
        <div class="left_top">
          <div>
            <div class="title">个人期望发展类型</div>
            <div class="target">{{ personnelDevData.developmentType }}</div>
          </div>
          <div>
            <div class="title">个人期望晋升职位</div>
            <div class="target">{{ personnelDevData.jobName }}</div>
          </div>
        </div>
        <div class="left_bottom">
          <div class="title">个人短板分析</div>
          <div class="target">{{ personnelDevData.analyzeSummarize }}</div>
        </div>
      </div>
      <div class="dev_left">
        <div class="left_top">
          <div>
            <div class="title">个人期望晋升周期</div>
            <div class="target">{{ personnelDevData.expectationCycle }}</div>
          </div>
        </div>
        <div class="left_bottom">
          <div class="title">个人发展计划</div>
          <div class="target">{{ personnelDevData.developmentPlan }}</div>
        </div>
      </div>
    </div>
    <div class="person_target">
      <div class="left_first">
        <div class="title">本岗位工作时长</div>
        <div class="target">
          <el-select class="item" v-model="personnelDevData.currentPostAge" placeholder="请选择">
            <el-option
              v-for="item in timeOptions"
              :key="item.dictCode"
              :label="item.codeName"
              :value="item.dictCode"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="left_first">
        <div class="title">最近晋升日期</div>
        <div class="target">
          <el-date-picker
            v-model="personnelDevData.lastPromotionDate"
            value-format="YYYY-MM-DD"
            type="date"
            placeholder="选择日期"
          ></el-date-picker>
        </div>
      </div>
    </div>
    <div class="person_analysis">
      <div class="dev_left">
        <div class="post_div">
          <div class="current_post">
            <div class="page_second_title">建议发展类型</div>
            <div class="type_wrap">
              <el-radio-group v-model="personnelDevData.superiorDevelopmentType" @change="devTypeChange">
                <el-radio v-for="(item, index) in developmentTypeOptions" :key="index" :label="item.dictCode" border>{{
                  item.codeName
                }}</el-radio>
              </el-radio-group>
            </div>
          </div>
          <div class="current_post">
            <div
              v-if="
                personnelDevData.superiorDevelopmentType == 1 ||
                personnelDevData.superiorDevelopmentType == 4 ||
                personnelDevData.superiorDevelopmentType == 5
              "
            >
              <div class="page_second_title">下一晋升职位</div>
              不需要选择晋升职位
            </div>
            <div class="goal_post" v-show="false">
              <div class="page_second_title">下一晋升职位</div>
              <div class="current_post_center marginT_16">
                <div class="item">
                  <div class="item_title">职位族群</div>
                  <div class="item_text">{{ goalPostInfo.parentJobClassName }}</div>
                </div>
                <div class="item">
                  <div class="item_title">职位序列</div>
                  <div class="item_text">{{ goalPostInfo.jobClassName }}</div>
                </div>
                <div class="item">
                  <div class="item_title">职位名称</div>
                  <div class="item_text">{{ goalPostInfo.jobName }}</div>
                </div>
                <div class="item">
                  <div class="item_title">职层</div>
                  <div class="item_text">{{ goalPostInfo.jobLevelName }}</div>
                </div>
                <div class="item">
                  <div class="item_title">职等</div>
                  <div class="item_text">{{ goalPostInfo.jobGradeName }}</div>
                </div>
              </div>
            </div>

            <div
              class="goal_post"
              v-show="personnelDevData.superiorDevelopmentType == 3 || personnelDevData.superiorDevelopmentType == 2"
            >
              <div class="page_second_title">建议目标职位</div>
              <div class="promotion_post box_flex clearfix">
                <div class="post_type">
                  <div class="title border_r_1">职族</div>
                  <div class="post_type_center border_r_1">
                    <div class="post_type_main">
                      <tree-comp-radio
                        :treeData="treeData"
                        :defaultExpandAll="false"
                        :nodeKey="'code'"
                        :defaultCheckedKeys="defaultCheckedKeys"
                        @clickCallback="treeCallback"
                      ></tree-comp-radio>
                    </div>
                  </div>
                </div>
                <div class="post_type">
                  <div class="title">职位名称</div>
                  <div class="post_type_center">
                    <div class="post_type_main">
                      <el-radio-group v-model="personnelDevData.superiorExpectationJob" size="small">
                        <el-radio class="post_item" v-for="item in postList" :key="item.jobCode" :label="item.jobCode">
                          {{ item.jobName }}({{ item.jobLevelName }})
                        </el-radio>
                      </el-radio-group>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dev_left">
        <div class="left_top">
          <div>
            <div class="title">预计周期</div>
            <div class="target">
              <el-select class="item" v-model="personnelDevData.superiorExpectationCycle" placeholder="请选择">
                <el-option
                  v-for="item in courseOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="left_bottom">
          <div class="title">个人短板分析</div>
          <div class="target">
            <el-input
              type="textarea"
              :rows="6"
              placeholder="请输入内容"
              v-model="personnelDevData.superiorAnalyzeSummarize"
            ></el-input>
          </div>
        </div>
        <div class="left_bottom">
          <div class="title">待提升能力</div>
          <div class="target">
            <el-input
              type="textarea"
              :rows="6"
              placeholder="请输入内容"
              v-model="personnelDevData.capacityToImproved"
            ></el-input>
          </div>
        </div>
      </div>
    </div>
    <div class="btn_wrap align_center marginT_30">
      <el-button class="page_confirm_btn" type="primary" @click="savePersonDev">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { getIdealJob, getPostData, getJobList, personnelDevelopmentConfirm } from '../../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import { formatterData } from '@/utils/utils'
import { useUserStore } from '@/stores/modules/user'
const userStore = useUserStore()
const props = defineProps({
  enqId: String,
  personnelDevData: Object
})

const emit = defineEmits(['getTab'])

const developmentTypeOptions = ref([])
const goalPostInfo = ref({})
const treeData = ref([])
const postList = ref([])
const hasParentPost = ref(true)
const courseOptions = ref([])
const timeOptions = ref([])
const defaultCheckedKeys = ref([])
const orgCode = ref('')

watch(
  () => props.personnelDevData,
  newVal => {
    defaultCheckedKeys.value = newVal.jobClassCode ? [newVal.jobClassCode] : []
  },
  { deep: true }
)

watch(
  () => props.personnelDevData.superiorDevelopmentType,
  val => {
    console.log(val)
    if (val == 3 || val == 2) {
      searchPostData()
    } else {
      getPostTypeFun()
    }
  }
)

onMounted(async () => {
  const res = await userStore.getDocList(['SUPERIOR_DEVELOPMENT_TYPE', 'EXPERIENCE_AGE', 'EXPECTATION_CYCLE'])

  developmentTypeOptions.value = res.SUPERIOR_DEVELOPMENT_TYPE
  courseOptions.value = res.EXPECTATION_CYCLE
  timeOptions.value = res.EXPERIENCE_AGE
})

const searchPostData = async () => {
  try {
    const res = await getPostData({})
    console.log(res)
    if (res.length > 0) {
      treeData.value = formatterData(res)
      if (
        (props.personnelDevData.superiorDevelopmentType == 3 || props.personnelDevData.superiorDevelopmentType == 2) &&
        props.personnelDevData.superiorExpectationJob
      ) {
        treeCallback(props.personnelDevData.superiorExpectationJob)
      }
    } else {
      treeData.value = []
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const treeCallback = async code => {
  console.log(code)
  orgCode.value = code
  console.log(props.personnelDevData.superiorDevelopmentType)
  if (props.personnelDevData.superiorDevelopmentType == 3 || props.personnelDevData.superiorDevelopmentType == 2) {
    await getPostListFun()
  }
}

const getPostListFun = async () => {
  try {
    const res = await getJobList({ jobClassCode: orgCode.value })
    console.log(res)
    if (res.code == 200) {
      postList.value = res.data
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const devTypeChange = val => {
  props.personnelDevData.superiorDevelopmentType = val
  if (val == '1') {
    props.personnelDevData.superiorExpectationJob = props.personnelDevData.postCode
  } else {
    getPostTypeFun()
  }
}

const getPostTypeFun = async () => {
  try {
    const res = await getIdealJob({
      enqId: props.enqId,
      developmentType: props.personnelDevData.superiorDevelopmentType,
      userId: props.personnelDevData.userId
    })

    console.log(res)
    if (res.code == 200) {
      const data = res.data
      if (data.idealJob) {
        goalPostInfo.value = data.idealJob
        props.personnelDevData.superiorExpectationJob = data.idealJob.jobCode
        console.log(props.personnelDevData.superiorExpectationJob)
      } else {
        props.personnelDevData.superiorExpectationJob = ''
      }
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const savePersonDev = async () => {
  if (
    !props.personnelDevData.currentPostAge ||
    !props.personnelDevData.superiorDevelopmentType ||
    !props.personnelDevData.superiorExpectationCycle ||
    !props.personnelDevData.superiorAnalyzeSummarize ||
    !props.personnelDevData.capacityToImproved
  ) {
    ElMessage.warning('请先填写人员发展数据!')
    return
  }

  if (
    (props.personnelDevData.superiorDevelopmentType == '3' || props.personnelDevData.superiorDevelopmentType == '2') &&
    !props.personnelDevData.superiorExpectationJob
  ) {
    ElMessage.warning('请先选择建议目标职位!')
    return
  }

  try {
    const res = await personnelDevelopmentConfirm({
      enqId: props.enqId,
      userId: props.personnelDevData.userId,
      developmentType: props.personnelDevData.superiorDevelopmentType,
      capacityToImproved: props.personnelDevData.capacityToImproved,
      currentPostAge: props.personnelDevData.currentPostAge,
      lastPromotionDate: props.personnelDevData.lastPromotionDate,
      superiorAnalyzeSummarize: props.personnelDevData.superiorAnalyzeSummarize,
      superiorDevelopmentType: props.personnelDevData.superiorDevelopmentType,
      superiorExpectationCycle: props.personnelDevData.superiorExpectationCycle,
      superiorExpectationJob:
        props.personnelDevData.superiorDevelopmentType == '3' || props.personnelDevData.superiorDevelopmentType == '2'
          ? props.personnelDevData.superiorExpectationJob
          : props.personnelDevData.expectationJob
    })

    if (res.code == 200) {
      ElMessage.success(res.msg)
      emit('getTab', true)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('保存失败')
  }
}
</script>

<style scoped lang="scss">
.person_dev_mess {
  .person_Mess {
    .name {
      color: #0099ff;
      font-weight: bold;
      font-size: 24px;
      margin-right: 15px;
    }
    .postion {
      color: #0099ff;
      font-size: 20px;
    }
  }
  .person_development {
    padding: 15px 0;
    display: flex;
    border-bottom: 1px solid #0099ff;
    div {
      flex: 1;
    }
    .dev_left {
      padding-right: 30px;
      .left_top {
        display: flex;
        padding: 10px 0;
        div {
          .title {
            margin-bottom: 5px;
            font-size: 14px;
            color: #0099ff;
          }
          .target {
            font-size: 14px;
            color: #bbb;
            min-height: 20px;
          }
        }
      }
      .left_bottom {
        margin-top: 15px;
        .title {
          margin-bottom: 5px;
          font-size: 14px;
          color: #0099ff;
        }
        .target {
          font-size: 14px;
          color: #bbb;
          min-height: 20px;
        }
      }
    }
  }
  .person_target {
    padding: 15px 0;
    border-bottom: 1px solid #0099ff;
    display: flex;
    .left_first {
      flex: 1;
      padding-right: 30px;
      .title {
        margin-bottom: 5px;
        font-size: 14px;
        color: #0099ff;
      }
      .target {
        padding-right: 15px;
        .el-select {
          width: 100%;
        }
        .el-date-editor.el-input,
        .el-date-editor.el-input__inner {
          width: 100% !important;
        }
      }
    }
  }
  .person_analysis {
    padding: 15px 0;
    display: flex;
    div {
      flex: 1;
    }
    .dev_left {
      padding-right: 30px;
      .post_div {
        display: flex;
        padding: 10px 0;
        .current_post {
          flex: 1;
          .page_second_title {
            font-size: 14px;
            color: #0099ff;
          }
          .current_post_center {
            width: 100%;
            border: 1px solid #e5e5e5;
            border-bottom: none;

            .item {
              width: 100%;
              display: flex;
              height: 40px;
              line-height: 40px;
              font-size: 12px;

              .item_title {
                width: 30%;
                text-align: center;
                background: #f4f4f4;
                border-bottom: 1px solid #e5e5e5;
              }

              .item_text {
                flex: 1;
                padding-left: 12px;
                border-bottom: 1px solid #e5e5e5;
                // white-space: nowrap;
              }
            }
          }

          .type_wrap {
            width: 160px;
            .el-radio-group {
              width: 100%;
              height: 35px;
              .el-radio {
                position: relative;
                width: 100%;
                height: 100%;
                margin: 0 0 15px 0;
                line-height: 35px;
                padding: 0;
                padding-left: 5px;
                border-color: #e5e5e5;
                &.is-checked {
                  border-color: #449cff;
                }
                .el-radio__input {
                  position: absolute;
                  width: 20px;
                  height: 20px;
                  right: 5px;
                  top: 6px;
                  border: 1px solid #e5e5e5;
                  border-radius: 50%;
                  overflow: hidden;
                  .el-radio__inner {
                    display: none;

                    &::after {
                      width: 0;
                      height: 0;
                    }
                  }
                  &.is-checked {
                    background: #0099ff;
                    .el-radio__inner {
                      display: block;
                      position: absolute;
                      width: 13px;
                      height: 7px;
                      left: 3px;
                      top: 4px;
                      border: 2px solid #fff;
                      border-radius: 0;
                      transform: rotate(-45deg);
                      border-top: none;
                      border-right: none;
                    }
                  }
                }
              }
              .el-radio {
                position: relative;
                width: 100%;
                height: 100%;
                margin: 0 0 15px 0;
                line-height: 35px;
                padding: 0;
                padding-left: 5px;
                border-color: #e5e5e5;

                &.is-checked {
                  border-color: #449cff;
                }
                .el-radio__input {
                  position: absolute;
                  width: 20px;
                  height: 20px;
                  right: 5px;
                  top: 6px;
                  border: 1px solid #e5e5e5;
                  border-radius: 50%;
                  overflow: hidden;

                  .el-radio__inner {
                    display: none;

                    &::after {
                      width: 0;
                      height: 0;
                    }
                  }

                  &.is-checked {
                    background: #0099ff;

                    .el-radio__inner {
                      display: block;
                      position: absolute;
                      width: 13px;
                      height: 7px;
                      left: 3px;
                      top: 4px;
                      border: 2px solid #fff;
                      border-radius: 0;
                      transform: rotate(-45deg);
                      border-top: none;
                      border-right: none;
                    }
                  }
                }
              }
            }
          }
          .box_flex {
            display: flex;
          }
          .post_type {
            border: 1px solid #e5e5e5;
            .title {
              background-color: #ebf4ff;
              height: 30px;
              line-height: 30px;
              padding-left: 10px;
              // font-size: 14px;
              color: #0099ff;
            }
          }
          .promotion_post {
            height: 400px;
            .post_type_center {
              height: calc(100% - 30px);
              overflow: auto;
              .el-radio-group {
                height: 100%;
                .el-radio {
                  height: 30px;
                  line-height: 30px;
                }
              }
            }
          }
          // .post_type_center {
          //   overflow: auto;
          // }
        }
      }
      .left_top {
        display: flex;
        padding: 10px 0;
        div {
          padding-right: 5px;
          .title {
            margin-bottom: 5px;
            font-size: 14px;
            color: #0099ff;
          }
          .target {
            font-size: 14px;
            color: #bbb;
            .el-select {
              width: 100%;
            }
          }
        }
      }
      .left_bottom {
        margin-top: 15px;
        padding-right: 15px;
        .title {
          margin-bottom: 5px;
          font-size: 14px;
          color: #0099ff;
        }
        .target {
          font-size: 14px;
          color: #bbb;
        }
      }
    }
  }
}
</style>
