<template>
    <div class="business_area_center clearfix">
        <div class="oper_btn_wrap marginB_16 align_right">
            <el-button class="page_add_btn" type="primary" size="mini" @click="addItem">新增</el-button>
        </div>
        <div class="edu_info_header flex_row_betweens">
            <div class="item desc">职责描述</div>
            <div class="item">时间占比(总和100%)</div>
            <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
            <div class="" v-if="dutyData.length > 0">
                <div class="edu_info_item flex_row_betweens"  v-for="(item, index) in dutyData">
                    <el-input class="item desc" v-model="item.respDesc" placeholder="请输入职责描述"></el-input>
                    <div class="time_percent_wrap">
                        <el-input class="item" type='number' v-model.number="item.timePercentage" min='0' placeholder="请选择时间占比"></el-input>
                        <span class="unit">%</span>
                    </div>
                    <div class="item item_icon_wrap">
                        <i class="item_icon el-icon-delete icon_del" @click="deleteItem(item.jobCode,item.jobRespId,index)"></i>
                    </div>
                </div>
            </div>
            <!-- <div class="" v-else >暂无数据</div> -->
            <!-- <div class="pagination_wrap">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page.current"
                    :page-sizes="[10,20, 50, 100, 200]"
                    :page-size="page.size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total"
                ></el-pagination>
            </div> -->
            <!-- <div class="tips">总比例>100%请修改</div> -->
            <div class="align_center paddT_12">
                <el-button class="page_confirm_btn" type="primary" @click="submit">保 存</el-button>
            </div>
        </div>
    </div>
</template>
 
<script>
import {jobRespInfo,deleteJobRespInfo,insertJobRespInfo} from '../../../../request/api'
export default {
    name: "duty",
    props: {
       jobCode:String, 
    },
    components: {
    },
    data() {
        return {
            dutyData: [],
        };
    },
    created(){
        this.getJobRespList()
    },
    mounted(){
    },
    computed:{
        companyId(){
            return this.$store.state.userInfo.companyId
        }
    },
    methods: {
        // 职责列表 数据回显
        getJobRespList(){
            jobRespInfo({
                jobCode:this.jobCode,
            }).then(res=>{
                console.log(res)
                if(res.code == 200){
                    if(res.data.length > 0){
                        this.dutyData = res.data
                    }else{
                        this.dutyData = [{
                            companyId:this.companyId,
                            jobCode:this.jobCode,
                            respDesc: "",
                            timePercentage: '',
                            jobRespId:'',
                            sortNbr:''
                        }]
                    }
                }else{
                    this.dutyData = []
                }
            })
        },
        
        addItem() {
            let addObj = {
                companyId:this.companyId,
                jobCode:this.jobCode,
                respDesc: "",
                timePercentage: '',
                jobRespId:'',
                sortNbr:''
            }
            if(this.dutyData.length > 0){
                let obj = this.dutyData[this.dutyData.length - 1];
                if(!obj.respDesc || !obj.timePercentage){
                    this.$msg.warning('请完善当前信息后新增！')
                }else{
                    this.dutyData.push(addObj); 
                }
            }else{
                this.dutyData.push(addObj); 
            }
        },

        createJobRespFun(){
            let respRequestList = this.dutyData
            insertJobRespInfo(
                respRequestList
            ).then(res=>{
                if(res.code == 200){
                    // this.getJobRespList()
                    this.$emit('submitSuccessTab','duty')
                    this.$msg.success(res.msg)
                }else{
                    this.$msg.error(res.msg)
                }
            })
        },
        submit(){
            if(this.dutyData.length > 0){
                let obj = this.dutyData[this.dutyData.length - 1];
                if(!obj.respDesc || !obj.timePercentage){
                    this.$msg.warning('请完善信息后提交！')
                }else{
                    let timePercentTotal = 0
                    for(let i=0; i < this.dutyData.length;i++){
                        timePercentTotal = timePercentTotal + this.dutyData[i].timePercentage
                    }
                    if(timePercentTotal > 100){
                        this.$msg.warning('时间占比总和不能大于100%！')
                        return;
                    }
                    this.createJobRespFun()
                }
            }else{
                this.$msg.warning('请新增后提交！')
            }
        },

        deleteItem(jobCode,jobRespId,index) {
            // console.table(jobCode,jobRespId,index)
            this.$confirm('确认删除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 有code 数据已被创建  没code数据为此次新增
                if(jobRespId){
                    this.delJobRespFun(jobCode,jobRespId)
                }else{
                    this.dutyData.splice(index, 1);
                }
            }).catch(() => {
                
            });
        },
        delJobRespFun(jobCode,jobRespId){
            deleteJobRespInfo({
                jobCode :jobCode,
                jobRespId:jobRespId
            }).then(res=>{
                // console.log(res)
                if(res.code == 200){
                    this.getJobRespList()
                    this.$msg.success(res.msg)
                }else{
                    this.$msg.error(res.msg)
                }
            })
        },
    },
};
</script>
 
<style scoped lang="scss">
.edu_info_header {
    .item {
        // flex: 1;
        width: 20%;
        &.desc {
            width: 65%;
        }
    }
    .item_icon_wrap {
        text-align: center;
        width: 5%;
    }
}
.business_area_center{
    .edu_info_item {
        .item {
            width: 20%;
        }
        .desc {
            width: 67%;
        }
        .time_percent_wrap{
            width: 23%;
            // background: pink;
            .item{
                margin: 0 5% 0 0;
                width: 80%;
            }

        }
        .item_icon_wrap {
            text-align: center;
            width: 5%;
            .item_icon {
                font-size: 20px;
                cursor: pointer;
            }
        }
    }
}



</style>