<template>
    <div class="org_report_main" :class="{'marginB_16':isPdf}">
        <slot></slot>
        <div class="page_second_title">工作驱动</div>
        <el-row :gutter="16">
            <el-col :span="24">
                <div class="item_title">工作驱动因素（ 哪些因素能够有效的驱动个人更加的投入工作 ）</div>
                <div class="chart_box" id="staffEstablishingByOrg"></div>
            </el-col>
            <el-col :span="24">
                <div class="item_title">激励因素（ 对其产生激励效果的因素 ）</div>
                <div class="incentive_wrap flex_row_betweens">
					<div class="incentive_item">
						<p class="title">强激励因素</p>
						<ul class="desc_wrap">
							<li class="desc_item" v-for="(item,index) in workIncentiveFactors.Strong" :key="index">{{item}}</li>
						</ul>
					</div>
					<div class="incentive_item">
						<p class="title">一般激励因素</p>
						<ul class="desc_wrap">
							<li class="desc_item" v-for="(item,index) in workIncentiveFactors.General" :key="index">{{item}}</li>
						</ul>
					</div>
					<div class="incentive_item">
						<p class="title">一般负激励因素</p>
						<ul class="desc_wrap">
							<li class="desc_item" v-for="(item,index) in workIncentiveFactors.GeneralNegative" :key="index">{{item}}</li>
						</ul>
					</div>
					<div class="incentive_item">
						<p class="title">强负激励因素</p>
						<ul class="desc_wrap">
							<li class="desc_item" v-for="(item,index) in workIncentiveFactors.StrongNegative" :key="index">{{item}}</li>
						</ul>
					</div>
				</div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import { getWorkDriven,getUserIncentiveFactors,getOrgIncentiveFactors } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import listComp from "./components/listComp.vue";
    export default {
        name: "workDriven",
        props: ["enqId", "orgCode","isPdf"],
        components: { tableComps,listComp },
        data() {
            return {
                size: 10,
                current: 1,
                // chartDom: [
                //     {
                //         chartDomId: "staffEstablishingByOrg1231",
                //         title: "工作驱动因素（ 哪些因素能够有效的驱动个人更加的投入工作 ）",
                //         elSpan: 18,
                //         chartType: "XBar",
                //         dataKey:'workDriven'
                //     },
                // ],
                workDriven:{
                    data:[]
                },
                listArr: [
                    {
                        title: "组织激励因素",
                        ajaxUrl: getOrgIncentiveFactors,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label:'部门',
                                prop:'orgName'
                            },
                            {
                                label:'部门负责人',
                                prop:'leaderName'
                            },
                        ],
                    },
                    {
                        title: "个人激励因素",
                        ajaxUrl: getUserIncentiveFactors,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label:'部门',
                                prop:'orgName'
                            },
                            {
                                label:'姓名',
                                prop:'userName'
                            },
                        ],
                    },
                ],
                workIncentiveFactors:[],
            };
        },
        created() {
            // this.getData();
            this.getWorkDrivenFun()
        },
        mounted() {},
        methods: {
            getWorkDrivenFun(){
                getWorkDriven({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then(res=>{
                    if(res.code == 200){
                        this.workDriven.data = res.data.workDriven
                        echartsRenderPage(
                            'staffEstablishingByOrg',
                            'XBar',
                            null,
                            '260',
                            this.workDriven
                        );
                        this.workIncentiveFactors = res.data.workIncentiveFactors
                    }
                })
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .org_report_main{
        .incentive_wrap{
            .incentive_item{
                padding: 0 10px 10px;
                width: 24%;
                color: #008fff;
                background: #dae8fd;
                .title{
                    height: 35px;
                    line-height: 35px;
                    font-weight: 700;
                }
                .desc_wrap{
                    line-height: 20px;
                }
            }
        }
    }
        

</style>