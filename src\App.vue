<script setup lang="js">
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import Dialog from '@/components/AI/dialog.vue'
const locale = zhCn // 插件语言改为中文
const AIDialog = ref(null)
const openAi = text => {
  AIDialog.value.addSession(text)
}
provide('openAi', openAi)
</script>
<template>
  <el-config-provider :locale="locale">
    <RouterView />
    <Dialog ref="AIDialog" />
  </el-config-provider>
</template>

<style scoped></style>
