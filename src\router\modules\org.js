// 组织效能导航
import Layout from '@/layout/index.vue'
const org = [
  {
    path: '/org',
    component: Layout,
    redirect: '/org/home',
    meta: {
      title: '组织效能导航'
    },
    children: [
      {
        path: '/org/home',
        component: () => import('@/views/org/home.vue'),
      },
      {
        path: '/org/entry',
        meta: {
          title: '快速入门'
        },
        component: () => import('@/views/org/entry/index.vue')
      },
      {
        path: '/org/externalbenchmarking',
        meta: {
          title: '组织效能对标'
        },
        component: () => import('@/views/org/externalbenchmarking/index.vue'),
      },
      {
        path: '/org/projectBMAdd',
        meta: {
          title: '组织效能对标'
        },
        component: () => import('@/views/org/externalbenchmarking/projectBMAdd.vue'),
      },
      {
        path: '/org/internalIdentification',
        meta: {
          title: '内部识别：人才盘点与测评'
        },
        component: () => import('@/views/org/internalIdentification/index.vue'),
        redirect:'/org/internalIdentification/talentInventoryS',
        children:[
          // 人才盘点服务
          {
            path: '/org/internalIdentification/talentInventoryS',
            meta: {
              title: ''
            },
            component: () => import('@/views/org/internalIdentification/talentInventoryS/index.vue'),
            redirect:'/org/internalIdentification/talentInventoryS/inventoryProject',
            children:[
              {
                path: '/org/internalIdentification/talentInventoryS/inventoryProject',
                meta: {
                  title: ''
                },
                component: () => import('@/views/org/internalIdentification/talentInventoryS/inventoryProject.vue'),
              },
              {
                path: '/org/internalIdentification/talentInventoryS/inventoryReport',
                meta: {
                  title: ''
                },
                component: () => import('@/views/org/internalIdentification/talentInventoryS/inventoryReport.vue'),
                
              },
            ]
          },
          // 人才测评服务
          {
            path: '/org/internalIdentification/talentAssessmentS',
            meta: {
              title: ''
            },
            component: () => import('@/views/org/internalIdentification/talentAssessmentS/index.vue'),
            redirect:'/org/internalIdentification/talentAssessmentS/inventoryProject',
            children:[
              {
                path: '/org/internalIdentification/talentAssessmentS/inventoryProject',
                meta: {
                  title: ''
                },
                component: () => import('@/views/org/internalIdentification/talentAssessmentS/inventoryProject.vue'),
              },
              {
                path: '/org/internalIdentification/talentAssessmentS/inventoryReport',
                meta: {
                  title: ''
                },
                component: () => import('@/views/org/internalIdentification/talentAssessmentS/inventoryReport.vue'),
                
              },
            ]
          },
        ]

        
      },
      {
        path: '/org/result',
        meta: {
          title: '看结果：指标视角看效能'
        },
        component: () => import('@/views/org/result/index.vue'),
        redirect:'/org/result/indicator/index',
        children:[
          {
            path: '/org/result/indicator/index',
            meta: {
              title: '指标'
            },
            component: () => import('@/views/org/result/indicator/index.vue'),
          },
          {
            path: '/org/result/ability/index',
            meta: {
              title: '能力'
            },
            component: () => import('@/views/org/result/ability/index.vue'),
          },
          {
            path: '/org/result/task/index',
            meta: {
              title: '任务'
            },
            component: () => import('@/views/org/result/task/index.vue'),
          },
        ]
      },
      {
        path: '/org/ability',
        meta: {
          title: '能力视角看效能'
        },
        component: () => import('@/views/org/ability/index.vue')
      },
      {
        path: '/org/execute',
        meta: {
          title: '任务视角看效能'
        },
        component: () => import('@/views/org/execute/index.vue')
      },
      {
        path: '/org/support/index',
        meta: {
          title: '组织与人员支撑'
        },
        // component: () => import('@/views/org/support/index.vue'),
        children:[
          {
            path: '/org/support',
            meta: {
              title: ''
            },
            component: () => import('@/views/org/support/index.vue'),
          },
          {
            path: '/org/support/index',
            meta: {
              title: ''
            },
            component: () => import('@/views/org/support/staffSupport/index.vue'),
          },
        ]
      },
    ]
  }
]

export default org
