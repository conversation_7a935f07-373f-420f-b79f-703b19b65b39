<template>
  <div class="capability_requirements_main">
    <tabs-custom :tabsData="tabsData" :activeName="'all'" @tab-click="tabClick" />
    <div class="flex_row_wrap_start">
      <div class="capability_item" v-for="item in capabilityData2" :key="item.moduleCode">
        <div class="title flex_row_between">
          <div class="text">{{ item.moduleName }}</div>
          <div class="score_bar flex_row_between">
            <div class="bar">
              <span class="bar_inside" :style="{ width: item.expectedScore + '%' }" />
            </div>
            <div class="score">{{ item.expectedScore }}</div>
          </div>
        </div>
        <div class="capability_item_content clearfix">
          <div class="item" v-for="list in item.items" :key="list.moduleCode">
            {{ list.moduleName }}
          </div>
        </div>
      </div>
    </div>
    <div class="btn_wrap">
      <el-button class="page_confirm_btn" type="primary" @click="submitBtn"> 下一页 </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { jobInfoCapabilityRequirements, searchPost } from '../../../../request/api'
import tabsCustom from '@/components/talent/tabsComps/tabsCustom'

// Props
const props = defineProps({
  curPostCodeCopy: String
})

// Emits
const emit = defineEmits(['submitSuccessTab'])

// Reactive State
const postCode = ref(props.curPostCodeCopy || '')
const tabsData = ref([
  {
    name: 'all',
    label: '全部'
  }
])
const capabilityData = ref([])
const capabilityData2 = ref([])

// Methods
const jobInfoCapabilityRequirementsFun = async () => {
  try {
    const res = await jobInfoCapabilityRequirements({
      competenceClass: null,
      jobCode: '',
      postCode: postCode.value
    })

    if (res.data) {
      capabilityData.value = res.data
      tabsData.value = res.data.map(item => ({
        name: item.moduleCode,
        label: item.moduleName
      }))
      capabilityData2.value = capabilityData.value
      tabsData.value.unshift({
        name: 'all',
        label: '全部'
      })
    }
  } catch (error) {
    console.error('获取能力要求失败:', error)
  }
}

const searchPostFun = async () => {
  if (!props.curPostCodeCopy) return

  try {
    await searchPost({
      postCode: props.curPostCodeCopy
    })
  } catch (error) {
    console.error('获取岗位信息失败:', error)
  }
}

const tabClick = tabItem => {
  const id = tabItem.name
  getCapabilityData(id)
}

const getCapabilityData = id => {
  if (id == 'all') {
    capabilityData2.value = capabilityData.value
  } else {
    capabilityData2.value = capabilityData.value.filter(item => item.moduleCode == id)
  }
}

const submitBtn = () => {
  emit('submitSuccessTab', 'ability')
}

// Lifecycle Hooks
onMounted(() => {
  jobInfoCapabilityRequirementsFun()
  searchPostFun()
})
</script>

<style lang="scss" scoped>
.capability_requirements_main {
  padding: 0 0 8px 8px;
}

.capability_item {
  width: 260px;
  padding: 8px 16px;
  margin: 0 16px 16px 0;
  border-radius: 8px;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.2);

  .title {
    line-height: 22px;
    color: #0099fd;
    font-weight: 700;
    padding-bottom: 8px;
    margin-bottom: 8px;
    border-bottom: 1px solid var(--el-color-info-light-8);

    .score_bar {
      align-items: center;
      font-size: 14px;

      .bar {
        position: relative;
        width: 57px;
        height: 6px;
        background: var(--el-color-info-light-8);
        border-radius: 3px;
        margin-right: 10px;

        .bar_inside {
          position: absolute;
          width: 65%;
          height: 100%;
          top: 0;
          left: 0;
          background: #87c947;
          border-radius: inherit;
        }
      }

      .score {
        color: #87c947;
      }
    }
  }

  .capability_item_content {
    .item {
      width: 110px;
      line-height: 30px;
      color: #0099fd;
      font-size: 12px;
      margin: 0 8px 8px 0;
      border-radius: 4px;
      padding-left: 8px;
    }
  }
}
</style>
