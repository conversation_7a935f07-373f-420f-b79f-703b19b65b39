<template>
  <div class="department_report_wrap performance_info_main">
    <div class="department_report_main">
      <div class="top_wrap marginT_30 flex_row_betweens">
        <div class="top_left_wrap">
          <div class="page_second_title">
            <span>KPI综合得分</span>
          </div>
          <div class="top_left_main">
            <div class="decs marginT_20">综合得分</div>
            <div class="score">86<span>分</span></div>
          </div>
        </div>
        <div class="top_right_wrap">
          <div class="page_second_title">
            <span>KPI综合表现分布</span>
          </div>
          <div class="top_right_main">
            <div class="" id="jyDistributeChart"></div>
          </div>
        </div>
      </div>
      <div class="bottom_wrap">
        <div class="page_second_title">
          <span>KPI指标信息</span>
        </div>
        <div class="bottom_main">
          <tableComponent
            :tableData="tableData"
            :border="true"
            :needIndex="true"
            :needPagination="false"
          ></tableComponent>
        </div>
      </div>
      <div class="align_center">
        <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
        <el-button class="page_confirm_btn" type="primary" @click="nextBtn">下一步</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { echartsRenderPage } from '../../../../../../public/js/echartsimg/echartsToImg.js'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const jyDistributeChart = ref({
  data: [
    {
      value: 50,
      name: '不合格'
    }
  ]
})

const tableData = ref({
  columns: [
    {
      label: '姓名',
      prop: 'name',
      width: ''
    },
    {
      label: '岗位',
      prop: 'post',
      width: ''
    },
    {
      label: '指标名称',
      prop: 'indexName',
      width: ''
    },
    {
      label: '目标',
      prop: 'target',
      width: ''
    },
    {
      label: '实际表现',
      prop: 'realPerformance',
      width: ''
    },
    {
      label: '上级评价',
      prop: 'higherEvaluation',
      width: ''
    },
    {
      label: 'HR总监',
      prop: 'HRdirector',
      width: ''
    },
    {
      label: '综合得分',
      prop: 'synthesisScore',
      width: ''
    },
    {
      label: '综合评价',
      prop: 'synthesisEvaluate',
      width: ''
    }
  ],
  data: [
    {
      name: '姓名',
      post: '销售总监',
      indexName: '物料周转天数',
      target: '55天',
      realPerformance: '',
      higherEvaluation: '',
      HRdirector: '',
      synthesisScore: '',
      synthesisEvaluate: ''
    }
  ]
})

onMounted(() => {
  initChart()
})

const initChart = () => {
  echartsRenderPage('jyDistributeChart', 'XBar', '1000', '200', jyDistributeChart.value)
}

const prevBtn = () => {
  // TODO: Implement previous step logic
}

const nextBtn = () => {
  // TODO: Implement next step logic
}
</script>

<style scoped lang="scss">
.department_report_wrap {
  .department_report_main {
    .top_wrap {
      height: 220px;
      .top_left_wrap {
        .top_left_main {
          width: 230px;
          margin: 0 0 0 15px;
          .decs {
          }
          .score {
            height: 45px;
            line-height: 45px;
            color: #008fff;
            font-weight: 600;
            span {
              font-weight: normal;
            }
          }
        }
      }
      .top_right_wrap {
        flex: 1;
        .top_right_main {
          div {
            // width: 900px;
            width: 93%;
            height: 160px;
          }
        }
      }
    }
    .bottom_wrap {
      // margin: 0 0 30px 0;
    }
  }
  .align_center {
    padding: 110px 0 0 0;
  }
}
</style>
