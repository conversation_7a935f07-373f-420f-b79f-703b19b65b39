<template>
    <div class="talent_main">
        <div class="aside_filter_wrap">
            <asideFilterBar
                :filterData="filterData"
                @getCode="getCode"
            ></asideFilterBar>
        </div>
        <div class="talent_number_content page_section">
            <div class="content_item_title marginB_16">离职风险影响矩阵</div>
            <div class>
                <div class="matrix_chart">
                    <div class="matrix_head">
                        <div class="title">离职影响</div>
                        <div class="flex_row_start border">
                            <div
                                class="item"
                                v-for="list in dimissionImpact"
                                :key="list.dictCode"
                            >
                                {{ list.codeName }}
                            </div>
                        </div>
                    </div>
                    <div class="clearfix">
                        <div class="matrix_aside">
                            <div class="matrix_aside_head flex_row_start">
                                <div class="title">离职风险</div>
                                <div class="flex_col_start border">
                                    <div
                                        class="item"
                                        v-for="list in retentionRisk"
                                        :key="list.dictCode"
                                    >
                                        {{ list.codeName }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="matrix_main flex_col_start">
                            <div
                                class="item flex_row_start"
                                v-for="(item, index) in matrixData"
                                :key="item.code + index"
                            >
                                <div
                                    class="list"
                                    :class="'level_' + item.code + list.code"
                                    v-for="(list, listIndex) in item.maps"
                                    :key="listIndex + list.code"
                                >
                                    {{ list.value }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title clearfix">
                        详情列表
                        <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
                    </div>
                    <div class="content_item_content">
                        <tableComponet
                            @handleSizeChange="handleSizeChange"
                            @handleCurrentChange="handleCurrentChange"
                            :tableData="tableData"
                            :needIndex="true"
                        ></tableComponet>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
 
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js"
import { retentionMatrix, retentionMatrixList, exportData } from "../../../../../request/api.js"
import asideFilterBar from "../../asideFilterBar.vue"
import tableComponet from "@/components/talent/tableComps/tableComponent.vue"

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref({})
const matrixData = ref([])
const dimissionImpact = ref([])
const retentionRisk = ref([])
const page = ref(1)
const size = ref(10)

const tableData = reactive({
    columns: [
        {
            label: "员工编码",
            prop: "employee_code"
        },
        {
            label: "员工姓名",
            prop: "user_name"
        },
        {
            label: "所属组织",
            prop: "org_name"
        },
        {
            label: "任职岗位",
            prop: "post_name"
        },
        {
            label: "职层",
            prop: "job_level_name"
        },
        {
            label: "离职风险",
            prop: "retentionRisk"
        },
        {
            label: "离职影响",
            prop: "dimissionImpact"
        },
        {
            label: "人才分类",
            prop: "talentType"
        },
        {
            label: "评价人",
            prop: "superior"
        },
        {
            label: "评价日期",
            prop: "evaluationTime"
        }
    ],
    data: [],
    page: {
        current: 1,
        total: 0,
        size: 10
    }
})

const retentionMatrixFun = async () => {
    try {
        const params = {
            enqId: enqId.value,
            jobClassCode: jobClassCode.value,
            orgCode: orgCode.value
        }
        const res = await retentionMatrix(params)
        if (res.code == "200") {
            matrixData.value = res.data
        }
    } catch (error) {
        console.error('获取离职风险矩阵数据失败:', error)
    }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
    jobClassCode.value = jobClassCodeVal
    orgCode.value = orgCodeVal
    page.value = 1
    retentionMatrixFun()
    getTableData()
}

const handleCurrentChange = pageVal => {
    page.value = pageVal
    getTableData()
}

const handleSizeChange = sizeVal => {
    size.value = sizeVal
    getTableData()
}

const getTableData = async () => {
    try {
        const params = {
            enqId: enqId.value,
            jobClassCode: jobClassCode.value,
            orgCode: orgCode.value,
            current: page.value,
            size: size.value
        }
        const res = await retentionMatrixList(params)
        if (res.code == 200) {
            tableData.data = res.data
            tableData.page = res.page
        }
    } catch (error) {
        console.error('获取表格数据失败:', error)
    }
}

const exportDataFn = async () => {
    try {
        const params = {
            enqId: enqId.value,
            orgCode: orgCode.value,
            type: "h"
        }
        const res = await exportData(params)
        window.$exportDownloadFile(res.data, "离职风险矩阵明细")
    } catch (error) {
        console.error('导出数据失败:', error)
    }
}

onMounted(async () => {
    enqId.value = route.query.enqId
    filterData.value = route.attrs.filterData
    try {
        const res = await window.$getDocList(["RETENTION_RISK", "DIMISSION_IMPACT"])
        retentionRisk.value = res.RETENTION_RISK
        dimissionImpact.value = res.DIMISSION_IMPACT
    } catch (error) {
        console.error('获取字典数据失败:', error)
    }
    retentionMatrixFun()
    getTableData()
})
</script>
 
<style scoped lang="scss">
    .matrix_chart {
        width: 700px;
        float: left;
        margin-right: 6px;
        margin-bottom: 16px;
        .matrix_head {
            width: 100%;
            text-align: center;
            line-height: 30px;
            .title {
                height: 30px;
                background: #fbfbfb;
                padding-left: 60px;
            }
            .flex_row_start {
                height: 30px;
                margin-left: 60px;
                &.border {
                    border-bottom: 1px solid #f6f6f6;
                }
            }
        }
    }
</style>