<template>
  <div class="work_activity_wrap">
    <div class="edu_info_header">
      <div class="item">工作活动类型</div>
      <div class="item">对应岗位类型</div>
      <div class="item">工作任务名称</div>
      <div class="item item_icon_wrap">操作</div>
    </div>
    <div class="edu_info_mmain">
      <div
        class="edu_info_item"
        v-if="workActivityList.length > 0"
        v-for="(item, index) in workActivityList"
        :key="item.jobActivityCode"
      >
        <el-select class="item" v-model="item.jobActivityType" disabled placeholder>
          <el-option
            v-for="activity in jobActivityList"
            :key="activity.dictCode"
            :label="activity.codeName"
            :value="activity.dictCode"
          />
        </el-select>
        <el-input class="item" v-model="item.jobClassName" disabled placeholder />
        <el-input class="item" v-model="item.jobActivityName" disabled placeholder />
        <div class="item item_icon_wrap">
          <el-icon class="item_icon icon_del" @click="deleteItem(item, index)">
            <Delete />
          </el-icon>
        </div>
      </div>

      <div class="no_data_tip" v-if="workActivityList.length == 0">暂无数据</div>
      <div class="align_center paddT_12">
        <el-button class="page_confirm_btn" type="primary" @click="addPostActivity"> 新增 </el-button>
      </div>
    </div>

    <el-dialog v-model="dialogVisible" title="选择工作活动" width="60%" center>
      <div class="page_section">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title">岗位族群</div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-checkbox
              :defaultCheckedKeys="defaultCheckedKeys"
              :treeData="treeData"
              @node-click-callback="nodeClickCallback"
            />
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <table-component
            :tableData="tableData"
            :needIndex="false"
            :needPagination="false"
            :checkSelection="checkSelection"
            :selectionStatus="true"
            @selectionChange="selectionChange"
            @curSelectInfo="curSelectInfo"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer align_right">
          <el-button class="page_clear_btn" @click="cancel">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="popUpSubmitBtn"> 确 定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import {
  getPostActivity,
  createPostActivity,
  getJobActivity,
  deletePostActivity,
  getDict,
  getJobClassTree
} from '../../../../request/api'
import treeCompCheckbox from '@/components/talent/treeComps/treeCompCheckbox'
import tableComponent from '@/components/talent/tableComps/tableComponent'

// Props
const props = defineProps({
  curPostCodeCopy: String,
  curJobClassCodeCopy: String
})

// Reactive State
const jobActivityList = ref([])
const workActivityList = ref([])
const dialogVisible = ref(false)
const defaultCheckedKeys = ref([])
const treeData = ref([])
const tableData = ref({
  columns: [
    {
      label: '岗位序列',
      prop: 'jobClassName'
    },
    {
      label: '活动类型',
      prop: 'jobActivityTypeName'
    },
    {
      label: '工作任务名称',
      prop: 'jobActivityName'
    }
  ],
  data: []
})
const checkSelection = ref([])
const checkWorkActivityList = ref([])
const defaultCheckWorkActivityList = ref([])

// Methods
const getDictFun = async () => {
  try {
    const res = await getDict({
      dictId: 'JOB_ACTIVITY_TYPE'
    })
    if (res.code == 200) {
      jobActivityList.value = res.data
    }
  } catch (error) {
    console.error('获取工作活动类型失败:', error)
    ElMessage.error('获取工作活动类型失败')
  }
}

const getPostActivityFun = async () => {
  workActivityList.value = []
  try {
    const res = await getPostActivity({
      postCode: props.curPostCodeCopy
    })
    if (res.code == 200 && res.data.length > 0) {
      workActivityList.value = res.data.map(item => ({
        jobActivityName: item.jobActivityName,
        jobActivityCode: item.jobActivityCode,
        jobActivityType: item.jobActivityType,
        jobClassName: item.jobClassName,
        jobClassCode: item.jobClassCode
      }))
    }
  } catch (error) {
    console.error('获取工作活动列表失败:', error)
    ElMessage.error('获取工作活动列表失败')
  }
}

const addPostActivity = () => {
  dialogVisible.value = true
}

const getJobClassTreeFun = async () => {
  try {
    const res = await getJobClassTree({})
    treeData.value = res.length > 0 ? res : []
  } catch (error) {
    console.error('获取族群树失败:', error)
    ElMessage.error('获取族群树失败')
  }
}

const nodeClickCallback = (val, curCheckCode) => {
  addDefaultCheckAct(val, curCheckCode)
  const jobClassCodes = val.join(',')
  getJobActivityFun(jobClassCodes)
}

const addDefaultCheckAct = (val, curCheckCode) => {
  if (val.includes(curCheckCode)) {
    val.forEach(jobClassCode => {
      defaultCheckedKeys.value.forEach(defaultCode => {
        if (jobClassCode == defaultCode) {
          defaultCheckWorkActivityList.value.forEach(activity => {
            if (jobClassCode == activity.jobClassCode) {
              const exists = checkWorkActivityList.value.some(item => item.jobActivityCode == activity.jobActivityCode)
              if (!exists) {
                checkWorkActivityList.value.push(activity)
              }
            }
          })
        }
      })
    })
  }
}

const getJobActivityFun = async jobClassCodes => {
  try {
    const res = await getJobActivity({
      jobClassCodes
    })
    if (res.code == 200) {
      tableData.value.data = res.data.map(item => ({
        jobActivityName: item.jobActivityName,
        jobActivityCode: item.jobActivityCode,
        jobActivityTypeName: item.jobActivityTypeName,
        jobClassName: item.jobClassName,
        jobClassCode: item.jobClassCode
      }))
    }
    curCheckPosts()
  } catch (error) {
    console.error('获取企业工作活动列表失败:', error)
    ElMessage.error('获取企业工作活动列表失败')
  }
}

const selectionChange = val => {
  checkWorkActivityList.value = val
}

const curSelectInfo = (selection, row) => {
  const isSelected = selection.some(item => item.jobActivityCode == row.jobActivityCode)
  if (isSelected) {
    checkWorkActivityList.value.push(row)
  } else {
    const index = checkWorkActivityList.value.findIndex(item => item.jobActivityCode == row.jobActivityCode)
    if (index !== -1) {
      checkWorkActivityList.value.splice(index, 1)
    }
  }
}

const curCheckPosts = () => {
  const curCheckSelection = []
  checkWorkActivityList.value.forEach(activity => {
    const matchingItem = tableData.value.data.find(item => activity.jobActivityCode == item.jobActivityCode)
    if (matchingItem) {
      curCheckSelection.push(matchingItem)
    }
  })
  checkSelection.value = curCheckSelection
}

const defaultCheckedTreeAndTable = () => {
  if (workActivityList.value.length == 0) {
    defaultCheckedKeys.value = []
    checkSelection.value = []
    checkWorkActivityList.value = []
    tableData.value.data = []
    return
  }

  const defaultJobClassCodes = []
  checkWorkActivityList.value = workActivityList.value
  defaultCheckWorkActivityList.value = checkWorkActivityList.value

  workActivityList.value.forEach(activity => {
    if (!defaultJobClassCodes.includes(activity.jobClassCode)) {
      defaultJobClassCodes.push(activity.jobClassCode)
    }
  })

  getJobActivityFun(defaultJobClassCodes.join(','))
  defaultCheckedKeys.value = defaultJobClassCodes
}

const cancel = () => {
  dialogVisible.value = false
}

const popUpSubmitBtn = () => {
  createPostActivityFun()
}

const createPostActivityFun = async () => {
  const postActivityRequestList = checkWorkActivityList.value.length
    ? checkWorkActivityList.value.map(item => ({
        jobActivityCode: item.jobActivityCode,
        postCode: props.curPostCodeCopy
      }))
    : [
        {
          jobActivityCode: '',
          postCode: props.curPostCodeCopy
        }
      ]

  try {
    const res = await createPostActivity(postActivityRequestList)
    if (res.code == 200) {
      dialogVisible.value = false
      await getPostActivityFun()
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('创建工作活动失败:', error)
    ElMessage.error('创建工作活动失败')
  }
}

const deleteItem = async item => {
  try {
    await ElMessageBox.confirm('确定删除?', '确定', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await deletePostActivityFun(item.jobActivityCode)
  } catch {
    // User cancelled the operation
  }
}

const deletePostActivityFun = async jobActivityCode => {
  try {
    const res = await deletePostActivity({
      jobActivityCode,
      postCode: props.curPostCodeCopy
    })
    if (res.code == 200) {
      ElMessage.success(res.msg)
      await getPostActivityFun()
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('删除工作活动失败:', error)
    ElMessage.error('删除工作活动失败')
  }
}

// Watchers
watch(
  () => dialogVisible.value,
  val => {
    if (val) {
      defaultCheckedTreeAndTable()
    }
  }
)

// Initial Setup
onMounted(() => {
  getJobClassTreeFun()
  getPostActivityFun()
  getDictFun()
})
</script>

<style lang="scss" scoped>
.aside_tree_title {
  margin-top: 0;
}

.edu_info_header {
  .item {
    width: 32%;
    padding: 0 8px;
  }

  .item_icon_wrap {
    text-align: center;
    width: 8%;
  }
}

.work_activity_wrap {
  .edu_info_item {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;

    .item {
      width: 32%;
    }

    .item_icon_wrap {
      text-align: center;
      width: 8%;

      .item_icon {
        margin-top: 5px;
      }
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__body {
    padding: 0 5px;

    .page_section {
      .page_section_aside {
        width: 25%;

        .aside_tree_title {
          .tree_title {
            width: 100%;
          }
        }

        .aside_tree_list {
          height: 350px !important;
          padding: 10px 5px;
        }
      }

      .page_section_main {
        height: 380px;
        min-height: 386px;
        flex: 1;
      }
    }
  }
}
</style>
