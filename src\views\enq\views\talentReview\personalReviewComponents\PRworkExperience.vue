<script setup>
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { updateEnqUserExperience, delEnqUserExperience, getEnqUserExperience } from '../../../request/api'
import workExperienceItem from './PRworkExperienceItem.vue'
import { objHasEmpty } from '@/utils/utils'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const submitFlag = ref(true)
const workData = ref([])

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const userId = computed(() => userInfo.value.userId)

const getExperienceData = () => {
  getEnqUserExperience({
    current: '1',
    size: '10',
    enqId: props.enqId
  }).then(res => {
    if (res.code == '200') {
      workData.value = res.data
    }
  })
}

onMounted(() => {
  getExperienceData()
})

function deleteItem(item, index) {
  if (!Object.prototype.hasOwnProperty.call(item, 'experienceId')) {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        workData.value.splice(index, 1)
        ElMessage.success('删除成功!')
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  } else {
    const experienceId = item.experienceId
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      type: 'warning'
    })
      .then(() => {
        delEnqUserExperience({ experienceId, enqId: props.enqId }).then(res => {
          if (res.code == '200') {
            ElMessage.success('删除成功!')
            workData.value.splice(index, 1)
          } else {
            ElMessage.error('删除失败！')
          }
        })
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  }
}

function addItem() {
  const obj = workData.value[workData.value.length - 1]
  let addObj = {
    companyName: '',
    jobLevelCode: '',
    jobClassCode: '',
    bizDomainCode: '',
    postRelated: '',
    industryRelated: '',
    beginDate: '',
    endDate: '',
    userId: userId.value,
    enqId: props.enqId
  }
  if (!obj) {
    workData.value.push(addObj)
    return
  }
  if (checkData(workData.value)) {
    ElMessage.warning('请完善当前数据后新增！')
    return
  }
  workData.value.push(addObj)
}

function submit(stepType) {
  if (!submitFlag.value) return
  if (checkData(workData.value)) {
    ElMessage.warning('请完善数据后提交！')
    return
  }
  submitFlag.value = false
  workData.value.forEach(obj => {
    if (typeof obj.jobClassCode !== 'string' && Array.isArray(obj.jobClassCode)) {
      obj.jobClassCode = obj.jobClassCode[obj.jobClassCode.length - 1]
    }
    if (typeof obj.jobLevelCode !== 'string' && Array.isArray(obj.jobLevelCode)) {
      obj.jobLevelCode = obj.jobLevelCode[obj.jobLevelCode.length - 1]
    }
  })
  updateEnqUserExperience(workData.value)
    .then(res => {
      if (res.code == '200') {
        ElMessage.success('保存成功!')
        submitFlag.value = true
        getExperienceData()
        emit(stepType)
      } else {
        submitFlag.value = true
        ElMessage.error('保存失败!')
      }
    })
    .catch(() => {
      submitFlag.value = true
    })
}

function prevStep() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submit('prevStep')
    })
    .catch(action => {
      if (action === 'cancel') {
        ElMessage.info('已放弃修改并返回上一步')
        emit('prevStep')
      } else {
        ElMessage.info('取消返回上一步')
      }
    })
}

function checkData(data) {
  for (const obj of data) {
    if (
      objHasEmpty(obj, [
        'companyName',
        'currentCompany',
        'beginDate',
        'industryRelated',
        'jobClassCode',
        'jobLevelCode',
        'postRelated',
        'bizDomainCode'
      ])
    ) {
      return true
    }
  }
  return false
}
</script>

<template>
  <div class="edu_info_wrap">
    <div class="clearfix">
      <div class="page_second_title marginT_16">
        <span>工作履历</span>
      </div>
      <div class="text_right">
        <el-button class="page_add_btn" type="primary" @click="addItem">新增</el-button>
      </div>
      <div class="edu_info_center marginT_16">
        <div class="edu_info_header">
          <div class="item long">公司名称</div>
          <div class="item short">本公司</div>
          <div class="item long">开始日期</div>
          <div class="item long">结束日期</div>
          <div class="item">岗位职层</div>
          <div class="item">是否最高职层</div>
          <div class="item">岗位类型</div>
          <div class="item">业务领域</div>
          <div class="item short">
            同岗位
            <el-tooltip
              class="item_tootip"
              effect="dark"
              content="在其他公司从事过与当前岗位一样或相近的岗位"
              placement="top"
            >
              <i class="el-icon-question icon_color"></i>
            </el-tooltip>
          </div>
          <div class="item short">
            同行业
            <el-tooltip
              class="item_tootip"
              effect="dark"
              content="其他工作的公司与当前公司属于同一行业或相近行业"
              placement="top"
            >
              <i class="el-icon-question icon_color"></i>
            </el-tooltip>
          </div>
          <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
          <work-experience-item :workData="workData" @deleteItem="deleteItem" />
          <div class="align_center marginT_30">
            <el-button
              class="page_confirm_btn"
              type="primary"
              @click="prevStep"
              v-show="props.currentIndex != props.currentFirstCode"
              >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="() => submit('nextStep')">{{
              props.nextBtnText
            }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.edu_info_header {
  .item {
    position: relative;
    width: 11%;

    &.short {
      width: 7%;
    }
    &.long {
      width: 15%;
    }
    &.long2 {
      width: 30%;
    }
    .item_tootip {
      position: absolute;
      top: 5px;
      cursor: pointer;
      &:hover {
        color: #0099fd;
      }
    }
  }
  .item_icon_wrap {
    text-align: center;
    width: 5%;
  }
}
</style>
