import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
export default (path, isBuild) => {
  return createSvgIconsPlugin({
    // 指定需要缓存的图标文件夹
    iconDirs: [
      path.resolve(path.resolve(__dirname, '../../src'), 'assets/svgIcon'),
      path.resolve(path.resolve(__dirname, '../../src'), 'assets/clearColorSvg')
    ],
    // 指定symbolId格式
    symbolId: 'icon-[dir]-[name]',
    svgoOptions: isBuild
  })
}
