<template>
  <el-tree
    :data="data"
    :props="defaultProps"
    node-key="id"
    default-expand-all
    :current-node-key="active"
    highlight-current
  />
</template>
<script setup>
const defaultProps = {
  children: 'children',
  label: 'label'
}
const active = ref(6)
const data = ref([
  {
    id: 1,
    label: 'H公司',
    children: [
      {
        id: 5,
        label: '采购部'
      },
      {
        id: 6,
        label: '供应链计划管理部'
      },
      {
        id: 7,
        label: '电子商务部'
      },
      {
        id: 8,
        label: '欧盟区产品部'
      },
      {
        id: 9,
        label: '工艺部'
      },
      {
        id: 10,
        label: '供应链计划管理部'
      },
      {
        id: 11,
        label: 'GTM部'
      },
      {
        id: 12,
        label: '结构研发部'
      },
      {
        id: 13,
        label: '经营与财务管理部'
      },
      {
        id: 14,
        label: '冷柜研发部'
      },
      {
        id: 15,
        label: '零售与用户运营部'
      },
      {
        id: 16,
        label: '品牌与产品营销部'
      }
    ]
  }
])
</script>
<style lang="scss" scoped></style>
