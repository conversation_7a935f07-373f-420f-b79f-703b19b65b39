<template>
    <div class="org_report_main job_recruit_wrap" :class="{ marginB_16: isPdf }">
        <div class="page_second_title">人员编制</div>
        <el-row :gutter="16">
            <el-col :span="6">
                <div class="item_title">人员编制</div>
                <div class="staff_authorized_strength flex_row_start">
                    <div >
                        <p class="title">现有人员</p>
                        <p><span>{{getStaffEstablishing.staffCount}}</span>人</p>
                    </div>
                    <div>
                        <p class="title">建议编制</p>
                        <p><span>{{getStaffEstablishing.budgetedCount}}</span>人</p>
                    </div>
                </div>
                 <div class="staff_authorized_strength flex_row_start">
                    <div>
                        <p class="title">空缺人数</p>
                        <p><span>{{getStaffEstablishing.shortageCount}}</span>人</p>
                    </div>
                    <div>
                        <p class="title">当前需求</p>
                        <p><span>{{getStaffEstablishing.recruitCount}}</span>人</p>
                    </div>
                </div>
            </el-col>
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24">
                <div class="item_title">岗位招募需求详情</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentChange"
                    @handleSizeChange="handleSizeChange"
                    :tableData="tableData"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf">
                    更多数据请查看网页版报告
                </div>
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    // import { orgStaffEstabList, orgStaffEstab } from "../../../../request/api";
    import { orgStaffEstabList, jobRecruitment } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "orgROrg",
        props: ["enqId", "orgCode", "isPdf"],
        components: { tableComps },
        data() {
            return {
                getStaffEstablishing:'',
                size: 10,
                current: 1,
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "各部门人员缺口",
                        elSpan: 18,
                        chartType: "XBar",
                        dataKey: "getOrgGap",
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "招募原因",
                        elSpan: 6,
                        height:'300',
                        chartType: "YBar",
                        dataKey: "getRecruitReason",
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "人员需求紧急程度",
                        elSpan: 6,
                        height:'300',
                        chartType: "YBar",
                        dataKey: "emergency",
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "人员需求紧急岗位族群分布",
                        elSpan: 6,
                        height:'300',
                        chartType: "YBar",
                        dataKey: "emergencyByClass",
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "人员紧急需求岗位分布",
                        elSpan: 6,
                        height:'300',
                        chartType: "YBar",
                        dataKey: "emergencyByPost",
                    },
                    // {
                    //     chartDomId: this.$util.createRandomId(),
                    //     title: "期望到岗周期",
                    //     elSpan: 6,
                    //     height: "451",
                    //     chartType: "YBar",
                    //     dataKey: "personalDevelopment",
                    // },
                ],
                tableData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "orgName",
                        },
                        {
                            label: "职位",
                            prop: "jobName",
                        },
                        {
                            label: "职层",
                            prop: "jobLevelName",
                        },
                        {
                            label: "招募原因",
                            prop: "postName",
                        },
                        {
                            label: "现有人员",
                            prop: "recruitmentReason",
                        },
                        {
                            label: "建议编制",
                            prop: "staffCount",
                        },
                        {
                            label: "空缺人数",
                            prop: "budgetedCount",
                        },
                        {
                            label: "当前需求人数",
                            prop: "shortageCount",
                        },
                        {
                            label: "紧急程度",
                            prop: "recruitCount",
                        },
                        {
                            label: "期望到岗日期",
                            prop: "emergency",
                        },
                        // {
                        //     label: "期望到岗周期",
                        //     prop: "trans_out_count",
                        // },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
            };
        },
        created() {
            // this.getData();
            this.orgStaffEstabFn();
            this.jobRecruitmentFun()
        },
        mounted() {},
        methods: {
            jobRecruitmentFun(){
                jobRecruitment({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then(res=>{
                    console.log(res)
                    if(res.code == 200){
                        this.getStaffEstablishing = res.data.getStaffEstablishing[0]
                        this.initChart(res.data)
                    }
                })
            },
            initChart(data) {
                console.log(data);
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            orgStaffEstabFn() {
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgStaffEstabList(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
            handleCurrentChange(current) {
                this.current = current;
                this.orgStaffEstabFn();
            },
            handleSizeChange(size) {
                this.size = size;
                this.orgStaffEstabFn();
            },
            // ------
            // getData() {
            //     let dataObj = {
            //         shortage_count: "缺口",
            //         final_count: "期末",
            //         trans_out_count: "调出",
            //         trans_in_count: "调入",
            //         resignation_count: "主动辞职",
            //         entrant_count: "入职",
            //         initial_count: "期初",
            //         budgeted_count: "编制人数",
            //     };
            //     let params = {
            //         enqId: this.enqId,
            //         orgCode: this.orgCode,
            //     };
            //     orgStaffEstab(params).then((res) => {
            //         if (res.code == "200") {
            //             let personalDevelopment =
            //                 res.data["personalDevelopment"][0];
            //             let result = [];
            //             for (const key in dataObj) {
            //                 if (Object.hasOwnProperty.call(dataObj, key)) {
            //                     if (personalDevelopment[key] != null) {
            //                         result.push({
            //                             name: dataObj[key],
            //                             value: personalDevelopment[key],
            //                         });
            //                     }
            //                 }
            //             }
            //             res.data["personalDevelopment"] = result;
            //             console.log("######", result);
            //             this.initChart(res.data);
            //         }
            //     });
            // },
        },
    };
</script>
 
<style scoped lang="scss">
.job_recruit_wrap{
    .el-row{
        .staff_authorized_strength{
            div{
                margin: 0 25px 30px 0;
                p{
                    line-height: 30px;
                }
                span{
                    color: #0099ff;
                }
            }
        }
    }
}
</style>