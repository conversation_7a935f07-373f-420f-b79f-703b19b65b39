<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <div class="page_second_title">任职匹配</div>
        <el-row :gutter="16">
            <el-col :span="8">
                <div class="item_content flex_row_around">
                    <div class="content_item">
                        <div class="content_title">任职匹配度</div>
                        <!-- <div class="content">86%</div> -->
                    </div>
                    <div class="content_item">
                        <div class="content_title">达标人数</div>
                        <!-- <div class="content">6人</div> -->
                    </div>
                    <div class="content_item">
                        <div class="content_title">未达标人数</div>
                        <!-- <div class="content">3人</div> -->
                    </div>
                </div>
            </el-col>
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24">
                <div class="item_title">组织任职匹配详情</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentChange"
                    @handleSizeChange="handleSizeChange"
                    :tableData="tableData"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf && tableData.page.total >10">
                    更多数据请查看网页版报告
                </div>
            </el-col>
            <el-col :span="24">
                <div class="item_title">人员任职匹配详情</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentChange"
                    @handleSizeChange="handleSizeChange"
                    :tableData="tableData2"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf && tableData2.page.total >10">
                    更多数据请查看网页版报告
                </div>
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        orgTalentDiff,
        orgTalentDiffDetail,
    } from "../../../../request/api";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "orgRJobMatch",
        props: ["enqId", "orgCode", "isPdf"],
        components: { tableComps },
        data() {
            return {
                kpiRankOption: [],
                competenceRankOptions: [],
                developmentOptions: [],
                kpiCapablity: {},
                developmentCapability: [],
                size: 10,
                current: 1,
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "任职匹配目标与实际",
                        elSpan: 16,
                        height: 198,
                        chartType: "XBar",
                        dataKey: "TALENT_CLASS",
                    },
                ],
                tableData: {
                    columns: [
                        {
                            label: "组织",
                            prop: "org_name",
                        },
                        {
                            label: "组织负责人",
                            prop: "user_name",
                        },
                        {
                            label: "学历匹配",
                            prop: "post_name",
                        },
                        {
                            label: "平均工作年限",
                            prop: "talent_type",
                        },
                        {
                            label: "知识水平",
                            prop: "competence_rank",
                        },
                        {
                            label: "专业能力",
                            prop: "development_potential",
                        },
                        {
                            label: "主动性",
                            prop: "kpi_rank",
                        },
                        {
                            label: "责任心",
                            prop: "1",
                        },
                        {
                            label: "管理能力",
                            prop: "2",
                        },
                        {
                            label: "执行力",
                            prop: "3",
                        },
                        {
                            label: "团队协作",
                            prop: "4",
                        },
                        {
                            label: "创新能力",
                            prop: "5",
                        },
                        {
                            label: "综合匹配度",
                            prop: "6",
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
                tableData2: {
                    columns: [
                         {
                            label: "姓名",
                            prop: "org_name",
                        },
                        {
                            label: "岗位",
                            prop: "user_name",
                        },
                        {
                            label: "学历",
                            prop: "post_name",
                        },
                        {
                            label: "工作经验",
                            prop: "222",
                        },
                        {
                            label: "本岗位工作年限",
                            prop: "talent_type",
                        },
                        {
                            label: "知识水平",
                            prop: "competence_rank",
                        },
                        {
                            label: "专业能力",
                            prop: "development_potential",
                        },
                        {
                            label: "主动性",
                            prop: "kpi_rank",
                        },
                        {
                            label: "责任心",
                            prop: "1",
                        },
                        {
                            label: "管理能力",
                            prop: "2",
                        },
                        {
                            label: "执行力",
                            prop: "3",
                        },
                        {
                            label: "团队协作",
                            prop: "4",
                        },
                        {
                            label: "创新能力",
                            prop: "5",
                        },
                        {
                            label: "综合匹配度",
                            prop: "6",
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
            };
        },
        created() {
            // let docList = ["KPI_RANK", "COMPETENCE_RANK", "DEVELOPMENT_POTENTIAL"];
            // this.$getDocList(docList).then((res) => {
            //     this.kpiRankOption = this.$util.deepClone(res.KPI_RANK).reverse();
            //     this.competenceRankOptions = res.COMPETENCE_RANK;
            //     this.developmentOptions = res.DEVELOPMENT_POTENTIAL;
            // });
            // this.getData();
            // this.orgTalentDiffFn();
        },
        mounted() {},
        methods: {
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                    };
                    if (chart.chartDomId == "TALENT_CLASS") {
                        console.log("人才区分-人才分类");
                        console.log(chartData);
                    }
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTalentDiff(params).then((res) => {
                    // console.log(res);
                    if (res.code == "200") {
                        this.initChart(res.data);
                    }
                });
            },
            orgTalentDiffFn() {
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTalentDiffDetail(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
            handleCurrentChange(current) {
                this.current = current;
                this.orgTalentDiffFn();
            },
            handleSizeChange(size) {
                this.size = size;
                this.orgTalentDiffFn();
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .item_content {
        margin-top: 32px;
        .content_item {
            .content_title {
                margin-bottom: 16px;
            }
            .content {
                color: #0099ff;
                font-size: 16px;
                font-weight: bold;
            }
        }
    }
</style>