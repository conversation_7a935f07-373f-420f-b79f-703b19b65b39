<template>
  <div class="present public">
    <div class="table-main">
      <el-table ref="tableDataRef" :data="tableData" highlight-current-row style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="name" label="指标名称" width="180" />
        <el-table-column prop="type" label="目标类别" width="180" />
        <el-table-column prop="time" label="目标期间" />
        <el-table-column prop="unit" label="指标单位" />
        <el-table-column prop="person" label="责任人" />
        <el-table-column prop="target" label="指标目标" />
        <el-table-column prop="practical" label="实际表现" />
        <el-table-column prop="rate" label="达成率" />
        <el-table-column prop="gap" label="差距" />
        <el-table-column>
          <template #default="scope">
            <div
              class="btn"
              @click="
                openAi(
                  `${scope.row.name}、${scope.row.type}、${scope.row.time}、指标目标${scope.row.target}、实际表现${scope.row.practical}`
                )
              "
            >
              AI解读
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 已选择 -->
    <div class="active">
      <div class="title">
        <div class="text">已选指标：</div>
        <div class="name">库存周转天数</div>
      </div>
      <div class="page-title-line">同岗位人员指标表现（库存周转天数）</div>
      <el-table ref="tableDataRef2" :data="table1" highlight-current-row :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="key1" label="指标名称" />
        <el-table-column prop="key2" label="人员" />
        <el-table-column prop="key3" label="部门" />
        <el-table-column prop="key4" label="岗位" />
        <el-table-column prop="key5" label="指标目标" />
        <el-table-column prop="key6" label="实际表现" />
        <el-table-column prop="key7" label="达成率" />
        <el-table-column prop="key8" label="同组织排名" />
      </el-table>
      <div class="page-title-line">关联人员能力（周伟）</div>
      <el-table :data="table2" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="key1" label="人员能力" />
        <el-table-column prop="key2" align="center" label="相关性" />
        <el-table-column prop="key3" label="关联逻辑" width="600" />
        <el-table-column prop="key4" align="center" label="能力表现" width="100">
          <template #default="scope">
            <div class="num" :style="{ background: scope.row.key4 >= 60 ? '#40D476' : '#FF5E4C' }">
              {{ scope.row.key4 }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="page-title-line">决策风格表现（供应链规划能力）</div>
      <div class="chart-main">
        <div class="item" v-for="(item, index) in styleList">
          <div class="title">{{ index + 1 }}、{{ item.title }}</div>
          <EChartsBar :options="getOptions(item)" type="horizontal"></EChartsBar>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
const openAi = inject('openAi')
const table2 = [
  {
    key1: '供应链规划能力',
    key2: '极高',
    key3: '供应链规划决定库存结构合理性，直接影响各环节库存水位。全局性资源调配和网络设计（如仓网布局、补货周期）可系统性降低冗余库存，缩短周转周期。',
    key4: '66'
  },
  {
    key1: '需求预测分析能力',
    key2: '极高',
    key3: '精准的需求预测是库存计划的核心依据。预测偏差将导致库存积压或短缺，直接影响周转天数。动态预测能力（如滚动预测、AI模型）可显著提升供需匹配度。',
    key4: '55'
  },
  {
    key1: '库存策略制定能力',
    key2: '极高',
    key3: '制定科学的安全库存策略、ABC分类管理、呆滞品处理机制，直接影响库存周转效率。策略制定者需平衡服务水平与库存成本，避免过度囤积或频繁断货。',
    key4: '69'
  },
  {
    key1: '数据分析与建模能力',
    key2: '高',
    key3: '通过数据挖掘识别库存异常（如库龄超标SKU）、建立库存优化模型（如动态安全水位计算），为决策提供量化依据。缺乏数据驱动易导致经验主义误判。',
    key4: '69'
  },
  {
    key1: '供应链协同能力',
    key2: '高',
    key3: '跨部门/企业协同（如CPFR、VMI）可打破信息孤岛，减少牛鞭效应。协同能力不足将导致各环节局部优化，放大整体库存波动（如生产过量、采购冗余）。',
    key4: '52'
  },
  {
    key1: '采购与库存联动能力',
    key2: '高',
    key3: '采购批量、频次与库存策略的匹配度直接影响库存水位。联动能力强的采购团队能动态调整订货策略（如经济批量优化、JIT采购），避免集中到货导致的库存峰值。',
    key4: '67'
  },
  {
    key1: '生产计划协调能力',
    key2: '高',
    key3: '生产计划与库存策略的协同能力决定在制品和成品库存量。柔性生产能力（如小批量生产、快速换线）可减少生产过剩，避免因排产僵化导致的库存积压。',
    key4: '64'
  },
  {
    key1: '物流与仓储优化能力',
    key2: '中高',
    key3: '仓储作业效率（如拣货速度、库位规划）影响库存周转速度，物流时效性决定在途库存天数。但该能力更多影响操作效率，对库存策略的全局性影响弱于前几项。',
    key4: '69'
  },
  {
    key1: '跨部门沟通协调能力',
    key2: '中',
    key3: '沟通能力不足会导致信息滞后（如销售未及时共享促销计划），间接引发库存失衡。但属于支撑性能力，需通过影响其他核心能力（如预测、计划）间接作用于库存周转。',
    key4: '52'
  },
  {
    key1: '库存成本管控能力',
    key2: '中高',
    key3: '成本管控意识驱动库存精细化管理（如减少高价值物料囤积），但过度关注成本可能牺牲周转效率（如为降低采购单价而大批量订货）。需与周转目标协同优化。',
    key4: '64'
  }
]
const table1 = [
  {
    key1: '库存周转天数',
    key2: '陈宇',
    key3: '供应链部',
    key4: '副总经理兼出口计划管理',
    key5: '≤45 天',
    key6: '38 天',
    key7: '118.42%',
    key8: '1'
  },
  {
    key1: '库存周转天数',
    key2: '刘畅',
    key3: '采购部',
    key4: '采购部总经理',
    key5: '≤45 天',
    key6: '42 天',
    key7: '107.14%',
    key8: '2'
  },
  {
    key1: '库存周转天数',
    key2: '王磊',
    key3: '计划与物资部',
    key4: '计划与物资管理部部长',
    key5: '≤45 天',
    key6: '45 天',
    key7: '100.00%',
    key8: '3'
  },
  {
    key1: '库存周转天数',
    key2: '王伟',
    key3: '采购部',
    key4: '高级采购资源工程师',
    key5: '≤45 天',
    key6: '48 天',
    key7: '93.75%',
    key8: '4'
  },
  {
    key1: '库存周转天数',
    key2: '李娜',
    key3: '供应链管理部',
    key4: '供应链管理部副部长',
    key5: '≤45 天',
    key6: '50 天',
    key7: '90.00%',
    key8: '5'
  },
  {
    key1: '库存周转天数',
    key2: '赵阳',
    key3: '采购部',
    key4: '采购部副部长',
    key5: '≤45 天',
    key6: '52 天',
    key7: '86.54%',
    key8: '6'
  },
  {
    key1: '库存周转天数',
    key2: '周伟',
    key3: '计划室',
    key4: '计划室室主任',
    key5: '≤45 天',
    key6: '55 天',
    key7: '81.82%',
    key8: '7'
  }
]
const tableData = [
  {
    name: '供应商交付准时率',
    type: '年度目标',
    time: '2025年',
    unit: '%',
    person: '王伟',
    target: '≥98%',
    practical: '95%',
    rate: '97.89%',
    gap: '-2%'
  },
  {
    name: '库存周转天数',
    type: '年度目标',
    time: '2025年',
    unit: '天',
    person: '王伟',
    target: '≤45天',
    practical: '48天',
    rate: '3.75%',
    gap: '+3天'
  },
  {
    name: '供应链协同指数',
    type: '年度目标',
    time: '2025年',
    unit: '分',
    person: '王伟',
    target: '≥85分',
    practical: '82.5分',
    rate: '96.47%',
    gap: '-3分'
  },
  {
    name: '替代供应商储备率',
    type: '月度目标',
    time: '2025-05',
    unit: '%',
    person: '王伟',
    target: '≥30%',
    practical: '35%',
    rate: '93.33%',
    gap: '-2%'
  },
  {
    name: '物流成本占比',
    type: '月度目标',
    time: '2025-05',
    unit: '%',
    person: '王伟',
    target: '≤4.5%',
    practical: '4.8%',
    rate: '94.12%',
    gap: '0.5%'
  }
]
const tableDataRef = ref(null)
const tableDataRef2 = ref(null)
onMounted(() => {
  tableDataRef.value.setCurrentRow(tableData[1])
  tableDataRef2.value.setCurrentRow(table1[6])
})

const styleList = ref([
  {
    title: '决策风格偏好',
    data: [
      {
        name: '经验依赖型',
        value: 45
      },
      {
        name: '数据驱动型',
        value: 15
      },
      {
        name: '系统思考型',
        value: 15
      },
      {
        name: '机会主义型',
        value: 25
      }
    ]
  },
  {
    title: '决策风险偏好',
    data: [
      {
        name: '高风险',
        value: 40
      },
      {
        name: '中风险',
        value: 30
      },
      {
        name: '低风险',
        value: 30
      }
    ]
  },
  {
    title: '团队协作偏好',
    data: [
      {
        name: '同小组合作',
        value: 30
      },
      {
        name: '单部门协作',
        value: 20
      },
      {
        name: '跨部门协同',
        value: 20
      },
      {
        name: '战略级协同',
        value: 15
      },
      {
        name: '生态级协同',
        value: 15
      }
    ]
  },
  {
    title: '成本收益偏好',
    data: [
      {
        name: '高投入高回报',
        value: 30
      },
      {
        name: '低投入高回报',
        value: 20
      },
      {
        name: '性价比平衡',
        value: 20
      },
      {
        name: '低投入低回报',
        value: 15
      },
      {
        name: '高投入低回报',
        value: 15
      }
    ]
  }
])

const getOptions = item => {
  return {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}%'
    },
    xAxisData: item.data.map(item => item.name).reverse(),
    grid: {
      top: 10,
      bottom: 20
    },
    series: [
      {
        data: item.data.map(item => item.value).reverse(),
        type: 'bar',
        showBackground: true,
        itemStyle: {
          color: '#85E5FF'
        },
        label: {
          show: true,
          formatter: '{c}%'
        }
      }
    ]
  }
}
</script>
<style lang="scss" scoped>
@import '../indicatorImprove.scss';
</style>
