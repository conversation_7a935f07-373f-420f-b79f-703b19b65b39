<script setup>
defineOptions({ name: 'AI' })
const appList = [
  { path: '/AI/diagnosis', title: '快速了解能力诊断', desc: '了解诊断场景，查看诊断模型，了解诊断步骤以及诊断报告 。' },
  { path: '/AI/displayer', title: '能力显示器（能力显差）', desc: '能力显示器，能力显差 。 ' },
  { path: '/AI/targetSpotDiagnosis', title: '靶点诊断仪（能力诊断）', desc: '靶点诊断仪能力诊断 。' },
  { path: '/AI/analysis', title: '靶点诊断仪（能力分析）', desc: '靶点诊断仪能力分析 。' },
  { path: '/AI/nlgs', title: '靶向加速器（能力改善）', desc: '核心能力改善关键任务 。' },
  { path: '/AI/xgjc', title: '成效监控器（效果监测）', desc: '核心能力改善效果追踪 。' }
]

const router = useRouter()
const jump = item => {
  router.push(item.path)
}
</script>
<template>
  <div class="page-container">
    <div class="page-title-line">应用定位</div>
    <div class="location border p-[16px] text-[16px] mb-[32px]">
      “AI 诊断改善引擎” 是融合智能分析与闭环管理的核心能力提升模块，聚焦企业核心能力的全周期管理。通过多源数据融合与 AI
      算法建模，实现核心能力现状的精准扫描、关键短板的靶向定位、改善路径的智能规划及提升效果的动态追踪，构建 “诊断 —
      规划 — 执行 — 优化” 的能力进化闭环。
    </div>
    <div class="page-title-line">功能导航</div>
    <div class="location border p-[20px] text-[16px] mb-[32px]">
      <img class="img" src="../../assets/imgs/AI/nav-img.webp" alt="" srcset="" />
      <div class="h-[1px] bg-[#d8d8d8] mb-[20px]"></div>
      <div class="page-title-icon">
        <SvgIcon name="indicator-scene-icon"></SvgIcon>
        <div class="text">了解诊断场景</div>
      </div>
      <div class="color-[#3d3d3d] text-4">
        全面了解核心能力诊断在各业务领域的场景，包括适用的管理背景、开展能力评估的重点、能能够解决的典型管理问题，以及管理意义等，如：在市场竞争加剧、客户需求碎片化及渠道多元化的背景下，销售管理能力诊断需要聚焦：客户洞察、渠道运营、销售转化及数据驱动能力评估，精准识别：高成本低产出渠道、商机转化率断层、客户流失预警等典型问题，为企业提供：优化核心客户资源分配、迭代渠道赋能方案、建立分层销售能力矩阵等管理建议，助力实现从
        “粗放获客” 到 “精准转化 — 持续复购” 的全链路效率提升，增强销售体系对市场变化的响应能力与价值创造能力。
      </div>
    </div>
    <div class="page-title-line">应用入口</div>
    <div class="app-wrap">
      <div class="app-list bg-gradient border" @click="jump(item)" v-for="item in appList">
        <div class="app-title">{{ item.title }}</div>
        <div class="app-desc">{{ item.desc }}</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.location {
  background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
  .img {
    margin-bottom: 25px;
  }
}
.border {
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #c6dbf3;
}
.bg-gradient {
  background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), #ffffff;
}
.app-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  .app-list {
    width: calc(20% - 16px);
    padding: 20px;
    cursor: pointer;
    &:hover {
      background: url('../../assets/imgs/AI/app-bg.webp') no-repeat center center;
      border-color: #53a9f9;
      .app-title {
        color: #53a9f9;
      }
    }
    .app-title {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      margin-bottom: 6px;
      .app-desc {
        font-size: 14px;
        color: #666666;
        line-height: 22px;
      }
    }
  }
}
</style>
