import { getCurrentInstance } from 'vue'

/**
 * 自定义指令集合
 */

// 存储全局 router 实例
let _router = null

/**
 * 路由跳转指令
 * @example v-link="'/path'"
 * @example v-link="{ path: '/home', query: { id: 1 } }"
 */
export const vLink = {
  mounted(el, binding) {
    el.style.cursor = 'pointer'
    el.onclick = () => {
      if (!_router) {
        console.error('[v-link] 未找到路由实例')
        return
      }
      if (!binding.value) {
        console.warn('[v-link] 路由路径不能为空')
        return
      }
      if (typeof binding.value == 'object' || typeof binding.value == 'string') {
        _router.push(binding.value)
      } else {
        console.warn('[v-link] 无效的路由路径类型')
      }
    }
  }
}

/**
 * 数字输入限制指令
 * @example v-only-number="{ max: 100, min: 0, precision: 2 }"
 */
export const vOnlyNumber = {
  mounted(el, binding) {
    // 获取实际的input元素（支持el-input）
    const input = el.tagName.toLowerCase() == 'input' ? el : el.querySelector('input')
    if (!input) return

    const params = binding.value || {}
    const { max, min, precision = 0 } = params

    // 处理输入时的限制
    const handleKeypress = event => {
      const inputKey = String.fromCharCode(event.charCode || event.keyCode)
      const numberRegex = /\d|\./

      // 阻止非数字和小数点的输入
      if (!numberRegex.test(inputKey) && !event.ctrlKey) {
        event.preventDefault()
        return
      }

      // 阻止多个小数点的输入
      if (input.value.includes('.') && inputKey == '.') {
        event.preventDefault()
      }
    }

    // 处理值的范围限制
    const handleInput = () => {
      let value = input.value.trim()

      // 处理空值
      if (!value) {
        input.value = min || '0'
        return
      }

      // 转换为数字
      let numValue = parseFloat(value)

      // 处理最大值限制
      if (typeof max == 'number' && numValue > max) {
        numValue = max
      }

      // 处理最小值限制
      if (typeof min == 'number' && numValue < min) {
        numValue = min
      }

      // 处理精度
      if (typeof precision == 'number') {
        numValue = Number(numValue.toFixed(precision))
      }

      // 更新输入框的值
      input.value = numValue.toString()
    }

    // 绑定事件
    input.addEventListener('keypress', handleKeypress)
    input.addEventListener('input', handleInput)
    input.addEventListener('blur', handleInput)

    // 存储清理函数
    el._onlyNumberCleanup = () => {
      input.removeEventListener('keypress', handleKeypress)
      input.removeEventListener('input', handleInput)
      input.removeEventListener('blur', handleInput)
    }
  },

  // 清理事件监听
  unmounted(el) {
    el._onlyNumberCleanup?.()
  }
}

// 导出所有指令
export default {
  install(app) {
    // 存储 router 实例到模块作用域
    _router = app.config.globalProperties.$router
    app.directive('link', vLink)
    app.directive('only-number', vOnlyNumber)
  }
}
