<template>
  <div class="wrap">
    <div class="step_bar_wrap">
      <div
        class="step_item"
        v-for="(step, index) in stepData"
        :key="step.code"
        :class="{
          inProgress: step.code == currentIndex,
          completed: step[statusKey] == 'Y'
        }"
      >
        <div class="step_item_icon_wrap">
          <i class="step_item_icon">
            <i class="icon_num" :class="{ pointer: needClick }" @click="stepClick(step.code, index)">{{ index + 1 }}</i>
          </i>
        </div>
        <div class="step_text">{{ step[labelKey] }}</div>
      </div>
    </div>

    <!-- 以下为搭配使用dom 按钮 -->
    <!-- 
          <div class="talent_raview_btn_wrap">
          <el-button type="primary" @click="prev()" v-if="currentIndex > 0">上一步</el-button>
          <el-button type="primary" v-if="currentIndex == stepData.length-1">确认</el-button>
          <el-button type="primary" @click="next()" v-else>下一步</el-button>
      </div>
      -->
  </div>
</template>

<script setup>
import { computed, watch, ref } from 'vue'

const props = defineProps({
  currentIndex: {
    type: [String, Number],
    default: '0'
  },
  labelKey: {
    type: String,
    default: 'name'
  },
  statusKey: {
    type: String,
    default: 'enqProgress'
  },
  needClick: {
    type: Boolean,
    default: false
  },
  stepData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['stepClick'])

const currentIndex = ref(props.currentIndex)

watch(
  () => props.currentIndex,
  val => {
    currentIndex.value = val
  }
)

function stepClick(stepCode, index) {
  if (props.needClick) {
    emit('stepClick', stepCode, index)
  }
}

// 保留原有的 nextClick/prevClick/submitClick 逻辑（如有需要可暴露）
function nextClick() {
  // if (currentIndex.value == props.stepData.length - 1) {
  //     return false;
  // }
  // emit('next')
  // props.stepData[currentIndex.value].state = "completed"
  // currentIndex.value++
  // props.stepData[currentIndex.value].state = "inProgress"
  // console.log("下一步")
}
function prevClick() {
  // emit('prev')
  // if (currentIndex.value == 0) {
  //     return false;
  // }
  // props.stepData[currentIndex.value].state = "inComplete"
  // currentIndex.value--
  // console.log("上一步")
}
function submitClick() {
  // if (currentIndex.value == props.stepData.length - 1) {
  //     return false;
  // }
  // props.stepData[currentIndex.value].state = "completed"
  // currentIndex.value++
  // props.stepData[currentIndex.value].state = "inProgress"
  // console.log("提交")
}

// 保留 addClass 计算属性（如有需要可暴露）
const addClass = computed(() => value => {
  return {
    inComplete: value > currentIndex.value,
    inProgress: value == currentIndex.value,
    completed: value < currentIndex.value
  }
})
</script>

<style scoped lang="scss">
.step_bar_wrap {
  width: 100%;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  // padding-bottom: 16px;
  .step_item {
    flex: 1;
    position: relative;
    color: #d1d1d1;
    text-align: center;
    z-index: 2;
    &::after {
      content: '';
      position: absolute;
      width: calc(50% - 21px);
      height: 7px;
      background-color: #f1f1f1;
      top: 17px;
      right: 0;
      z-index: 1;
    }
    &::before {
      content: '';
      position: absolute;
      width: calc(50% - 21px);
      height: 7px;
      background-color: #f1f1f1;
      top: 17px;
      left: 0;
      z-index: 1;
    }
    &.completed {
      color: #449cff;
      &::after {
        background-color: #0099ff;
      }
      &::before {
        background-color: #0099ff;
      }
      .step_item_icon {
        background-color: #b0d5ff;
        .icon_num {
          background: #0099ff;
        }
      }
    }
    &.inProgress {
      color: #449cff;
      color: #92d050;
      &::after {
        background-color: #0099ff;
        // background-color: #92D050;
      }
      &::before {
        background-color: #0099ff;
        // background-color: #92D050;
      }
      .step_item_icon {
        background-color: #0099ff;
        background-color: #92d050;
        .icon_num {
          // background: #0099ff;
          background-color: #92d050;
        }
      }
    }
    .step_item_icon {
      position: relative;
      display: inline-block;
      width: 40px;
      height: 40px;
      font-size: 22px;
      line-height: 23px;
      text-align: center;
      border-radius: 50%;
      font-style: normal;
      margin-bottom: 5px;
      background-color: #e9e9e9;
      // cursor: pointer;
      .icon_num {
        position: absolute;
        display: block;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        left: 4px;
        top: 4px;
        color: #fff;
        line-height: 32px;
        font-style: normal;
        text-align: center;
        font-size: 14px;
        background-color: #d1d1d1;
      }
    }
    .el-icon-circle-check {
      font-size: 30px;
      color: #5cb87a;
      display: inline-block;
      margin-bottom: 9px;
      background-color: #f5faff;
    }
    .step_text {
      font-size: 12px;
    }
  }
}
</style>
