<template>
  <div>
    <div class="from_wrap clearfix marginT_30">
      <div class="clearfix">
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="130px"
          class="rule_form"
          label-position="left"
        >
          <el-form-item :class="{ event_none: !isEdit }" label="盘点起止日期" prop="copyDate">
            <el-date-picker
              v-model="ruleForm.copyDate"
              type="daterange"
              value-format="YYYY-MM-DD"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="盘点项目名称" prop="enqName">
            <el-input v-model="ruleForm.enqName"></el-input>
          </el-form-item>
        </el-form>
        <div class="line_box"></div>
        <div class="center_right">
          <div class="review_content"></div>
        </div>
      </div>
      <div class="review_main_wrap" :class="{ event_none: !isEdit }">
        <div class="page_second_title">盘点内容选择</div>
        <div class="review_main_select marginT_20">
          <div class="title fs14">个人盘点要素</div>
          <div class="factor_item_wrap flex_row_wrap_start">
            <div
              class="factor_item"
              @click="selectEnqModule('personModule', index)"
              :class="{
                event_none: item.enqDefault == 'Y',
                enqSelected: item.inEnq == 'Y'
              }"
              v-for="(item, index) in personModule"
              :key="item.enqModuleCode"
            >
              <span class="overflow_elps" :title="item.enqModuleName">{{ item.enqModuleName }}</span>
            </div>
          </div>
          <div class="selectModel paddT_12" v-show="showSelectModel">
            选择调研模板
            <el-select v-model="surveyModelId" placeholder="请选择调研模板">
              <el-option
                v-for="item in modelOptions"
                :key="item.modelId"
                :label="item.modelName"
                :value="item.modelId"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="review_main_select marginT_20">
          <div class="title fs14">部门盘点要素</div>
          <div class="factor_item_wrap flex_row_wrap_start">
            <div
              class="factor_item"
              @click="selectEnqModule('deptModule', index)"
              :class="{
                event_none: item.enqDefault == 'Y',
                enqSelected: item.inEnq == 'Y'
              }"
              v-for="(item, index) in deptModule"
              :key="item.enqModuleCode"
            >
              <span class="overflow_elps" :title="item.enqModuleName">{{ item.enqModuleName }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="btn_wrap align_center" v-if="isEdit">
        <el-button class="page_confirm_btn" type="primary" @click="submit">下一步</el-button>
      </div>
      <div class="btn_wrap align_center" v-if="!isEdit">
        <el-button class="page_confirm_btn" type="primary" @click="submitEdit">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useEnqStore } from '@/views/enq/store'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getCeEnqModule,
  getDictList,
  createReview,
  getAllUser,
  getEnqInfo,
  getCeEnqModuleById,
  getModelInfo
} from '../../../request/api'

const props = defineProps({
  getEnqId: {
    type: Function,
    required: true
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['nextStep'])

const router = useRouter()
const userStore = useUserStore()
const enqStore = useEnqStore()

const ruleFormRef = ref(null)
const adminOptions = ref([])
const loading = ref(false)
const enqTypeOptions = ref([])
const enqTypeDesc = ref({})
const deptModule = ref([])
const personModule = ref([])
const surveyModelId = ref(null)
const surveyStatus = ref(false)
const modelOptions = ref([])
const showSelectModel = ref(false)

const ruleForm = ref({
  copyDate: [],
  beginDate: '',
  endDate: '',
  enqName: '',
  enqType: '',
  adminUserName: '',
  adminUserId: '',
  companyId: '',
  enqItems: 0,
  enqId: null
})

const rules = {
  copyDate: [
    {
      required: true,
      message: '请选择日期',
      trigger: 'change'
    }
  ],
  enqName: [
    {
      required: true,
      message: '请输入盘点项目名称',
      trigger: 'blur'
    }
  ],
  enqType: [
    {
      required: true,
      message: '请选择盘点类型',
      trigger: 'change'
    }
  ]
}

const enqId = computed(() => props.getEnqId())
const isEdit = computed(() => props.isEdit)
const companyId = computed(() => userStore.userInfo.companyId)

// 获取数据字典 -- 盘点类型
const getDocList = async () => {
  const res = await getDictList({ dictIds: 'ENQ_TYPE' })
  enqTypeOptions.value = res.data.ENQ_TYPE
  enqTypeOptions.value.forEach(item => {
    const code = item['dictCode']
    enqTypeDesc.value[code] = item.codeDesc
  })
}

const getCeEnqModuleData = async () => {
  const res = await getCeEnqModule()
  if (res.code == 200) {
    res.data.personModule.forEach(item => {
      item['inEnq'] = item.enqDefault == 'Y' ? 'Y' : 'N'
    })
    res.data.deptModule.forEach(item => {
      item['inEnq'] = item.enqDefault == 'Y' ? 'Y' : 'N'
    })
    personModule.value = res.data.personModule
    deptModule.value = res.data.deptModule
    calcEnqModuleLength()
  }
}

const selectEnqModule = (type, index) => {
  const row = type == 'personModule' ? personModule.value[index] : deptModule.value[index]
  const code = row['enqModuleCode']
  const inEnq = row['inEnq']
  row['inEnq'] = inEnq == 'Y' ? 'N' : 'Y'

  if (code == 'P09') {
    if (inEnq == 'N') {
      surveyStatus.value = true
      showSelectModel.value = true
      if (modelOptions.value.length == 0) {
        getModelInfoFun()
      }
    } else {
      surveyStatus.value = false
      showSelectModel.value = false
      surveyModelId.value = null
    }
  }

  // 个人盘点的个人工作选择一个都点亮
  if (type == 'personModule' && code == 'PN06') {
    if (personModule.value[index]['inEnq'] == 'Y') {
      personModule.value.forEach(element => {
        element.inEnq = 'Y'
      })
    }
  }

  //部门盘点的工作饱和度评价选择一个都点亮
  if (type == 'deptModule' && code == 'DN07') {
    if (deptModule.value[index]['inEnq'] == 'Y') {
      deptModule.value.forEach(element => {
        element.inEnq = 'Y'
      })
      personModule.value.forEach(element => {
        element.inEnq = 'Y'
      })
    }
  }
  calcEnqModuleLength()
}

const getModelInfoFun = async () => {
  const res = await getModelInfo()
  modelOptions.value = res.data
}

const calcEnqModuleLength = () => {
  setTimeout(() => {
    ruleForm.value.enqItems = document.querySelectorAll('.review_main_select .enqSelected').length
  }, 500)
}

const submit = async () => {
  if (!ruleFormRef.value) return
  ruleForm.value.companyId = companyId.value

  await ruleFormRef.value.validate(async valid => {
    if (valid) {
      if (surveyStatus.value && surveyModelId.value == null) {
        ElMessage.error('请选择调研模板！')
        return
      }
      await createReviewSubmit()
    }
  })
}

const submitEdit = async () => {
  await submit()
}

const createReviewSubmit = async () => {
  const params = {
    ...ruleForm.value,
    companyId: companyId.value,
    enqId: enqId.value,
    beginDate: ruleForm.value.copyDate[0],
    endDate: ruleForm.value.copyDate[1],
    enqModuleRequests: personModule.value.concat(deptModule.value),
    surveyModelId: surveyModelId.value
  }

  try {
    const res = await createReview(params)
    if (res.code == 200) {
      enqStore.setCreateEnqId(res.data)
      ElMessage.success(res.msg)
      emit('nextStep')
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const remoteMethod = async query => {
  if (query == '') {
    adminOptions.value = []
    return
  }

  loading.value = true
  try {
    const params = {
      id: companyId.value,
      userName: query
    }
    const res = await getAllUser(params)
    adminOptions.value = res.data
  } catch (error) {
    ElMessage.error(error.message)
  } finally {
    loading.value = false
  }
}

const adminChange = value => {
  ruleForm.value.adminUserId = value.userId
  ruleForm.value.adminUserName = value.userName
}

const getEnqInfoById = async id => {
  try {
    const res = await getEnqInfo({ id })
    if (res.code == 200) {
      const data = res.data
      surveyModelId.value = data.surveyModelId
      ruleForm.value.copyDate = [data.beginDate, data.endDate]

      Object.keys(ruleForm.value).forEach(key => {
        if (data[key] !== undefined) {
          ruleForm.value[key] = data[key]
        }
      })
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const getCeEnqModuleByIdFun = async id => {
  try {
    const res = await getCeEnqModuleById({ id })
    if (res.code == 200) {
      personModule.value = res.data.personalEnqModule
      deptModule.value = res.data.departEnqModule
      calcEnqModuleLength()

      personModule.value.forEach(item => {
        const code = item.enqModuleCode
        const inEnq = item.inEnq
        if (code == 'P09' && inEnq == 'Y') {
          getModelInfoFun()
          showSelectModel.value = true
        }
      })
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

onMounted(async () => {
  await getDocList()
  console.log(enqId.value)

  if (enqId.value) {
    // 二次进入
    await getEnqInfoById(enqId.value)
    await getCeEnqModuleByIdFun(enqId.value)
  } else {
    // 初次进入--创建
    await getCeEnqModuleData()
  }
})
</script>

<style scoped lang="scss">
.from_wrap {
  .rule_form {
    float: left;
    width: 39%;

    .enqTypeTip {
      position: absolute;
      right: 20px;
      top: 0;
      background-color: #ebf4ff;
      line-height: 20px;
      font-size: 12px;
      width: 210px;
      color: #525e6c;
      padding: 2px;
    }
  }

  margin-right: 16px;
  .el-form-item__content {
    width: calc(39% - 130px);
  }
  .el-date-editor,
  .el-input,
  .el-select {
    width: 100%;
  }
}

.review_main_wrap {
  .review_main_title {
    margin-bottom: 8px;
    color: #606266;
  }

  .review_main_select {
    margin-bottom: 16px;

    .title {
      line-height: 34px;
      margin-bottom: 8px;
      border-bottom: 1px solid #0099fd;
    }

    .factor_item {
      position: relative;
      width: 150px;
      height: 34px;
      line-height: 34px;
      margin: 0 8px 8px 0;
      border: 1px solid #e5e5e5;
      border-radius: 2px;
      padding: 0 8px;
      color: #525e6c;
      cursor: pointer;

      span {
        display: block;
        width: 110px;
        text-align: center;
      }

      &.enqSelected {
        color: #0099ff;
        border-color: #0099ff;
        background: #ebf4ff;

        &::after {
          content: '';
          display: block;
          position: absolute;
          right: 5px;
          top: 7px;
          width: 16px;
          height: 8px;
          border: 2px solid #0099ff;
          transform: rotate(-45deg);
          border-right: none;
          border-top: none;
        }
      }
    }
  }
}

.line_box {
  float: left;
  width: 2%;
  height: 100px;
  .line {
    margin: 0 auto;
    width: 1px;
    height: 100%;
    background-color: #ccc;
  }
}

.center_right {
  float: left;
  width: calc(59% - 5px);
  overflow: hidden;

  .review_content {
    .title_box {
      padding: 10px 0;
      color: #0099fd;
      font-weight: bold;
    }
    .item_box {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .item {
        width: 130px;
        height: 60px;
        border: 1px solid #e5e5e5;
        text-align: center;
        padding: 10px;
        margin-right: 10px;
        border-radius: 2px;
        color: #555;
        .title {
          font-size: 14px;
        }

        .text {
          font-size: 20px;
          color: #0099fd;
          font-weight: bold;
        }
      }
    }
  }
}

.el-date-editor .el-range-separator {
  padding: 0;
}

.el-form-item {
  margin-bottom: 35px;
}
</style>
