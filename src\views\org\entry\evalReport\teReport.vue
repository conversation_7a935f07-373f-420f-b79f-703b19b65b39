<script setup>
import SectionTab from '../../components/sectionTab.vue'
const router = useRouter()
const route = useRoute()

const sectionTabCheckSign = ref(1)
const sectionTabList = ref([
  {
    name: '组织报告',
    code: 1
  },
  {
    name: '个人报告',
    code: 2
  }
])
const leftMenu1 = ref([
  {
    name: '团队能力画像',
    code: 'A',
    children: [
      {
        name: '整体能力表现',
        code: 1
      },
      {
        name: '能力优势与短板',
        code: 2
      },
      {
        name: '团队能力图谱',
        code: 3
      }
    ]
  },
  {
    name: '管理风格特征分析',
    code: 'B',
    children: [
      {
        name: '决策偏好分析',
        code: 11
      },
      {
        name: '风险偏好分析',
        code: 12
      },
      {
        name: '团队协作偏好',
        code: 13
      },
      {
        name: '成本收益偏好',
        code: 14
      },
      {
        name: '专项能力分析',
        code: 15
      },
      {
        name: '专项能力综合表现',
        code: 16
      },
      {
        name: '能力影响与改善',
        code: 17
      }
    ]
  },
  {
    name: '团队人才发展建议',
    code: 'C',
    children: [
      {
        name: '决策层级匹配',
        code: 21
      },
      {
        name: '业务场景匹配',
        code: 22
      },
      {
        name: '能力提升路线图',
        code: 23
      },
      {
        name: '资源投入优先级',
        code: 24
      },
      {
        name: '团队人才调配建议',
        code: 25
      }
    ]
  }
])
const leftMenu2 = ref([
  {
    name: '个人能力画像',
    code: 'A',
    children: [
      {
        name: '整体能力表现',
        code: 1
      },
      {
        name: '能力优势与短板',
        code: 2
      },
      {
        name: '核心能力排名',
        code: 3
      }
    ]
  },
  {
    name: '管理风格特征分析',
    code: 'B',
    children: [
      {
        name: '决策偏好分析',
        code: 11
      },
      {
        name: '风险偏好分析',
        code: 12
      },
      {
        name: '团队协作偏好',
        code: 13
      },
      {
        name: '成本收益偏好',
        code: 14
      }
    ]
  },
  {
    name: '能力提升地图',
    code: 'C',
    children: [
      {
        name: '短板提升项',
        code: 21
      },
      {
        name: '优势发展项',
        code: 22
      },
      {
        name: '推荐学习资源',
        code: 23
      }
    ]
  },
  {
    name: '人才发展建议',
    code: 'D',
    children: [
      {
        name: '人岗匹配建议',
        code: 31
      },
      {
        name: '能力提升建议',
        code: 32
      }
    ]
  }
])

const leftMenuCheckSign = ref(2)
const leftMenu2CheckSign = ref(2)
const dxwt = ref([
  {
    title: '明确发展方向：',
    info: '如果整体得分较高且处于较高能力阶段，说明企业在当前业务领域具备较强的竞争力，可考虑进行市场扩张、多元化发展等战略。若整体得分较低或处于较低能力阶段，企业则需要专注于提升核心能力，弥补短板，可能需要收缩战线，集中资源解决关键问题。  '
  },
  {
    title: '设定战略目标：',
    info: '依据整体得分和能力阶段，制定具体的、可衡量的战略目标。如处于能力提升阶段的企业，目标可以是在一定时间内将整体得分提高到某个水平，达到下一个能力阶段。'
  }
])

const checkSecTab = c => {
  sectionTabCheckSign.value = c
}

const leftMenuCheck = c => {
  leftMenuCheckSign.value = c
}
const leftMenu2Check = c => {
  leftMenu2CheckSign.value = c
}
</script>
<template>
  <div class="teReport_wrap">
    <div class="mt-5">
      <SectionTab
        :sectionTabList="sectionTabList"
        :sectionTabCheckSign="sectionTabCheckSign"
        :itemWidth="'13.2%'"
        @checkSecTab="checkSecTab"
      ></SectionTab>
    </div>
    <div class="teReport_main justify-start" v-if="sectionTabCheckSign == 1">
      <div class="left_menu_wrap">
        <div class="title">测评报告内容</div>
        <div class="item_wrap" v-for="it in leftMenu1">
          <div class="name">{{ it.name }}</div>
          <div
            class="item_c_wrap"
            :class="{ item_c_act: leftMenuCheckSign == item.code }"
            v-for="item in it.children"
            @click="leftMenuCheck(item.code)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="teReport_right_wrap">
        <div class="page-title-line">整体能力表现含义</div>
        <div class="section_box_wrap">
          基于团队所有成员的所有题目得分率，呈现团队能力达标率、高/低分段分布比例，用于发现团队整体能力水位，并定位急需提升的能力层级
        </div>
        <div class="chart_section_wrap justify-between">
          <div class="l_chart_section chart_section_item">
            <div class="page-title-line">报告内容示例1</div>
            <div class="section_box_wrap justify-start">
              <div class="chart_box_wrap">
                <div class="title">整体得分</div>
                <div class="chart_box"></div>
              </div>
              <div class="chart_box_wrap">
                <div class="title">能力阶段</div>
                <div class="chart_box"></div>
              </div>
            </div>
          </div>
          <div class="chart_section_item r_chart_section">
            <div class="page-title-line">报告内容示例2</div>
            <div class="section_box_wrap">
              <div class="chart_box"></div>
            </div>
          </div>
        </div>
        <div class="page-title-line">如何使用该项评估结果</div>
        <div class="section_box_wrap dot_content_wrap">
          <div class="item_wrap" v-for="item in dxwt">
            <span class="title">{{ item.title }}</span>
            <span class="info">{{ item.info }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="teReport_main justify-start" v-if="sectionTabCheckSign == 2">
      <div class="left_menu_wrap">
        <div class="title">测评报告内容</div>
        <div class="item_wrap" v-for="it in leftMenu2">
          <div class="name">{{ it.name }}</div>
          <div
            class="item_c_wrap"
            :class="{ item_c_act: leftMenu2CheckSign == item.code }"
            v-for="item in it.children"
            @click="leftMenu2Check(item.code)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="teReport_right_wrap">
        <div class="page-title-line">整体得分与所处阶段</div>
        <div class="section_box_wrap">
          基于兮易AI测评技术，从多个维度对核心能力进行评估，为每一项测评生成专业的测评整体得分，并进行能力阶段的定位划分；整体得分直观反映企业综合能力水平，高分意味着企业在各关键能力领域表现出色，运营效率高，市场竞争力强；低分则表明企业存在较多问题，在很多方面需要改进，运营效率亟待提升。
        </div>
        <div class="chart_section_wrap justify-between">
          <div class="l_chart_section chart_section_item">
            <div class="page-title-line">报告内容示例1</div>
            <div class="section_box_wrap justify-start">
              <div class="chart_box_wrap">
                <div class="title">整体得分</div>
                <div class="chart_box"></div>
              </div>
              <div class="chart_box_wrap">
                <div class="title">能力阶段</div>
                <div class="chart_box"></div>
              </div>
            </div>
          </div>
          <div class="chart_section_item r_chart_section">
            <div class="page-title-line">报告内容示例2</div>
            <div class="section_box_wrap">
              <div class="chart_box"></div>
            </div>
          </div>
        </div>
        <div class="page-title-line">如何使用该项评估结果</div>
        <div class="section_box_wrap dot_content_wrap">
          <div class="item_wrap" v-for="item in dxwt">
            <span class="title">{{ item.title }}</span>
            <span class="info">{{ item.info }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../../style/common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.teReport_wrap {
  .teReport_main {
    .left_menu_wrap {
      margin-right: 20px;
      padding: 12px 10px;
      width: 198px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #a5c1dc;
      .item_wrap {
        text-align: center;
        .name {
          height: 35px;
          line-height: 35px;
          color: #333333;
        }
        .item_c_wrap {
          margin: 0 auto;
          width: 178px;
          height: 35px;
          line-height: 35px;
          font-size: 14px;
          border-radius: 5px 5px 5px 5px;
          cursor: pointer;
          &.item_c_act {
            border: 1px solid #53a9f9;
            color: #53a9f9;
          }
        }
      }
    }
    .teReport_right_wrap {
      flex: 1;
      .chart_section_wrap {
        .chart_section_item {
          width: 49.5%;
        }
        .l_chart_section {
          .chart_box_wrap {
            width: 49%;
            .title {
              width: 253px;
              height: 30px;
              line-height: 30px;
              text-align: center;
              color: #40a0ff;
              background: #eaf4ff;
              border-radius: 51px 51px 51px 51px;
            }
            .chart_box {
              width: 100%;
              height: 200px;
            }
          }
        }
        .r_chart_section {
          .chart_box {
            width: 100%;
            height: 230px;
          }
        }
      }
      .dot_content_wrap {
        .item_wrap {
          .title {
            margin: 0 10px 0 0px !important;
          }
        }
      }
    }
  }
}
</style>
