import qs from 'qs'
import service from '@/api/request' // 引入axios封装

// 并发请求
export const requestAll = arr => Promise.all(arr)

// 通用action
export const setActionType = data => service({ url: `/tenantEval/${data.url}`, method: 'post', data: data.data })

// ********************************获取数据字典列表
export const getDictItem = data => service({ url: `/entp/dict`, method: 'get', params: data })
export const getDictList = data => service({ url: `/tenantEval/dict/list`, method: 'get', params: data })

// 获取报告图表信息
export const getReportChartData = data => service({ url: `/tenantEval/${data.url}`, method: 'get', params: data.data })

// 下载
export const fileDownload = data =>
  service({ url: `/tenantEval/attach/download`, method: 'get', params: data, responseType: 'blob' })

//模型管理-------------------------------------------

// --------模型中心-------
// 获取平台模型列表
export const getPlatformModelList = data =>
  service({ url: `/tenantEval/model/getPlatformModelList`, method: 'get', params: data })
// 获取平台开通模型列表
export const getPlatformOpenModelList = data =>
  service({ url: `/tenantEval/model/getPlatformOpenModelList`, method: 'get', params: data })
// 获取模型信息
export const getPlatformModelInfo = data => service({ url: `/tenantEval/model/getModel`, method: 'get', params: data })
// 获取能力分类/获取词典能力
export const getPlatformModelChartData = data =>
  service({ url: `/tenantEval/model/getModelModule`, method: 'get', params: data })

// 模型列表
export const getModelList = data => service({ url: `/tenantEval/model/getModelList`, method: 'get', params: data })

//获取业务类型
export const getModelType = data => service({ url: `/tenantEval/model/getModelType`, method: 'get', params: data })

//新增模型基本信息
export const createModelInfo = data =>
  service({
    url: `/tenantEval/model/createModel`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
//获取模型基本信息
export const getModelInfo = data => service({ url: `/tenantEval/model/getModel`, method: 'get', params: data })
//删除模型
export const deleteModel = data => service({ url: `/tenantEval/model/deleteModel`, method: 'delete', params: data })
// 获取模块列表
export const getCapabilityCategory = data =>
  service({ url: `/tenantEval/model/getCapabilityCategory`, method: 'get', params: data })
// 获取词典列表
export const getCompetenceDictionary = data =>
  service({ url: `/tenantEval/model/getCompetenceDictionary`, method: 'get', params: data })

//新增能力分类/词典能力
export const createModelModule = data => service({ url: `/tenantEval/model/createModelModule`, method: 'post', data })
//获取能力分类/获取词典能力
export const getModelModule = data => service({ url: `/tenantEval/model/getModelModule`, method: 'get', params: data })
//刪除能力分类/词典能力
export const deleteModelModule = data =>
  service({ url: `/tenantEval/model/deleteModelModule`, method: 'delete', params: data })

//新增分级标准
export const createModelOption = data => service({ url: `/tenantEval/model/createModelOption`, method: 'post', data })
//获取分级标准
export const getModelGrade = data => service({ url: `/tenantEval/model/getModelGrade`, method: 'get', params: data })
//刪除分级标准
export const deleteModelOption = data =>
  service({ url: `/tenantEval/model/deleteModelOption`, method: 'delete', params: data })
//获取最后一次结束分值
export const getGradeLastScore = data =>
  service({ url: `/tenantEval/model/getGradeLastScore`, method: 'get', params: data })
//数据校验
export const gradeCheck = data => service({ url: `/tenantEval/model/gradeCheck`, method: 'get', params: data })

//预览确认
export const modelInfoPreview = data =>
  service({ url: `/tenantEval/model/modelInfoPreview`, method: 'get', params: data })

//更新模型状态
export const updateModelStatus = data =>
  service({
    url: `/tenantEval/model/updateModelStatus`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//岗能建模---------------------------------------
//建模列表
export const getModelPageList = data => service({ url: `/tenantEval/model/page`, method: 'get', params: data })

//获取所有的组织职群和职层，部门等信息
export const getFilter = data => service({ url: `/tenantEval/model/getFilter`, method: 'get', params: data })

//根据职层，职群，部门返回岗位信息
export const getActivePost = data =>
  service({
    url: `/tenantEval/model/getActivePost`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//根据职群职层部门返回建模人员
export const selectModelUser = data =>
  service({ url: `/tenantEval/model/selectModelUser`, method: 'get', params: data })

//岗能建模提交
export const gangnengModelin = data => service({ url: `/tenantEval/model/gangnengModelin`, method: 'post', data })

//建模进度
export const getModelProgress = data => service({ url: `/tenantEval/model/modelProgress`, method: 'get', params: data })
//进入模型确认
export const modelStayConfirm = data =>
  service({
    url: `/tenantEval/model/modelStayConfirm`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//  参与建模
export const getUserModel = data => service({ url: `/tenantEval/model/getUserModel`, method: 'get', params: data })

//模型确认列表
export const getConfirmModelPageList = data =>
  service({ url: `/tenantEval/model/getModelConfirm`, method: 'get', params: data })

//查询确认能力词典
export const getConfirmModuleDict = data =>
  service({ url: `/tenantEval/model/getConfirmModuleBuildModule`, method: 'get', params: data })

//确认能力词典
export const confirmModuleBuildModule = data =>
  service({
    url: `/tenantEval/model/confirmModuleBuildModule`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
//确认能力词典下一步
export const confirmModuleBuildModuleNextStep = data =>
  service({
    url: `/tenantEval/model/confirmModuleBuildModuleNextStep`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//查询确认能力目标
export const getConfirmModuleTarget = data =>
  service({ url: `/tenantEval/model/getConfirmModuleTarget`, method: 'get', params: data })

//修改确认能力目标
export const setConfirmModuleTarget = data =>
  service({ url: `/tenantEval/model/confirmModuleTarget`, method: 'put', data })
//下载模板
export const downloadExcelFile = data =>
  service({ url: `/tenantEval/model/downloadExcelFile`, method: 'get', params: data, responseType: 'blob' })
//上传数据
export const importData = data =>
  service({
    url: `/tenantEval/model/importData`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })

//确认能力目标点击下一步
export const confirmModuleTargetNextStep = data =>
  service({
    url: `/tenantEval/model/confirmModuleTargetNextStep`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//获取确认最终模型页面
export const getConfirmFinalModel = data =>
  service({ url: `/tenantEval/model/getConfirmFinalModel`, method: 'get', params: data })

//确认最终模型
export const confirmModelBuild = data =>
  service({
    url: `/tenantEval/model/confirmModelBuild`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

export const getTwolevelTarget = data =>
  service({ url: `/tenantEval/model/getTwolevelTarget`, method: 'get', params: data })

//------------------------测评-----------------------
// 获取action状态
export const getAction = data => service({ url: `/tenantEval/evaluation/getAction`, method: 'get', params: data })
//获取测评列表
export const getEvalList = data => service({ url: `/tenantEval/evaluation/getEvalList`, method: 'get', params: data })
//获取测评基本信息
export const getEvalInfo = data => service({ url: `/tenantEval/evaluation/getEvalInfo`, method: 'get', params: data })
//获取测评模型
export const getModelCheckList = data =>
  service({ url: `/tenantEval/evaluation/getModelInfo`, method: 'get', params: data })
// 模块列表
export const getCompetenceModule = data =>
  service({ url: `/tenantEval/model/getCompetenceModule`, method: 'get', params: data })

// 词典弹窗模块下拉
export const moduleList = data => service({ url: `/tenantEval/model/getCompetenceModule`, method: 'get', params: data })

//创建测评基本信息
export const createEvalInfo = data => service({ url: `/tenantEval/evaluation/createEval`, method: 'post', data })

//获取测评对象
export const getEvalPost = data => service({ url: `/tenantEval/evaluation/getEvalPost`, method: 'get', params: data })
// 导出参评人员模板
export const exportSheet = data =>
  service({ url: `/tenantEval/evaluation/exportEvalPostObject`, method: 'get', params: data, responseType: 'blob' })
// 导入测评对象
export const importEvalPostObject = data =>
  service({
    url: `/tenantEval/evaluation/importEvalPostObject`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })

//保存测评对象
export const setUpEvalUserObject = data =>
  service({ url: `/tenantEval/evaluation/setUpEvalUserObject`, method: 'post', data })

export const getRelationInfo = data =>
  service({ url: `/tenantEval/evaluation/getRelationInfo`, method: 'get', params: data })

//被评岗位列表
export const getEvalPostList = data =>
  service({ url: `/tenantEval/evaluation/getEvalPostInfo`, method: 'get', params: data })

// ------------------参与建模--------------
// 获取答题信息
export const getItemInfo = data => service({ url: `/tenantEval/evaluation/getItemInfo`, method: 'get', params: data })
// 获取用户答题对应模块
export const getUserModule = data =>
  service({ url: `/tenantEval/evaluation/getUserModule`, method: 'get', params: data })
// 保存答题记录
export const saveEvalSubmit = data => service({ url: `/tenantEval/evaluation/saveEvalSubmit`, method: 'post', data })
// 答题提交
export const answerSubmit = data =>
  service({
    url: `/tenantEval/evaluation/itemSubmit`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//  人才测评-参与建模

//获取测评对象岗位对应参评岗位的评价关系
export const getEvalRelation = data =>
  service({ url: `/tenantEval/evaluation/getEvalRelation`, method: 'get', params: data })
export const getEvalPostObject = data =>
  service({ url: `/tenantEval/evaluation/getEvalPostObject`, method: 'get', params: data })

//获取测评对象岗位对应模块
export const getPostModule = data =>
  service({ url: `/tenantEval/evaluation/getPostModule`, method: 'get', params: data })
// 更新测评单人时间
export const updateEvalInfo = data =>
  service({
    url: `/tenantEval/evaluation/updateEvalInfo`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//保存参评人员对应测评对象关系
export const saveUserObjectRelation = data =>
  service({ url: `/tenantEval/evaluation/saveUserObjectRelation`, method: 'post', data })

//保存评价关系权重
export const saveEvalRelation = data =>
  service({ url: `/tenantEval/evaluation/saveEvalRelation`, method: 'post', data })

//导出评价关系模板
export const exportRelation = data =>
  service({ url: `/tenantEval/evaluation/exportRelation`, method: 'get', params: data, responseType: 'blob' })

//查询报告范围
export const getReportScope = data =>
  service({ url: `/tenantEval/evaluation/getReportScope`, method: 'get', params: data })

//保存报告范围
export const saveReportScope = data =>
  service({
    url: `/tenantEval/evaluation/saveReportScope`,
    method: 'put',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//岗位评价关系导入功能
export const importRelation = data =>
  service({
    url: `/tenantEval/evaluation/importRelation`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })

export const getEvalConfigInfo = data =>
  service({ url: `/tenantEval/evaluation/getEvalConfigInfo`, method: 'get', params: data })

//启动测评
export const startUpEval = data =>
  service({
    url: `/tenantEval/evaluation/startUpEval`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

export const getEvalUserInfo = data =>
  service({ url: `/tenantEval/evaluation/getEvalUserInfo`, method: 'get', params: data })
// 判断开始答题跳转的页面
export const getDevelopmentFlag = data =>
  service({ url: `/tenantEval/evaluation/getDevelopmentFlag`, method: 'get', params: data })

// 测评报告列表
export const evaluationReportList = data =>
  service({ url: `/tenantEval/report/evaluationReportList`, method: 'get', params: data })
// 部门报告
export const queryEvalReportList = data =>
  service({ url: `/tenantEval/report/queryEvalReportList`, method: 'get', params: data })
// 部门报告
export const queryEvalUserReportList = data =>
  service({ url: `/tenantEval/report/queryEvalUserReportList`, method: 'get', params: data })

//获取报告模板图表集合
export const getRptChartList = data =>
  service({ url: `/tenantEval/report/getRptChartList`, method: 'get', params: data })
//报告图表上传
export const uploadRptChart = data => service({ url: `/tenantEval/report/uploadRptChart`, method: 'post', data })

//生成pdf
export const genReportPdf = data =>
  service({
    url: `/tenantEval/report/genReportPDF`,
    method: 'post',
    data: qs.stringify(data),
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })

//pdf下载
export const downloadReportPdf = data =>
  service({ url: `/tenantEval/report/download`, method: 'get', params: data, responseType: 'blob' })

// 测评分析

// 整体
export const wholeComprehensiveScore = data =>
  service({ url: `/tenantEval/evalAnalysis/wholeComprehensiveScore`, method: 'get', params: data })
// 模块综合得分
export const modularComprehensiveScore = data =>
  service({ url: `/tenantEval/evalAnalysis/modularComprehensiveScore`, method: 'get', params: data })
// 能力词典得分
export const competenceDictionaryScore = data =>
  service({ url: `/tenantEval/evalAnalysis/competenceDictionaryScore`, method: 'get', params: data })

// 部门匹配度
export const departmentMatchingDegree = data =>
  service({ url: `/tenantEval/evalAnalysis/departmentMatchingDegree`, method: 'get', params: data })
// 岗位匹配度
export const jobMatchingDegree = data =>
  service({ url: `/tenantEval/evalAnalysis/jobMatchingDegree`, method: 'get', params: data })

// 人员匹配度
export const personnelMatchingDegree = data =>
  service({ url: `/tenantEval/evalAnalysis/personnelMatchingDegree`, method: 'get', params: data })

// 评价关系
// 360
export const assessment = data => service({ url: `/tenantEval/evalAnalysis/assessment`, method: 'get', params: data })
// 差距
export const poorEvaluation = data =>
  service({ url: `/tenantEval/evalAnalysis/poorEvaluation`, method: 'get', params: data })
// 认知偏差
export const cognitiveBias = data =>
  service({ url: `/tenantEval/evalAnalysis/cognitiveBias`, method: 'get', params: data })
// 评价关系详情
export const relationshipDetails = data =>
  service({ url: `/tenantEval/evalAnalysis/relationshipDetails`, method: 'get', params: data })
  
export const getRelationUser = data =>
  service({ url: `/tenantEval/evaluation/getRelationUser`, method: 'get', params: data })
   // 保存评价下级关系
export const relationConfirm = data =>
  service({ url: `/tenantEval/evaluation/relationConfirm`, method: 'post', data: data,headers: {
    'Content-Type': 'application/json;chartset=utf-8'
  }, })
