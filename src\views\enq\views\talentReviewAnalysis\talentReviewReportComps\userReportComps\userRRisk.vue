<template>
    <div class="report_section userR_risk_wrap">
        <div class="page_second_title">离职风险</div>
        <div class="userR_risk_main">
            <div class="top_wrap marginB_32 flex_row_start">
                <div class="top_left_wrap">
                    <div class="page_third_title">
                        <span>离职风险指数</span>
                    </div>
                    <div class="top_left_main flex_row_betweens">
                        <customProcess
                            :size="100"
                            :fontSize="20"
                            :strokeWidth="12"
                            :num="retentionRisk.score"
                        />
                    </div>
                </div>
                <div class="top_center_wrap top_classify_wrap">
                    <div class="page_third_title">离职可能性</div>
                    <div class="top_center_main flex_row_start">
                        <!-- <p class="degree">中</p> -->
                        <p class="desc two_overflow_elps">
                            {{ retentionRisk.POSSIBILITY }}
                        </p>
                    </div>
                </div>
                <div class="top_center_wrap top_classify_wrap">
                    <div class="page_third_title">离职对业务的影响</div>
                    <div class="top_center_main flex_row_start">
                        <!-- <p class="degree">中</p> -->
                        <p class="desc two_overflow_elps">
                            {{ retentionRisk.IMPACT }}
                        </p>
                    </div>
                </div>
                <div class="top_right_wrap top_classify_wrap">
                    <div class="page_third_title">
                        <span>人员是否可替代</span>
                    </div>
                    <div class="top_center_main flex_row_start">
                        <!-- <p class="degree">否</p> -->
                        <p class="desc two_overflow_elps">
                            {{ retentionRisk.PERSONNEL }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="bottom_wrap marginB_32">
                <div class="page_third_title">
                    <span>离职风险因素</span>
                </div>
                <div class="report_section_content">
                    <div class="chart_box factor" :id="ristChart"></div>
                </div>
            </div>
            <div class="bottom_wrap marginB_32">
                <div class="page_third_title">
                    <span>具体表现</span>
                </div>
                <div class="reault_expression_item">
                    <tableComponent
                        :tableData="tableData"
                        :border="true"
                        :needPagination="false"
                        :needIndex="false"
                        :overflowTooltip="!isPdf"
                    ></tableComponent>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    // 离职风险
    import { getRetentionRisk } from "../../../../request/api";
    import customProcess from "@/components/talent/common/customProcess.vue";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    export default {
        name: "userRRisk",
        props: ["nextBtnText", "enqId", "userId", "postCode","isPdf"],
        components: {
            tableComponent,
            customProcess,
        },
        data() {
            return {
				ristChart:this.$util.createRandomId(),
                retentionRisk: "",
                ristChartData: {
                    data: [],
                    legend: [],
                },
                tableData: {
                    columns: [
                        {
                            label: "评估维度",
                            prop: "name",
                            width: 150,
                        },
                        {
                            label: "评估内容",
                            prop: "content",
                        },
                        {
                            label: "表现",
                            prop: "performance",
                        },
                    ],
                    data: [],
                },
            };
        },
        created() {},
        computed: {},
        mounted() {
            this.getRetentionRiskFun();
        },
        methods: {
            setItemText(value) {
                return () => {
                    return value;
                };
            },
            getRetentionRiskFun() {
                getRetentionRisk({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    if (res.code == 200) {
                        this.retentionRisk = res.data.retentionRisk;
                        this.tableData.data = res.data.specificPerformance;
                        this.ristChartData.data =
                            res.data.turnoverRiskFactors.chartData;
                        this.ristChartData.legend =
                            res.data.turnoverRiskFactors.legend;
                        echartsRenderPage(
                            this.ristChart, //id
                            "XBar", // 图表类型
                            null, //宽
                            "280", //高
                            this.ristChartData //图表数据
                        );
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .userR_risk_wrap {
        padding: 0 10px;
        height: 480px;
        overflow: auto;
        pointer-events: auto;
        .userR_risk_main {
            .top_wrap {
                .top_left_wrap {
                    // width: 590px;
                    width: 30%;
                    .top_left_main {
                        text-align: center;
                        .annulus_item {
                            text-align: center;
                            p {
                                margin: 10px 0 0 0;
                            }
                            .el-progress {
                                .el-progress__text {
                                    margin: 0 0 0 15%;
                                    width: 42px;
                                    height: 42px;
                                    line-height: 45px;
                                    background: #dae8fd;
                                    border-radius: 50%;
                                }
                            }
                        }
                    }
                }
                .top_center_wrap {
                    margin: 0 3%;
                    width: 30%;
                }
                .top_right_wrap {
                    width: 30%;
                }
                .top_classify_wrap {
                    .top_center_main {
                        margin: 25px 0 0 0;
                        line-height: 30px;
                        .degree {
                            color: #0099ff;
                            font-weight: 600;
                            margin: 0 10px 0 0;
                        }
                        .desc {
                        }
                    }
                }
            }
            .bottom_wrap {
                .page_second_title {
                    margin: 15px 0;
                }
                .report_section_content {
                    border: 1px solid #dcdfe6;
                    width: 100%;
                    height: 300px;
                }
            }
        }
    }
</style>
