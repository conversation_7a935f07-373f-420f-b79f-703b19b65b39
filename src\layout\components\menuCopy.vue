<template>
  <div class="menu">
    <!-- <div class="logo">
      <img src="@/assets/imgs/logo.webp" alt="" />
    </div> -->
    <div class="menu-list">
      <div class="first-child" @click="menuChange('/dialogue')">
        <img :src="ico01" alt="" />
        <div class="name">新建对话</div>
      </div>
      <div class="menu-main">
        <template v-for="item in menuList">
          <div
            class="menu-item"
            :class="{
              active: router.currentRoute.value.path.indexOf(item.path) != -1
            }"
            :style="{
              marginBottom: item.children && item.children.length > 0 ? '' : '10px'
            }"
            @click="menuChange(item.path)"
          >
            <img :src="router.currentRoute.value.path.indexOf(item.path) != -1 ? item.ico_ac : item.ico" alt="" />
            <div class="name">{{ item.name }}</div>
          </div>
          <div
            class="children-item"
            :class="{
              active: router.currentRoute.value.path.indexOf(chil.path) != -1
            }"
            v-for="chil in item.children"
            @click="menuChange(chil.path)"
          >
            {{ chil.name }}
          </div>
        </template>
      </div>
    </div>
    <div class="history">
      <div class="title">30天内</div>
      <div class="history-list">
        <div
          class="hostory-item"
          :class="{ active: item.id == historyActive }"
          v-for="item in hostoryList"
          :key="item.id"
          @click="toDetails(item.id)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import ico01 from '@/assets/imgs/layout/01.webp'
import ico02 from '@/assets/imgs/layout/02.webp'
import ico02ac from '@/assets/imgs/layout/02_ac.webp'
import ico03 from '@/assets/imgs/layout/03.webp'
import ico03ac from '@/assets/imgs/layout/03_ac.webp'
import ico04 from '@/assets/imgs/layout/04.webp'
import ico04ac from '@/assets/imgs/layout/04_ac.webp'
import ico05 from '@/assets/imgs/layout/05.webp'
import ico05ac from '@/assets/imgs/layout/05_ac.webp'
import ico06 from '@/assets/imgs/layout/06.webp'
import ico06ac from '@/assets/imgs/layout/06_ac.webp'
import ico07 from '@/assets/imgs/layout/07.webp'
import ico07ac from '@/assets/imgs/layout/07_ac.webp'
import api from '@/api/index.js'

import entpRoutes from '@/router/modules/entp'

const router = useRouter()
const route = useRoute()
const menuList = [
  {
    name: '核心业务导航',
    ico: ico02,
    ico_ac: ico02ac,
    path: '/core'
  },
  {
    name: '组织效能导航',
    ico: ico02,
    ico_ac: ico02ac,
    path: '/org'
  },
  {
    name: '指标透视罗盘',
    ico: ico03,
    ico_ac: ico03ac,
    path: '/indicator/home',
    children: [
      {
        name: '参考指标库',
        path: '/indicator/libraryRefer/typical'
      },
      {
        name: '指标库维护',
        path: '/indicator/libraryTending'
      },
      {
        name: '目标值与实际值维护',
        path: '/indicator/targetTending'
      },
      {
        name: '指标诊断与根因分析',
        path: '/indicator/diagnosticAnalysis'
      },
      {
        name: '指标趋势预测与风险预警',
        path: '/indicator/trendRisk'
      },
      {
        name: '指标智能对标',
        path: '/indicator/benchmarking/'
      },
      {
        name: '指标改善任务一览',
        path: '/indicator/improveTasks'
      },
      {
        name: '指标改善效果追踪',
        path: '/indicator/indicatorImprove/'
      }
    ]
  },
  {
    name: 'AI诊断改善引擎',
    ico: ico04,
    ico_ac: ico04ac,
    path: '/AI/home',
    children: [
      {
        name: '快速了解能力诊断',
        path: '/AI/diagnosis'
      },
      {
        name: '能力显差概览',
        path: '/AI/displayer/overview/module'
      },
      {
        name: '靶点诊断仪（能力诊断）',
        path: '/AI/targetSpotDiagnosis'
      },
      {
        name: '靶点诊断仪（能力分析）',
        path: '/AI/analysis'
      }
    ]
  },
  {
    name: '产业适配引擎',
    ico: ico05,
    ico_ac: ico05ac,
    path: '/industry'
  },
  {
    name: '价值实证案例库',
    ico: ico06,
    ico_ac: ico06ac,
    path: '/case'
  },
  {
    name: '管理增效工具箱',
    ico: ico06,
    ico_ac: ico06ac,
    path: '/tools'
  },
  {
    name: '数据管理',
    ico: ico06,
    ico_ac: ico06ac,
    path: '/dataManage'
    // children: entpRoutes
  },
  {
    name: '我的关注',
    ico: ico07,
    ico_ac: ico07ac,
    path: '/myFollow'
  }
]
function menuChange(path) {
  router.push(path)
}
//#region 历史记录
const historyActive = ref('')
const hostoryList = ref([])
const getSessionList = () => {
  api.dialogue.sessionList().then(res => {
    if (res.code == 200) {
      hostoryList.value = res.data.slice(0, 3)
    }
  })
}
const toDetails = id => {
  router.push(`/dialogue/${id}`)
}
getSessionList()
watch(
  () => route.fullPath,
  (newPath, oldPath) => {
    getSessionList()
    historyActive.value = route.params.id
  },
  {
    immediate: true
  }
)

//#endregion
</script>
<style lang="scss" scoped>
.menu {
  padding: 0 20px;
  position: relative;
  height: 100%;
  .logo {
    width: 168px;
    height: 50px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .menu-list {
    margin-top: 35px;
    .first-child {
      display: flex;
      align-items: center;
      color: #333333;
      line-height: 20px;
      height: 40px;
      padding: 0 12px;
      margin-bottom: 10px;
      background: #53a9f9;
      border-radius: 8px 8px 8px 8px;
      color: #ffffff;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
      }
      .name {
        font-size: 16px;
        flex-grow: 1;
      }
    }
    .menu-main {
      height: calc(100vh - 350px);
      overflow-y: auto;
      padding-right: 10px;
    }
    .menu-item {
      display: flex;
      align-items: center;
      color: #333333;
      line-height: 20px;
      height: 40px;
      padding: 0 12px;
      // margin-bottom: 10px;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
      }
      .name {
        font-size: 16px;
        flex-grow: 1;
      }
      &.active {
        background: rgba(134, 171, 211, 0.1);
        border-radius: 10px 10px 10px 10px;
        color: #53a9f9;
      }
    }
    .children-item {
      line-height: 40px;
      height: 40px;
      width: 100%;
      padding-left: 38px;
      padding-right: 8px;
      font-size: 14px;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &.active {
        background: rgba(134, 171, 211, 0.1);
        border-radius: 10px 10px 10px 10px;
        color: #53a9f9;
      }
    }
  }
  .history {
    padding: 0 20px;
    position: absolute;
    bottom: 40px;
    right: 0;
    left: 0;
    margin: 0 auto;
    height: 165px;
    .title {
      border-top: 1px solid #d8d8d8;
      padding-top: 14px;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
    }
    .history-list {
      margin-top: 8px;
      .hostory-item {
        padding: 0 10px;
        height: 36px;
        line-height: 20px;
        color: #333333;
        font-size: 14px;
        line-height: 36px;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 5px;
        &.active,
        &:hover {
          color: #53a9f9;
          background: #daeaff;
          border-radius: 10px 10px 10px 10px;
        }
      }
    }
  }
}
</style>
