<template>
    <div class="assess_quality_manage_info_wrap">
        <div class="page_main_title">评估质量管理</div>
        <tabsChangeData :tabsData="tabsData" :activeName="tabsData[0].name" :handleClick="changeTabs"></tabsChangeData>
        <div class="department_level_wrap" v-if="showDepartmentLevel">
            <span class="select_title">显示部门层级</span>
            <el-select v-model="departmentLevel" placeholder="请选择">
                <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                </el-option>
            </el-select>
        </div>
         <tableComponent :tableData="tableData" :needIndex="true">
            <template v-slot:oper >
                <el-table-column label="完成率" min-width='120px' v-if="showTabId == 1">
                    <template slot-scope="scope">
                        <div class="completion_rate flex_row_between">
                            <div class="bar_wrap">
                                <div
                                    class="bar_progress"
                                    :class="scope.row.completionRate < 50 ? 'bg_low' : (scope.row.completionRate < 70 ? 'bg_normal' : (scope.row.completionRate < 90 ? 'bg_middle' : 'bg_high'))"
                                    :style="{'width':scope.row.completionRate+'%'}"
                                ></div>
                            </div>
                            <div
                                class="completion_rate_num"
                                :class="scope.row.completionRate < 50 ? 'color_low' : (scope.row.completionRate < 70 ? 'color_normal' : (scope.row.completionRate < 90 ? 'color_middle' : 'color_high'))"
                            >{{scope.row.completionRate}}%</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="200px" v-if="showTabId != 1">
                    <template slot-scope="scope">
                        <el-button
                            @click.native.prevent="sendBack(scope.$index, tableData.data)"
                            type="primary"
                            
                        >退回</el-button>
                        <el-button
                            @click.native.prevent="cancellation(scope.$index, tableData.data)"
                            type="primary"
                            
                        >作废</el-button>
                    </template>
                </el-table-column>
            </template>
        </tableComponent>
    </div>
</template>
 
<script>
import tabsChangeData from "@/components/talent/tabsComps/tabsChangeData";
import tableComponent from "@/components/talent/tableComps/tableComponent";
export default {
    name: "assessQualityManageInfo",
    components: {
        tabsChangeData,
        tableComponent
    },
    data() {
        return {
            tabsData: [
                {
                    id: 1,
                    label: "部门整体质量",
                    name: "department"
                },
                {
                    id: 2,
                    label: "高质量评价人员",
                    name: "highQuingityStaff"
                },
                {
                    id: 3,
                    label: "低质量评价人员",
                    name: "inferiorQualityStaff"
                },
            ],
            departmentLevel: "一级",
            options:[
                {
                    value: '1',
                    label: '一级'
                },
                {
                    value: '2',
                    label: '二级'
                },
            ],
            showDepartmentLevel: true,
            showTabId:1,
            tableData: {},
            departmentData: {
                columns: [
                    {
                        label: "部门名称",
                        prop: "departmentName",
                        // width: 200
                    },
                    {
                        label: "部门负责人",
                        prop: "departmentHead",
                        // width: 120,
                        className: "align_center"
                    },
                    {
                        label: "参评人数",
                        prop: "contestantNumber",
                        // width: 120,
                        className: "align_center"
                    },
                    {
                        label: "综合质量",
                        prop: "overallQuality",
                        // width: 120,
                        className: "align_center overall_quality"  
                    },
                    {
                        label: "高质量",
                        prop: "highQuingity",
                        // width: 120,
                        className: "align_center overall_quality"  
                    },
                    {
                        label: "低质量",
                        prop: "inferiorQuality",
                        // width: 120,
                        className: "align_center inferior_Quality"  
                    },
                ],
                data: [
                    {
                        id: "1",
                        departmentName: "销售一部",
                        departmentHead: "王伟",
                        contestantNumber: 21,
                        overallQuality: 19,
                        highQuingity: 23,
                        inferiorQuality: 2,
                        completionRate: "80"
                    },
                ],
                page:{
                    total:0,
                    current:1,
                    size:10
                }
            },
            highQuingityStaffData: {
                columns: [
                    {
                        label: "部门名称",
                        prop: "departmentName",
                        width: 200
                    },
                    {
                        label: "姓名",
                        prop: "personnelName",
                        width: 100,
                        className: "align_center"
                    },
                    {
                        label: "岗位",
                        prop: "post",
                        width: 100,
                        className: "align_center"
                    },
                    {
                        label: "状态",
                        prop: "status",
                        width: 100,
                        className: "align_center"
                    },
                    {
                        label: "信度",
                        prop: "reliability",
                        width: 100,
                        className: "align_center completed"
                    },
                    {
                        label: "用时",
                        prop: "time",
                        width: 120,
                        className: "align_center"
                    },
                    {
                        label: "最后登录时间",
                        prop: "lastLoginTime",
                        width: 180,
                        className: "align_center"
                    },
                ],
                data: [
                    {
                        id: "1",
                        departmentName: "销售一部",
                        personnelName: "王伟",
                        post: "销售总监",
                        status: "已完成",
                        reliability:98,
                        time: "1天3小时",
                        lastLoginTime:"2020-02-25 16:44"
                    },
                ],
                page:{
                    total:0,
                    current:1,
                    size:10
                }
            },
            inferiorQualityStaffData: {
                columns: [
                    {
                        label: "部门名称",
                        prop: "departmentName",
                        width: 200
                    },
                    {
                        label: "姓名",
                        prop: "personnelName",
                        width: 100,
                        className: "align_center"
                    },
                    {
                        label: "岗位",
                        prop: "post",
                        width: 100,
                        className: "align_center"
                    },
                    {
                        label: "状态",
                        prop: "status",
                        width: 100,
                        className: "align_center"
                    },
                    {
                        label: "信度",
                        prop: "reliability",
                        width: 100,
                        className: "align_center completed"
                    },
                    {
                        label: "用时",
                        prop: "time",
                        width: 120,
                        className: "align_center"
                    },
                    {
                        label: "最后登录时间",
                        prop: "lastLoginTime",
                        width: 180,
                        className: "align_center"
                    },
                ],
                data: [
                    {
                        id: "1",
                        departmentName: "销售一部",
                        personnelName: "王伟",
                        post: "销售总监",
                        status: "已完成",
                        reliability:98,
                        time: "1天3小时",
                        lastLoginTime:"2020-02-25 16:44"
                    },
                ],
                page:{
                    total:0,
                    current:1,
                    size:10
                }
            }
        };
    },
    created() {
        this.tableData = this.departmentData;
    },
    methods: {
        changeTabs(tab, event) {
            let index = tab.index;
            let key = tab.name + "Data";
            let id = this.tabsData[index]["id"];
            this.showDepartmentLevel = id == "1" ? true : false;
            this.showTabId = id;
            this.tableData = this._data[key];
        },
        sendBack(index, rows) {
            console.log(rows[index]);
            // this.$router.push({
            //     path: "/talentAssessment/viewModel",
            //     query: {}
            // });
        },
        cancellation(index,row){

        }
    }
};
</script>
 
<style scoped lang="scss">
.assess_quality_manage_info_wrap{
   .department_level_wrap {
        margin: 10px 0 20px 0;
        .select_title{
            display: inline-block;
            width: 120px;
        }
        .el-input__inner{
            height: 35px;
            line-height: 35px;
        }
        .el-input__suffix{
            display: flex;
            align-items: center;
        }
    }
    .el-table{
        margin: 10px 0 0 0;
        .has-gutter{
            tr th{
                background: #f2f8ff;
            }
        }
        .align_center {
            text-align: center;
            font-weight: bold;
            &.completed {
                color: #00b050;
            }
            &.incomplete {
                color: #ffc000;
            }
            &.overall_quality {
                color: #00b0f0;
            }
            &.inferior_Quality{
                color: #ff8f8f;
            }
        }
    }

    .el-table__body .last_date {
        font-size: 12px;
    }
    .completion_rate {
        .bar_wrap {
            width: calc(100% - 60px);
            height: 18px;
            background: #f2f2f2;
            position: relative;
            padding-top: 5px;
            .bar_progress {
                background: #00b050;
                height: 8px;
                width: 50%;
                &.bg_high {
                    background: #00b050;
                }
                &.bg_middle {
                    background: #00b0f0;
                }
                &.bg_normal {
                    background: #ffc000;
                }
                &.bg_low {
                    background: #ff8181;
                }
            }
        }
        .completion_rate_num {
            font-weight: bold;
            &.not_login {
                color: #ff6d6d;
            }
            &.color_high {
                color: #00b050;
            }
            &.color_middle {
                color: #00b0f0;
            }
            &.color_normal {
                color: #ffc000;
            }
            &.color_low {
                color: #ff8181;
            }
        }
    }
}
</style>