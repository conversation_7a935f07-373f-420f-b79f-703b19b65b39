<template>
  <div class="personnel_associated_wrap bg_write">
    <div class="page_main_title">指标关联确认</div>
    <div class="page_section">
      <div class="personnel_associated_center clearfix">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title">组织</div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio :treeData="treeData" @clickCallback="clickCallback"></tree-comp-radio>
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <el-table
            class="table_wrap"
            max-height="540"
            :data="tableData.data"
            @cell-click="cellClick"
            :header-cell-class-name="tableRowClassName"
          >
            <el-table-column
              v-for="(item, index1) in tableTitleNoCheck"
              :label="item.label"
              :prop="item.prop"
              :key="item.code || item.prop"
              :width="item.width"
              align="center"
            >
            </el-table-column>
            <el-table-column
              v-for="(item, index1) in tableTitleCanCheck"
              width="40"
              class-name="check_column"
              :label="item.label"
              :prop="item.prop"
              :key="item.code || item.prop"
              align="center"
            >
              <template #header>
                <div class="table-header-text">{{ item.label }}</div>
              </template>
              <template #default="scope">
                <div :class="{ check_box_wrap: true }">
                  <span>
                    <el-icon :color="scope.row[item.prop] ? '#0099ff' : 'transparent'"><Select /></el-icon>
                  </span>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="align_center">
            <el-button
              type="primary"
              class="page_confirm_btn"
              v-if="tableData.tableTitle.length > 0"
              @click="undateTargetBtn()"
              >确认</el-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { getPostTree, getKpiUserList, relateUserKpi, getOrgDeptTree } from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio.vue'
import { Select } from '@element-plus/icons-vue'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

const treeData = ref([])
const checkedId = ref('')
const tableData = reactive({
  tableTitle: [],
  data: []
})
const kpiCodeList = ref([])
const checkTargetSignList = ref([])
const checkArr = ref([])

// 过滤表头
const tableTitleNoCheck = computed(() => tableData.tableTitle.filter(item => !item.canCheck))
const tableTitleCanCheck = computed(() => tableData.tableTitle.filter(item => item.canCheck))

// 获取组织树
const getOrgDeptTreeFun = () => {
  getOrgDeptTree({ companyId: companyId.value }).then(res => {
    treeData.value = res.code == 200 && res.data.length > 0 ? res.data : []
  })
}

// 勾选左侧组织树
const clickCallback = (val, isLastNode) => {
  if (val) {
    checkedId.value = val
    getKpiUserListFun()
  } else {
    tableData.tableTitle = []
    tableData.data = []
  }
}

const getKpiUserListFun = () => {
  getKpiUserList({ orgCode: checkedId.value }).then(res => {
    if (res.code == 200) {
      if (res.data.kpiList.length == 0 && res.data.rowModels.length == 0) {
        tableData.tableTitle = []
        tableData.data = []
        return
      }
      // 初始表头固定部分 及表格数据
      tableData.tableTitle = [
        { label: '序号', prop: 'index', canCheck: false, width: 50 },
        { label: '姓名', prop: 'abilityClassity', canCheck: false, width: 100 },
        { label: '岗位', prop: 'abilityDict', canCheck: false }
      ]
      tableData.data = []
      kpiCodeList.value = []
      checkTargetSignList.value = []
      // 表头数据
      for (let i = 0; i < res.data.kpiList.length; i++) {
        tableData.tableTitle.push({
          label: res.data.kpiList[i].val,
          prop: 'targetName-' + (i + 1),
          canCheck: true,
          code: res.data.kpiList[i].code
        })
        checkTargetSignList.value.push('targetName-' + (i + 1))
        kpiCodeList.value.push(res.data.kpiList[i].code)
      }
      // 表格数据
      for (let j = 0; j < res.data.rowModels.length; j++) {
        const row = {
          index: j + 1,
          abilityClassity: res.data.rowModels[j].userName,
          abilityDict: res.data.rowModels[j].postName,
          userId: res.data.rowModels[j].userId,
          postCode: res.data.rowModels[j].postCode,
          opts: [...res.data.rowModels[j].opts],
          kpiCodeList: kpiCodeList.value
        }
        // 将所有表格初始为未被选中
        for (let i = 0; i < kpiCodeList.value.length; i++) {
          row[checkTargetSignList.value[i]] = false
        }
        // 变更所有表格选中状态
        for (let k = 0; k < res.data.rowModels[j].opts.length; k++) {
          const checkColumnn = kpiCodeList.value.indexOf(res.data.rowModels[j].opts[k])
          if (checkColumnn !== -1) {
            row['targetName-' + (checkColumnn + 1)] = true
          }
        }
        tableData.data.push(row)
      }
    }
  })
}

const cellClick = (row, column, cell, event) => {
  console.log('row', row)
  console.log('column', column.property)
  if (row[column.property] != true && row[column.property] != false) return
  let checkKpiCode
  for (let i = 0; i < tableData.data.length; i++) {
    if (tableData.data[i].index == row.index) {
      checkKpiCode = tableData.data[i].kpiCodeList[column.property.split('-')[1] - 1]
      if (tableData.data[i][column.property] == true) {
        tableData.data[i][column.property] = false
        const idx = tableData.data[i].opts.indexOf(checkKpiCode)
        if (idx !== -1) tableData.data[i].opts.splice(idx, 1)
      } else {
        tableData.data[i][column.property] = true
        tableData.data[i].opts.push(checkKpiCode)
      }
      // Vue 3 响应式直接赋值即可
      tableData.data[row.index - 1] = row
      // tableData.data[i] = { ...tableData.data[i] }
      return
    }
  }
}

const undateTargetBtn = () => {
  checkArr.value = []
  for (let i = 0; i < tableData.data.length; i++) {
    checkArr.value.push({
      userId: tableData.data[i].userId,
      postCode: tableData.data[i].postCode,
      opts: tableData.data[i].opts
    })
  }
  relateUserKpiFun()
}

const relateUserKpiFun = () => {
  relateUserKpi({ jsonArr: JSON.stringify(checkArr.value) }).then(res => {
    // 你可以根据项目实际情况替换为 ElMessage
    if (res.code == 200) {
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 表头样式
const tableRowClassName = ({ row, colum, rowIndex, columnIndex }) => {
  if (row[columnIndex] && row[columnIndex].label && row[columnIndex].label.length > 3) {
    return 'check_column_act'
  }
  return ''
}

// 监听 companyId 变化
watch(
  companyId,
  val => {
    if (val) {
      getOrgDeptTreeFun()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.personnel_associated_wrap {
  .page_section_main {
    .table_wrap {
      .el-table__row {
        height: 41px;
      }
    }
    .table_wrap tbody .cell {
      cursor: pointer;
    }
    .table_wrap thead {
      .check_column {
        .cell {
          height: 69px;
          span {
            display: block;
            height: 69px;
            word-wrap: break-word;
            letter-spacing: 20px;
          }
        }
      }
    }
    .table_wrap .check_box_wrap {
      color: #0099fd;
      font-weight: 700;
    }
    .align_center {
      margin: 15px 0 0;
    }
    .table_wrap tbody {
      .check_column {
        .cell {
          width: 30px;
          height: 30px;
          padding: 0;
          margin: 5px 0 4px;
        }
        .check_box_wrap {
          display: inline-block;
          width: 28px;
          height: 28px;
          border: 1px solid #dcdfe6;
          color: #000;
          text-align: center;
          line-height: 28px;
          // // margin-right: 3px;
          cursor: pointer;
        }
        .check_box_wrap:hover {
          border: 1px solid #0099ff;
        }
        .el-icon-check {
          border: 1px solid #0099ff;
          color: #fff;
          background: #0099ff;
        }
      }
    }
    .check_column_act {
      .cell::after {
        content: '...';
        font-weight: bold;
        position: absolute;
        bottom: -6px;
        right: 0;
        left: -8px;
      }
    }
  }
}
</style>
