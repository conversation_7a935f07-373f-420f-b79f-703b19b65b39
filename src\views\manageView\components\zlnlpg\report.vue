<script setup>
defineOptions({ name: 'report' })
const asideList = ref([
  {
    title: '整体能力表现',
    list: [
      {
        id: 'ztnl',
        name: '整体能力'
      },
      {
        id: 'nlcdb',
        name: '能力长短板'
      },
      {
        id: 'nltp',
        name: '能力图谱'
      }
    ]
  },
  {
    title: '能力短板分析',
    list: [
      {
        id: 'nlcy',
        name: '能力差因'
      },
      {
        id: 'nljm',
        name: '能力解码'
      },
      {
        id: 'dbxj',
        name: '短板详解'
      }
    ]
  },
  {
    title: '各模块能力详细分析',
    list: [
      {
        id: 'mkztbx',
        name: '模块整体表现'
      },
      {
        id: 'mkcyfx',
        name: '模块差因分析'
      },
      {
        id: 'mknlgs',
        name: '模块能力改善'
      }
    ]
  },
  {
    title: '能力改善总结',
    list: [
      {
        id: 'nldbfx',
        name: '能力短板风险'
      },
      {
        id: 'nltsjz',
        name: '能力提升举措'
      },
      {
        id: 'dqxdhj',
        name: '短期行动计划'
      }
    ]
  }
])
const activeAside = ref('ztnl')
const change = list => {
  activeAside.value = list.id
}
</script>
<template>
  <div class="report-page">
    <div class="aside-wrap">
      <div class="aside-title">测评报告内容</div>
      <div class="aside-item" v-for="item in asideList" :key="item.title">
        <div class="item-title">{{ item.title }}</div>
        <div
          class="item-list"
          :class="{ active: activeAside == list.id }"
          v-for="list in item.list"
          :key="list.id"
          @click="change(list)"
        >
          {{ list.name }}
        </div>
      </div>
    </div>
    <div class="report-page-content">
      <div class="page-title-line">整体得分与所处阶段</div>
      <div class="value-stage border">
        基于兮易AI测评技术，从多个维度对核心能力进行评估，为每一项测评生成专业的测评整体得分，并进行能力阶段的定位划分；整体得分直观反映企业综合能力水平，高分意味着企业在各关键能力领域表现出色，运营效率高，市场竞争力强；低分则表明企业存在较多问题，在很多方面需要改进，运营效率亟待提升。
      </div>
      <div class="flex-box">
        <div class="box-item">
          <div class="page-title-line">报告内容示例1</div>
          <div class="content border">
            <div class="item">
              <div class="title">能力指数</div>
              <img src="@/assets/imgs/manageView/report-1.png" alt="" srcset="" />
            </div>
            <div class="item">
              <div class="title">能力阶段</div>
              <img style="margin-top: 40px" src="@/assets/imgs/manageView/report-2.png" alt="" srcset="" />
            </div>
          </div>
        </div>
        <div class="box-item">
          <div class="page-title-line">报告内容示例2</div>
          <div class="content border">
            <img style="margin-top: 9px" src="@/assets/imgs/manageView/report-3.png" alt="" srcset="" />
          </div>
        </div>
      </div>
      <div class="page-title-line">如何使用该项评估结果</div>
      <div class="value-stage border">
        <div>
          <b>明确发展方向：</b
          >如果整体得分较高且处于较高能力阶段，说明企业在当前业务领域具备较强的竞争力，可考虑进行市场扩张、多元化发展等战略。若整体得分较低或处于较低能力阶段，企业则需要专注于提升核心能力，弥补短板，可能需要收缩战线，集中资源解决关键问题。
        </div>
        <div>
          <b>设定战略目标：</b
          >依据整体得分和能力阶段，制定具体的、可衡量的战略目标。如处于能力提升阶段的企业，目标可以是在一定时间内将整体得分提高到某个水平，达到下一个能力阶段。
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.report-page {
  display: flex;
  flex-flow: row nowrap;
  gap: 18px;
  .aside-wrap {
    flex: 0 0 198px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px 8px 8px 8px;
    padding: 14px 9px;
    .aside-title {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
      line-height: 20px;
      margin-bottom: 26px;
    }
    .aside-item {
      text-align: center;
      .item-title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        line-height: 50px;
      }
      .item-list {
        line-height: 42px;
        border-radius: 5px 5px 5px 5px;
        border: 1px solid transparent;
        color: #333;
        font-size: 14px;
        cursor: pointer;
        &.active {
          border-color: #53a9f9;
          color: #53a9f9;
        }
      }
    }
  }
  .report-page-content {
    flex: 1;
    .value-stage {
      margin-bottom: 25px;
    }
    .flex-box {
      display: flex;
      align-items: stretch;
      gap: 28px;
      margin-bottom: 30px;
      .box-item {
        flex: 1;
        height: 100%;
        .content {
          display: flex;
          gap: 22px;
          .item {
            flex: 1;
            .title {
              width: 100%;
              line-height: 30px;
              background: #eaf4ff;
              border-radius: 51px;
              font-weight: 600;
              text-align: center;
              font-size: 16px;
              color: #40a0ff;
            }
          }
          img {
            width: 100%;
            height: auto;
          }
        }
      }
    }
  }
  .border {
    padding: 16px 25px;
    background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
  }
}
</style>
