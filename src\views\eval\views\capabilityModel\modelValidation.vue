<template>
    <div class="model_validation_wrap bg_write">
        <div class="page_main_title clearfix">
            模型确认
            <div class="goback_geader" @click="$util.goback()">
                <i class="el-icon-arrow-left"></i>返回
            </div>
        </div>
        <div class="page_section">
            <div class="model_validation_main padd_TB_16">
                <step-bar :stepData="stepData" :currentIndex="currentIndex" :needClick="needClick" @stepClick="stepClick"></step-bar>
                <component :is="compArr[currentIndex]" :modelId="modelId" :buildId="buildId" :type="type" @prevStep="prev" @nextStep="next"></component>
<!--                <div class="model_validation_btn_wrap">-->
<!--                    <el-button type="primary" @click="prev()" v-if="this.currentIndex > 0">上一步</el-button>-->
<!--                    <el-button type="primary" @click="affirm()" v-if="this.currentIndex == this.stepData.length-1">确认</el-button>-->
<!--                    <el-button type="primary" @click="next()" v-else>下一步</el-button>-->
<!--                </div>-->
            </div>
        </div>
    </div>
</template>
 
<script>
import stepBar from "@/components/talent/stepsComps/stepBar";
import modelValidationDict from "./modelValidationComponents/modelValidationDict";
import modelMateInfoTarget from "./modelValidationComponents/modelMateInfoTarget";
import modelMateInfoRelation from "./modelValidationComponents/modelMateInfoRelation";
import modelMateInfoFinal from "./modelValidationComponents/modelMateInfoFinal";
export default {
    name: "modelValidation",
    components: {
        stepBar,
        modelValidationDict,
        modelMateInfoTarget,
        modelMateInfoRelation,
        modelMateInfoFinal
    },
    data() {
        return {
            needClick:true,
            modelId:this.$route.query.modelId,
            buildId:this.$route.query.buildId,
            type:this.$route.query.type,
            compArr: [
                modelValidationDict,
                modelMateInfoTarget,
                // modelMateInfoRelation,
                modelMateInfoFinal
            ],
            currentIndex: 0,
            stepData: [
                {
                    name: "确认能力词典",
                    state: "inComplete",
                    code:0
                },
                {
                    name: "确认能力目标",
                    state: "inComplete",
                    code:1
                },
                // {
                //     name: "确认评价关系",
                //     state: "inComplete"
                // },
                {
                    name: "确认最终模型",
                    state: "inComplete",
                    code:2
                },
            ]
        };
    },
    created() {
        this.stepData.forEach(item=>{
            item.enqProgress ="Y"
        })
    },
    methods: {
        next() {
            if (this.currentIndex == this.stepData.length - 1) {
                return false;
            }
            this.stepData[this.currentIndex].enqProgress = "Y";
            this.currentIndex++;
            this.stepData[this.currentIndex].state = "inProgress";
        },
        affirm(){

        },
        prev() {
            if (this.currentIndex == 0) {
                return false;
            }
            this.currentIndex--;
        },
        stepClick(code ,index){
            console.log(index)
            this.currentIndex =index;
        }

    }
};
</script>
 
<style scoped lang="scss">
.model_validation_wrap{
    .page_section{
        .model_validation_btn_wrap{
            text-align: center;
        }
    }
}

</style>