<script setup>
import Table from '../components/table.vue'
import Tree from '@/components/tree/project.vue'
import { libraryTending } from '@/assets/data/data9.js'

defineOptions({ name: 'projectRelevancy' })
const data = ref(libraryTending.projectData)
const columns = ref([
  {
    prop: 'code',
    label: '指标编码',
    width: 100
  },
  {
    prop: 'name',
    label: '指标名称',
    width: 250
  },
  {
    prop: 'type',
    label: '指标类型'
  },
  {
    prop: 'class',
    label: '指标类别'
  },
  {
    prop: 'nature',
    label: '指标性质'
  },
  {
    prop: 'nlms',
    label: '计算公式',
    width: 320,
    showOverflowTooltip: true
  },
  {
    prop: 'unit',
    label: '单位'
  },
  // {
  //   prop: "o",
  //   label: "数据来源",
  // },
  {
    prop: 'cycle',
    label: '指标周期'
  },
  {
    prop: 'polarity',
    label: '指标极性'
  },
  {
    prop: 'parentIndicator',
    label: '上级指标'
  },
  {
    prop: 'affect',
    label: '影响指标'
  },
  {
    prop: 'charge',
    label: '责任人'
  }
])
const defaultProps = ref({
  children: 'children',
  label: 'label'
})
const treeData = ref([
  {
    id: 1,
    label: 'H公司冰箱公司',
    children: [
      {
        id: 10,
        label: '供应链效率提升项目'
      },
      {
        id: 11,
        label: '工厂产能优化与成本控制项目'
      },
      {
        id: 12,
        label: '经销商渠道管理升级项目'
      },
      {
        id: 13,
        label: '新品研发流程优化项目'
      },
      {
        id: 14,
        label: '库存周转与滞销品清理项目'
      },
      {
        id: 15,
        label: '售后服务标准化建设项目'
      },
      {
        id: 16,
        label: '跨境物流成本管控项目'
      },
      {
        id: 17,
        label: '生产计划与采购协同项目'
      },
      {
        id: 18,
        label: '终端门店陈列优化项目'
      },
      {
        id: 19,
        label: '组织架构与绩效考核优化项目'
      }
    ]
  }
])
const defaultChecked = ref([])
const defaultExpand = ref(true)
onMounted(() => {})
</script>
<template>
  <div class="projectRelevancy_wrap">
    <div class="second_wrap justify-between">
      <div class="page-title-line">指标信息</div>
      <div class="second_r justify-between">
        <!-- <div class="add_btn"><span class="icon"></span> 新增目标</div> -->
        <div class="operate_data justify-between">
          <div class="l_btn operate_btn"><span class="icon icon_import"></span> 导入</div>
          <div class="r_btn operate_btn"><span class="icon icon_export"></span> 导出</div>
        </div>
      </div>
    </div>
    <div class="projectRelevancy_content justify-between">
      <div class="l">
        <div class="title">组织</div>
        <!-- <div class="second_t">未关联指标组织</div> -->
        <div class="tree_wrap">
          <Tree></Tree>
        </div>
      </div>
      <div class="r">
        <Table :data="data" :columns="columns" :showIndex="true" :showSelect="true"></Table>
        <div class="affirm_btn">确认</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.projectRelevancy_wrap {
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
  font-size: 14px;
  .justify-between {
    display: flex;
    justify-content: space-between;
  }
  .second_wrap {
    margin-bottom: 10px;
    height: 36px;
    align-items: center;
    .page-title-line {
      margin-bottom: -5px;
    }
    .second_r {
      .add_btn {
        width: 100px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        color: #fff;
        background: #40a0ff;
        border-radius: 6px 6px 6px 6px;
        cursor: pointer;
        .icon {
          display: inline-block;
          margin-bottom: -2px;
          width: 16px;
          height: 16px;
          background: url('@/assets/imgs/indicator/icon_01.png') no-repeat center;
        }
      }
      .operate_data {
        margin-left: 10px;
        height: 36px;
        line-height: 36px;
        .operate_btn {
          width: 68px;
          text-align: center;
          border: 1px solid #666666;
          cursor: pointer;
          .icon {
            display: inline-block;
            margin-bottom: -2px;
            width: 16px;
            height: 16px;
          }
        }
        .l_btn {
          border-radius: 6px 0 0 6px;
          border-right: 1px solid transparent;
          .icon {
            background: url('@/assets/imgs/indicator/icon_02.png') no-repeat center center;
          }
        }
        .l_btn:hover {
          color: #40a0ff;
          border: 1px solid #40a0ff;
          .icon {
            background: url('@/assets/imgs/indicator/icon_04.png') no-repeat center center;
          }
          + .r_btn {
            border-left: 1px solid transparent;
          }
        }
        .r_btn {
          border-radius: 0 6px 6px 0;
          .icon {
            background: url('@/assets/imgs/indicator/icon_03.png') no-repeat center center;
          }
        }
        .r_btn:hover {
          color: #40a0ff;
          border: 1px solid #40a0ff;
          .icon {
            background: url('@/assets/imgs/indicator/icon_05.png') no-repeat center center;
          }
        }
      }
    }
  }
  .projectRelevancy_content {
    .l {
      padding: 8px;
      margin-right: 20px;
      width: 230px;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      .title {
        margin: 0 auto;
        width: 210px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
        background: #e3effa;
        border-radius: 8px 8px 8px 8px;
      }
      .second_t {
        font-size: 12px;
        height: 40px;
        line-height: 40px;
        color: #40a0ff;
      }
    }
    .r {
      width: calc(100% - 250px);
      .affirm_btn {
        margin: 20px 0 20px;
        width: 100px;
        height: 36px;
        line-height: 36px;
        color: #fff;
        text-align: center;
        background: #40a0ff;
        border-radius: 6px 6px 6px 6px;
      }
    }
  }
}
</style>
