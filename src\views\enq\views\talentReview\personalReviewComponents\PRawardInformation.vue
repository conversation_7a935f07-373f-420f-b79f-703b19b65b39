<template>
  <div class="edu_info_wrap performance_info_main">
    <div class="clearfix">
      <div class="page_second_title marginT_16">
        <span>获奖信息</span>
        <div class="align_right">
          <el-button type="primary" class="page_add_btn" @click="addItem">新增</el-button>
        </div>
      </div>
      <div class="edu_info_center marginT_16">
        <div class="edu_info_header">
          <div class="item">奖项名称</div>
          <div class="item">颁发单位</div>
          <div class="item">获奖日期</div>
          <div class="item">获奖类型</div>
          <div class="item">是否在本公司</div>
          <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
          <PRawardInformationList
            :performanceInfoData="performanceInfoData"
            @deleteItem="deleteItem"
          ></PRawardInformationList>
          <div class="align_center marginT_30">
            <el-button
              class="page_confirm_btn"
              type="primary"
              @click="prevStep"
              v-show="currentIndex !== currentFirstCode"
              >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="() => submit('nextStep')">{{
              nextBtnText
            }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import PRawardInformationList from './PRawardInformationList.vue'
import { delEnqUserAward, getEnqUserAward, updateEnqUserAward } from '../../../request/api'
import { useAttrs, defineProps, defineEmits } from 'vue'
import { objHasEmpty } from '@/utils/utils' // 假设你的工具函数在这里

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])
const userStore = useUserStore()

const submitFlag = ref(true)
const performanceInfoData = ref([])

const userId = computed(() => userStore.userInfo.userId)

function deleteItem(item, index) {
  // 以当前行数据是否包含 awardId 字段判断是否是入库的数据
  if (!Object.prototype.hasOwnProperty.call(item, 'awardId')) {
    // 未入库的数据直接删除，不调用接口
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      type: 'warning'
    })
      .then(() => {
        performanceInfoData.value.splice(index, 1)
        ElMessage.success('删除成功!')
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  } else {
    // 调用删除接口
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        delEnqUserAward({
          awardId: item.awardId,
          enqId: props.enqId
        }).then(res => {
          if (res.code == '200') {
            ElMessage.success('删除成功!')
            performanceInfoData.value.splice(index, 1)
          } else {
            ElMessage.error('删除失败！')
          }
        })
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  }
}

function addItem() {
  let obj = performanceInfoData.value[performanceInfoData.value.length - 1]
  let addObj = {
    enqId: props.enqId,
    awardName: '',
    awardIssuer: '',
    awardDate: '',
    awardType: '',
    currentCompany: '',
    userId: userId.value
  }
  if (!obj) {
    performanceInfoData.value.push(addObj)
    return
  }
  if (checkData(performanceInfoData.value)) {
    ElMessage.warning('请完善当前数据后新增！')
    return
  }
  performanceInfoData.value.push(addObj)
}

function submit(stepType) {
  if (!submitFlag.value) return
  // 校验数据中有没有空值
  if (checkData(performanceInfoData.value)) {
    ElMessage.warning('请完善数据后提交！')
    return
  }
  submitFlag.value = false
  let params = performanceInfoData.value
  updateEnqUserAward(params)
    .then(res => {
      if (res.code == '200') {
        ElMessage.success('保存成功!')
        getEnqUserAwardFun()
        submitFlag.value = true
        emit(stepType)
      } else {
        submitFlag.value = true
        ElMessage.error('保存失败!')
      }
    })
    .catch(() => {
      submitFlag.value = true
    })
}

function getEnqUserAwardFun() {
  getEnqUserAward({
    enqId: props.enqId
  }).then(res => {
    if (res.code == '200') {
      performanceInfoData.value = res.data
    } else {
      ElMessage.error('获取数据失败!')
    }
  })
}

function checkData(data) {
  // 校验数据 是否有空值
  let arr = data
  let len = arr.length
  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    if (objHasEmpty(obj, ['awardName', 'awardIssuer', 'awardDate', 'awardType', 'currentCompany'])) {
      return true
    }
  }
  return false
}

function prevStep() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submit('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}

// 初始化时获取数据
getEnqUserAwardFun()
</script>

<style scoped lang="scss">
.edu_info_header {
  .item {
    width: 20%;
    padding-left: 10px;
  }

  .item_icon_wrap {
    text-align: center;
    width: 10%;
  }
}
</style>
