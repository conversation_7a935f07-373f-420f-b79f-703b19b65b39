<template>
  <div class="matrix_view_main">
    <div class="chart_wrap" id="matrix"></div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { echartsRenderPage } from '../../../../../../public/js/echartsimg/echartsToImg'

const props = defineProps({
  searchViewSign: Boolean
})

const scatterDeptData = reactive({
  legend: ['进度(%)', '质量(%)'],
  data: [
    [
      {
        name: '姓名1',
        value: [20, 80, 10] // x,y,R
      },
      {
        name: '姓名2',
        value: [80, 70, 20]
      },
      {
        name: '姓名3',
        value: [80, 60, 40]
      }
    ],
    [
      {
        name: '姓名4',
        value: [90, 50, 50]
      },
      {
        name: '姓名5',
        value: [65, 50, 65]
      },
      {
        name: '姓名6',
        value: [95, 30, 80]
      }
    ]
  ]
})

const scatterStaffData = reactive({
  legend: ['进度(%)', '质量(%)'],
  data: [
    [
      {
        name: '23姓名1',
        value: [10, 10, 10] // x,y,R
      },
      {
        name: '32姓名2',
        value: [10, 30, 20]
      },
      {
        name: '33姓名3',
        value: [20, 10, 40]
      }
    ],
    [
      {
        name: '姓名4',
        value: [10, 50, 50]
      },
      {
        name: '姓名5',
        value: [15, 10, 65]
      },
      {
        name: '姓名6',
        value: [15, 10, 80]
      }
    ]
  ]
})

const renderChart = () => {
  if (props.searchViewSign) {
    // 部门视角
    echartsRenderPage('matrix', 'Scatter', '900', '500', scatterDeptData)
  } else {
    // 人员视角
    echartsRenderPage('matrix', 'Scatter', '900', '500', scatterStaffData)
  }
}

onMounted(() => {
  console.log(props.searchViewSign)
  renderChart()
})

watch(
  () => props.searchViewSign,
  val => {
    renderChart()
  }
)
</script>

<style scoped lang="scss"></style>
