<template>
  <div class="dialogue">
    <div class="introduce">
      <div class="title">我是兮小易，你的个人AI助手</div>
      <div class="brief">您的随身管理顾问专家，我可以帮助您：</div>
      <div class="list">
        <div class="item" v-for="(item, index) in data.introduceList" :key="index" @click="goPath(item)">
          <img :src="item.img" alt="" />
          <div class="text">{{ item.text }}</div>
        </div>
      </div>
    </div>
    <Input ref="InputRef" @send="toDetails()" />
    <!-- <div class="special">
      <div class="title">
        <img src="@/assets/imgs/dialogue/ico.webp" alt="" />
        <div class="text">专题领域</div>
      </div>
      <div class="menu">
        <div
          class="menu-item"
          :class="{ active: checked == item.title }"
          v-for="(item, index) in data.specialList"
          :key="index"
          @click="menuChange(item.title)"
        >
          {{ item.title }}
        </div>
      </div>
      <div class="content">
        <div class="content-item" v-for="(item, index) in contentList" :key="index">
          <div class="name">{{ item.title }}</div>
          <div class="text">
            {{ item.text }}
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>
<script setup>
defineOptions({ name: 'Dialogue' })
import { data } from '@/assets/data/dialogue'
import Input from '@/components/AI/input.vue'
import { sessionAdd } from '@/api/modules/dialogue.js'
import { useDialogueStore } from '@/stores'

const router = useRouter()
const InputRef = ref(null)
const goPath = item => {
  if (item.path) {
    router.push(item.path)
  }
}
const toDetails = () => {
  sessionAdd({ message: InputRef.value.input }).then(res => {
    if (res.code == 200) {
      useDialogueStore().setFirst(InputRef.value.input)
      router.push(`/dialogue/${res.msg}`)
    }
  })
}
//#region 专题领域
const checked = ref('供应链产销协同')
const contentList = ref(getListByTitle(data.specialList, checked.value))
const menuChange = title => {
  checked.value = title
  contentList.value = getListByTitle(data.specialList, title)
}
function getListByTitle(data, targetTitle) {
  for (let i = 0; i < data.length; i++) {
    if (data[i].title == targetTitle) {
      return data[i].list
    }
  }
  return []
}
//#endregion
</script>
<style lang="scss" scoped>
.dialogue {
  height: 80%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .introduce {
    text-align: center;
    .title {
      color: #333333;
      line-height: 20px;
      font-size: 24px;
      font-weight: 500;
      margin-bottom: 16px;
    }
    .brief {
      color: #888888;
      font-size: 16px;
      line-height: 20px;
      margin-bottom: 20px;
    }
    .list {
      display: flex;
      justify-content: center;
      .item {
        width: 162px;
        height: 120px;
        border-radius: 10px 10px 10px 10px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        margin-right: 11px;
        &:nth-child(9) {
          margin-right: 0;
        }
        img {
          width: 46px;
          height: 46px;
        }
        .text {
          line-height: 20px;
          font-size: 14px;
          font-weight: 500;
          margin-top: 10px;
        }
        &:nth-child(1),
        &:nth-child(5),
        &:nth-child(8) {
          background: linear-gradient(47deg, #e2f1f7 0%, #d5f7fc 97%);
          .text {
            color: #3ec1d4;
          }
        }
        &:nth-child(2),
        &:nth-child(6),
        &:nth-child(9) {
          background: linear-gradient(45deg, #dbedfa 0%, #c1dbfd 100%);
          .text {
            color: #589ef9;
          }
        }
        &:nth-child(3),
        &:nth-child(7) {
          background: linear-gradient(45deg, #f1effb 0%, #cecafa 99%);
          .text {
            color: #7b6fff;
          }
        }
        &:nth-child(4) {
          background: linear-gradient(43deg, #d8e9f4 0%, #d1e9fd 100%);
          .text {
            color: #4fa1ed;
          }
        }
        &:nth-child(8),
        &:nth-child(9) {
          cursor: pointer;
        }
      }
    }
  }
  .input-main {
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid #dddddd;
    padding: 12px;
    margin-top: 40px;
    :deep(.el-textarea__inner) {
      outline: none;
      border: none;
      resize: none;
      box-shadow: none;
      // background: #f8f8f8;
    }
    .action-bar {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      .right {
        display: flex;
        align-items: center;
        div {
          cursor: pointer;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .accessory,
        .voice {
          width: 24px;
          height: 24px;
          background: #ebf3ff;
          border-radius: 2px 2px 2px 2px;
          margin-right: 12px;
          &:hover {
            background: #53a9f9;
          }
        }
        .send {
          width: 32px;
          height: 32px;
          background: #53a9f9;
          border-radius: 50%;
          margin-left: 4px;
          &:nth-child(1) {
            cursor: not-allowed;
          }
        }
      }
      .think {
        display: flex;
        align-items: center;
        height: 36px;
        border-radius: 10px;
        background: #ffffff;
        border: 1px solid #dddddd;
        padding: 0 10px;
        .ico {
          width: 18px;
          height: 18px;
          margin-right: 4px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .text {
          font-size: 14px;
          color: #333333;
          margin-right: 7px;
        }
      }
    }
  }
  .special {
    margin-top: 40px;
    .title {
      display: flex;
      img {
        width: 5px;
        height: 18px;
        margin-right: 5px;
      }
      .text {
        color: #333333;
        line-height: 20px;
        font-size: 16px;
        font-weight: 500;
      }
    }
    .menu {
      display: flex;
      margin-top: 20px;
      .menu-item {
        height: 40px;
        line-height: 40px;
        padding: 0 10px;
        margin-right: 20px;
        cursor: pointer;
        &.active,
        &:hover {
          background: #daeaff;
          color: #53acff;
          border-radius: 6px 6px 6px 6px;
        }
      }
    }
    .content {
      display: flex;
      justify-content: space-between;
      align-items: stretch;
      margin-top: 20px;
      gap: 20px;
      .content-item {
        width: calc(25% - 10px);
        // height: 110px;
        background: url('../../assets/imgs/dialogue/bag.webp') no-repeat 0 0;
        background-size: 100% 100%;
        padding: 20px;
        .name {
          color: #333333;
          line-height: 20px;
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 10px;
        }
        .text {
          color: #666666;
          line-height: 21px;
          font-size: 14px;
        }
        &:hover {
          .name {
            color: #53acff;
          }
        }
      }
    }
  }
}
</style>
