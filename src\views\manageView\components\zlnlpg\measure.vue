<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'
defineOptions({ name: 'measure' })
const reportList = ref([
  { id: 1, title: '20250201 销售预测核心能力测评报告（单人测评）', expand: false },
  { id: 2, title: '20250201 销售竞争力测评报告（多人测评）', expand: true }
])

const toggleViewReport = item => {
  item.expand = !item.expand
}
const columns = ref([
  {
    label: '序号',
    type: 'index',
    width: 60,
    align: 'center'
  },
  {
    label: '提升举措',
    prop: 'name'
  },
  {
    label: '完备性',
    prop: 'target'
  },
  {
    label: '紧迫性',
    prop: 'probability'
  },
  {
    label: '工作量',
    prop: 'period'
  }
])

const tableData = ref([
  {
    name: '销售战略规划制定',
    target: '高',
    probability: '中',
    period: '高'
  },
  {
    name: '搭建市场洞察、开拓与规划体系',
    target: '高',
    probability: '中',
    period: '高'
  },
  {
    name: '销售业绩分析对标',
    target: '高',
    probability: '中',
    period: '低'
  },
  {
    name: '产品竞争力差距分析',
    target: '高',
    probability: '中',
    period: '高'
  },
  {
    name: '销售竞争力差距分析',
    target: '高',
    probability: '中',
    period: '高'
  },
  {
    name: '营销竞争力差距分析',
    target: '高',
    probability: '中',
    period: '高'
  },
  {
    name: '销售流程运作分析',
    target: '高',
    probability: '中',
    period: '高'
  },
  {
    name: '销售战略机会分析',
    target: '高',
    probability: '中',
    period: '高'
  },
  {
    name: '销售战略定位',
    target: '高',
    probability: '中',
    period: '高'
  },
  {
    name: '战略目标制定周期',
    target: '高',
    probability: '中',
    period: '高'
  }
])
const chooseTableData = ref(tableData.value[0])
const tableRef = ref(null)
const handCurrentChange = val => {
  console.log(val)
  chooseTableData.value = val
}

onMounted(() => {
  tableRef.value[0]?.simplenessTableRef?.setCurrentRow(tableData.value[0])
})
</script>
<template>
  <div class="report-page">
    <div class="head-title">查看如下能力测评对应的能力提升举措：</div>
    <div class="report-content">
      <div class="report-item" v-for="item in reportList" :key="item.id">
        <div class="item-head">
          <div class="title">{{ item.title }}</div>
          <div class="view-btn" @click="toggleViewReport(item)">{{ item.expand ? '收起' : '查看报告' }}</div>
        </div>
        <div class="report-main" v-if="item.expand">
          <div class="report-page-content">
            <div class="page-title-line">
              <span>能力改善优先级矩阵</span>
            </div>
            <img src="" alt="能力改善优先级矩阵" srcset="" />
            <div class="page-title-line">
              <span>能力改善矩阵说明</span>
              <div class="line"></div>
              <div class="main-color">选择可查看详情</div>
            </div>
            <SimplenessTable
              ref="tableRef"
              :highlightCurrentRow="true"
              @current-change="handCurrentChange"
              :roundBorder="false"
              :columns="columns"
              :data="tableData"
            >
            </SimplenessTable>
            <div class="page-title-line">
              <span>能力改善实施建议</span>
            </div>
            <div class="section border">
              <div class="section-desc">
                1. 实施目标
                建立科学的市场洞察体系，实现精准需求预测；搭建标准化市场开拓流程，提升客户转化率；构建动态战略规划能力，确保资源高效配置。<br />
                2. 关键举措 管理标准导入：制定《市场洞察SOP》《客户分级标准》，规范数据采集、分析及决策流程。
                管理工具开发：开发客户画像工具、竞争分析模型及自动化商机管理系统。
                数据管理完善：建立统一数据仓库，整合CRM、ERP及外部市场数据，实现实时可视化。
                信息系统完善：升级BI系统，嵌入AI预测模块，提升数据响应速度至分钟级。
                管理制度建设：发布《市场规划管理章程》，明确跨部门协作机制与决策权限。
                组织岗位优化：设立市场洞察中心，新增数据分析师、战略规划岗，优化前线销售团队分工。
                人员能力提升：开展季度轮训（市场分析、工具应用、谈判技巧），通过沙盘模拟考核实战能力。
                考核机制完善：将客户覆盖率、商机转化率、预测准确率纳入KPI，挂钩奖金池分配。<br />
                3. 预计预算 系统开发：200-300万元（工具+BI升级） 数据治理：50-80万元 培训与咨询：100-150万元
                人力成本：80-120万元（新增岗位） 总计：430-650万元<br />
                4. 预计收益 效率提升：市场响应速度提升40%，客户转化率提高15%-25%。
                成本优化：资源错配率降低30%，年节约运营成本200万+。
                战略价值：预测准确率≥85%，支撑3-5年滚动战略规划迭代<br />
                5. 开展周期 体系搭建：3个月（标准制定、工具开发） 试点运行：2个月（区域验证，迭代模型）
                全面推广：4个月（系统部署、组织调整） 固化提升：3个月（考核落地、持续优化） 总周期：12个月
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.report-page {
  .head-title {
    font-size: 16px;
    color: #888888;
    line-height: 20px;
    margin-bottom: 16px;
  }
}
.report-content {
  .report-item {
    margin-bottom: 15px;

    .item-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 26px 28px;
      background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      margin-bottom: 15px;
      .title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
      }
      .view-btn {
        font-size: 14px;
        color: #fff;
        cursor: pointer;
        line-height: 30px;
        background: #40a0ff;
        border-radius: 3px 3px 3px 3px;
        text-align: center;
        padding: 0 22px;
      }
    }
    .report-main {
      display: flex;
      flex-flow: row nowrap;
      gap: 18px;
      .report-page-content {
        flex: 1;
        background: #ffffff;
        box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
        border-radius: 8px 8px 8px 8px;
        padding: 0px 30px 20px;
        .title {
          font-weight: 500;
          font-size: 16px;
          color: #53a9f9;
          margin-bottom: 20px;
        }
        .page-title-line {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 20px;
          margin-top: 30px;
          .line {
            flex: 1;
            height: 1px;
            background: #e5e5e5;
          }
          .ai-btn {
            width: 73px;
            text-align: center;
            line-height: 30px;
            background: #e1f3ff;
            border-radius: 30px;
            font-weight: 500;
            font-size: 16px;
            color: #40a0ff;
            cursor: pointer;
          }
        }
      }
    }
  }
}
.section {
  flex: 1;
  padding: 20px;
  margin-bottom: 20px;
  &-title {
    font-weight: 600;
    font-size: 16px;
    color: #3d3d3d;
    line-height: 16px;
    margin-bottom: 12px;
  }
  &-desc {
    font-size: 14px;
    color: #666666;
    line-height: 28px;
  }
}
.border {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #c6dbf3;
}
</style>
