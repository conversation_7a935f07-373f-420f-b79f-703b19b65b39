<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">职层分布</div>
          <div class="content_item_content" id="job_level"></div>
        </div>
      </div>
      <div class="content_item el-col-16">
        <div class="content_item_main">
          <div class="content_item_title">部门分布</div>
          <div class="content_item_content" id="org"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title">职层分布</div>
          <div class="content_item_content">
            <tableComponent
              :needIndex="true"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :size="'small'"
              :tableData="tableData"
            ></tableComponent>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { staffSupply, postDistribute } from '../../../../../request/api'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import asideFilterBar from '../../asideFilterBar'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const route = useRoute()
const enqId = ref(route.query.enqId)
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref([])
const pageSize = ref(10)
const currPage = ref(1)

const tableData = reactive({
  columns: [
    {
      label: '部门名称',
      prop: 'orgName'
    },
    {
      label: '岗位',
      prop: 'postName'
    },
    {
      label: '职层',
      prop: 'jobLevelName'
    },
    {
      label: '人员新增',
      prop: 'recruitCount'
    },
    {
      label: '单人成本',
      prop: 'department3'
    },
    {
      label: '预计总成本',
      prop: 'department4'
    }
  ],
  page: {},
  data: []
})

const orgDistribute = reactive({
  data: []
})

const jobLevelDistribute = reactive({
  data: []
})

const initChart = () => {
  echartsRenderPage('job_level', 'YBar', '230', '220', jobLevelDistribute)
  echartsRenderPage('org', 'XBar', '470', '220', orgDistribute)
}

const staffSupplyFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await staffSupply(params)
    if (res.code == 200) {
      const data = res.data
      orgDistribute.data = data.orgDistribute
      jobLevelDistribute.data = data.jobLevelDistribute
      initChart()
    }
  } catch (error) {
    console.error('获取人员补充数据失败:', error)
  }
}

const postDistributeFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      size: pageSize.value,
      current: currPage.value
    }
    const res = await postDistribute(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error('获取岗位分布数据失败:', error)
  }
}

const getCode = (orgCode, jobClassCode) => {
  jobClassCode.value = jobClassCode
  orgCode.value = orgCode
  staffSupplyFun()
  postDistributeFun()
}

const handleCurrentChange = currPage => {
  currPage.value = currPage
  postDistributeFun()
}

const handleSizeChange = size => {
  pageSize.value = size
  postDistributeFun()
}

onMounted(() => {
  staffSupplyFun()
  postDistributeFun()
  filterData.value = route.attrs.filterData
})
</script>

<style scoped lang="scss"></style>
