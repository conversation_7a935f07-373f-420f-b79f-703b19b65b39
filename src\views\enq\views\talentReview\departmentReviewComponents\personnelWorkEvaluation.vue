<template>
  <div class="task_confirmation_main">
    <div class="department_main marginT_16">
      <div class="personnel_item_wrap_left">
        <div
          class="personnel_item"
          v-for="(item, index) in personnelData"
          :class="{ curr: currIndex === index }"
          :key="index"
          @click="() => selectPersonnel(item, index)"
        >
          <span>{{ item.userName }}</span>
          <i class="icon disc"></i>
        </div>
      </div>
      <div class="personnel_item_wrap_right">
        <div class="content">
          <div class="table_box">
            <personnelWorkEvaluationSelect :workData="workData.list"></personnelWorkEvaluationSelect>
          </div>
          <div class="list_box">
            <div class="item_box">
              <div class="item_title">月度工时</div>
              <div class="item_value">{{ workData.workingHoursM }}H</div>
            </div>
            <div class="item_box">
              <div class="item_title">自评饱和度</div>
              <div class="item_value">{{ workData.selfEvalDaturation }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getDirectSubordinates, personalJobEval } from '../../../request/api'
import personnelWorkEvaluationSelect from './personnelWorkEvaluationSelect.vue'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number]
})

const currIndex = ref(0)
const personnelData = ref([])
const workData = ref({})

const getDeptUserPostFun = () => {
  const params = {
    enqId: props.enqId,
    type: 'work'
  }
  getDirectSubordinates(params).then(res => {
    if (res.code == '200') {
      personnelData.value = res.data
      getUserMessFun(personnelData.value[currIndex.value])
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const getUserMessFun = data => {
  const params = {
    enqId: props.enqId,
    userId: data.userId
  }
  personalJobEval(params).then(res => {
    if (res.code == '200') {
      workData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const selectPersonnel = (item, index) => {
  currIndex.value = index
  getUserMessFun(item)
}

onMounted(() => {
  getDeptUserPostFun()
})
</script>

<style scoped lang="scss">
.department_main {
  display: flex;
  .personnel_item_wrap_left {
    width: 200px;
    padding-right: 15px;
    border-right: 1px solid #ccc;
    .personnel_item {
      line-height: 30px;
      padding: 0 8px;
      color: #525e6c;
      font-size: 14px;
      background: #f8f8f8;
      margin-bottom: 5px;
      font-weight: bold;
      cursor: pointer;

      &.completed {
        color: #0099fd;
        background: #eef5fb;

        .icon {
          display: block;
        }
      }

      &.curr {
        background: #0099fd;
        color: #fff;

        .icon {
          display: block;
          color: #fff;

          &.disc {
            background: #fff;
          }
        }
      }

      .icon {
        display: none;
        float: right;
        font-weight: bold;
        line-height: 30px;
        text-align: center;
        color: #0099fd;

        &.disc {
          width: 8px;
          height: 8px;
          margin: 10px 4px 0 auto;
          border-radius: 50%;
          background: #ffc000;
        }
      }
    }
  }
  .personnel_item_wrap_right {
    padding-left: 15px;
    width: calc(100% - 200px);
    .project_item {
      padding: 10px 0;
    }
    .content {
      display: flex;
      .table_box {
        width: 80%;
      }
      .list_box {
        width: 20%;
        padding: 15px;
        .item_box {
          margin-bottom: 30px;
          border: 1px solid #0099ff;
          height: 80px;
          text-align: center;
          font-size: 15px;
          padding: 18px 0;
          .item_title {
            color: #666;
          }
          .item_value {
            color: #0099ff;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
