<template>
  <div class="report_page" id="page1">
    <div class="report_section">
      <div class="page_second_title">整体敬业度</div>
      <div class="report_section_content clearfix">
        <div class="chart_box overflow_y_hidden" id="fan_chart"></div>
        <div class="explain_text">
          员工敬业度是员工对于企业在情感和智力上的参与和承诺，通过大量的数据分析与跟踪调查，绩效表现较好的企业中有超过65%的员工敬业，并且拥有更优的经营业绩，而最佳表现的企业敬业度甚至能达到85%，而员工敬业度得分低于45%的企业，其经营业绩开始出现下滑。整体上，本次评估整体敬业度为
          {{
            wholeAvg
          }}%企业高敬业推动企业快速发展，企业需要保持员工的高敬业度，从而促进企业的跨越式发展
        </div>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">敬业度分布</div>
      <div class="report_section_content clearfix">
        <div class="chart_box_wrap">
          <div class="item_title">
            离职风险矩阵 <span>{{ jyDistributeOverall }}</span>
          </div>
          <div class="chart_box" id="jyDistributeChart"></div>
        </div>
        <div class="explain_text">
          根据员工敬业得分情况将员工分为“敬业”，“基本敬业”，“不敬业”和“完全不敬业”。基本敬业的员工介于不敬业和敬业之间，他们对工作的投入度较低，或对工作的留任意愿较低，或对公司工作的认同度低；但基本敬业的员工最易转变为敬业员工，是未来敬业度提升的机会点；公司目前有8.33%的基本敬业员工。不敬业的员工对工作缺乏投入，或不愿再在公司留、任，或对公司、工作的认同度很低；这批员工的扭转会比较困难。完全不敬业的员工，在提升无效的情况下，应列入公司淘汰员工的考虑对象。
        </div>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">下级部门敬业度情况</div>
      <div class="report_section_content clearfix">
        <div
          class="chart_box factor"
          :id="isPdf ? 'subOrgjydChartPdf' : 'subOrgjydChart'"
        ></div>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">具体敬业度表现</div>
      <div class="report_section_content clearfix">
        <!-- :spanMethod="objectSpanMethod" -->
        <tableComp
          :tableData="tableData"
          :needPagination="false"
          :overflowTooltip="false"
          :border="true"
        ></tableComp>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">敬业度驱动因素</div>
      <div class="report_section_content clearfix">
        <div class="chart_box factor" id="jyDriversChart">
          <div id></div>
        </div>
        <!-- <div class="table_wrap flex_row_between">
                    <div class="top_table marginR_16">
                        <tableComp
                            :tableData="itemScoreRankingTOP"
                            :needPagination="false"
                            :overflowTooltip="false"
                            :border="true"
                        ></tableComp>
                    </div>
                    <div class="last_table">
                        <tableComp
                            :tableData="itemScoreRankingLAST"
                            :needPagination="false"
                            :overflowTooltip="false"
                            :border="true"
                        ></tableComp>
                    </div>
                </div> -->
      </div>
    </div>
  </div>
</template>
 
<script>
import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js";
import { getOrgEngagement } from "../../../../../request/api.js"; //敬业度新接口

import tableComp from "@/components/talent/tableComps/tableComponent";
export default {
  name: "",
  props: ["enqId", "orgCode", "isPdf", "userId"],
  components: { tableComp },
  data() {
    return {
      pageData: "",
      fanChart: {
        data: [],
        actual: "",
      },
      wholeAvg: "",
      jyDistributeChart: {
        data: [],
      },
      jyDistributeOverall: "",
      engagementData: {
        data: [],
        legend: [],
      },
      tableData: {
        columns: [
          {
            label: "词典分类",
            prop: "parentModuleName",
            width: "100",
          },
          {
            label: "对应意愿",
            prop: "moduleName",
            width: "100",
          },
          {
            label: "行为名称",
            prop: "itemName",
            width: "250",
          },
          {
            label: "得分",
            prop: "score",
            width: "80",
          },
          {
            label: "行为描述",
            prop: "itemDesc",
          },
        ],
        data: [],
      },
      jyDriversChart: {
        data: [],
        padding: 100,
      },
    };
  },
  created() {
    this.getOrgEngagementFun();
  },
  mounted() {},
  methods: {
    getOrgEngagementFun() {
      getOrgEngagement({
        enqId: this.enqId,
        orgCode: this.orgCode,
        userId: this.userId,
      }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.pageData = res.data;
          // console.log(this.pageData)
          this.initData();
        }
      });
    },
    initData() {
      if (this.pageData) {
        // 整体敬业度
        this.fanChart.data = this.pageData.modelGrades.map((item) => {
          return {
            name: item.gradeName + "%",
            min: item.beginScore + "%",
            max: item.endScore + "%",
          };
        });
        this.fanChart.actual = this.pageData.wholeAvg + "%";
        echartsRenderPage("fan_chart", "Fan", "280", "280", this.fanChart);
        this.wholeAvg = this.pageData.wholeAvg;
        // 敬业度分布
        this.jyDistributeOverall =
          this.pageData.jyDistributeChart[
            this.pageData.jyDistributeChart.length - 1
          ].value;
        this.pageData.jyDistributeChart.pop();
        this.jyDistributeChart.data = this.pageData.jyDistributeChart;
        echartsRenderPage(
          "jyDistributeChart",
          "YBar",
          "300",
          "220",
          this.jyDistributeChart
        );
        // 下级部门敬业度情况
        this.engagementData.data = this.pageData.engagement.chartData;
        this.engagementData.legend = this.pageData.engagement.legend;
        let id = this.isPdf ? "subOrgjydChartPdf" : "subOrgjydChart";
        echartsRenderPage(id, "XBar", "1100", "260", this.engagementData);
        // 具体敬业度表现
        this.tableData.data = this.pageData.jydOrgList;
        // 敬业驱动因素
        this.jyDriversChart.data = this.pageData.jyDriversChart.map((item) => {
          return {
            name: item.moduleName,
            value: item.score,
          };
        });
        echartsRenderPage(
          "jyDriversChart",
          "XBar",
          "1100",
          "260",
          this.jyDriversChart
        );
      }
    },
  },
  watch: {
    // pageData(val) {
    //   console.log(val);
    //   this.initData();
    // },
  },
};
</script>
 
<style scoped lang="scss">
.report_section {
  margin-bottom: 32px;
  .chart_box_wrap {
    width: 300px;
    .item_title {
      span {
        font-weight: 600;
      }
    }
  }
}
.chart_box {
  float: left;
  width: 300px;
  height: 170px;
  margin-right: 32px;
  border: none;
  &.factor {
    width: 100%;
    height: 260px;
    margin-bottom: 16px;
  }
}
.table_wrap {
  width: 100%;
  align-items: flex-start;
  .top_table,
  .last_table {
    flex: 1;
  }
}
.explain_text {
  overflow: hidden;
  color: #212121;
  line-height: 24px;
}
</style>