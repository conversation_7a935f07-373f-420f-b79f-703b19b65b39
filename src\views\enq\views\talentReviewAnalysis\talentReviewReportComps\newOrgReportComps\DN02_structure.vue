<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <div class="page_second_title">
            人员数量与结构
            <div class="fr">人数：{{ userNum }}人</div>
        </div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
            <!-- <el-col :span="24">
                <div class="item_title">近一年新进人员</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentChange"
                    @handleSizeChange="handleSizeChange"
                    :tableData="tableData"
                    :needPagination="!isPdf"
                    :overflowTooltip="!isPdf"
                ></tableComps>
                <div
                    class="table_tip"
                    v-if="isPdf && tableData.page.total > size"
                >
                    更多数据请查看网页版报告
                </div>
            </el-col> -->
            <!-- <el-col :span="24">
                <div class="item_title">近一年流失人员</div>
                <tableComps
                    :needIndex="true"
                    @handleCurrentChange="handleCurrentChange2"
                    @handleSizeChange="handleSizeChange2"
                    :tableData="tableData2"
                    :needPagination="!isPdf"
                ></tableComps>
                <div class="table_tip" v-if="isPdf && tableData2.data.length > 10">更多数据请查看网页版报告</div>
            </el-col> -->
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import { orgPersonnelNum, orgPersonnelChanges } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import listComp from "./components/listComp.vue";

    export default {
        name: "orgRStructure",
        props: ["enqId", "orgCode", "isPdf"],
        components: { tableComps, listComp },
        data() {
            return {
                userNum: null,
                size: 50,
                current: 1,
                size2: 10,
                current2: 1,
                chartDom: [
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "性别结构",
                        elSpan: 6,
                        chartType: "YBar",
                        dataKey: "age",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "年龄结构",
                        elSpan: 6,
                        chartType: "YBar",
                        dataKey: "age",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "学历结构",
                        elSpan: 6,
                        chartType: "YBar",
                        dataKey: "qualification",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "职群结构",
                        elSpan: 6,
                        chartType: "YBar",
                        dataKey: "jobClass",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "工作年限结构",
                        elSpan: 8,
                        chartType: "YBar",
                        dataKey: "experience",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "主要岗位职层工作年限结构",
                        elSpan: 16,
                        chartType: "XStack",
                        dataKey: "jobLevelWorkingTime",
                    },
                    
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "近一年新进人员年龄结构",
                        elSpan: 8,
                        chartType: "YBar",
                        dataKey: "ageAYear",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "近一年新进人员学历结构",
                        elSpan: 8,
                        chartType: "YBar",
                        dataKey: "qualificationAYear",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "近一年新进人员职群结构",
                        elSpan: 8,
                        chartType: "YBar",
                        dataKey: "jobClassAYear",
                    },
                    
                ],
                listArr: [
                    {
                        title: "近一年新进人员",
                        ajaxUrl: orgPersonnelChanges,
                        otherParams:{
                            type:'A'
                        },
                        columns: [
                            {
                                label: "部门",
                                prop: "org_name",
                            },
                            {
                                label: "姓名",
                                prop: "user_name",
                            },
                            {
                                label: "岗位族群",
                                prop: "parent_job_class_name",
                            },
                            {
                                label: "岗位序列",
                                prop: "job_class_name",
                            },
                            {
                                label: "岗位",
                                prop: "post_name",
                            },
                            {
                                label: "年龄结构",
                                prop: "age",
                            },
                            {
                                label: "学历结构",
                                prop: "qualification",
                            },
                            {
                                label: "人才类别",
                                prop: "talent_type",
                            },
                        ],
                    },
                ],
                columns: [
                    {
                        label: "部门",
                        prop: "org_name",
                    },
                    {
                        label: "姓名",
                        prop: "user_name",
                    },
                    {
                        label: "岗位族群",
                        prop: "parent_job_class_name",
                    },
                    {
                        label: "岗位序列",
                        prop: "job_class_name",
                    },
                    {
                        label: "岗位",
                        prop: "post_name",
                    },
                    {
                        label: "年龄结构",
                        prop: "age",
                    },
                    {
                        label: "学历结构",
                        prop: "qualification",
                    },
                    {
                        label: "人才类别",
                        prop: "talent_type",
                    },
                ],
                tableData: {
                    columns: [],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
                tableData2: {
                    columns: [],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                        total: 0,
                    },
                },
            };
        },
        created() {
            this.getData();
            this.orgPersonnelChangesFn("A");
            this.orgPersonnelChangesFn("B");
        },
        mounted() {},
        methods: {
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    if (chart.chartType == "XStack") {
                    }
                    let chartData = {
                        data: data[chart.dataKey],
                    };
                    if (chart.chartType == "XStack") {
                        chartData = data[chart.dataKey];
                    }
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgPersonnelNum(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.initChart(res.data);
                        this.userNum = res.data.userNum;
                        // let data = this.formatterData(
                        //     res.data,
                        //     [
                        //         "age",
                        //         "job_class_name",
                        //         "job_level_name",
                        //         "experience",
                        //     ],
                        //     "name"
                        // );
                        // console.log(data);
                        // this.initChart(data);
                    }
                });
            },
            orgPersonnelChangesFn(type) {
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                    type: type,
                };
                orgPersonnelChanges(params).then((res) => {
                    console.log(res);
                    this.$set(this.tableData, "columns", this.columns);
                    this.$set(this.tableData2, "columns", this.columns);

                    if (res.code == 200) {
                        if (type == "A") {
                            this.$set(this.tableData, "columns", this.columns);
                            this.$set(this.tableData, "data", res.data);
                            this.$set(this.tableData, "page", res.page);
                        } else {
                            this.$set(this.tableData2, "data", res.data);
                            this.$set(this.tableData2, "page", res.page);
                        }
                    }
                });
            },
            handleCurrentChange(current) {
                this.current = current;
                this.orgPersonnelChangesFn("A");
            },
            handleSizeChange(size) {
                this.size = size;
                this.orgPersonnelChangesFn("A");
            },
            handleCurrentChange2(current) {
                this.current2 = current;
                this.orgPersonnelChangesFn("B");
            },
            handleSizeChange2(size) {
                this.size2 = size;
                this.orgPersonnelChangesFn("B");
            },
            formatterData(data, nameArr, toName) {
                for (const key in data) {
                    if (Object.hasOwnProperty.call(data, key)) {
                        const res = data[key];
                        rename(res, nameArr, toName);
                    }
                }
                function rename(arr, nameArr, toName) {
                    nameArr.forEach((name) => {
                        arr.forEach((item) => {
                            if (item[name]) {
                                item[toName] = item[name];
                                delete item[name];
                            }
                        });
                    });
                }
                return data;
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>