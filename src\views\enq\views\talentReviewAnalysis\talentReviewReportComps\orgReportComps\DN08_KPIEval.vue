<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <slot></slot>
        <div class="page_second_title">KPI评价</div>
        <el-row :gutter="16">
            <el-col :span="8">
                <div class="item_title">KPI综合得分</div>
                <div class="text_center score">
                    <customProcess :size="250" :num="comprehensiveScore" />
                    <!-- <el-progress
                        type="circle"
                        :stroke-width="20"
                        :stroke-linecap="'butt'"
                        :percentage="comprehensiveScore"
                        :format="progressFromat"
                    ></el-progress> -->
                </div>
            </el-col>
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
            <!-- <el-col :span="24">
                <listComp
                    :options="listConfig"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col> -->
            <el-col :span="24">
                <div class="item_title">核心岗位人员KPI表现对比 （ 分公司总经理）</div>
                <div class="">
                    <KPITable></KPITable>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        getKPIEvaluate,
        getKPIIndicatorInformation,
        getKPIIndicatorDetails,
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import listComp from "./components/listComp.vue";
    import customProcess from "@/components/talent/common/customProcess.vue";
    import KPITable from "./components/KPITable.vue"

    export default {
        name: "DN08_KPIEval",
        props: {
            enqId: String,
            orgCode: String,
            isPdf: {
                type: Boolean,
                default: false,
            },
        },
        components: { tableComps, listComp, customProcess,KPITable },
        data() {
            return {
                comprehensiveScore: null,
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "KPI 综合表现分布",
                        elSpan: 16,
                        chartHeight: "200",
                        chartType: "XBar",
                        dataKey: "kpiPerformanceDistribution",
                    },
                ],
                listArr: [
                    {
                        title: "组织KPI指标详情",
                        ajaxUrl: getKPIIndicatorDetails,
                        columns: [
                            {
                                label: "指标名称",
                                prop: "kpiName",
                            },
                            {
                                label: "本组织",
                                prop: "score",
                            },
                            {
                                label: "同类组织平均",
                                prop: "orgScore",
                            },
                            {
                                label: "本组织排名",
                                prop: "sortNbr",
                            },
                        ],
                    },
                    {
                        title: "KPI指标信息",
                        ajaxUrl: getKPIIndicatorInformation,
                        columns: [
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                            {
                                label: "岗位",
                                prop: "postName",
                            },
                            {
                                label: "指标名称",
                                prop: "kpiName",
                            },
                            {
                                label: "目标",
                                prop: "kpiObjective",
                            },
                            {
                                label: "实际表现",
                                prop: "kpiActual",
                            },
                            {
                                label: "上级评价",
                                prop: "supScore",
                            },
                            {
                                label: "HR总监",
                                prop: "userName2",
                            },
                            {
                                label: "综合得分",
                                prop: "overallScore",
                            },
                            {
                                label: "综合评价",
                                prop: "overallMerit",
                            },
                        ],
                    },
                ],
            };
        },
        created() {
            this.getData();
        },
        mounted() {},
        methods: {
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                        padding: 115

                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getKPIEvaluate(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.comprehensiveScore = res.data.comprehensiveScore;
                        this.initChart(res.data);
                    }
                });
            },
            progressFromat(val) {
                console.log(val);
                return val;
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .score {
        margin-top: 30px;
    }
    // .item_content {
    //     margin-top: 32px;
    //     .content_item {
    //         .content_title {
    //             margin-bottom: 16px;
    //         }
    //         .content {
    //             color: #0099ff;
    //             font-size: 16px;
    //             font-weight: bold;
    //         }
    //     }
    // }
</style>