<template>
  <el-dialog
    :title="props.popupTitle ? '新增职层' : '修改职层'"
    v-model="dialogVisible"
    @close="$emit('update:show', false)"
    width="40%"
    center
  >
    <div class="line_wrap flex_row_betweens">
      <span>选择上级职层：</span>
      <div>
        <el-cascader
          ref="cascaderJobLevel"
          :options="treeData"
          v-model="parentCodes"
          :placeholder="props.popupTitle ? '请选择职层' : parentJobLevelCode ? '' : '尚无上级职层'"
          :change-on-select="true"
          :props="{
            label: 'value',
            value: 'code',
            expandTrigger: 'hover'
          }"
          @change="handleItemChange"
          :key="cascaderKey"
          :disabled="!props.popupTitle"
          clearable
        />
      </div>
    </div>

    <div class="line_wrap flex_row_betweens">
      <span>职层名称：</span>
      <div>
        <el-input v-model="jobLevelName" />
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>职层描述：</span>
      <div>
        <el-input type="textarea" v-model="jobLevelDesc" />
      </div>
    </div>
    <div class="line_wrap flex_row_betweens">
      <span>排序号：</span>
      <div>
        <el-input v-model.number="sortNbr" type="number" min="1" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer text_right">
        <el-button class="page_clear_btn" @click="cancel">取 消</el-button>
        <el-button class="page_add_btn" type="primary" @click="submitBtn">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { jobLevelTree, createJobLevel, updateJobLevel, getJobLevelInfo } from '../../request/api'
import { formatterData } from '@/utils/utils'

// Props定义
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  popupTitle: {
    type: [Boolean, String],
    default: ''
  },
  checkedId: {
    type: String,
    default: ''
  },
  isDeleteSign: {
    type: Boolean,
    default: false
  },
  tebleEditId: {
    type: String,
    default: ''
  }
})

// Emits定义
const emit = defineEmits(['update:show'])

// 响应式状态
const dialogVisible = computed({
  get: () => props.show,
  set: value => emit('update:show', value)
})

const cascaderKey = ref(1)
const treeData = ref([])
const jobLevelName = ref('')
const jobLevelDesc = ref('')
const sortNbr = ref('')
const parentJobLevelCode = ref('')
const parentCodes = ref('')

// 方法定义
const jobLevelTreeFun = async () => {
  try {
    const res = await jobLevelTree({})
    if (res.length > 0) {
      treeData.value = formatterData(res)
    } else {
      treeData.value = []
    }
  } catch (error) {
    console.error('获取职层树失败:', error)
    ElMessage.error('获取职层树失败')
  }
}

const handleItemChange = val => {
  if (val) {
    parentJobLevelCode.value = val[val.length - 1]
  }
}

const clearFormData = () => {
  jobLevelDesc.value = ''
  jobLevelName.value = ''
  parentJobLevelCode.value = ''
  parentCodes.value = ''
  sortNbr.value = ''
}

const getJobLevelInfoFun = async val => {
  try {
    const res = await getJobLevelInfo({
      jobLevelCode: val
    })

    if (res.data) {
      jobLevelDesc.value = res.data.jobLevelDesc
      jobLevelName.value = res.data.jobLevelName
      parentJobLevelCode.value = res.data.parentJobLevelCode
      parentCodes.value = res.data.parentCodes ? res.data.parentCodes.reverse() : ''
      sortNbr.value = res.data.sortNbr
    } else {
      clearFormData()
    }
  } catch (error) {
    console.error('获取职层信息失败:', error)
    ElMessage.error('获取职层信息失败')
  }
}

const createJobLevelFun = async () => {
  if (!jobLevelDesc.value && !jobLevelName.value && !parentJobLevelCode.value && !sortNbr.value) {
    ElMessage.warning('请完善信息！')
    return
  }

  if (!jobLevelName.value) {
    ElMessage.warning('请填写职层名称！')
    return
  }

  if (!sortNbr.value) {
    ElMessage.warning('请填写排序号！')
    return
  }

  try {
    const res = await createJobLevel({
      jobLevelDesc: jobLevelDesc.value,
      jobLevelName: jobLevelName.value,
      parentJobLevelCode: parentJobLevelCode.value,
      sortNbr: sortNbr.value
    })

    if (res.code == 200) {
      await jobLevelTreeFun()
      dialogVisible.value = false
      ElMessage.success(res.msg)
    } else {
      ElMessage.warning(res.msg)
    }
  } catch (error) {
    console.error('创建职层失败:', error)
    ElMessage.error('创建职层失败')
  }
}

const updateJobLevelFun = async val => {
  try {
    const res = await updateJobLevel({
      jobLevelCode: val,
      jobLevelDesc: jobLevelDesc.value,
      jobLevelName: jobLevelName.value,
      parentJobLevelCode: parentJobLevelCode.value || '',
      sortNbr: sortNbr.value
    })

    if (res.code == 200) {
      await jobLevelTreeFun()
      dialogVisible.value = false
      ElMessage.success(res.msg)
    } else {
      ElMessage.warning(res.msg)
    }
  } catch (error) {
    console.error('更新职层失败:', error)
    ElMessage.error('更新职层失败')
  }
}

const cancel = () => {
  dialogVisible.value = false
}

const submitBtn = () => {
  if (props.popupTitle) {
    createJobLevelFun()
  } else {
    updateJobLevelFun(props.tebleEditId || props.checkedId)
  }
}

// 生命周期钩子
onMounted(() => {
  jobLevelTreeFun()
})
</script>

<style scoped>
.line_wrap {
  margin-bottom: 20px;
}

.line_wrap span {
  width: 120px;
  text-align: right;
  margin-right: 10px;
  color: #606266;
}

.line_wrap div {
  flex: 1;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

:deep(.el-dialog__header) {
  background-color: var(--color-tagbg);
  padding: 15px 20px;
}

:deep(.el-input),
:deep(.el-cascader) {
  width: 100%;
}
</style>
