<template>
  <div class="view_report_main">
    <div class="report_title">
      {{ reportTitle }} {{ startTime | removeTime }}
      <div class="goback_geader" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="">
      <div class="filter_tree_wrap fl">
        <div class="filter_tree_title">部门筛选</div>
        <div class="filter_tree">
          <treeCompRadio :treeData="orgTreeData" @clickCallback="getOrgCode"></treeCompRadio>
        </div>
      </div>
      <div class="report_container">
        <div class="view_report_content dept">
          <div class="content_title clearfix">
            <span class="title_text">部门报告</span>
            <span>{{ orgReportTotal }} 份</span>
            <span class="fr">
              <el-input v-model.trim="orgName" @change="searchReport('org')" placeholder="搜索">
                <template v-slot:suffix>
                  <i @click="searchReport('org')" class="el-input__icon el-icon-search pointer"></i>
                </template>
              </el-input>
            </span>
          </div>
          <div class="content">
            <div class="flex_row_wrap_start">
              <router-link
                class="report_item clearfix"
                :to="{
                  path: '/talentReviewHome/talentReviewAnalysis/orgReport',
                  query: {
                    enqId: enqId,
                    orgCode: item.orgCode,
                    viewType: 'p'
                  }
                }"
                v-for="(item, index) in orgReportData"
                :key="item.orgCode"
              >
                <div class="report_index fl">{{ (orgReportCurrent - 1) * 10 + index + 1 }}.</div>
                <div class="report_name fl">
                  {{ item.orgName }}
                </div>
                <div class="report_date fr">
                  {{ item.startTime | removeTime }}
                </div>
              </router-link>
            </div>
            <paginationComps :total="orgReportTotal" @pageChange="orgReportPageChange"></paginationComps>
          </div>
        </div>
        <div class="view_report_content personnel">
          <div class="content_title clearfix">
            <span class="title_text">个人报告</span>
            <span>{{ userReportTotal }} 份</span>
            <span class="fr">
              <el-input v-model.trim="userName" @change="searchReport('user')" placeholder="搜索">
                <template v-slot:suffix>
                  <i @click="searchReport('user')" class="el-input__icon el-icon-search pointer"></i>
                </template>
              </el-input>
            </span>
          </div>
          <div class="content">
            <div class="flex_row_wrap_start">
              <router-link
                class="report_item clearfix"
                :to="{
                  path: '/talentReviewHome/talentReviewAnalysis/userReport',
                  query: {
                    enqId: enqId,
                    userId: item.userId,
                    postCode: item.postCode,
                    viewType: 'p'
                  }
                }"
                v-for="(item, index) in userReportData"
                :key="item.postCode + item.userId"
              >
                <div class="report_index fl">{{ (userReportCurrent - 1) * 10 + index + 1 }}.</div>
                <div class="report_name fl">{{ item.userName }}</div>
                <div class="report_date fl">{{ item.orgName }} / {{ item.postName }}</div>
              </router-link>
            </div>
            <paginationComps :total="userReportTotal" @pageChange="userReportPageChange"></paginationComps>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAllOrgInfo, queryOrgReportList, queryUserReportList, getEnqInfo } from '../../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import paginationComps from '@/components/talent/paginationComps/coustomPagination'

export default {
  name: 'reportProjectViewContent',
  props: [],
  components: { treeCompRadio, paginationComps },
  data() {
    return {
      enqId: null,
      orgName: '',
      orgCode: '',
      userName: '',
      reportTitle: '',
      startTime: '',
      orgReportData: [],
      orgReportSize: 10,
      orgReportCurrent: 1,
      orgReportTotal: 0,
      userReportData: [],
      userReportSize: 10,
      userReportCurrent: 1,
      userReportTotal: 0,
      orgTreeData: []
    }
  },
  created() {
    this.enqId = this.$route.query.enqId
    this.init()
  },
  mounted() {},
  methods: {
    init() {
      // this.queryOrgReportListFun();
      // this.queryUserReportListFun();
      this.getEnqInfoFun()
      this.getAllOrgInfoFun()
    },
    initPageData() {
      this.orgReportSize = 10
      this.orgReportCurrent = 1
      this.orgReportTotal = 0
      this.userReportSize = 10
      this.userReportCurrent = 1
      this.userReportTotal = 0
    },
    orgReportPageChange(size, current) {
      this.orgReportCurrent = current
      this.orgReportSize = size
      this.queryOrgReportListFun()
    },
    queryOrgReportListFun() {
      let params = {
        size: this.orgReportSize,
        current: this.orgReportCurrent,
        enqId: this.enqId,
        orgName: this.orgName,
        orgCode: this.orgCode
      }
      queryOrgReportList(params).then(res => {
        console.log(res)
        if (res.code == '200' && res.data) {
          this.orgReportData = res.data
          this.orgReportTotal = res.total
          // if(res.data.length > 0 ){
          // this.reportTitle = res.data[0].enqName;
          // this.startTime = res.data[0].startTime;
          // }
        }
      })
    },
    userReportPageChange(size, current) {
      this.userReportCurrent = current
      this.userReportSize = size
      this.queryUserReportListFun()
    },
    queryUserReportListFun() {
      let params = {
        size: this.userReportSize,
        current: this.userReportCurrent,
        enqId: this.enqId,
        userName: this.userName,
        orgCode: this.orgCode
      }
      queryUserReportList(params).then(res => {
        console.log(res)
        if (res.code == '200') {
          this.userReportData = res.data
          this.userReportTotal = res.total
        }
      })
    },
    searchReport(reportType) {
      if (reportType == 'org') {
        this.queryOrgReportListFun()
      } else if (reportType == 'user') {
        this.queryUserReportListFun()
      }
    },
    getAllOrgInfoFun() {
      getAllOrgInfo({ enqId: this.enqId }).then(res => {
        console.log(res)
        if (res.code == '200') {
          this.orgTreeData = res.data
        } else {
          this.$msg.error(res.msg)
        }
      })
    },
    getOrgCode(code) {
      this.orgCode = code
      this.initPageData()
      this.queryOrgReportListFun()
      this.queryUserReportListFun()
    },
    getEnqInfoFun() {
      getEnqInfo({ id: this.enqId }).then(res => {
        console.log(res)
        if (res.code == '200' && res.data) {
          this.reportTitle = res.data.enqName
          this.startTime = res.data.beginDate
        }
      })
    }
  },
  filters: {
    removeTime: function (val) {
      return val ? val.split(' ')[0] : ' '
    }
  }
}
</script>

<style scoped lang="scss">
.view_report_main {
  padding-top: 16px;
  .report_title {
    text-align: left;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 16px;
    position: relative;
    padding-left: 15px;
    &::after {
      content: '';
      position: absolute;
      width: 10px;
      height: 10px;
      background: #0099ff;
      border-radius: 50%;
      left: 0;
      top: 5px;
    }
    .goback_geader {
      cursor: pointer;
      float: right;
      display: inline-block;
      text-align: right;
      color: #0099ff;
      font-weight: normal;
    }
  }
  .filter_tree_wrap {
    width: 220px;
    margin-right: 16px;
    .filter_tree_title {
      line-height: 40px;
      background-color: #f4f4f4;
      color: #525e6c;
      padding-left: 16px;
    }
    .filter_tree {
      border: 1px solid #e5e5e5;
      min-height: 400px;
      border-top: none;
      max-height: 500px;
      overflow: auto;
    }
  }
  .report_container {
    overflow: hidden;
  }
  .view_report_content {
    margin-bottom: 16px;
  }
  .content_title {
    line-height: 40px;
    font-size: 14px;
    background-color: #f4f4f4;
    padding: 0 16px;
    .title_text {
      margin-right: 32px;
    }
  }
  .content {
    padding: 10px 16px 20px;
    border: 1px solid #e5e5e5;
    border-top: none;
    .report_item {
      width: 49%;
      padding: 0 16px 0 16px;
      line-height: 30px;
      margin-right: 1%;
      margin-bottom: 8px;
      cursor: pointer;
      border: 1px solid #e5e5e5;
      &:nth-of-type(2n) {
        margin-right: 0;
      }
      &:hover {
        background: #ebf4ff;
      }
      .report_index {
        width: 20px;
        margin-right: 5px;
      }
      .report_name {
        min-width: 80px;
        margin-right: 10px;
      }
    }
  }
}
</style>
