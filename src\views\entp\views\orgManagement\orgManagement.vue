<template>
  <div class="org_management_wrap bg_write">
    <div class="page_main_title flex_row_betweens">
      <div class="title">组织管理</div>
      <div class="go_back_btn" v-if="orgSafeguardSign" @click="gobBackBtn">
        <el-icon><ArrowLeft /></el-icon>返回
      </div>
    </div>
    <div class="page_section">
      <p class="duty_set_wrap flex_row_betweens" v-if="!orgSafeguardSign && noSetDeptLeader != 0">
        <span class="tips overflow_elps" :title="noSetDeptLeader">{{ noSetDeptLeader }}</span>
        <el-button type="primary" text class="oper_btn" v-if="noOrgLeader != 'Y'" @click="setDeptLeader">
          查看
        </el-button>
        <el-button type="primary" text class="oper_btn" v-if="noOrgLeader == 'Y'" @click="setDeptLeader">
          取消查看
        </el-button>
      </p>
      <div class="org_management_center clearfix">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title" :title="treeTitle">{{ treeTitle }}</div>
            <div class="btn_wrap" v-if="!orgSafeguardSign">
              <el-button type="primary" link class="oper_btn" @click="addTreeNode">新增</el-button>
              <el-button type="primary" link class="oper_btn" @click="editTreeNode">修改</el-button>
              <el-button type="primary" link class="oper_btn" @click="deleteTreeNode">删除</el-button>
            </div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio
              v-model="defaultCheckedKeys"
              :treeData="treeData"
              :defaultExpandAll="false"
              @clickCallback="clickCallback"
            />
          </div>
        </div>
        <div class="page_section_main page_shadow org_list_wrap" v-if="!orgSafeguardSign">
          <div class="filter_bar_wrap">
            <div class="flex_row_start">
              <div class="filter_item title">筛选</div>
              <div class="filter_item">
                <el-input v-model="filterName" placeholder="按组织名称查询">
                  <template #suffix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              <div class="filter_item">
                <el-button class="page_add_btn" type="primary" @click="keyWordSearch"> 查询 </el-button>
              </div>
            </div>

            <div class="filter_item">
              <el-button :class="{ froBid_import: hasOrgSign, page_add_btn: true }" type="primary" @click="orgImport">
                导入
              </el-button>
              <el-button class="page_add_btn" type="primary" @click="orgExport"> 导出 </el-button>
            </div>
          </div>
          <table-component
            :tableData="tableData"
            :needIndex="needIndex"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          >
            <template #oper>
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button
                    @click="tableEdit(scope.$index, tableData.data)"
                    type="primary"
                    :icon="Edit"
                    class="icon_edit"
                    link
                  />
                  <el-button
                    class="color_danger icon_del"
                    @click="tableDeleteRow(scope.$index, tableData.data)"
                    type="danger"
                    :icon="Delete"
                    link
                  />
                  <el-button
                    class="icon_set"
                    @click="tableDutyAllocation(scope.$index, tableData.data)"
                    type="primary"
                    :icon="Setting"
                    link
                  />
                </template>
              </el-table-column>
            </template>
          </table-component>
        </div>

        <div class="page_section_main page_shadow" v-if="orgSafeguardSign">
          <div class="page_second_title">组织信息</div>
          <div class="org_info_wrap">
            <div class="line_wrap flex_row_betweens">
              <span>组织名称：</span>
              <div>
                <el-input v-model="orgName" disabled />
              </div>
            </div>
            <div class="line_wrap flex_row_betweens">
              <span>上级组织：</span>
              <div>
                <el-cascader
                  :options="treeData"
                  v-model="parentOrgCode"
                  :props="{ label: 'value', value: 'code' }"
                  :show-all-levels="true"
                  disabled
                  clearable
                />
              </div>
            </div>
            <div class="line_wrap flex_row_betweens">
              <span>组织层级：</span>
              <div>
                <el-select v-model="orgLevelCode" disabled clearable placeholder="请选择">
                  <el-option
                    v-for="opt in orgLevelOptions"
                    :key="opt.codeName"
                    :label="opt.codeName"
                    :value="opt.dictCode"
                  />
                </el-select>
              </div>
            </div>
            <div class="line_wrap flex_row_betweens">
              <span>业务领域：</span>
              <div>
                <el-select v-model="bizDomainCode" disabled clearable placeholder="请选择">
                  <el-option
                    v-for="opt in bizDomainOptions"
                    :key="opt.codeName"
                    :label="opt.codeName"
                    :value="opt.dictCode"
                  />
                </el-select>
              </div>
            </div>
          </div>
          <div class="org_duty_allocation_wrap">
            <div class="page_second_title flex_row_betweens">
              <div>职责配置</div>
              <div>
                <el-button class="page_add_btn" plain type="primary" @click="allocationDuty('chooseDuty')">
                  选择职责
                </el-button>
                <el-button class="page_add_btn" plain type="primary" @click="allocationDuty('copyDuty')">
                  从其它组织复制
                </el-button>
              </div>
            </div>
            <table-component :tableData="dutyTableData" :needIndex="false" :needPagination="false">
              <template #oper>
                <el-table-column label="操作" width="150">
                  <template #default="scope">
                    <el-button
                      class="color_danger icon_del"
                      @click="tableDeleteDutyRow(scope.$index, dutyTableData.data)"
                      type="danger"
                      :icon="Delete"
                      link
                    />
                  </template>
                </el-table-column>
              </template>
            </table-component>
          </div>
        </div>
      </div>
    </div>
    <orgDeptTreePopUp
      v-model:show="show"
      :checkedId="checkedId"
      :popupTitleSign="popupTitleSign"
      :isDeleteSign="isDeleteSign"
      :tebleEditId="tebleEditId"
      @isUpdateSign="isUpdateSign"
    />
    <orgDutyAllocationPopUp v-model:showDA="showDA" :checkedId="checkedId" @allocatinDutySign="allocatinDutySign" />
    <orgDutyAllocationCopyPopUp v-model:showDAC="showDAC" :checkedId="checkedId" @copyDutySign="copyDutySign" />
    <org-import-popUp v-model:show="showPopUp" @importSign="importSign" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ArrowLeft, Search, Edit, Delete, Setting } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/modules/user'
import {
  getOrgDeptTree,
  getOrgDeptList,
  deleteOrgDept,
  getDomainList,
  getOrgLevelList,
  getOrgDeptInfo,
  getOrgRespList,
  deleteOrgResp,
  getNoOrgLeader,
  exportOrgData,
  exportDownload
} from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import orgDeptTreePopUp from '../tPopUpComps/orgDeptTreePopUp'
import orgDutyAllocationPopUp from '../tPopUpComps/orgDutyAllocationPopUp'
import orgDutyAllocationCopyPopUp from '../tPopUpComps/orgDutyAllocationCopyPopUp'
import orgImportPopUp from '../tPopUpComps/orgImportPopUp'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)
const treeTitle = computed(() => userStore.companyInfo?.companyName || '')

// 响应式状态
const needIndex = ref(true)
const defaultCheckedKeys = ref('')
const treeData = ref([])
const checkOrgCode = ref('')
const checkedId = ref('')
const tableDeleteId = ref('')
const isLastNodeSign = ref('')
const state = ref('')
const filterName = ref('')
const stateOption = ref([])

// 表格数据
const tableData = reactive({
  columns: [
    {
      label: '组织外部编码',
      prop: 'orgCodeExtn'
    },
    {
      label: '组织名称',
      prop: 'orgName'
    },
    {
      label: '上级组织',
      prop: 'supOrg'
    },
    {
      label: '组织层级',
      prop: 'orgLevelName'
    },
    {
      label: '部门负责人',
      prop: 'orgLeaderName'
    },
    {
      label: '业务领域',
      prop: 'businessArea'
    }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})

// 弹窗状态
const show = ref(false)
const popupTitleSign = ref(false)
const isDeleteSign = ref(false)
const tebleEditId = ref('')
const showDA = ref(false)
const orgSafeguardSign = ref(false)
const orgName = ref('')
const parentOrgCode = ref([])
const rStatus = ref('')
const bizDomainCode = ref('')
const bizDomainOptions = ref([])
const orgLevelCode = ref('')
const orgLevelOptions = ref([])

// 职责表格数据
const dutyTableData = reactive({
  columns: [
    {
      label: '职责名称',
      prop: 'respName'
    },
    {
      label: '职责描述',
      prop: 'respDesc'
    }
  ],
  data: []
})

const showDAC = ref(false)
const noSetDeptLeader = ref('')
const noOrgLeader = ref('')
const showPopUp = ref(false)
const hasOrgSign = ref(false)

// 方法
const getNoOrgLeaderFun = async () => {
  try {
    const res = await getNoOrgLeader()
    noSetDeptLeader.value = '个部门未设置负责人'
    if (res.code == 200) {
      noSetDeptLeader.value = res.data.count == 0 ? res.data.count : res.data.count + '个部门未设置负责人'
    }
  } catch (error) {
    console.error('获取未设置部门负责人信息失败:', error)
  }
}

const getOrgTreeFun = async () => {
  try {
    getOrgDeptTree({
      companyId: companyId.value
    }).then(res => {
      if (res.code == 200) {
        treeData.value = res.data.length > 0 ? res.data : []
      } else {
        treeData.value = []
      }
    })
  } catch (error) {
    console.error('获取组织树失败:', error)
    treeData.value = []
  }
}

const getOrgDeptListFun = async () => {
  tableData.data = []

  try {
    const res = await getOrgDeptList({
      companyId: companyId.value,
      orgCode: checkedId.value,
      orgName: filterName.value,
      current: tableData.page.current,
      size: tableData.page.size,
      noOrgLeader: noOrgLeader.value
    })

    if (res.code == 200) {
      hasOrgSign.value = res.total > 0

      if (res.data.length > 0) {
        tableData.data = res.data.map(item => ({
          companyId: item.companyId,
          orgCodeExtn: item.orgCodeExtn,
          orgName: item.orgName,
          orgCode: item.orgCode,
          supOrg: item.parentOrgName,
          orgLevelName: item.orgLevelName,
          orgLeaderName: item.orgLeaderName,
          businessArea: item.bizDomainName
        }))
        tableData.page.total = res.total
      } else {
        tableData.data = []
        tableData.page.current = 1
      }
    } else {
      tableData.data = []
      tableData.page.current = 1
    }
  } catch (error) {
    console.error('获取组织列表失败:', error)
    tableData.data = []
    tableData.page.current = 1
  }
}

const handleSizeChange = val => {
  tableData.page.size = val
  getOrgDeptListFun()
}

const handleCurrentChange = val => {
  tableData.page.current = val
  getOrgDeptListFun()
}

const clickCallback = (val, isLastNode, data) => {
  console.log('clickCallback', val, isLastNode, data)
  defaultCheckedKeys.value = val

  checkedId.value = val
  if (orgSafeguardSign.value) {
    getOrgDeptInfoFun(val)
    getOrgRespListFun(val)
  } else {
    isLastNodeSign.value = isLastNode
    tableDeleteId.value = ''
    tableData.page.current = 1
    getOrgDeptListFun()
  }
}

const addTreeNode = () => {
  popupTitleSign.value = true
  show.value = true
}

const editTreeNode = () => {
  tebleEditId.value = ''
  if (!checkedId.value) {
    ElMessage.warning('请选择节点！')
    return
  }
  popupTitleSign.value = false
  show.value = true
}

const deleteTreeNode = async () => {
  if (!checkedId.value) {
    ElMessage.warning('请选择节点！')
    return
  }

  try {
    await ElMessageBox.confirm('确定删除?', '确定', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteOrgDeptFun(companyId.value, checkedId.value)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除组织失败:', error)
    }
  }
}

const tableDeleteRow = async (index, rows) => {
  tableDeleteId.value = rows[index].orgCode

  try {
    await ElMessageBox.confirm('确定删除?', '确定', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteOrgDeptFun(rows[index].companyId, rows[index].orgCode)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除组织失败:', error)
    }
  }
}

const deleteOrgDeptFun = async (cpId, code) => {
  isDeleteSign.value = false

  try {
    const res = await deleteOrgDept({
      companyId: cpId,
      orgCode: code
    })

    if (res.code == 200) {
      if (checkedId.value == tableDeleteId.value || isLastNodeSign.value) {
        checkedId.value = ''
        defaultCheckedKeys.value = ''
      }
      tableData.page.current = 1
      getOrgTreeFun()
      getOrgDeptListFun()
      isDeleteSign.value = true
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('删除组织失败:', error)
    ElMessage.error('删除组织失败')
  }
}

const tableEdit = (index, rows) => {
  tebleEditId.value = rows[index].orgCode
  popupTitleSign.value = false
  show.value = true
}

const isUpdateSign = isUpdateSign => {
  if (isUpdateSign) {
    defaultCheckedKeys.value = checkedId.value
    getOrgTreeFun()
    getOrgDeptListFun()
    getNoOrgLeaderFun()
  }
}

const keyWordSearch = () => {
  tableData.page.current = 1
  getOrgDeptListFun()
}

const allocatinDutySign = allocatinDutySign => {
  if (allocatinDutySign) {
    getOrgRespListFun(checkedId.value)
  }
}

const copyDutySign = copyDutySign => {
  if (copyDutySign) {
    getOrgRespListFun(checkedId.value)
  }
}

const gobBackBtn = () => {
  checkedId.value = ''
  defaultCheckedKeys.value = ''
  getOrgTreeFun()
  getOrgDeptListFun()
  orgSafeguardSign.value = false
}

const getDomainListFun = async () => {
  try {
    const res = await getDomainList({
      companyId: companyId.value,
      rStatus: rStatus.value
    })

    if (res.code == 200) {
      bizDomainOptions.value =
        res.data.length > 0
          ? res.data.map(item => ({
              dictCode: item.bizDomainCode,
              codeName: item.bizDomainName
            }))
          : []
    } else {
      bizDomainOptions.value = []
    }
  } catch (error) {
    console.error('获取业务领域列表失败:', error)
    bizDomainOptions.value = []
  }
}

const getOrgLevelListFun = async () => {
  try {
    const res = await getOrgLevelList({
      companyId: companyId.value,
      rStatus: rStatus.value
    })

    if (res.data.length > 0) {
      orgLevelOptions.value = res.data.map(item => ({
        dictCode: item.orgLevelCode,
        codeName: item.orgLevelName
      }))
    } else {
      orgLevelOptions.value = []
    }
  } catch (error) {
    console.error('获取组织层级列表失败:', error)
    orgLevelOptions.value = []
  }
}

const tableDutyAllocation = (index, data) => {
  defaultCheckedKeys.value = ''
  getOrgTreeFun()
  defaultCheckedKeys.value = data[index].orgCode
  checkedId.value = data[index].orgCode
  getOrgDeptInfoFun(data[index].orgCode)
  getOrgRespListFun(data[index].orgCode)
  orgSafeguardSign.value = true
}

const getOrgDeptInfoFun = async val => {
  try {
    const res = await getOrgDeptInfo({
      companyId: companyId.value,
      orgCode: val
    })

    if (res.code == 200) {
      orgName.value = res.data.orgName
      bizDomainCode.value = res.data.bizDomainCode
      parentOrgCode.value = res.data.parentOrgCode
      orgLevelCode.value = res.data.orgLevelCode
    } else {
      orgName.value = ''
      bizDomainCode.value = ''
      parentOrgCode.value = ''
      orgLevelCode.value = ''
    }
  } catch (error) {
    console.error('获取组织部门详情失败:', error)
    orgName.value = ''
    bizDomainCode.value = ''
    parentOrgCode.value = ''
    orgLevelCode.value = ''
  }
}

const getOrgRespListFun = async val => {
  try {
    const res = await getOrgRespList({
      orgCode: val
    })

    dutyTableData.data = []

    if (res.code == 200) {
      dutyTableData.data = res.data.map(item => ({
        respName: item.respName,
        respDesc: item.respDesc,
        respCode: item.respCode
      }))
    }
  } catch (error) {
    console.error('获取职责配置列表失败:', error)
    dutyTableData.data = []
  }
}

const allocationDuty = val => {
  if (!checkedId.value) {
    ElMessage.warning('请选择组织！')
    return
  }

  if (val == 'chooseDuty') {
    showDA.value = true
  } else {
    showDAC.value = true
  }
}

const tableDeleteDutyRow = async (index, data) => {
  try {
    await ElMessageBox.confirm('确定删除?', '确定', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    await deleteOrgRespFun(data[index].respCode)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除职责失败:', error)
    }
  }
}

const deleteOrgRespFun = async val => {
  try {
    const res = await deleteOrgResp({
      orgCode: checkedId.value,
      respCode: val
    })

    if (res.code == 200) {
      getOrgRespListFun(checkedId.value)
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('删除职责失败:', error)
    ElMessage.error('删除职责失败')
  }
}

const setDeptLeader = () => {
  if (noOrgLeader.value == 'Y') {
    noOrgLeader.value = ''
    getOrgDeptListFun()
  } else {
    noOrgLeader.value = 'Y'
    getOrgDeptListFun()
  }
}

const orgImport = () => {
  if (hasOrgSign.value) {
    ElMessage.warning('企业无组织时才有导入功能!')
    return
  }
  showPopUp.value = true
}

const orgExport = () => {
  exportOrgDataFun()
}

const exportOrgDataFun = async () => {
  try {
    const res = await exportOrgData({
      bizDomainCode: bizDomainCode.value
    })

    if (res.code == 200) {
      await exportDownloadFun(res.data)
    } else {
      ElMessage.warning(res.msg)
    }
  } catch (error) {
    console.error('导出组织数据失败:', error)
    ElMessage.error('导出组织数据失败')
  }
}

const exportDownloadFun = async val => {
  try {
    const res = await exportDownload({
      fileName: val
    })

    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '组织信息列表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

const importSign = importSign => {
  if (importSign) {
    getOrgDeptListFun()
  }
}

// 监听公司ID变化
watch(
  () => companyId.value,
  val => {
    if (val) {
      getOrgTreeFun()
      getDomainListFun()
      getOrgLevelListFun()
    }
  },
  { immediate: true }
)

// 初始化
onMounted(() => {
  getNoOrgLeaderFun()
})
</script>

<style scoped lang="scss">
.org_management_wrap {
  .page_main_title {
    .go_back_btn {
      padding-right: 20px;
      color: #0099ff;
      cursor: pointer;
      font-weight: normal;
      font-size: 14px;
    }
  }
  .froBid_import {
    background: #dcdfe6;
    border: 1px solid #dcdfe6;
    color: #666;
  }
  .duty_set_wrap {
    width: 220px;
    .tips {
      line-height: 30px;
      color: #f56c6c;
      font-size: 12px;
      width: 140px;
    }
    .oper_btn {
      // border:#EBF4FF !important;
    }
  }
  .org_list_wrap {
    .icon_set {
      margin: 0 0 0 15px;
      .el-icon-setting {
        font-size: 20px;
      }
    }
  }
  .page_second_title {
    margin: 0 0 10px 0;
    line-height: 30px;
    .page_add_btn {
      width: 120px;
    }
  }
  .page_second_title::before {
    top: 10px;
  }
  :deep(.org_info_wrap) {
    width: 400px;
    .line_wrap {
      margin: 0 0 10px 0;
      line-height: 40px;
      span {
        padding: 0 10px 0 0;
        width: 110px;
        text-align: right;
        .required_fields_icon {
          padding: 0;
          color: #f56c6c;
        }
      }
      div {
        flex: 1;
      }
      .el-cascader,
      .el-select {
        width: 100%;
        .el-input--suffix {
          width: 100%;
        }
      }
    }
  }
  .org_duty_allocation_wrap {
    padding: 10px 0 0 0;
    border-top: 1px solid #e5e5e5;
    .edu_info_header {
      .item {
        // flex: 1;
        width: 20%;
        &.desc {
          width: 65%;
        }
      }
      .item_icon_wrap {
        text-align: center;
        width: 5%;
      }
    }
  }
}
.aside_tree_title{
  .oper_btn{
    margin-left: 0;
  }
}
</style>
