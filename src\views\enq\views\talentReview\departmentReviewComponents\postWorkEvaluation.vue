<template>
  <div class="edu_info_wrap">
    <div class="clearfix">
      <div class="edu_info_center marginT_16">
        <div class="edu_info_header">
          <div class="item item_icon_wrap">序号</div>
          <div class="item item_icon_wrap">姓名</div>
          <div class="item item_icon_wrap">部门</div>
          <div class="item item_icon_wrap">岗位</div>
          <div class="item item_icon_wrap">职层</div>
          <div class="item">加班强度</div>
          <div class="item item_icon_wrap special">自评饱和度</div>
          <div class="item">实际工作饱度</div>
          <div class="item">目标是否量化</div>
          <div class="item">结果是否考核</div>
          <div class="item">工作效率表现</div>
          <div class="item">工作质量表现</div>
          <div class="item">工作目标达成</div>
        </div>
        <div class="edu_info_mmain">
          <postWorkEvaluationItem :eduInfoData="eduInfoData"></postWorkEvaluationItem>
          <div class="align_center marginT_30">
            <el-button class="page_confirm_btn" type="primary" @click="() => submit('nextStep')">确定</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { postJobEvalConfirm, postJobEvalList } from '../../../request/api.js'
import { objHasEmpty } from '@/utils/utils.js'
import { useUserStore } from '@/stores/modules/user.js'
import postWorkEvaluationItem from './postWorkEvaluationItem.vue'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number]
})

const submitFlag = ref(true)
const eduInfoData = ref([])
const userStore = useUserStore()
const userId = computed(() => userStore.userInfo.userId)

const submit = stepType => {
  if (!submitFlag.value) return
  if (checkData(eduInfoData.value)) {
    ElMessage.warning('请完善数据后提交！')
    return
  }
  submitFlag.value = false
  const arr = eduInfoData.value.map(item => ({
    overtimeNtensity: item.overtimeNtensity,
    goalQuantization: item.goalQuantization,
    actualWorkingSaturation: item.actualWorkingSaturation,
    resultAssessment: item.resultAssessment,
    workEfficiencyPerformance: item.workEfficiencyPerformance,
    workQualityPerformance: item.workQualityPerformance,
    workObjectivesReach: item.workObjectivesReach,
    userId: item.userId
  }))
  const params = {
    enqId: props.enqId,
    list: arr
  }
  postJobEvalConfirm(params).then(res => {
    if (res.code == '200') {
      ElMessage.success('保存成功!')
      getEducationData()
      submitFlag.value = true
    } else {
      submitFlag.value = true
      ElMessage.error('保存失败!')
    }
  })
}

const getEducationData = () => {
  postJobEvalList({
    enqId: props.enqId,
    superiorId: userId.value
  }).then(res => {
    if (res.code == '200') {
      eduInfoData.value = res.data
    } else {
      ElMessage.error('获取数据失败!')
    }
  })
}

const checkData = data => {
  for (let index = 0; index < data.length; index++) {
    const obj = data[index]
    if (
      objHasEmpty(obj, [
        'overtimeNtensity',
        'goalQuantization',
        'actualWorkingSaturation',
        'resultAssessment',
        'workEfficiencyPerformance',
        'workQualityPerformance',
        'workObjectivesReach'
      ])
    ) {
      return true
    }
  }
  return false
}

onMounted(() => {
  getEducationData()
})
</script>

<style scoped lang="scss">
.edu_info_wrap {
  margin-bottom: 16px;
}
.edu_info_header {
  .item {
    width: 9%;
    // padding-left: 15px;
  }

  .item_icon_wrap {
    text-align: center;
    width: 4%;
  }
  .special {
    width: 7%;
  }
}
</style>
