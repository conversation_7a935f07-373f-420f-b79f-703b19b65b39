<script setup>
import * as echarts from 'echarts'
import { onMounted, ref, watch } from 'vue'

const props = defineProps({
  // 图表配置
  options: {
    type: Object,
    default: () => ({})
  },
  // 图表类型：vertical(竖向)/horizontal(横向)
  type: {
    type: String,
    default: 'vertical'
  },
  // 是否显示折线
  showLine: {
    type: Boolean,
    default: false
  },
  // 是否堆叠
  stacked: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

// 更新图表配置
const updateChart = () => {
  if (!chartInstance) return

  const copyOptions = { ...props.options }
  delete copyOptions.xAxis
  delete copyOptions.yAxis
  delete copyOptions.series
  delete copyOptions.grid
  const baseOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: props.options.legend || []
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
      ...(props.options.grid || {}) // 保留用户自定义的 grid 配置
    },
    ...copyOptions
  }

  const series = props.options.series.map(item => ({
    ...item,
    type: props.showLine && item.type !== 'bar' ? 'line' : 'bar',
    stack: props.stacked ? 'total' : undefined
  }))

  const xAxis =
    props.type == 'horizontal'
      ? {
          ...(props.options.xAxis || {}),
          type: 'value'
        }
      : {
          ...(props.options.xAxis || {}),
          type: 'category',
          data: props.options.xAxisData || []
        }

  const yAxis =
    props.type == 'horizontal'
      ? {
          ...(props.options.yAxis || {}),
          type: 'category',
          data: props.options.xAxisData || []
        }
      : {
          ...(props.options.yAxis || {}),
          type: 'value'
        }

  chartInstance.setOption({
    ...baseOption,
    xAxis,
    yAxis,
    series
  })
}

// 监听窗口变化
const handleResize = () => {
  chartInstance?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

watch(() => props.options, updateChart, { deep: true })

defineExpose({
  getInstance: () => chartInstance
})
</script>

<template>
  <div ref="chartRef" style="width: 100%; height: 100%"></div>
</template>
<!-- 
## EChartsBar 图表组件文档

### 功能特性
- 支持横向/竖向柱状图展示
- 支持单柱图/多柱图显示
- 支持柱状+折线混合图表
- 支持数据堆叠显示
- 响应式设计，自动适应容器大小

### Props 配置

| 参数名    | 类型    | 默认值     | 说明                     |
|----------|---------|-----------|--------------------------|
| options  | Object  | {}        | 图表配置对象             |
| type     | String  | 'vertical'| 图表方向: vertical/horizontal |
| showLine | Boolean | false     | 是否显示折线             |
| stacked  | Boolean | false     | 是否启用堆叠显示         |

### options 配置示例
```javascript
const options = {
  xAxisData: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  legend: ['销量', '增长率'],
  series: [
    {
      name: '销量',
      type: 'bar',
      data: [120, 200, 150, 80, 70, 110, 130]
    },
    {
      name: '增长率',
      type: 'line',
      data: [0.1, 0.2, 0.15, 0.08, 0.07, 0.11, 0.13]
    }
  ]
} 
使用示例
<template>
  <div>
    <EChartsBar :options="options" />
  </div>
</template>

-->
