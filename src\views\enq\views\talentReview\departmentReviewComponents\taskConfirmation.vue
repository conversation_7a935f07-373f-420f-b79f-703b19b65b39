<template>
  <div class="task_confirmation_main marginT_30">
    <div class="page_second_title">工作任务确认</div>
    <div class="personnel_info marginT_20">
      <span class="name">{{ personnelName }}</span>
      <span class="department">{{ personnelDepartment }}</span>
      <div class="post_list_wrap flex_row_start">
        <div
          class="post_list"
          :class="{ active: postCode == item.postCode }"
          @click="changePost(item.postCode)"
          v-for="item in postList"
          :key="item.postCode"
        >
          {{ item.postName }}
        </div>
      </div>
    </div>
    <div class="department_main">
      <div class="personnel_item_wrap">
        <div
          class="personnel_item flex_row_between"
          :class="{ completed: item.confirmStatus == 'Y', curr: currIndex == index }"
          v-for="(item, index) in personnelList"
          :key="index"
          @click="selectPersonnel(index)"
        >
          <span>{{ item.userName }}</span>
          <i class="el-icon-check" v-if="item.confirmStatus == 'Y'"></i>
        </div>
      </div>
      <div class="personnel_table_wrap">
        <el-table :data="tableData">
          <el-table-column prop="jobActivityType" label="活动类型">
            <template #default="scope">
              <el-select class="show_select_type" v-model="scope.row.jobActivityType" size="small">
                <el-option
                  v-for="item in activityOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="jobActivityName" label="工作任务名称"></el-table-column>
          <el-table-column prop="workType" label="在司/出差">
            <template #default="scope">
              <el-select v-model="scope.row.workType" size="small">
                <el-option
                  v-for="item in workTypeOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="timePercentage" label="时间占比">
            <template #default="scope">
              <el-input v-model="scope.row.timePercentage" type="number" size="small">
                <template #append>%</template>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="overtimeFlag" label="需要加班">
            <template #default="scope">
              <el-select @change="overTimeChange(scope.row)" v-model="scope.row.overtimeFlag" size="small">
                <el-option
                  v-for="item in yesOrNo"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="overtimeFrequency" label="加班频率">
            <template #default="scope">
              <el-select v-model="scope.row.overtimeFrequency" :disabled="scope.row.overtimeFlag == 'N'" size="small">
                <el-option
                  v-for="item in overTimeOptions"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="activityScore" label="质量得分">
            <template #default="scope">
              <el-select v-model="scope.row.activityScore" size="small">
                <el-option v-for="i in 10" :key="i" :label="i" :value="i.toString()"></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="oper_btn_wrap align_right">
        <el-button class="page_add_btn" type="primary" @click="save">保存</el-button>
      </div>
    </div>
    <div class="oper_btn_wrap align_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDeptUserPost, getEnqOrgUserActivity, confirmEnqUserActivity } from '../../../request/api'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  orgCode: String
})

const emit = defineEmits(['prevStep', 'nextStep'])

const userId = ref(null)
const postList = ref([])
const postCode = ref(null)
const overTimeOptions = ref([])
const yesOrNo = ref([])
const workTypeOptions = ref([])
const activityOptions = ref([])
const currIndex = ref(0)
const personnelName = ref('')
const personnelDepartment = ref('')
const personnelPost = ref('')
const personnelList = ref([])
const tableData = ref([])

onMounted(async () => {
  const res = await window.$getDocList(['OVERTIME_FREQUENCY', 'YES_NO', 'WORK_TYPE', 'JOB_ACTIVITY_TYPE'])

  overTimeOptions.value = res.OVERTIME_FREQUENCY
  yesOrNo.value = res.YES_NO
  workTypeOptions.value = res.WORK_TYPE
  activityOptions.value = res.JOB_ACTIVITY_TYPE

  await getDeptUserPostFun()
})

const getDeptUserPostFun = async (refreshFlag = false) => {
  try {
    const res = await getDeptUserPost({
      enqId: props.enqId,
      orgCode: props.orgCode,
      moduleId: 'D07'
    })

    if (res.code == 200) {
      const data = res.data
      personnelList.value = data

      if (data.length == 0) {
        ElMessage.warning('没有人员列表')
        return
      }

      if (!refreshFlag) {
        personnelName.value = data[0].userName
        personnelDepartment.value = data[0].orgName
        postList.value = data[0].postNameList
        postCode.value = data[0].postNameList[0].postCode
        userId.value = data[0].userId
      }
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const getEnqOrgUserActivityFun = async () => {
  try {
    const res = await getEnqOrgUserActivity({
      enqId: props.enqId,
      postCode: postCode.value,
      userId: userId.value
    })

    if (res.code == 200) {
      tableData.value = res.data
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const save = async prevStep => {
  const params = []
  let timePercentageCount = 0

  tableData.value.forEach(item => {
    const obj = {
      activityScore: item.activityScore,
      enqId: item.enqId,
      jobActivityCode: item.jobActivityCode,
      overtimeFlag: item.overtimeFlag,
      overtimeFrequency: item.overtimeFrequency,
      postCode: item.postCode,
      timePercentage: item.timePercentage,
      userId: item.userId,
      workType: item.workType
    }
    timePercentageCount += parseInt(item.timePercentage)
    params.push(obj)
  })

  if (timePercentageCount !== 100) {
    ElMessage.warning('时间占比之和需要等于100')
    return
  }

  if (checkData(params)) {
    ElMessage.warning('请完善当前数据后保存')
    return
  }

  try {
    const res = await confirmEnqUserActivity(params)

    if (res.code == 200) {
      ElMessage.success(res.msg)
      if (prevStep) {
        emit('prevStep')
      }
      await getDeptUserPostFun(true)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('保存失败')
  }
}

const selectPersonnel = index => {
  currIndex.value = index
}

const overTimeChange = row => {
  if (row.overtimeFlag == 'N') {
    row.overtimeFrequency = '0'
  }
}

const changePost = newPostCode => {
  postCode.value = newPostCode
}

const checkData = data => {
  for (const obj of data) {
    if (window.$util.objHasEmpty(obj)) {
      console.log('有空值')
      return true
    }
  }
  return false
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      save('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') {
        emit('prevStep')
      }
    })
}

const nextBtn = () => {
  const list = personnelList.value
  for (const item of list) {
    if (item.confirmStatus == 'N') {
      ElMessage.warning(`请完善人员：${item.userName} 的工作活动数据！`)
      return
    }
  }
  emit('nextStep')
}

watch(
  () => currIndex.value,
  async val => {
    const data = personnelList.value[val]
    userId.value = data.userId
    personnelName.value = data.userName
    personnelDepartment.value = data.orgName
    personnelPost.value = data.postNameList[0].postName
    postList.value = data.postNameList
    postCode.value = data.postNameList[0].postCode
    await getEnqOrgUserActivityFun()
  }
)

watch(
  () => postCode.value,
  async () => {
    await getEnqOrgUserActivityFun()
  }
)
</script>

<style scoped lang="scss">
.el-select-dropdown__item.selected {
  color: #212121;
  font-weight: normal;
}
.personnel_info {
  font-weight: bold;
  background: #f4f4f4;
  line-height: 25px;
  margin-bottom: 16px;
  padding: 12px 16px;
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-end;
  .name {
    color: #0099ff;
    margin-right: 16px;
  }
  .department {
    // color: #0099FF;
    // margin-right: 16px;
    color: #449cff;
    border: 1px solid #449cff;
    padding: 1px 6px;
    line-height: 21px;
    border-radius: 3px;
    margin: 0 16px;
    background: #fff;
  }
  .post_list_wrap {
    .post_list {
      cursor: pointer;
      margin-right: 16px;
      &.active {
        color: #ffc000;
        // color: #0099FF;
      }
    }
  }
}
.personnel_item_wrap {
  width: 120px;
  float: left;
  margin-right: 16px;
  .personnel_item {
    line-height: 30px;
    padding: 0 8px;
    color: #525e6c;
    font-size: 14px;
    background: #f8f8f8;
    margin-bottom: 5px;
    font-weight: bold;
    cursor: pointer;
    &.completed {
      color: #0099ff;
      background: #ebf4ff;
      .icon {
        display: block;
      }
    }
    &.curr {
      background: #0099ff;
      color: #fff;
      .icon {
        display: block;
        color: #fff;
        &.disc {
          background: #fff;
        }
      }
    }
    .icon {
      display: none;
      float: right;
      font-weight: bold;
      line-height: 30px;
      text-align: center;
      color: #0099ff;

      &.disc {
        width: 8px;
        height: 8px;
        // margin: 10px 4px 0 auto;
        border-radius: 50%;
        background: #ffc000;
      }
    }
  }
}

.personnel_table_wrap {
  overflow: hidden;
}

// // 去除input number类型 加减箭头
// input::-webkit-outer-spin-button,
// input::-webkit-inner-spin-button {
//     -webkit-appearance: none;
// }

// 设置从数据字典中取出数据，用select做显示效果，禁止select原有功能
.show_select_type {
  background-color: transparent;
  pointer-events: none;
  .el-input__suffix {
    display: none;
    pointer-events: none;
  }
  .el-input__inner {
    background-color: transparent;
    border: none;
  }
  .el-input__inner {
    background-color: transparent;
    border: none;
  }

  // 去除select下拉箭头
  .el-input__suffix {
    display: none;
    pointer-events: none;
  }
}
.el-table tr {
  height: 45px;
}
</style>
