<script setup>
import Table from "@/components/table/simplenessTable.vue";

const columns = ref([
  {
    label: "人员姓名",
    prop: "a",
  },
  {
    label: "当前岗位",
    prop: "b",
  },
  {
    label: "核心能力表现",
    prop: "c",
  },
  {
    label: "当前岗位匹配度",
    prop: "d",
  },

  {
    label: "推荐岗位",
    prop: "e",
  },
  {
    label: "推荐岗位匹配度",
    prop: "f",
  },
  {
    label: "任用建议",
    prop: "h",
  },
  {
    label: "实施优先级",
    prop: "j",
    slot: "jSlot",
    width: 130,
  },
]);
const data = ref([
  {
    a: "王伟",
    b: "市场调研专员",
    c: "15",
    d: "35%",
    e: "海外市场战略分析师",
    f: "85% ",
    h: "评级调岗+专项培养",
    j: 3,
  },
]);

const columns2 = ref([
  {
    label: "主要能力优势",
    prop: "a",
  },
  {
    label: "目标岗位匹配主要能力缺口",
    prop: "b",
    width: 260,
  },
  {
    label: "决策偏好适配",
    prop: "c",
  },
  {
    label: "风险偏好适配",
    prop: "d",
  },
  {
    label: "团队协作偏好适配",
    prop: "e",
  },
  {
    label: "成本收益偏好适配",
    prop: "f",
  },
]);
const data2 = ref([
  {
    a: "1. 市场洞察与分析（87分，105%匹配度）",
    b: "1. 业务深化设计（57分，75%匹配度）",
    c: "数据驱动型（25%）",
    d: "中风险（30%）",
    e: "跨部门协同（25%）",
    f: "",
  },
]);

const columns3 = ref([
  {
    label: "优化维度",
    prop: "a",
  },
  {
    label: "能力/素质项",
    prop: "b",
  },
  {
    label: "当前水平",
    prop: "c",
  },
  {
    label: "目标岗位要求",
    prop: "d",
  },
  {
    label: "差距分析",
    prop: "e",
  },
  {
    label: "提升措施",
    prop: "f",
  },
]);
const data3 = ref([
  {
    a: "专项能力提升",
    b: "市场分析与战略规划",
    c: "68",
    d: ">65",
    e: " ",
    f: "",
  },
]);

onMounted(() => {});
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">人岗匹配建议</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        基于能力测评数据，精准识别团队成员能力与目标岗位胜任力要求的匹配程度，通过岗位调整实现
        “人岗适配”，最大化释放人才效能。
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">人岗匹配建议</div>
      <Table
        :roundBorder="false"
        :columns="columns"
        :data="data"
        headerColor
        showIndex
      >
        <template v-slot:jSlot="scope">
          <el-rate v-model="scope.row.j" disabled />
        </template>
      </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">人岗匹配建议</div>
      <Table
        :roundBorder="false"
        :columns="columns2"
        :data="data2"
        headerColor
        showIndex
      >
      </Table>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">人岗匹配优化建议</div>
      <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor>
      </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
