<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">族群分布</div>
          <div class="content_item_content" id="post_num"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">
            <span v-show="jobClassName">{{ jobClassName }}-</span>序列分布
          </div>
          <div class="content_item_content" id="post_personnel_num"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            岗位分布明细
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { postDistribution, queryPostList, exportData } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const jobClassName = ref('')
const orgCode = ref('')
const page = ref(1)
const size = ref(10)

const postData = reactive({
  data: []
})

const postPersonnelData = reactive({
  data: []
})

const filterData = ref([])

const tableData = reactive({
  columns: [
    {
      label: '一级组织',
      prop: 'onLevelName'
    },
    {
      label: '二级组织',
      prop: 'twoLevelName'
    },
    {
      label: '三级组织',
      prop: 'threeLevelName'
    },
    {
      label: '四级组织',
      prop: 'fourLevelName'
    },
    {
      label: '岗位族群',
      prop: 'parentJobClassName'
    },
    {
      label: '岗位序列',
      prop: 'jobClassName'
    },
    {
      label: '岗位名称',
      prop: 'postName'
    },
    {
      label: '职层',
      prop: 'jobLevelName'
    },
    {
      label: '职等',
      prop: 'jobGradeName'
    },
    {
      label: '对应职位名称',
      prop: 'jobName',
      width: 120
    },
    {
      label: '人数',
      prop: 'value',
      width: 60
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('post_num', 'YBar', '350', '400', postData)
  echartsRenderPage('post_personnel_num', 'YBar', '350', '400', postPersonnelData)
}

const postDistributionFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await postDistribution(params)
    if (res.code == 200) {
      postData.data = res.data.post
      postPersonnelData.data = res.data.postJobClass
      initChart()
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal, jobClassNameVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  jobClassName.value = jobClassNameVal
  page.value = 1
  postDistributionFun()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryPostList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error(error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'b'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '岗位分布明细')
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  postDistributionFun()
  getTableData()
})
</script>

<style scoped lang="scss"></style>
