<template>
    <div class="scatter_plot_main">
        <div :id="id" class="chart_dom" :style="styleObj"></div>
    </div>
</template>
 
<script>
export default {
    name: "quadrantScatterPlot",
    props: ["chartData", "width", "height"],
    data() {
        return {
            id: "",
            styleObj: {
                width: this.width + "px",
                height: this.height + "px"
            },
            // chartData:{
            //     nameList:['新增行业','衰退行业','上升行业','上升'],
            //     symbolList:['circle','triangle','rect','diamond'],
            //     dataList:[
            //         [
            //             ["29", "-40", ""],
            //             ["52", "-30", ""],
            //             ["35", "-50", ""],
            //             ["18", "-70", ""],
            //             ["49", "-15", ""],
            //             ["68", "-25", ""],
            //             ["39", "-40", ""]
            //         ],
            //         [
            //             ["23", "-15", ""],
            //             ["37", "-22", ""],
            //             ["17", "-42", ""],
            //             ["70", "-12", ""],
            //             ["47", "-32", ""],
            //         ],
            //         [
            //             ["17", "-65", ""],
            //             ["24", "-10", ""],
            //             ["37", "-42", ""],
            //             ["72", "-22", ""],
            //             ["47", "-22", ""],
            //             ["57", "-22", ""],
            //         ],
            //         [
            //             ["10", "-55", ""],
            //             ["24", "-25", ""],
            //             ["37", "-48", ""],
            //             ["72", "-32", ""],
            //             ["43", "-22", ""],
            //             ["67", "-22", ""],
            //         ],
            //     ]
            // },
            
        };
    },
    watch: {
        chartData: {
            handler() {
                this.init(this.chartData);
            },
            deep: true
        }
    },
    created() {},
    components: {},
    mounted() {
        if (this.chartData.dataList.length == 0) {
            return;
        }
        this.init(this.chartData);
    },
    methods: {
        init(chartData) {
            let id = this.$util.createRandomId();
            this.id = id;
            this.$nextTick(() => {
                this.toDraw(id, chartData);
            });
        },
        toDraw(id, data) {
            let _this = this;
            let myChart = this.$EC.init(document.getElementById(id));
            if (data.dataList.length == 0) {
                myChart.clear();
                return;
            }
            function typeShow(){
                let curSeries = []
                for(let i = 0;i<data.nameList.length;i++){
                    curSeries.push({
                        name: data.nameList[i],
                        type: 'scatter',
                        symbol: data.symbolList[i],   
                        data: data.dataList[i],
                        label: {
                            normal: {
                                show: true,
                                formatter: function(params) {
                                    return params.value[2];
                                },
                                position: 'right'
                            }
                        },
                    })
                }
                let splitPhase = {
                    name: '平均值',
                    type: 'scatter',
                    data: data,
                    markLine: {
                        symbol: 'none', //去掉箭头
                        lineStyle: {
                            normal: {
                                type: 'solid',
                                color: '#5cb87a'
                            }
                        },
                        data: [{
                                type: 'average',
                                name: '平均值'
                            }, {
                                yAxis: 0
                            },
                            {
                                xAxis: 50
                            },

                        ]
                    },
                }
                curSeries.push(splitPhase)
                return curSeries
            }
            let option = {
                grid: {
                    left: '0%',
                    right: '5%',
                    bottom: '5%',
                    containLabel: true,
                },
                color: ['#1F7EF9', '#3FFFB8', '#CA2648','#eccd8b'],
                tooltip: {},
                xAxis: [{
                    name: '综合得分',
                    nameLocation: 'middle',
                    nameGap:25,
                    nameTextStyle:{
                        height:60
                    },
                    type: 'value',
                    min: 0,
                    max: 100,
                    splitNumber: 5,
                    splitLine: {
                        show: false
                    },
                    // 坐标轴刻度
                    axisTick: {
                        "show": true
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#333'
                        },
                        onZero:false
                        
                    }
                }],
                yAxis: [{
                    name: '偏离度',
                    type: 'value',
                    min: -120,
                    max: 120,
                    interval: 40,
                    splitLine: {
                        show: true,
                        lineStyle:{
                            color:'#F5F7FA'
                        }
                    },
                    axisLabel: {},
                    // 坐标轴刻度
                    axisTick: {
                        "show": true
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#333'
                        }
                    }
                    
                }],
                series:typeShow(),  
            };
            myChart.setOption(option);
            myChart.resize()
        },
       
    }
};
</script>
 
<style scoped lang="scss">
.scatter_plot_main {
    width: 100%;
    margin: 0 auto;
}
</style>