<script setup>
import { Search } from "@element-plus/icons-vue";
import SectionTab from "../../components/sectionTab.vue";
import Tree from "@/components/tree/index.vue";

import staffI from "./staffI.vue";
import keyTask from "./keyTask.vue";
import staffAbility from "./staffAbility.vue";
import staffPower from "./staffPower.vue";
import engagement from "./engagement.vue";
import riskResignation from "./riskResignation.vue";
import talentDev from "./talentDev.vue";

const sectionTabList = ref([
  {
    name: "人员指标",
    code: 1,
  },
  {
    name: "关键任务",
    code: 2,
  },
  {
    name: "人员能力",
    code: 3,
  },
  {
    name: "人员动力",
    code: 4,
  },
  {
    name: "满意度&敬业度",
    code: 5,
  },
  {
    name: "离职风险",
    code: 6,
  },
  {
    name: "人才发展",
    code: 7,
  },
]);
const sectionTabCheckSign = ref(1);
const tabContentList = ref([
  staffI,
  keyTask,
  staffAbility,
  staffPower,
  engagement,
  riskResignation,
  talentDev,
]);
const checkSecTab = (c) => {
  sectionTabCheckSign.value = c;
};
</script>
<template>
  <div class="support_wrap">
    <div class="justify-start">
      <div class="left_menu_wrap marginR20">
        <div class="z_z">组织</div>
        <el-input
          class="marginB20"
          v-model="key"
          style="width: 100%; height: 30px"
          placeholder="按组织名称检索"
          :suffix-icon="Search"
        >
        </el-input>
        <Tree></Tree>
      </div>
      <div class="right_main">
        <div class="tab-wrap justify-between">
          <div class="Section_Tab">
            <SectionTab
              :sectionTabList="sectionTabList"
              :sectionTabCheckSign="sectionTabCheckSign"
              @checkSecTab="checkSecTab"
              :itemWidth="'12%'"
            ></SectionTab>
          </div>
          <div class="t_btn" @click="goBack">
            <span class="icon"></span>返回
          </div>
        </div>
        <div class="content-mian">
          <component :is="tabContentList[sectionTabCheckSign - 1]" />
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.support_wrap {
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
}
.left_menu_wrap {
  width: 240px;
  padding: 8px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #c6dbf3;
  .z_z {
    margin-bottom: 10px;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    text-align: center;
    background: #e3effa;
    border-radius: 8px 8px 8px 8px;
  }
}
.right_main {
  flex: 1;
  overflow: hidden;
}

.tab-wrap {
  .Section_Tab {
    flex: 1;
    :deep .section_tab_wrap {
      .s_tab_item {
        &.act {
          background: linear-gradient(
            -20deg,
            #0594fa 0%,
            #83c8f8 39%,
            #83c8f8 59%,
            #0594fa 99%
          );
          color: #fff;
          border-color: transparent !important;
        }
      }
    }
  }
  .t_btn {
    margin-top: 8px;
    width: 74px;
    height: 28px;
    line-height: 28px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #40a0ff;
    text-align: center;
    color: #40a0ff;
    cursor: pointer;
    .icon {
      display: inline-block;
      margin: 0px 6px -1px 0;
      width: 16px;
      height: 16px;
      background: url("@/assets/imgs/org/icon_06.png") no-repeat center;
      background-size: 100% 100%;
    }
  }
}
</style>
