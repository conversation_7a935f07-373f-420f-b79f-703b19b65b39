<template>
  <div class="post_relationship_map">
    <div class="map_center">
      <div class="sup_post" v-if="postData.supPost">
        <div class="item">
          <div class="item_title">
            <div class="post_name">{{ postData.supPost.postName }}</div>
            <div class="post_type">{{ postData.supPost.postType }}</div>
          </div>
          <div class="item_center">
            <div class="personnel_name personnels_name overflow_elps" :title="postData.supPost.name">
              {{ postData.supPost.name }}
            </div>
          </div>
          <div class="item_line"></div>
        </div>
      </div>
      <div class="curr_post">
        <div class="item">
          <div class="item_title">
            <div class="post_name">{{ postData.postName }}</div>
            <div class="post_type">{{ postData.postType }}</div>
          </div>
          <div class="item_center flex_row_betweens" v-if="postData.personnelNum > 0">
            <span class="personnel_name personnels_name overflow_elps" :title="postData.name">{{ postData.name }}</span>
            <div class="personnel_name">
              <i class="bold">{{ postData.personnelNum }}</i>
              <span>人</span>
            </div>
          </div>
          <div class="item_center" v-if="!postData.personnelNum">
            <span class="personnel_name">暂无</span>
          </div>
        </div>
      </div>
      <div class="sub_post" v-if="postData.subPost || postData.common || postData.team || postData.outside">
        <div class="sub_post_item" v-if="postData.subPost">
          <div class="item" v-for="(item, index) in postData.subPost" :key="index">
            <div class="item_title">
              <div class="post_name">{{ item.postName }}</div>
              <div class="post_type">{{ item.postType }}</div>
            </div>
            <div class="item_center flex_row_betweens" v-if="item.personnelNum > 0">
              <span class="personnel_name personnels_name overflow_elps" :title="item.name">{{ item.name }}</span>
              <div class="personnel_name">
                <i class="bold">{{ item.personnelNum }}</i>
                <span>人</span>
              </div>
            </div>
            <div class="item_center" v-if="!item.personnelNum">
              <span class="personnel_name">暂无</span>
            </div>
            <div class="item_line" v-if="index < 1"></div>
          </div>
        </div>
        <div class="sub_post_item" v-if="postData.common">
          <div class="item" v-for="(item, index) in postData.common" :key="index">
            <div class="item_title">
              <div class="post_name">{{ item.postName }}</div>
              <div class="post_type">{{ item.postType }}</div>
            </div>
            <div class="item_center flex_row_betweens" v-if="item.personnelNum > 0">
              <span class="personnel_name personnels_name overflow_elps" :title="item.name">{{ item.name }}</span>
              <div class="personnel_name">
                <i class="bold">{{ item.personnelNum }}</i>
                <span>人</span>
              </div>
            </div>
            <div class="item_center" v-if="!item.personnelNum">
              <span class="personnel_name">暂无</span>
            </div>
            <div class="item_line" v-if="index < 1"></div>
          </div>
        </div>
        <div class="sub_post_item" v-if="postData.team">
          <div class="item" v-for="(item, index) in postData.team" :key="index">
            <div class="item_title">
              <div class="post_name">{{ item.postName }}</div>
              <div class="post_type">{{ item.postType }}</div>
            </div>
            <div class="item_center flex_row_betweens" v-if="item.personnelNum > 0">
              <span class="personnel_name personnels_name overflow_elps" :title="item.name">{{ item.name }}</span>
              <div class="personnel_name">
                <i class="bold">{{ item.personnelNum }}</i>
                <span>人</span>
              </div>
            </div>
            <div class="item_center" v-if="!item.personnelNum">
              <span class="personnel_name">暂无</span>
            </div>
            <div class="item_line" v-if="index < 1"></div>
          </div>
        </div>
        <div class="sub_post_item" v-if="postData.outside">
          <div class="item" v-for="(item, index) in postData.outside" :key="index">
            <div class="item_title">
              <div class="post_name">{{ item.postName }}</div>
              <div class="post_type">{{ item.postType }}</div>
            </div>
            <div class="item_center">
              <span class="personnel_name">{{ item.postName }}</span>
            </div>
            <div class="item_line" v-if="index < 1"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  postData: {
    type: Object,
    required: true
  }
})
</script>

<style scoped lang="scss">
.map_center {
  display: flex;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  .item {
    position: relative;
    width: 150px;
    // height: 80px;
    border: 1px solid #ecf3f9;
    border-radius: 8rpx;
    font-size: 14px;
    padding: 8px 8px;
    margin-bottom: 16px;
    .item_line {
      position: absolute;
      width: 2px;
      height: 17px;
      left: 50%;
      top: 100%;
      background: #bcd6ed;
      z-index: 1;
      &::after,
      &::before {
        content: '';
        position: absolute;
        left: -3px;
        top: -3px;
        width: 8px;
        height: 8px;
        background-color: #bcd6ed;
        border-radius: 50%;
        z-index: 3;
      }
      &::after {
        top: initial;
        bottom: -6px;
      }
    }
    &_title {
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
      font-weight: bold;
      color: #26b6e3;
      font-size: 12px;
      border-bottom: 1px solid #ecf3f9;
      padding-bottom: 5px;
      margin-bottom: 5px;
    }
    &_center {
      color: #68c8e6;
      padding-bottom: 6px;
      .bold {
        font-style: normal;
      }
    }
  }
  .curr_post {
    .post_type {
      color: #00af50;
    }
    .item_line::after {
      display: none;
    }
  }
  .sub_post {
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: -16px;
      left: 50%;
      width: 2px;
      height: 16px;
      background: #bcd6ed;
    }
    &::before {
      content: '';
      position: absolute;
      top: -20px;
      left: 50%;
      width: 8px;
      height: 8px;
      margin-left: -3px;
      background-color: #bcd6ed;
      border-radius: 50%;
      z-index: 3;
    }
    .item {
      .item_line {
        top: -21px;
        &::before {
          display: none;
        }
      }
    }
    .sub_post_item {
      &:first-of-type {
        .item::before {
          display: none;
        }
      }
      &:last-of-type {
        .item::after {
          display: none;
        }
      }
      .item {
        margin: 0 10px 10px;
        flex: 1;
        &:first-of-type {
          &::before,
          &::after {
            content: '';
            position: absolute;
            top: -21px;
            left: 0;
            width: 50%;
            height: 2px;
            background-color: #bcd6ed;
          }
          &::after {
            left: initial;
            right: -22px;
            width: calc(50% + 22px);
            background: bcd6ed;
          }
        }
      }
    }
  }
}
.post_relationship_map {
  .personnels_name {
    cursor: pointer;
    width: 100px;
    height: 30px;
  }
}
</style>
