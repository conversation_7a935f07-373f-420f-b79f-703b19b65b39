<script setup>
import SectionTab from "../components/sectionTab.vue";
const router = useRouter();
const route = useRoute();
const sectionTabCheckSign = ref(1);
const sectionTabList = ref([
  {
    name: "人才数量盘点",
    code: 1,
  },
  {
    name: "人才结构盘点",
    code: 2,
  },
  {
    name: "人才质量盘点",
    code: 3,
  },
  {
    name: "人才效能盘点",
    code: 4,
  },
  {
    name: "人才发展盘点",
    code: 5,
  },
  {
    name: "人才风险盘点",
    code: 6,
  },
]);

const sectionTab2CheckSign = ref(1);
const sectionTab2List = ref([
  {
    name: "战略意识与全局观念",
    code: 1,
  },
  {
    name: "市场分析与战略规划",
    code: 2,
  },
  {
    name: "战略解码与目标分解",
    code: 3,
  },
  {
    name: "绩效监控与数据分析",
    code: 4,
  },
  {
    name: "战略调整与创新",
    code: 5,
  },
  {
    name: "团队协作与沟通",
    code: 6,
  },
  {
    name: "资源整合与利用",
    code: 7,
  },
  {
    name: "风险识别与应对",
    code: 8,
  },
  {
    name: "执行力与结果导向",
    code: 9,
  },
  {
    name: "创新思维与问题解决",
    code: 10,
  },
]);

const checkSecTab = (c) => {
  sectionTabCheckSign.value = c;
};
const checkSec2Tab = (c) => {
  sectionTab2CheckSign.value = c;
};
</script>
<template>
  <div class="evalProcess_wrap">
    <div class="evalProcess_main">
      <div class="process_wrap">
        <div class="proc_title_wrap justify-start">
          <div class="num">1</div>
          <div class="info">选择模型</div>
        </div>
        <div class="section_wrap">
          <div class="t_t_wrap justify-start">
            <div class="icon"></div>
            <div class="r_info">
              <div class="t">
                <span class="n">人才盘点</span>
                <span class="">（您一人独自完成核心能力的评估）</span>
              </div>
              <div class="tips">选择部分或全部要素</div>
            </div>
          </div>
          <div class="marginT20">
            <SectionTab
              :sectionTabList="sectionTabList"
              :sectionTabCheckSign="sectionTabCheckSign"
              :itemWidth="'15.75%'"
              :actColor="'#51D0E1'"
              @checkSecTab="checkSecTab"
            ></SectionTab>
          </div>
        </div>

        <div class="section_wrap section_02_wrap">
          <div class="t_t_wrap justify-start">
            <div class="icon icon_02"></div>
            <div class="r_info">
              <div class="t">
                <span class="n">人才测评</span>
                <span class=""
                  >（邀请公司其他人员一起参与核心能力的评估 ）</span
                >
              </div>
              <div class="tips">选择部分或全部能力</div>
            </div>
          </div>
          <div class="mt-5">
            <SectionTab
              :sectionTabList="sectionTab2List"
              :sectionTabCheckSign="sectionTab2CheckSign"
              :itemWidth="'15.75%'"
              @checkSecTab="checkSec2Tab"
            ></SectionTab>
          </div>
        </div>
      </div>

      <div class="process_wrap">
        <div class="proc_title_wrap justify-start">
          <div class="num">2</div>
          <div class="info">邀请参与人</div>
        </div>
        <div class="pro2_section_wrap">
          <div class="tips">
            可以邀请他人一起参与人才盘点或人才测评，可通过以下两种方式：
          </div>
          <div class="pro2_section_main justify-between">
            <div class="item_wrap">
              <span class="num">1</span>
              <span class="black_t">发送自动生成的测评链接，如：</span>
              <span class="blue_t">https://ceping.xiyiqq.com/xxxxxx</span>
            </div>
            <div class="item_wrap">
              <span class="num">2</span>
              <span class="black_t"
                >发送自动生成的盘点或测评二维码给指定人员或指定微信群</span
              >
              <span class="icon"></span>
            </div>
          </div>
        </div>
      </div>

      <div class="process_wrap">
        <div class="proc_title_wrap justify-start">
          <div class="num">3</div>
          <div class="info">开始测评&盘点</div>
        </div>
        <div class="pro2_section_wrap">
          <div class="pro3_section_main justify-between">
            <div class="item_wrap">
              <div class="title">
                <span class="num">1</span>
                <span class="black_t">阅读盘点&测评示例</span>
              </div>
              <div class="img_01"></div>
              <div class="bnt_wrap">
                <div class="btn">盘点示例</div>
                <div class="btn">测评示例</div>
              </div>
            </div>
            <div class="item_wrap">
              <div class="title">
                <span class="num">2</span>
                <span class="black_t">正式开始盘点&测评</span>
              </div>
              <div class="img_01"></div>
              <div class="bnt_wrap">
                <div class="btn">盘点示例</div>
                <div class="btn">测评示例</div>
              </div>
            </div>
            <div class="item_wrap">
              <div class="title">
                <span class="num">3</span>
                <span class="black_t">跟进盘点&测评进度</span>
              </div>
              <div class="img_01"></div>
              <div class="bnt_wrap">
                <div class="btn">盘点示例</div>
                <div class="btn">测评示例</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="process_wrap process_04_wrap">
        <div class="proc_title_wrap justify-start">
          <div class="num">4</div>
          <div class="info">查看报告</div>
        </div>
        <div class="pro2_section_wrap">
          <div class="tips">阅读人才盘点报告/人才测评报告</div>
          <div class="pro4_section_main justify-between">
            <div class="item_wrap">
              <div class="title">整体能力表现</div>
              <div class="bnt_wrap">
                <div class="btn">整体表现</div>
                <div class="btn">能力长短板</div>
                <div class="btn">能力图谱</div>
              </div>
            </div>
            <div class="item_wrap">
              <div class="title">能力短板分析</div>
              <div class="bnt_wrap">
                <div class="btn">能力差因</div>
                <div class="btn">能力解码</div>
                <div class="btn">短板详解</div>
              </div>
            </div>
            <div class="item_wrap">
              <div class="title">各模块能力详细分析</div>
              <div class="bnt_wrap">
                <div class="btn">模块整体表现</div>
                <div class="btn">模块差因分析</div>
                <div class="btn">模块能力改善</div>
              </div>
            </div>
            <div class="item_wrap">
              <div class="title">能力改善总结</div>
              <div class="bnt_wrap">
                <div class="btn">能力短板风险</div>
                <div class="btn">能力提升举措</div>
                <div class="btn">短期行动计划</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.evalProcess_wrap {
  margin: 0 0 0 10px;
  .evalProcess_wrap {
  }
  .process_wrap {
    border-left: 8px solid #dcecfc;
    padding-bottom: 1px;
    .proc_title_wrap {
      width: 166px;
      height: 32px;
      line-height: 32px;
      background: #dcecfc;
      border-radius: 68px 68px 68px 68px;
      padding: 0 5px;
      margin: 0 0 0 -24px;
      .num {
        margin: 4px 10px 0 0;
        width: 26px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        color: #fff;
        background: linear-gradient(90deg, #4276f0 5%, #59b9fc 97%);
        border-radius: 50%;
      }
      .info {
        font-weight: 600;
        color: #333;
      }
    }
    .section_wrap {
      margin: 16px 0 0 20px;
      padding: 20px 20px 0px;
      // width: 100%;
      background: linear-gradient(360deg, #dff3fa 0%, #c5f1f7 100%),
        linear-gradient(
          216deg,
          rgba(216, 250, 255, 0) 0%,
          rgba(213, 241, 244, 0.12) 100%
        );
      border-radius: 8px 8px 8px 8px;
      .t_t_wrap {
        .icon {
          margin-right: 12px;
          width: 34px;
          height: 40px;
          background: url("@/assets/imgs/org/icon_01.png") no-repeat center;
          background-size: 100% 100%;
        }
        .r_info {
          margin-bottom: 20px;
          .t {
            .n {
              color: #3d3d3d;
              font-weight: 600;
            }
          }
          .tips {
            font-size: 14px;
            color: #6da0ac;
          }
        }
      }
    }
    .section_02_wrap {
      background: linear-gradient(360deg, #e7f3fc 0%, #c1dbfd 100%),
        linear-gradient(
          216deg,
          rgba(216, 250, 255, 0) 0%,
          rgba(181, 212, 255, 0.2) 100%
        );
      .t_t_wrap {
        .icon_02 {
          background: url("@/assets/imgs/org/icon_02.png") no-repeat center;
          background-size: 100% 100%;
        }
        .r_info {
          .tips {
            color: #6581a5;
          }
        }
      }
    }
    .pro2_section_wrap {
      margin: 16px 0 0 20px;
      .pro2_section_main {
        margin: 17px 0;
        .item_wrap {
          width: 49.5%;
          height: 119px;
          text-align: center;
          background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #c6dbf3;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            display: inline-block;
          }
          .num {
            margin-right: 14px;
            width: 30px;
            height: 30px;
            line-height: 30px;
            color: #fff;
            border-radius: 50%;
            background: #53a9f9;
          }
          .blue_t {
            color: #53a9f9;
          }
          .icon {
            margin-left: 10px;
            width: 89px;
            height: 89px;
            background: url("@/assets/imgs/org/icon_01.png") no-repeat center;
            background-size: 100% 100%;
          }
        }
      }
      .pro3_section_main {
        .item_wrap {
          margin-bottom: 10px;
          padding: 20px;
          width: 33%;
          background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #c6dbf3;
          .title {
            text-align: left;
            .num {
              display: inline-block;
              margin: 0px 8px 0 0;
              text-align: center;
              width: 20px;
              height: 20px;
              line-height: 20px;
              border-radius: 50%;
              font-size: 12px;
              color: #fff;
              background: #53a9f9;
            }
          }
          .img_01 {
            margin: 10px auto 16px;
            width: 366px;
            height: 190px;
            background: url("@/assets/imgs/org/img_01.png") no-repeat center;
            background-size: 100% 100%;
          }
          .bnt_wrap {
            display: flex;
            justify-content: center;
            .btn {
              margin: 0 10px;
              width: 120px;
              height: 36px;
              line-height: 36px;
              text-align: center;
              color: #fff;
              background: #40a0ff;
              border-radius: 6px 6px 6px 6px;
              cursor: pointer;
            }
          }
        }
      }
      .pro4_section_main {
        .item_wrap {
          padding: 20px 17px;
          width: 24.5%;
          background: linear-gradient(
            224deg,
            #ddefff 0%,
            rgba(230, 252, 255, 0.6) 100%
          );
          border-radius: 8px 8px 8px 8px;
          color: #333;
        }
        .btn {
          margin: 12px auto 0;
          width: 293px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          background: linear-gradient(
              228deg,
              #e6f5ff 3%,
              #ffffff 21%,
              #ffffff 82%,
              #e6f5ff 100%
            ),
            rgba(255, 255, 255, 0.5);
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #c6dbf3;
        }
      }
    }
  }
  .process_04_wrap {
    border-left: 8px solid transparent;
    .pro2_section_wrap {
      .tips {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
