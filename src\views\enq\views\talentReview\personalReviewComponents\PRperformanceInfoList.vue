<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in performanceInfoData" :key="item.id">
      <div class="item border">{{ item.month }}</div>
      <el-input class="item" v-model="item.score" type="number" placeholder="填写考核得分" />
      <el-select class="item" v-model="item.level">
        <el-option v-for="option in educationOption" :key="option.key" :label="option.label" :value="option.key" />
      </el-select>
      <div class="item item_icon_wrap">
        <!-- <el-icon @click="deleteItem(item, index)"><Delete /></el-icon> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { ElInput, ElSelect, ElOption } from 'element-plus'

const props = defineProps({
  performanceInfoData: {
    type: Array,
    default: () => [
      {
        id: '1',
        schoolName: '',
        graduationDate: '',
        education: '',
        post: '',
        industry: ''
      }
    ]
  }
})

const emit = defineEmits(['deleteItem'])

const educationOption = [
  {
    label: '前5%',
    key: '1'
  },
  {
    label: '5%~30%之间',
    key: '2'
  },
  {
    label: '30%~45%之间',
    key: '3'
  }
]

const deleteItem = (item, index) => {
  emit('deleteItem', index)
}
</script>

<style scoped lang="scss">
.edu_info_item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 0 16px;

  .item {
    width: 31%;
    padding: 0 4px;
    line-height: 40px;

    &.border {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      line-height: 38px;
      padding: 0 15px;
    }
  }

  .item_icon_wrap {
    text-align: center;
    width: 6%;

    .item_icon {
      font-size: 20px;
      color: #0099fd;
      cursor: pointer;
    }
  }
}
</style>
