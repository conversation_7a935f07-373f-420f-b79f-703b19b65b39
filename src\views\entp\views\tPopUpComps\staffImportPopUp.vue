<template>
  <el-dialog title="开始导入" v-model="dialogVisible" width="600px" :before-close="handleClose">
    <div class="import_staff_wrap">
      <div class="import_staff_title">操作步骤:</div>
      <div class="oper_step">
        <p>1、下载《员工批量导入模板》</p>
        <p>2、打开下载表，将对应字段信息输入或粘贴进本表，为了保障粘贴信息被有效导入，请使用纯文本或者数字。</p>
        <p>3、信息输入完毕，点击"选择文件"按钮，选择excel文档。</p>
        <p>4、点击"开始导入"。</p>
      </div>
      <a class="fs16 main_color download_file" href="/edp/api/entp/userTemplate.xlsx" download="员工批量导入模板.xlsx">
        立即下载《员工批量导入模板》
      </a>
      <div class="upload_file_wrap">
        <el-input v-model="fileName" placeholder="请选择文件" readonly>
          <template #append>
            <label for="up" class="upload_label">
              选择文件
              <input id="up" style="display: none" ref="fileRef" type="file" accept=".xlsx,.xls" @change="fileChange" />
            </label>
          </template>
        </el-input>
      </div>
      <div class="import_staff_title"><span class="error_icon">*</span> 当手机号重复时:</div>
      <div class="marginT_16">
        <el-radio-group v-model="phoneType" @change="checkImportType">
          <el-radio label="Y">覆盖更新</el-radio>
          <el-radio label="N">不导入</el-radio>
        </el-radio-group>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="importStaff">开始导入</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { readExcelData } from '../../request/api'

const router = useRouter()

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'importSign'])

const dialogVisible = computed({
  get: () => props.show,
  set: val => emit('update:show', val)
})

const fileName = ref('')
const phoneType = ref('')
const fileRef = ref(null)
const formData = ref(new FormData())

function handleClose() {
  fileName.value = ''
  phoneType.value = ''
  if (fileRef.value) {
    fileRef.value.value = ''
  }
  dialogVisible.value = false
}

function fileChange(e) {
  const file = e.target.files[0]
  formData.value = new FormData()
  formData.value.append('file', file)
  fileName.value = file.name
}

function checkImportType(val) {
  if (formData.value.get('coverUpdate')) {
    formData.value.set('coverUpdate', val)
  } else {
    formData.value.append('coverUpdate', val)
  }
}

function importStaff() {
  if (!phoneType.value) {
    ElMessage.warning('请选择手机号重复时导入方式!')
    return
  }
  if (!fileName.value) {
    ElMessage.warning('请选择要导入的文件!')
    return
  }

  readExcelData(formData.value).then(res => {
    if (res.code == 200) {
      ElMessage.success(res.msg)
      if (res.data.obj.length > 0) {
        router.push({
          path: '/basicSettingHome/staffManagement/staffImportView',
          query: {
            data: res.data.obj
          }
        })
      } else {
        handleClose()
        emit('importSign', true)
      }
    } else {
      ElMessage.error('模板文件格式不正确，请重新下载模板文件')
    }
  })
}
</script>

<style scoped lang="scss">
.import_staff_wrap {
  .import_staff_title {
    color: #515c71;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;

    .error_icon {
      display: inline-block;
      color: #f56c6c;
      font-size: 18px;
    }
  }

  .oper_step {
    line-height: 28px;
    color: #515c71;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .download_file {
    display: block;
    margin-bottom: 16px;
  }

  .upload_file_wrap {
    margin-bottom: 16px;

    .upload_label {
      display: block;
      height: 32px;
      line-height: 32px;
      padding: 0 15px;
      cursor: pointer;
    }
  }
}

.marginT_16 {
  margin-top: 16px;
}

:deep(.el-dialog__header) {
  background-color: #ebf4ff;
  padding: 15px 20px;
  margin-right: 0;
}
</style>
