<template>
  <div class="project_view_wrap">
    <div class="project_view_main page_one">
      <div class="project_view_item flex_row_between" v-for="(item, index) in listData" :key="item.evalId">
        <div class="item_index">{{ index + 1 }}</div>
        <div class="item_content_wrap">
          <div class="item_content">
            <p class="project_name overflow_elps" :title="item.projectName">{{ item.evalName }}</p>
            <p class="time">{{ item.beginTime | removeTime }}~{{ item.endTime | removeTime }}</p>
            <div class="last_line_descript_wrap flex_row_betweens">
              <div class="left flex_row_betweens">
                <span class="company_name overflow_elps" :title="item.companyName">{{ item.companyName }}</span>
                <span>{{ item.userName }}</span>
                <span>{{ item.phoneNumber }}</span>
              </div>
              <div class="right">
                <span class="num">{{ item.submitNumber }}</span
                >提交 <span></span>/ <span class="num">{{ item.totalNumber }}</span
                >总数
              </div>
            </div>
          </div>
        </div>
        <div class="item_content_wrap progress_details">
          <div class="item_content flex_row_around">
            <div class="item_content_list">
              <div class="list_title">报告日期</div>
              <div class="list_text">
                <span class="report_date list_num">{{ item.reportGenTime | removeTime }}</span>
              </div>
            </div>
            <div class="item_content_list">
              <div class="list_title">报告份数</div>
              <div class="list_text">
                <span class="list_num">{{ item.numberOfCopies }}</span
                >份
              </div>
            </div>
            <!-- <div class="item_content_list">
                            <div class="list_title">有权限查看人</div>
                            <div class="list_text">
                                <span class="list_num">{{item.viewPerson}}</span>人
                            </div>
                        </div> -->
          </div>
        </div>
        <div class="item_oper flex_row_start">
          <div class="item_oper_list" @click="viewReport(item.evalId)">
            <i class="icon el-icon-document"></i>
            <div class="text">查看报告</div>
          </div>
        </div>
      </div>
      <div class="pagination_wrap">
        <coustomPagination :total="total" @pageChange="pageChange"></coustomPagination>
        <!-- <el-pagination
                    :page-sizes="[20, 50, 100, 200]"
                    :page-size="100"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="400"
                ></el-pagination> -->
      </div>
    </div>
  </div>
</template>

<script>
import { evaluationReportList } from '../../../request/api'
import coustomPagination from '@/components/talent/paginationComps/coustomPagination.vue'
export default {
  name: 'projectView',
  components: {
    coustomPagination
  },
  data() {
    return {
      isInfoShow: false,
      pageSize: 10,
      currPage: 1,
      total: 0,
      listData: []
    }
  },
  created() {
    this.evaluationReportListFun()
  },
  methods: {
    viewReport(evalId) {
      this.$router.push('/talentAssessment/assessmentReport/report/projectView/showReport?evalId=' + evalId)
      // this.isInfoShow = true;
    },
    evaluationReportListFun() {
      let params = {
        current: this.currPage,
        size: this.pageSize
      }
      evaluationReportList(params).then(res => {
        console.log(res)
        if (res.code == '200' && res.data) {
          this.listData = res.data
          this.total = res.total
        }
      })
    },
    pageChange(size, currPage) {
      this.pageSize = size
      this.currPage = currPage
      this.evaluationReportListFun()
    }
  }
}
</script>

<style scoped lang="scss">
.project_view_wrap {
  .project_view_main {
    .project_view_title {
      margin: 0 0 8px 0;
      padding: 3px 8px;
      font-size: 16px;
      line-height: 28px;
      color: #0099fd;
      font-weight: bold;
      background-color: #f2f8ff;
      border-radius: 3px;
    }
  }
  .page_one {
    .project_view_item {
      position: relative;
      padding: 16px 8px 16px;
      margin-bottom: 8px;
      border: 1px solid #d9d9d9;
      overflow: hidden;
      .item_index {
        width: 50px;
        font-weight: bold;
        font-size: 20px;
        color: #409eff;
        text-align: center;
      }
      .item_content_wrap {
        width: 50%;
        padding: 0 8px;
        .item_content {
          // padding-right: 10px;
          .item_content_list {
            color: #525e6c;
            .list_title {
              font-weight: bold;
              margin-bottom: 8px;
            }
            .list_num {
              height: 20px;
              line-height: 20px;
              color: #409eff;
              font-weight: bold;
              font-size: 14px;
            }
          }
          .project_name,
          .time {
            line-height: 28px;
          }
          .project_name {
            color: #409eff;
            font-weight: 700;
            cursor: pointer;
          }
          .time {
          }
          .last_line_descript_wrap {
            height: 28px;
            line-height: 28px;
            white-space: nowrap;
            .left {
              span {
                margin: 0 8px 0 0;
              }
              .company_name {
                width: 130px;
                cursor: pointer;
              }
            }
            .right {
              span {
                margin: 0 4px;
                display: inline-block;
              }
              .num {
                color: #409eff;
                font-weight: 700;
              }
            }
          }
        }
      }
      .progress_details {
        width: 30%;
        border-left: 1px solid #d9d9d9;
      }
      .item_oper {
        width: 80px;
        padding: 0 8px;
        border-left: 1px solid #d9d9d9;
        align-items: center;
        color: #0099fd;
        text-align: center;
        .icon {
          font-size: 30px;
        }
        &_list {
          width: 100%;
          cursor: pointer;
        }
      }
    }
  }
  .page_two {
    .project_view_title {
      .search_wrap {
        position: relative;
        .search_icon {
          position: absolute;
          top: 7px;
          right: 5px;
          font-size: 14px;
        }
        .el-input__inner {
          padding: 0 28px 0 10px;
          font-size: 14px;
        }
      }
    }
    .back_btn {
      text-align: right;
      height: 40px;
    }
    .has-gutter {
      tr {
        th {
          // background: #f2f8ff;
        }
      }
    }
  }
}
</style>
