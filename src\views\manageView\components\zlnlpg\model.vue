<script setup>
defineOptions({ name: 'model' })
const modelList = ref([
  {
    id: 1,
    name: '市场洞察与趋势预测',
    desc: '市场洞察与趋势预判是指中小企业通过系统化的数据收集、行业分析、竞争观察及客户需求研究，识别市场机会与风险，并提前布局以应对未来变化的能力。',
    question: [
      {
        label: '战略短视',
        value: '仅关注短期营收，忽视行业周期变化（如成长期与衰退期的应对策略），导致资源错配。'
      },
      {
        label: '盲目跟风',
        value: '缺乏独立判断，跟随竞争对手动作而非基于自身基因布局，易陷入红海竞争。'
      },
      {
        label: '数据孤岛',
        value: '部门间信息割裂，无法整合客户行为、供应链等数据支持决策。'
      },
      {
        label: '被动应对风险',
        value: '外部环境突变（如政策调整、技术颠覆）时反应滞后，错失转型窗口期。'
      },
      {
        label: '低效资源投入',
        value: '在市场需求未验证时盲目扩张，导致产能过剩或创新失败。'
      }
    ],
    manageValue: [
      {
        label: '捕捉先机',
        value: '提前发现新兴需求（如绿色能源、数字化转型）并抢占蓝海市场。'
      },
      {
        label: '规避风险',
        value: '识别行业衰退信号（如产能过剩、技术替代），及时调整业务重心。'
      },
      {
        label: '优化资源配置',
        value: '通过精准客户画像（如《客户移情图》）减少无效营销投入，聚焦高价值用户。'
      },
      {
        label: '提升竞争力',
        value: '借助集群化发展（如与大企业协同创新）和差异化定位，增强抗风险能力。'
      },
      {
        label: '增强战略韧性',
        value: '构建长期视角（如20年战略框架），避免被短期波动干。'
      }
    ]
  },
  {
    id: 2,
    name: '略意图定义与共识构建',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 3,
    name: '业务设计与商业模式创新',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 4,
    name: '创新焦点与机会管理',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 5,
    name: '战略解码与目标穿透',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 6,
    name: '关键任务与资源统筹',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 7,
    name: '组织协同与变革管理',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 8,
    name: '执行体系与流程构建',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 9,
    name: '动态校准与偏差修复',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 10,
    name: '数据驱动与智能决策',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 11,
    name: '绩效评估与激励对齐',
    desc: '',
    question: [],
    manageValue: []
  },
  {
    id: 12,
    name: '战略动态刷新机制',
    desc: '',
    question: [],
    manageValue: []
  }
])

const activeId = ref(1)
const changeModel = id => {
  activeId.value = id
}
const activeModel = computed(() => modelList.value.find(item => item.id == activeId.value))
</script>
<template>
  <div class="model-content">
    <div class="model-list-wrap">
      <div
        class="model-list"
        :class="{ active: activeId == list.id }"
        v-for="list in modelList"
        :key="list.id"
        @click="changeModel(list.id)"
      >
        {{ list.name }}
      </div>
    </div>
    <div class="model-info">
      <div class="page-title-line">能力简介</div>
      <div class="desc">{{ activeModel.desc }}</div>
      <div class="page-title-line">缺乏该项能力的典型管理问题</div>
      <div class="list-content">
        <div class="list-item" v-for="item in activeModel.question" :key="item.label">
          <div class="label">{{ item.label }}：</div>
          <div class="value">{{ item.value }}</div>
        </div>
      </div>
      <div class="page-title-line">对企业的管理价值</div>
      <div class="list-content">
        <div class="list-item" v-for="item in activeModel.question" :key="item.label">
          <div class="label">{{ item.label }}：</div>
          <div class="value">{{ item.value }}</div>
        </div>
      </div>
      <div class="btn-wrap">
        <div class="btn">取消关注</div>
        <div class="btn">加入评估模块</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.model-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-start;
  .model-list-wrap {
    width: 197px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px 8px 8px 8px;
    padding: 16px 13px;
    margin-right: 17px;
    .model-list {
      font-size: 14px;
      color: #6c757e;
      line-height: 35px;
      background: #f0f9ff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #a5c1dc;
      text-align: center;
      margin-bottom: 10px;
      cursor: pointer;
      &.active {
        background: #83c1ff;
        border-color: #83c1ff;
        color: #fff;
      }
    }
  }
  .model-info {
    .desc {
      padding: 18px 25px;
      font-weight: 400;
      font-size: 14px;
      color: #3d3d3d;
      line-height: 28px;
      background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      margin-bottom: 25px;
    }
    .list-content {
      background: linear-gradient(226deg, #f3f9fd 0%, #ffffff 100%);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      padding: 20px 26px;
      margin-bottom: 25px;
      .list-item {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        .label {
          font-weight: 600;
          font-size: 14px;
          color: #3d3d3d;
          line-height: 28px;
        }
      }
    }
    .btn-wrap {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .btn {
        width: 200px;
        line-height: 40px;
        text-align: center;
        color: #fff;
        background: #53a9f9;
        border-radius: 42px;
        cursor: pointer;
        & + .btn {
          margin-left: 20px;
        }
      }
    }
  }
}
</style>
