<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section">
      <div class="content_item_title marginB_16">能力潜力矩阵</div>
      <div class>
        <div class="matrix_chart">
          <div class="matrix_head">
            <div class="title">核心能力</div>
            <div class="flex_row_start border">
              <div class="item" v-for="item in competenceRankOptions" :key="item.dictCode">
                {{ item.codeName }}
              </div>
            </div>
          </div>
          <div class="clearfix">
            <div class="matrix_aside">
              <div class="matrix_aside_head flex_row_start">
                <div class="title">发展潜力</div>
                <div class="flex_col_start border">
                  <div class="item" v-for="item in developmentOptions" :key="item.dictCode">
                    {{ item.codeName }}
                  </div>
                </div>
              </div>
            </div>
            <div class="matrix_main flex_col_start">
              <div class="item flex_row_start" v-for="(item, index) in matrixData" :key="item.code + index">
                <div
                  class="list"
                  :class="'level_' + item.code + list.code"
                  v-for="(list, listIndex) in item.maps"
                  :key="listIndex + list.code"
                  @click="showDialogFun(list.code, item.code, list.name, item.name)"
                >
                  {{ list.value }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            详情列表
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog v-model="showDialog" width="700px">
      <div class="dialog_title flex_row_betweens">
        <div class>核心能力：{{ competenceRankName }}</div>
        <div class>发展潜力：{{ developmentName }}</div>
        <div class>{{ personnelList.length }}人</div>
      </div>
      <el-table :data="personnelList" height="270">
        <el-table-column type="index"></el-table-column>
        <el-table-column property="org_name" label="部门"></el-table-column>
        <el-table-column property="post_name" label="岗位"></el-table-column>
        <el-table-column property="job_level_name" label="职层"></el-table-column>
        <el-table-column property="user_name" label="姓名"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { potential, potentialMembers, queryCompetenceList, exportData } from '../../../../../request/api.js'
import asideFilterBar from '../../asideFilterBar.vue'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref({})
const competenceRankName = ref('')
const developmentName = ref('')
const showDialog = ref(false)
const personnelList = ref([])
const page = ref(1)
const size = ref(10)

const competenceRankOptions = reactive([
  {
    dictCode: 'A',
    codeName: '高'
  },
  {
    dictCode: 'B',
    codeName: '中'
  },
  {
    dictCode: 'C',
    codeName: '低'
  }
])

const developmentOptions = ref([])
const matrixData = ref([])

const tableData = reactive({
  columns: [
    {
      label: '员工编码',
      prop: 'employee_code'
    },
    {
      label: '员工姓名',
      prop: 'user_name'
    },
    {
      label: '所属组织',
      prop: 'org_name'
    },
    {
      label: '任职岗位',
      prop: 'post_name'
    },
    {
      label: '职层',
      prop: 'job_level_name'
    },
    {
      label: '核心能力',
      prop: 'competence'
    },
    {
      label: '发展潜力',
      prop: 'development'
    },
    {
      label: '评价人',
      prop: 'superior'
    },
    {
      label: '评价日期',
      prop: 'evaluationTime'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const potentialFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await potential(params)
    if (res.code == 200) {
      matrixData.value = res.data
    }
  } catch (error) {
    console.error('获取能力潜力矩阵数据失败:', error)
  }
}

const showDialogFun = async (dictCodeC, dictCodeP, competenceRankNameVal, developmentNameVal) => {
  competenceRankName.value = competenceRankNameVal
  developmentName.value = developmentNameVal
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      dictCodeP: dictCodeP,
      dictCodeC: dictCodeC
    }
    const res = await potentialMembers(params)
    if (res.code == 200) {
      personnelList.value = res.data
      showDialog.value = true
    }
  } catch (error) {
    console.error('获取人员列表数据失败:', error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  page.value = 1
  potentialFun()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryCompetenceList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error('获取表格数据失败:', error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'n'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '能力潜力矩阵详情列表')
  } catch (error) {
    console.error('导出数据失败:', error)
  }
}

onMounted(async () => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  potentialFun()
  const res = await window.$getDocList(['COMPETENCE_RANK', 'DEVELOPMENT_POTENTIAL'])
  developmentOptions.value = res.DEVELOPMENT_POTENTIAL
  getTableData()
})
</script>

<style scoped lang="scss">
.dialog_title {
  line-height: 40px;
  font-size: 17px;
  color: #0099fd;
  font-weight: bold;
}
.matrix_chart {
  width: 700px;
  float: left;
  margin-right: 6px;
  margin-bottom: 16px;
  .matrix_head {
    width: 100%;
    // padding-left: 45px;
    // margin-left: 45px;
    text-align: center;
    line-height: 30px;
    .title {
      height: 30px;
      background: #fbfbfb;
      padding-left: 90px;
    }
    .flex_row_start {
      height: 30px;
      margin-left: 90px;
      &.border {
        border-bottom: 1px solid #f6f6f6;
      }
    }
    .item {
      flex: 1;
    }
  }
  .matrix_aside {
    float: left;
    width: 90px;
    height: 450px;
    text-align: center;
    .matrix_aside_head {
      height: 100%;
    }
    .title {
      height: calc(100% + 50px);
      padding: 90px 5px 0;
      width: 30px;
      background: #fbfbfb;
      margin-top: -50px;
    }
    .flex_col_start {
      height: 100%;
      width: 60px;
      &.border {
        border-right: 1px solid #f6f6f6;
      }
    }
    .item {
      flex: 1;
      line-height: 90px;
    }
  }
  .matrix_main {
    overflow: hidden;
    height: 450px;
    .item {
      flex: 1;
      align-items: center;
      color: #fff;
      font-size: 18px;
      .list {
        color: #fff;
        flex: 1;
        height: calc(100% - 1px);
        margin: 0 1px 1px 0;
        text-align: right;
        padding: 100px 32px 0 0;
        font-size: 32px;
        cursor: pointer;
        &.level {
          &_HA {
            background-color: #e28d80;
          }
          &_HB,
          &_MA {
            background-color: #719dd5;
          }
          &_HC,
          &_MB,
          &_MC,
          &_LA,
          &_LB {
            background-color: #a3d0f3;
          }

          &_LC {
            background-color: #dddee3;
          }
        }
      }
    }
  }
}
</style>
