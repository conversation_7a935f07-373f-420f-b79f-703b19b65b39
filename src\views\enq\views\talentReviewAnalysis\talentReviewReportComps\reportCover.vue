<template>
    <div class="report_cover">
        <img class="report_img" :src="imgSrc" />
        <div class="report_title">提升人才竞争力，支持战略业务目标达成</div>
        <div class="report_info">
            <div class="report_type">
                {{ type == "P" ? "个人" : "组织" }}报告
            </div>
            <div class="user_info" v-if="type == 'P'">
                <span class="user_name">{{ userInfo.userName }} </span>
                <span class="user_post">
                    {{ userInfo.orgName }} / {{ userInfo.postName }}
                </span>
            </div>
            <div class="org_name" v-else>
                {{ orgName }}
            </div>
        </div>
        <div class="report_date">{{ beginDate | removeTime }}</div>
    </div>
</template>

<script>
    import orgReportCover from "../../../../../../public/images/report_cover_bg.jpg";
    import userReportCover from "../../../../../../public/images/report_cover_bg_P.png";
    import imgSrc from "../../../../../../public/images/enq_report_cover_bg.jpg";
    export default {
        name: "reportCover",
        props: {
            type: String, // D:组织报告 P: 个人报告
            beginDate: String,
            orgName: String,
            userInfo: Object,
        },
        data() {
            return {
                orgReportCover: orgReportCover,
                userReportCover: userReportCover,
                imgSrc: imgSrc,
            };
        },
    };
</script>

<style lang="scss" scoped>
    .report_cover {
        position: relative;
        // height: 842px;
        background: #017ba0;
        .report_img {
            width: 100%;
            // height: 100%;
            display: block;
        }
        .report_title {
            position: absolute;
            top: 130px;
            left: 100px;
            font-size: 48px;
            color: #fff;
            // text-align: center;
        }
        .report_info {
            position: absolute;
            top: 48.526%;
            top: 250px;
            left: 100px;
            color: #fff;
            .report_type {
                font-size: 35px;
                padding-bottom: 20px;
                border-bottom: 2px solid #0070c0;
                margin-bottom: 45px;
            }
            .org_name {
                font-size: 28px;
            }
            .user_name {
                font-size: 28px;
            }
            .user_post {
                font-size: 20px;
            }
        }
        .report_date {
            position: absolute;
            bottom: 5.65%;
            width: 100%;
            text-align: center;
            color: #017ba0;
            font-size: 22px;
        }
    }
</style>