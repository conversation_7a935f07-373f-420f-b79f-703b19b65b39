<script setup>
import Table from '@/components/table/simplenessTable.vue'

const columns = ref([
  {
    label: '决策层级',
    prop: 'a'
  },
  {
    label: '能力要求',
    prop: 'b'
  },
  {
    label: '实际表现',
    prop: 'c'
  },
  {
    label: '整体匹配度',
    prop: 'd'
  },

  {
    label: '个人匹配度<60%',
    prop: 'e'
  },
  {
    label: '个人匹配度60~80%',
    prop: 'f'
  },
  {
    label: '个人匹配度80~100%',
    prop: 'g'
  },
  {
    label: '个人匹配100~120%',
    prop: 'h'
  },
  {
    label: '个人匹配度120%以上',
    prop: 'i'
  }
])
const data = ref([
  {
    a: '最高决策层',
    b: '15',
    c: '15',
    d: '15',
    e: '15'
  },
  {
    a: '战略解码与目标分解',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  }
])

const columns2 = ref([
  {
    label: '姓名',
    prop: 'a'
  },
  {
    label: '岗位',
    prop: 'b'
  },
  {
    label: '个人匹配度',
    prop: 'c'
  },
  {
    label: '实际岗位层级',
    prop: 'd'
  },
  {
    label: '错配等级',
    prop: 'e'
  },
  {
    label: '最高决策层得分',
    prop: 'f'
  },
  {
    label: '业务管理层得分',
    prop: 'g'
  },
  {
    label: '部门管理层得分',
    prop: 'h'
  },
  {
    label: '基层主管层得分',
    prop: 'i'
  },
  {
    label: '一线执行层得分',
    prop: 'j'
  },
  {
    label: '推荐决策层级',
    prop: 'k'
  }
])
const data2 = ref([
  {
    a: '王伟',
    b: '采购经理',
    c: '110%',
    d: '基层主管层',
    e: '红色预警',
    f: '52',
    g: '62',
    h: '75',
    i: '58',
    j: '62',
    k: '部门管理层'
  }
])

const columns3 = ref([
  {
    label: '二级能力',
    prop: 'a'
  },
  {
    label: '能力要求',
    prop: 'b',
    width: 150
  },
  {
    label: '实际表现',
    prop: 'c',
    width: 150
  },
  {
    label: '匹配度',
    prop: 'd',
    width: 150
  },
  {
    label: '错配等级',
    prop: 'e'
  },
  {
    label: '干预策略',
    prop: 'f'
  }
])
const data3 = ref([
  {
    a: '市场分析与战略规划',
    b: '50',
    c: '60',
    d: '120%',
    e: '红色预警',
    f: ''
  }
])

onMounted(() => {})
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">决策层级匹配</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        基于能力测评数据，精准识别团队成员能力与目标岗位胜任力要求的匹配程度，通过岗位调整实现
        “人岗适配”，最大化释放人才效能。
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">决策层级匹配</div>
      <Table :roundBorder="false" :columns="columns" :data="data" headerColor showIndex> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">决策层级人员匹配（基层主管层-个人匹配度120%以上）</div>
      <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor showIndex> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">决策层级人员匹配详情（基层主管层-个人匹配度120%以上-王伟-采购经理）</div>
      <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor showIndex> </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../../../../style/common.scss';
@import './common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
