<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'

defineOptions({ name: 'dna' })

const columns = ref([
  {
    label: '序号',
    type: 'index',
    width: 50
  },
  {
    label: '评估维度',
    prop: 'dim',
    width: 120
  },
  {
    label: '参考标准',
    prop: 'reference'
  }
])
const tableData = ref([
  {
    dim: '闭环验证',
    reference: '每个环节设置输入/输出验收标准（如规则修订需委员会审批文件），且触发验证动作（季度复盘会议）',
    flag: 'Y'
  },
  {
    dim: '反馈改进',
    reference: '是否建立季度评审机制，根据执行结果优化流程（如客户投诉率>5%时触发流程重构）',
    flag: 'N'
  },
  {
    dim: '可追溯性',
    reference: '流程关键决策（如阈值调整）可追溯至原始数据（如市场分析报告），变更记录保留≥2年',
    flag: 'N'
  },
  {
    dim: '流程完整性',
    reference: '是否覆盖数据采集→规则制定→审批发布→执行监控全环节',
    flag: 'Y'
  },
  {
    dim: '目标一致性',
    reference: '流程设计目标与战略规划（如大客户营收占比提升）直接关联，每个环节设置目标对齐验证点',
    flag: 'N'
  },
  {
    dim: '完整性',
    reference: '流程覆盖数据采集→规则制定→审批发布→执行监控全环节，明确标注各环节输入输出标准，且无断点或盲区',
    flag: 'Y'
  }
])
</script>
<template>
  <div class="eval-answer-content">
    <div class="module-name">大客户管理</div>
    <div class="tip">提示：开展能力DNA级的诊断，请做出有无判断</div>
    <div class="model-list-wrap">
      <div class="model-list active">大客户分类分级</div>
      <div class="model-list">大客户关系管理</div>
      <div class="model-list">大客户价值挖掘</div>
      <div class="model-list">大客户绩效管理</div>
      <div class="model-list">我的改善期望</div>
    </div>
    <div class="eval-answer-main">
      <div class="comp-wrap">
        <div class="comp-item active">
          <div class="comp-name">明确大客户评价标准</div>
          <div class="comp-status">进行中</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">建立客户数据模板</div>
          <div class="comp-status">未开始</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">客户分级与标签化管理</div>
          <div class="comp-status">未开始</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">大客户资源匹配论证</div>
          <div class="comp-status">未开始</div>
        </div>
        <div class="comp-item">
          <div class="comp-name">大客户作战地图绘制</div>
          <div class="comp-status">未开始</div>
        </div>
      </div>
      <div class="eval-answer">
        <div class="eval-amswer-box">
          <div class="eval-answer-title">DNA级诊断</div>
          <div class="eval-answer-item">
            <div class="item active">流程端到端闭环</div>
          </div>
          <div class="eval-answer-table">
            <SimplenessTable :roundBorder="false" :columns="columns" :data="tableData">
              <template v-slot:oper>
                <el-table-column prop="oper" label="有无判断" width="130">
                  <template v-slot="scope">
                    <div class="answer-oper">
                      <div class="btn" :class="{ active: scope.row.flag == 'N' }">无</div>
                      <div class="btn" :class="{ active: scope.row.flag == 'Y' }">有</div>
                    </div>
                  </template>
                </el-table-column>
              </template>
            </SimplenessTable>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.eval-answer-content {
  .module-name {
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 16px;
    margin-bottom: 10px;
  }
  .tip {
    line-height: 35px;
    background: #eff4f9;
    border-radius: 5px 5px 5px 5px;
    font-size: 14px;
    color: #40a0ff;
    padding-left: 14px;
    margin-bottom: 14px;
  }
  .model-list-wrap {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 40px;
    border-bottom: 1px solid #d8d8d8;
    padding-left: 20px;
    .model-list {
      padding: 19px 20px;
      border-bottom: 3px solid transparent;
      margin-bottom: -1px;
      font-size: 14px;
      color: #3d3d3d;
      cursor: pointer;
      &.active {
        font-weight: 600;
        color: #40a0ff;
        border-bottom-color: #40a0ff;
      }
    }
  }
  .eval-answer-main {
    display: flex;
    align-items: flex-start;
    .comp-wrap {
      flex: 0 0 266px;
      border-right: 1px solid #d8d8d8;
      margin-right: 20px;
      .comp-item {
        border-bottom: 1px solid #d8d8d8;
        border-right: 3px solid transparent;
        padding: 10px 20px;
        color: #333;
        cursor: pointer;
        &.active {
          color: #53a9f9;
          border-right-color: #40a0ff;
          background: linear-gradient(-90deg, rgba(64, 160, 255, 0.3) 0%, rgba(64, 160, 255, 0) 100%);
          .comp-status {
            color: inherit;
          }
        }
        .comp-name {
          font-size: 16px;
          margin-bottom: 10px;
        }
        .comp-status {
          color: #888;
          font-size: 16px;
        }
      }
    }
    .eval-answer {
      flex: 1;
      padding-top: 20px;
      .title {
        font-weight: 600;
        font-size: 16px;
        color: #3d3d3d;
        line-height: 16px;
        margin-bottom: 12px;
      }
      .desc {
        font-size: 14px;
        color: #666666;
        line-height: 24px;
        padding-bottom: 20px;
        border-bottom: 1px dashed #e7e7e7;
        margin-bottom: 20px;
      }
      .eval-amswer-box {
        .eval-answer-title {
          font-weight: 600;
          font-size: 16px;
          color: #3d3d3d;
          line-height: 16px;
          margin-bottom: 20px;
        }
        .eval-answer-item {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          line-height: 42px;
          background: #f0f0f0;
          font-size: 14px;
          margin-bottom: 10px;
          .item {
            padding: 0 10px;
            &.active {
              background: #40a0ff;
              color: #fff;
            }
          }
        }
        .eval-answer-table {
          :deep(.el-table th.el-table__cell) {
            background: #e8eff7;
            color: #3d3d3d;
          }
        }
      }
      .answer-oper {
        display: flex;
        gap: 10px;
        .btn {
          width: 50px;
          line-height: 24px;
          background: #d8d8d8;
          text-align: center;
          border-radius: 3px 3px 3px 3px;
          font-size: 12px;
          color: #898989;
          cursor: pointer;
          &.active {
            background: #40a0ff;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
