<script setup>
import Table from "@/components/table/simplenessTable.vue";

const columns = ref([
  {
    label: "二级能力",
    prop: "a",
    width: 260,
  },
  {
    label: "能力得分",
    prop: "b",
  },
  {
    label: "新手入门",
    prop: "c",
  },
  {
    label: "合格执行者",
    prop: "d",
  },

  {
    label: "骨干能手",
    prop: "e",
  },
  {
    label: "资深专家",
    prop: "f",
  },
  {
    label: "业内标杆",
    prop: "g",
  },
  {
    label: "",
    prop: "h",
  },
]);
const data = ref([
  {
    a: "市场分析与战略规划",
    b: "15人",
    c: "15人",
    d: "15人",
    e: "15人",
  },
  {
    a: "战略解码与目标分解",
    b: "15人",
    c: "15人",
    d: "15人",
    e: "15人",
  },
]);

const columns2 = ref([
  {
    label: "缺陷维度",
    prop: "e",
    width: 150,
  },
  {
    label: "缺陷简介",
    prop: "a",
  },

  {
    label: "具体表征",
    prop: "d",
    width: 460,
  },
]);
const data2 = ref([
  {
    a: "经验依赖型",
    b: "12",
    c: "36",
    d: "以过往成功或失败案例为决策核心依据，通过类比历史情境解决当下问题",
    e: "高投入高回报",
    f: "面对新业务或复杂场景时，易因路径依赖导致资源错配与目标偏差，阻碍战略转型与创新突破。",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);

const columns3 = ref([
  {
    label: "影响维度",
    prop: "a",
    width: 150,
  },
  {
    label: "量化指标",
    prop: "b",
  },
  {
    label: "作用机制",
    prop: "c",
  },
]);
const data3 = ref([
  {
    a: "经验依赖型 （12人）",
    b: "王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）",
    c: "1.划清经验边界：填写《经验适用边界清单》，标注差异点与潜在风险，避免盲目复制。 2.反经验训练：每月推演 “非惯性做法”，输出《反经验行动清单》，强制尝试新策略。3. 新场景探索：分配 10% 时间接触跨界案例，撰写《旧经验失效分析》，打破路径依赖。",
    e: "高投入高回报",
  },
]);

const columns4 = ref([
  {
    label: "影响维度",
    prop: "a",
    width: 150,
  },
  {
    label: "传导路径",
    prop: "b",
  },
]);
const data4 = ref([
  {
    a: "市场分析与战略规划",
    b: "战略预见性缺失 → 错失2个技术代际跃迁窗口 → 核心产品市占率年降4.2% → 跌出行业第一梯队",
    c: "15人",
    d: "15人",
    e: "15人",
  },
]);
const zbdb = ref([
  {
    title: "精准分析能力",
    info: "掌握市场动态量化建模技术，实现需求预测准确率≥85%",
  },
  {
    title: "战略解码能力",
    info: "能将战略目标拆解为可执行的战术动作，确保部门级战略对齐度≥90%",
  },
  {
    title: "动态迭代能力",
    info: "建立环境变化敏感度，关键战略指标调整响应周期≤14天",
  },
]);
const sznl = ref([
  {
    title: "红蓝对抗演练",
    info: "每季度组织跨部门战略推演，模拟市场突变、技术颠覆等场景",
  },
  {
    title: "战略复盘会",
    info: "按月检视战略执行偏差，要求团队使用RCA（根因分析）工具输出改进方案",
  },
  {
    title: "动态案例库",
    info: "建立实时更新的行业危机案例库，要求人员48小时内提交应对策略分析",
  },
]);

const columns5 = ref([
  {
    label: "模块",
    prop: "a",
    width: 150,
  },
  {
    label: "内容",
    prop: "b",
  },
  {
    label: "形式",
    prop: "c",
  },
  {
    label: "考核标准",
    prop: "d",
  },
]);
const data5 = ref([
  {
    a: "市场洞察专项",
    b: "- 行业数据采集规范（爬虫/API接口） - 客户价值迁移图谱构建 - 竞争情报分析七步法",
    c: "工作坊+实战演练",
    d: "独立完成细分市场分析报告（准确性≥80%）",
  },
]);

const columns6 = ref([
  {
    label: "职级",
    prop: "a",
    width: 150,
  },
  {
    label: "认证要求",
    prop: "b",
  },
  {
    label: "晋升门槛",
    prop: "c",
  },
]);
const data6 = ref([
  {
    a: "初级分析师",
    b: "掌握基础数据分析工具，能独立完成市场周报",
    c: "通过战略基础知识考试（≥85分）",
  },
]);

const columns7 = ref([
  {
    label: "类别",
    prop: "a",
    width: 150,
  },
  {
    label: "认证要求",
    prop: "b",
  },
]);
const data7 = ref([
  {
    a: "工具赋能",
    b: "智能决策助手：部署AI战略引擎，实时推送市场预警信号与策略建议 战略沙盘系统：引入军事级兵棋推演平台，支持多变量动态模拟 数据驾驶舱：开发战略健康度仪表盘，集成20+关键领先指标",
    c: "通过战略基础知识考试（≥85分）",
  },
]);

onMounted(() => {});
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">能力影响与改善</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        通过人才测评数据构建能力差距分析模型，精准诊断团队在认知能力、分析能力、计划能力、评审能力、规制能力、决策能力、落实能力等维度的优势与短板。基于分析结果，针对性优化能力提升机制（如认知培训体系、分析工具赋能、计划流程再造、评审标准迭代、规制流程设计、决策框架搭建、落实追踪机制），系统性补足能力缺口，有效减少因认知偏差、分析缺位、计划粗放、评审缺失、规制模糊、决策失误、落实拖沓导致的执行断层与目标偏差。
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">核心能力人员分布</div>
      <Table
        :roundBorder="false"
        :columns="columns"
        :data="data"
        headerColor
        showIndex
      >
      </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">核心能力短板影响 （市场分析与战略规划）</div>
      <div class="section_box_wrap">
        <div class="text_b marginB20">关联逻辑</div>
        <div class="">
          专业能力短板 → 战略解码失真 → 绩效目标悬浮 → 执行监控盲区 →
          修正机制缺失 → 战略-运营脱钩 → 组织发展失速
        </div>
      </div>
      <div class="section_box_wrap">
        <div class="text_b marginB20">典型表现</div>
        <Table
          :roundBorder="false"
          :columns="columns2"
          :data="data2"
          headerColor
        >
        </Table>
      </div>
      <div class="section_box_wrap">
        <div class="text_b marginB20">直接影响（短期-中期）</div>
        <Table
          :roundBorder="false"
          :columns="columns3"
          :data="data3"
          headerColor
        >
        </Table>
      </div>
      <div class="section_box_wrap">
        <div class="text_b marginB20">间接影响（长期）</div>
        <Table
          :roundBorder="false"
          :columns="columns4"
          :data="data4"
          headerColor
        >
        </Table>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">能力改善建议 （市场分析与战略规划）</div>
      <div class="section_box_wrap">
        <div class="text_b marginB20">能力提升目标</div>
        <div class="icon_content_wrap">
          <div class="item_wrap" v-for="item in zbdb">
            <span class="icon"></span>
            <span class="title">{{ item.title }}</span>
            <span class="info">{{ item.info }}</span>
          </div>
        </div>
      </div>
      <div class="section_box_wrap">
        <div class="text_b marginB20">关键能力提升举措</div>
        <Table
          :roundBorder="false"
          :columns="columns5"
          :data="data5"
          headerColor
        >
        </Table>
      </div>
      <div class="section_box_wrap">
        <div class="text_b marginB20">实战能力验证机制</div>
        <div class="icon_content_wrap">
          <div class="item_wrap" v-for="item in sznl">
            <span class="icon"></span>
            <span class="title">{{ item.title }}</span>
            <span class="info">{{ item.info }}</span>
          </div>
        </div>
      </div>
      <div class="section_box_wrap">
        <div class="text_b marginB20">岗位能力认证</div>
        <Table
          :roundBorder="false"
          :columns="columns6"
          :data="data6"
          headerColor
        >
        </Table>
      </div>
      <div class="section_box_wrap">
        <div class="text_b marginB20">支撑体系建设</div>
        <Table
          :roundBorder="false"
          :columns="columns7"
          :data="data7"
          headerColor
        >
        </Table>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
