<template>
  <div class="clearfix" :class="{ disable: !isEdit }">
    <div class="marginT_30">
      <div class="flex_row_betweens">
        <div class="page_second_title">选择人员</div>
        <div class="forecast_count">
          预估总盘点人数 <span class="number">{{ forecastCount }}</span>
        </div>
      </div>
      <div class="marginT_20 clearfix">
        <div class="selection_range_left clearfix">
          <div class="filter_tree_wrap">
            <div class="selection_range_title">部门</div>
            <div class="tree_list">
              <tree-comp-checkbox
                :treeData="cmpyOrgInfoList"
                @node-click-callback="nodeClickCallback"
              ></tree-comp-checkbox>
            </div>
          </div>
        </div>
        <div class="filter_table_wrap">
          <div class="selection_range_title">参与盘点人员</div>
          <div class="table_list">
            <div class="search_box">
              <div class="form_div">
                <el-form :inline="true" :model="formInline" class="demo-form-inline">
                  <el-form-item label="是否选择">
                    <el-select v-model="formInline.isCheck" placeholder="请选择" clearable>
                      <el-option label="是" value="Y"></el-option>
                      <el-option label="否" value="N"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="姓名">
                    <el-input v-model="formInline.userName" placeholder="请输入" clearable></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" class="page_add_btn" @click="getEnqPostFun()">查询</el-button>
                  </el-form-item>
                </el-form>
              </div>
              <div class="btn_box">
                <!-- <el-button type="primary" class="page_add_btn btn_color" >下载模板</el-button> -->
                <el-button type="primary" v-if="isEdit" class="page_add_btn" @click="importStaffDialog = true"
                  >导入</el-button
                >
              </div>
            </div>
            <tableComponent
              :tableData="tableData"
              :needIndex="true"
              :selectionStatus="true"
              height="400"
              :checkSelection="checkedList"
              @selectionChange="selectionChange"
              @curSelectInfo="curSelectInfo"
              @selectAll="selectAll"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
            ></tableComponent>
          </div>
        </div>
        <!-- <div class="center_right">
                    <div class="review_content">
                        <div class="item">
                            <div class="title">总盘点人数预估</div>
                            <div class="content">
                                <span class="text">{{ enqUserCount }}</span>
                                <span>人</span>
                            </div>
                        </div>
                        <div class="item">
                            <div class="title">需完善数据项目预估</div>
                            <div class="content">
                                <span class="text">28</span>
                                <span>分钟</span>
                            </div>
                        </div>
                        <div class="item">
                            <div class="title">单人维护时间预估</div>
                            <div class="content">
                                <span class="text">30</span>
                                <span>分钟</span>
                            </div>
                        </div>
                    </div>
        </div>-->
      </div>
    </div>

    <div class="clearfix"></div>
    <div class="btn_wrap align_center marginT_30" v-if="isEdit">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="submit">下一步</el-button>
    </div>
    <!-- 导入弹窗 -->
    <el-dialog title="开始导入" v-model="importStaffDialog" width="800px" :before-close="importStaffDialogBeforeClose">
      <div class="import_staff_wrap">
        <div class="import_staff_title">操作步骤:</div>
        <div class="oper_step">
          <p>1、下载《参与人员导入模板》</p>
          <p>2、该模板中的人员为所有人员在职人员。</p>
          <p>3、如该人员无需参与人才盘点，请将其从excel中删除，仅保留需要参与盘点的人员即可。</p>
          <p>4、按需标准参与个人盘点、部门盘点与上级评价，如人员不是上级或部门盘点，则无法选中 。</p>
          <p>5、打开下载表，标记需要参与本次盘点的人员。</p>
          <p>6、信息输入完毕，点击“选择文件”按钮，选择excel文档。</p>
          <p>7、点击"开始导入",导入中如有任何疑问，请致电010-86482868。</p>
        </div>
        <div class="downBtn" @click="exportDownloadFun">立即下载《参与人才盘点导入模板》</div>
        <div class="upload_file_wrap">
          <el-input placeholder="请输入内容" v-model="fileName" readonly>
            <template v-slot:append>
              <label for="up" class="upload_label">
                选择文件
                <!-- <el-button type="primary">选择文件</el-button> -->
                <input
                  id="up"
                  style="display: none"
                  ref="file"
                  type="file"
                  class="form-control page_clear_btn"
                  @change="fileChange"
                />
              </label>
            </template>
          </el-input>
        </div>
        <!-- <div class="import_staff_title">当手机号重复时:</div>
                <div class="marginT_16">
                    <el-radio-group v-model="phoneType">
                        <el-radio label="Y">覆盖更新</el-radio>
                        <el-radio label="N">不导入</el-radio>
                    </el-radio-group>
        </div>-->
      </div>
      <template v-slot:footer>
        <div>
          <el-button class="page_clear_btn" @click="importStaffDialog = false">取 消</el-button>
          <el-button type="primary" class="page_add_btn" @click="importStaff">开始导入</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, getCurrentInstance } from 'vue'
import {
  getFilter,
  getActivePost,
  saveEnqScopeUser,
  getEnqPointPost,
  exportDownload,
  readUserInfoData,
  checkEnqUser,
  getEnqCountUsing
} from '../../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import asideFilterCheckbox from './asideFilterCheckbox'
import treeCompCheckbox from '@/components/talent/treeComps/treeCompCheckbox'
import { ElMessage } from 'element-plus'

defineProps({
  getEnqId: {
    type: Function,
    required: true
  },
  isEdit: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['prevStep', 'nextStep'])
const { proxy } = getCurrentInstance()

const forecastCount = ref('')
const submitStatus = ref(true)
const enqId = ref(null)
const listPostCode = ref([])
const enqUserCount = ref(0)
const checkedJobClassCode = ref([])
const checkedJobLevelCode = ref([])
const checkedOrgCode = ref([])
const checkedList = ref([])
const choosePostAllStatus = ref(true)
const resPostList = ref([])
const checkedPostList = ref([])
const checkedPostCodeArr = ref([])
const cmpyJobClassList = ref([])
const cmpyJobLevelList = ref([])
const cmpyOrgInfoList = ref([])
const tableData = reactive({
  columns: [
    { label: '员工姓名', prop: 'userName' },
    { label: '一级组织', prop: 'oneLevelName' },
    { label: '二级组织', prop: 'twoLevelName' },
    { label: '三级组织', prop: 'threeLevelName' },
    { label: '四级组织', prop: 'fourLevelName' },
    { label: '五级组织', prop: 'fiveLevelName' },
    { label: '任职岗位', prop: 'postName' },
    { label: '职层', prop: 'jobLevelName' },
    {
      label: '参与盘点',
      prop: 'inEmpEnq',
      formatterFun: (row, column, val) => (row.inEmpEnq == 'Y' ? '是' : '否')
    }
  ],
  page: {
    total: 0,
    current: 1,
    size: 10
  },
  data: []
})
const formInline = reactive({
  userName: '',
  isCheck: ''
})
const importStaffDialog = ref(false)
const fileName = ref('')
const uploadFile = ref(null)

watch(checkedPostList, val => {
  listPostCode.value = []
  enqUserCount.value = 0
  val.forEach(item => {
    listPostCode.value.push(item.postCode)
    enqUserCount.value += item.userNum
  })
})

onMounted(() => {
  enqId.value = proxy.$props.getEnqId()
  if (!proxy.$props.isEdit) {
    formInline.isCheck = 'Y'
  }
  getFilterFun()
  getEnqPostFun()
  getEnqCountUsingFun()
})

function getEnqCountUsingFun() {
  getEnqCountUsing({ enqId: enqId.value }).then(res => {
    forecastCount.value = res
  })
}
function setUpEnqlUserObjectFun(params) {
  saveEnqScopeUser(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      getEnqPostFun()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
function exportDownloadFun() {
  let params = { enqId: enqId.value }
  exportDownload(params).then(res => {
    // proxy.$exportDownloadFile(res.data, '参与人才盘点导入模板')
    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '参与人才盘点导入模板.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  })
}
function importStaffDialogBeforeClose(done) {
  fileName.value = ''
  document.getElementById('up').value = null
  done()
}
function fileChange(e) {
  let formData = new FormData()
  let file = e.target.files[0]
  formData.append('file', file)
  formData.append('enqId', enqId.value)
  fileName.value = file.name
  uploadFile.value = formData
}
function importStaff() {
  readUserInfoData(uploadFile.value).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      uploadFile.value = null
      importStaffDialog.value = false
      getEnqPostFun()
    } else {
      ElMessage.error('模板文件格式不正确，请重新下载模板文件')
    }
  })
  importStaffDialog.value = true
}
function selectionChange(data) {}
function curSelectInfo(state, info) {
  let params = {
    enqId: enqId.value,
    endUserId: info.userId,
    postCode: info.postCode,
    orgCode: info.orgCode,
    inEmpEnq: info.inEmpEnq == 'Y' ? 'N' : 'Y'
  }
  setUpEnqlUserObjectFun([params])
}
function selectAll(selection) {
  let list = null
  let flag = 'Y'
  let arr = []
  if (selection.length > 0) {
    list = selection
  } else {
    flag = 'N'
    list = proxy.$util.deepClone(tableData.data)
  }
  list.forEach(item => {
    arr.push({
      enqId: enqId.value,
      endUserId: item.userId,
      postCode: item.postCode,
      orgCode: item.orgCode,
      inEmpEnq: flag
    })
  })
  setUpEnqlUserObjectFun(arr)
}
function handleCurrentChange(currentPage) {
  tableData.page.current = currentPage
  getEnqPostFun()
}
function handleSizeChange(size) {
  tableData.page.size = size
  getEnqPostFun()
}
function pageChange(pageSize, currentPage) {
  tableData.page.size = pageSize
  tableData.page.current = currentPage
  getEnqPostFun()
}
function deleteSelectData(row) {}
function getActivePostFun() {
  let params = {
    jobClassCodes: checkedJobClassCode.value.join(','),
    jobLevelCodes: checkedJobLevelCode.value.join(','),
    orgCodes: checkedOrgCode.value.join(',')
  }
  getActivePost(params).then(res => {
    if (res.code == '200') {
      resPostList.value = res.data.filter(item => item.userNum > 0)
      for (let i = 0; i < resPostList.value.length; i++) {
        if (checkedPostCodeArr.value.includes(resPostList.value[i].postCode)) {
          resPostList.value[i].isChoose = true
        }
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}
function getFilterFun() {
  getFilter().then(res => {
    if (res.code == '200') {
      let data = res.data
      data.cmpyJobClassList.map(item => {
        delete item['children']
      })
      cmpyJobClassList.value = res.data.cmpyJobClassList
      data.cmpyJobLevelList.map(item => {
        delete item['children']
      })
      cmpyJobLevelList.value = res.data.cmpyJobLevelList
      cmpyOrgInfoList.value = res.data.cmpyOrgInfoList
    } else {
      ElMessage.error(res.msg)
    }
  })
}
function nodeClickCallback(checkedCode) {
  checkedOrgCode.value = checkedCode
  getEnqPostFun()
}
function jobClassCallback(checkedCode) {
  checkedJobClassCode.value = checkedCode
  getEnqPostFun()
}
function jobLevelCallback(checkedCode) {
  checkedJobLevelCode.value = checkedCode
  getEnqPostFun()
}
function choosePostAll() {
  let list = resPostList.value
  if (list.length == 0) return
  let chooseStatusArr = []
  for (let i = 0, len = list.length; i < len; i++) {
    let chooseStatus = list[i].isChoose
    chooseStatusArr.push(chooseStatus)
  }
  choosePostAllStatus.value = chooseStatusArr.includes(true)
  resPostList.value.forEach((item, index) => {
    item.isChoose = !choosePostAllStatus.value
  })
  resPostList.value.forEach((item, index) => {
    if (choosePostAllStatus.value) {
      let clearCode = item.postCode
      checkedPostList.value.some((list, idx) => {
        if (list.postCode == clearCode) {
          checkedPostList.value.splice(idx, 1)
          checkedPostCodeArr.value.splice(idx, 1)
        }
      })
    } else {
      item.isChoose = true
      checkedPostList.value.push(item)
      checkedPostCodeArr.value.push(item.postCode)
    }
  })
}
function choosePost(row, index) {
  if (!resPostList.value[index].isChoose) {
    resPostList.value[index].isChoose = true
    checkedPostList.value.push(row)
    checkedPostCodeArr.value.push(row.postCode)
  } else {
    resPostList.value[index].isChoose = false
    let clearCode = row.postCode
    checkedPostList.value.some((item, idx) => {
      if (item.postCode == clearCode) {
        checkedPostList.value.splice(idx, 1)
        checkedPostCodeArr.value.splice(idx, 1)
      }
    })
  }
}
function removePost(code, index) {
  proxy
    .$confirm('确认删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      checkedPostList.value.splice(index, 1)
      checkedPostCodeArr.value.splice(index, 1)
      resPostList.value.some((item, i) => {
        if (item.postCode == code) {
          resPostList.value[i].isChoose = false
        }
      })
    })
    .catch(() => {})
}
function saveEnqRangeFun(type) {
  submitStatus.value = false
  let params = { enqId: enqId.value }
  checkEnqUser(params)
    .then(res => {
      if (res.code == '200') {
        ElMessage.success({ message: res.msg })
        submitStatus.value = true
        emit(type)
      } else {
        ElMessage.error(res.msg)
        submitStatus.value = true
      }
    })
    .catch(err => {
      console.log(err)
    })
}
function prevBtn() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      saveEnqRangeFun('prevStep')
    })
    .catch(action => {
      ElMessage.info({
        message: action === 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步'
      })
      action === 'cancel' ? emit('prevStep') : ''
    })
}
function submit() {
  if (submitStatus.value) {
    saveEnqRangeFun('nextStep')
  }
}
function getEnqPostFun() {
  getEnqPointPost({
    enqId: enqId.value,
    type: formInline.isCheck,
    userName: formInline.userName,
    current: tableData.page.current,
    orgCodes: checkedOrgCode.value.join(','),
    size: tableData.page.size
  }).then(res => {
    if (res.code == '200') {
      checkedList.value = []
      tableData.data = res.data
      tableData.page.total = res.total
      tableData.data.forEach(item => {
        if (item.inEmpEnq == 'Y') {
          checkedList.value.push(item)
        }
      })
    } else {
      ElMessage.error(res.msg)
    }
  })
}
</script>

<style scoped lang="scss">
.disable {
  ::v-deep .el-table {
    pointer-events: none;
  }
}
.forecast_count {
  margin-right: 10px;
  // width: 100px;
  .number {
    // line-height: 28px;
    // text-align: center;
    color: #0099ff;
  }
}
.selection_range_left {
  float: left;
  /*width: 460px;*/
  // margin-right: 16px;

  .aside_filte_wrap {
    float: left;
    width: 160px;
  }

  .filter_tree_wrap {
    width: 220px;
    float: left;
    padding-right: 16px;

    .tree_title {
      font-size: 14px;
      padding-left: 16px;
    }

    .title {
      font-size: 16px;
      margin-bottom: 2px;
      background: #e5f0f9;
      padding: 5px 16px;
      color: #525e6c;
    }
    .tree_list {
      max-height: 400px;
      padding: 10px;
      overflow-y: auto;
      border: 1px solid #e5e5e5;
      border-top: none;
    }
  }
}
.arrow_wrap {
  .arrow_icon {
    margin: 150px 16px 0;
    height: 80px;
    .arrow_img {
      height: 100%;
      cursor: pointer;
    }
  }
}
.filter_table_wrap {
  float: left;
  width: calc(100% - 230px);
  .table_list {
    // height: 400px;
    padding: 10px;
    overflow-y: auto;
    border: 1px solid #e5e5e5;
    border-top: none;
    position: relative;
    .search_box {
      display: flex;
      justify-content: space-between;
      .btn_box {
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 99;
        .btn_color {
          background-color: #666;
          border-color: #666;
        }
      }
    }
  }
}
.selection_range_select_post {
  float: left;
  width: 255px;
  margin-right: 16px;

  .selected_post {
    padding: 5px 0;
    height: 400px;
    overflow-y: auto;

    .item {
      display: flex;
      justify-content: space-between;
      border: 1px solid #efefef;
      margin-bottom: 3px;
      line-height: 26px;
      padding: 0 5px;
      align-items: center;
      .post_name {
        width: 200px;
      }

      .post_num {
        width: 50px;
      }

      .close_btn {
        width: 30px;
        text-align: center;
      }
    }
  }
}

.selection_range_title {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  /*margin-bottom: 2px;*/
  background: #e5f0f9;
  padding: 0px 16px;
  color: #525e6c;
  height: 40px;
  .icon_check_all {
    display: inline-block;
    border: 2px solid #666;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 18px;
    font-weight: bold;
    color: #e5f0f9;
    margin-left: 5px;
    line-height: 22px;
    text-align: center;
    margin-right: -5px;
    &.active {
      color: #0099ff;
      border-color: #0099ff;
    }
  }
}
.center_right {
  float: right;
  width: 180px;
  .review_content {
    /*padding: 16px;*/

    .item {
      // width: 230px;
      height: 80px;
      border: 1px solid #e5e5e5;
      text-align: center;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 2px;
      color: #555;
      .title {
        margin-bottom: 5px;
        font-size: 14px;
      }

      .text {
        font-size: 30px;
        color: #0099fd;
        font-weight: bold;
      }
    }
  }
}
.second_level_post_wrap {
  padding: 5px;
  border: 1px solid #e4e4e4;
  border-top: none;
  height: 340px;
  // min-height: 300px;
  overflow-y: auto;
  li {
    position: relative;
    margin: 5px 0 0 0;
    padding: 0 5px;
    height: 36px;
    line-height: 36px;
    cursor: pointer;
    border: 1px solid #e4e4e4;

    .icon_check {
      position: absolute;
      border: 1px solid #ddd;
      border-radius: 50%;
      right: 5px;
      top: 5px;
      width: 24px;
      height: 24px;
    }

    .el-icon-check {
      height: 35px;
      font-size: 24px;
      line-height: 35px;
      font-weight: bold;
    }

    .el_del_bg {
      position: absolute;
      right: 5px;
      top: 5px;
      width: 24px;
      height: 24px;
      background: #ddd;
      border-radius: 50%;
      line-height: 24px;
      font-size: 20px;
      color: #fff;
      text-align: center;
    }

    .el-icon-remove-outline {
      height: 35px;
      font-size: 24px;
      line-height: 35px;
      color: #ccc;
    }
  }

  .active {
    border: 1px solid #0099ff;
    color: #0099ff;
  }

  .hover_style:hover {
    background: #ebf4ff;
  }
}
// 导入
.import_staff_wrap {
  .import_staff_title {
    color: #515c71;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .oper_step {
    line-height: 28px;
    color: #515c71;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .download_file {
    display: block;
    margin-bottom: 16px;
  }

  .downBtn {
    color: #449cff;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
  }
  .upload_file_wrap {
    margin-bottom: 16px;

    .upload_label {
      display: block;
      height: 28px;
      line-height: 28px;
      width: 100%;
      cursor: pointer;
    }
  }
}
</style>
