<script setup>
import ReportList from "./reportInfo/reportList.vue";
import Reporto from "./reportInfo/reporto.vue";
import Reportp from "./reportInfo/reportp.vue";

const props = defineProps([]);
const router = useRouter();
const route = useRoute();
const comList = ref([
  {
    sign: "list",
    com: ReportList,
  },
  {
    sign: "o",
    com: Reporto,
  },
  {
    sign: "p",
    com: Reportp,
  },
]);
const curCom = ref({
  sign: "",
  com: "",
});
const comInit = (p) => {
  if (!route.query.sign) {
    curCom.value = comList.value[0];
  } else {
    let searchKey = ref("");
    if (p) {
      searchKey.value = p;
    } else {
      searchKey.value = route.query.sign;
    }
    comList.value.forEach((e, index) => {
      if (e.sign == searchKey.value) {
        curCom.value = comList.value[index];
      }
    });
  }
  router.push({
    path: route.path,
    query: { sign: curCom.value.sign },
  });
};
const curSign = (p) => {
  comInit(p);
};
onMounted(() => {
  comInit();
});
watch(
  () => route,
  (o, c) => {
    comInit();
  },
  {
    deep: true,
  }
);
</script>
<template>
  <div class="index_wrap inventoryReport_wrap">
    <component :is="curCom.com" @sign="curSign" />
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
