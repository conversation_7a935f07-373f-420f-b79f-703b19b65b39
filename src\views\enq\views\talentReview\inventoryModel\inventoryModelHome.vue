<template>
  <div class="talent_review_report_wrap">
    <div class="page_main_title">盘点模型</div>
    <div class="page_section" v-if="tabsData.length > 0">
      <div class="talent_review_report_center">
        <tabsDiffentPane
          :isDefaultTheme="true"
          :tabsData="tabsData"
          :tabsDefault="tabsPanesign"
          @tabsChange="tabsChange"
        ></tabsDiffentPane>
        <evaluationItems
          :tabsPanesign="tabsPanesign"
          :tabsPaneInfo="tabsPaneInfo"
        ></evaluationItems>
      </div>
    </div>
  </div>
</template>

<script>
  import {
   selectModel
  } from "../../../request/api";
import tabsDiffentPane from "@/components/talent/tabsComps/tabsDiffentPane";
import evaluationItems from "./evaluationItems";
export default {
  name: "inventoryModelHome",
  components: {
    tabsDiffentPane,
    evaluationItems,
  },
  data() {
    return {
      tabsPanesign: "",
      tabsPaneInfo: "",
      tabsData: [],
    };
  },
  created() {
    this.selectModelFun();
  },
  mounted() {},
  methods: {
    tabsChange(data) {
      console.log(data);
      //  根据点击tabs的信息，展示响应的组some
      this.tabsPanesign = data.name;
      this.tabsPaneInfo = this.tabsData.filter((r) => {
        if (r.modelId == data.name) return r;
      });
      console.log(this.tabsPaneInfo);
    },
    selectModelFun() {
      selectModel({}).then((res) => {
        if (res.code == 200) {
          this.tabsPanesign = res.data[0].moduleCode;
          this.tabsData = res.data.map((item) => {
            return {
              label: item.modelName,
              name: item.modelId,
              moduleCode: item.moduleCode,
              modelId: item.modelId,
            };
          });
          this.tabsPaneInfo = [this.tabsData[0]];
        }
      });
    },
  },
};
</script>
 
<style scoped lang="scss">
</style>