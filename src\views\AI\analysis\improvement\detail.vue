<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import SimplenessTable from '@/components/table/simplenessTable.vue'
import classfiy from './classfiy.vue'
import org from './org.vue'
defineOptions({ name: 'decodeDetail' })

const activeAside = ref('classfiy')
const getChartOpt = item => {
  return {
    xAxisData: item.data.map(i => i.name),
    xAxis: {
      show: false
    },
    grid: {
      left: 0,
      top: 10,
      right: 0,
      bottom: 0
    },
    series: [
      {
        data: item.data.map(i => i.value),
        type: 'bar',
        showBackground: true,
        itemStyle: {
          color: '#40a0ff'
        },
        label: {
          show: true
        }
      }
    ]
  }
}

const dnaColumns = ref([
  {
    label: '序号',
    type: 'index',
    width: '50px',
    align: 'center'
  },
  {
    label: '一级NDA',
    prop: 'name',
    width: '100px'
  },
  {
    label: '二级NDA',
    prop: 'name2',
    width: '110px'
  },
  {
    label: '表现',
    prop: 'value',
    width: '50px'
  },
  {
    label: '能力解码结果',
    prop: 'result'
  }
])
const dnaData = ref([
  {
    name: '流程赋能',
    name2: '流程端到端闭环',
    value: '28',
    result:
      '商机漏跟与阶段混乱：缺乏全生命周期闭环管理，导致商机推进断层，资源浪费，转化率下降（如立项后无交付跟踪，丢单率增加20%+）'
  },
  {
    name: '流程赋能',
    name2: '输出文档',
    value: '62',
    result:
      '文档标准缺失与效率低下：人工撰写报告耗时长且质量参差，关键信息遗漏（如客户需求偏差未被记录），引发交付纠纷或客户信任危机'
  },
  {
    name: '流程赋能',
    name2: '业务规则',
    value: '41',
    result:
      '商机筛选盲目性：未定义分级规则，销售盲目追逐低质量商机（如利润率<5%的订单占比过高），挤占资源且拉低整体毛利'
  },
  {
    name: '流程赋能',
    name2: '业务KPI',
    value: '19',
    result: '目标与执行脱节：缺乏漏斗健康度监控，管理层无法识别瓶颈（如商机储备量不足却盲目扩编团队），造成人力成本浪费'
  }
])

const affectColumns = ref([
  {
    label: '影响维度',
    prop: 'name',
    width: '150px'
  },
  {
    label: '对管理的影响',
    prop: 'affect'
  },
  {
    label: '对指标的影响',
    prop: 'affectIndicator'
  },
  {
    label: '概率',
    prop: 'value',
    width: '50px'
  }
])
const affectData = ref([
  {
    name: '流程断裂点：阶段模糊与责任真空',
    affect: '• 商机识别低效： 未定义准入标准（如客户预算、决策链完整性）',
    affectIndicator: '• 低质量商机进入率： 缺乏准入规则的企业中低质量商机占比超40%（行业标杆<15%）',
    value: '高'
  },
  {
    name: '流程断裂点：阶段模糊与责任真空',
    affect: '• 需求分析偏差： 缺乏需求验证闭环（如客户需求未与交付团队对齐）',
    affectIndicator: '• 方案重做率： 因需求理解偏差导致的方案修改率增加25%-40%',
    value: '高'
  },
  {
    name: '流程断裂点：阶段模糊与责任真空',
    affect: '• 缺乏交付复盘： 未建立交付后客户满意度回访及复盘机制',
    affectIndicator: '• 同类错误复发率： 未经验复盘的团队重复同类错误概率达60%+',
    value: '中'
  },

  {
    name: '资源损耗：低效投入与重复浪费',
    affect: '• 资源错配：未按商机等级动态分配资源（如高价值商机未绑定专家）',
    affectIndicator: '• 资源浪费率：中低价值商机占用核心资源比例超35%（行业合理值<15%）',
    value: '高'
  },
  {
    name: '资源损耗：低效投入与重复浪费',
    affect: '• 重复投入：跨部门信息不透明导致重复工作（如技术团队多次响应同一客户同类问题）',
    affectIndicator: '• 人力损耗率：重复性工作导致有效人效降低20',
    value: '高'
  },

  {
    name: '决策盲区：数据断层与管理失控',
    affect: '• 数据孤岛：商机数据分散在邮件/表格/系统中，缺乏实时整合',
    affectIndicator: '• 决策滞后率：因数据不全导致的策略调整延迟超3天的概率增加70%',
    value: '高'
  },
  {
    name: '决策盲区：数据断层与管理失控',
    affect: '• 过程黑盒：无法追踪商机推进关键动作（如客户接触频率、方案修改版本）',
    affectIndicator: '• 过程失控率：关键动作缺失的商机占比达45%+',
    value: '高'
  },

  {
    name: '客户价值流失：体验下降与信任危机',
    affect: '• 体验断层：跨部门服务响应不一致（如销售承诺与交付能力脱节）',
    affectIndicator: '• 客户流失预警率：因体验问题触发流失预警的商机占比提升至25%+',
    value: '高'
  },

  {
    name: '客户价值流失：体验下降与信任危机',
    affect: '• 信任链断裂：缺乏长期培育机制（如非交易期客户互动缺失）',
    affectIndicator: '• 老客户复购率：未实施培育策略的企业老客户复购率低于行业均值30%+',
    value: '高'
  }
])

const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  console.log(rowIndex)

  if (columnIndex == 0) {
    if (row.name == '流程断裂点：阶段模糊与责任真空') {
      if (rowIndex % 3 == 0) {
        return {
          rowspan: 3,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    } else {
      if ((rowIndex + 3) % 2 == 0) {
        return {
          rowspan: 2,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    }
  }
}

const suggestColumns = ref([
  {
    label: '序号',
    type: 'index',
    width: '50px',
    align: 'center'
  },
  {
    label: '举措',
    prop: 'name',
    width: '120px'
  },
  {
    label: '关键行动',
    prop: 'action'
  },
  {
    label: '建议责任人',
    prop: 'personal',
    width: '120px'
  },
  {
    label: '输出成果',
    prop: 'result'
  },
  {
    label: '优先级',
    prop: 'value',
    width: '60px'
  }
])
const suggestData = ref([
  {
    name: '建立商机准入与分级机制',
    action:
      '1. 定义商机准入标准（预算阈值、决策链验证等），开发自动化校验工具；2. 设计商机质量评分模型（含需求匹配度、客户偿付能力等维度），实现动态分级；3. 制定《商机分级资源投入规则》，明确高/中/低潜力商机的资源分配比例。',
    personal: '销售运营部主管',
    result: '《商机准入规则文档》+自动化分级工具',
    value: '高'
  },
  {
    name: '构建端到端商机流程引擎',
    action:
      '1. 标准化商机生命周期阶段（5大核心阶段），配置阶段推进强制条件（如文档、审批）；2. 开发流程可视化看板，实时监控阶段停留超期预警；3. 建立跨部门协同规则（如交付团队介入需求验证的触发机制）。',
    personal: '流程管理部主管',
    result: '商机流程SOP+可视化监控系统',
    value: '高'
  },
  {
    name: '实施资源动态调度机制',
    action:
      '1. 建立资源分级标准（专家资源、高层支持等），绑定商机等级；2. 开发资源占用实时看板，支持资源抢占预警与自动释放；3. 制定《资源超耗追责制度》，关联部门成本考核。',
    personal: '资源管理部主管',
    result: '《资源调度手册》+资源动态看板工具',
    value: '高'
  },
  {
    name: '构建商机数据治理体系',
    action:
      '1. 定义商机核心数据字段（预算、决策周期等）及采集规范；2. 部署数据血缘追踪工具，异常数据自动冻结；3. 开发BI商机健康度仪表盘（含漏斗分析、过程指标预警）。',
    personal: '数据治理委员会',
    result: '《数据治理白皮书》+商机健康度分析平台',
    value: '中'
  },
  {
    name: '设计客户旅程一致性管理方案',
    action:
      '1. 绘制客户关键接触点地图，定义各部门服务标准（如响应时效、信息口径）；2. 部署客户体验监测工具（NPS实时采集+负面反馈自动升级）；3. 建立跨部门体验问题联合复盘机制。',
    personal: '客户成功部主管',
    result: '《客户旅程蓝图》+体验监测平台',
    value: '中'
  },
  {
    name: '推行商机复盘与知识沉淀机制',
    action:
      '1. 制定商机关闭强制复盘规则（成功/失败均需归因分析）；2. 构建AI驱动的知识库（自动抽取方案、沟通记录中的最佳实践）；3. 设计《知识应用激励制度》，按知识贡献度分配商机优先权。',
    personal: '知识管理部主管',
    result: '商机复盘模板+智能知识库系统',
    value: '中'
  }
])
const router = useRouter()
const back = () => {
  router.back()
}
</script>
<template>
  <div class="decode-detail">
    <div class="title">
      <SvgIcon name="icon-back" class="pointer" @click="back" />
      <div class="back-text" @click="back">返回</div>
      <div class="title-name">改善策略库（制定需求计划对象）</div>
    </div>
    <div class="detail-content">
      <div class="aside">
        <div class="org-item" :class="{ active: activeAside == 'classfiy' }" @click="activeAside = 'classfiy'">
          按类型查看
        </div>
        <!-- <div class="org-item" :class="{ active: activeAside == 'org' }" @click="activeAside = 'org'">按组织查看</div> -->
      </div>
      <div class="content-main">
        <classfiy v-if="activeAside == 'classfiy'"></classfiy>
        <org v-if="activeAside == 'org'"></org>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.decode-detail {
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;

  .title {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #53a9f9;
    padding-bottom: 20px;
    border-bottom: 1px solid #d8d8d8;
    margin-bottom: 18px;
    .back-text {
      margin-left: 10px;
      cursor: pointer;
      font-size: 14px;
      color: #888888;
      margin-right: 10px;
    }
  }
  .detail-content {
    display: flex;
    gap: 20px;
    .aside {
      flex: 0 0 240px;
      .aside-title {
        font-weight: 600;
        font-size: 16px;
        color: #3d3d3d;
        line-height: 16px;
        margin-bottom: 18px;
      }
      .item {
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #c7e1fa;
        padding: 16px;
        margin-bottom: 13px;
        cursor: pointer;
        &.active {
          border-color: #40a0ff;
          .item-score {
            display: none;
          }
          .item-chart {
            display: block;
          }
        }
        .item-title {
          font-weight: 600;
          font-size: 14px;
          color: #3d3d3d;
          line-height: 16px;
          margin-bottom: 10px;
        }
        .item-score {
          width: calc(100% - 20px);
          position: relative;
          background: #e9edf0;
          height: 12px;
          border-radius: 12px;
          .bar {
            height: 100%;
            top: 0;
            left: 0;
            background: #79d2fb;
            border-radius: 12px;
          }
          .score {
            position: absolute;
            left: calc(100% + 10px);
            top: 0;
            font-weight: 500;
            font-size: 16px;
            color: #40a0ff;
            margin-top: -6px;
          }
        }
        .item-chart {
          display: none;
          height: 200px;
        }
      }
      .org-item {
        background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
        border-radius: 5px 5px 5px 5px;
        border: 1px solid #c6dbf3;
        font-size: 14px;
        color: #40a0ff;
        line-height: 35px;
        text-align: center;
        cursor: pointer;
        margin-bottom: 10px;
        &.active {
          box-shadow: 0px 0px 10px 0px rgba(124, 182, 237, 0.5);
          border-radius: 5px 5px 5px 5px;
          border: 1px solid #53acff;
        }
      }
    }
  }
  .content-main {
    flex: 1;
  }
}
</style>
