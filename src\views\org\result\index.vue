<script setup>
import SectionTab from "../components/sectionTab.vue";
const router = useRouter();
const sectionTabList = ref([
  {
    name: "指标",
    code: 1,
    path: "/org/result/indicator/index",
  },
  {
    name: "能力",
    code: 2,
    path: "/org/result/ability/index",
  },
  {
    name: "任务",
    code: 3,
    path: "/org/result/task/index",
  },
]);
const sectionTabCheckSign = ref(1);
const checkSecTab = (c) => {
  sectionTabCheckSign.value = c;
  router.push(sectionTabList.value[c - 1].path);
};
</script>
<template>
  <div class="org_index_wrap">
    <div class="justify-between">
      <div class="justify-start">
        <div class="title">指标视角</div>
        <SectionTab
          :sectionTabList="sectionTabList"
          :sectionTabCheckSign="sectionTabCheckSign"
          @checkSecTab="checkSecTab"
        ></SectionTab>
      </div>
      <div class="t_btn" @click="goBack"><span class="icon"></span>返回</div>
    </div>
    <div class="content-mian">
      <router-view></router-view>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.org_index_wrap {
  .title {
    line-height: 40px;
    margin-right: 20px;
  }
  .t_btn {
    width: 74px;
    height: 28px;
    line-height: 28px;
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #40a0ff;
    text-align: center;
    color: #40a0ff;
    cursor: pointer;
    .icon {
      display: inline-block;
      margin: 0px 6px -1px 0;
      width: 16px;
      height: 16px;
      background: url("@/assets/imgs/org/icon_06.png") no-repeat center;
      background-size: 100% 100%;
    }
  }
  .content-mian {
    padding: 20px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
    border-radius: 8px 8px 8px 8px;
  }
}
</style>
