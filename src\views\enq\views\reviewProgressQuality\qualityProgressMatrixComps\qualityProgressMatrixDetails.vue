<template>
  <div class="quality_progress_matrix_details_wrap">
    <div class="page_main_title flex_row_betweens">
      <div class="title flex_row_start">
        <p>质量进度矩阵</p>
        <!-- <div class="check_title"><span>/</span>国机集团2020年第一季度人才盘点</div> -->
        <div class="check_title" v-if="enqName"><span>/</span>{{ enqName }}</div>
      </div>
      <div class="flex_row_betweens">
        <div class="filter_item search_view_btn">
          <el-button
            :class="{ act_btn: searchViewSign == false, page_add_btn: true }"
            type="primary"
            @click="searchView('dept')"
            >部门视角</el-button
          >
          <el-button
            :class="{ act_btn: searchViewSign == true, page_add_btn: true }"
            type="primary"
            @click="searchView('staff')"
            >人员视角</el-button
          >
        </div>
        <div class="go_back_btn" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
      </div>
    </div>
    <div class="page_section quality_progress_matrix_details_main">
      <!-- <tabs-diffent-pane :tabsData="tabsData" :isDefaultTheme="true" @tabsChange='tabsChange'></tabs-diffent-pane> -->
      <tabsChangeData :tabsData="tabsData" :activeName="tabsData[0].name" :handleClick="tabsClick"></tabsChangeData>
      <matrixView v-if="tabIndex == 0" :searchViewSign="searchViewSign"></matrixView>
      <listView v-if="tabIndex == 1" :searchViewSign="searchViewSign"></listView>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import tabsChangeData from '@/components/talent/tabsComps/tabsChangeData'
import matrixView from './matrixView'
import listView from './listView'

const route = useRoute()
const enqId = route.query.enqId
const enqName = route.query.enqName
const searchViewSign = ref(true)
const tabIndex = ref(0)

const tabsData = [
  {
    id: 1,
    label: '矩阵视图',
    name: 'matrix'
  },
  {
    id: 2,
    label: '列表视图',
    name: 'list'
  }
]

const searchView = val => {
  if (val == 'dept') {
    // 部门视角
    searchViewSign.value = true
  } else {
    // 人员视角
    searchViewSign.value = false
  }
}

const tabsClick = tab => {
  tabIndex.value = tab.index
}
</script>

<style scoped lang="scss">
.quality_progress_matrix_details_wrap {
  .page_main_title {
    .title {
      p {
      }
      .check_title {
        span {
          display: inline-block;
          margin: 0 6px;
        }
      }
    }
    .go_back_btn {
      padding-right: 20px;
      color: #0099FF;
      cursor: pointer;
      font-weight: normal;
      font-size: 14px;
    }
    .search_view_btn {
      margin: 0 15px 0 0;
    }
    .act_btn {
      background: #fff;
      color: #449cff;
    }
  }
  .quality_progress_matrix_details_main {
    .el-tabs {
      width: 1178px;
    }
  }
}
</style>
