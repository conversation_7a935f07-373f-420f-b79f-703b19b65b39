<script setup>
import Table from "../../components/table.vue";
const columns = ref([
  {
    label: "姓名",
    prop: "a",
  },
  {
    label: "岗位",
    prop: "b",
  },
  {
    label: "关键任务数量",
    prop: "c",
    align: "center",
  },
  {
    label: "本期达成率",
    prop: "d",
    align: "center",
  },
  {
    label: "上期达成率",
    prop: "e",
    align: "center",
  },
  {
    label: "变化",
    prop: "f",
    align: "center",
  },
  {
    label: "操作",
    prop: "g",
    slot: "gSlot",
    align: "center",
    width: 220,
  },
]);
const data = ref([
  {
    a: "王伟",
    b: "销售",
    c: "3",
    d: "95%",
    e: "95%",
    f: "+5%",
  },
]);

const columns2 = ref([
  {
    label: "组织名称",
    prop: "a",
    width: 90,
  },
  {
    label: "举措",
    prop: "b",
    width: 90,
  },
  {
    label: "关联策略",
    prop: "c",
    width: 90,
  },
  {
    label: "关键行动举措",
    prop: "d",
    width: 230,
  },
  {
    label: "责任人",
    prop: "e",
    width: 90,
  },
  {
    label: "输出成果",
    prop: "f",
    width: 90,
  },
  {
    label: "优先级",
    prop: "g",
    width: 90,
  },
  {
    label: "截止日期",
    prop: "h",
    width: 90,
  },
  {
    label: "进度",
    prop: "i",
  },

  // {
  //   label: "操作",
  //   prop: "m",
  //   slot: "mSlot",
  //   align: "center",
  //   width: 220,
  // },
]);
const data2 = ref([
  {
    a: "供应链管理部",
    b: "智能需求预测与库存联动机制",
    c: "需求驱动的精准库存策略",
    d: "1. 建立跨部门数据湖（销售/市场/供应链），开发AI预测模型（准确率≥85%）",
    e: "刘威（供应链总监）",
    f: "《智能预测模型白皮书》",
    g: "高",
    h: "2025.06.30",
    i: "规划中",
  },
  {
    a: "供应链管理部",
    b: "智能需求预测与库存联动机制",
    c: "需求驱动的精准库存策略",
    d: "2. 设置库存水位动态校准规则（预测偏差>10%自动触发重算）",
    e: "刘威（供应链总监）",
    f: "《库存健康度诊断报告》",
    g: "高",
    h: "2025.06.30",
    i: "规划中",
  },
  {
    a: "供应链管理部",
    b: "智能需求预测与库存联动机制",
    c: "需求驱动的精准库存策略",
    d: "3. 每月输出《预测-库存联动分析报告》，标注呆滞库存改善点",
    e: "刘威（供应链总监）",
    f: "",
    g: "高",
    h: "2025.06.30",
    i: "规划中",
  },
]);

const columns3 = ref([
  {
    label: "时间节点",
    prop: "a",
  },
  {
    label: "核心任务",
    prop: "b",
  },
  {
    label: "交付物",
    prop: "c",
  },
  {
    label: "检视重点",
    prop: "d",
    width: 300,
  },
  {
    label: "关联风险",
    prop: "e",
    width: 300,
  },
]);
const data3 = ref([
  {
    a: "2025.05.15",
    b: "跨部门数据上线",
    c: "数据接入清单、数据治理规范",
    d: "销售/市场/供应链数据实时性；数据清洗规则有效性",
    e: "部门数据壁垒未打通（如市场促销数据延迟）",
    f: "",
  },
]);

const columns4 = ref([
  {
    label: "指标类别",
    prop: "a",
  },
  {
    label: "具体指标",
    prop: "b",
  },
  {
    label: "目标值",
    prop: "c",
  },
  {
    label: "考核周期",
    prop: "d",
  },
]);
const data4 = ref([
  {
    a: "预测质量",
    b: "品类级预测准确率",
    c: "≥85%（核心SKU≥90%）",
    d: "周度",
    e: "",
    f: "",
  },
]);

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex !== 4 && columnIndex !== 6) {
    if (rowIndex % 3 === 0) {
      return {
        rowspan: 3,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};
const aiJD = ref([
  {
    t: "1、业务视角解读：",
    data: [
      {
        st: "",
        infoList: [
          {
            title: "",
            info: "痛点根源： 传统预测依赖历史数据和经验，对促销、新品、市场突变（如天气、竞品、社交媒体热点）响应滞后，导致预测偏差大（常>20%）。库存要么积压（占用资金、增加仓储/贬值风险），要么断货（错失销售、损害客户满意度）。",
          },
          {
            title: "",
            info: "战略价值： 这是整个供应链优化的“大脑”。精准预测是降低牛鞭效应、提升库存周转率、保障供应的基石。联动机制确保预测能真正指导库存策略，避免“两张皮”。",
          },
        ],
      },
    ],
  },
  {
    t: "2、关键行动深度解读：",
    data: [
      {
        st: "跨部门数据湖 & AI模型 (准确率≥85%)：",
        infoList: [
          {
            title: "",
            info: "挑战与机遇： 打破部门墙是关键难点。销售数据（订单、渠道库存）、市场数据（促销计划、新品上市、舆情）、供应链数据（在途、生产计划、BOM）必须打通。AI模型需融合传统时间序列、因果分析（如促销影响因子）甚至外部数据（天气、宏观经济）。85%准确率是底线目标，尤其在促销季和新品期需重点攻坚。",
          },
          {
            title: "",
            info: "刘威的行动： 推动建立跨部门数据治理委员会，明确数据标准、所有权和共享机制。亲自协调IT资源，确保模型开发贴合业务场景（如分品类、分区域建模），并预留模型迭代空间。",
          },
        ],
      },
      {
        st: "库存水位动态校准规则 (偏差>10%自动触发)：",
        infoList: [
          {
            title: "",
            info: "业务意义： 将预测偏差转化为即时行动。10%是经验阈值，家电行业SKU多、价值差异大，需考虑设置不同品类的敏感度。自动触发避免人为延误，确保库存策略实时更新。",
          },
          {
            title: "",
            info: "刘威的行动： 主导制定校准规则逻辑，例如：偏差>10%时，自动重新计算安全库存、触发补货建议或预警呆滞风险。与计划、采购团队确认规则可行性，并建立例外审批流程。",
          },
        ],
      },
      {
        st: "月度《预测-库存联动分析报告》：",
        infoList: [
          {
            title: "",
            info: "价值输出： 不仅仅是展示数据，核心是驱动决策和行动。报告必须清晰标注预测偏差原因（模型问题？输入数据问题？突发事件？）、呆滞/高库龄库存清单、根因分析（设计过时？预测失误？采购过量？）及具体改善建议（促销清仓？调拨？退供？）。",
          },
          {
            title: "",
            info: "刘威的行动： 要求报告模板必须包含“行动项”和“负责人”字段。亲自审阅报告，并在月度运营会议上推动跨部门（销售、市场、财务）共同制定和执行呆滞库存处理方案。将报告质量纳入相关团队考核。",
          },
        ],
      },
      {
        st: "预期成果考量：",
        infoList: [
          {
            title: "",
            info: "《白皮书》： 对内是知识沉淀和最佳实践推广；对外（可选）可展示技术实力，提升供应链品牌形象。",
          },
          {
            title: "",
            info: "《诊断报告》： 是库存健康度的“体检报告”，为后续优化（如举措2）提供输入。刘威会重点关注库存周转天数、呆滞库存占比、现货率等核心指标的变化趋势。",
          },
          {
            title: "",
            info: "业务收益： 降低整体库存水平 (目标：5-10%)，提升现货率 (目标：+3-5%)，显著减少呆滞库存损失。",
          },
        ],
      },
    ],
  },
]);

const aiJD2 = ref([
  {
    t: "3、关键行动专项审计",
    data: [
      {
        st: "数据湖质量审计",
        infoList: [
          {
            title: "",
            info: "检视指标：数据更新延迟率（要求<1%）、缺失字段占比（要求<0.5%）",
          },
          {
            title: "",
            info: "工具：数据血缘图谱（追踪销售数据源头至预测模块）",
          },
        ],
      },
      {
        st: "模型迭代机制审计",
        infoList: [
          {
            title: "",
            info: "要求：建立“预测偏差-反馈闭环”，当模型连续3天偏差>15%时自动启动优化迭代",
          },
        ],
      },
      {
        st: "库存重算有效性验证",
        infoList: [
          {
            title: "",
            info: "方法：模拟极端场景（如突发天气导致空调需求激增），测试重算响应是否避免缺货/积压",
          },
        ],
      },
    ],
  },
]);

const aiJD3 = ref([
  {
    t: "3、考核结果激励",
    data: [
      {
        st: "正向激励：",
        infoList: [
          {
            title: "",
            info: "达成准确率目标：奖励团队年度预算提升10%，用于AI算力扩容6 ",
          },
          {
            title: "",
            info: "呆滞库存降低超目标：节省资金的20%转为团队奖金池",
          },
        ],
      },
      {
        st: "负向约束：",
        infoList: [
          {
            title: "",
            info: "连续2个月准确率<80%：冻结模型开发预算，启动第三方审计7",
          },
          {
            title: "",
            info: "销售数据延迟超3次：扣减销售部门供应链协作绩效分（影响年终评优）",
          },
        ],
      },
      {
        st: "4、行为考核维度（适用于责任人评估）",
        infoList: [
          {
            title: "",
            info: "正向创新性：提出预测因子优化方案（如纳入社交媒体声量数据）并被模型采纳；",
          },
          {
            title: "",
            info: "风险管控：提前识别重大偏差（如竞品价格异动）并启动人工干预；",
          },
          {
            title: "",
            info: "知识沉淀：主导编写《白皮书》中“家电需求波动应对”实战案例；",
          },
        ],
      },
      {
        st: "5、任务管控要点：",
        infoList: [
          {
            title: "",
            info: "战略层面：确保预测机制与公司“AI智慧家电”战略对齐（参考TCL智家技术投入方向）4",
          },
          {
            title: "",
            info: "战术层面：每月召开“预测-产销校准会”，用库存健康度报告驱动生产计划调整（避免重蹈某企业VMI库存积压30%覆辙）7",
          },
          {
            title: "",
            info: "文化层面：推行“预测共担”文化，将销售预测准确性纳入区域总经理考",
          },
        ],
      },
    ],
  },
]);
</script>
<template>
  <div class="content-wrap">
    <div class="justify-between">
      <div class="page-title-line">关键任务概要</div>
      <div class=""><span class="pearl_blue">已选指标：</span>库存周转天数</div>
    </div>
    <Table
      :roundBorder="false"
      :columns="columns"
      :data="data"
      headerColor
      showIndex
    >
      <template v-slot:gSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>详情</el-button>
        <el-button class="ai_btn" type="primary" plain round>对比</el-button>
      </template>
    </Table>
    <div class="page-title-line marginT20">关键任务详情</div>
    <el-table
      :data="data2"
      :span-method="objectSpanMethod"
      border
      class="self_table"
    >
      <el-table-column type="index" label="序号" align="center" />
      <el-table-column
        v-for="item in columns2"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        :align="item.align ? item.align : 'center'"
        :width="item.width ? item.width : ''"
      />
      <el-table-column label="操作" align="center" width="90">
        <el-button class="ai_btn" type="primary" plain round>Ai解读</el-button>
      </el-table-column>
    </el-table>
    <el-divider />

    <div class="page-title-line marginT20">人员指标AI解读</div>
    <div class="marginB20 text_b">
      举措1：智能需求预测与库存联动机制（需求驱动的精准库存策略）
    </div>
    <div v-for="item in aiJD">
      <div class="marginT20 marginB20">{{ item.t }}</div>
      <div class="dot_content_wrap marginB20" v-for="item1 in item.data">
        <div class="marginB20 text_b">{{ item1.st }}</div>
        <div class="item_wrap" v-for="item2 in item1.infoList">
          <span class="icon"></span>
          <span class="title"></span>
          <span class="info">{{ item2.info }}</span>
        </div>
      </div>
    </div>

    <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor>
    </Table>

    <div v-for="item in aiJD3">
      <div class="marginT20 marginB20">{{ item.t }}</div>
      <div class="dot_content_wrap marginB20" v-for="item1 in item.data">
        <div class="marginB20 text_b">{{ item1.st }}</div>
        <div class="item_wrap" v-for="item2 in item1.infoList">
          <span class="icon"></span>
          <span class="title"></span>
          <span class="info">{{ item2.info }}</span>
        </div>
      </div>
    </div>
    <!-- <div class="marginB20">4、建议的任务考核：</div>
    <Table :roundBorder="false" :columns="columns4" :data="data4" headerColor>
    </Table> -->
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.content-wrap {
  .pearl_blue {
    color: #94a1af;
  }
  :deep .el-table {
    .ai_btn {
      padding: 0 15px;
      height: 24px;
      font-size: 14px;
    }
    &.self_table {
      width: 100%;
      --el-font-size-base: 14px;
      --el-table-header-text-color: #93abcb;
      --el-table-border-color: #c6dbf3;
      --el-table-tr-bg-color: transparent;
      --el-table-header-bg-color: transparent;
      background-color: transparent;

      .el-table__header .el-table__cell {
        // 隐藏table底边框
        background: #eaf4ff;
      }
    }
  }

  .dot_content_wrap {
    // margin: 0 0 40px 0;
    .item_wrap {
      margin-bottom: 0;
      .icon {
        margin: -2px 0px 0 10px;
      }
    }
  }
}
</style>
