// 本地存储封装
const storage = (type = 'session') => {
  const storage = type == 'session' ? sessionStorage : localStorage
  return {
    set(key, value) {
      try {
        storage.setItem(key, JSON.stringify(value))
      } catch (e) {
        console.error('storage存储失败:', e)
      }
    },

    get(key) {
      try {
        return JSON.parse(storage.getItem(key))
      } catch (error) {
        console.log(error)
        return storage.getItem(key)
      }
    },

    remove(key) {
      storage.removeItem(key)
    },

    clear() {
      storage.clear()
    }
  }
}

export default storage
