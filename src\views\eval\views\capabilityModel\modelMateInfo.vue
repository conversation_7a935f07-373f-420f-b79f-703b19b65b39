<template>
    <div class="model_mate_info_wrap bg_write">
        <div class="page_main_title clearfix">
            岗能建模进度
            <div class="goback_geader" @click="$util.goback()">
                <i class="el-icon-arrow-left"></i>返回
            </div>
        </div>
        <div class="page_section" v-if="showPage">
            <tableComponent :tableData="tableData" :needIndex="true" :needPagination="false">
                <template v-slot:oper>
                    <el-table-column v-for="(item,index) in tableData.userList" :label="item.userName" :key="index" align="center">
                        <template slot-scope="scope">
                            <div class="completion_rate flex_row_between" v-if="scope.row.percent[index].progress">
                                <div class="bar_wrap" >
                                    <div
                                        class="bar_progress"
                                        :class="scope.row.percent[index].progress< 50 ? 'bg_low' : (scope.row.percent[index].progress < 70 ? 'bg_normal' : (scope.row.percent[index].progress < 90 ? 'bg_middle' : 'bg_high'))"
                                        :style="{'width':scope.row.percent[index].progress+'%'}"
                                    ></div>
                                </div>
                                <div
                                        class="completion_rate_num"
                                        :class="{'not_login':scope.row.percent[index].progress == '0'}"
                                >{{scope.row.percent[index].progress}}%
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                </template>
            </tableComponent>
        </div>
        <div class="align_center marginT_30" v-if="status != 4 && status != 3 ">
            <el-button type="primary" @click="modelStayConfirmFun">岗能建模已完成，点击进入模型确认环节</el-button>
        </div>
    </div>
</template>

<script>
    import {getModelProgress,modelStayConfirm} from "../../request/api"
    import tableComponent from "@/components/talent/tableComps/tableComponent";

    export default {
        name: "modelMateInfo",
        components: {
            tableComponent
        },
        data() {
            return {
                showPage:false,
                buildId:this.$route.query.buildId,
                tableData: {
                    columns: [
                        {
                            label: "匹配岗位",
                            prop: "postName",
                            width: 200,
                        },
                    ],
                    data: [],
                    userList: [],
                },
                status:""
            };
        },
        created() {
            console.log(this.buildId);
            this.getModelProgressFun();
        },
        methods: {
            // mate(index, rows) {
            //     console.log(rows[index]);
            //     this.$router.push({
            //         path:'/talentAssessment/capabilityModel/creatModelMate',
            //         query:{}
            //     })
            // },
            progress() {

            },
            manage() {

            },
            getModelProgressFun(){
                getModelProgress({
                    buildId:this.buildId,
                    current: 1,
                    size: 10,
                }).then(res=>{
                    console.log(res);
                    if(res.code == 200){
                        this.showPage = true;
                        this.status = res.data.status;
                        this.tableData.data=res.data.post.map(item=>{
                            return {
                                postName:item.postProgress.post_name,
                                postCode:item.postProgress.post_code,
                                percent:item.userProgress.map(user=>{
                                    return {
                                        userId:user.userId,
                                        progress:user.progress ? user.progress.split("/")[0] : ""
                                        // progress:user.progress ? "89.3" : ""
                                    }
                                })
                            }
                        })
                        this.tableData.userList = res.data.users.map(item=>{
                            return {
                                userName:item.user_name,
                                userId:item.user_id,
                            }
                        })
                    }else{
                        this.$msg.warning(res.msg)
                    }
                })
            },
            modelStayConfirmFun(){
                this.$confirm('此操作将结束岗能建模, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    modelStayConfirm({
                        buildId:this.buildId
                    }).then(res=>{
                        if(res.code == 200){
                            this.$msg.success(res.msg);
                            this.$util.goback()
                        }else{
                            this.$msg.warning(res.msg)
                        }
                    })
                }).catch(() => {

                });

            }

        }
    };
</script>

<style scoped lang="scss">
    .model_mate_info_wrap {
        .completion_rate {
            .bar_wrap {
                width: calc(100% - 45px);
                height: 18px;
                background: #EBF4FF;
                position: relative;
                padding-top: 4px;

                .bar_progress {
                    background: #00b050;
                    height: 8px;
                    width: 50%;

                    &.bg_high {
                        background: #00b050;
                    }

                    &.bg_middle {
                        background: #00b0f0;
                    }

                    &.bg_normal {
                        background: #ffc000;
                    }

                    &.bg_low {
                        background: #ff8181;
                    }
                }
            }

            .completion_rate_num {
                font-weight: bold;

                &.not_login {
                    color: #ff6d6d;
                }
            }
        }
    }

</style>