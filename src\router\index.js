import { createRouter, createWebHashHistory } from 'vue-router'
const modules = import.meta.glob('./modules/*.js', { eager: true })
const autoRoutes = Object.values(modules).flatMap(module => module.default)
console.log(autoRoutes)

const routes = [
  ...autoRoutes,
  {
    path: '/',
    redirect: '/login'
    // component: () => import('@/views/home/<USER>')
  },
  {
    path: '/login',
    component: () => import('@/views/login/index.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes
})

router.beforeEach((to, from, next) => {
  if (to.meta.dynamicTitle && to.query.title) {
    console.log('to', to)
    to.meta.title = to.query.title
    to.matched.at(-1).meta.title = to.query.title
  }
  next()
})

export default router
