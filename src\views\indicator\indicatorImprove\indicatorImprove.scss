.public {
  .table-main {
    border-radius: 10px;
    border: 1px solid #c6dbf3;
    padding: 10px;
    width: 100%;

    :deep(.el-table__header-wrapper) {
      .cell {
        color: #93abcb;
      }
    }

    .btn {
      width: 68px;
      height: 24px;
      border-radius: 78px 78px 78px 78px;
      border: 1px solid #40a0ff;
      font-size: 12px;
      color: #40a0ff;
      line-height: 24px;
      text-align: center;
      cursor: pointer;

      &.active,
      &:hover {
        background: #40a0ff;
        color: #ffffff;
      }
    }
  }
  .active {
    margin-top: 20px;

    .title {
      font-size: 14px;
      display: flex;

      .text {
        color: #94a1af;
      }

      .name {
        color: #3d3d3d;
        font-weight: 600;
      }
    }
    .page-title-line {
      font-size: 16px;
      margin-top: 40px;
    }
    :deep(.el-table__header-wrapper) {
      .cell {
        color: #93abcb;
      }
    }

    :deep(.el-table th.el-table__cell) {
      background: #eaf4ff;
    }
    .num {
      width: 60px;
      height: 23px;
      color: #fff;
      border-radius: 33px 33px 33px 33px;
      margin: 0 auto;
    }
    .decode {
      padding: 20px;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      font-size: 14px;
      color: #666666;
      line-height: 28px;

      .paragraph {
        a {
          color: #666666;
          font-weight: 700;
        }
      }
    }
    .chart-main {
      display: flex;
      justify-content: space-between;
      .item {
        width: 318px;
        height: 265px;
        background:
          linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), rgba(255, 255, 255, 0.5);
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #c6dbf3;
        padding: 26px;
        .title {
          font-size: 14px;
          color: #333333;
          font-weight: 500;
        }
      }
    }
  }
}
