<template>
    <div class="ability_dict_wrap">
        <div class="ability_dict_main flex_row_betweens">
            <div class="main_left page_section">
                <div class="ability_dict_title">
                    <div class="page_second_title">能力分类</div>
                </div>
                <ul class="degree_list_wrap">
                    <li
                        :class="{
                            flex_row_betweens: true,
                            active: item.moduleCode == currModuleCode,
                        }"
                        v-for="(item, index) in abilityList"
                        @click="searchDict(item.moduleCode, index)"
                    >
                        <div class="left">{{ item.moduleName }}</div>
                        <div class="left">{{ item.count }}</div>
                    </li>
                    <li class="no_data_tip" v-if="abilityList.length == 0">
                        暂无数据
                    </li>
                </ul>
            </div>
            <div class="main_left page_section">
                <div class="ability_dict_title flex_row_betweens">
                    <div class="page_second_title">能力词典</div>
                    <div class="align_right" v-if="buildStatus !== '4'">
                        <el-button
                            class="icon_plus"
                            type="primary"
                            size="mini"
                            @click="toggleModel"
                            >选择</el-button
                        >
                        <el-button
                            class="icon_plus"
                            icon="el-icon-plus"
                            link
                            size="mini"
                            @click="creatDegree"
                        ></el-button>
                    </div>
                </div>
                <ul class="degree_list_wrap">
                    <li
                        class="flex_row_betweens"
                        :class="{ active: index == currDictIndex }"
                        v-for="(item, index) in abilityDictList"
                        @click="selectItem(index)"
                    >
                        <div class="left">{{ item.moduleName }}</div>
                        <div class="icon_group" v-if="buildStatus !== '4'">
                            <!--                            <span class="el-icon-edit icon_edit" @click="editorDegree"></span>-->
                            <span
                                class="el-icon-delete icon_del"
                                @click="removeDegree(item.moduleCode, index)"
                            ></span>
                        </div>
                    </li>
                    <li class="no_data_tip" v-if="abilityDictList.length == 0">
                        暂无数据
                    </li>
                </ul>
            </div>
            <div class="main_right" :class="{ event_none: buildStatus == '4' }">
                <div class="degree_info_wrap" v-show="abilityDictList.length">
                    <el-form
                        :model="ruleForm"
                        :rules="rules"
                        ref="ruleForm"
                        label-position="top"
                        class="demo-ruleForm"
                    >
                        <el-form-item label="能力词典" prop="moduleName">
                            <el-input
                                v-model.trim="ruleForm.moduleName"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="能力描述" prop="moduleDesc">
                            <el-input
                                type="textarea"
                                v-model.trim="ruleForm.moduleDesc"
                                min="3"
                                autosize
                                placeholder="最多256个字符"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="关键行为" prop="itemDesc">
                            <el-input
                                type="textarea"
                                v-model="ruleForm.itemDesc"
                                :autosize="{ minRows: 2, maxRows: 6}"
                                placeholder="最多1024个字符"
                            ></el-input>
                        </el-form-item>
                        <div class="align_center" v-if="buildStatus !== '4'">
                            <el-button
                                class="page_add_btn"
                                type="primary"
                                @click="submitItem"
                                >确认</el-button
                            >
                        </div>
                    </el-form>
                    <!-- <div class="line_item_wrap">
                        <div class="descript">能力词典</div>
                        <div class="input_wrap">
                            <el-input v-model='dictInfo.moduleName'></el-input>
                        </div>
                    </div>
                    <div class="line_item_wrap">
                        <div class="descript">能力描述</div>
                        <div class="input_wrap">
                            <el-input type="textarea" v-model='dictInfo.moduleDesc' rows="3" resize="none"></el-input>
                        </div>
                    </div>
                    <div class="line_item_wrap">
                        <div class="descript">关键行为</div>
                        <div class="input_wrap">
                            <el-input type="textarea" v-model='dictInfo.itemList[0].itemDesc' rows="3" resize="none"></el-input>
                        </div>
                    </div>
                    <div class="align_center" v-if="buildStatus !== '4'">
                        <el-button class="page_add_btn" type="primary" @click="submitItem">确认</el-button>
                    </div> -->
                </div>
            </div>
        </div>
        <div class="align_center" v-if="buildStatus !== '4'">
            <el-button class="page_confirm_btn" type="primary" @click="prev()"
                >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="next()"
                >下一步</el-button
            >
        </div>
        <div class="popup_wrap">
            <el-dialog :title="popupName" :visible.sync="dialogFormVisible">
                <div class="line_item_wrap">
                    <div class="descript">能力词典</div>
                    <div class="input_wrap">
                        <el-input
                            ref="degreeName"
                            v-model="degreeName"
                            @keyup.enter.native="addDictItem"
                            autofocus="true"
                        ></el-input>
                    </div>
                </div>
                <div slot="footer" class="dialog-footer">
                    <el-button
                        class="page_clear_btn"
                        @click="dialogFormVisible = false"
                        >取 消</el-button
                    >
                    <el-button
                        class="page_add_btn"
                        type="primary"
                        @click="addDictItem"
                        >确 定</el-button
                    >
                </div>
            </el-dialog>
        </div>
        <!-- 选择能力模块弹窗 -->
        <el-dialog
            title="选择词典"
            :width="'80%'"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :visible.sync="dialogTableVisible"
        >
        <div class="filter_bar_wrap">
          <div class="flex_row_start">
            <div class="filter_item title">筛选:</div>
            <div class="filter_item">
              <el-select
                v-model="dialogFilter.modelId"
                clearable
                placeholder="所属模型"
                
              >
                <el-option
                                v-for="item in modelOption"
                                :key="item.modelId"
                                :label="item.modelName"
                                :value="item.modelId"
                            ></el-option>
              </el-select>
            </div>
            <div class="filter_item">
              <el-select
                v-model="dialogFilter.moduleCode"
                clearable
                placeholder="所属模块"
                
              >
                <el-option
                  v-for="item in moduleOptions"
                  :label="item.moduleName"
                  :value="item.moduleCode"
                ></el-option>
              </el-select>
            </div>
            <div class="filter_item">
              <el-input v-model="dialogFilter.dictionaryName" placeholder="按词典名称检索" clearable  />
            </div>
            <div class="filter_item">
              <el-input v-model="dialogFilter.dictionaryDesc" placeholder="按词典描述检索" clearable  />
            </div>
            
            <div class="filter_item">
              <el-button
                class="page_add_btn"
                type="primary"
                
                @click="getModelList"
                >查询</el-button
              >
            </div>
          </div>
        </div>
            <tableComponent
                :needIndex="true"
                :tableData="gridData"
                @page="pageChange"
            >
                <template v-slot:oper>
                    <el-table-column label="操作" width="100">
                        <template slot-scope="scope">
                            <el-button
                                @click.native.prevent="
                                    rowChoose(scope.$index, gridData.data[scope.$index])
                                "
                                :type="scope.row.status ? '' : 'primary'"
                                size="mini"
                                >{{scope.row.status ? '取消':'确定'}}</el-button
                            >
                            <!-- <el-button
                                class="color_danger"
                                @click.native.prevent="
                                    tableDeleteRow(scope.$index, tableData.data)
                                "
                                link
                                
                                >删除</el-button
                            > -->
                        </template>
                    </el-table-column>
                </template>
            </tableComponent>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogTableVisible = false">取 消</el-button>
                <!-- <el-button type="primary" @click="dialogComfirm"
                    >确 定</el-button
                > -->
            </span>
        </el-dialog>
    </div>
</template>

<script>
    import {
        getModelModule,
        createModelModule,
        deleteModelModule,
        getCompetenceDictionary,
        getDictItem,
        getCompetenceModule,
        getModelCheckList,
        moduleList
    } from "../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";

    export default {
        name: "abilityDict",
        props: ["modelId", "buildStatus"],
        components: {tableComponent},
        data() {
            return {
                currIndex: 0,
                currDictIndex: 0,
                abilityList: [],
                abilityDictList: [],//选中能力分类下的能力词典列表
                currModuleCode: "",
                currDictCode: "",
                dialogFormVisible: false,
                degreeName: "",
                popupName: "",
                // dictInfo: {
                //     itemList: [
                //         {
                //             itemDesc: "",
                //         },
                //     ],
                //     itemDesc:'',
                //     itemId: "",
                //     moduleName: "",
                //     moduleDesc: "",
                // },
                ruleForm: {
                    itemList: [
                        {
                            itemDesc: "",//关键行为
                        },
                    ],
                    itemDesc: "",//关键行为
                    itemId: "",
                    moduleName: "",// 词典名称
                    moduleDesc: "",// 词典描述
                },
                rules: {
                    moduleName: [
                        {
                            required: true,
                            message: "请填写能力词典",
                            trigger: "blur",
                        },
                    ],
                    moduleDesc: [
                        {
                            required: true,
                            message: "请填写能力描述",
                            trigger: "blur",
                        },
                        {
                            min: 0,
                            max: 256,
                            message: "最多256个字符",
                            trigger: "blur",
                        },
                    ],
                    itemDesc: [
                        {
                            required: true,
                            message: "请填写关键行为",
                            trigger: "blur",
                        },
                        {
                            min: 0,
                            max: 1024,
                            message: "最多1024个字符",
                            trigger: "blur",
                        },
                    ],
                },
                moduleTypeList: [],
                moduleInfo:{},// 表格选中的模块信息
                dialogTableVisible: false,
                modelOption:[],
                moduleOptions:[],
                dialogFilter:{
                    modelCode:null,
                    moduleCode:null,
                    dictionaryName: "",// 词典名称
                    dictionaryDesc: "",// 词典描述
                    current:1,
                    size:10
                },
                gridData: {
                    columns: [
                        {
                            label: "词典编码",
                            prop: "moduleCode",
                        },
                        {
                            label: "词典名称",
                            prop: "dictionaryName",
                        },
                        {
                            label: "所属模型",
                            prop: "modelName",
                        },
                        {
                            label: "所属模块",
                            prop: "moduleName",
                        },
                        {
                            label: "词典描述",
                            prop: "dictionaryDesc",
                        },
                    ],
                    data: [
                    ],
                    page:{
                        current:1,
                        size:10,
                        total:0
                    }
                },
            };
        },
        created() {
            this.getModelModuleFun();
            this.getModelCheckListFn();
            // this.getDictItemFun("COMPETENCE_CLASS").then((res) => {
            //     // this.moduleOptions = res;
            // });
            moduleList().then(res => {
                console.log(res);
                this.moduleOptions = res;
            })
        },
        mounted() {},
        methods: {
            //获取分类属性
            async getDictItemFun(dictCode) {
                return await getDictItem({
                    dictId: dictCode,
                }).then((res) => {
                    if (res.code == 200) {
                        return res.data;
                    }
                });
            },
            // 获取模型列表
            getModelCheckListFn(){
                getModelCheckList().then(res => {
                    console.log(res);
                    this.modelOption = res;
                })
            },
            searchDict(code, index) {
                console.log(code);
                this.currModuleCode = code;
                this.currIndex = index;
                this.currDictIndex = 0;
                this.abilityDictList = this.abilityList[index].childrenList;
                console.log(this.abilityDictList);
                if (this.abilityDictList.length > 0) {
                    // this.ruleForm = this.$util.deepClone(this.abilityDictList[this.currDictIndex]);
                    // this.ruleForm.itemDesc = this.ruleForm.itemList[0].itemDesc;
                    let copyData = this.$util.deepClone(
                        this.abilityDictList[this.currDictIndex]
                    );
                    this.ruleForm = copyData;
                    this.$set(this.ruleForm,'itemDesc',copyData.itemList[0].itemDesc)
                } else {
                    this.ruleForm = {
                        itemList: [
                            {
                                itemDesc: "",
                            },
                        ],
                        itemDesc: "",
                        itemId: "",
                        moduleName: "",
                        moduleDesc: "",
                    };
                }
            },
            creatDegree() {
                this.dialogFormVisible = true;
                // this.$refs.degreeName.focus();
                this.popupName = "新增";
                this.$nextTick(() => {
                    this.$refs.degreeName.focus();
                });
            },
            // 获取模块列表
            getModelList(){
                getCompetenceDictionary(this.dialogFilter).then(res => {
                    console.log(res);
                    if(res.code == 200 ){
                        this.gridData.data = res.data;
                        this.gridData.page = res.page;
                    }
                })
            },
            // 弹窗显隐
            toggleModel() {
                this.dialogTableVisible = !this.dialogTableVisible;
                if(this.dialogTableVisible){
                    this.getModelList();
                }
            },
            // 选择能力分类
            chooseDegree() {},
            // 表格行确定按钮
            rowChoose(index, row) {
                console.log(index, row);
                this.moduleInfo = row.status ? {} : row;
                row.status = !row.status;
                console.log(this.moduleInfo);
                this.toggleModel();
                this.abilityDictList.push({
                    itemList: [
                        {
                            itemDesc: this.moduleInfo.dictionaryDesc,
                        },
                    ],
                    itemId: "",
                    itemDesc: this.moduleInfo.itemList[0].itemDesc,
                    moduleName: this.moduleInfo.dictionaryName,
                    moduleDesc: this.moduleInfo.dictionaryDesc,
                });
                this.currDictIndex = this.abilityDictList.length - 1;

                let copyData = this.$util.deepClone(
                    this.abilityDictList[this.currDictIndex]
                );
                this.ruleForm = copyData;

            },
            // 弹窗确定按钮
            dialogComfirm() {
                if(this.$util.isEmptyObj(this.moduleInfo)){
                    this.$msg.warning("请选择词典");
                    return
                }
                // this.abilityDictList.push(this.$util.deepClone(this.moduleInfo));
                // this.degreeList.push(this.$util.deepClone(this.moduleInfo));
                console.log(this.moduleInfo);
                this.toggleModel();
                this.abilityDictList.push({
                    itemList: [
                        {
                            itemDesc: this.moduleInfo.itemDesc,
                        },
                    ],
                    ...this.moduleInfo
                    // itemId: "",
                    // itemDesc: "",
                    // moduleName: this.degreeName,
                    // moduleDesc: "",
                });
                // this.currIndex = this.abilityDictList.length - 1;
                // this.ruleForm = this.abilityDictList[this.currIndex];

            },
            editorDegree() {
                this.dialogFormVisible = true;
                this.popupName = "编辑";
            },
            //
            pageChange(size, page) {
                console.log(size, page);
                this.dialogFilter.size = size;
                this.dialogFilter.current = page;
                this.getModelList();
            },
            addDictItem() {
                this.abilityDictList.push({
                    itemList: [
                        {
                            itemDesc: "",
                        },
                    ],
                    itemId: "",
                    itemDesc: "",
                    moduleName: this.degreeName,
                    moduleDesc: "",
                });
                this.currDictIndex = this.abilityDictList.length - 1;
                // this.ruleForm = this.$util.deepClone(this.abilityDictList[this.currDictIndex]);
                // this.ruleForm.itemDesc = this.ruleForm.itemList[0].itemDesc;

                let copyData = this.$util.deepClone(
                    this.abilityDictList[this.currDictIndex]
                );
                this.ruleForm = copyData;
                this.$set(this.ruleForm,'itemDesc',copyData.itemList[0].itemDesc)

                this.dialogFormVisible = false;
                this.degreeName = "";
            },

            selectItem(index) {
                this.currDictIndex = index;
                let copyData = this.$util.deepClone(
                    this.abilityDictList[this.currDictIndex]
                );
                this.ruleForm = copyData;
                this.$set(this.ruleForm,'itemDesc',copyData.itemList[0].itemDesc)
            },
            //删除按钮
            removeDegree(moduleCode, index) {
                this.$confirm("此操作将删除该分类, 是否继续?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        if (!moduleCode) {
                            this.abilityDictList.splice(index, 1);
                            this.$message({
                                type: "success",
                                message: "删除成功!",
                            });
                            this.getModelModuleFun();
                            this.currDictIndex = 0;
                        } else {
                            this.deleteModelModuleFun(moduleCode);
                        }
                    })
                    .catch(() => {});
            },

            prev() {
                this.$emit("prevStep");
            },
            next() {
                for (let index = 0; index < this.abilityList.length; index++) {
                    const item = this.abilityList[index];
                    if (item.childrenList.length == 0) {
                        this.$msg.warning(
                            `能力分类：${item.moduleName} 下至少添加一项能力词典`
                        );
                        return;
                    } else {
                        for (let j = 0; j < item.childrenList.length; j++) {
                            const ele = item.childrenList[j];
                            if (ele.itemId == "") {
                                this.$msg.warning(
                                    `能力分类：${item.moduleName} 的能力词典：${ele.moduleName} 信息未确认`
                                );
                                return;
                            }
                        }
                    }
                }
                this.$emit("nextStep");
            },
            submitItem() {
                this.submitForm("ruleForm");
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.createModelModuleFun();
                    } else {
                        console.log("error submit!!");
                        return false;
                    }
                });
            },
            //获取能力分类
            getModelModuleFun() {
                getModelModule({
                    modelId: this.modelId,
                }).then((res) => {
                    console.log(res);
                    this.abilityList = res;
                    if (this.abilityList.length > 0) {
                        this.currModuleCode = this.abilityList[
                            this.currIndex
                        ].moduleCode;
                        this.abilityDictList = this.abilityList[
                            this.currIndex
                        ].childrenList;
                        if (this.abilityDictList.length > 0) {
                            // this.ruleForm = this.$util.deepClone(this.abilityDictList[this.currDictIndex])
                            // this.ruleForm.itemDesc = this.ruleForm.itemList[0].itemDesc;

                            let copyData = this.$util.deepClone(
                                this.abilityDictList[this.currDictIndex]
                            );
                            this.ruleForm = copyData;
                            this.$set(this.ruleForm,'itemDesc',copyData.itemList[0].itemDesc)
                        } else {
                            this.ruleForm = {
                                itemList: [
                                    {
                                        itemDesc: "",
                                    },
                                ],
                                itemDesc: "",
                                itemId: "",
                                moduleName: "",
                                moduleDesc: "",
                            };
                        }
                    } else {
                        this.ruleForm = {
                            itemList: [
                                {
                                    itemDesc: "",
                                },
                            ],
                            itemDesc: "",
                            itemId: "",
                            moduleName: "",
                            moduleDesc: "",
                        };
                    }
                    console.log(this.ruleForm);
                });
            },
            //创建、修改能力分类
            createModelModuleFun() {
                console.log(this.ruleForm);
                let params = {
                    itemList: [
                        {
                            itemId:
                                this.ruleForm.itemList &&
                                this.ruleForm.itemList.length > 0
                                    ? this.ruleForm.itemList[0].itemId
                                    : "",
                            itemName: this.ruleForm.moduleDesc,
                            itemDesc: this.ruleForm.itemDesc,
                        },
                    ],

                    modelId: this.modelId,
                    layerNo: 2,
                    moduleCode: this.ruleForm.moduleCode,
                    moduleName: this.ruleForm.moduleName,
                    moduleDesc: this.ruleForm.moduleDesc,
                    parentModuleCode: this.currModuleCode,
                };
                console.log(params);
                createModelModule(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$msg.success(res.msg);
                        this.currDictIndex = 0;
                        this.getModelModuleFun();
                    }
                });
            },
            //删除能力分类
            deleteModelModuleFun(moduleCode) {
                let params = {
                    modelId: this.modelId,
                    moduleCode: moduleCode,
                };
                deleteModelModule(params).then((res) => {
                    if (res.code == 200) {
                        this.$msg.success("删除成功！");
                        this.getModelModuleFun();
                        this.currDictIndex = 0;
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .ability_dict_wrap {
        .ability_dict_main {
            margin: 20px 0;

            .ability_dict_title {
                line-height: 30px;
                border-radius: 3px;
            }

            .main_left {
                width: 25%;

                .ability_dict_title {
                    height: 30px;
                    line-height: 30px;
                    margin-bottom: 16px;

                    span {
                        margin: 0 0 0 10px;

                        &.creat {
                            font-weight: 700;
                            cursor: pointer;
                        }

                        &.creat:hover {
                            color: #4ab6fd;
                        }
                    }
                }

                .degree_list_wrap {
                    height: 400px;
                    overflow-y: auto;
                    border: 1px solid #e5e5e5;

                    li {
                        height: 40px;
                        line-height: 40px;
                        padding: 0 8px;
                        cursor: pointer;

                        .icon_group {
                            span {
                                margin: 0 0 0 10px;
                                line-height: 30px;
                                cursor: pointer;
                            }
                        }
                    }

                    .active {
                        cursor: pointer;

                        .left {
                            color: #fff;
                        }
                        .icon_del {
                            color: #fff !important;
                        }

                        background: #0099FF;
                    }
                }
            }

            .main_right {
                width: 50%;
                height: 400px;
                border: 1px solid #e5e5e5;
                margin-top: 50px;
                overflow-y: auto;

                .degree_info_wrap {
                    .line_item_wrap {
                        padding: 0 8px;
                        margin-bottom: 20px;

                        .descript {
                            line-height: 32px;
                            font-size: 14px;
                            position: relative;
                            margin-top: 10px;
                            padding-left: 15px;

                            &::after {
                                content: "";
                                position: absolute;
                                width: 10px;
                                height: 10px;
                                left: 0;
                                top: 12px;
                                background: #0099FF;
                                border-radius: 50%;
                            }

                            .tips {
                                display: inline-block;
                                color: #f33333;
                                font-size: 12px;
                            }
                        }

                        .input_wrap {
                        }
                    }

                    .btn {
                        margin: 10px 8px 0 10px;
                        text-align: right;
                    }
                }
            }
        }
    }

    .popup_wrap {
        .line_item_wrap {
            display: flex;

            .descript {
                position: relative;
                width: 100px;
                height: 40px;
                line-height: 40px;
            }

            .input_wrap {
                width: 85%;
            }
        }
    }
    .demo-ruleForm {
        padding-left: 8px;
        padding-right: 8px;
    }
    .el-form--label-top .el-form-item__label {
        position: relative;
        padding-left: 15px;
    }
    .el-form-item.is-required:not(.is-no-asterisk)
        > .el-form-item__label:before {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        left: 0;
        top: 11px;
        background: #0099FF;
        border-radius: 50%;
    }
</style>