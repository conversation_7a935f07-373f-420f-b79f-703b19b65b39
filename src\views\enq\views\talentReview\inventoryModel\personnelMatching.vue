<template>
  <div class="talent_review_report_center clearfix">
    <div class="oper_btn_wrap">
      <el-button class="page_add_btn" type="primary" @click="Import"
        >导入</el-button
      >
    </div>
    <table-component
      :needIndex="true"
      :tableData="tableData"
      :needPagination="false"
    ></table-component>
    <personnel-import-popUp
      :show.sync="showPopUp"
      @importSign="importSign"
    ></personnel-import-popUp>
  </div>
</template>
 
<script>
import tableComponent from "@/components/talent/tableComps/tableComponent";
import personnelImportPopUp from "./personnelImportPopUp.vue";
import { personnelMatchingList } from "../../../request/api";
export default {
  name: "personnelMatching",
  props: ["tabsPanesign"],
  data() {
    return {
      modelId: "",
      tableData: {
        columns: [
          {
            label: "一级部门",
            prop: "userName",
          },
          {
            label: "二级部门",
            prop: "userName",
          },
          {
            label: "三级部门",
            prop: "userName",
          },
          {
            label: "四级部门",
            prop: "userName",
          },
          {
            label: "姓名",
            prop: "userName",
          },
          {
            label: "工号",
            prop: "userName",
          },
          {
            label: "岗位",
            prop: "userName",
          },
          {
            label: "职层",
            prop: "userName",
          },
          {
            label: "职级",
            prop: "userName",
          },
          {
            label: "字典1",
            prop: "userName",
          },
          {
            label: "字典2",
            prop: "userName",
          },
        ],
        data: [],
      },
      showPopUp: false,
    };
  },
  components: {
    tableComponent,
    personnelImportPopUp,
  },
  mounted() {
    this.personnelMatchingListFun();
  },
  methods: {
    personnelMatchingListFun() {
      personnelMatchingList({
        // modelId:this.modelId,
        modelId: 30,
      }).then((res) => {
        this.tableData.data = [];
        if (res.code == 200) {
          this.tableData.data = res.data;
        }
      });
    },
    // ------从前------
    Import() {
      this.showPopUp = true;
    },
    importSign(importSign) {
      if (importSign) {
        this.tableData.page.size = 10;
        this.tableData.page.current = 1;
        this.reportRelationshipListFun();
      }
    },
  },
};
</script>
 
<style scoped lang="scss">
</style>