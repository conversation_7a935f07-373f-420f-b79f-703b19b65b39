<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import SimplenessTable from '@/components/table/simplenessTable.vue'

defineOptions({ name: 'org' })
const activeType = ref('整体')
const typeList = ref(['整体', '详情'])
const changeType = item => {
  activeType.value = item
}
const overviewColumns = ref([
  { prop: 'A', label: '流程赋能', width: 150 },
  { prop: 'B', label: '组织赋能', width: 150 },
  { prop: 'C', label: '人岗赋能', width: 150 },
  { prop: 'D', label: '数字化赋能', width: 150 }
])
const overviewData = ref([
  {
    name: '采购部',
    A: '51',
    B: '50',
    C: '37',
    D: '50',
    E: '31'
  },
  {
    name: '重庆冰箱工厂',
    A: '39',
    B: '35',
    C: '53',
    D: '52',
    E: '43'
  },
  {
    name: '电子商务部',
    A: '42',
    B: '47',
    C: '52',
    D: '51',
    E: '38'
  },
  {
    name: '欧盟区产品部',
    A: '47',
    B: '54',
    C: '52',
    D: '49',
    E: '33'
  },
  {
    name: '工艺部',
    A: '45',
    B: '42',
    C: '45',
    D: '45',
    E: '36'
  },
  {
    name: '供应链计划管理部',
    A: '47',
    B: '50',
    C: '39',
    D: '47',
    E: '34'
  },
  {
    name: 'GTM部',
    A: '36',
    B: '50',
    C: '41',
    D: '48',
    E: '44'
  },
  {
    name: '结构研发部',
    A: '44',
    B: '46',
    C: '42',
    D: '53',
    E: '34'
  },
  {
    name: '经营管理部',
    A: '42',
    B: '41',
    C: '54',
    D: '54',
    E: '35'
  },
  {
    name: '冷柜研发部',
    A: '40',
    B: '42',
    C: '48',
    D: '50',
    E: '37'
  },
  {
    name: '零售与用户运营部',
    A: '44',
    B: '51',
    C: '43',
    D: '38',
    E: '30'
  },
  {
    name: '品牌与产品营销部',
    A: '45',
    B: '50',
    C: '36',
    D: '50',
    E: '32'
  },
  {
    name: '威海冰冷工厂',
    A: '38',
    B: '43',
    C: '53',
    D: '52',
    E: '43'
  },
  {
    name: '渠道运营部',
    A: '43',
    B: '45',
    C: '45',
    D: '45',
    E: '35'
  },
  {
    name: '全球产品经理部',
    A: '52',
    B: '38',
    C: '50',
    D: '45',
    E: '39'
  },
  {
    name: '格米冰冷GTM部',
    A: '42',
    B: '55',
    C: '51',
    D: '41',
    E: '33'
  },
  {
    name: '东莞冰冷工厂',
    A: '38',
    B: '37',
    C: '38',
    D: '45',
    E: '40'
  },
  {
    name: '东莞研发部',
    A: '41',
    B: '38',
    C: '39',
    D: '46',
    E: '38'
  },
  {
    name: '苏州冰箱工厂',
    A: '41',
    B: '40',
    C: '46',
    D: '51',
    E: '40'
  },
  {
    name: '营销管理部',
    A: '44',
    B: '54',
    C: '42',
    D: '49',
    E: '31'
  },
  {
    name: '用户服务部',
    A: '44',
    B: '47',
    C: '36',
    D: '41',
    E: '32'
  },
  {
    name: '战略与变革管理部',
    A: '42',
    B: '40',
    C: '41',
    D: '55',
    E: '39'
  },
  {
    name: '制造中心领导',
    A: '52',
    B: '49',
    C: '53',
    D: '37',
    E: '32'
  },
  {
    name: '质量部',
    A: '45',
    B: '49',
    C: '46',
    D: '47',
    E: '31'
  },
  {
    name: '智能制造推进部',
    A: '55',
    B: '50',
    C: '37',
    D: '37',
    E: '42'
  }
])
const setColor = value => {
  if (value <= 40) {
    return '#83ECF7'
  } else if (value <= 50) {
    return '#A0F0F6'
  } else {
    return '#6ACEFC'
  }
}

const activeDNAType = ref('优势组织排名')
const typeDNAList = ref(['优势组织排名', '劣势组织渲染'])

const chartList = ref([
  {
    title: '流程赋能排名',
    data: [
      {
        name: '智能制造推进部',
        value: '55'
      },
      {
        name: '全球产品经理部',
        value: '52'
      },
      {
        name: '制造中心领导',
        value: '52'
      },
      {
        name: '采购部',
        value: '51'
      },
      {
        name: '欧盟区产品部',
        value: '47'
      },
      {
        name: '供应链计划管理部',
        value: '47'
      },
      {
        name: '工艺部',
        value: '45'
      },
      {
        name: '品牌与产品营销部',
        value: '45'
      },
      {
        name: '质量部',
        value: '45'
      },
      {
        name: '结构研发部',
        value: '44'
      }
    ]
  },
  {
    title: '组织赋能排名',
    data: [
      {
        name: '格米冰冷GTM部',
        value: '55'
      },
      {
        name: '欧盟区产品部',
        value: '54'
      },
      {
        name: '营销管理部',
        value: '54'
      },
      {
        name: '零售与用户运营部',
        value: '51'
      },
      {
        name: '采购部',
        value: '50'
      },
      {
        name: '供应链计划管理部',
        value: '50'
      },
      {
        name: 'GTM部',
        value: '50'
      },
      {
        name: '品牌与产品营销部',
        value: '50'
      },
      {
        name: '重庆冰箱工厂',
        value: '50'
      },
      {
        name: '制造中心领导',
        value: '49'
      }
    ]
  },
  {
    title: '人岗赋能排名',
    data: [
      {
        name: '经营与财务管理部',
        value: '54'
      },
      {
        name: '成都冰箱工厂',
        value: '53'
      },
      {
        name: '胶州冰冷工厂',
        value: '53'
      },
      {
        name: '制造中心领导',
        value: '53'
      },
      {
        name: '电子商务部',
        value: '52'
      },
      {
        name: '欧盟区产品部',
        value: '52'
      },
      {
        name: '冰冷GTM部',
        value: '51'
      },
      {
        name: '全球产品经理部',
        value: '50'
      },
      {
        name: '冷柜研发部',
        value: '48'
      },
      {
        name: '扬州冰箱工厂',
        value: '46'
      }
    ]
  },
  {
    title: '数字化赋能排名',
    data: [
      {
        name: '战略与变革管理部',
        value: '55'
      },
      {
        name: '经营与财务管理部',
        value: '54'
      },
      {
        name: '结构研发部',
        value: '53'
      },
      {
        name: '成都冰箱工厂',
        value: '52'
      },
      {
        name: '胶州冰冷工厂',
        value: '52'
      },
      {
        name: '电子商务部',
        value: '51'
      },
      {
        name: '扬州冰箱工厂',
        value: '51'
      },
      {
        name: '采购部',
        value: '50'
      },
      {
        name: '冷柜研发部',
        value: '50'
      },
      {
        name: '品牌与产品营销部',
        value: '50'
      }
    ]
  },
  {
    title: 'AI赋能排名',
    data: [
      {
        name: 'GTM部',
        value: '44'
      },
      {
        name: '成都冰箱工厂',
        value: '43'
      },
      {
        name: '胶州冰冷工厂',
        value: '43'
      },
      {
        name: '全球产品经理部',
        value: '42'
      },
      {
        name: '顺德冰冷工厂',
        value: '40'
      },
      {
        name: '扬州冰箱工厂',
        value: '40'
      },
      {
        name: '全球产品经理部',
        value: '39'
      },
      {
        name: '战略与变革管理部',
        value: '39'
      },
      {
        name: '电子商务部',
        value: '38'
      },
      {
        name: '顺德研发部',
        value: '38'
      }
    ]
  }
])
const getChartOpt = item => {
  return {
    xAxisData: item.data.map(i => i.name).reverse(),
    xAxis: {
      show: false
    },
    grid: {
      left: 0,
      top: 10,
      right: 0,
      bottom: 0
    },
    series: [
      {
        data: item.data.map(i => i.value).reverse(),
        type: 'bar',
        showBackground: true,
        itemStyle: {
          color: '#40a0ff'
        },
        label: {
          show: true
        }
      }
    ]
  }
}

const DNAOrgColumns = ref([
  {
    label: '序号',
    type: 'index'
  },
  {
    label: '画像类型',
    prop: 'name'
  },
  {
    label: '典型组织名称',
    prop: 'orgName',
    width: '100px'
  },
  {
    label: '核心表现',
    prop: 'core'
  },
  {
    label: '数据表现',
    prop: 'data'
  },
  {
    label: '优势',
    prop: 'advantage'
  },
  {
    label: '风险',
    prop: 'risk'
  }
])

const DNAOrgData = ref([
  {
    name: '流程驱动型组织',
    orgName: '智能制造推进部',
    core: '流程标准化程度高，阶段推进强制规则明确，但人员能力或系统支撑可能存在短板',
    data: '・流程维度 55（高）・人岗维度 37（低）・数字化维度 37（低）',
    advantage: '商机推进可预测性强，减少人为失误',
    risk: '过度僵化导致客户响应灵活性不足'
  },
  {
    name: '人员能力型组织',
    orgName: '冰冷 GTM 部、经营与财务管理部',
    core: '依赖高技能团队驱动商机转化，培训与评估体系完善，但流程与系统支撑较弱',
    data: '・人岗维度 51/54（高）・流程维度 42/42（中）・AI 维度 33/35（低）',
    advantage: '高技能团队驱动效率，培训体系完善',
    risk: '流程不规范导致执行效率波动，系统支撑不足影响规模化'
  },
  {
    name: '系统支撑型组织',
    orgName: '战略与变革管理部',
    core: '技术工具深度嵌入业务流程，自动化与集成度高，但规则执行与人员能力匹配不足',
    data: '・数字化维度 55（高）・流程维度 42（中）・人岗维度 41（中）',
    advantage: '技术工具提升业务自动化水平',
    risk: '规则执行松散导致流程失控，人员能力不足影响工具效能'
  },
  {
    name: '数据驱动型组织',
    orgName: '经营与财务管理部、战略与变革管理部',
    core: '数据治理与 BI 应用成熟，依赖数据决策，但流程与人员能力可能成为瓶颈',
    data: '・数字化维度 54/55（高）・AI 维度 35/39（中）・流程维度 42/42（中）',
    advantage: '数据驱动决策提升科学性与精准度',
    risk: '流程不完善导致决策落地低效，人员能力限制数据价值挖掘'
  },
  {
    name: '规则执行型组织',
    orgName: '采购部、制造中心领导',
    core: '强规则约束与文档输出，合规性高，但创新能力与客户体验管理薄弱',
    data: '・流程维度 51/52（高）・组织维度 50/49（高）・AI 维度 31/32（低）',
    advantage: '合规性高，减少操作风险',
    risk: '创新不足导致市场响应滞后，客户体验管理缺失'
  },
  {
    name: '碎片化组织',
    orgName: '顺德研发部、零售与用户运营部、工艺部、渠道运营部',
    core: '各维度能力分散且无突出优势，存在多处能力断点',
    data: '・流程维度 41/44/45/43（中）・组织维度 38/51/42/45（中低）・人岗维度 39/43/45/45（中）・数字化维度 46/38/45/45（中）・AI 维度 38/30/36/35（低）',
    advantage: '无显著优势维度',
    risk: '能力断点导致协同效率低下，战略目标落地困难'
  }
])

const orgSuggestionCol = ref([
  {
    label: '序号',
    type: 'index'
  },
  {
    label: '组织名称',
    prop: 'orgName',
    width: '80px'
  },
  {
    label: '画像类型',
    prop: 'type',
    width: '100px'
  },
  {
    label: '首要改善项',
    prop: 'improve'
  },
  {
    label: '次要加强项',
    prop: 'reinforce'
  },
  {
    label: '优势保持项',
    prop: 'advantage'
  },
  {
    label: '关键任务',
    prop: 'task',
    width: '50px'
  }
])

const orgSuggestionData = ref([
  {
    orgName: '智能制造推进部',
    type: '流程驱动型组织',
    improve: '・人岗维度短板（人员能力评估 37 分）',
    reinforce: '・数字化维度升级（自动化设备 37 分）',
    advantage: '・流程端到端闭环（55 分）',
    task: '2'
  },
  {
    orgName: '战略与变革管理部',
    type: '系统支撑型组织',
    improve: '・流程维度断裂（流程端到端闭环 42 分）',
    reinforce: '・人岗匹配优化（岗位协同 RACI 41 分）',
    advantage: '・系统赋能（55 分）',
    task: '3'
  },
  {
    orgName: '冰冷 GTM 部',
    type: '人员能力型组织',
    improve: '・流程闭环缺失（流程维度 42 分）',
    reinforce: '・AI 能力建设（算法赋能 33 分）',
    advantage: '・人员能力评估（51 分）',
    task: '3'
  },
  {
    orgName: '经营与财务管理部',
    type: '数据驱动型组织',
    improve: '・规则体系薄弱（业务规则 42 分）',
    reinforce: '・人员能力培训（能力培训 41 分）',
    advantage: '・数据治理（54 分）',
    task: '3'
  },
  {
    orgName: '采购部',
    type: '规则执行型组织',
    improve: '・创新能力缺失（AI 维度 31 分）',
    reinforce: '・客户体验管理（用户运营相关指标未突出）',
    advantage: '・业务规则（51 分）',
    task: '3'
  },
  {
    orgName: '制造中心领导',
    type: '规则执行型组织',
    improve: '・系统支撑不足（数字化维度 37 分）',
    reinforce: '・人员动力激活（人员动力未明确量化）',
    advantage: '・组织岗位 KPI（49 分）',
    task: '3'
  },
  {
    orgName: '顺德研发部',
    type: '碎片化组织',
    improve: '・全维度能力断点（最高维度 46 分，最低 38 分）',
    reinforce: '・流程标准化（流程端到端闭环 41 分）',
    advantage: '・数据治理（46 分）',
    task: '3'
  },
  {
    orgName: '零售与用户运营部',
    type: '碎片化组织',
    improve: '・数字化断层（数字化维度 38 分）',
    reinforce: '・组织协同优化（岗位协同 RACI 43 分）',
    advantage: '・组织维度（51 分）',
    task: '2'
  },
  {
    orgName: '工艺部',
    type: '碎片化组织',
    improve: '・流程与系统双薄弱（流程 45 分，数字化 45 分）',
    reinforce: '・AI 基础建设（AI 维度 36 分）',
    advantage: '・人岗能力匹配（45 分）',
    task: '3'
  },
  {
    orgName: '渠道运营部',
    type: '碎片化组织',
    improve: '・流程与数据双缺失（流程 43 分，数字化 45 分）',
    reinforce: '・人员能力评估（45 分）',
    advantage: '・岗位角色职责（45 分）',
    task: '2'
  },
  {
    orgName: '欧盟区产品部',
    type: '人员能力型组织',
    improve: '・流程与 AI 双薄弱（流程 47 分，AI33 分）',
    reinforce: '・数据治理深化（数字化 49 分）',
    advantage: '・组织协同（岗位协同 RACI 52 分）',
    task: '2'
  },
  {
    orgName: 'H公司冰冷 GTM 部',
    type: '系统支撑型组织',
    improve: '・流程维度（36 分）与 AI 维度（44 分）不匹配',
    reinforce: '・人岗能力校准（人员能力要求 41 分）',
    advantage: '・数字化应用（48 分）',
    task: '3'
  },
  {
    orgName: '胶州冰冷工厂',
    type: '人员能力型组织',
    improve: '・局部优化（AI 维度 43 分可提升算法应用）',
    reinforce: '・流程精细化（流程维度 38 分）',
    advantage: '・人岗与数字化双优（53/52 分）',
    task: '3'
  },
  {
    orgName: '扬州冰箱工厂',
    type: '系统支撑型组织',
    improve: '・流程与组织协同（流程 41 分，组织 40 分）',
    reinforce: '・AI 场景落地（AI 维度 40 分）',
    advantage: '・数字化基础（51 分）',
    task: '3'
  }
])

const taskCol = ref([
  {
    label: '序号',
    type: 'index'
  },
  {
    label: '举措',
    prop: 'name',
    width: '110px'
  },
  {
    label: '关键行动',
    prop: 'action'
  },
  {
    label: '建议责任人',
    prop: 'personal',
    width: '110px'
  },
  {
    label: '输出成果',
    prop: 'result',
    width: '150px'
  },
  {
    label: '优先级',
    prop: 'status',
    width: '60px'
  }
])
const taskData = ref([
  {
    name: '流程 - 人岗协同优化',
    action:
      '制定《需求计划岗位能力矩阵》明确流程操作与系统应用技能标准；开发需求计划流程模拟培训系统嵌入自动化校验规则；建立 “流程执行 - 能力评估” 双周复盘机制',
    personal: '流程管理部主管',
    result: '岗位能力手册 + 培训模拟系统',
    status: '高'
  },
  {
    name: '系统 - 流程集成建设',
    action:
      '搭建需求计划数字化中台集成 ERP/MES 数据接口；配置需求波动预警算法自动触发流程变更审批；开发需求计划版本管理模块记录变更日志与影响分析',
    personal: '系统开发部主管',
    result: '需求计划中台原型 + 算法配置文档',
    status: '高'
  },
  {
    name: '人岗 - 流程能力补位',
    action:
      '开展需求洞察专项培训引入 KANO 模型工具包；设计需求计划评审 RACI 矩阵明确跨部门签批权责；建立需求变更追溯机制关联至人员能力评估档案',
    personal: '人力资源部主管',
    result: '培训课件 + 评审流程规范',
    status: '高'
  },
  {
    name: '数据 - 规则治理强化',
    action:
      '制定《需求计划数据字典》统一业务口径与计算逻辑；搭建需求预测 BI 看板集成历史履约数据标签；建立需求合理性财务校验规则嵌入预算管控系统',
    personal: '数据治理部主管',
    result: '数据字典 + 预测分析看板',
    status: '高'
  },
  {
    name: '规则 - 创新平衡机制',
    action:
      '引入 AI 需求预测算法试点非标准件采购场景；建立 “合规性 - 灵活性” 双轨审批流程设置创新需求例外通道；开展客户需求 workshops 建立采购需求转化模型',
    personal: '采购管理部主管',
    result: '算法试点报告 + 需求转化手册',
    status: '高'
  },
  {
    name: '系统 - 组织协同升级',
    action:
      '部署需求计划智能排产系统对接车间设备物联网数据；重构岗位协同流程图明确需求传递各节点时效 KPI；开发产能负荷预警模块自动触发需求分流建议',
    personal: '智能制造总监',
    result: '排产系统部署方案 + 协同 KPI 清单',
    status: '高'
  },
  {
    name: '全维度能力筑基',
    action:
      '开展需求工程基础培训覆盖 JAD 需求收集法等工具；建立需求管理模板库（含调研报告 / 原型设计 / 评审记录）；搭建跨部门需求沟通平台设置需求澄清专属流程节点',
    personal: '研发管理部主管',
    result: '培训考核记录 + 模板库 + 沟通平台',
    status: '高'
  },
  {
    name: '数字化 - 组织协同修复',
    action:
      '接入用户行为数据中台构建需求预测特征变量体系；设计用户需求分级响应机制绑定会员等级与服务时效；开发需求反馈闭环系统自动关联至运营策略调整流程',
    personal: '用户运营总监',
    result: '数据变量清单 + 响应机制文档',
    status: '高'
  },
  {
    name: '流程 - 系统基础建设',
    action:
      '绘制需求到工艺转化流程图标注数据输入输出节点；配置工艺参数自动提取工具对接需求计划系统字段；建立工艺需求版本管理规范明确变更影响评估维度',
    personal: '工艺技术主管',
    result: '转化流程图 + 工具配置手册',
    status: '高'
  },
  {
    name: '流程 - 数据标准化建设',
    action:
      '统一渠道需求提报模板嵌入必填字段校验规则；开发渠道需求汇总 BI 工具自动生成区域需求热力图；建立渠道需求真实性验证机制关联历史销售数据维度',
    personal: '渠道管理部主管',
    result: '提报模板 + 热力分析报告',
    status: '高'
  },
  {
    name: '人岗 - 数据能力强化',
    action:
      '开展东南亚市场需求分析专项培训引入区域消费特征数据库；建立需求计划跨文化评审机制设置本地化需求权重指标；开发多语言需求管理模块支持数据实时翻译与对比',
    personal: '国际业务总监',
    result: '培训案例库 + 跨文化评审规则',
    status: '高'
  },
  {
    name: '系统 - 人岗匹配优化',
    action:
      '实施需求计划岗位胜任力建模匹配系统操作熟练度指标；开发系统使用行为分析模块自动生成能力提升建议；建立 “系统应用 - 需求准确性” 联动考核机制',
    personal: '人力资源总监',
    result: '胜任力模型 + 能力提升工单系统',
    status: '高'
  },
  {
    name: '人岗 - 数字化深化应用',
    action:
      '开展智能设备需求采集培训掌握 IoT 数据解读方法；优化需求计划系统工单逻辑自动关联设备产能数据；建立班组需求预测竞赛机制设置准确性提升奖励指标',
    personal: '工厂运营主管',
    result: '培训操作手册 + 竞赛评估方案',
    status: '高'
  },
  {
    name: '流程 - 系统场景落地',
    action:
      '设计小批量定制需求快速响应流程设置 “需求 - 生产” 直连通道；部署需求计划敏捷迭代系统支持 72 小时快速版本更新；开发生产需求模拟排产沙盒验证需求变更影响',
    personal: '工厂技术主管',
    result: '快速响应流程 SOP + 模拟排产报告',
    status: '高'
  }
])
</script>
<template>
  <div class="org-detail">
    <div class="page-title-line">
      <div class="page-title">参评组织解码—各组织DNA解码数据一览</div>
      <div class="choose-dna">制定需求计划对象</div>
    </div>
    <div class="choose-type">
      <div class="type-list" @click="changeType(item)" :class="{ active: activeType == item }" v-for="item in typeList">
        {{ item }}
      </div>
    </div>
    <el-table stripe :data="overviewData">
      <el-table-column label="序号" type="index" width="80" align="center"></el-table-column>
      <el-table-column label="组织" prop="name" align="center"></el-table-column>
      <el-table-column
        v-for="col in overviewColumns"
        align="center"
        :label="col.label"
        :prop="col.prop"
        :key="col.prop"
      >
        <template #default="{ row }">
          <div class="table-score">
            <div class="score" :style="{ background: setColor(row[col.prop]) }">{{ row[col.prop] }}</div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="page-title-line mt-11">
      <div class="page-title">各能力DNA优势组织与短板组织排名</div>
      <div class="choose-dna">制定需求计划对象</div>
    </div>
    <div class="choose-type">
      <div
        class="type-list"
        @click="activeDNAType = item"
        :class="{ active: activeDNAType == item }"
        v-for="item in typeDNAList"
      >
        {{ item }}
      </div>
    </div>
    <div class="chart-box">
      <div class="chart-list" v-for="item in chartList">
        <div class="chart-title">{{ item.title }}</div>
        <div class="chart">
          <EChartsBar type="horizontal" :options="getChartOpt(item)"></EChartsBar>
        </div>
      </div>
    </div>
    <div class="page-title-line mt-11">
      <div class="page-title">基于能力DNA的组织画像分析</div>
      <div class="choose-dna">制定需求计划对象</div>
    </div>
    <SimplenessTable :columns="DNAOrgColumns" :data="DNAOrgData"></SimplenessTable>
    <div class="page-title-line mt-11">
      <div class="page-title">各组织能力DNA管理建议</div>
      <div class="choose-dna">制定需求计划对象</div>
    </div>
    <SimplenessTable highlight-current-row :columns="orgSuggestionCol" :data="orgSuggestionData"></SimplenessTable>
    <div class="page-title-line mt-11">
      <div class="page-title">关键任务</div>
    </div>
    <SimplenessTable highlight-current-row :columns="taskCol" :data="taskData"></SimplenessTable>
  </div>
</template>
<style lang="scss" scoped>
.page-title-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .choose-dna {
    font-size: 16px;
    color: #40a0ff;
  }
}
.choose-type {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  .type-list {
    min-width: 81px;
    font-size: 14px;
    color: #6c757e;
    line-height: 25px;
    text-align: center;
    background: #ffffff;
    border-radius: 25px;
    border: 1px solid #dee6ee;
    cursor: pointer;
    padding: 0 10px;
    &:hover,
    &.active {
      color: #fff;
      background: #40a0ff;
      border-color: #40a0ff;
    }
  }
}
.table-score {
  display: flex;
  align-items: center;
  justify-content: center;
  .score {
    width: 65px;
    line-height: 24px;
    background: #6acefc;
    border-radius: 20px;
    color: #fff;
    font-size: 14px;
  }
}
.chart-box {
  display: flex;
  gap: 16px;
  .chart-list {
    flex: 1;
    font-weight: 400;
    font-size: 16px;
    color: #3d3d3d;
    line-height: 20px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 18px;
    .chart-title {
      font-weight: bold;
    }
    .chart {
      height: 300px;
    }
  }
}
</style>
