<template>
    <div class="">
         <div class="page_third_title">能力模块差距分布</div>
        <table-component
            :height="'500'"
            :tableData="tableData"
            :loading="loading"
            :needIndex="true"
            :needPagination="false"
        ></table-component>
    </div>
</template>
 
<script>
    import { getTypeSbGap } from "../../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "referModule",
        props: ["orgCode", "evalId"],
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading: false,
                tableData: {
                    columns: [
                        {
                            label: "能力模块",
                            prop: "module_name",
                        },
                         {
                            label: "参评人数",
                            prop: "num",
                            formatterFun:function(data){
                                return data['notStandard'] + data['standard']
                            }
                        },
                        {
                            label: "得分",
                            prop: "overall_score",
                        },
                         {
                            label: "达标",
                            prop: "standard",
                        },
                        {
                            label: "未达标",
                            prop: "notStandard",
                        },
                        {
                            label: "差距<10%",
                            prop: "diffOne",
                        },
                        {
                            label: "差距<20%",
                            prop: "diffTwo",
                        },
                        {
                            label: "差距<40%",
                            prop: "diffThree",
                        },
                        {
                            label: "差距>40%",
                            prop: "diffFour",
                        },
                        {
                            label: "偏离度",
                            prop: "dev_degree",
                            formatterFun:function(data){
                               return data['dev_degree']+"%"
                            }
                        },
                    ],
                    data: [
                    ],
                    page: {
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        components: {
            tableComponent,
        },
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
        },

        methods: {
            getData() {
                this.loading = true;
                let params = {
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                getTypeSbGap(params).then((res) => {
                    console.log(res);
                    if(res.code == "200"){
                       this.$set(this.tableData,'data',res.data);
                        this.loading = false;
                    } else {
                        this.loading = false;
                    }
                });
            },
            handleSizeChange(size){
               this.pageSize = size;
               this.getData();
            },
            handleCurrentChange(current){
               this.currPage = current;
               this.getData();
            }
        },
    };
</script>
 
<style scoped lang="scss">
</style>