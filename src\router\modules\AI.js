// AI诊断改善引擎
import Layout from '@/layout/index.vue'
const AI = [
  {
    path: '/AI',
    component: Layout,
    redirect: '/AI/home',
    children: [
      {
        path: '/AI/home',
        meta: {
          title: 'AI诊断改善引擎'
        },
        component: () => import('@/views/AI/home.vue')
      },
      {
        path: '/AI/diagnosis',
        meta: {
          title: '快速了解能力诊断'
        },
        component: () => import('@/views/AI/diagnosis/index.vue')
      },
      {
        path: '/AI/displayer',
        meta: {
          title: '能力显示器（能力显差）'
        },
        component: () => import('@/views/AI/displayer/index.vue'),
        redirect: '/AI/displayer/overview',
        children: [
          {
            path: '/AI/displayer/overview',
            meta: {
              title: '能力显差概览'
            },
            component: () => import('@/views/AI/displayer/overview/index.vue'),
            redirect: '/AI/displayer/overview/module',
            children: [
              {
                path: '/AI/displayer/overview/module',
                meta: {
                  title: '能力模块'
                },
                component: () => import('@/views/AI/displayer/overview/module.vue')
              },
              {
                path: '/AI/displayer/overview/componentGroup',
                meta: {
                  title: '能力组件群'
                },
                component: () => import('@/views/AI/displayer/overview/componentGroup.vue')
              },
              {
                path: '/AI/displayer/overview/component',
                meta: {
                  title: '能力组件'
                },
                component: () => import('@/views/AI/displayer/overview/component.vue')
              },
              {
                path: '/AI/displayer/overview/componentDetail',
                meta: {
                  title: '能力组件详情'
                },
                component: () => import('@/views/AI/displayer/overview/componentDetail.vue')
              }
            ]
          }
        ]
      },
      {
        path: '/AI/targetSpotDiagnosis',
        meta: {
          title: '靶点诊断仪（能力诊断）'
        },
        redirect: '/AI/targetSpotDiagnosis/project',
        component: () => import('@/views/AI/targetSpotDiagnosis/index.vue'),
        children: [
          {
            path: '/AI/targetSpotDiagnosis/project',
            meta: {
              title: '诊断项目'
            },
            component: () => import('@/views/AI/targetSpotDiagnosis/project/index.vue')
          },
          // 诊断首页
          {
            path: '/AI/targetSpotDiagnosis/project/diagnose',
            meta: {
              title: '诊断项目'
            },
            component: () => import('@/views/AI/targetSpotDiagnosis/project/diagnose/index.vue'),
            redirect: '/AI/targetSpotDiagnosis/project/diagnose/start',
            children: [
              {
                path: '/AI/targetSpotDiagnosis/project/diagnose/start',
                meta: {
                  // title: '诊断项目'
                },
                component: () => import('@/views/AI/targetSpotDiagnosis/project/diagnose/example.vue')
              },
              {
                path: '/AI/targetSpotDiagnosis/project/diagnose/model',
                meta: {
                  // title: '诊断项目'
                },
                component: () => import('@/views/AI/targetSpotDiagnosis/project/diagnose/modelList.vue')
              },
              {
                path: '/AI/targetSpotDiagnosis/project/diagnose/answer',
                meta: {
                  // title: '诊断项目'
                },
                component: () => import('@/views/AI/targetSpotDiagnosis/project/diagnose/answer.vue')
              }
            ]
          },
          {
            path: 'progress',
            meta: {
              title: '诊断进度'
            },
            component: () => import('@/views/AI/targetSpotDiagnosis/progress/index.vue')
          },
          {
            path: 'report',
            meta: {
              title: '诊断报告'
            },
            component: () => import('@/views/AI/targetSpotDiagnosis/report/index.vue')
          }
        ]
      },
      {
        path: '/AI/analysis',
        meta: {
          title: '靶点诊断仪（能力分析）'
        },
        component: () => import('@/views/AI/analysis/index.vue'),
        redirect: '/AI/analysis/decode',
        children: [
          {
            path: '/AI/analysis/decode',
            meta: {
              title: '能力解码'
            },
            component: () => import('@/views/AI/analysis/decode/index.vue')
          },
          {
            path: '/AI/analysis/decode/detail',
            meta: {
              title: '能力解码'
            },
            component: () => import('@/views/AI/analysis/decode/detail.vue')
          },
          {
            path: '/AI/analysis/probe',
            meta: {
              title: '根因探查'
            },
            component: () => import('@/views/AI/analysis/probe/index.vue')
          },
          {
            path: '/AI/analysis/probe/detail',
            meta: {
              title: '根因探查'
            },
            component: () => import('@/views/AI/analysis/probe/detail.vue')
          },
          {
            path: '/AI/analysis/affect',
            meta: {
              title: '影响分析'
            },
            component: () => import('@/views/AI/analysis/affect/index.vue')
          },
          {
            path: '/AI/analysis/affect/detail',
            meta: {
              title: '影响分析'
            },
            component: () => import('@/views/AI/analysis/affect/detail.vue')
          },
          {
            path: '/AI/analysis/improvement',
            meta: {
              title: '改善策略库'
            },
            component: () => import('@/views/AI/analysis/improvement/index.vue')
          },
          {
            path: '/AI/analysis/improvement/detail',
            meta: {
              title: '改善策略库'
            },
            component: () => import('@/views/AI/analysis/improvement/detail.vue')
          }
        ]
      }
    ]
  }
]

export default AI
