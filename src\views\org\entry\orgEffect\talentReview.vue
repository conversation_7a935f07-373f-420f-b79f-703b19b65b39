<script setup>
const router = useRouter()
const route = useRoute()
const gnjj = ref([
  {
    title: '支持战略规划：',
    info: '确保组织拥有与战略目标相匹配的人才队伍，明确未来人才需求的方向和重点，为组织的长期发展提供人力保障。'
  },
  {
    title: '优化人才结构：',
    info: ' 了解人才在年龄、学历、专业、层级等方面的分布情况，发现结构失衡问题，如某些部门人员冗余，而某些关键岗位人才短缺等，以便进行合理的人员调配和补充。 '
  },
  {
    title: '识别高潜人才：',
    info: '挖掘具有高潜力的员工，为他们提供个性化的培养和发展机会，加速他们的成长，使其成为组织未来的核心力量。'
  },
  {
    title: '提升组织绩效：',
    info: ' 通过对员工能力和绩效的评估，发现影响绩效的因素，制定针对性的培训和发展计划，提高员工个体和整体的绩效水平。'
  },
  {
    title: ' 完善人才管理体系：',
    info: '根据人才盘点的结果，审视和优化招聘、培训、薪酬、晋升等人力资源管理流程和政策，提高人才管理的科学性和有效性。'
  }
])
const rcpd = ref([
  {
    title: '人才数量盘点：',
    info: '主要关注企业中人才的数量情况，包括岗位需求数量、实际人员数量、空缺岗位数量等，以及与人才数量相关的招募、配置等方面的内容。'
  },
  {
    title: '人才结构盘点：',
    info: '对企业人才的结构进行分析，包括岗位结构、层级结构、专业结构、年龄结构、性别结构等，了解人才在不同维度上的分布和比例情况，为优化人才结构提供依据。'
  },
  {
    title: '人才质量盘点：',
    info: '评估人才的能力、素质、绩效、潜力等方面的质量水平，全面了解员工的综合能力和发展潜力，为人才选拔、培养、评价等提供依据。'
  },
  {
    title: '人才效能盘点：',
    info: '关注人才在工作中的效率和效果，包括岗位工作效率、岗位可优化性、人才协同网络的效率等，评估人才为企业创造价值的能力和效率。'
  },
  {
    title: '人才发展盘点：',
    info: '围绕员工的职业发展和成长，包括员工职业规划、培训计划、晋升与发展等内容，了解员工的发展需求和企业为员工提供的发展机会，促进员工与企业的共同发展。'
  },
  {
    title: '人才风险盘点：',
    info: '评估企业面临的人才相关风险，包括离职风险、岗位空缺风险、人才结构不合理风险等，提前采取措施降低风险，保障企业的稳定发展。'
  }
])
</script>
<template>
  <div class="talentReview_wrap">
    <div class="page-title-line">什么是人才盘点</div>
    <div class="section_box_wrap">
      通过一系列科学的方法和手段，对内部人才的数量、质量、结构、能力、绩效、潜力等方面进行全面、系统的评估和分析，以了解人才现状，发现人才优势与不足，为人才的培养、发展、晋升、调配等人力资源决策提供依据，从而实现人才与组织战略的有效匹配，提升组织整体绩效和竞争力
    </div>
    <div class="page-title-line">为什么要做人才盘点</div>
    <div class="section_box_wrap gnjj_content_wrap">
      <div class="item_wrap" v-for="item in gnjj">
        <span class="icon"></span>
        <span class="title">{{ item.title }}</span>
        <span class="info">{{ item.info }}</span>
      </div>
    </div>
    <div class="page-title-line">人才盘点的主要内容</div>
    <div class="section_box_wrap gnjj_content_wrap">
      <div class="item_wrap" v-for="item in rcpd">
        <span class="icon"></span>
        <span class="title">{{ item.title }}</span>
        <span class="info">{{ item.info }}</span>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.talentReview_wrap {
  .section_box_wrap {
    margin-bottom: 30px;
    padding: 16px 20px;
    line-height: 26px;
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
  }
  .gnjj_content_wrap {
    .item_wrap {
      margin-bottom: 12px;
      position: relative;

      .icon {
        position: absolute;
        top: 12px;
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: #000;
      }
      .title {
        margin: 0 10px 0 21px;
        color: #000;
        white-space: nowrap;
        font-weight: 600;
      }
    }
  }
}
</style>
