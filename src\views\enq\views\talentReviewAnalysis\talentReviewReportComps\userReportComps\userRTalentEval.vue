<template>
    <div class="report_section talent_eval_main">
        <div class="matrix_chart_wrap flex_row_around">
            <div class="matrix_chart three_item">
                <div class="page_second_title marginB_16 marginT_16">能力业绩矩阵</div>
                <div class="matrix_head long">
                    <div class="title">核心能力</div>
                    <div class="flex_row_start border">
                        <div
                            class="item"
                            v-for="item in capabilityKpi.x"
                            :key="item.dictCode"
                        >{{item.codeName}}</div>
                    </div>
                </div>
                <div class="clearfix">
                    <div class="matrix_aside long">
                        <div class="matrix_aside_head  flex_row_start">
                            <div class="title">绩效指标</div>
                            <div class="flex_col_start border">
                                <div
                                    class="item"
                                    v-for="item in capabilityKpi.y"
                                    :key="item.dictCode"
                                >{{item.codeName}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="matrix_main long">
                        <div
                            class="matrix_row"
                            :class="'matrix_row_'+(index+1)"
                            v-for="(item,index) in capabilityKpi.x"
                            :key="item.dictCode"
                        >
                            <div
                                class="item "
                                :class="'item_'+(listIndex+1)"
                                v-for="(list,listIndex) in capabilityKpi.y"
                                :key="list.dictCode"
                            >
                                <span
                                    v-for="(selectList,selectListIndex) in capabilityKpi.capabilityKpiSelect"
                                    :key="selectListIndex"
                                >
                                    <div
                                        class="item_mark"
                                        :class="{'current':selectList.current == 'Y'}"
                                        v-if="(selectList.x+selectList.y) == (item.dictCode+list.dictCode)"
                                    >
                                        <i class="item_icon el-icon-coordinate"></i>
                                        <span class="item_date">{{selectList.date}}</span>
                                    </div>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="matrix_chart three_item">
                <div class="page_second_title marginB_16 marginT_16">能力潜力矩阵</div>
                <div class="matrix_head">
                    <div class="title">核心能力</div>
                    <div class="flex_row_start border">
                        <div
                            class="item"
                            v-for="item in developmentKpi.x"
                            :key="item.dictCode"
                        >{{item.codeName}}</div>
                    </div>
                </div>
                <div class="clearfix">
                    <div class="matrix_aside">
                        <div class="matrix_aside_head flex_row_start">
                            <div class="title">发展潜力</div>
                            <div class="flex_col_start border">
                                <div
                                    class="item"
                                    v-for="item in developmentKpi.x"
                                    :key="item.dictCode"
                                >{{item.codeName}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="matrix_main long">
                        <div
                            class="matrix_row"
                            :class="'matrix_row_'+(index+1)"
                            v-for="(item,index) in developmentKpi.x"
                            :key="item.dictCode"
                        >
                            <div
                                class="item item_long"
                                :class="'item_'+(listIndex+1)"
                                v-for="(list,listIndex) in developmentKpi.y"
                                :key="list.dictCode"
                            >
                                <span
                                    v-for="(selectList,selectListIndex) in developmentKpi.developmentKpiSelect"
                                    :key="selectListIndex"
                                >
                                    <div
                                        class="item_mark"
                                        :class="{'current':selectList.current == 'Y'}"
                                        v-if="(selectList.x+selectList.y) == (item.dictCode+list.dictCode)"
                                    >
                                        <i class="item_icon el-icon-coordinate"></i>
                                        <span class="item_date">{{selectList.date}}</span>
                                    </div>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="page_second_title marginB_16 marginT_16">个人优势项</div>
        <div class="">{{strength}}</div>
        <div class="page_second_title marginB_16 marginT_16">待发展</div>
        <div class="">{{weakness}}</div>
    </div>
</template>
 
<script>
    import { getTalentEvaluation } from "../../../../request/api";
    export default {
        name: "userRTalentEval",
        props: ["enqId", "userId","postCode"],
        components: {},
        data() {
            return {
                capabilityRank: [],
                kpiRank: [],
                capabilityKpi: null,
                developmentKpi:null
            };
        },
        created() {
            let docList = ["KPI_RANK", "COMPETENCE_RANK"];
            this.$getDocList(docList).then((res) => {
                this.kpiRank = res.KPI_RANK;
                this.capabilityRank = res.COMPETENCE_RANK;
            });
            this.getTalentEvaluationFun();
        },
        mounted() {},
        methods: {
            getTalentEvaluationFun() {
                let params = {
                    enqId: this.enqId,
                    userId: this.userId,
                    postCode: this.postCode
                };
                getTalentEvaluation(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.capabilityKpi = res.data.capabilityKpi;
                        this.developmentKpi = res.data.developmentKpi;
                        this.weakness = res.data.weakness;
                        this.strength = res.data.strength;
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .matrix_chart_wrap {
    }
    .matrix_chart {
        width: 480px;
        /*float: left;*/
        margin-right: 6px;
        margin-bottom: 16px;

        &.three_item {
            float: left;
            margin-top: 20px;

            .matrix_head {
                line-height: 20px;
                &.long{
                    .title{
                        padding-left:100px;
                    }
                    .flex_row_start {
                        margin-left: 100px;
                    }
                }

                .title {
                    height: 20px;
                    padding-left: 40px;
                }

                .flex_row_start {
                    height: 20px;
                    margin-left: 40px;
                }
            }

            .matrix_aside {
                width: 40px;
                height: 230px;
                &.long{
                    width:100px;
                    .flex_col_start{
                        width:80px;
                    }
                }

                .title {
                    width: 20px;
                    height: calc(100% + 40px);
                    margin-top: -40px;
                }

                .flex_col_start {
                    width: 20px;
                }

                .item {
                    line-height: 76px;
                }
            }

            .matrix_main {
                .matrix_row {
                    &_1 {
                        .item_1 {
                            background-color: #e28d80;
                        }

                        .item_2 {
                            background-color: #719dd5;
                        }

                        .item_3 {
                            background-color: #a3d0f3;
                        }
                    }

                    &_2 {
                        .item_1 {
                            background-color: #719dd5;
                        }

                        .item_2,
                        .item_3 {
                            background-color: #a3d0f3;
                        }
                    }

                    &_3 {
                        .item_1,
                        .item_2 {
                            background-color: #a3d0f3;
                        }

                        .item_3 {
                            background-color: #dddee3;
                        }
                    }
                }
                &.long{
                    .item {
                        width: 125px;
                    }
                    .item_long{
                        width: 145px;
                    }
                }

                .item {
                    width: 146px;
                    height: 76px;
                }
            }
        }

        .matrix_head {
            width: 100%;
            text-align: left;
            line-height: 30px;

            .title {
                height: 30px;
                background: #fbfbfb;
                padding-left: 100px;
            }

            .flex_row_start {
                height: 30px;
                margin-left: 100px;

                &.border {
                    border-bottom: 1px solid #f6f6f6;
                }
            }

            .item {
                flex: 1;
                text-align: center;
            }
        }

        .matrix_aside {
            float: left;
            width: 100px;
            height: 230px;
            text-align: center;

            .matrix_aside_head {
                height: 100%;
            }

            .title {
                height: calc(100% + 50px);
                padding: 30px 10px 0 5px;
                width: 30px;
                background: #fbfbfb;
                margin-top: -50px;
            }

            .flex_col_start {
                height: 100%;
                width: 70px;

                &.border {
                    border-right: 1px solid #f6f6f6;
                }
            }

            .item {
                flex: 1;
                line-height: 45px;
            }
        }

        .matrix_main {
            overflow: hidden;
            display: flex;
            flex-flow: column ;
            .matrix_row {
                width: 100%;
                flex:1;
                &_1 {
                    .item_1,
                    .item_2 {
                        background-color: #e28d80;
                    }

                    .item_3 {
                        background-color: #719dd5;
                    }

                    .item_4,
                    .item_5 {
                        background-color: #bed269;
                    }
                }

                &_2 {
                    .item_1 {
                        background-color: #e28d80;
                    }

                    .item_2,
                    .item_3 {
                        background-color: #719dd5;
                    }

                    .item_4,
                    .item_5 {
                        background-color: #bed269;
                    }
                }

                &_3 {
                    .item_1,
                    .item_2 {
                        background-color: #719dd5;
                    }

                    .item_3 {
                        background-color: #a3d0f3;
                    }

                    .item_4,
                    .item_5 {
                        background-color: #bed269;
                    }
                }

                &_4 {
                    .item_1,
                    .item_2,
                    .item_3 {
                        background-color: #a3d0f3;
                    }

                    .item_4 {
                        background-color: #bed269;
                    }

                    .item_5 {
                        background-color: #dddee3;
                    }
                }

                &_5 {
                    .item_1,
                    .item_2,
                    .item_3 {
                        background-color: #a3d0f3;
                    }

                    .item_4,
                    .item_5 {
                        background-color: #dddee3;
                    }
                }
            }

            .item {
                width: 74px;
                height: 45px;
                float: left;
                margin: 0 1px 1px 0;
                padding: 2px;
                color: #fff;
                font-size: 16px;
                .item_mark {
                    white-space: nowrap;
                    color: #ebf4ff;
                    &.current{
                        color: #1299DF;
                    }
                }

                .item_icon {
                }
                .item_date {
                    position: relative;
                    font-size: 14px;
                    white-space: nowrap;
                    z-index: 9;
                }

                &.level_1 {
                    background-color: #e28d80;
                }

                &.level_2 {
                    background-color: #719dd5;
                }

                &.level_3 {
                    background-color: #bed269;
                }

                &.level_4 {
                    background-color: #a3d0f3;
                }

                &.level_5 {
                    background-color: #dddee3;
                }
            }
        }
    }
</style>