# CE-Agent 项目文档

## 技术栈

- Vue 3.5.13
- Vite 6.2.0
- Element Plus 2.9.5
- Pinia 3.0.1
- Vue Router 4.5.0
- Tailwind CSS 4.1.7

## 样式体系

项目采用双样式方案：

1. **SCSS体系**：
   - 全局变量管理 (`variables.scss`)
   - 混入函数 (`mixin.scss`)
2. **Tailwind CSS**：
   - 原子化CSS工具类
   - 通过`@tailwind`指令引入基础样式
   - 入口文件：`src/styles/tailwind.css`
   - 默认尺寸计算 --spacing: 0.25rem; // 4px
   - eg: margin-bottom: 20px; mb-[20px] (4px \* 5)

## 目录结构

````
├── src/
│   ├── api/        # 接口封装
│   ├── assets/     # 静态资源
│   ├── components/ # 公共组件
│   ├── config/     # 应用配置
│   ├── router/     # 路由配置（支持模块化自动加载）
│   ├── stores/     # Pinia状态管理（支持持久化存储）
│   ├── styles/     # 全局样式
│   │   ├── variables.scss # SCSS变量
│   │   ├── mixin.scss     # SCSS混入
│   ├── utils/      # 工具函数库
│   │   ├── dateFormat.js    # 日期格式化
│   │   ├── debounceThrottle.js # 防抖节流
│   │   ├── localStorage.js  # 本地存储封装

## 核心特性
### 路由模块化
通过`import.meta.glob`实现路由配置自动加载：
```js
const modules = import.meta.glob('./modules/*.js', { eager: true })
const autoRoutes = Object.values(modules).flatMap(module => module.default)
````

### Pinia持久化

通过`src/config/persist.js`实现状态持久化存储，支持：

- localStorage/sessionStorage
- 自定义加密策略
- 存储白名单配置

### 工具函数库

封装了以下常用功能：

- 类型判断（isType.js）
- 货币格式化（currencyFormat.js）
- 本地存储管理（localStorage.js）
- 防抖节流函数（debounceThrottle.js）

## 环境配置

| 环境文件         | 说明                 |
| ---------------- | -------------------- |
| .env.development | 开发环境（本地代理） |
| .env.test        | 测试环境             |
| .env.production  | 生产环境             |

## 开发命令

```bash
# 启动开发服务器
npm run dev

# 测试环境构建
npm run build:test

# 生产环境构建
npm run build
```

## 架构特点

1. **自动导入**：通过unplugin-auto-import实现Vue/Vue Router API自动导入
2. **样式体系**：SCSS变量集中管理，支持全局混入
3. **代理配置**：Vite反向代理配置（vite.config.js）
4. **组件自动注册**：通过unplugin-vue-components实现Element Plus组件自动注册

```js
// Element Plus自动导入配置（vite.config.js）
Components({
  resolvers: [ElementPlusResolver()],
  dts: false
})
```
