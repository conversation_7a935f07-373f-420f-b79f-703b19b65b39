<template>
    <div class="assess_progress_manage_info_wrap bg_write" v-if="showPage">
        <div class="page_main_title">测评进度管理（点评）--{{evalName}}
            <div class="goback_geader" @click="$util.goback()">
                <i class="el-icon-arrow-left"></i>返回
            </div>
        </div>
        <div class="page_section">
            <tabsChangeData
                    :tabsData="tabsData"
                    :activeName="tabsData[tabIndex].name"
                     :handleClick="changeTabs">
            </tabsChangeData>
            <div class="department_level_wrap" v-if="tabIndex == 0">
                <span class="select_title">显示部门层级</span>
                <el-select v-model="departmentLevel" placeholder="请选择">
                    <el-option
                            v-for="item in departmentLevelList"
                            :key="item.layerNo"
                            :label="item.layerName"
                            :value="item.layerNo">
                    </el-option>
                </el-select>
            </div>
            <tableComponent v-if="tableData.data.length > 0" :tableData="tableData" :needIndex="true" @handleSizeChange='handleSizeChange'  @handleCurrentChange='handleCurrentChange'>
            </tableComponent>
        </div>
    </div>
</template>

<script>
    import tabsChangeData from "@/components/talent/tabsComps/tabsChangeData";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import {getOrgLevel,reviewOverallProgress,notStartReviewObjPage,reviewSchedule,userReturn,userInvalid,getEvalInfo} from "../../request/api"

    export default {
        name: "assessProgressManageInfo",
        components: {
            tabsChangeData,
            tableComponent
        },
        data() {
            return {
                showPage:true,
                evalId: this.$route.query.evalId,
                evalName:'',
                evalStatus:null,
                tabsData: [
                    {
                        label: "部门整体进度",
                        name: "department"
                    },
                    {
                        label: "已完成人员",
                        name: "completed"
                    },
                    {
                        label: "未完成人员",
                        name: "incomplete"
                    },
                    {
                        label: "未开始人员",
                        name: "notLogin"
                    }
                ],
                tabIndex:0,
                departmentLevel: "1",
                departmentLevelList: [],
                showDepartmentLevel: true,
                tableData: {
                    columns:[],
                    data:[],
                    page:{
                        total: 0,
                        current: 1,
                        size: 10
                    }
                },
                departmentData: {
                    columns: [
                        {
                            label: "部门名称",
                            prop: "org_name",
                        },
                        {
                            label: "部门负责人",
                            prop: "org_leader_name",
                            className: "align_center"
                        },
                        {
                            label: "应点评人数",
                            prop: "reviewsRequired",
                            className: "align_center"
                        },
                        {
                            label: "已点评人数",
                            prop: "reviewed",
                            className: "align_center completed"
                        },
                        {
                            label: "未点评人数",
                            prop: "notReviewed",
                            className: "align_center incomplete"
                        },
                        // {
                        //     label: "未登陆",
                        //     prop: "unStartCount",
                        //     className: "align_center not_login"
                        // }
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10
                    }
                },
                completedData: {
                    columns: [
                        {
                            label: "部门名称",
                            prop: "orgName",
                            // width: 200
                        },
                        {
                            label: "姓名",
                            prop: "userName",
                            // width: 100,
                            className: "align_center"
                        },
                        {
                            label: "岗位",
                            prop: "postName",
                            // width: 100,
                            className: "align_center"
                        },

                        {
                            label: "完成时间",
                            prop: "reviewTime",
                            // width: 180,
                            className: "align_center"
                        },
                        // {
                        //     label: "用时",
                        //     prop: "elapsedTime",
                        //     // width: 120,
                        //     className: "align_center"
                        // }
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10
                    }
                },
                incompleteData: {
                    columns: [
                        {
                            label: "部门名称",
                            prop: "orgName"
                        },
                        {
                            label: "姓名",
                            prop: "userName",
                            // width: 70,
                            className: "align_center"
                        },
                        {
                            label: "岗位",
                            prop: "postName",
                            // width: 120,
                            className: "align_center"
                        },

                        {
                            label: "需点评",
                            prop: "reviewsRequired",
                            // width: 70,
                            className: "align_center incomplete"
                        },
                        {
                            label: "已点评",
                            prop: "reviewed",
                            // width: 70,
                            className: "align_center completed"
                        },
                        {
                            label: "未点评",
                            prop: "notReviewed",
                            // width: 70,
                            className: "align_center not_login"
                        },
                        // {
                        //     label: "用时",
                        //     prop: "elapsedTime",
                        //     // width: 120,
                        //     className: "align_center"
                        // },
                        // {
                        //     label: "最后登录时间",
                        //     prop: "lastLoginTime",
                        //     // width: 120,
                        //     className: "align_center last_date"
                        // }
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10
                    }
                },
                notLoginData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "orgName"
                        },
                        {
                            label: "姓名",
                            prop: "userName",
                            // width: 70,
                            className: "align_center"
                        },
                        {
                            label: "岗位",
                            prop: "postName",
                            // width: 120,
                            className: "align_center"
                        },

                        {
                            label: "部门负责人",
                            prop: "orgLeaderName",
                            // width: 120,
                            className: "align_center"
                        }
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10
                    }
                }
            };
        },
        watch:{
            departmentLevel(){
                this.tableData.page.current = 1;
                this.reviewOverallProgressFun()
            }
        },
        created() {
            //获取测评状态
            getEvalInfo({
                evalId:this.evalId
            }).then(res=>{
                console.log(res);
                this.evalStatus = res.evalStatus;
                this.evalName = res.evalName;
            })
            this.getOrgLevelFun();

        },
        methods: {
            changeTabs(tab, event) {
                this.tabIndex = tab.index;
                let key =tab.name;
                console.log(this.tabIndex);
                this.tableData = this[key+"Data"];
                if(this.tableData.data.length == 0){
                     if(this.tabIndex == 0){
                        this.reviewOverallProgressFun();
                    }else{
                        this.reviewScheduleFun(this.tabIndex)
                    }

                    // if(this.tabIndex == '0'){
                    //     console.log('整体');
                    //     this.reviewOverallProgressFun();
                    // }else if(this.tabIndex == 1 || this.tabIndex == 2){
                    //     console.log('已完成&未完成');
                    //     this.reviewScheduleFun(this.tabIndex)
                    // }else{
                    //     this.notStartReviewFun();
                    //     console.log('未开始');
                    // }
                }
            },
            //切换页容量
            handleSizeChange(val){
                this.tableData.page.current =1;
                if(this.tabIndex == 0){
                    this.departmentData.page.size = val;
                    this.reviewOverallProgressFun();
                }else if(this.tabIndex == 1){
                    this.completedData.page.size = val
                    this.reviewScheduleFun(this.tabIndex)
                }else if(this.tabIndex == 2){
                    this.incompleteData.page.size = val
                    this.reviewScheduleFun(this.tabIndex)
                }else if(this.tabIndex == 3){
                    this.notLoginData.page.size = val
                    this.reviewScheduleFun(this.tabIndex)
                }
            },
            //分页
            handleCurrentChange(val){
                if(this.tabIndex == 0){
                    this.departmentData.page.current = val
                    this.reviewOverallProgressFun();
                }else if(this.tabIndex == 1){
                    this.completedData.page.current = val
                    this.reviewScheduleFun(this.tabIndex)
                }else if(this.tabIndex == 2){
                    this.incompleteData.page.current = val
                    this.reviewScheduleFun(this.tabIndex)
                }else if(this.tabIndex == 3){
                    this.notLoginData.page.current = val
                    this.reviewScheduleFun(this.tabIndex)
                }
            },
            getOrgLevelFun(){
                getOrgLevel({
                    evalId:this.evalId
                }).then(res=>{
                    console.log(res)
                    if(res.code ==200){
                        this.departmentLevelList = res.data;
                        this.departmentLevel=res.data[0].layerNo;
                        this.reviewOverallProgressFun()
                    }else{
                        this.$msg.warning(res.msg)
                    }

                })
            },
            reviewOverallProgressFun(){
                reviewOverallProgress({
                    evalId:this.evalId,
                    layerNo:this.departmentLevel,
                    current:this.tableData.page.current,
                    size:this.tableData.page.size,
                }).then(res=>{
                    console.log(res);
                    if(res.code ==200){
                        this.departmentData.data = res.data;
                        this.departmentData.page = res.page;
                        this.tableData = this.departmentData;
                    }else{
                        this.$msg.warning(res.msg)
                    }
                })
            },
            reviewScheduleFun(flag){
                reviewSchedule({
                    evalId:this.evalId,
                    reviewStatus:flag,
                    current:this.tableData.page.current,
                    size:this.tableData.page.size,
                }).then(res=>{
                    console.log(res);
                    if(res.code ==200){
                        if(flag == 1){
                            this.completedData.data = res.data;
                            this.completedData.page = res.page;
                            this.tableData = this.completedData;
                        }
                        if(flag == 2){
                            this.incompleteData.data = res.data;
                            this.incompleteData.page = res.page;
                            this.tableData = this.incompleteData;
                        }
                        if(flag == 3){
                            this.notLoginData.data = res.data;
                            this.notLoginData.page = res.page;
                            this.tableData = this.notLoginData;
                        }
                    }else{
                        this.$msg.warning(res.msg)
                    }
                })
            },
            // 未完成人员
            // notStartReviewFun(){
            //     notStartReviewObjPage({
            //         evalId:this.evalId,
            //         current:this.tableData.page.current,
            //         size:this.tableData.page.size,
            //     }).then(res => {
            //         console.log(res);
            //         this.notLoginData.data = res.data;
            //                 this.notLoginData.page = res.page;
            //                 this.tableData = this.notLoginData;

            //     })
            // }
        }
    };
</script>

<style scoped lang="scss">
    .assess_progress_manage_info_wrap {
        .department_level_wrap {
            margin: 10px 0 20px 0;

            .select_title {
                display: inline-block;
                width: 120px;
            }

             .el-input__inner {
                height: 35px;
                line-height: 35px;
            }

             .el-input__suffix {
                display: flex;
                align-items: center;
            }
        }

         .el-table {
            margin: 10px 0 0 0;

            .has-gutter {
                tr th {
                    background: #f2f8ff;
                }
            }

            .align_center {
                text-align: center;
                /*font-weight: bold;*/

                &.completed {
                    color: #00b050;
                }

                &.incomplete {
                    color: #ffc000;
                }

                &.not_login {
                    color: #00b0f0;
                }

                &.status {

                }
            }
        }

         .el-table__body .last_date {
            font-size: 12px;
        }

        .completion_rate {
            .bar_wrap {
                width: calc(100% - 40px);
                height: 18px;
                background: #EBF4FF;
                position: relative;
                padding-top: 5px;

                .bar_progress {
                    background: #00b050;
                    height: 8px;
                    width: 50%;

                    &.bg_high {
                        background: #00b050;
                    }

                    &.bg_middle {
                        background: #00b0f0;
                    }

                    &.bg_normal {
                        background: #ffc000;
                    }

                    &.bg_low {
                        background: #ff8181;
                    }
                }
            }

            .completion_rate_num {
                font-weight: bold;

                &.not_login {
                    color: #ff6d6d;
                }

                &.color_high {
                    color: #00b050;
                }

                &.color_middle {
                    color: #00b0f0;
                }

                &.color_normal {
                    color: #ffc000;
                }

                &.color_low {
                    color: #ff8181;
                }
            }
        }
    }
</style>