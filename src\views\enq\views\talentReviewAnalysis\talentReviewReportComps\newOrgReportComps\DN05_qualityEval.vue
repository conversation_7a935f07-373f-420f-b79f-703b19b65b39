<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <div class="page_second_title">素质评价</div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        getQualityEval,
        getQualityEvalOverallScore,
        getQualityEvalWhole,
        getQualityEvalClass,
        getQualityEvalUserScore,
        getQualityEvalUserWhole,
        getQualityEvalUserComment
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import listComp from "./components/listComp.vue";
    export default {
        name: "orgRQualityEval",
        props: {
            enqId: String,
            orgCode: String,
            isPdf: {
                type: Boolean,
                default: false,
            },
        },
        components: { tableComps, listComp },
        data() {
            return {
                chartDom: [
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "素质评价得分",
                        elSpan: 6,
                        chartHeight: "200",
                        chartType: "YBar",
                        dataKey: "grade",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "素质评价分级",
                        elSpan: 6,
                        chartHeight: "200",
                        chartType: "YBar",
                        dataKey: "grade",
                    },
                    {
                        chartDomId:this.$util.createRandomId(),
                        title: "人员综合素质明细维度",
                        elSpan: 12,
                        chartType: "XBar",
                        dataKey: "dimension",
                    },
                ],
                listArr: [
                    {
                        title: "本组织及所有下级组织综合得分",
                        ajaxUrl: getQualityEvalOverallScore,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                        ],
                    },
                    {
                        title: "本组织及所有下级组织360°评价",
                        ajaxUrl: getQualityEvalWhole,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                        ],
                    },
                    {
                        title: "本组织及所有下级组织分级情况",
                        ajaxUrl: getQualityEvalClass,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                        ],
                    },
                    {
                        title: "人员素质评价得分与评级",
                        ajaxUrl: getQualityEvalUserScore,
                        isAsyncColumns: true,
                        columns: [
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                        ],
                    },
                    {
                        title: "人员素质评价360°得分",
                        ajaxUrl: getQualityEvalUserWhole,
                        isAsyncColumns: true,
                        afterColumns: [
                            {
                                label: "最终评价等级",
                                prop: "userName111",
                            },
                        ],
                        columns: [
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                        ],
                    },
                    {
                        title: "人员素质评价评语",
                        ajaxUrl: getQualityEvalUserComment,
                        isAsyncColumns: false,
                        columns: [
                            {
                                label: "姓名",
                                prop: "userName",
                                width:200
                            },
                            {
                                label: "评语来源",
                                prop: "relationType",
                                width:200
                            },
                            {
                                label: "评语说明",
                                prop: "comment",
                            },
                        ],
                    },
                ],
            };
        },
        created() {
            this.getData();
        },
        mounted() {},
        methods: {
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getQualityEval(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.initChart(res.data);
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>