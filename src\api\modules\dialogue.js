import service from '../request'

// 新建会员
export const sessionAdd = data =>
  service({
    url: '/agent/session/add',
    method: 'post',
    data
  })

// 对话
export const converse = data =>
  service({
    url: '/agent/converse',
    method: 'post',
    data
  })

// 历史对话
export const conversion = data =>
  service({
    url: '/agent/conversion',
    method: 'get',
    params: data
  })

// 历史列表
export const sessionList = data =>
  service({
    url: '/agent/session/list',
    method: 'get',
    params: data
  })
/*-----------------排产智能体begin--------------------- */

// 智能排产
export const uploadFile = data =>
  service({
    headers: { 'Content-Type': 'multipart/form-data' },
    url: '/haier/workflowAps/files/upload',
    method: 'post',
    data
  })

// 智能排产 -下载模板
export const exportExample = data =>
  service({
    url: '/haier/workflowAps/download',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob',
    method: 'post',
    params: data
  })

// 智能排产 -历史对话
export const scheduleConversion = data =>
  service({
    url: '/haier/workflowAps/historicalDetail',
    method: 'get',
    params: data
  })

// 智能排产 -导出计划表
export const exportSchedule = data =>
  service({
    url: '/haier/workflowAps/exportSchedule',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob',
    method: 'post',
    params: data
  })

// 智能排产 -导出库存表
export const exportInventory = data =>
  service({
    url: '/haier/workflowAps/exportStock',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob',
    method: 'post',
    params: data
  })

/*-----------------排产智能体end--------------------- */

/*-----------------考勤智能体begin--------------------- */

// 智能问数 - 历史对话
export const questionConversion = data =>
  service({
    url: '/workflowQuestion/historicalDetail',
    method: 'get',
    params: data
  })

/*-----------------考勤智能体end--------------------- */
