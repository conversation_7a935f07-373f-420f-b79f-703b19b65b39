<template>
  <div class="post_process_main">
    <div class="departmen_main">
      <div class="survey_wrap marginT_16">
        <el-row class="header flex_row_start">
          <el-col :span="1" class="item index">序号</el-col>
          <el-col :span="12" class="item name">调研项目</el-col>
          <el-col :span="2" class="item" v-for="(col, i) in columns" :key="i">{{ col.name }}</el-col>
          <el-col :span="1" class="item oper"></el-col>
        </el-row>
        <div class="main_content">
          <div
            class="main_row"
            :class="['row_' + index, { warning_row: setStyleArr.includes(index) }]"
            v-for="(row, index) in tableData"
            :key="index"
          >
            <el-row class="row flex_row_start">
              <el-col :span="1" class="item index">{{ index + 1 }}</el-col>
              <el-col :span="12" class="item name">{{ row.name }}</el-col>
              <template v-if="row.type == 'S'">
                <el-col :span="2" class="item" v-for="(col, i) in columns" :key="i">
                  <div
                    class="check_item"
                    @click="checkClick(row, col)"
                    :class="{ active: row.selectedCodes.includes(col.code) }"
                  >
                    <el-icon class="icon_check"><Check /></el-icon>
                  </div>
                </el-col>
              </template>
              <template v-if="row.type == 'F'">
                <el-col :span="10" class="item item_input">
                  <el-input
                    type="textarea"
                    v-model="row.answer"
                    :autosize="{ minRows: 3, maxRows: 5 }"
                    placeholder="请输入内容"
                  />
                </el-col>
              </template>
              <el-col :span="1" class="item oper"></el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
    <div class="oper_btn_wrap align_center" v-show="dataIsDone">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn" v-show="currentIndex != currentFirstCode"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import { getTopicList, saveWorkSurveySubmit } from '../../../request/api'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  currentIndex: String,
  currentFirstCode: String
})

const emit = defineEmits(['nextStep', 'prevStep'])

const userStore = useUserStore()

// 响应式状态
const examples = ref('')
const columnKey = ref([])
const resData = ref([])
const tableData = ref([])
const columns = ref([])
const setStyleArr = ref([])
const dataIsDone = ref(false)
const modelId = ref('')

// 计算属性
const userId = computed(() => userStore.userInfo.userId)

// 方法
const getEnqUserSurveyByIdFun = async () => {
  try {
    const res = await getTopicList({
      enqId: props.enqId,
      modelType: 'satisfy'
    })
    if (res.code == 200) {
      tableData.value = res.data.itemList
      columns.value = res.data.resultList
      modelId.value = res.data.modelId
      dataIsDone.value = true
    } else {
      ElMessage.error('获取数据失败!')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败!')
  }
}

const saveEnqUserSurvey = async stepType => {
  try {
    const selectArr = tableData.value.map(item => ({
      itemId: item.code,
      answer: item.answer,
      moduleCode: item.reasonCode,
      optionNbr: item.selectedCodes
    }))

    const res = await saveWorkSurveySubmit({
      modelId: modelId.value,
      enqId: props.enqId,
      enqItemOptionRequests: selectArr
    })

    if (res.code == 200) {
      ElMessage.success('保存成功!')
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('保存失败!')
  }
}

const checkClick = (row, col) => {
  row.selectedCodes = [col.code]
}

const prevBtn = async () => {
  try {
    await ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '保存',
      cancelButtonText: '放弃修改'
    })
    await saveEnqUserSurvey('prevStep')
  } catch (action) {
    ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
    if (action == 'cancel') {
      emit('prevStep')
    }
  }
}

const nextBtn = () => {
  setStyleArr.value = checkData(tableData.value)
  if (setStyleArr.value.length > 0) {
    ElMessage.error('请完善数据后提交！')
    const element = document.getElementsByClassName('row_' + setStyleArr.value[0])
    element[0].scrollIntoView({ block: 'center' })
    return
  }
  saveEnqUserSurvey('nextStep')
}

const checkData = data => {
  const arr = data
  const len = arr.length
  const resultArr = []
  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    if (obj.type == 'S' && (!obj.selectedCodes || obj.selectedCodes.length == 0)) {
      resultArr.push(index)
    }
    if (obj.type == 'F' && !obj.answer) {
      resultArr.push(index)
    }
  }
  return resultArr
}

// 生命周期钩子
onMounted(() => {
  getEnqUserSurveyByIdFun()
})
</script>

<style scoped lang="scss">
.oper_btn_wrap {
  text-align: center;
}

.warning_row {
  background-color: #fef0f0 !important;
}

.post_column {
  margin-right: 4px;
  text-align: center;
}

.post_ipt {
  display: inline-block;
  width: 30px;
  height: 30px;
  border: 1px solid #e5e5e5;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  color: #000;
  margin-right: 3px;
}

.survey_wrap {
  .header {
    background: #f5f7fa;
    padding: 12px 0;
    border-radius: 4px;

    .item {
      text-align: center;

      &.index {
        text-align: center;
      }

      &.name {
        text-align: left;
        padding-left: 16px;
      }
    }
  }

  .main_content {
    .main_row {
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      &:last-child {
        border-bottom: none;
      }

      .row {
        .item {
          text-align: center;

          &.index {
            text-align: center;
          }

          &.name {
            text-align: left;
            padding-left: 16px;
          }

          &.item_input {
            padding: 0 16px;
          }

          .check_item {
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
            }

            &.active {
              background-color: #409eff;
              border-color: #409eff;

              .icon_check {
                display: inline-block;
                color: #fff;
              }
            }

            .icon_check {
              display: none;
              font-size: 16px;
              line-height: 22px;
            }
          }
        }
      }
    }
  }
}
</style>
