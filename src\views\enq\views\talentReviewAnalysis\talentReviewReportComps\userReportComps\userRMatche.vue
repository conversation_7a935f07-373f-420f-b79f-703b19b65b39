<template>
	<div class="report_section userR_matche_wrap">
		<div class="userR_matche_main">
			<div class="bottom_wrap">
				<div class="page_second_title ">
					<span>任职匹配-个人综合素质匹配</span>
				</div>
				<div class="reault_expression_item">
	        		<tableComponent :tableData="tableData" :border="true" :needPagination="false" :needIndex="false"></tableComponent>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	// 任职匹配
	// import{} from "../../../../request/api"
    import tableComponent from "@/components/talent/tableComps/tableComponent";
	export default {
		name: "userRMatche",
		props: ["userRMatche", "enqId", "userId", "postCode"],
		components: {
			tableComponent
		},
		data() {
			return {
				tableData:{
					columns: [
                        {
                            label: "",
                            prop: "name",
                            width: "400",
                        },
						{
							label: "",
                            prop: "info",
                            width: "",
						},
						{
							label: "本岗位要求",
                            prop: "claim",
                            width: "",
						},
						{
							label: "是否达标",
                            prop: "standards",
                            width: "",
						},
						
					],
					data: [
						{
							name:'学历',
							info:'本科',
							claim:'本科',
							standards:'是'
						}
					],
				},
			};
		},
		created() {
			
		},
		computed:{
			
		},
		mounted(){
		},
		methods: {

		}
	};
</script>

<style scoped lang="scss">
.userR_matche_wrap {
	padding: 0 10px;
	height: 480px;
	overflow:auto ;	
	pointer-events: auto;
	.userR_matche_main{
		.bottom_wrap{
			.page_second_title{
				margin: 15px 0;
			}
		}
	}
}
</style>
