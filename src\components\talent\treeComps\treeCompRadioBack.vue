<template>
  <div class="tree_wrap noselect">
    <el-tree
      ref="treeForm"
      show-checkbox
      :check-strictly="true"
      :data="treeData"
      :node-key="nodeKey"
      :default-expand-all="defaultExpandAll"
      :default-expanded-keys="expandedKeys"
      :default-checked-keys="defaultCheckedKeyArr"
      :expand-on-click-node="false"
      :props="{ label: labelKey }"
      @node-click="nodeClickCallback"
    >
      <template #default="{ node }">
        <span class="custom-tree-node" :class="{ disabled: node.disabled }">
          <span class="tree_node" :title="node.label">
            {{ node.label }}
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'

const props = defineProps({
  treeData: { type: Array, default: () => [] },
  defaultCheckedKeys: { type: Array, default: () => [] },
  labelKey: { type: String, default: 'value' },
  nodeKey: { type: String, default: 'code' },
  needCheckedFirstNode: { type: Boolean, default: true },
  defaultExpandedKeys: { type: Array, default: () => [] },
  defaultExpandAll: { type: Boolean, default: true },
  expandedLevel: { type: Number, default: 3 },
  canCancel: { type: Boolean, default: false }
})
const emit = defineEmits(['clickCallback'])
const treeForm = ref(null)
const checkedId = ref('')
const lastNode = ref(false)
const checkedData = ref('')
const defaultCheckedKeyArr = ref([])
const expandedKeys = ref([...props.defaultExpandedKeys])

watch(
  () => props.treeData,
  val => {
    if (val.length == 0) return
    setCheckedNode(props.defaultCheckedKeys, val)
    setExpandKeys()
  }
)

watch(
  () => props.defaultCheckedKeys,
  val => {
    if (val.length > 0) {
      setCheckedNode(val, props.treeData)
    }
  },
  { deep: true, immediate: true }
)

function setCheckedNode(propsDefaultCheckedKeys, treeData) {
  treeForm.value && treeForm.value.setCheckedKeys([])
  if (props.needCheckedFirstNode) {
    if (propsDefaultCheckedKeys.length > 0) {
      defaultCheckedKeyArr.value = [propsDefaultCheckedKeys[0]]
      checkedId.value = propsDefaultCheckedKeys[0]
      emit('clickCallback', checkedId.value, false, null)
    } else if (treeData.length > 0) {
      let code = treeData[0][props.nodeKey]
      defaultCheckedKeyArr.value = [code]
      checkedId.value = code
      emit('clickCallback', code, false, treeData[0])
    }
  } else if (propsDefaultCheckedKeys[0]) {
    defaultCheckedKeyArr.value = [propsDefaultCheckedKeys[0]]
    checkedId.value = propsDefaultCheckedKeys[0]
    emit('clickCallback', checkedId.value, false, null)
  }
}

async function nodeClickCallback(data, node) {
  if (data.disabled) return
  console.log(data, node)
  console.log('checked', node.checked)

  const checked = node.checked
  let nodeId = node.data[props.nodeKey]
  if (checked) {
    console.log(1)
    if (checkedId.value == nodeId && props.canCancel) {
      console.log(2)

      treeForm.value.setCheckedKeys([])
      checkedId.value = ''
      checkedData.value = ''
      await nextTick()
      emit('clickCallback', checkedId.value, lastNode.value, data)
    }
  } else {
    console.log(3)

    treeForm.value.setCheckedKeys([])
    checkedId.value = nodeId
    lastNode.value = !(data.children && data.children.length > 0)
    treeForm.value.setCheckedKeys([data[props.nodeKey]])
    checkedData.value = node
    emit('clickCallback', checkedId.value, lastNode.value, data)
  }
}

function setExpandKeys() {
  if (props.defaultExpandAll) return
  expandedKeys.value = []
  function getKeys(data) {
    data.forEach(item => {
      if (item.layerNo < props.expandedLevel + 1) {
        expandedKeys.value.push(item.code)
      }
      if (item.children && item.children.length) {
        getKeys(item.children)
      }
    })
  }
  getKeys(props.treeData)
}
</script>

<style scoped lang="scss">
.tree_wrap {
  width: 100%;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  &.disabled {
    color: #b3b3b3;
    cursor: not-allowed;
  }
}

.el-tree-node__content > label.el-checkbox {
  pointer-events: none;
}

// 超出显示省略号 鼠标移入展示
.custom-tree-node {
  width: 75%;
}

.tree_node {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
:deep(.el-checkbox) {
  pointer-events: none;
}
:deep(.el-checkbox__original) {
  pointer-events: none;
}
:deep(.el-checkbox__inner) {
  border-radius: 50%;
}
:deep(.el-checkbox__inner::after) {
  // display: none;
}
</style>
