<template>
    <div class="model_mateInfo_final_wrap">
        <div class="model_mateInfo_final_top flex_row_betweens">
            <div class="top_left_wrap page_section">
                <p class="page_second_title">一级能力目标</p>
                <div id="chartWrap1">

                </div>
            </div>
            <div class="top_right_wrap page_section">
                <p class="page_second_title">二级能力目标</p>
                <div id="chartWrap2">


                </div>
            </div>
        </div>
        <div class="model_mateInfo_final_bottom page_section">
            <p class="page_second_title">各岗位能力目标</p>
            <div id="chartWrap3">

            </div>
        </div>
        <div class="align_center marginT_20" v-if="type !='show'">
            <el-button class="page_confirm_btn" type="primary" @click="prev()">上一步</el-button>
            <el-button class="page_confirm_btn" type="primary" @click="affirm()">确认</el-button>
        </div>
    </div>
</template>

<script>
    // import {echartsRenderPage} from "../../../../../../public/js/echartsimg/echartsToImg"
    import {getConfirmFinalModel,confirmModelBuild,getTwolevelTarget} from "../../../request/api"

    export default {
        name: "modelMateInfoFinal",
        props: ["modelId", "buildId","type"],
        data() {
            return {
                stairTargetData: {
                    data: []
                },
                secondLevelTargetData: {
                    data: []
                },
                abilityTargetData: {
                    data: []
                },

            };
        },
        created() {
        },
        mounted() {
            this.getConfirmFinalModelFun();
        },
        methods: {
            prev() {
                this.$emit("prevStep")
            },
            affirm() {
                this.confirmModelBuildFun();
            },
             getConfirmFinalModelFun(){
                 getConfirmFinalModel({
                    buildId:this.buildId,
                    modelId:this.modelId
                }).then(res=>{
                    // console.log(res)
                    if(res.code ==200){
                        res.data.firstlevelTarget.forEach(item => {
                            item.value = parseFloat(item.value)
                        });

                        res.data.twolevelTarget.forEach(item => {
                            item.value = parseFloat(item.value)
                        });

                        this.stairTargetData.data=res.data.firstlevelTarget;
                        this.stairTargetData.addEvent=true;
                        this.stairTargetData.defaultChecked=true;
                        this.secondLevelTargetData.data=res.data.twolevelTarget;
                        this.abilityTargetData.data=res.data.postTarget;
                        echartsRenderPage("chartWrap1", "XBar", 500, 230, this.stairTargetData,(item)=>{
                            // console.log(item);
                            let dataIndex = item.dataIndex
                            let params={
                                buildId:this.buildId,
                                modelId:this.modelId,
                                moduleCode:this.stairTargetData.data[dataIndex].code
                            }
                            this.getTwolevelTargetFun(params)

                        });
                        echartsRenderPage("chartWrap2", "XBar", 500, 230, this.secondLevelTargetData);
                        echartsRenderPage("chartWrap3", "XBar", 1050, 230, this.abilityTargetData);
                    }else{
                        this.$msg.warning(res.msg)
                    }
                })
            },
            getTwolevelTargetFun(params){
                getTwolevelTarget(params).then(res=>{
                    // console.log(res)
                    if(res.code ==200){
                        this.secondLevelTargetData.data=res.data.twolevelTarget;
                        echartsRenderPage("chartWrap2", "XBar", 500, 230, this.secondLevelTargetData);
                    }
                })
            },
            confirmModelBuildFun(){
                confirmModelBuild({
                    buildId:this.buildId
                }).then(res=>{
                    if(res.code ==200){
                        this.$msg.success(res.msg)
                        this.$util.goback()
                    }
                })
            },

        }
    };
</script>

<style scoped lang="scss">
    .model_mateInfo_final_wrap {
        .model_mateInfo_final_title {
            padding: 3px 8px;
            font-size: 16px;
            line-height: 28px;
            color: #0099fd;
            font-weight: bold;
            background-color: #f2f8ff;
            border-radius: 3px;
        }

        .model_mateInfo_final_top {
            .top_left_wrap {
                // width:431px;
                width: 50%;
            }

            .top_right_wrap {
                // width:431px;
                width: 50%;
            }
        }

        .model_mateInfo_final_bottom {

        }
    }

</style>