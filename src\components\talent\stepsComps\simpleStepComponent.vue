<template>
  <div class="simple_step_main">
    <div
      class="step_item"
      :class="{ completed: index + 1 <= completedStep }"
      v-for="(step, index) in stepsData"
      :key="step.name"
    >
      <div class="step_text">{{ step.name }}</div>
      <div class="step_item_icon_wrap">
        <i class="step_item_icon"></i>
      </div>
      <div class="step_item_children"></div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  stepsData: {
    type: Array,
    default: () => [
      {
        name: '',
        state: 'completed'
      }
    ]
  },
  completedStep: {
    type: Number,
    default: 1
  }
})

// Example data - moved to a separate constant if needed
const egData = [
  {
    name: '参与盘点',
    state: 'completed'
  },
  {
    name: '查看进度',
    state: 'incomplete'
  },
  {
    name: '查看报告',
    state: 'incomplete'
  }
]
</script>

<style scoped lang="scss">
.simple_step_main {
  width: 100%;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  // padding-bottom: 16px;
  .step_item {
    flex: 1;
    position: relative;
    color: #909399;
    text-align: center;
    z-index: 2;
    font-weight: bold;
    &::after {
      content: '';
      position: absolute;
      width: calc(50% - 5px);
      height: 2px;
      background-color: #ccc;
      top: 29px;
      right: 0;
      z-index: 1;
    }
    &::before {
      content: '';
      position: absolute;
      width: calc(50% - 5px);
      height: 2px;
      background-color: #ccc;
      top: 29px;
      left: 0;
      z-index: 1;
    }
    &.completed {
      color: #0099ff;
      &::after {
        background-color: #0099ff;
      }
      &::before {
        background-color: #0099ff;
      }
      .step_item_icon {
        color: #0099ff;
        border-color: #0099ff;
        background-color: #0099ff;
      }
    }
    .step_text {
      /*margin-bottom: 11px;*/
    }
    .step_item_icon {
      display: inline-block;
      width: 10px;
      height: 10px;
      font-size: 17px;
      color: #909399;
      text-align: center;
      border-radius: 50%;
      font-style: normal;
      border: 1.5px solid #909399;
      // margin-bottom: 11px;
      background-color: #f5faff;
      // cursor: pointer;
    }
  }
}
</style>
