<template>
  <div class="edu_info_wrap performance_info_main">
    <div class="clearfix">
      <div class="page_second_title marginT_16">
        <span>参与过的培训</span>
      </div>
      <div class="align_right">
        <el-button class="page_add_btn" type="primary" @click="addItem">新增</el-button>
      </div>
      <div class="edu_info_center marginT_16">
        <div class="edu_info_header">
          <div class="item">培训名称</div>
          <div class="item">培训日期</div>
          <div class="item">课程类型</div>
          <div class="item">培训方式</div>
          <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
          <PRtrainInfoList :trainData="trainData" v-on:deleteItem="deleteItem"></PRtrainInfoList>
          <div class="align_center marginT_30">
            <el-button
              class="page_confirm_btn"
              type="primary"
              @click="prevStep"
              v-show="currentIndex != currentFirstCode"
              >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="submit('nextStep')">{{ nextBtnText }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { getEnqUserTraining, updateEnqUserTraining, delEnqUserTraining } from '../../../request/api'
import PRtrainInfoList from './PRtrainInfoList.vue'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])
const userStore = useUserStore()

const submitFlag = ref(true)
const trainData = ref([])

const userId = computed(() => userStore.userInfo.userId)

onMounted(() => {
  getEnqUserTrainingFun()
})

function deleteItem(item, index) {
  if (!Object.prototype.hasOwnProperty.call(item, 'trainingId')) {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      type: 'warning'
    })
      .then(() => {
        trainData.value.splice(index, 1)
        ElMessage.success('删除成功!')
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  } else {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        delEnqUserTraining({
          trainingId: item.trainingId,
          enqId: props.enqId
        }).then(res => {
          if (res.code == '200') {
            ElMessage.success('删除成功!')
            trainData.value.splice(index, 1)
          } else {
            ElMessage.error('删除失败！')
          }
        })
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  }
}

function addItem() {
  let obj = trainData.value[trainData.value.length - 1]
  let addObj = {
    enqId: props.enqId,
    trainingCourse: '',
    trainingDate: '',
    trainingName: '',
    trainingType: '',
    userId: userId.value
  }
  if (!obj) {
    trainData.value.push(addObj)
    return
  }
  if (checkData(trainData.value)) {
    ElMessage.warning('请完善当前数据后新增！')
    return
  }
  trainData.value.push(addObj)
}

function submit(stepType) {
  if (!submitFlag.value) return
  if (checkData(trainData.value)) {
    ElMessage.warning('请完善数据后提交！')
    return
  }
  submitFlag.value = false
  let params = trainData.value
  updateEnqUserTraining(params)
    .then(res => {
      if (res.code == '200') {
        ElMessage.success('保存成功!')
        getEnqUserTrainingFun()
        submitFlag.value = true
        emit(stepType)
      } else {
        submitFlag.value = true
        ElMessage.error('保存失败!')
      }
    })
    .catch(() => {
      submitFlag.value = true
    })
}

function getEnqUserTrainingFun() {
  getEnqUserTraining({
    enqId: props.enqId
  }).then(res => {
    if (res.code == '200') {
      trainData.value = res.data
    } else {
      ElMessage.error('获取数据失败!')
    }
  })
}

function checkData(data) {
  for (let index = 0; index < data.length; index++) {
    const obj = data[index]
    if (!obj.trainingCourse || !obj.trainingDate || !obj.trainingName || !obj.trainingType) {
      return true
    }
  }
  return false
}

function prevStep() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submit('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}
</script>

<style scoped lang="scss">
.performance_info_main {
  /*width: 60%;*/
}

.edu_info_wrap {
  margin-bottom: 16px;
}

.edu_info_header {
  .item {
    // flex: 1;
    width: 25%;
  }

  .item_icon_wrap {
    text-align: center;
    width: 10%;
  }
}
</style>
