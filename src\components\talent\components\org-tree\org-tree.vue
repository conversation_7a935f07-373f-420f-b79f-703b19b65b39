<template>
  <div class="org-tree-container">
    <div class="org-tree" :class="{ horizontal, collapsable }">
      <OrgTreeNode
        :data="data"
        :props="props"
        :horizontal="horizontal"
        :label-width="labelWidth"
        :collapsable="collapsable"
        :render-content="renderContent"
        :label-class-name="labelClassName"
        :selected-class-name="selectedClassName"
        :selected-key="selectedKey"
        @on-expand="onExpand"
        @on-node-focus="onNodeFocus"
        @on-node-click="onNodeClick"
        @on-node-mouseover="onNodeMouseover"
        @on-node-mouseout="onNodeMouseout"
      />
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import OrgTreeNode from './node'

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  props: {
    type: Object,
    default: () => ({
      label: 'label',
      expand: 'expand',
      children: 'children'
    })
  },
  horizontal: Bo<PERSON>an,
  selectedKey: String,
  collapsable: Bo<PERSON>an,
  renderContent: Function,
  labelWidth: [String, Number],
  labelClassName: [Function, String],
  selectedClassName: [Function, String]
})

const emit = defineEmits(['on-expand', 'on-node-focus', 'on-node-click', 'on-node-mouseover', 'on-node-mouseout'])

function onExpand(e, data) {
  emit('on-expand', e, data)
}
function onNodeFocus(e, data) {
  emit('on-node-focus', e, data)
}
function onNodeClick(e, data) {
  emit('on-node-click', e, data)
}
function onNodeMouseover(e, data) {
  emit('on-node-mouseover', e, data)
}
function onNodeMouseout(e, data) {
  emit('on-node-mouseout', e, data)
}
</script>

<style lang="scss">
@import './org-tree.scss';
</style>
