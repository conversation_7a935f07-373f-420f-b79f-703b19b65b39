<script setup>
defineOptions({ name: 'analyse' })
const route = useRoute()
console.log(route)

const stepList = ref([
  {
    id: 'decode',
    title: '能力解码',
    path: '/AI/analysis/decode'
  },
  {
    id: 'probe',
    title: '根因探查',
    path: '/AI/analysis/probe'
  },
  {
    id: 'affect',
    title: '影响分析',
    path: '/AI/analysis/affect'
  },
  {
    id: 'improvement',
    title: '改善策略库',
    path: '/AI/analysis/improvement'
  }
])
const router = useRouter()
const changeStep = (step, index) => {
  router.push(step.path)
}
</script>
<template>
  <div class="page-content">
    <div class="content-aside">
      <div class="aside-title">能力显差类型:</div>
      <div
        class="step-list"
        :class="{ active: route.path.indexOf(step.path) != -1 }"
        v-for="(step, index) in stepList"
        :key="step.id"
        @click="changeStep(step, index)"
      >
        {{ step.title }}
      </div>
    </div>
    <div class="content-mian">
      <router-view></router-view>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.page-content {
  display: flex;
  align-items: flex-start;
  .content-aside {
    flex: 0 0 200px;
    padding: 24px 20px;
    background: linear-gradient(224deg, #d0e4f9 0%, rgba(195, 230, 255, 0.6) 100%);
    border-radius: 8px 8px 8px 8px;
    margin-right: 16px;
    .aside-title {
      font-size: 14px;
      color: #333333;
      line-height: 35px;
    }
    .step-list {
      width: 100%;
      line-height: 33px;
      background: #f0f9ff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #a5c1dc;
      text-align: center;
      cursor: pointer;
      margin-bottom: 10px;

      &.active {
        background: #83c1ff;
        border: 1px solid #83c1ff;
        color: #ffffff;
      }
    }
  }
  .content-mian {
    flex: 1;
  }
}
:deep(.next-btn) {
  width: 269px;
  line-height: 45px;
  font-size: 16px;
  color: #ffffff;
  text-align: center;
  background: #53a9f9;
  border-radius: 8px 8px 8px 8px;
  margin: 0 auto;
  cursor: pointer;
}
</style>
