<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title">人才编制/一级部门</div>
          <div class="content_item_content" id="talent_org"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            详情列表
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
      <!-- <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">二级部门</div>
                    <div class="content_item_content">
                      
                    </div>
                </div>
            </div>-->
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { establish, establishList, exportData } from '../../../../../request/api.js'
import asideFilterBar from '../../asideFilterBar.vue'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'

const route = useRoute()
const enqId = ref(route.query.enqId)
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref({})

const chartData = reactive({
  legend: [
    {
      legendName: '现有',
      legendKey: 'actualCount'
    },
    {
      legendName: '编制',
      legendKey: 'budgetedCount'
    }
  ],
  data: []
})

const page = ref(1)
const size = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '所属组织',
      prop: 'org_name'
    },
    {
      label: '职位编码',
      prop: 'job_code'
    },
    {
      label: '职位名称',
      prop: 'job_name'
    },
    {
      label: '职位族群',
      prop: 'parent_job_class_name'
    },
    {
      label: '职位序列',
      prop: 'job_class_name'
    },
    {
      label: '职层',
      prop: 'job_level_name'
    },
    {
      label: '职等',
      prop: 'job_grade_name'
    },
    {
      label: '现有',
      prop: 'userCount'
    },
    {
      label: '编制',
      prop: 'budgetedCount'
    },
    {
      label: '差距',
      prop: 'disparity'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('talent_org', 'XBar', '700', '220', chartData)
}

const establishFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await establish(params)
    if (res.code == 200) {
      chartData.data = res.data
      initChart()
    }
  } catch (error) {
    console.error('获取编制数据失败:', error)
  }
}

const getCode = (orgCode, jobClassCode) => {
  jobClassCode.value = jobClassCode
  orgCode.value = orgCode
  page.value = 1
  establishFun()
  getTableData()
}

const handleCurrentChange = page => {
  page.value = page
  getTableData()
}

const handleSizeChange = size => {
  size.value = size
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await establishList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error('获取表格数据失败:', error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'f'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '人才编制详情列表')
  } catch (error) {
    console.error('导出数据失败:', error)
  }
}

onMounted(() => {
  establishFun()
  filterData.value = route.attrs.filterData
  getTableData()
})
</script>

<style scoped lang="scss"></style>
