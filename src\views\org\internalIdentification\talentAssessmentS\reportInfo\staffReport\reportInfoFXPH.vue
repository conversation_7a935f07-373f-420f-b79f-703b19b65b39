<script setup>
import Table from "@/components/table/simplenessTable.vue";
//
const columns7 = ref([
  {
    label: "二级能力",
    prop: "a",
    width: 260,
  },
  {
    label: "高风险",
    prop: "b",
  },
  {
    label: "中风险",
    prop: "c",
  },
  {
    label: "低风险",
    prop: "d",
  },
]);
const data7 = ref([
  {
    a: "市场分析与战略规划",
    b: "15人",
    c: "15人",
    d: "15人",
    e: "15人",
  },
  {
    a: "战略解码与目标分解",
    b: "15人",
    c: "15人",
    d: "15人",
    e: "15人",
  },
]);

const columns8 = ref([
  {
    label: "风险类型",
    prop: "f",
  },
  {
    label: "三级能力",
    prop: "a",
  },
  {
    label: "能力得分",
    prop: "c",
  },
  {
    label: "风险偏好对能力的影响",
    prop: "d",
    width: 360,
  },
]);
const data8 = ref([
  {
    a: "经验依赖型",
    b: "12",
    c: "36",
    d: "以过往成功或失败案例为决策核心依据，通过类比历史情境解决当下问题",
    e: "快速复用历史验证的成熟方案，在核心业务领域凭借经验积累精准判断关键成功因子，提升战略落地效率。",
    f: "高风险",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);

const columns12 = ref([
  // {
  //   label: "",
  //   prop: "d",

  // },
  {
    label: "改进方向",
    prop: "a",
    width: 150,
  },
  {
    label: "核心措施",
    prop: "b",
  },
  {
    label: "时间安排",
    prop: "c",
    width: 150,
  },
]);
const data12 = ref([
  {
    a: "方法升级",
    b: "1. 构建 “数据源交叉验证 + 逻辑校验规则 + 专家经验校准” 三层校验体系，引入外部权威数据源交叉比对，制定校验规则，每月邀请专家评审数据质量；2. 采用 “目标导向 - 场景细分 - 动态迭代” 流程优化方法，明确核心数据指标，按数据类型和获取难度细分场景设计收集方案，每季度动态调整；3. 按 “风险等级 - 业务场景 - 数据颗粒度” 维度进行数据分类标注，建立标签库和检索索引，每月更新分类目录",
    c: "3个月内",
    d: "",
  },
]);

onMounted(() => {});
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">风险偏好分析</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        通过人才测评中参评人员的选项数据，构建风险偏好分析模型，精准识别其在不同能力场景下决策可能面临的风险。基于分析结果，针对性制定能力提升方案，有效降低错误决策或非最优决策对业务运作的影响，保障业务高效推进。
      </div>
    </div>
    <div class="info_section_wrap three_seven_wrap justify-between">
      <div class="l_wrap">
        <div class="page-title-line">整体风险偏好占比</div>
        <div class="chart_box"></div>
      </div>
      <div class="r_wrap">
        <div class="page-title-line">不同能力下的风险偏好分布</div>
        <Table
          :roundBorder="false"
          :columns="columns7"
          :data="data7"
          headerColor
          showIndex
        >
        </Table>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">各项风险偏好分析（市场分析与战略规划）</div>
      <Table :roundBorder="false" :columns="columns8" :data="data8" headerColor>
      </Table>
    </div>
    <!-- <div class="info_section_wrap">
      <div class="page-title-line">决策模式改善建议</div>
      <Table :roundBorder="false" :columns="columns9" :data="data9" headerColor>
      </Table>
    </div> -->

    <div class="info_section_wrap">
      <div class="page-title-line">
        各项风险偏好的改善建议（高风险-市场数据收集与整合）
      </div>
      <Table
        :roundBorder="false"
        :columns="columns12"
        :data="data12"
        headerColor
      >
      </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
