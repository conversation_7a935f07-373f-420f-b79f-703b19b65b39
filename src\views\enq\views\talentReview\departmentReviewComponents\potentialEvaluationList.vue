<template>
  <div class="edu_info_wrap">
    <div class="clearfix">
      <div style="color: #0099ff; font-weight: 600">
        上级需要根据系统给出的各项维度分数，对下级人员进行等级评价，评价标准分为5档：S杰出（通常占10%），A一贯超出预期（30%），B符合预期（30%），C需要提高（20%），D不合格（10%）
      </div>
      <div class="btn_wrap align_right" style="margin-bottom: 18px">
        <el-button class="page_add_btn" type="primary" @click="exportDownloadFun">下载详细数据</el-button>
      </div>
      <div class="edu_info_center">
        <el-table :data="eduInfoData" style="width: 100%">
          <el-table-column type="index" label="序号" width="70" align="center" fixed="left"></el-table-column>
          <el-table-column prop="userName" label="姓名" width="150" align="center" fixed="left"></el-table-column>
          <el-table-column
            prop="potentialEvaluationScore"
            label="综合得分"
            width="150"
            align="center"
            fixed="left"
          ></el-table-column>
          <el-table-column prop="actualPotentialScore" label="实际得分" width="150" align="center" fixed="left">
            <template #default="scope">
              <el-input class="item" v-model="scope.row.actualPotentialScore" />
            </template>
          </el-table-column>
          <el-table-column prop="actualPotentialGrade" label="实际等级" width="150" align="center">
            <template #default="scope">
              <el-select class="item" v-model="scope.row.actualPotentialGrade" placeholder="请选择">
                <el-option
                  v-for="(item, index) in qualificationOptions"
                  :label="item.codeName"
                  :value="item.dictCode"
                  :key="item.dictCode"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="potentialComments" label="潜力评价上级评语" width="300" align="center">
            <template #default="scope">
              <el-input class="item" v-model="scope.row.potentialComments" placeholder="请输入"></el-input>
            </template>
          </el-table-column>
        </el-table>
        <div class="edu_info_mmain">
          <div class="align_center marginT_30">
            <el-button class="page_confirm_btn" type="primary" @click="submit">确定</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { potentialList, potentialLevelScore, exportQualityEvalList } from '../../../request/api.js'
import { useUserStore } from '@/stores/modules/user'
const userStore = useUserStore()
const props = defineProps({
  enqId: [String, Number]
})
const emit = defineEmits(['getPotentialData'])

const qualificationOptions = ref([])
const eduInfoData = ref([])

function submit() {
  if (checkData(eduInfoData.value)) {
    ElMessage({
      message: '请完善数据后提交！',
      type: 'warning'
    })
    return
  } else {
    emit('getPotentialData', eduInfoData.value)
  }
}

function getEducationData() {
  potentialList({
    enqId: props.enqId
  }).then(res => {
    if (res.code == '200') {
      eduInfoData.value = res.data
    } else {
      ElMessage.error('获取数据失败!')
    }
  })
}

function exportDownloadFun() {
  let params = {
    enqId: props.enqId,
    type: 'potential'
  }
  exportQualityEvalList(params).then(res => {
    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '部门潜力评价列表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  })
}

function getPotentialLevelScore() {
  potentialLevelScore({
    enqId: props.enqId
  }).then(res => {
    if (res.code == '200') {
      getEducationData()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function checkData(data) {
  let arr = data
  let len = arr.length
  for (let index = 0; index < len; index++) {
    const obj = arr[index]
    if (!obj.actualPotentialScore || !obj.actualPotentialGrade || !obj.potentialComments) {
      return true
    }
  }
}

onMounted(() => {
  // 假设有全局方法 $getDocList
  userStore.getDocList(['ACTUAL_GRADE']).then(res => {
    qualificationOptions.value = res.ACTUAL_GRADE
  })
  getPotentialLevelScore()
  getEducationData()
})
</script>

<style scoped lang="scss">
.edu_info_wrap {
  margin-bottom: 16px;
}
.edu_info_header {
  .item {
    width: 13%;
    // padding-left: 15px;
  }

  .item_icon_wrap {
    text-align: center;
    width: 12%;
  }
}
</style>
