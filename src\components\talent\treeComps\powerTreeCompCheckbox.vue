<template>
  <div class="power_tree_wrap">
    <el-tree
      ref="treeCheckbox"
      :data="treeData"
      show-checkbox
      node-key="menuCode"
      default-expand-all
      check-strictly
      :default-checked-keys="defaultCheckedKeys"
      :expand-on-click-node="false"
      :props="{ label: 'menuName' }"
      @node-click="nodeClickCallback"
    >
      <template v-slot="{ node, data }">
        <span class="custom-tree-node">
          <span class="tree_node" :title="node.label"> {{ node.label }} </span>
          <span class="checkbox_group_wrap" @click.stop>
            <el-checkbox
              v-for="item in data.menuActions"
              :label="item.val"
              :key="item.code"
              :checked="item.checked"
              :disabled="item.disabled"
              @click.stop.native="menuActionCheck($event, item.type, data.menuCode, item)"
            >
              {{ item.val }}
            </el-checkbox>
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script>
let id = 1000
export default {
  name: 'powerTreeCompCheckbox',
  props: {
    treeData: {
      type: Array,
      default: function () {
        return []
      }
    },
    // 节点唯一key，点击节点需要返回的字段
    nodeKey: {
      type: String,
      default: 'code'
    },
    // 节点显示文本的字段
    labelKey: {
      type: String,
      default: 'value'
    },
    checkedAll: {
      type: Boolean,
      default: false
    },
    defaultCheckedKeys: {
      type: Array,
      default: function () {
        return []
      }
    }
    // disabledCheckSign:Boolean,
  },
  data() {
    return {
      maxexpandId: 1000,
      checkedId: [],
      checkedData: '',
      checkedCodes: [],
      checkActionCode: [],
      // curCheckNode:[],
      checkSign: ''
    }
  },
  created() {},
  mounted() {
    console.log(this.treeData)
    this.$nextTick(() => {
      if (this.checkedAll) {
        this.$refs.treeCheckbox.setCheckedNodes(this.treeData)
      }
    })
  },
  watch: {
    defaultCheckedKeys: function (val, oVal) {
      this.$refs.treeCheckbox.setCheckedNodes(val)
    },
    treeData: {
      handler(val) {
        // console.log(val)
      },
      deep: true
    }
  },
  methods: {
    setCheckedData() {},
    getCheckedKeys() {
      this.checkedCodes = this.$refs.treeCheckbox.getCheckedKeys()
      this.$emit('node-click-callback', this.checkedCodes, this.checkSign)
    },

    nodeClickCallback(data, node, self) {
      // console.log(data)
      // console.log(node)
      // console.log(self)
      // console.log('节点选中状态',node.checked)
      if (data.disabled) {
        // 禁止勾选
        this.getCheckedKeys()
        return
      }
      let checked = node.checked
      node.checked = !checked
      if (!checked) {
        //勾选
        this.checkSign = true
      } else {
        //取消勾选
        this.checkSign = false
      }
      this.setChildNodesChecked(node, checked)
      this.setParentNodesChecked(node, checked)
      this.getCheckedKeys()
    },
    addNode() {},
    remove() {},
    setChildNodesChecked(node, checked) {
      if (node.childNodes.length > 0) {
        node.childNodes.forEach(item => {
          item.checked = !checked
          this.setChildNodesChecked(item, checked)
        })
      } else {
        return
      }
    },
    // 子节点选中 父节点同时选中
    // 子节点取消选中 父节不被取消选中
    // 最后一个子节点取消选中 父节点取消选中
    setParentNodesChecked(node, checked) {
      // console.log(checked)
      if (node.parent != null) {
        if (checked == false) {
          node.parent.checked = true
        } else {
          node.parent.checked = false
          node.parent.childNodes.forEach(item => {
            if (item.checked == true) {
              // 还有被勾选的子节点
              node.parent.checked = true
              return
            }
          })
        }
        this.setParentNodesChecked(node.parent, checked)
      }
    },
    // 操作权限check
    setNodesMenuActionCheck(node, menuCode, menuActionCode) {
      if (node.length == 0) {
        return
      }
      node.forEach(item => {
        if (item.menuCode == menuCode) {
          if (item.menuActions.length > 0) {
            item.menuActions.forEach(item1 => {
              if (item1.code == menuActionCode) {
                if (item1.checked == true) {
                  item1.checked = false
                } else if (item1.checked == false) {
                  item1.checked = true
                }
                return
              }
            })
          }
        }
        this.setNodesMenuActionCheck(item.children, menuCode, menuActionCode)
      })
    },
    menuActionCheck(e, menuActionType, menuCode, menuAction) {
      if (e.target.tagName == 'INPUT') {
        return
      }
      if (menuAction.disabled) {
        //禁止勾选
        this.$emit('menuActionCheck', menuActionType, menuCode, menuAction.code, menuAction.checked)
        return
      }
      this.setNodesMenuActionCheck(this.treeData, menuCode, menuAction.code)
      this.$emit('menuActionCheck', menuActionType, menuCode, menuAction.code, menuAction.checked)
    }
  }
}
</script>

<style scoped lang="scss">
.power_tree_wrap {
  .el-tree {
    .el-tree-node__children {
      overflow: visible !important;
    }
    .el-tree-node {
      width: 200px;
    }
    .el-tree-node__content:hover {
      background-color: transparent;
    }
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  padding-right: 8px;
}
.el-tree-node__content > label.el-checkbox {
  pointer-events: none;
}

.custom-tree-node {
  width: 100%;
  .el-checkbox__inner:hover {
    border: 1px solid #dcdfe6;
  }
}
.tree_node {
  width: 120px;
  min-width: 120px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.checkbox_group_wrap {
  width: 600px;
}
.is-disabled {
  .el-checkbox__inner {
    //     background: #fff;
    cursor: pointer;
  }
  .el-checkbox__label {
    flex: 1;
    color: #606266 !important;
    cursor: pointer !important;
  }
}
</style>
