<template>
  <div class="riskView all">
    <div class="tab-card">
      <el-segmented v-model="type" :options="options" />
    </div>
    <div class="active" v-if="type == '列表'">
      <div class="page-title-line">风险预警列表</div>
      <el-table :data="table1" border :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="60" />
        <el-table-column prop="orgName" label="组织" width="180" />
        <el-table-column label="风险总数">
          <el-table-column prop="total" align="center" label="总数" />
          <el-table-column prop="attentionLevel" align="center" label="关注级" />
          <el-table-column prop="warnLevel" align="center" label="警示级" />
          <el-table-column prop="seriousLevel" align="center" label="严重级" />
        </el-table-column>
        <el-table-column label="本月新增">
          <el-table-column prop="addAttentionLevel" align="center" label="关注级" />
          <el-table-column prop="addWarnLevel" align="center" label="警示级" />
          <el-table-column prop="addSeriousLevel" align="center" label="严重级" />
        </el-table-column>
        <el-table-column prop="handelProgress" align="center" label="风险处理进度" />
        <el-table-column width="200">
          <template #default="scope">
            <div class="btn" style="margin-right: 5px">详情</div>
            <div
              class="btn"
              @click="openAi(`${scope.row.orgName}、风险总数${scope.row.total}、风险处理进度${handelProgress}`)"
            >
              AI报告
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="page-title-line">风险预警详情</div>
      <el-table :data="table2" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="60" />
        <el-table-column prop="key1" label="指标" width="160" />
        <el-table-column prop="key2" label="预警等级" />
        <el-table-column prop="key3" label="触发规制（阈值规则）" width="180" />
        <el-table-column prop="key4" label="触发规制（动态基线）" width="180" />
        <el-table-column prop="key5" label="是否触发" />
        <el-table-column prop="key6" label="风险传导模拟" width="350" />
        <el-table-column prop="key7" align="center" label="改善任务" width="150" />
        <el-table-column prop="key8" align="center" label="状态" width="80" />
      </el-table>
    </div>
    <div class="charts active" v-if="type == '统计图'">
      <div class="page-title-line">风险预警统计</div>
      <div class="chart-main">
        <div class="item" v-for="item in echartsList">
          <div class="title">{{ item.title }}</div>
          <EChartsBar type="horizontal" :stacked="true" :options="getOptions2(item)" />
        </div>
      </div>
      <div class="page-title-line">风险预警详情</div>
      <div class="chart-main">
        <div class="item" v-for="item in echartsList">
          <div class="title">{{ item.title }}</div>
          <EChartsBar type="horizontal" :stacked="true" :options="getOptions2(item)" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import { trendRisk } from '@/assets/data/data9.js'
const openAi = inject('openAi')

defineOptions({ name: 'RiskView' })
//#region echarts
const echartsList = ref([
  {
    title: '风险等级',
    color: ['#85E5FF', '#41AAF4', '#8FDB6C', '#847AFC'],
    legend: [
      {
        name: '关注级',
        code: 'A'
      },
      {
        name: '警示级',
        code: 'B'
      },
      {
        name: '严重级',
        code: 'C'
      }
    ],
    data: [
      {
        name: '需求计划部',
        A: '2',
        B: '2',
        C: '1'
      },
      {
        name: '生产计划部',
        A: '1',
        B: '2',
        C: '2'
      },
      {
        name: '采购计划部',
        A: '5',
        B: '3',
        C: '1'
      },
      {
        name: '物流计划部',
        A: '4',
        B: '2',
        C: '0'
      }
    ]
  },
  {
    title: '风险趋势',
    color: ['#85E5FF', '#41AAF4', '#8FDB6C', '#847AFC'],
    legend: [
      {
        name: '关注级',
        code: 'A'
      },
      {
        name: '警示级',
        code: 'B'
      },
      {
        name: '严重级',
        code: 'C'
      }
    ],
    data: [
      {
        name: '2025-02',
        A: '2',
        B: '2',
        C: '1'
      },
      {
        name: '2025-03',
        A: '1',
        B: '2',
        C: '2'
      },
      {
        name: '2025-04',
        A: '5',
        B: '3',
        C: '1'
      },
      {
        name: '2025-05',
        A: '4',
        B: '2',
        C: '0'
      }
    ]
  },
  {
    title: '风险改善任务',
    color: ['#85E5FF', '#41AAF4', '#8FDB6C', '#847AFC'],
    legend: [
      {
        name: '未开始',
        code: 'A'
      },
      {
        name: '进行中',
        code: 'B'
      },
      {
        name: '已完成',
        code: 'C'
      },
      {
        name: '已逾期',
        code: 'D'
      }
    ],
    data: [
      {
        name: '需求计划部',
        A: '2',
        B: '2',
        C: '1',
        D: '1'
      },
      {
        name: '生产计划部',
        A: '1',
        B: '2',
        C: '2',
        D: '2'
      },
      {
        name: '采购计划部',
        A: '5',
        B: '3',
        C: '1',
        D: '2'
      },
      {
        name: '物流计划部',
        A: '4',
        B: '2',
        C: '0',
        D: '2'
      }
    ]
  }
])
const getData = (item, data) => {
  let arr = []

  data.map(i => {
    arr.push(i[item.code])
  })
  return arr.reverse() // 反转数组，确保数据顺序与xAxisData对应
}
const getOptions2 = item => {
  let legend = item.legend
  return {
    xAxisData: item.data.map(list => list.name).reverse(),
    xAxis: {
      show: false
    },
    legend: {
      data: legend.map(item => item.name)
    }, // 自定义 lege
    grid: {
      left: '3%',
      right: '4%',
      bottom: 0,
      containLabel: true
    },
    color: item.color,
    series: legend.map((list, index) => {
      return {
        name: list.name,
        type: 'bar',
        stack: '总量',
        data: getData(list, item.data)
      }
    })
  }
}
//#endregion

const type = ref('列表')
const options = ['列表', '统计图']
const table1 = ref(trendRisk.viewList)
const table2 = [
  {
    key1: '库存周转天数',
    key2: '关注级',
    key3: '偏离基准 10%-15%',
    key4: '对比同期下降 10%',
    key5: '动态基线触发',
    key6: '库存周转天数↑15% → 仓储成本占比↑2% → 现金流周转率↓0.5 次 → 紧急采购频次↑3 次 / 月 → 客户订单交付准时率↓5%',
    key7: '2 项',
    key8: '处理中'
  },
  {
    key1: '替代供应商储备率',
    key2: '警示级',
    key3: '偏离基准 15%-25%',
    key4: '对比同期下降 20%',
    key5: '阈值规则触发',
    key6: '库存周转天数↑22% → 呆滞库存金额↑300 万 → 资金占用成本↑80 万 / 年 → 采购计划准确率↓12% → 供应商交货延迟率↑8% → 客户投诉率↑10%',
    key7: '3 项',
    key8: '已关闭'
  },
  {
    key1: '物流成本占比',
    key2: '严重级',
    key3: '偏离 > 25%',
    key4: '对比同期下降 30%',
    key5: '阈值规则触发',
    key6: '库存周转天数↑35% → 仓储爆仓率↑40% → 现金流断裂风险↑橙色 → 生产停线频次↑2 次 / 周 → 核心客户流失率↑15% → 银行授信额度下调 20%',
    key7: '3 项',
    key8: '已关闭'
  }
]
</script>
<style lang="scss" scoped>
@import './trendRisk.scss';
.tab-card {
  @include flex-center(row, right, right);
  height: 36px;
  :deep(.el-segmented) {
    height: 100%;
    font-size: 16px;
    font-weight: 500;
    --el-segmented-item-selected-color: #fff;
    --el-border-radius-base: 18px;
    --el-segmented-bg-color: #d8ebff;
    --el-segmented-color: #76b2ed;
    --el-segmented-item-hover-color: #76b2ed;
    --el-segmented-item-hover-bg-color: transparent;
    --el-segmented-item-active-bg-color: transparent;
    .el-segmented__item {
      padding: 0 34px;
    }
  }
}
.charts {
  .chart-main {
    display: flex;
    justify-content: space-between;
    .item {
      width: 440px;
      height: 265px;
      background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), rgba(255, 255, 255, 0.5);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      padding: 20px;
      .title {
        font-size: 14px;
        color: #333333;
        font-weight: 600;
      }
    }
  }
}
</style>
