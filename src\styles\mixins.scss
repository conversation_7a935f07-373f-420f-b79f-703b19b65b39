// 移动端优先媒体查询混合宏
// @param {'phone'|'tablet'|'desktop'|'wide'} $breakpoint - 媒体查询断点类型
// @example scss
//   @include respond-to(tablet) {
//     padding: 20px;
//   }
// @browser-compatibility - 支持所有现代浏览器及IE10+
@mixin respond-to($breakpoint) {
  @if $breakpoint == phone {
    @media (max-width: 767px) {
      @content;
    }
  } @else if $breakpoint == tablet {
    @media (min-width: 768px) and (max-width: 1279px) {
      @content;
    }
  } @else if $breakpoint == desktop {
    @media (min-width: 1280px) {
      @content;
    }
  } @else if $breakpoint == wide {
    @media (min-width: 1920px) {
      @content;
    }
  }
}

// 基于1920px设计稿的视口单位转换混合宏
// @param {array} $properties - 需要转换的CSS属性数组（如：(width, height)）
// @param {number} $px-values - 设计稿中的像素值
// @example scss
//   @include vw-base((width, height), 400);
//   /* 生成：
//      width: 20.83333vw;
//      height: 20.83333vw; */
// @note - 计算公式：(像素值 / 1920) * 100vw
@mixin vw-base($properties, $px-values) {
  @each $property in $properties {
    #{$property}: ($px-values / 1920) * 100vw;
  }
}

// 快速flex居中布局混合宏
// @param {'row'|'column'} [$direction=row] - 主轴方向
// @param {'flex-start'|'flex-end'|'center'|'space-between'|'space-around'} [$justify=center] - 主轴对齐方式
// @param {'flex-start'|'flex-end'|'center'|'stretch'|'baseline'} [$align=center] - 交叉轴对齐方式
// @example scss
//   @include flex-center(column, space-between, flex-start);
// @browser-compatibility - 需要前缀兼容IE10+
@mixin flex-center($direction: row, $justify: center, $align: center) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
}

// 文本溢出处理混合宏
// @param {number} [$line=1] - 显示行数（1为单行省略）
// @example scss
//   @include text-ellipsis(2);
// @browser-compatibility - 多行省略需-webkit-box支持（Chrome 25+, Safari 6.1+）
@mixin text-ellipsis($line: 1) {
  @if $line == 1 {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  } @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line;
    overflow: hidden;
  }
}

// 清除浮动混合宏
// @example scss
//   .container {
//     @include clearfix;
//   }
// @browser-compatibility - 支持IE8+（需触发hasLayout）
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 通用容器样式混合宏
// @param {number} [$radius=4px] - 边框圆角半径
// @param {string} [$shadow=0 2px 12px 0 rgba(0,0,0,0.1)] - 盒子阴影样式
// @example scss
//   @include box-style(8px, 0 4px 16px rgba(0,0,0,0.2));
// @note - 默认使用element-plus主题变量$--border-color-light
@mixin box-style($radius: 4px, $shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)) {
  border-radius: $radius;
  box-shadow: $shadow;
  background: $--color-white;
  border: 1px solid $--border-color-light;
}
