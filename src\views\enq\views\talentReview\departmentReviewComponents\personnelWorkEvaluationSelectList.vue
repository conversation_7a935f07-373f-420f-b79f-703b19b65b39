<template>
  <div>
    <div class="edu_info_item" v-for="item in workData" :key="item.id">
      <el-input class="item" v-model="item.workingContent" placeholder="填写工作内容" disabled></el-input>
      <div class="item">{{ item.workingFreq }}</div>
      <el-input class="item" v-model="item.workingCount" placeholder="填写工作次数" disabled></el-input>
      <el-input class="item" v-model="item.workingDuration" placeholder="填写单次工作时长" disabled></el-input>
      <div class="item item_divs">
        <el-input class="item_div" v-model="item.durationEstimation" placeholder="填写月度工作时长" disabled></el-input>
        <el-input class="item_div" v-model="item.proportion" placeholder="填写月度工作占比" disabled>
          <template #append>%</template>
        </el-input>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  workData: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped lang="scss">
.edu_info_item {
  .el-input-group__append {
    padding: 0 5px;
  }
  .item {
    // flex: 1;
    width: 25%;
    text-align: center;
  }
  .item_divs {
    display: flex;
    justify-content: space-between;
    .item_div {
      width: 45%;
    }
  }
  .el-radio-group {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
  .el-radio {
    margin: 0;
  }
  .el-radio__label {
    padding: 0;
  }
  .el-input.is-disabled .el-input__inner {
    background-color: #fff;
    color: #666;
    border-color: #ccc;
  }
  .item_icon_wrap {
    text-align: center;
    width: 10%;
    padding-top: 2px;
  }
}
</style>
