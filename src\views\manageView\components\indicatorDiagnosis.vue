<script setup>
defineOptions({ name: 'indicatorDiagnosis' })
</script>
<template>
  <div class="view-detail-wrap">
    <div class="page-title-line">下载模板</div>
    <div class="i-diag">
      <img class="icon" src="@/assets/imgs/zb-moban.png" alt="" srcset="" />
      <div class="i-diag-content">
        <div>
          <div class="title">战略指标诊断模板</div>
          <div class="text">下载模板提交战略指标数据，由AI进行诊断</div>
        </div>
        <div class="btn-wrap">
          <div class="btn">下载模板</div>
          <div class="btn">上传</div>
        </div>
      </div>
    </div>
    <div class="page-title-line">
      查看AI解读
      <div class="btn">AI分析</div>
    </div>
    <div class="jiedu">
      <div class="jiedu-content">
        <div class="title">AI分析过程与结果</div>
        <div class="title">AI输出结果</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.view-detail-wrap {
  .page-title-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    .btn {
      font-weight: normal;
      width: 117px;
      line-height: 30px;
      background: #53a9f9;
      border-radius: 5px 5px 5px 5px;
      text-align: center;
      font-size: 14px;
      color: #fff;
      cursor: pointer;
    }
  }
  .i-diag {
    display: flex;
    align-items: center;
    width: 100%;
    height: 170px;
    background-image: url('@/assets/imgs/bg-i-diag.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    margin-bottom: 30px;
    padding: 35px 200px 35px 75px;
    .icon {
      width: 99px;
      height: 99px;
    }
    .i-diag-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 40px;
      .title {
        font-size: 20px;
        color: #6da0ac;
        line-height: 36px;
        margin-bottom: 5px;
      }
      .text {
        font-size: 18px;
        color: #333333;
        line-height: 32px;
      }
    }
    .btn-wrap {
      display: flex;
      align-items: center;
      margin-left: 30px;
      .btn {
        width: 210px;
        line-height: 40px;
        text-align: center;
        color: #fff;
        font-size: 16px;
        background: linear-gradient(180deg, #8feaf7 0%, #4ed7eb 100%);
        border-radius: 5px 5px 5px 5px;
        cursor: pointer;
        & + .btn {
          margin-left: 20px;
        }
      }
    }
  }
}
.jiedu {
  height: 589px;
  background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #c6dbf3;
  padding: 26px 17px;
  .title {
    font-size: 16px;
    color: #3d3d3d;
    line-height: 20px;
    margin-bottom: 200px;
  }
}
</style>
