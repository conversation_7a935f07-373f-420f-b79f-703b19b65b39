<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'

defineOptions({ name: 'process' })

const columns = ref([
  {
    label: '阶段',
    prop: 'name',
    width: 90
  },
  {
    label: '时间',
    prop: 'time',
    width: 90
  },
  {
    label: '核心任务',
    prop: 'task'
  },
  {
    label: '输出成果',
    prop: 'result',
    width: 120
  },
  {
    label: '关键里程碑',
    prop: 'core',
    width: 150
  }
])
const tableData = ref([
  {
    strategy: '需求计划全流程端到端贯通',
    name: '诊断期',
    time: '第 1-2 月',
    task: '①开展全公司需求计划流程现状调研（访谈 80 + 计划、销售、生产等部门员工，分析 300 + 历史需求计划档案）②绘制当前需求计划流程价值流图，识别端到端贯通的核心断点与低效环节',
    result: '《需求计划流程诊断报告》《需求计划流程价值流图》',
    core: '断点识别准确率≥90%'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '设计期',
    time: '第 3-4 月',
    task: '①设计涵盖市场需求收集、分析、计划制定、执行反馈等 8 个关键环节的端到端流程标准与管控规则，制定《需求计划全流程操作手册》②开发端到端贯通监控看板原型，完成与 ERP、CRM 等系统的数据接口设计',
    result: '《需求计划全流程标准手册》《端到端贯通监控看板原型方案》',
    core: '标准覆盖率 100%，接口联调通过'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '试点期',
    time: '第 5-6 月',
    task: '①选择 2 个重点区域 / 产品线（如华南大区、白色家电产品线）开展试点②收集试点过程中各环节的反馈，优化流程标准与管控规则，迭代《需求计划全流程操作手册》',
    result: '《需求计划全流程试点总结报告》《需求计划流程规则修订版》',
    core: '试点流程完整率≥85%，异常处理效率提升 35%'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '推广期',
    time: '第 7-10 月',
    task: '①开展全公司需求计划全流程培训（线下培训 8 场 + 线上考试，通过率≥80% 方可上岗）②分批次上线端到端贯通监控看板，首月覆盖 60% 部门，次月全量上线',
    result: '《需求计划全流程培训记录》《端到端贯通监控看板系统上线报告》',
    core: '员工合规操作率≥80%，系统使用率≥85%'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '优化期',
    time: '第 11-12 月',
    task: '①召开季度复盘会，基于监控数据优化各环节耗时标准（如需求分析阶段从 10 天压缩至 7 天）②建立需求计划流程知识库，沉淀最佳实践案例（目标≥40 个 / 年）',
    result: '《需求计划流程优化方案》《需求计划最佳实践库》',
    core: '环节效率提升 12%，案例沉淀率 100%'
  }
])

const columns2 = ref([
  {
    label: '风险类型',
    prop: 'name',
    width: 80
  },
  {
    label: '风险描述',
    prop: 'desc',
    width: 120
  },
  {
    label: '应对措施',
    prop: 'measure'
  },
  {
    label: '责任部门',
    prop: 'org',
    width: 80
  }
])

const tableData2 = ref([
  {
    strategy: '需求计划全流程端到端贯通',
    name: '系统集成风险',
    desc: '各业务系统数据接口不兼容，导致流程贯通受阻',
    measure: '①提前开展系统兼容性测试，制定数据接口转换方案②建立跨系统数据对账机制，每日核查数据一致性',
    org: 'IT 部'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '员工适应风险',
    desc: '基层员工对新流程操作不熟悉，导致执行效率下降',
    measure: '①组织分阶段实操培训，搭配 “老带新” 结对辅导②开发流程模拟演练系统，设置考核达标奖励',
    org: '人力资源部'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '数据质量风险',
    desc: '历史数据不准确或缺失，影响流程诊断与设计',
    measure: '①启动数据清洗专项行动，引入第三方数据校验工具②建立数据质量追溯机制，明确各环节数据录入责任',
    org: '计划部'
  }
])
</script>
<template>
  <div class="core">
    <div class="core-title">
      <div class="index">1</div>
      <div class="text">实施路线图</div>
    </div>
    <SimplenessTable :columns="columns" :data="tableData"></SimplenessTable>
    <div class="core-title mt-6">
      <div class="index">2</div>
      <div class="text">资源配置方案</div>
    </div>
    <div class="border">
      <b> 人力投入：</b>
      <p>
        组建 20 人专项小组：计划部（8 人，牵头流程设计与试点）、IT 部（5 人，系统开发与接口调试）、销售 / 生产部（4
        人，业务需求对接）、流程管理部（3 人，监控执行）。外聘供应链流程专家（2 名，10 年以上家电行业经验），每月驻场 3
        天指导。
      </p>
      <b>资金预算：</b>
      <p>系统开发：80 万元（含监控看板、ERP 接口优化）。</p>
      <p>流程调研与培训：30 万元（数据清洗工具、线下培训物料、线上课程开发）。</p>
      <p>风险储备金：15 万元。</p>
    </div>
    <div class="core-title mt-6">
      <div class="index">3</div>
      <div class="text">异常管理机制</div>
    </div>
    <SimplenessTable :columns="columns2" :data="tableData2"></SimplenessTable>
  </div>
</template>
<style lang="scss" scoped>
.core {
  .core-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
    color: #3d3d3d;
    margin-bottom: 13px;
    .index {
      width: 16px;
      height: 16px;
      background: #40a0ff;
      border-radius: 50%;
      text-align: center;
      line-height: 16px;
      color: #fff;
      font-weight: normal;
      margin-right: 7px;
    }
  }
  .border {
    background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), rgba(255, 255, 255, 0.5);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 16px 20px;
  }
}
</style>
