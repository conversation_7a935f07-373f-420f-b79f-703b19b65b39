<template>
  <div class="indicatorImprove">
    <div class="classify">
      <div class="title">指标类别</div>
      <div
        class="item"
        :class="{ active: item.path == activeStepPath }"
        v-for="item in classify"
        :key="item.id"
        @click="changeStep(item)"
      >
        {{ item.name }}
      </div>
    </div>
    <RouterView />
  </div>
</template>
<script setup>
defineOptions({ name: 'IndicatorImprove' })
const classify = ref([
  { id: 1, name: '组织指标', path: '/indicator/indicatorImprove/zzIndex' },
  { id: 2, name: '人员指标', path: '/indicator/indicatorImprove/personnel' },
  { id: 3, name: '项目指标', path: '/indicator/indicatorImprove/project' }
])
const router = useRouter()
const route = useRoute()
console.log(999, route.path)
const activeStepPath = ref(route.path)
const changeStep = item => {
  router.push(item.path)
  activeStepPath.value = item.path
}
</script>
<style lang="scss" scoped>
.indicatorImprove {
  .classify {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      color: #333333;
      font-weight: 600;
      margin-right: 8px;
    }

    .item {
      width: 311px;
      height: 35px;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #c6dbf3;
      font-size: 14px;
      color: #333333;
      text-align: center;
      line-height: 35px;
      margin-right: 9px;
      background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
      cursor: pointer;

      &.active {
        color: #40a0ff;
        box-shadow: 0px 0px 20px -4px rgba(64, 160, 255, 0.48);
      }
    }
  }
}
</style>
