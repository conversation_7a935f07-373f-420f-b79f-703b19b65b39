<script setup>
import Table from "../../components/table.vue";
const numberArea = ref([
  {
    num: "0~59",
  },
  {
    num: "60~69",
  },
  {
    num: "70~79",
  },
  {
    num: "80~89",
  },
  {
    num: "90~100",
  },
]);

const columns = ref([
  {
    label: "姓名",
    prop: "a",
  },
  {
    label: "岗位",
    prop: "b",
  },
  {
    label: "对外宣传",
    prop: "c",
    slot: "cSlot",
    width: 80,
  },
  {
    label: "求职宣传",
    prop: "d",
    slot: "dSlot",
    width: 80,
  },
  {
    label: "不轻易离开",
    prop: "e",
    slot: "eSlot",
    width: 80,
  },
  {
    label: "少跳槽",
    prop: "f",
    slot: "fSlot",
    width: 80,
  },
  {
    label: "全力工作",
    prop: "g",
    slot: "gSlot",
    width: 80,
  },
  {
    label: "额外工作",
    prop: "h",
    slot: "hSlot",
    width: 80,
  },
  {
    label: "敬业度表现",
    prop: "i",
    slot: "iSlot",
    width: 80,
  },
  {
    label: "操作",
    prop: "j",
    slot: "jSlot",
    align: "center",
    width: 180,
  },
]);
const data = ref([
  {
    a: "王伟",
    b: "供应链总监",
    c: 94,
    d: 83,
    e: 14,
    f: 83,
    g: 93,
    h: 43,
    i: 54,
    j: 63,
    k: 74,
    l: 83,
    m: 93,
    n: 93,
    o: 93,
    p: 93,
    q: 93,
    r: 33,
    s: 94,
    t: 94,
  },
]);

const columns2 = ref([
  {
    label: "指标",
    prop: "a",
    width: 150,
  },
  {
    label: "得分",
    prop: "b",
    width: 150,
  },
  {
    label: "企业内部对比",
    prop: "c",
    width: 150,
  },
  {
    label: "行为解读",
    prop: "d",
  },
]);
const data2 = ref([
  {
    a: "全力工作",
    b: 94,
    c: "TOP 5%",
    d: "典型“战役型”管理者，618/双十一等大促期间常驻仓库指挥",
  },
]);
const columns3 = ref([
  {
    label: "驱动层级",
    prop: "a",
    width: 150,
  },
  {
    label: "维度",
    prop: "b",
    width: 150,
  },
  {
    label: "得分",
    prop: "c",
    width: 150,
  },
  {
    label: "管理意义",
    prop: "d",
  },
]);
const data3 = ref([
  {
    a: "超满意区",
    b: "成就感",
    c: "92.63",
    d: "供应链降本增效成果（如库存周转率提升）带来强烈满足，需持续供给挑战性目标",
  },
  {
    a: "",
    b: "工作任务",
    c: "92.63",
    d: "对职责内容高度认可（如战略采购谈判），警惕任务过载挤占决策时间",
  },
  {
    a: "",
    b: "管理团队",
    c: "92.63",
    d: "团队领导效能获肯定，是抵御低工作氛围需求的关键缓冲",
  },
]);

const columns4 = ref([
  {
    label: "驱动维度",
    prop: "a",
    width: 150,
  },
  {
    label: "行动方案",
    prop: "b",
    width: 150,
  },
  {
    label: "执行要点",
    prop: "c",
  },
]);
const data4 = ref([
  {
    a: "成就感",
    b: "创建“供应链战报”机制",
    c: "每日推送库存周转改善值（如：呆滞库存下降200万/天）",
    d: "",
    e: "",
    f: "",
  },
]);

const incentiveFactor = ref([
  {
    title: "强激励因素",
    info: ["成就", "认可", "自我成长", "发展前景"],
  },
  {
    title: "一般激励因素",
    info: ["竞争", "事业心"],
  },
  {
    title: "一般负激励因素",
    info: ["权力", "工作氛围", "趣味性", "自主"],
  },
  {
    title: "强负激励因素",
    info: ["求胜心", "灵活性", "物质奖励", "地位"],
  },
]);

const circleColor = (v) => {
  if (v < 59) {
    return "bg1_b";
  } else if (v > 59 && v < 69) {
    return "bg2_b";
  } else if (v > 69 && v < 79) {
    return "bg3_b";
  } else if (v > 79 && v < 89) {
    return "bg4_b";
  } else if (v > 89 && v <= 100) {
    return "bg5_b";
  }
};

const aiData = ref({
  t: "",
  info: [
    {
      title: "敬业驱动因素解码：",
    },
    {
      title: "核心驱动力：成就满足感（92.63）",
      info: [
        {
          title: "",
          info: "触发机制：主导项目产生行业影响力（如智能预测模型被中国家电协会推广）",
        },
        {
          title: "",
          info: "失衡风险：若长期陷于事务性工作（如签批日常补货单），敬业度可能下滑30%+",
        },
      ],
    },
    {
      title: "关键支撑点：战略级工作任务（91.31）",
      info: [
        {
          title: "",
          info: "最佳实践：如负责集团级“动态库存感知系统”开发（关联库存周转率提升1.2次）",
        },
        {
          title: "",
          info: "管理雷区：切忌将其调离创新岗位（如转管仓储物流），将导致敬业度崩塌式下降",
        },
      ],
    },
    {
      title: "领导赋能双引擎：",
      info: [
        {
          title: "",
          info: "管理团队（90.97）：要求下属均为领域专家",
        },
        {
          title: "",
          info: "直接上级（90.67）：COO给予“战略防火墙”",
        },
      ],
    },
    {
      title: "隐性风险点：薪酬（84.15）与职业发展（87.46）：",
      info: [
        {
          title: "",
          info: "薪酬悖论：虽得分仅84.15，但被成就感补偿（研究显示高成就可抵消20%薪酬差距）",
        },
        {
          title: "",
          info: "发展焦虑：87.46分预示晋升瓶颈（若2年内未升CPO，可能触发外部机会考量）",
        },
      ],
    },
  ],
});

const ai2Data = ref({
  t: "",
  info: [
    {
      title: "敬业驱动因素分层诊断：",
      info: [
        {
          title: "",
          info: "敬业度脆弱点：（按满意度得分降序，阈值：≥90超满意，85-89健康，≤84隐患）",
        },
      ],
    },
  ],
});

const ai3Data = ref({
  t: "",
  info: [
    {
      title: "管理行动建议：",
    },
  ],
});
const ai4Data = ref({
  t: "",
  info: [
    {
      title: "人员敬业度提升总结：",
      info: [
        {
          title: "",
          info: "敬业度提升公式：92%成就感 = 70%战略任务 + 15%行业声望 + 10%团队赋能 + 5%物质奖励；",
        },
        {
          title: "",
          info: "此模型下，即使薪酬低于市场20%，刘威敬业度仍可保持85+分位，关键在于持续供给改变行业规则的战略机遇，这是解锁其巅峰敬业状态的核心密码。",
        },
      ],
    },
  ],
});
</script>
<template>
  <div class="content-wrap">
    <div class="page-title-line">敬业度整体表现</div>
    <div class="overall_performance justify-between">
      <div class="chart1_box"></div>
      <div class="info">
        参与评估人员整体敬业度达 91.9
        分，显著高于行业均值，处于高敬业度核心区间，体现出团队对工作目标高度认同、组织归属感强烈且具备持续主动付出的意愿。高敬业度员工呈现工作投入度、组织认同度、情感承诺度高而离职意愿低的
        “三高一低”
        特征，尤其在供应链计划等核心岗位，能有效避免关键人员流失导致的业务波动，守护流程熟稔度与数据资产等核心竞争力。组织赋能感通过资源供给、能力提升、价值实现三级机制强化这一优势，数据显示赋能感每提升
        10%，离职率可额外降低
        8%-10%，能有效对冲低得分维度的潜在风险。管理上需采取 “精准强化 +
        风险防控” 策略，针对强激励因素设立
        “战略合伙人计划”“预测优化利润分享机制”，同时警惕
        “高敬业疲劳”，对核心员工推行
        “赋能休假”，将高敬业优势转化为供应链能力升级的持续动力，构建人才竞争壁垒。
      </div>
    </div>

    <div class="page-title-line">人员敬业度详情</div>
    <div class="number_area_list justify-start">
      分值：
      <div class="item_wrap" v-for="(item, index) in numberArea">
        <span
          class="icon"
          :class="{
            act: index == 0,
            act1: index == 1,
            act2: index == 2,
            act3: index == 3,
            act4: index == 4,
          }"
        ></span
        >{{ item.num }}
      </div>
    </div>
    <Table
      :roundBorder="false"
      :columns="columns"
      :data="data"
      headerColor
      showIndex
    >
      <template v-slot:cSlot="scope">
        <span class="circle" :class="circleColor(scope.row.c)">{{
          scope.row.c
        }}</span>
      </template>
      <template v-slot:dSlot="scope">
        <span class="circle" :class="circleColor(scope.row.d)">{{
          scope.row.d
        }}</span>
      </template>
      <template v-slot:eSlot="scope">
        <span class="circle" :class="circleColor(scope.row.e)">{{
          scope.row.e
        }}</span>
      </template>
      <template v-slot:fSlot="scope">
        <span class="circle" :class="circleColor(scope.row.f)">{{
          scope.row.f
        }}</span>
      </template>
      <template v-slot:gSlot="scope">
        <span class="circle" :class="circleColor(scope.row.g)">{{
          scope.row.g
        }}</span>
      </template>
      <template v-slot:hSlot="scope">
        <span class="circle" :class="circleColor(scope.row.h)">{{
          scope.row.h
        }}</span>
      </template>
      <template v-slot:iSlot="scope">
        <span class="circle" :class="circleColor(scope.row.i)">{{
          scope.row.i
        }}</span>
      </template>

      <template v-slot:jSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>详情</el-button>
        <el-button class="ai_btn" type="primary" plain round>对比</el-button>
      </template>
    </Table>
    <div class="tips">已选人员：<span>王伟</span></div>

    <div class="section_title blue_section_wrap justify-between">
      <div class="t">人员动力详情（刘威-供应链总监）</div>
      <div class="line"></div>
      <div class="ai">AI解读</div>
    </div>
    <div class="tip_blue_bg marginB20">
      工作驱动（ 哪些因素能够有效的驱动个人更加的投入工作 ）
    </div>
    <div class="chart_box marginB20"></div>
    <div class="tips">已选人员：<span>王伟</span></div>

    <div class="page-title-line">
      人员敬业度满意度 AI解读（刘威-供应链总监）
    </div>
    <div class="">敬业度表现分析：高投入型战略领袖</div>

    <div class="table2_wrap">
      <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor>
      </Table>
    </div>

    <div class="">
      <div class="t marginB20">{{ aiData.t }}</div>
      <div class="dot_content_wrap" v-for="item in aiData.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          <span class="icon"></span>
          <span class="title">{{ item1.title }}</span>
          <span class="info">{{ item1.info }}</span>
        </div>
      </div>
    </div>

    <div class="">
      <div class="t marginB20">{{ ai2Data.t }}</div>
      <div class="dot_content_wrap" v-for="item in ai2Data.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          <span class="icon"></span>
          <span class="title">{{ item1.title }}</span>
          <span class="info">{{ item1.info }}</span>
        </div>
      </div>
    </div>
    <div class="table3_wrap">
      <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor>
      </Table>
    </div>
    <div class="">
      <div class="t marginB20">{{ ai3Data.t }}</div>
      <div class="dot_content_wrap" v-for="item in ai3Data.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          <span class="icon"></span>
          <span class="title">{{ item1.title }}</span>
          <span class="info">{{ item1.info }}</span>
        </div>
      </div>
    </div>
    <div class="table4_wrap">
      <Table :roundBorder="false" :columns="columns4" :data="data4" headerColor>
      </Table>
    </div>
    <div class="">
      <div class="t marginB20">{{ ai4Data.t }}</div>
      <div class="dot_content_wrap" v-for="item in ai4Data.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          <span class="icon"></span>
          <span class="title">{{ item1.title }}</span>
          <span class="info">{{ item1.info }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}

.content-wrap {
  .overall_performance {
    margin-bottom: 85px;
    align-items: center;
    .chart1_box {
      margin-right: 40px;
      width: 360px;
      height: 220px;
      border: 1px solid #e0e0e0;
    }
    .info {
      flex: 1;
    }
  }

  :deep .el-table {
    .ai_btn {
      padding: 0 15px;
      height: 24px;
      font-size: 14px;
    }
    .circle {
      display: inline-block;
      width: 65px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      color: #fff;
      border-radius: 10px;
    }
    .bg1_b {
      background: #deeff9;
      color: #3d3d3d;
    }
    .bg2_b {
      background: #95d9f0;
    }
    .bg3_b {
      background: #65bbea;
    }
    .bg4_b {
      background: #2c89cd;
    }
    .bg5_b {
      background: #00659b;
    }
  }

  .tips {
    margin: 16px 0 40px;
    color: #94a1af;
    span {
      color: #3d3d3d;
    }
  }
  .tip_blue_bg {
    padding: 0 12px;
    height: 36px;
    line-height: 36px;
    background: #eff4f9;
    border-radius: 5px 5px 5px 5px;
    font-size: 14px;
    color: #40a0ff;
  }
  .chart_box {
    height: 300px;
  }
  .number_area_list {
    font-size: 12px;
    .item_wrap {
      margin: 0 25px 24px 0;
      .icon {
        display: inline-block;
        margin: 0 5px 0 15px;
        width: 14px;
        height: 8px;
        &.act {
          background: #deeff9;
        }
        &.act1 {
          background: #95d9f0;
        }
        &.act2 {
          background: #65bbea;
        }
        &.act3 {
          background: #2c89cd;
        }
        &.act4 {
          background: #00659b;
        }
      }
    }
  }
  .chart_list_wrap {
    margin: 0 -6px;
    .item_wrap {
      margin: 0 5px;
      padding: 7px 9px;
      flex: 1;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      .chart_t {
        height: 29px;
        line-height: 29px;
        text-align: center;
        background: #e4eef6;
        border-radius: 5px 5px 5px 5px;
        color: #7a94ad;
      }
      .type_box {
        padding: 5px 0 10px 0;
        width: 100%;
        height: 150px;
        flex-wrap: wrap;
        align-items: center;
        .item_factor {
          margin-top: 10px;
          padding: 7px;
          width: 48%;
          height: 56px;
          border-radius: 5px 5px 5px 5px;
          border: 2px solid #cee7ff;
          span {
            display: block;
            width: 100%;
            height: 40px;
            line-height: 40px;
            text-align: center;
            border-radius: 5px 5px 5px 5px;
            background: #cee7ff;
            color: #40a0ff;
            font-size: 16px;
          }
        }
      }
    }
  }
  .section_title {
    margin: 36px 0 15px;
    .t {
      color: #40a0ff;
    }
    .line {
      flex: 1;
      margin: 10px 7px 0;
      height: 1px;
      background: #d8d8d8;
    }
    .ai {
      margin-top: -5px;
      width: 73px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #40a0ff;
      border: 1px solid #40a0ff;
      border-radius: 18px;
    }
    &.blue_section_wrap {
    }
  }
  .table2_wrap {
    margin: 10px 0 40px;
  }
  .table3_wrap {
    margin: 10px 0 47px;
  }
  .table4_wrap {
    margin: 13px 0 27px 0;
  }

  .section_box_wrap {
    background: #fff;
    .item_wrap {
      .item_title {
        margin: 10px 0 10px 0;
      }
      .info_item {
      }
    }
  }
  :deep .demo-form-inline {
    .el-form-item {
      width: 25%;
      margin-right: 0;
      .el-form-item__label {
        width: 130px;
        text-align: right;
      }
    }
  }
  .dot_content_wrap {
    // margin: 0 0 40px 0;
    .t {
      line-height: 34px;
    }
    .item_wrap {
      margin-bottom: 0;
      .icon {
        margin: -2px 0px 0 15px;
      }
      .info {
        line-height: 34px;
      }
    }
  }
}
</style>
