<template>
  <div>
    <div class="personal_planning_main clearfix">
      <div class="marginT_16 clearfix">
        <div class="panel_left fl">
          <div class="current_post">
            <div class="page_second_title">当前职位</div>
            <div class="current_post_center marginT_16">
              <div class="item">
                <div class="item_title">职位名称</div>
                <div class="item_text">{{ currPostName }}</div>
              </div>
              <div class="item">
                <div class="item_title">所属部门</div>
                <div class="item_text">{{ currOrgName }}</div>
              </div>
              <div class="item">
                <div class="item_title">职位族群</div>
                <div class="item_text">
                  {{ parentJobClassName }}
                </div>
              </div>
              <div class="item">
                <div class="item_title">职位序列</div>
                <div class="item_text">{{ jobClassName }}</div>
              </div>
            </div>
          </div>
          <div class="current_post marginT_30">
            <div class="page_second_title">发展类型</div>
            <div class="type_wrap marginT_20">
              <el-radio-group v-model="developmentType" @change="devTypeChange">
                <el-radio v-for="(item, index) in developmentTypeOptions" :label="item.dictCode" :key="index" border>{{
                  item.codeName
                }}</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
        <div class="panel_center fl">
          <div class="current_post">
            <div class="" v-if="developmentType == 1">
              <div class="page_second_title">下一晋升职位</div>
              不需要选择晋升职位
            </div>
            <div class="goal_post" v-show="false">
              <div class="page_second_title">下一晋升职位</div>
              <div class="current_post_center marginT_16">
                <div class="item">
                  <div class="item_title">职位族群</div>
                  <div class="item_text">
                    {{ goalPostInfo.parentJobClassName }}
                  </div>
                </div>
                <div class="item">
                  <div class="item_title">职位序列</div>
                  <div class="item_text">
                    {{ goalPostInfo.jobClassName }}
                  </div>
                </div>
                <div class="item">
                  <div class="item_title">职位名称</div>
                  <div class="item_text">
                    {{ goalPostInfo.jobName }}
                  </div>
                </div>
                <div class="item">
                  <div class="item_title">职层</div>
                  <div class="item_text">
                    {{ goalPostInfo.jobLevelName }}
                  </div>
                </div>
                <div class="item">
                  <div class="item_title">职等</div>
                  <div class="item_text">
                    {{ goalPostInfo.jobGradeName }}
                  </div>
                </div>
              </div>
            </div>

            <div class="" v-show="developmentType == 3 || developmentType == 2">
              <div class="page_second_title">下一晋升职位</div>
              <div class="promotion_post clearfix marginT_16">
                <div class="post_type fl">
                  <div class="title border_r_1">职族</div>
                  <div class="post_type_center border_r_1">
                    <div class="post_type_main">
                      <tree-comp-radio
                        :treeData="treeData"
                        :defaultExpandAll="false"
                        :nodeKey="'code'"
                        :defaultCheckedKeys="defaultCheckedKeys"
                        @clickCallback="treeCallback"
                      ></tree-comp-radio>
                    </div>
                  </div>
                </div>
                <div class="post_type fl">
                  <div class="title">职位名称</div>
                  <div class="post_type_center">
                    <div class="post_type_main">
                      <el-radio-group v-model="expectationJob">
                        <el-radio class="post_item" v-for="item in postList" :key="item.jobCode" :label="item.jobCode"
                          >{{ item.jobName }}({{ item.jobLevelName }})</el-radio
                        >
                      </el-radio-group>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel_right fl">
          <div class="current_post">
            <div class="page_second_title">发展目标</div>
            <div class="development_goals marginT_16">
              <div class="item">
                <div class="title">预计周期</div>
                <div class="center">
                  <el-select v-model="expectationCycle" placeholder="请选择">
                    <el-option
                      v-for="item in expectationCycleOptions"
                      :key="item.dictCode"
                      :label="item.codeName"
                      :value="item.dictCode"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div class="item">
                <div class="title">个人短板分析与总结</div>
                <div class="center">
                  <el-input type="textarea" v-model="analyzeSummarize" rows="5" resize="none" placeholder></el-input>
                </div>
              </div>
              <div class="item">
                <div class="title">个人发展计划</div>
                <div class="center">
                  <el-input type="textarea" v-model="developmentPlan" rows="5" resize="none" placeholder></el-input>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="marginT_30 align_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn" v-show="currentIndex != currentFirstCode"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio.vue'
import {
  getEnqUserDevelopment,
  updateEnqUserDevelopment as updateEnqUserDevelopmentApi,
  getCcurrentPosition,
  getIdealJob,
  getPostData,
  getJobList
} from '../../../request/api'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])
const userStore = useUserStore()

const developmentId = ref(null)
const jobCode = ref(null)
const orgCode = ref(null)
const currPostName = ref('')
const currOrgName = ref('')
const jobClassName = ref('')
const parentJobClassName = ref('')
const parentJobClassCode = ref('')
const developmentType = ref('')
const developmentTypeOptions = ref([])
const expectationJob = ref('')
const developmentPlan = ref('')
const analyzeSummarize = ref('')
const expectationCycle = ref('')
const expectationCycleOptions = ref([])
const goalPostInfo = ref({})
const treeData = ref([])
const postList = ref([])
const hasParentPost = ref(true)
const defaultCheckedKeys = ref([])

const userId = computed(() => userStore.userInfo.userId)

watch(developmentType, val => {
  if (val == 3 || val == 2) {
    getOrgDeptTreeFun()
  }
})

onMounted(() => {
  userStore.getDocList(['DEVELOPMENT_TYPE', 'EXPECTATION_CYCLE']).then(res => {
    developmentTypeOptions.value = res.DEVELOPMENT_TYPE
    expectationCycleOptions.value = res.EXPECTATION_CYCLE
  })
  getCurrPostFun()
})

function getEnqUserDevelopmentFun() {
  getEnqUserDevelopment({
    enqId: props.enqId
  }).then(res => {
    if (res.code == '200') {
      let data = res.data
      if (data) {
        developmentId.value = data.developmentId
        expectationCycle.value = data.expectationCycle
        developmentType.value = data.developmentType
        developmentPlan.value = data.developmentPlan
        analyzeSummarize.value = data.analyzeSummarize
        defaultCheckedKeys.value = data.jobClassCode ? [data.jobClassCode] : []
        expectationJob.value = data.expectationJob
      } else {
        developmentType.value = '1'
      }
    }
  })
}

function getCurrPostFun() {
  getCcurrentPosition({
    enqId: props.enqId,
    userId: userId.value
  }).then(res => {
    if (res.code == '200') {
      let data = res.data
      jobCode.value = data.jobCode
      orgCode.value = data.orgCode
      currPostName.value = data.jobName
      currOrgName.value = data.orgName
      jobClassName.value = data.jobClassName
      parentJobClassName.value = data.parentJobClassName
      parentJobClassCode.value = data.parentJobClassCode
      getEnqUserDevelopmentFun()
    }
  })
}

function devTypeChange(val) {
  developmentType.value = val
}

function getPostTypeFun() {
  let params = {
    enqId: props.enqId,
    developmentType: developmentType.value,
    userId: userId.value
  }
  getIdealJob(params).then(res => {
    if (res.code == '200') {
      let data = res.data
      if (data.idealJob) {
        if (developmentType.value == 2) {
          hasParentPost.value = true
          expectationJob.value = data.idealJob.jobCode
        }
        goalPostInfo.value = data.idealJob
      } else {
        if (developmentType.value == 2) {
          hasParentPost.value = false
        }
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function getOrgDeptTreeFun() {
  getPostData({}).then(res => {
    if (res.length > 0) {
      treeData.value = res
    } else {
      treeData.value = []
    }
  })
}

function treeCallback(code) {
  orgCode.value = code
  if (developmentType.value == 3 || developmentType.value == 2) {
    getPostListFun()
  }
}

function getPostListFun() {
  getJobList({ jobClassCode: orgCode.value }).then(res => {
    if (res.code == 200) {
      postList.value = res.data
    }
  })
}

function updateEnqUserDevelopment(stepType) {
  let params = {
    expectationCycle: expectationCycle.value,
    developmentId: developmentId.value,
    developmentPlan: developmentPlan.value,
    analyzeSummarize: analyzeSummarize.value,
    developmentType: developmentType.value,
    enqId: props.enqId,
    expectationJob: expectationJob.value,
    jobCode: jobCode.value,
    userId: userId.value
  }
  updateEnqUserDevelopmentApi(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function prevBtn() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      updateEnqUserDevelopment('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}

function nextBtn() {
  if (developmentType.value == 2 && !hasParentPost.value) {
    ElMessage.warning('当前岗位暂无上级岗位，请选择其他发展类型')
    return
  }
  let checkArr = []
  let tips = []
  if (developmentPlan.value == '3' || developmentPlan.value == '2') {
    checkArr = [
      developmentType.value,
      expectationJob.value,
      expectationCycle.value,
      developmentPlan.value,
      analyzeSummarize.value
    ]
    tips = [
      '请选择发展类型！',
      '请选择晋升职位!',
      '请选择发展预计周期!',
      '请填写个人短板分析与总结!',
      '请填写个人发展计划!'
    ]
  } else {
    checkArr = [developmentType.value, expectationCycle.value, developmentPlan.value, analyzeSummarize.value]
    tips = ['请选择发展类型！', '请选择发展预计周期!', '请填写个人短板分析与总结!', '请填写个人发展计划!']
  }
  for (let i = 0; i < checkArr.length; i++) {
    if (!checkArr[i]) {
      ElMessage.warning(tips[i])
      return
    }
  }
  updateEnqUserDevelopment('nextStep')
}
</script>

<style scoped lang="scss">
.panel_left {
  width: 220px;

  .current_post {
    width: 100%;

    .current_post_center {
      width: 100%;
      border: 1px solid #e5e5e5;
      border-bottom: none;

      .item {
        width: 100%;
        display: flex;
        min-height: 40px;
        // line-height: 40px;
        font-size: 12px;
        align-items: center;
        border-bottom: 1px solid #e5e5e5;
        .item_title {
          position: relative;
          width: 30%;
          height: 100%;
          line-height: 40px;
          text-align: center;
          background: #f4f4f4;
          // background: #f4f4f4;
        }

        .item_text {
          flex: 1;
          padding-left: 12px;
          // white-space: nowrap;
          background: #fff;
        }
      }
    }

    .type_wrap {
      width: 168px;
      .el-radio-group {
        width: 100%;
        height: 35px;
        .el-radio {
          position: relative;
          width: 100%;
          height: 100%;
          margin: 0 0 15px 0;
          line-height: 35px;
          padding: 0;
          border-color: #e5e5e5;
          &.is-checked {
            border-color: #449cff;
          }
          .el-radio__input {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 5px;
            top: 6px;
            border: 1px solid #e5e5e5;
            border-radius: 50%;
            overflow: hidden;
            .el-radio__inner {
              display: none;

              &::after {
                width: 0;
                height: 0;
              }
            }
            &.is-checked {
              background: #0099ff;
              .el-radio__inner {
                display: block;
                position: absolute;
                width: 13px;
                height: 7px;
                left: 3px;
                top: 4px;
                border: 2px solid #fff;
                border-radius: 0;
                transform: rotate(-45deg);
                border-top: none;
                border-right: none;
              }
            }
          }
        }
        .el-radio {
          position: relative;
          width: 100%;
          height: 100%;
          margin: 0 0 15px 0;
          line-height: 35px;
          padding: 0;
          border-color: #e5e5e5;
          padding-left: 3px;

          &.is-checked {
            border-color: #449cff;
          }
          .el-radio__input {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 5px;
            top: 6px;
            border: 1px solid #e5e5e5;
            border-radius: 50%;
            overflow: hidden;

            .el-radio__inner {
              display: none;

              &::after {
                width: 0;
                height: 0;
              }
            }

            &.is-checked {
              background: #0099ff;

              .el-radio__inner {
                display: block;
                position: absolute;
                width: 13px;
                height: 7px;
                left: 3px;
                top: 4px;
                border: 2px solid #fff;
                border-radius: 0;
                transform: rotate(-45deg);
                border-top: none;
                border-right: none;
              }
            }
          }
        }
      }
    }
  }
}

.panel_center {
  min-width: 300px;
  margin-left: 70px;
  .current_post_center {
    width: 100%;
    border: 1px solid #e5e5e5;
    border-bottom: none;

    .item {
      width: 100%;
      display: flex;
      height: 40px;
      line-height: 40px;
      font-size: 12px;

      .item_title {
        width: 40%;
        text-align: center;
        background: #f4f4f4;
        border-bottom: 1px solid #e5e5e5;
      }

      .item_text {
        flex: 1;
        padding-left: 12px;
        border-bottom: 1px solid #e5e5e5;
      }
    }
  }

  .promotion_post {
    width: 350px;
    border: 1px solid #e5e5e5;

    .post_type {
      width: 50%;

      .title {
        background: #ebf4ff;
        height: 45px;
        line-height: 45px;
        text-align: center;
      }

      .post_type_center {
        padding: 10px;
        height: 360px;

        .post_type_main {
          width: 100%;
          height: 100%;
          overflow-y: auto;
          .post_item {
            line-height: 26px;
            display: block;
            margin-right: 0;
          }
        }

        .item {
          line-height: 40px;
          cursor: pointer;

          .icon {
            color: transparent;
          }

          &.active {
            color: #0099ff;

            .icon {
              color: #0099ff;
            }
          }
        }
      }
    }
  }
}

.panel_right {
  margin-left: 70px;
  width: 280px;

  .development_goals {
    .item {
      margin-bottom: 20px;
    }

    .title {
      padding-bottom: 10px;
      font-size: 14px;
    }
  }
}
.post_info {
  border: 1px solid #e5e5e5;
  padding: 16px;
  line-height: 24px;
  .post_info_item {
    .label {
      width: 80px;
      margin-right: 8px;
      color: #212121;
    }
  }
}
.tree_wrap {
  width: 300px;
}
</style>
