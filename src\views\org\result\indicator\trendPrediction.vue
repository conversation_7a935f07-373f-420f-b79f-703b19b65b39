<script setup>
import { Search } from "@element-plus/icons-vue";
import SectionTab from "../../components/sectionTab.vue";
import Tree from "@/components/tree/index.vue";
import Table from "../../components/table.vue";
const key = ref("");

const columns = ref([
  {
    label: "指标名称",
    prop: "a",
  },
  {
    label: "目标类别",
    prop: "b",
  },
  {
    label: "目标期间",
    prop: "c",
  },
  {
    label: "指标单位",
    prop: "d",
  },

  {
    label: "责任人",
    prop: "e",
  },
  {
    label: "指标目标",
    prop: "f",
  },
  {
    label: "实际表现",
    prop: "g",
  },

  {
    label: "",
    prop: "j",
    slot: "jSlot",
    width: 130,
  },
]);
const data = ref([
  {
    a: "物流成本占比",
    b: "年度目标",
    c: "2025年",
    d: "天",
    e: "王伟",
    f: ">98%",
    g: ">98%",
    h: ">98%",
    i: ">98%",
    j: 3,
  },
]);

const numberArea = ref([
  {
    num: "0~59",
  },
  {
    num: "60~69",
  },
  {
    num: "70~79",
  },
  {
    num: "80~89",
  },
  {
    num: "90~100",
  },
]);

const infoList = ref([
  {
    title: " 一、预测逻辑",
    info: [
      " 1. 供应链稳定性下降：2026 年全球物流网络可能因地缘政治冲突加剧，导致原材料运输周期延长 15%-20%，到货不及时引发生产端缓冲库存增加；港口拥堵或空运成本飙升迫使企业提前储备 30% 以上安全库存，直接推高库存总量。",
      " 2. 需求预测偏差扩大：市场消费趋势受经济周期波动影响，若 2026 年出现区域性经济衰退，消费者购买力下降导致实际需求低于预测值 20%-25%，已生产商品滞销积压，周转天数因库存消化速度放缓而增加。",
      "3. 库存管理效率降低：仓储自动化系统老化未及时升级，订单拣货效率下降 10%，库存盘点误差率从 3% 上升至 8%，导致滞销品未及时清理，有效库存占比从 90% 降至 75%，周转效率被动拉低。",
      "4. 采购策略失误：为规避原材料涨价风险，采购部门批量囤货导致部分原材料库存超过 6 个月使用量，而同期产品设计变更使该类原材料需求下降 40%，专用料件积压形成呆滞库存，占比超过库存总额 15%。",
      "5. 促销活动效果不及预期：季末清库存促销因定价策略僵化，折扣力度不足或渠道覆盖缺失，滞销品去化率仅达 40%（目标为 70%），导致应周转库存滞留仓库，延长平均周转时间。",
    ],
  },
  {
    title: "  二、最容易受到哪些指标影响 ",
    info: [
      "1. 库存持有成本率：库存周转天数每增加 1 天，库存持有成本率约上升 0.4%（基于历史数据测算），悲观情景下周转天数增加 3 天，直接导致该指标超预期上升，且与库存总量正相关，形成 “周转慢 - 成本高” 的强关联。 ",
      "2. 现金流周转率：库存占用资金每增加 1%，现金流周转率下降约 0.6%，因库存周转放缓导致的资金沉淀会直接挤压经营性现金流，尤其在悲观情景下，应收账款周期同步延长时，资金链紧张风险加剧。 ",
      "3. 订单满足率：有效库存占比每下降 5%，订单满足率下降约 3%，滞销库存积压导致 SKU 可得性降低，紧急订单履约能力削弱，直接影响客户交付体验，形成 “库存高 - 交付差” 的负向循环。 ",
      "4. 滞销库存占比：周转天数与滞销库存占比呈显著正相关（相关系数 0.85），库龄超 90 天的滞销品每增加 10%，周转天数延长约 2 天，悲观情景下两者相互强化，导致库存结构恶化。 ",
      "5. 供应商交付准时率：因库存积压导致采购计划频繁调整，供应商订单取消率上升 15%，触发供应商产能调配优先级下降，交付准时率从 90% 降至 75%，形成 “需求波动 - 供应延迟 - 库存进一步积压” 的链式反应。",
    ],
  },
  {
    title: "三、可能重点影响的关键指标 ",
    info: [
      "    1. 库存持有成本率：库存周转天数增加 3 天（从 75 到 78），按年化计算库存持有成本率将上升 1.2%-1.5%（假设库存平均价值 5 亿元，年持有成本率 8%，则成本增加 120-150 万元），侵蚀利润率。 ",
      "    2. 现金流周转率：库存占用资金增加导致经营性现金流减少，若库存总额上升 5%，现金流周转率可能下降 2%-3%，影响短期偿债能力和投资能力。 ",
      "    3. 订单满足率：无效库存增加导致有效库存不足，紧急订单响应率从 95% 降至 85%，客户订单交付延迟率上升 10%，引发客户投诉率增加 20% 以上。 ",
      "    4. 滞销库存占比：周转天数延长伴随滞销品积压，占比从 8% 升至 15%，需计提更多减值准备，影响资产负债表质量。 ",
      "    5. 供应商满意度：为消化库存可能推迟付款周期，应付账款周转率下降，供应商账期违约率上升，导致供应商交货优先级降低，形成恶性循环",
    ],
  },
  {
    title: " 四、需要重点改善的能力 ",
    info: [
      "    1. 供应链韧性建设能力：需建立多区域供应商备份体系，关键物料本地产能储备达 40% 以上，物流应急预案覆盖海运、空运、陆运多渠道，通过供应链风险评估系统实时监控运输时效，将原材料到货延迟率控制在 5% 以内。",
      "    2. 需求预测精准度提升能力：引入 AI 预测模型融合宏观经济数据、消费者调研、历史销售数据，季度预测误差率从 20% 降至 10% 以下；建立市场反馈快速响应机制，新品试销期缩短至 4 周，及时调整生产计划。",
      "    3. 库存管理数字化能力：升级 WMS 系统实现智能货位管理，引入 RFID 盘点技术将盘点误差率控制在 2% 以内，设置滞销品自动预警阈值（库龄超 90 天触发），每月呆滞库存处理率不低于 60%。",
      "    4. 采购策略动态优化能力：建立原材料价格波动预警模型，采用阶梯式采购策略（价格波动 ±10% 时自动调整采购量），专用料件采购前与研发部门联动确认技术稳定性，将呆滞料占比控制在 5% 以下。",
      "    5. 促销策略敏捷调整能力：基于实时库存数据和市场竞品分析，动态制定促销组合（如买赠、限时折扣、跨渠道联动），滞销品去化率目标提升至 60% 以上；建立客户分层营销体系，高价值客户复购率提升 15% 以拉动有效周转。",
    ],
  },
]);
</script>
<template>
  <div class="indicator_main">
    <div class="page-title-line">指标一览</div>
    <Table roundBorder :columns="columns" :data="data" showIndex>
      <template v-slot:jSlot="scope">
        <el-button class="ai_btn" type="primary" plain round
          >指标预测</el-button
        >
      </template>
    </Table>
    <div class="tips">已选指标：<span>库存周转天数</span></div>

    <div class="page-title-line">趋势预测说明</div>
    <div class="section_box_wrap">
      <div class="item_wrap" v-for="item in infoList">
        <div class="item_title">{{ item.title }}</div>
        <div class="info_item" v-for="itemC in item.info">{{ itemC }}</div>
      </div>
    </div>
    <div class="tips">已选能力：<span>库存动态分析</span></div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
@import "../common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
