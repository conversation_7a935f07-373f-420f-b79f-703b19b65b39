<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section">
      <div class="content_item_title marginB_16">部门活动分析</div>
      <!-- <tableComponent :needIndex="true" :tableData="tableData"></tableComponent> -->
      <template>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column type="index" fixed="left"></el-table-column>
          <el-table-column prop="activityTypeName" fixed="left" label="工作类别" width="80"></el-table-column>
          <el-table-column prop="activityName" fixed="left" label="主要活动" width="120"></el-table-column>
          <el-table-column
            prop="efficiencyValue"
            fixed="left"
            label="高绩效部门"
            :formatter="addPercentSign"
          ></el-table-column>
          <el-table-column
            v-for="list in columns"
            :formatter="addPercentSign"
            :key="list.orgCode"
            :prop="list.orgCode"
            :label="list.orgName"
          ></el-table-column>
        </el-table>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { orgActivityCompare } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const route = useRoute()
const enqId = ref(route.query.enqId)
const jobClassCode = ref('')
const orgCode = ref('')
const pageCurrent = ref(1)
const pageSize = ref(10)
const columns = ref([])
const tableData = ref([])
const filterData = ref({})

const orgActivityCompareFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: pageCurrent.value,
      size: pageSize.value
    }
    const res = await orgActivityCompare(params)
    if (res.code == 200) {
      columns.value = dotToline(res.data.orgList, 'value', 'orgCode')
      tableData.value = dotToline(res.data.records, 'key')
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  orgActivityCompareFun()
}

const addPercentSign = (row, column, cellValue) => {
  return cellValue + '%'
}

const dotToline = (param, type, valueKey) => {
  if (Array.isArray(param)) {
    if (param.length == 0) {
      return
    }
    param.forEach(item => {
      if (typeof item == 'object') {
        for (const key in item) {
          if (Object.prototype.hasOwnProperty.call(item, key)) {
            if (type == 'key') {
              const newKey = key.split('.').join('-')
              item[newKey] = item[key]
            } else if (type == 'value') {
              const val = item[valueKey]
              item[valueKey] = val.split('.').join('-')
            }
          }
        }
      }
    })
    return param
  }
}

onMounted(() => {
  filterData.value = route.attrs.filterData
  orgActivityCompareFun()
})
</script>

<style scoped lang="scss"></style>
