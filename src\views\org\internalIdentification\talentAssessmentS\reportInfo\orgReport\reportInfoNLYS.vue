<script setup>
import Table from "@/components/table/simplenessTable.vue";
const emits = defineEmits(["closeReportInfo"]);
// 2
const columns3 = ref([
  {
    label: "一级能力",
    prop: "a",
  },
  {
    label: "二级能力",
    prop: "b",
  },
  {
    label: "综合得分",
    prop: "d",
    slot: "dSlot",
  },

  {
    label: "级别",
    prop: "e",
  },
]);
const data3 = ref([
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "61.1",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "58.6",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "41.1",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);
const value = ref(40);
const columns4 = ref([
  {
    label: "二级能力",
    prop: "a",
    width: 260,
  },
  {
    label: "三级能力",
    prop: "b",
  },
  {
    label: "能力得分",
    prop: "c",
  },
  {
    label: "新手入门",
    prop: "d",
  },

  {
    label: "合格执行者",
    prop: "e",
  },
  {
    label: "骨干能手",
    prop: "f",
  },
  {
    label: "资深专家",
    prop: "g",
  },
  {
    label: "业内标杆",
    prop: "h",
  },
]);
const data4 = ref([
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "已完成",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);
const columns5 = ref([
  {
    label: "二级能力",
    prop: "a",
    width: 260,
  },
  {
    label: "三级能力",
    prop: "b",
  },
  {
    label: "能力得分",
    prop: "c",
  },
  {
    label: "新手入门",
    prop: "d",
  },

  {
    label: "合格执行者",
    prop: "e",
  },
  {
    label: "骨干能手",
    prop: "f",
  },
  {
    label: "资深专家",
    prop: "g",
  },
  {
    label: "业内标杆",
    prop: "h",
  },
]);
const data5 = ref([
  {
    a: "供应链计划管理部",
    b: "王海峰",
    c: "主计划",
    d: "已完成",
    e: "39",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);

const columns6 = ref([
  {
    label: "姓名",
    prop: "b",
  },
  {
    label: "部门",
    prop: "a",
  },
  {
    label: "岗位",
    prop: "e",
  },
  {
    label: "综合得分",
    prop: "d",
    slot: "dSlot",
  },
]);
const data6 = ref([
  {
    a: "采购部",
    b: "王海峰",
    c: "主计划",
    d: "61.1",
    e: "采购工程师",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
  {
    a: "采购部",
    b: "王海峰",
    c: "主计划",
    d: "58.6",
    e: "采购工程师",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
  {
    a: "采购部",
    b: "王海峰",
    c: "主计划",
    d: "41.1",
    e: "采购工程师",
    f: "39",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);
const marksStyle = ref({
  fontSize: "12px",
  color: "#40A0FF",
  width: "38px",
  height: "18px",
  lineHeight: "18px",
  background: "#D1E5FA",
  borderRadius: "93px 93px 93px 93px",
  textAlign: "center",
});
const marks = ref({
  0: {
    style: marksStyle.value,
    label: "基本",
  },
  50: {
    style: marksStyle.value,
    label: "平均",
  },
  100: {
    style: marksStyle.value,
    label: "领先",
  },
});

const closeInfo = (i) => {
  emits("closeReportInfo", true);
};

const circleColor = (v, o) => {
  if (o == "g") {
    if (v < 50) {
      return "green_bg2";
    } else if (v >= 50) {
      return "green_bg1";
    }
  } else if (o == "r") {
    if (v < 50) {
      return "red_bg2";
    } else if (v >= 50) {
      return "red_bg1";
    }
  }
};
onMounted(() => {});
</script>
<template>
  <div class="com_right_wrap right2_wrap">
    <div class="t">能力优势与短板</div>
    <div class="info_section_wrap">
      <div class="section_title justify-between">
        <div class="page-title-line">相对较好的能力</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>

      <Table
        :roundBorder="false"
        :columns="columns3"
        :data="data3"
        headerColor
        showIndex
      >
        <template v-slot:dSlot="scope">
          <span class="circle" :class="circleColor(scope.row.d, 'g')">{{
            scope.row.d
          }}</span>
        </template>
        <template #default="scope">
          <el-table-column
            class=""
            width="420px"
            align="center"
            label="所有参评企业中的位置"
          >
            <el-slider v-model="value" :marks="marks" disabled />
          </el-table-column>
        </template>
        <!--  -->
      </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">
        对应能力表现较好的人员（战略寻源—采购招投标）
      </div>
      <div class="justify-between">
        <div class="half_wrap">
          <Table
            :roundBorder="false"
            :columns="columns6"
            :data="data6"
            headerColor
            showIndex
          >
            <template v-slot:dSlot="scope">
              <span class="circle" :class="circleColor(scope.row.d, 'g')">{{
                scope.row.d
              }}</span>
            </template>
          </Table>
        </div>
        <div class="half_wrap">
          <Table
            :roundBorder="false"
            :columns="columns6"
            :data="data6"
            headerColor
            showIndex
          >
            <template v-slot:dSlot="scope">
              <span class="circle" :class="circleColor(scope.row.d, 'g')">{{
                scope.row.d
              }}</span>
            </template>
          </Table>
        </div>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="section_title justify-between">
        <div class="page-title-line">相对较差的能力</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <Table
        :roundBorder="false"
        :columns="columns3"
        :data="data3"
        headerColor
        showIndex
      >
        <template v-slot:dSlot="scope">
          <span class="circle" :class="circleColor(scope.row.d, 'r')">{{
            scope.row.d
          }}</span>
        </template>
        <template #default="scope">
          <el-table-column
            class=""
            width="420px"
            align="center"
            label="所有参评企业中的位置"
          >
            <el-slider
              class="red_el-slider"
              v-model="value"
              :marks="marks"
              disabled
            />
          </el-table-column>
        </template>
        <!--  -->
      </Table>
      <div class="desc_title">可能导致的问题（采购招投标）</div>
      <div class="section_box_wrap">
        1、招标失败率升高：资格条件设置不科学、需求参数偏离市场实际‌，投标人不足或有效投标人不足，导致采购计划中断；<br />
        2、合规风险加剧：虚假招标、围标串标等违规操作频发<br />
        3、成本控制失效：低价中标导致质量缺陷，后期维修成本激增‌，预算编制失误造成超支，资金使用效率低下‌‌；<br />
        4、供应商质量不稳定：关系中标替代实力筛选，引入劣质供应商‌，供应商履约能力不足引发供货延迟、质量问题‌；<br />
        5、‌项目推进延误：流标/废标导致采购周期延长，合同纠纷处理耗时影响施工进度‌；<br />
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">
        对应能力表现相对较差的人员（战略寻源—供应商培育）
      </div>
      <div class="justify-between">
        <div class="half_wrap">
          <Table
            :roundBorder="false"
            :columns="columns6"
            :data="data6"
            headerColor
            showIndex
          >
            <template v-slot:dSlot="scope">
              <span class="circle" :class="circleColor(scope.row.d, 'g')">{{
                scope.row.d
              }}</span>
            </template>
          </Table>
        </div>
        <div class="half_wrap">
          <Table
            :roundBorder="false"
            :columns="columns6"
            :data="data6"
            headerColor
            showIndex
          >
            <template v-slot:dSlot="scope">
              <span class="circle" :class="circleColor(scope.row.d, 'g')">{{
                scope.row.d
              }}</span>
            </template>
          </Table>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
