<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <slot></slot>
        <div class="page_second_title">目标与结果综合得分</div>
        <el-row :gutter="16">
            <el-col :span="6">
                <div class="item_title">综合得分</div>
                <div class="text_center score">
                    <customProcess :size="250" :num="comprehensiveScore"/>
                    <!-- <el-progress
                        type="circle"
                        :stroke-width="20"
                        :stroke-linecap="'butt'"
                        :percentage="comprehensiveScore"
                        :format="progressFromat"
                    ></el-progress> -->
                </div>
            </el-col>
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
            <!-- <el-col :span="24">
                <listComp
                    :options="listConfig"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col> -->
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        getObjectivesResultsList,
        getEnqObjective,
        getEnqObjectiveResult
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import listComp from "./components/listComp.vue";
    import customProcess from "@/components/talent/common/customProcess.vue";

    export default {
        name: "DN07_targetRes",
        props: {
            enqId: String,
            orgCode: String,
            isPdf: {
                type: Boolean,
                default: false,
            },
        },
        components: { tableComps, listComp,customProcess },
        data() {
            return {
                comprehensiveScore:0,
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "目标与结果得分分布情况",
                        elSpan: 16,
                        chartHeight: "200",
                        chartType: "XBar",
                        dataKey: "kpiPopulationDistribution",
                    },
                ],
                listArr: [
                    {
                        title: "组织目标信息",
                        ajaxUrl: getEnqObjective,
                        columns: [
                            {
                                label: "组织",
                                prop: "orgName",
                                width: 180
                            },
                            {
                                label: "姓名",
                                prop: "userName",
                                width: 130
                            },
                            
                            {
                                label: "目标名称",
                                prop: "objectiveName",
                            },
                            {
                                label: "自评得分",
                                prop: "selfScore",
                                width: 80
                            },
                            {
                                label: "上级评价",
                                prop: "supScore",
                                width: 80
                            },
                            {
                                label: "领导评价",
                                prop: "leaderScore",
                                width: 80
                            },
                            {
                                label: "综合得分",
                                prop: "overallScore",
                                width: 80
                            },
                            {
                                label: "综合评价",
                                prop: "overallMerit",
                                width: 80
                            },
                        ],
                    },
                    {
                        title: "关键结果信息",
                        ajaxUrl: getEnqObjectiveResult,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                                width: 100
                            },
                            {
                                label: "姓名",
                                prop: "userName",
                                width: 130
                            },
                            {
                                label: "岗位",
                                prop: "postName",
                                width: 100
                            },
                            {
                                label: "目标名称",
                                prop: "objectiveName",
                            },
                            {
                                label: "关键结果",
                                prop: "resultName",
                            },
                            {
                                label: "自评得分",
                                prop: "selfScore",
                                width: 80
                            },
                            {
                                label: "上级评价",
                                prop: "supScore",
                                width: 80
                            },
                            {
                                label: "领导评价",
                                prop: "leaderScore",
                                width: 80
                            },
                            {
                                label: "综合得分",
                                prop: "overallScore",
                                width: 80
                            },
                            {
                                label: "综合评价",
                                prop: "overallMerit",
                                width: 80
                            },
                        ],
                    },
                ],
            };
        },
        created() {
            this.getData();
        },
        mounted() {},
        methods: {
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                        padding: 115
                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getObjectivesResultsList(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.comprehensiveScore = res.data.comprehensiveScore;
                        this.initChart(res.data);
                    }
                });
            },
            progressFromat(val) {
                console.log(val);
                return val;
            },
        },
    };
</script>
 
<style scoped lang="scss">
.score {
        margin-top: 30px;
    }
    .item_content {
        margin-top: 32px;
        .content_item {
            .content_title {
                margin-bottom: 16px;
            }
            .content {
                color: #0099ff;
                font-size: 16px;
                font-weight: bold;
            }
        }
    }
</style>