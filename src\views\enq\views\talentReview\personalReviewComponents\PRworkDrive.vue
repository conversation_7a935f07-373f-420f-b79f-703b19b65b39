<template>
  <div class="post_process_main">
    <div class="departmen_main">
      <!-- <div class="marginT_30">
				<div class="page_second_title">部门职责分配</div>
      </div>-->
      <div class="work_drive_wrap marginT_16">
        <el-row class="header flex_row_start">
          <el-col :span="1" class="item index">序号</el-col>
          <el-col :span="12" class="item name">诊断项目</el-col>
          <el-col :span="2" class="item" v-for="(col, i) in columns" :key="i">{{ col.name }}</el-col>
          <el-col :span="1" class="item oper"></el-col>
        </el-row>
        <div class="main_content">
          <div
            class="main_row"
            :class="['row_' + index, { warning_row: setStyleArr.includes(index) }]"
            v-for="(row, index) in tableData"
            :key="index"
          >
            <el-row class="row flex_row_start">
              <el-col :span="1" class="item index">
                {{ index + 1 }}
              </el-col>
              <el-col :span="12" class="item name">
                {{ row.name }}
              </el-col>
              <el-col :span="2" class="item" v-for="(col, i) in columns" :key="i">
                <!-- <div
                  class="post_ipt"
                  @click="checkClick(row, col)"
                  :class="{ active: row.selectedCodes.includes(col.code) }"
                >
                  <el-icon class="icon_check el-icon-check">
                    <Check v-if="row.selectedCodes.includes(col.code)" />
                  </el-icon>
                </div> -->
                <div
                  class="check_item"
                  @click="checkClick(row, col)"
                  :class="{ active: row.selectedCodes.includes(col.code) }"
                >
                  <el-icon class="icon_check"><Check /></el-icon>
                </div>
              </el-col>
              <el-col :span="1" class="item oper"></el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
    <div class="oper_btn_wrap align_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn" v-show="currentIndex != currentFirstCode"
        >上一步</el-button
      >
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { getTopicList, saveWorkSurveySubmit } from '../../../request/api'
import { Check } from '@element-plus/icons-vue'
// import useUtil from '@/utils/utils' // 如有深拷贝工具请引入

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])
const useUser = useUserStore()

const tableData = ref([])
const columns = ref([])
const setStyleArr = ref([])
const modelId = ref('')

const userId = computed(() => useUser.userInfo.userId)

onMounted(() => {
  getEnqUserSurveyByIdFun()
})

function getEnqUserSurveyByIdFun() {
  let params = {
    enqId: props.enqId,
    modelType: 'drive'
  }
  getTopicList(params).then(res => {
    let data = res.data
    if (res.code == '200') {
      tableData.value = data.itemList
      columns.value = data.resultList
      modelId.value = data.modelId
    }
  })
}

function saveWorkSurveySubmitFn(stepType) {
  // 如有深拷贝工具请替换为深拷贝，否则直接用JSON
  let copyData = JSON.parse(JSON.stringify(tableData.value))
  let selectArr = []
  copyData.forEach(item => {
    selectArr.push({
      itemId: item.code,
      moduleCode: item.reasonCode,
      optionNbr: item.selectedCodes
    })
  })
  let params = {
    modelId: modelId.value,
    enqId: props.enqId,
    enqItemOptionRequests: selectArr
  }
  saveWorkSurveySubmit(params).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  })
}

function checkClick(row, col) {
  row.selectedCodes = []
  row.selectedCodes.push(col.code)
}

function prevBtn() {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      saveWorkSurveySubmitFn('prevStep')
    })
    .catch(action => {
      ElMessage.info(action === 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action === 'cancel') emit('prevStep')
    })
}

function nextBtn() {
  setStyleArr.value = checkData(tableData.value)
  if (setStyleArr.value.length > 0) {
    ElMessage.error('请先填写工作驱动！')
    let element = document.getElementsByClassName('row_' + setStyleArr.value[0])
    if (element && element[0]) {
      element[0].scrollIntoView({ block: 'center' })
    }
    return
  }
  saveWorkSurveySubmitFn('nextStep')
}

function checkData(data) {
  let resultArr = []
  for (let index = 0; index < data.length; index++) {
    const obj = data[index]
    if (obj.selectedCodes && obj.selectedCodes.length === 0) {
      resultArr.push(index)
    }
  }
  return resultArr
}
</script>

<style scoped lang="scss">
// .post_process_table {
// }
.oper_btn_wrap {
  text-align: center;
}
.warning_row {
  background-color: red !important;
}
.post_column {
  margin-right: 4px;
  text-align: center;
}

.post_ipt {
  display: inline-block;
  width: 30px;
  height: 30px;
  border: 1px solid #e5e5e5;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  color: #000;
  margin-right: 3px;

  &.active {
    background: #0099ff;
    color: #fff;
    font-size: 20px;

    .icon_check {
      visibility: visible;
    }
  }

  .icon_check {
    visibility: hidden;
  }
}

.el-table__body tr {
  &:nth-child(even) {
    background: #fff;
  }

  td {
    padding: 7px 0;
  }
}

.el-table th > .cell {
  padding-left: 10px;
  padding-right: 10px;
  margin-right: 4px;
}

.el-table__row {
  .cell {
    padding: 0;
  }

  td {
    &:first-child {
      padding-left: 20px;
    }
  }
}

.el-table th {
  background-color: #ebf4ff !important;
}

.el-table thead {
  color: #0099ff;
}

.el-table tr {
  &:nth-child(even) {
    background: #f4f4f4;
  }
}

.work_drive_wrap {
  .item {
    padding: 0 4px;
    flex: 0.7;
    text-align: center;
    border-bottom: 1px solid #ebeef5;
    justify-items: center;
    display: flex;
    align-items: center;
    &.index {
      text-align: center;
      flex: 0.5;
    }
    &.name {
      flex: 4;
      text-align: left;
    }
    &.oper {
      flex: 0.5;
      .oper_btn {
        padding: 3px 6px;
        font-size: 12px;
        border: 1px solid #0099fd;
        color: #0099fd;
        line-height: 1;
        border-radius: 3px;
        cursor: pointer;
        background: #fff;
        &.confirm {
          background: #0099fd;
          color: #fff;
        }
      }
    }
  }
  .header {
    line-height: 25px;
    background-color: #ebf4ff;
    align-items: stretch;
    .item {
      display: flex;
      align-items: center;
    }
  }
  .main_content {
    max-height: 400px;
    overflow-y: auto;
    .main_row {
      align-items: center;
      line-height: 40px;
      &:nth-of-type(even) {
        background-color: #f5f7fa;
      }
    }
  }
}

.check_item {
  display: inline-flex;
  width: 24px;
  height: 24px;
  justify-content: center;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #409eff;
  }

  &.active {
    background-color: #409eff;
    border-color: #409eff;

    .icon_check {
      display: inline-block;
      color: #fff;
    }
  }

  .icon_check {
    display: none;
    font-size: 16px;
    line-height: 22px;
  }
}
</style>
