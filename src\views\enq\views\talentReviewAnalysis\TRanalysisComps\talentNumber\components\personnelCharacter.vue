<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">性别</div>
          <div class="content_item_content" id="gender_chart"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">婚姻状况</div>
          <div class="content_item_content" id="marital_chart"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">年龄分布</div>
          <div class="content_item_content" id="age_chart"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title">省份分布</div>
          <div class="content_item_content" id="province_chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { characteristics } from '../../../../../request/api.js'
import asideFilterBar from '../../asideFilterBar.vue'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')

const genderData = reactive({
  data: []
})

const maritalData = reactive({
  data: []
})

const ageData = reactive({
  data: []
})

const provinceData = reactive({
  data: []
})

const filterData = ref([])

const initChart = () => {
  echartsRenderPage('gender_chart', 'Ring', '230', '200', genderData)
  echartsRenderPage('marital_chart', 'Ring', '230', '200', maritalData)
  echartsRenderPage('age_chart', 'YBar', '230', '200', ageData)
  echartsRenderPage('province_chart', 'XBar', '700', '250', provinceData)
}

const characteristicsFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await characteristics(params)
    if (res.code == 200) {
      const data = res.data
      data.age.map(item => {
        item.name = item.age
        item.value = item.value + '%'
      })
      ageData.data = data.age

      data.gender.map(item => {
        item.value = item.value + '%'
      })
      genderData.data = data.gender

      data.marital.map(item => {
        item.value = item.value + '%'
      })
      maritalData.data = data.marital

      provinceData.data = data.region
      initChart()
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  characteristicsFun()
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  characteristicsFun()
})
</script>

<style scoped lang="scss"></style>
