<template>
    <div class="report_cover">
        <img
            class="report_cover_bg"
            :src="type == 'D' ? orgReportCover : userReportCover"
            alt=""
            srcset=""
        />

        <div class="report_info">
            <div class="user_name" v-if="type == 'P'">
                {{ userInfo.userName }}
            </div>
            <div class="post_name" v-if="type == 'P'">
                {{ userInfo.postName }}
            </div>
            <div class="org_name" :class="type == 'P' ? 'user' : ''">
                {{ type == "P" ? userInfo.orgName : orgName }}
            </div>
            <div class="report_date">{{ beginDate | removeTime }}</div>
        </div>
    </div>
</template>

<script>
    import orgReportCover from "../../../../../../public/images/report_cover_bg.jpg";
    import userReportCover from "../../../../../../public/images/report_cover_bg_P.png";
    export default {
        name: "reportCover",
        props: {
            type: String, // D:组织报告 P: 个人报告
            beginDate: String,
            orgName: String,
            userInfo: Object,
        },
        data() {
            return {
                orgReportCover: orgReportCover,
                userReportCover: userReportCover,
            };
        },
    };
</script>

<style lang="scss" scoped>
    .report_cover {
        position: relative;
        width: 100%;
        // height: 2000px;
        .report_cover_bg {
            width: 100%;
            // height: 1800px;
            display: block;
        }
        .report_type {
            position: absolute;
            top: 48.526%;
            right: 0;
            width: 45%;
            font-size: 70px;
            line-height: 130px;
            padding: 0 55px 0 60px;
            background-color: #017ba0;
            border-radius: 65px 0 0 65px;
            color: #fff;
        }
        .report_info {
            color: #fff;
            font-size: 24px;
        }
        .user_name {
            position: absolute;
            top: 61.653%;
            left: 59.523%;
            font-size: 28px;
        }
        .post_name {
            position: absolute;
            top: 64.5%;
            left: 59.523%;
        }
        .org_name {
            position: absolute;
            top: 63.71%;
            left: 59.523%;
            font-size: 28px;
            &.user {
                font-size: 24px;
                top: 70%;
            }
        }
        .report_date {
            position: absolute;
            bottom: 5.65%;
            width: 100%;
            text-align: center;
            color: #fff;
            font-size: 22px;
        }
    }
</style>