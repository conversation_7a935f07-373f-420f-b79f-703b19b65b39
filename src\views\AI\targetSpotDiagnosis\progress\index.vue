<script setup>
defineOptions({ name: 'diagnosisProgress' })

const departData = [
  {
    level: 'H公司',
    level2: '',
    level3: '供应链计划......',
    level4: '',
    manager: '',
    people: 21,
    complete: 21,
    incomplete: 0,
    noStart: 0,
    percentage: 100
  }
]
const singleData = [
  {
    department: '供应链计划管理部',
    name: '王海峰',
    post: '主计划',
    status: '已完成',
    evaluate: 39,
    evaluated: 0,
    noevaluate: 0,
    time: '2024-09-26 09:59:59',
    percentage: 100
  }
]
const form = ref({
  department: '',
  user: '',
  status: null
})
const activeName = ref('1')

const handleClick = (tab, event) => {
  console.log(tab, event)
  activeName.value = tab.name
}
</script>

<template>
  <div class="page-container">
    <div class="page-back">
      <div class="back-icon">
        <SvgIcon name="icon-back" class="icon"></SvgIcon>
        <div class="back-text">返回</div>
      </div>
      <div class="progress-name">当前正进行的诊断项目</div>
    </div>
    <div class="project-card">
      <div class="tag">流程赋能诊断</div>
      <div class="info-card">
        <div class="top">
          <div class="title">供应链整体能力诊断</div>
          <div class="progress-tag">进行中</div>
        </div>
        <div class="bottom">
          <span class="time">2022/05/22~2022/06/22</span>
          <span class="time">王伟 13856063220</span>
        </div>
      </div>
      <div class="module-card">
        <div class="item">
          <span class="item-tag">模块</span>
          <span class="text">12</span>
        </div>
        <div class="item">
          <span class="item-tag">组件</span>
          <span class="text">126</span>
        </div>
        <div class="item">
          <span class="item-tag">DNA</span>
          <span class="text">1221</span>
        </div>
      </div>
      <div class="progress-card">
        <el-progress type="circle" :percentage="70" :width="74" :stroke-width="10" />
      </div>
      <div class="number-card">
        <div class="item">
          <div class="item-tag">参评人数</div>
          <div class="number"><span>21</span>人</div>
        </div>
        <div class="item">
          <div class="item-tag">已完成</div>
          <div class="number"><span>21</span>人</div>
        </div>
        <div class="item">
          <div class="item-tag">进行中</div>
          <div class="number"><span>0</span>人</div>
        </div>
        <div class="item">
          <div class="item-tag">未开始</div>
          <div class="number"><span>0</span>人</div>
        </div>
      </div>
      <div class="operate-card">
        <span>收起</span>
      </div>
    </div>
    <div class="table-card">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="部门进度" name="1">
          <div class="department-card">
            <el-button type="primary" class="btn">部门进度统计</el-button>
            <div class="app-table">
              <el-table :data="departData">
                <el-table-column type="index" label="序号" width="80" align="center" />
                <el-table-column prop="level" label="一级组织" width="120" align="center"> </el-table-column>
                <el-table-column prop="level2" label="二级组织" width="" align="center"> </el-table-column>
                <el-table-column prop="level3" label="三级组织" width="120" align="center"> </el-table-column>
                <el-table-column prop="level4" label="四级组织" width="" align="center"> </el-table-column>
                <el-table-column prop="manager" label="部门负责人" align="center"> </el-table-column>
                <el-table-column prop="people" label="参评人数" align="center"> </el-table-column>
                <el-table-column prop="complete" label="已完成" align="center"> </el-table-column>
                <el-table-column prop="incomplete" label="未完成" align="center"> </el-table-column>
                <el-table-column prop="noStart" label="未开始" align="center"> </el-table-column>
                <el-table-column prop="percentage" label="完成率" align="center"> </el-table-column>
                <el-table-column label="完成率" width="150">
                  <template #default="scope">
                    <el-progress :percentage="scope.row.percentage" color="#40A0FF" :stroke-width="7" />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="个人进度" name="2">
          <div class="single-card">
            <div class="search-card">
              <div class="form">
                <el-form :inline="true" :model="form" class="demo-form-inline">
                  <el-form-item label="部门">
                    <el-select v-model="form.department" placeholder="请选择" clearable style="width: 280px">
                      <!-- <el-option label="Zone one" value="shanghai" />
                    <el-option label="Zone two" value="beijing" /> -->
                    </el-select>
                  </el-form-item>
                  <el-form-item label="人员">
                    <el-input v-model="form.user" placeholder="请按名称查询" style="width: 280px" />
                  </el-form-item>
                  <el-form-item label="状态">
                    <el-select v-model="form.status" placeholder="请选择人员状态" clearable style="width: 280px">
                      <el-option label="已完成" value="0" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="onSubmit">查询</el-button>
                  </el-form-item>
                </el-form>
              </div>
              <el-button type="primary" class="export-btn">导出</el-button>
            </div>
            <div class="app-table">
              <el-table :data="singleData">
                <el-table-column type="index" label="序号" width="80" align="center" />
                <el-table-column prop="department" label="部门名称" width="250"> </el-table-column>
                <el-table-column prop="name" label="姓名"> </el-table-column>
                <el-table-column prop="post" label="岗位"> </el-table-column>
                <el-table-column prop="status" label="状态"> </el-table-column>
                <el-table-column prop="evaluate" label="需评价"> </el-table-column>
                <el-table-column prop="evaluated" label="已评价"> </el-table-column>
                <el-table-column prop="noevaluate" label="未评价"> </el-table-column>
                <el-table-column prop="time" label="最后登录时间" width="200"> </el-table-column>
                <el-table-column label="完成率" width="150">
                  <template #default="scope">
                    <el-progress :percentage="scope.row.percentage" color="#40A0FF" :stroke-width="7" />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div class="qrcode-card">
        <div class="top">还可以继续邀请 10人</div>
        <div class="bottom">
          <div class="text">您可以复制链接或下载二维码，分享至微信群</div>
          <div class="qr-info">
            <div class="link-card">
              <div class="link-text">测评链接：</div>
              <div class="link-input">https://ceping.xiyiqq.com/710350</div>
              <el-button type="primary" class="copy-btn">一键复制</el-button>
            </div>
            <div class="qr-card">
              <img src="" alt="" class="qr-img" />
              <div class="info">
                <div class="qr-text">测评二维码</div>
                <el-button type="primary" class="copy-btn">下载</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="btn-card">
        <el-button type="primary" class="btn-main">下载测评进度信息，督促相关人员加快进度</el-button>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.page-container {
  width: 100%;
  height: 100%;
  padding: 22px 20px;
  background-color: #fff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px;
  .page-back {
    @include flex-center(row, flex-start, center);
    margin-bottom: 21px;
    .back-icon {
      @include flex-center(row, flex-start, center);
      .icon {
        font-size: 12px;
        margin-right: 6px;
      }
      .back-text {
        margin-right: 20px;
        font-size: 14px;
        color: #888888;
      }
    }
    .progress-name {
      font-size: 16px;
      color: #53a9f9;
    }
  }

  .project-card {
    @include flex-center(row, space-around, stretch);
    width: 100%;
    height: 110px;
    margin-bottom: 20px;
    padding: 10px 17px 10px 22px;
    border-radius: 8px;
    border: 1px solid #c6dbf3;
    .tag {
      height: 23px;
      left: 23px;
      padding: 0 14px;
      margin-top: 32px;
      border-radius: 5px;
      font-size: 14px;
      color: #40a0ff;
      background-color: #e3f1ff;
    }
    .info-card {
      padding-top: 14px;
      padding-right: 56px;
      border-right: 1px solid #d8d8d8;
      .top {
        @include flex-center(row, flex-start, center);
        margin-bottom: 15px;
        .title {
          margin-right: 13px;
          font-size: 18px;
          color: #3d3d3d;
          font-weight: 500;
        }
        .progress-tag {
          padding: 3px 8px;
          font-size: 12px;
          line-height: 12px;
          color: #ffffff;
          background: #40a0ff;
          border-radius: 3px;
        }
      }
      .bottom {
        .time {
          margin-right: 44px;
          font-size: 14px;
          line-height: 12px;
          color: #acacac;
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
    .module-card {
      padding-top: 4px;
      padding-right: 25px;
      border-right: 1px solid #d8d8d8;
      .item {
        margin-bottom: 5px;
        &:last-child {
          margin-bottom: 0;
        }
        .item-tag {
          padding: 1px 9px;
          margin-right: 11px;
          font-size: 12px;
          line-height: 21px;
          border-radius: 46px;
          color: #40a0ff;
          background-color: #e3f1ff;
        }
        .text {
          color: #40a0ff;
          font-size: 16px;
        }
      }
    }
    .progress-card {
      padding-top: 6px;
      padding-right: 33px;
      border-right: 1px solid #d8d8d8;
    }
    .number-card {
      @include flex-center(row, flex-start, center);
      padding-right: 22px;
      border-right: 1px solid #d8d8d8;
      .item {
        margin-right: 40px;
        &:last-child {
          margin-right: 0;
        }
        .item-tag {
          width: 80px;
          text-align: center;
          margin-bottom: 17px;
          line-height: 20px;
          font-size: 14px;
          color: #40a0ff;
          border-radius: 42px;
          background-color: #cbe4fd;
        }
        .number {
          text-align: center;
          font-size: 14px;
          color: #3d3d3d;
          span {
            font-size: 30px;
            font-weight: 500;
            color: #40a0ff;
          }
        }
      }
    }
    .operate-card {
      @include flex-center(column, center, center);
      font-size: 14px;
      color: #40a0ff;
    }
  }

  .table-card {
    :deep(.demo-tabs) {
      .el-tabs__item {
        border-bottom: 2px solid transparent;
        padding: 0 30px;
        margin-right: 22px;
        font-size: 14px;
        font-weight: 500;
      }
      .el-tabs__item.is-active {
        border-bottom: 2px solid #40a0ff;
      }
      .el-tabs__active-bar {
        height: 0;
      }
    }
    .app-table {
      width: 100%;
      margin-top: 12px;
      :deep(.el-table) {
        th.el-table__cell {
          background-color: #eff3f6;
          .cell {
            font-size: 14px;
            color: #86add3;
          }
        }
      }
    }

    .search-card {
      @include flex-center(row, space-between, flex-start);
    }

    .qrcode-card {
      margin-top: 30px;
      .top {
        width: 100%;
        height: 40px;
        background: #eff3f6;
        border-radius: 6px 6px 0px 0px;
        padding: 0 20px;
        line-height: 40px;
        font-size: 16px;
        color: #40a0ff;
      }
      .bottom {
        width: 100%;
        height: 204px;
        padding: 13px 20px 0;
        border: 1px solid #dddddd;
        border-top: none;
        .text {
          margin-bottom: 30px;
          font-size: 14px;
          color: #666666;
        }
        .qr-info {
          @include flex-center(row, center, stretch);
          .link-card {
            padding-right: 60px;
            border-right: 1px solid #d8d8d8;
            .link-text {
              margin-bottom: 8px;
              font-size: 14px;
              color: #3d3d3d;
            }
            .link-input {
              width: 310px;
              height: 36px;
              padding: 0 10px;
              margin-bottom: 12px;
              line-height: 36px;
              font-size: 14px;
              color: #333333;
              border: 1px solid #dddddd;
            }
          }
          .qr-card {
            padding-left: 60px;
            @include flex-center(row, center, flex-end);
            .qr-img {
              width: 110px;
              height: 100%;
              margin-right: 20px;
            }
            .info {
              .qr-text {
                margin-bottom: 34px;
                font-size: 14px;
                color: #3d3d3d;
              }
            }
          }
          .copy-btn {
            width: 122px;
            height: 36px;
            font-size: 14px;
          }
        }
      }
    }
    .btn-card {
      text-align: center;
      .btn-main {
        margin-top: 20px;
        width: 366px;
        height: 36px;
      }
    }
  }
}
</style>
