<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <div class="page_second_title">KPI综合得分</div>
        <el-row :gutter="16">
            <el-col :span="8">
                <div class="item_content flex_row_start">
                    <div class="content_item">
                        <div class="content_title">综合得分</div>
                        <div class="content">{{comprehensiveScore}}分</div>
                    </div>
                </div>
            </el-col>
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
            <!-- <el-col :span="24">
                <listComp
                    :options="listConfig"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col> -->
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    import {
        getKPIEvaluate,
        getKPIIndicatorInformation,
    } from "../../../../request/api.js";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    import listComp from "./components/listComp.vue";

    export default {
        name: "DN08_KPIEval",
        props: {
            enqId: String,
            orgCode: String,
            isPdf: {
                type: Boolean,
                default: false,
            },
        },
        components: { tableComps, listComp },
        data() {
            return {
                comprehensiveScore:null,
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "KPI 综合表现分布",
                        elSpan: 16,
                        chartHeight: "200",
                        chartType: "XBar",
                        dataKey: "kpiPerformanceDistribution",
                    },
                ],
                listArr: [
                    {
                        title: "KPI指标信息",
                        ajaxUrl: getKPIIndicatorInformation,
                        columns: [
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                             {
                                label: "岗位",
                                prop: "postName",
                            },
                             {
                                label: "指标名称",
                                prop: "kpiName",
                            },
                             {
                                label: "目标",
                                prop: "kpiObjective",
                            },
                             {
                                label: "实际表现",
                                prop: "kpiActual",
                            },
                             {
                                label: "上级评价",
                                prop: "supScore",
                            },
                             {
                                label: "HR总监",
                                prop: "userName2",
                            },
                             {
                                label: "综合得分",
                                prop: "overallScore",
                            },
                             {
                                label: "综合评价",
                                prop: "overallMerit",
                            },
                        ],
                    },
                    
                ],
            };
        },
        created() {
            this.getData();
        },
        mounted() {},
        methods: {
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getKPIEvaluate(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.comprehensiveScore = res.data.comprehensiveScore;
                        this.initChart(res.data);
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .item_content {
        margin-top: 32px;
        .content_item {
            .content_title {
                margin-bottom: 16px;
            }
            .content {
                color: #0099ff;
                font-size: 16px;
                font-weight: bold;
            }
        }
    }
</style>