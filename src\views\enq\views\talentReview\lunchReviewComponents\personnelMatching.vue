<template>
  <div class="personnel_matching_wrap clearfix">
    <div class="marginT_30">
      <tabsDiffentPane
        v-if="tabsData.length > 0"
        :isDefaultTheme="true"
        :tabsData="tabsData"
        :tabsDefault="tabsPanesign"
        @tabsChange="tabsChange"
      ></tabsDiffentPane>
      <div class="oper_btn_wrap">
        <el-button class="page_add_btn" type="primary" @click="Import">导入</el-button>
        <el-button class="page_add_btn" type="primary" @click="Export">导出</el-button>
      </div>

      <table-component
        ref="tableList"
        :needIndex="true"
        :tableData="tableData"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      ></table-component>

      <div class="btn_wrap align_center marginT_30" v-if="isEdit">
        <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
        <el-button class="page_confirm_btn" type="primary" @click="submit">下一步</el-button>
      </div>
    </div>
    <personnel-import-popUp
      v-model:show="showPopUp"
      @importSign="importSign"
      :getEnqId="getEnqId"
      :modelId="modelId"
      :tabsPaneInfo="tabsPaneInfo"
    ></personnel-import-popUp>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import tabsDiffentPane from '@/components/talent/tabsComps/tabsDiffentPane'
import personnelImportPopUp from './personnelImportPopUp.vue'
import {
  creatModel,
  personnelMatchingList,
  // positionMatchingList,
  exportperModuleDictionary,
  exportPosModuleDictionary,
  selectEnqModel
} from '../../../request/api'

// Props
const props = defineProps({
  getEnqId: {
    type: Function,
    required: true
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['prevStep', 'nextStep'])

// Refs
const tableList = ref(null)
const tabsPanesign = ref('')
const tabsPaneInfo = ref('')
const tabsData = ref([])
const enqId = ref('')
const modelId = ref('')
const tableData = ref({
  columns: [],
  data: [],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})
const showPopUp = ref(false)

// Methods
const selectEnqModelFun = async () => {
  try {
    const res = await selectEnqModel({ enqId: enqId.value })
    if (res.code == 200) {
      tabsPanesign.value = res.data[0].moduleCode
      modelId.value = res.data[0].modelId
      tabsData.value = res.data.map(item => ({
        label: item.modelName,
        name: item.modelId,
        moduleCode: item.moduleCode,
        modelId: item.modelId
      }))
      tabsPaneInfo.value = [tabsData.value[0]]
      await personnelMatchingListFun()
    }
  } catch (error) {
    console.error('Error in selectEnqModelFun:', error)
  }
}

const tabsChange = async data => {
  tabsPanesign.value = data.name
  tabsPaneInfo.value = tabsData.value.filter(r => r.modelId == data.name)
  modelId.value = tabsPaneInfo.value[0].modelId
  tableData.value.page = {
    current: 1,
    size: 10,
    total: 0
  }
  if (tabsPanesign.value == 'XZW') {
    await positionMatchingListFun()
  } else {
    await personnelMatchingListFun()
  }
}

const personnelMatchingListFun = async () => {
  try {
    const res = await personnelMatchingList({
      enqId: enqId.value,
      modelId: modelId.value,
      current: tableData.value.page.current,
      size: tableData.value.page.size
    })

    console.log('res', res)

    if (res.code == 200) {
      const columnsFixed = [
        { label: '一级部门', prop: 'oneLevelName' },
        { label: '二级部门', prop: 'twoLevelName' },
        { label: '三级部门', prop: 'threeLevelName' },
        { label: '四级部门', prop: 'fourLevelName' },
        { label: '姓名', prop: 'userName' },
        { label: '工号', prop: 'employeeCode' },
        { label: '岗位', prop: 'postName' },
        { label: '职层', prop: 'jobLevelName' },
        { label: '职级', prop: 'jobClassName' }
      ]

      for (const [key, value] of Object.entries(res.data.moduleDictionary)) {
        columnsFixed.push({
          label: value,
          prop: key
        })
      }

      tableData.value.columns = columnsFixed
      tableData.value.data = res.data.enqUserInfo.map(item => ({
        ...item,
        ...item.userDicMap
      }))
      tableData.value.page.total = res.total
    }
  } catch (error) {
    console.error('Error in personnelMatchingListFun:', error)
  }
}

const positionMatchingListFun = async () => {
  try {
    const res = await positionMatchingList({
      enqId: enqId.value,
      modelId: modelId.value,
      current: tableData.value.page.current,
      size: tableData.value.page.size
    })

    if (res.code == 200) {
      const columnsFixed = [
        { label: '族群', prop: 'parentJobClassName' },
        { label: '序列', prop: 'jobClassName' },
        { label: '职位编码', prop: 'jobCode' },
        { label: '职位名称', prop: 'jobName' },
        { label: '职层', prop: 'jobLevelName' },
        { label: '职等', prop: 'jobGradeName' }
      ]

      for (const [key, value] of Object.entries(res.data.moduleDictionary)) {
        columnsFixed.push({
          label: value,
          prop: key
        })
      }

      tableData.value.columns = columnsFixed
      tableData.value.data = res.data.enqUserInfo.map(item => ({
        ...item,
        ...item.userDicMap
      }))
      tableData.value.page = res.total
    }
  } catch (error) {
    console.error('Error in positionMatchingListFun:', error)
  }
}

const handleCurrentChange = async curr => {
  tableData.value.page.current = curr
  if (tabsPanesign.value == 'XZW') {
    // await positionMatchingListFun()
  } else {
    await personnelMatchingListFun()
  }
}

const handleSizeChange = async size => {
  tableData.value.page.current = 1
  tableData.value.page.size = size
  if (tabsPanesign.value == 'XZW') {
    // await positionMatchingListFun()
  } else {
    await personnelMatchingListFun()
  }
}

const Import = () => {
  showPopUp.value = true
}

const importSign = async sign => {
  if (sign) {
    tableData.value.page.current = 1
    tableData.value.page.size = 10
    if (tabsPanesign.value == 'XZW') {
      // await positionMatchingListFun()
    } else {
      personnelMatchingListFun()
    }
  }
}

const Export = () => {
  if (tabsPanesign.value == 'XZW') {
    exportPosModuleDictionaryFun()
  } else {
    exportperModuleDictionaryFun()
  }
}

const exportperModuleDictionaryFun = async () => {
  try {
    const res = await exportperModuleDictionary({
      enqId: enqId.value,
      modelId: modelId.value
    })
    downloadFile(res, tabsPaneInfo.value[0].label + '列表.xlsx')
  } catch (error) {
    console.error('Error in exportperModuleDictionaryFun:', error)
  }
}

const exportPosModuleDictionaryFun = async () => {
  try {
    const res = await exportPosModuleDictionary({
      enqId: enqId.value,
      modelId: modelId.value
    })
    downloadFile(res, tabsPaneInfo.value[0].label + '列表.xlsx')
  } catch (error) {
    console.error('Error in exportPosModuleDictionaryFun:', error)
  }
}

const downloadFile = (res, fileName) => {
  const blob = new Blob([res])
  const elink = document.createElement('a')
  elink.download = fileName
  elink.style.display = 'none'
  elink.href = URL.createObjectURL(blob)
  document.body.appendChild(elink)
  elink.click()
  URL.revokeObjectURL(elink.href)
  document.body.removeChild(elink)
}

const prevBtn = () => {
  emit('prevStep')
}

const submit = () => {
  emit('nextStep')
}

// Lifecycle
onMounted(() => {
  enqId.value = props.getEnqId()
  selectEnqModelFun()
})
</script>

<style scoped lang="scss"></style>
