<template>
    <div class="report_section work_activities_main">
        <div class="marginT_30"></div>
        <div class="page_second_title">本岗位工作活动</div>
        <div class="work_activities_content">
            <el-table :data="activityList" style="width: 100%">
                <el-table-column prop="jobActivityType" label="活动类型" width="120">
                    <template slot-scope="scope">
                        <el-select
                            class="select_style"
                            v-model="scope.row.jobActivityType"
                            disabled
                        >
                            <el-option
                                v-for="item in jobActivityOptions"
                                :label="item.codeName"
                                :value="item.dictCode"
                                :key="item.dictCode"
                            ></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="jobActivityName" label="工作任务名称"></el-table-column>
                <el-table-column prop="workType" label="在司/出差" width="120">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.workType" placeholder>
                            <el-option
                                v-for="item in workTypeoptions"
                                :label="item.codeName"
                                :value="item.dictCode"
                                :key="item.dictCode"
                            ></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="timePercentage" label="时间占比" width="150">
                    <template slot-scope="scope">
                        <el-input
                            v-model="scope.row.timePercentage"
                            min="0"
                            type="number"
                            placeholder
                        >
                            <template slot="append">%</template>
                        </el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="overtimeFlag" label="需要加班" width="120">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.overtimeFlag" placeholder>
                            <el-option
                                :label="item.codeName"
                                :value="item.dictCode"
                                v-for="item in yesOrNoOptions"
                                :key="item.dictCode"
                            ></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="overtimeFrequency" label="加班频率" width="180">
                    <template slot-scope="scope">
                        <el-select
                            v-model="scope.row.overtimeFrequency"
                            :disabled="scope.row.overtimeFlag == 'N'"
                            placeholder
                        >
                            <el-option
                                v-for="item in overTimeOptions"
                                :label="item.codeName"
                                :value="item.dictCode"
                                :key="item.dictCode"
                            ></el-option>
                        </el-select>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="page_second_title marginT_30">本岗主要对内的协同岗位（与哪些岗位有工作协同）</div>
        <div class="collaboration_post_content">
            <div class="post_item_wrap flex_row_wrap_start">
                <div class="flex_row_betweens xt_wrap">
                    <div class="icon"></div>
                </div>
                <div class="post_item_main">
                    <div class="post_item" v-for="(item,index) in teamPost" :key="item.coopPostCode">
                        <div class="name">{{item.coopPostName}}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="page_second_title marginT_30">本岗主要对外的协同角色（与哪些外部角色打交道）</div>
        <div class="collaborative_role flex_row_start">
            <div class="icon"></div>
            <el-checkbox-group v-model="coopRoleList">
                <el-checkbox
                    v-for="item in coopRole"
                    :label="item.dictCode"
                    :key="item.dictCode"
                >{{item.codeName}}</el-checkbox>
            </el-checkbox-group>
        </div>
    </div>
</template>
<script>
    import { getEnqUserActivity } from "../../../../request/api.js";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "userRActivities",
        props: ["nextBtnText", "enqId", "userId", "postCode"],
        components: {
            tableComponent,
        },
        created() {
            // this.userId = this.$store.state.userInfo.userId;
            this.$getDocList([
                "OVERTIME_FREQUENCY",
                "YES_NO",
                "WORK_TYPE",
                "COOP_ROLE",
                "JOB_ACTIVITY_TYPE",
            ]).then((res) => {
                this.overTimeOptions = res.OVERTIME_FREQUENCY;
                this.yesOrNoOptions = res.YES_NO;
                this.workTypeoptions = res.WORK_TYPE;
                this.coopRole = res.COOP_ROLE;
                this.jobActivityOptions = res.JOB_ACTIVITY_TYPE;
            });
            this.getUserActivitysFun();
        },
        methods: {
            getUserActivitysFun() {
                let params = {
                    enqId: this.enqId,
                    userId: this.userId,
                    postCode: this.postCode,
                };
                getEnqUserActivity(params).then((res) => {
                    this.activityList = res.data.activityList;
                    this.teamPost = res.data.postCoopListI;
                    this.coopRoleList = res.data.postCoopListO;
                });
            },
        },
        data() {
            return {
                coopRoleList: [],
                jobActivityOptions: [],
                yesOrNoOptions: [],
                overTimeOptions: [],
                workTypeoptions: [],
                coopRole: [],
                teamPost: [],
                activityList: [],
            };
        },
    };
</script>
<style scoped lang="scss">
    .el-table__row {
        height: 45px;
    }
    .work_activities_content {
        margin-bottom: 16px;
        margin-top: 16px;
    }

    .collaboration_post_content {
        margin-bottom: 16px;
        .post_item_wrap {
            color: #525e6c;
            margin: 20px 0;
            .post_item_main {
                flex: 1;
                margin-left: 10px;
                display: flex;
                flex-wrap: wrap;
                .post_item {
                    width: 18%;
                    height: 35px;
                    display: flex;
                    padding: 0 10px;
                    justify-content: space-between;
                    line-height: 35px;
                    margin: 0 5px 8px 5px;
                    border: 1px solid #e4e7ed;
                    .name_icon {
                        width: 20px;
                        height: 20px;
                        background: #d2d2d2;
                        border-radius: 50%;
                        margin-top: 6px;
                        text-align: center;
                        line-height: 22px;
                        font-size: 16px;
                        color: #fff;
                        cursor: pointer;
                        &:hover {
                            opacity: 0.6;
                        }
                    }
                }
                .work_add_btn {
                    width: 18%;
                    height: 35px;
                    margin: 0 5px;
                    button {
                        width: 100%;
                        height: 100%;
                        border: 1px dashed #0099FF;
                        line-height: 35px;
                        padding: 0;
                    }
                }
            }

            .xt_wrap {
                .icon {
                    width: 85px;
                    height: 85px;
                    background: url("../../../../../../../public/icons/icon_xt.png")
                        no-repeat center;
                }
            }
        }
    }
    .collaborative_role {
        margin: 20px 0;
        .icon {
            width: 85px;
            height: 35px;
            background: url("../../../../../../../public/icons/icon_xt2.png")
                no-repeat center;
        }
        .el-checkbox-group {
            flex: 1;
            margin-left: 10px;
            label {
                width: 18%;
                height: 35px;
                border: 1px solid #e5e5e5;
                line-height: 35px;
                font-size: 12px;
                margin: 0 5px;
                &.is-checked {
                    border-color: #449CFF;
                }
                .el-checkbox__input {
                    float: right;
                    width: 30px;
                    height: 30px;
                    margin: 2px 2px;
                    &.is-checked {
                        .el-checkbox__inner {
                            background: none;
                        }
                        & + .el-checkbox__label {
                            color: #449CFF;
                        }
                    }
                    .el-checkbox__inner {
                        width: 100%;
                        height: 100%;
                        border: none;
                        &::after {
                            top: 4px;
                            left: 12px;
                            width: 10px;
                            height: 16px;
                            font-size: 30px;
                            border-width: 2px;
                            border-color: #449CFF;
                        }
                    }
                    .el-checkbox__original {
                        width: 33px;
                        height: 33px;
                    }
                }
            }
        }
    }
    // 去除input number类型 加减箭头
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }

    input[type="number"] {
        -moz-appearance: textfield;
    }

    .select_style .el-input.is-disabled .el-input__inner {
        background: transparent;
        color: #606266;
        border: none;
    }

    // 去除select下拉箭头
    .select_style .el-input__suffix {
        display: none;
    }
</style>
