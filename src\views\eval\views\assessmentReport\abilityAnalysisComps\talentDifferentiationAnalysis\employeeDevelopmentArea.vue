<template>
    <div class="employee_development_area_wrap">
        <div class="employee_development_area_main">
            <div class="talent_type_area">
                <p class="title page_third_title">分析图</p>
                <div class="item_line_wrap flex_row_betweens">
                    <div class="item_line_title">他评高</div>
                    <div class="item_area_wrap">
                        <p>潜力人才区</p>
                        <ul>
                            <li v-for="(item, index) in potential">
                                {{ item }}
                            </li>
                        </ul>
                    </div>
                    <div class="item_area_wrap item_area_02">
                        <p>优秀人才共识区</p>
                        <ul>
                            <li v-for="(item, index) in excellent">
                                {{ item }}
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="item_line_wrap flex_row_betweens">
                    <div class="item_line_title">他评低</div>
                    <div class="item_area_wrap item_area_03">
                        <p>待发展人才共识区</p>
                        <ul>
                            <li v-for="(item, index) in toBeDeveloped">
                                {{ item }}
                            </li>
                        </ul>
                    </div>
                    <div class="item_area_wrap item_area_04">
                        <p>能力盲区</p>
                        <ul>
                            <li v-for="(item, index) in blindArea">
                                {{ item }}
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="item_line_wrap flex_row_betweens">
                    <div class="item_line_title"></div>
                    <div class="area_descript">自评低</div>
                    <div class="area_descript">自评高</div>
                </div>
            </div>
            <div class="table_area_wrap">
                <table-component
                    :tableData="tableData"
                    :needIndex="true"
                    :loading="loading"
                    @handleSizeChange="handleSizeChange"
                    @handleCurrentChange="handleCurrentChange"
                ></table-component>
            </div>
        </div>
    </div>
</template>
 
<script>
    import {
        personnelRegionPool,
        personnelRegionPoolTable,
    } from "../../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "employeeDevelopmentArea",
        props: ["orgCode", "evalId"],
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading: false,
                // 盲区
                blindArea: [],
                // 优秀
                excellent: [],
                // 潜力人才
                potential: [],
                // 待发展
                toBeDeveloped: [],
                tableData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "org_name",
                        },
                        {
                            label: "岗位",
                            prop: "post_name",
                        },
                        {
                            label: "姓名",
                            prop: "object_name",
                        },
                        {
                            label: "自评",
                            prop: "self_score",
                        },
                        {
                            label: "他评",
                            prop: "other_score",
                        },
                        {
                            label: "人才区分",
                            prop: "peopleDist",
                        },
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        components: {
            tableComponent,
        },
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                        this.getTableData();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                        this.getTableData();
                    }
                },
            },
        },
        methods: {
            getData() {
                this.loading = true;
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                personnelRegionPool(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.blindArea = res.data.blindArea;
                        this.excellent = res.data.excellent;
                        this.potential = res.data.potential;
                        this.toBeDeveloped = res.data.toBeDeveloped;
                        this.loading = false;
                    } else {
                        this.loading = false;
                    }
                });
            },
            handleSizeChange(size){
                this.pageSize = size;
                this.getTableData();
            },
            handleCurrentChange(current){
                this.currPage = current;
                this.getTableData();
            },
            getTableData() {
                this.loading=true;
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                    type: "",
                };
                personnelRegionPoolTable(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                        this.loading = false;
                    } else {
                        this.loading = false;
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .employee_development_area_wrap {
        .employee_development_area_main {
            .item_line_wrap {
                margin: 0 0 15px 0;
                .item_line_title {
                    width: 20px;
                    max-width: 4%;
                    text-align: center;
                    display: flex;
                    align-items: center;
                }
                .item_area_wrap {
                    padding: 10px 15px;
                    width: 46%;
                    background: #e8f5fe;
                    min-height: 160px;
                    p {
                        color: #93c2eb;
                        font-weight: 600;
                        text-align: center;
                    }
                    ul {
                        li {
                            margin: 8px 4px 0 0;
                            display: inline-block;
                            width: 60px;
                            height: 24px;
                            line-height: 24px;
                            background: #fff;
                            text-align: center;
                            font-size: 12px;
                        }
                    }
                }
                .item_area_02 {
                    background: #e0ebc7;
                    p {
                        color: #7a9f2f;
                    }
                }
                .item_area_03 {
                    background: #daf0f4;
                    p {
                        color: #497c88;
                    }
                }
                .item_area_04 {
                    background: #f5e9e7;
                    p {
                        color: #de7a2a;
                    }
                }
                .area_descript {
                    width: 46%;
                    text-align: center;
                }
            }
        }
    }
</style>