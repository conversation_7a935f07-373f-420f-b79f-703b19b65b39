<script setup>
import ReportList from "./reportInfo/reportList.vue";
import ReportOrgIndex from "./reportInfo/orgReport/reportOrgIndex.vue";
import ReportStaffIndex from "./reportInfo/staffReport/reportStaffIndex.vue";

const props = defineProps([]);
const router = useRouter();
const route = useRoute();
const comList = ref([
  {
    sign: "list",
    com: ReportList,
  },
  {
    sign: 1,
    com: ReportOrgIndex,
  },
  {
    sign: 2,
    com: ReportStaffIndex,
  },

  // {
  //   sign: 1,
  //   com: ReportInfoZTNL,
  // },
  // {
  //   sign: 2,
  //   com: reportInfoNLYS,
  // },
  // {
  //   sign: 3,
  //   com: reportInfoTDNL,
  // },
  // {
  //   sign: 4,
  //   com: reportInfoJCPH,
  // },
]);
const curCom = ref({
  sign: "",
  com: "",
});
const comInit = (p) => {
  if (!route.query.sign) {
    curCom.value = comList.value[0];
  } else {
    let searchKey = ref("");
    if (p) {
      searchKey.value = p;
    } else {
      searchKey.value = route.query.sign;
    }
    comList.value.forEach((e, index) => {
      if (e.sign == searchKey.value) {
        curCom.value = comList.value[index];
      }
    });
  }
  router.push({
    path: route.path,
    query: { sign: curCom.value.sign },
  });
};
const curSign = (p) => {
  comInit(p);
};
onMounted(() => {
  comInit();
});
watch(
  () => route,
  (o, c) => {
    comInit();
  },
  {
    deep: true,
  }
);
</script>
<template>
  <div class="index_wrap inventoryReport_wrap">
    <component :is="curCom.com" @sign="curSign" />
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
