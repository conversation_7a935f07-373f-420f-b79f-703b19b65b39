<script setup>
import { Search } from "@element-plus/icons-vue";
import SectionTab from "../../components/sectionTab.vue";
import Tree from "@/components/tree/index.vue";
import Table from "../../components/table.vue";
const key = ref("");

const columns = ref([
  {
    label: "指标名称",
    prop: "a",
  },
  {
    label: "目标类别",
    prop: "b",
  },
  {
    label: "目标期间",
    prop: "c",
  },
  {
    label: "指标单位",
    prop: "d",
  },

  {
    label: "责任人",
    prop: "e",
  },
  {
    label: "指标目标",
    prop: "f",
  },
  {
    label: "实际表现",
    prop: "g",
  },
  {
    label: "达成率",
    prop: "h",
  },
  {
    label: "差距",
    prop: "i",
  },
  {
    label: "",
    prop: "j",
    slot: "jSlot",
    width: 130,
  },
]);
const data = ref([
  {
    a: "物流成本占比",
    b: "年度目标",
    c: "2025年",
    d: "天",
    e: "王伟",
    f: ">98%",
    g: ">98%",
    h: ">98%",
    i: ">98%",
    j: 3,
  },
]);

const numberArea = ref([
  {
    num: "0~59",
  },
  {
    num: "60~69",
  },
  {
    num: "70~79",
  },
  {
    num: "80~89",
  },
  {
    num: "90~100",
  },
]);

const columns2 = ref([
  {
    label: "能力模块",
    prop: "a",
  },
  {
    label: "能力组件",
    prop: "b",
  },
  {
    label: "相关性",
    prop: "c",
  },
  {
    label: "关联逻辑",
    prop: "d",
    width: 420,
  },
  {
    label: "能力表现",
    prop: "j",
    slot: "jSlot",
    width: 130,
  },
]);
const data2 = ref([
  {
    a: "仓储物流管理",
    b: "库存动态分析",
    c: "高",
    d: "库存动态分析能力通过实时监控库存状态（数量、库龄、周转率），快速识别积压风险并触发调整动作。实时数据反馈机制可缩短信息滞后周期，减少因数据失真导致的过量备货，直接降低库存持有天数。",
    j: 66,
  },
]);

const columns3 = ref([
  {
    label: "能力组件",
    prop: "a",
    width: 150,
  },
  {
    label: "短板领域",
    prop: "b",
    width: 150,
  },
  {
    label: "影响说明",
    prop: "c",
    // width: 460,
  },
  {
    label: "影响程度",
    prop: "d",
    width: 150,
  },
]);
const data3 = ref([
  {
    a: "仓储物流管理",
    b: "流程端到端",
    c: "端到端流程不连贯（如需求计划到补货执行脱节），导致信息孤岛、库存数据割裂，增加缺货风险（尤其在促销季），并延长决策周期。",
    d: "高",
  },
]);

const circleColor = (v) => {
  if (v < 59) {
    return "bg1_b";
  } else if (v > 59 && v < 69) {
    return "bg2_b";
  } else if (v > 69 && v < 79) {
    return "bg3_b";
  } else if (v > 79 && v < 89) {
    return "bg4_b";
  } else if (v > 89 && v <= 100) {
    return "bg5_b";
  }
};
</script>
<template>
  <div class="indicator_main">
    <div class="page-title-line">指标一览</div>
    <Table roundBorder :columns="columns" :data="data" showIndex>
      <template v-slot:jSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>AI解读</el-button>
      </template>
    </Table>
    <div class="tips">已选指标：<span>库存周转天数</span></div>

    <div class="page-title-line">关联能力</div>
    <div class="number_area_list justify-start">
      分值：
      <div class="item_wrap" v-for="(item, index) in numberArea">
        <span
          class="icon"
          :class="{
            act: index == 0,
            act1: index == 1,
            act2: index == 2,
            act3: index == 3,
            act4: index == 4,
          }"
        ></span
        >{{ item.num }}
      </div>
    </div>
    <Table
      :roundBorder="false"
      :columns="columns2"
      :data="data2"
      headerColor
      showIndex
    >
      <template v-slot:jSlot="scope">
        <span class="circle" :class="circleColor(scope.row.j)">{{
          scope.row.j
        }}</span>
      </template>
    </Table>
    <div class="tips">已选能力：<span>库存动态分析</span></div>

    <div class="page-title-line">能力DNA解码</div>
    <div class="chart_list_wrap justify-between">
      <div class="item_wrap">
        <div class="chart_t">流程赋能</div>
        <div class="chart_box"></div>
      </div>
      <div class="item_wrap">
        <div class="chart_t">组织赋能</div>
        <div class="chart_box"></div>
      </div>
      <div class="item_wrap">
        <div class="chart_t">人岗赋能</div>
        <div class="chart_box"></div>
      </div>
      <div class="item_wrap">
        <div class="chart_t">数字化赋能</div>
        <div class="chart_box"></div>
      </div>
      <div class="item_wrap">
        <div class="chart_t">AI赋能</div>
        <div class="chart_box"></div>
      </div>
    </div>
    <div class="tips">已选能力：<span>库存动态分析</span></div>

    <div class="page-title-line">能力短板对指标的影响</div>
    <Table
      :roundBorder="false"
      :columns="columns3"
      :data="data3"
      headerColor
      showIndex
    >
    </Table>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
@import "../common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
