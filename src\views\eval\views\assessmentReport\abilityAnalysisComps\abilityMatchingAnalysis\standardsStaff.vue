<template>
    <div class="">
        <div class="page_third_title">人员匹配度</div>
        <table-component
            :tableData="tableData"
            :needIndex="true"
            :loading="loading"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
        ></table-component>
    </div>
</template>
 
<script>
    import { personnelMatchingDegree } from "../../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "standardsStaff",
        props: ["orgCode","evalId"],
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading: false,
                tableData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "orgName",
                        },
                        {
                            label: "岗位",
                            prop: "postName",
                        },
                        {
                            label: "姓名",
                            prop: "objectName",
                        },
                        {
                            label: "目标",
                            prop: "expectedScore",
                        },
                        {
                            label: "得分",
                            prop: "overallScore",
                        },
                        {
                            label: "达标能力项",
                            prop: "itemAchieveCount",
                        },
                        {
                            label: "未达标能力项",
                            prop: "noAchieve",
                        },
                        {
                            label: "能力匹配度",
                            prop: "matching",
                            formatterFun: function (data) {
                                return data["matching"] + "%";
                            },
                        },
                    ],
                    data: [],
                    page: {
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        components: {
            tableComponent,
        },
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                    }
                },
            },
        },

        methods: {
            getData() {
                this.loading = true;
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                personnelMatchingDegree(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                        this.loading = false;
                    }else{
                        this.loading = false;
                    }

                });
            },
            handleSizeChange(size) {
                this.pageSize = size;
                this.getData();
            },
            handleCurrentChange(current) {
                this.currPage = current;
                this.getData();
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>