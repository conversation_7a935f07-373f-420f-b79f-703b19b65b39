<template>
  <div class="post_import_wrap bg_write">
    <div class="page_main_title">
      <div class="goback_geader" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>返回
      </div>
      {{ tabsPaneInfo[0].label }}
    </div>
    <div class="page_second_title">{{ tabsPaneInfo[0].label }}信息导入--错误一览</div>
    <div class="page_section staff_import_center clearfix">
      <!-- <el-table 
        :data="tableData" 
        stripe 
        ref="tableRef"
        v-if="flag"
      >
        <el-table-column type="index" width="50" />
        <el-table-column
          v-for="col in columns"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :width="col.width"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tooltip
              effect="dark"
              :content="row[col.prop].msg"
              placement="top"
              :disabled="row[col.prop].accept"
            >
              <el-input
                v-if="col.prop !== 'isLeader' || row[col.prop].accept"
                v-model="row[col.prop].val"
                :type="col.prop == 'userPasswd' ? 'password' : 'text'"
                :disabled="row[col.prop].accept"
                :class="{ error: !row[col.prop].accept }"
                size="small"
              />
              <el-select
                v-else
                v-model="row[col.prop].val"
                :class="{ 'error_select': !row[col.prop].accept }"
                :disabled="row[col.prop].accept"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in options"
                  :key="item.dictCode"
                  :label="item.codeName"
                  :value="item.dictCode"
                />
              </el-select>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table> -->
      <!-- <coustom-pagination
        :total="tableDataCopy.length"
        @page-change="pageChange"
      /> -->
      <div class="btn_wrap align_center">
        <el-button type="primary" @click="saveBtn">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
// import { importPostData } from '../../request/api'
import CoustomPagination from '@/components/talent/paginationComps/coustomPagination.vue'

const router = useRouter()
const route = useRoute()

const flag = ref(true)
const tabsPaneInfo = ref(route.query.tabsPaneInfo)
const errorData = ref(route.query.data)
const tableData = ref([])
const tableDataCopy = ref([]) // 备份
const tableRef = ref(null)

const columns = [
  {
    label: '岗位编码',
    prop: 'postCodeExtn'
  },
  {
    label: '岗位名称',
    prop: 'postName'
  },
  {
    label: '所属组织编码',
    prop: 'orgCode'
  },
  {
    label: '上级岗位编码',
    prop: 'parentPostCodeExtn'
  },
  {
    label: '是否是负责人岗位',
    prop: 'isLeader'
  },
  {
    label: '职位编码',
    prop: 'jobCode'
  }
]

const options = [
  {
    dictCode: '是',
    codeName: '是'
  },
  {
    dictCode: '否',
    codeName: '否'
  }
]

const goBack = () => {
  router.go(-1)
}

const getPageData = (pageSize, currentPage) => {
  const offset = (currentPage - 1) * pageSize
  tableData.value =
    offset + pageSize >= tableDataCopy.value.length
      ? tableDataCopy.value.slice(offset, tableDataCopy.value.length)
      : tableDataCopy.value.slice(offset, offset + pageSize)
}

const pageChange = (pageSize, currentPage) => {
  getPageData(pageSize, currentPage)
}

const saveBtn = async () => {
  const data = tableDataCopy.value.map(item => {
    const obj = {
      postCodeExtn: '',
      postName: '',
      orgCode: '',
      parentPostCodeExtn: '',
      isLeader: '',
      jobCode: '',
      parentPostCode: '',
      postCode: ''
    }

    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        obj[key] = item[key].val
      }
    }
    return obj
  })

  // Uncomment when API is ready
  // try {
  //   const res = await importPostData(data)
  //   if (res.code == 200) {
  //     tableData.value = []
  //     tableDataCopy.value = []
  //     tableData.value = res.data.obj
  //     tableDataCopy.value = res.data.obj
  //     flag.value = false
  //
  //     if (res.data.errorCount == 0) {
  //       goBack()
  //       return
  //     }
  //
  //     getPageData(10, 1)
  //     nextTick(() => {
  //       flag.value = true
  //     })
  //   } else {
  //     ElMessage.error(res.msg)
  //   }
  // } catch (error) {
  //   ElMessage.error('保存失败')
  // }
}

onMounted(() => {
  tableData.value = errorData.value
  tableDataCopy.value = errorData.value
  pageChange(10, 1)
})
</script>

<style scoped lang="scss">
.post_import_wrap {
  .page_second_title {
    margin: 0 0 0 15px;
  }
}

:deep(.el-input.error .el-input__inner) {
  border-color: red;
}

:deep(.cell .error_select) {
  .el-input {
    .el-input__inner {
      border-color: red !important;
    }
  }
}
</style>
