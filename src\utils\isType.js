// 类型判断工具
export const isArray = val => {
  return Object.prototype.toString.call(val) == '[object Array]'
}

export const isObject = val => {
  return Object.prototype.toString.call(val) == '[object Object]'
}

export const isFunction = val => {
  return typeof val == 'function'
}

export const isString = val => {
  return typeof val == 'string'
}

export const isNumber = val => {
  return typeof val == 'number' && !isNaN(val)
}

export const isBoolean = val => {
  return typeof val == 'boolean'
}

export const isNull = val => {
  return val == null
}

export const isUndefined = val => {
  return val == undefined
}

export const isEmpty = val => {
  return val == '' || val == null || val == undefined
}
