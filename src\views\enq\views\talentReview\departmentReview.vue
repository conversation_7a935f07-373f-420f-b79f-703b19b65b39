<template>
  <div class="bg_write">
    <div class="page_main_title">
      <div class="goback_geader" @click="goback"><i class="el-icon-arrow-left"></i>返回</div>
      部门盘点--{{ enqName }}
    </div>
    <div class="page_section">
      <div class="talent_raview_main">
        <step-bar
          :needClick="enqStatus == 'P'"
          @stepClick="stepClick"
          :labelKey="'enqModuleName'"
          :stepData="stepData"
          :currentIndex="currentModuleCode"
        ></step-bar>
        <!-- currentFirstCode与currentModuleCode对比是否需要上一步 -->
        <component
          :is="moduleObj[currentModuleCode]"
          :nextBtnText="nextBtnText"
          :enqId="enqId"
          :orgCode="orgCode"
          :currentIndex="currentModuleCode"
          :currentFirstCode="currentFirstCode"
          @nextStep="nextStep"
          @prevStep="prevStep"
          @nextStepClick="departNextStepFun"
        ></component>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import {
  personalNextStep,
  getDeptEnqModule,
  departNextStep,
  nextStep as apiNextStep,
  syncSupInfo,
  getOrgInfo,
  getEnqInfo
} from '../../request/api'

import stepBar from '@/components/talent/stepsComps/stepBar'
import departmentProcess from './departmentReviewComponents/departmentProcess'
import departmentDuty from './departmentReviewComponents/departmentDuty'
import departmentPostInfo from './departmentReviewComponents/departmentPostInfo'
import postProcess from './departmentReviewComponents/postProcess'
import departmentPerformanceInfo from './departmentReviewComponents/departmentPerformanceInfo'
import postRequirement from './departmentReviewComponents/postRequirement'
import taskConfirmation from './departmentReviewComponents/taskConfirmation'
import personnelEvaluation from './departmentReviewComponents/personnelEvaluation'
import personnelCalibration from './departmentReviewComponents/personnelCalibration'
import positionRequirement from './departmentReviewComponents/positionRequirement'
import positionWorth from './departmentReviewComponents/positionWorth'
import qualityEvaluation from './departmentReviewComponents/qualityEvaluation'
import performanceEvaluation from './departmentReviewComponents/performanceEvaluation'
import potentialEvaluation from './departmentReviewComponents/potentialEvaluation'
import leavingRisk from './departmentReviewComponents/leavingRisk'
import workEvaluation from './departmentReviewComponents/workEvaluation'
import targetAndResult from './departmentReviewComponents/targetAndResult.vue'
import personnelDevelopment from './departmentReviewComponents/personnelDevelopment'
import trainPlan from './departmentReviewComponents/trainPlan'
import departmentKPIEvaluate from './departmentReviewComponents/departmentKPIEvaluate.vue'
import coreQuality from './departmentReviewComponents/coreQuality.vue' //核心素质

const route = useRoute()
const enqStatus = ref(null)
const enqId = ref(route.query.enqId)
const orgCode = ref(route.query.orgCode)
const enqName = ref('')
const moduleArr = ref([])
const moduleObj = {
  DN01: positionRequirement,
  DN02: positionWorth,
  DN03: qualityEvaluation,
  DN04: performanceEvaluation,
  DN05: potentialEvaluation,
  DN06: leavingRisk,
  DN07: workEvaluation,
  DN08: targetAndResult,
  DN09: departmentKPIEvaluate,
  DN10: personnelDevelopment,
  DN11: trainPlan,
  DN12: coreQuality
}
const currentModuleCode = ref('')
const currentIndex = ref(0)
const currentFirstCode = ref('')
const nextBtnText = ref('下一步')
const stepData = ref([])

const router = useRouter()
const goback = () => {
  router.back()
}

const getEnqInfoFun = async () => {
  const res = await getEnqInfo({ id: enqId.value })
  if (res.code == 200) {
    enqName.value = res.data.enqName
  }
}

const getDeptEnqModuleFun = async () => {
  const params = {
    enqId: enqId.value,
    orgCode: orgCode.value
  }
  const res = await getDeptEnqModule(params)
  moduleArr.value = []
  if (res.code == 200) {
    res.data.forEach(item => {
      moduleArr.value.push(item.enqModuleCode)
      item.name = item.enqModuleName
      item.code = item.enqModuleCode
    })
    stepData.value = res.data
    currentModuleCode.value = moduleArr.value[currentIndex.value]
    if (moduleArr.value && moduleArr.value.length !== 0) {
      currentFirstCode.value = moduleArr.value[0]
    }
    if (currentIndex.value == stepData.value.length - 1) {
      nextBtnText.value = '提交'
    } else {
      nextBtnText.value = '下一步'
    }
  }
}

const departNextStepFun = async params => {
  await apiNextStep(params)
}

const stepClick = (code, index) => {
  currentIndex.value = index
}

const getOrgInfoFun = async () => {
  const params = {
    enqId: enqId.value
  }
  const res = await getOrgInfo(params)
  if (res.code == 200 && res.data) {
    enqStatus.value = res.data.enqStatus
  }
}

const nextStep = async () => {
  const params = {
    enqId: enqId.value,
    module: currentModuleCode.value
  }
  console.log(params)

  await departNextStepFun(params)
  stepData.value[currentIndex.value].enqProgress = 'Y'
  if (currentIndex.value == stepData.value.length - 1) {
    console.log('最后一步')
    await submitOrgInfoFun()
    return false
  }
  currentIndex.value++
  if (currentIndex.value == stepData.value.length - 1) {
    nextBtnText.value = '提交'
  } else {
    nextBtnText.value = '下一步'
  }
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

const prevStep = () => {
  if (currentIndex.value == 0) {
    return false
  }
  currentIndex.value--
  if (currentIndex.value == stepData.value.length - 1) {
    nextBtnText.value = '提交'
  } else {
    nextBtnText.value = '下一步'
  }
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

const submitOrgInfoFun = async () => {
  const params = {
    enqId: enqId.value,
    orgCode: orgCode.value
  }
  const res = await syncSupInfo(params)
  if (res.code == 200) {
    goback()
  }
}

watch(orgCode, val => {
  if (val) {
    getDeptEnqModuleFun()
    getOrgInfoFun()
  }
})

watch(currentIndex, () => {
  currentModuleCode.value = moduleArr.value[currentIndex.value]
  if (currentIndex.value == stepData.value.length - 1) {
    nextBtnText.value = '提交'
  } else {
    nextBtnText.value = '下一步'
  }
})

onMounted(() => {
  getEnqInfoFun()
  getOrgInfoFun()
  getDeptEnqModuleFun()
})
</script>

<style scoped lang="scss">
.talent_raview_main {
  .talent_raview_btn_wrap {
    text-align: center;
    padding-top: 26px;
  }
}

.from_wrap {
  .basic_info {
    float: left;
    width: 50%;
  }

  .post_info {
    overflow: hidden;
  }

  .el-input__inner {
    width: 280px;
  }
}

.oper_btn_wrap {
  padding-top: 32px;
  text-align: center;
  &.align_right {
    text-align: right;
  }
}
// 去除input number类型 加减箭头
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input {
  -moz-appearance: textfield;
}
</style>
