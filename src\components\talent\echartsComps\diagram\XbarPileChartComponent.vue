<template>
    <div class="chart_main">
        <div class="_chart" :id="id" :style="styleObj"></div>
    </div>
</template>
 
<script>
export default {
    name: "XbarPileChartComponent",
    /**
     isPercent : 是否是百分比数字，默认false ， true则在数字后面拼接 “ % ”
     seriesName : 数据表名称，主要用于tooltip显示类似title;
    **/

    props: ["chartData", "width", "height", "isPercent", "seriesName"],
    components: {},
    // barPileData:{
    //     titleData: ['极差', '欠佳', '尚可', '佳', '极佳'],
    //     typeData: ["课程准备充分","具备足够主题专业知识和"],
    //     data: [
    //         [2, 4, 2, 4, 2, 3, 2],
    //         [2, 4, 2, 4, 2, 3, 2],
    //         [2, 4, 2, 4, 2, 3, 2],
    //         [2, 4, 2, 4, 2, 3, 2],
    //         [2, 4, 2, 4, 2, 3, 2],
    //     ],
    // }
    data() {
        return {
            id: null,
            styleObj: {
                width: this.width + "px",
                height: this.height + "px"
            }
        };
    },
    watch: {
        chartData: function() {
            this.init(this.chartData);
        }
    },
    mounted() {
        if (this.chartData.data.length == 0) {
            return;
        }
        this.init(this.chartData);
    },
    methods: {
        init(chartData) {
            let id = this.$util.createRandomId();
            this.id = id;
            this.$nextTick(() => {
                this.toDraw(id, chartData);
            });
        },
        toDraw(id, chartData) {
            let _this = this;
            let myChart = this.$EC.init(document.getElementById(id));
            if (chartData.data.length == 0) {
                myChart.clear();
                return;
            }
            let curSeries = []
            for(let i=0;i<chartData.titleData.length;i++){
                curSeries.push({
                    name: chartData.titleData[i],
                    type: 'bar',
                    stack: '分数',
                    barWidth:24,
                    label: {
                        normal:{
                            show:true,
                            formatter:function(params){
                                if(params.value > 0){
                                    return params.value
                                }else{
                                    return '';
                                }
                            }
                        }
                    },
                    data: chartData.data[i]
                })
            }
            // console.log(curSeries)
            let options = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                    },
                    transitionDuration:0
                },
                color: ['#52cbff','#f49021', '#00aaff','#f9c515', '#159692','#f8f509','#2aba51',],
                legend: {
                    data: chartData.titleData
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: false,

                    },
                    splitLine: {   
                        show: false
                    },
                    axisLabel: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'category',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: true,
                        lineStyle:{
                            color:"#e5e5e5"
                        }
                    },
                    axisLabel: {
                        show: true,
                        color: '#333',
                    },
                    splitLine: {   
                        show: false
                    },
                    data: chartData.typeData
                },
                series: curSeries
            };

            myChart.setOption(options);
        }
    }
};
</script>
 
<style scoped lang="scss">
.chart_main {
    width: 100%;
    height: 100%;
    ._chart {
        width: 100%;
        height: 100%;
    }
}
</style>