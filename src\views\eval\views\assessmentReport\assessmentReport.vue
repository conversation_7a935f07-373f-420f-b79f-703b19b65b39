<template>
    <div class="assessment_report_home_wrap bg_write">
        <div class="page_main_title">评估报告</div>
        <div class="page_section">
            <div class="marginB_16">
                <tabsLink :tabsData="tabsData" :isDefaultTheme="true"></tabsLink>
            </div>
            <!-- <tabsChangeData
                :tabsData="tabsData"
                :activeName="tabsData[0].name"
                :handleClick="changeTabs"
            ></tabsChangeData> -->
            <!-- <component :is="compArr[currentIndex]"></component> -->
            <router-view></router-view>
        </div>
    </div>
</template>
 
<script>
    import tabsLink from "@/components/talent/tabsComps/tabsLink.vue";
    import tabsChangeData from "@/components/talent/tabsComps/tabsChangeData";
    import reportViewList from "./assessmentReportComps/reportViewList";
    import projectView from "./assessmentReportComps/projectView";
    export default {
        name: "assessmentReport",
        components: {
            tabsLink,
            reportViewList,
            projectView,
        },
        data() {
            return {
                activeName: "listView",
                currentIndex: 0,
                compArr: [projectView, reportViewList],
                tabsData: [
                    {
                        id: 1,
                        label: "项目视图",
                        name: "项目视图",
                        path:
                            "/talentAssessment/assessmentReport/report/projectView",
                    },
                    {
                        id: 2,
                        label: "列表视图",
                        name: "列表视图",
                        path:
                            "/talentAssessment/assessmentReport/report/listView",
                    },
                ],
            };
        },
        methods: {
            changeTabs(tab, event) {
                this.currentIndex = tab.index;
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>