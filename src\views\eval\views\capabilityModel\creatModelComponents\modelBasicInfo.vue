<template>
    <div
        class="model_basic_info_wrap"
        :class="{ event_none: buildStatus == '4' }"
    >
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="80px"
            class="demo-ruleForm"
        >
            <el-form-item label="模型名称" prop="modelName">
                <el-input v-model="ruleForm.modelName"></el-input>
            </el-form-item>
            <el-form-item label="模型简称" prop="modelShortName">
                <el-input v-model="ruleForm.modelShortName"></el-input>
            </el-form-item>
            <el-form-item label="业务类型" prop="jobClassCode">
                <el-select
                    v-model="ruleForm.jobClassCode"
                    clearable
                    placeholder="请选择"
                    :disabled="buildStatus == '4'"
                >
                    <el-option
                        v-for="item in jobClassList"
                        :key="item.value"
                        :label="item.jobClassName"
                        :value="item.jobClassCode"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="模型简介" prop="modelDesc">
                <el-input
                    type="textarea"
                    v-model="ruleForm.modelDesc"
                    resize="none"
                    placeholder="最多256个字符"
                ></el-input>
            </el-form-item>
            <div class="align_center" v-if="buildStatus !== '4'">
                <el-button
                    class="page_confirm_btn"
                    type="primary"
                    @click="next()"
                    >下一步</el-button
                >
            </div>
        </el-form>
    </div>
</template>
 
<script>
    import {
        createModelInfo,
        getModelType,
        getModelInfo,
    } from "../../../request/api";
    export default {
        name: "modelBasicInfo",
        props: ["modelId", "buildStatus"],
        components: {},
        data() {
            return {
                ruleForm: {
                    modelName: "",
                    modelShortName: "",
                    jobClassCode: "",
                    modelDesc: "",
                },
                rules: {
                    modelName: [
                        {
                            required: true,
                            message: "请输入模型名称",
                            trigger: "blur",
                        }
                    ],
                    modelShortName: [
                        {
                            required: true,
                            message: "请输入模型简称",
                            trigger: "blur",
                        },
                        {
                            min: 0,
                            max: 8,
                            message: "最多8个字符",
                            trigger: "blur",
                        },
                    ],
                    jobClassCode: [
                        {
                            required: true,
                            message: "请选择业务类型",
                            trigger: "change",
                        },
                    ],
                    modelDesc: [
                        {
                            required: true,
                            message: "请填写模型简介",
                            trigger: "blur",
                        },{
                            min: 0,
                            max: 256,
                            message: "最多256个字符",
                            trigger: "blur",
                        },
                    ],
                },
                jobClassList: [],
            };
        },
        created() {
            this.getModelTypeFun();
            if (this.modelId) {
                this.getModelInfoFun();
            }
        },
        methods: {
            next() {
                this.submitForm('ruleForm');
            },
            submitForm(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.createModelInfoFun();
                    } else {
                        console.log("error submit!!");
                        return false;
                    }
                });
            },
            //获取业务类型
            getModelTypeFun() {
                getModelType().then((res) => {
                    console.log(res);
                    this.jobClassList = res;
                });
            },

            getModelInfoFun() {
                getModelInfo({
                    modelId: this.modelId,
                }).then((res) => {
                    console.log(res);
                    this.ruleForm.modelName = res.modelName;
                    this.ruleForm.modelShortName = res.modelShortName;
                    this.ruleForm.jobClassCode = res.jobClassCode;
                    this.ruleForm.modelDesc = res.modelDesc;
                });
            },
            createModelInfoFun() {
                let params = {
                    modelId: this.modelId,
                    modelShortName: this.ruleForm.modelShortName,
                    modelName: this.ruleForm.modelName,
                    jobClassCode: this.ruleForm.jobClassCode,
                    modelDesc: this.ruleForm.modelDesc,
                };
                createModelInfo(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$emit("nextStep", res.data);
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .model_basic_info_wrap {
        padding: 0 70px;
        margin: 20px 0;
        .line_item_wrap {
            margin: 0 0 22px 0;
            .descript {
                width: 100px;
            }
            .input_wrap {
                width: 280px;
                .el-input__inner {
                    width: 280px;
                }
            }
            .textarea_wrap {
                width: 600px;
                .el-textarea__inner {
                    height: 130px;
                }
            }
        }
        .textarea_item_wrap {
            .descript {
                padding: 10px 0 0 0;
                height: 130px;
            }
        }
    }
</style>