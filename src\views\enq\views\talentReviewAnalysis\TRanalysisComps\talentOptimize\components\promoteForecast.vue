<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title">部门人效提升</div>
          <div class="content_item_content">
            <XbarChartComponent :chartData="postData" :width="770" :height="230" :seriesName="''"></XbarChartComponent>
          </div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title">详情</div>
          <div class="content_item_content">
            <tableComponent :needIndex="true" :size="'small'" :tableData="tableData"></tableComponent>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import asideFilter from '@/components/talent/asideNav/asideFilter'
import XbarChartComponent from '@/components/talent/echartsComps/diagram/XbarChartComponent'
import tableComponent from '@/components/talent/tableComps/tableComponent'

const tableData = reactive({
  columns: [
    {
      label: '部门名称',
      prop: 'workType'
    },
    {
      label: '岗位',
      prop: 'highPartment'
    },
    {
      label: '职层',
      prop: 'department1'
    },
    {
      label: '人员新增',
      prop: 'department2'
    },
    {
      label: '单人成本',
      prop: 'department3'
    },
    {
      label: '预计总成本',
      prop: 'department4'
    }
  ],
  data: [
    {
      id: '123123',
      workType: '部门六',
      highPartment: '区域销售主管',
      department1: '主管',
      department2: '3',
      department3: '20万/年',
      department4: '60万/年'
    },
    {
      id: '12dsa3123',
      workType: '部门六',
      highPartment: '区域销售主管',
      department1: '主管',
      department2: '3',
      department3: '20万/年',
      department4: '60万/年'
    }
  ]
})

const postData = ref([
  {
    name: '山西省',
    value: 1
  },
  {
    name: '辽宁省',
    value: 1
  },
  {
    name: '未知',
    value: 1
  },
  {
    name: '西川省',
    value: 24
  },
  {
    name: '河南省',
    value: 41
  },
  {
    name: '贵州省',
    value: 91
  },
  {
    name: '安徽省',
    value: 118
  },
  {
    name: '云南省',
    value: 217
  },
  {
    name: '浙江省',
    value: 441
  }
])

const navData = ref([
  {
    id: '1',
    name: '按组织架构',
    children: [
      {
        id: '1-1',
        name: '组织架构1'
      },
      {
        id: '1-2',
        name: '组织架构2'
      },
      {
        id: '1-3',
        name: '组织架构3'
      },
      {
        id: '1-4',
        name: '组织架构4'
      }
    ]
  },
  {
    id: '2',
    name: '按职务类型',
    children: [
      {
        id: '2-1',
        name: '全部'
      },
      {
        id: '2-2',
        name: '战略运营类'
      },
      {
        id: '2-3',
        name: '市场营销类'
      },
      {
        id: '2-4',
        name: '供应链类'
      },
      {
        id: '2-5',
        name: '业务流程设置'
      }
    ]
  },
  {
    id: '3',
    name: '研发技术类',
    children: [
      {
        id: '3-1',
        name: '研发技术1'
      },
      {
        id: '3-2',
        name: '研发技术2'
      }
    ]
  }
])

const getId = id => {
  console.log(id)
}
</script>

<style scoped lang="scss"></style>
