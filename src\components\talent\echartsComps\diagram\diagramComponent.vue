<template>
    <div classs="diagram_component">
        <div class="chart_wrap" :id="id" :style="styleObj"></div>
    </div>
</template>

<script>
export default {
    name: "diagramComponent",
    props:["chartData","width","height"],
    data() {
        return {
            id: null,
            styleObj: {
                width: this.width + 'px',
                height: this.height + 'px'
            }
        };
    },
    watch: {
        chartData:{
            handler() {
                this.init(this.chartData);
            },
            deep: true
        }
    },
    mounted() {
        if (this.chartData.data.length == 0) {
            return
        }
        this.init(this.chartData);
    },
    methods: {
        init(chartData) {
            let id = this.$util.createRandomId();
            this.id = id;
            this.$nextTick(() => {
                this.toDraw(id, chartData);
            });
        },
        toDraw(id, chartData) {
            let myChart = this.$EC.init(document.getElementById(id));

            if (chartData.data.length == 0) {
                myChart.clear();
                return;
            }
            let option = {
                legend: {
                    show: true,
                    itemGap: 10,
                    right: 10
                    // formatter: function(name) {
                    //     console.log(name);
                        
                    //     return name == "value" ? "实际" : "目标";
                    // }
                },
                tooltip: {
                    //提示框组件
                    trigger: "axis",
                    axisPointer:{   // 坐标轴指示器
                        type: 'shadow'   // 触发区域标识，默认直线，可选 'line' | 'shadow'
                    }
                },
                dataset: {
                    // source: chartData.datas
                },
                xAxis: {
                    type: "category", //坐标轴类型
                    data: chartData.data.map(function(item) {
                        // X轴类别
                        return item.name;
                    }),
                    axisTick:{
                        show: true,
                        alignWithLabel: true,
                        interval: 0
                    },
                    axisLabel:{
                        show: true,
                        interval: 0,
                        fontSize: 12,
                        rotate: 45
                    }
                },
                yAxis: {
                    splitNumber: 4,
                    splitLine:{
                        show:false
                    }
                },
                valueAxis: null,
                // Declare several bar series, each will be mapped
                // to a column of dataset.source by default.
                series: [
                    {
                        name: chartData.nameList[0],
                        type: "bar",
                        itemStyle:{
                            color:'#4298FC'
                        },
                        data: chartData.data.map(function(item) {
                            return item.value;
                        })
                    },
                    {
                        name: chartData.nameList[1],
                        type: "bar",
                        itemStyle:{
                            color:'#60CFFF'
                        },
                        data: chartData.data.map(function(item) {
                            return item.target;
                        }) 
                    }
                ]
            };
            myChart.setOption(option);
        }
    }
};
</script>

<style lang="scss" scoped>
.chart_wrap {
    width: 100%;
    height: 100%;
}
</style>