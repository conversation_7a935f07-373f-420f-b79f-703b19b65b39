<template>
    <div class="relationship_wrap">
        <div class="level_wrap" :class="{ event_none: !isEdit }">
            <div class="page_second_title">
                设置评价关系权重
                <span class="level_count"
                    >(选中的评价关系比例总和需等于100)</span
                >
            </div>
            <div class="flex_row_start marginT_16">
                <ul
                    class="level_wrap_ul flex_row_start"
                    :class="{ event_none: disFlag }"
                >
                    <li
                        @click="checkedLevel(item, index)"
                        v-for="(item, index) in weightList"
                        :class="{ active: item.selected }"
                    >
                        <span>{{ item.relationTypeName }}</span>

                        <el-input
                            placeholder="请输入整数"
                            v-model="item.weight"
                            :disabled="disFlag"
                            :class="{ border_blue: !disFlag }"
                            @click.stop.native
                        >
                            <template slot="append">%</template>
                        </el-input>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn_wrap marginT_16" :class="{ event_none: !isEdit }">
            <el-button
                v-if="disFlag"
                class="page_add_btn"
                type="primary"
                @click="switchDisFlag(0)"
                >编辑</el-button
            >
            <el-button
                v-else="!disFlag"
                class="page_add_btn"
                type="primary"
                @click="switchDisFlag(1)"
                >保存</el-button
            >
        </div>
        <!-- <div class="search_wrap">
            <div class="search_title">
                评价岗位
            </div>
            <div class="search_main">
                <div class="search_item flex_row_start" v-for="item in searchConfirmList">
                    <span class="search_label">{{item.relationTypeName}}</span>
                    <el-radio-group v-model="item.checkedVal" :disabled="disFlag">
                        <el-radio :label="data.relationScope" v-for="data in item.relationScopeList">{{data.relationScopeName}}</el-radio>
                    </el-radio-group>
                </div>
                <div class="text_center no_data_row" v-if="searchConfirmList.length == 0">未选择评价关系</div>
            </div>
        </div> -->
        <div class="flex_row_end marginT_20">
            <el-button
                class="page_add_btn"
                type="primary"
                plain
                @click="exportTemplate"
                >导出模板</el-button
            >
            <div class="upload_wrap" :class="{ event_none: !isEdit }">
                <el-button class="page_add_btn" type="primary"
                    >导入数据</el-button
                >
                <input
                    ref="file"
                    type="file"
                    title=""
                    class="form-control import_btn"
                    @change="fileChange"
                />
            </div>
        </div>
        <div
            class="loading_style"
            v-loading.fullscreen.lock="loading"
            element-loading-text="上传中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.5)"
        ></div>
        <div class="relationship_main flex_row_start">
            <div class="post_left">
                <div class="tree_box">
                    <div class="tree_title">测评对象</div>
                    <div class="tree_main">
                        <treeCompRadio
                            :treeData="pageData"
                            @clickCallback="clickCallback"
                        ></treeCompRadio>
                    </div>
                </div>
            </div>
            <div class="relationship_right" :class="{ event_none: !isEdit }">
                <!--                <el-table class="table_wrap" :data="tableData.data" border @cell-click="cellClick">-->
                <!--                    <el-table-column-->
                <!--                            v-for="(item,index) in tableData.tableTitle"-->
                <!--                            :label="item.label"-->
                <!--                            align="center"-->
                <!--                    >-->
                <!--                        <el-table-column-->
                <!--                                v-for="(item1,index2) in item.childrenLabels"-->
                <!--                                v-if="!item.canCheck"-->
                <!--                                :label="item1.label"-->
                <!--                                :prop="item1.prop"-->
                <!--                                align="center"-->
                <!--                        >-->
                <!--                        </el-table-column>-->
                <!--                        <el-table-column-->
                <!--                                v-for="(item1,index3) in item.childrenLabels"-->
                <!--                                v-if="item.canCheck"-->
                <!--                                :label="item1.label"-->
                <!--                                :prop="item1.prop"-->
                <!--                                align="center"-->
                <!--                        >-->
                <!--                            <template slot-scope="scope">-->
                <!--                                <span :class="{ 'el-icon-check':scope.row.checkList[index3].checked,'check_box_wrap':true}"></span>-->
                <!--                            </template>-->
                <!--                        </el-table-column>-->

                <!--                    </el-table-column>-->
                <!--                </el-table>-->
                <table>
                    <thead>
                        <tr>
                            <td colspan="3">评价关系</td>
                            <td
                                :colspan="
                                    tableData.tableTitle[1].childrenLabels
                                        .length
                                "
                            >
                                {{ tableData.tableTitle[1].label }}
                            </td>
                        </tr>
                        <tr>
                            <td width="60">序号</td>
                            <td width="120">参评人</td>
                            <td width="100">关系</td>
                            <td
                                v-for="item in tableData.tableTitle[1]
                                    .childrenLabels"
                                v-html="item.label"
                            ></td>
                        </tr>
                    </thead>
                    <tbody class="text_center relationship_right_center">
                        <tr v-for="(item, index) in tableData.data">
                            <td>{{ item.index }}</td>
                            <td>{{ item.userName }}</td>
                            <td>{{ item.relationName }}</td>
                            <template
                                v-for="(col, index2) in tableData.tableTitle[1]
                                    .childrenLabels"
                            >
                                <td
                                    class="can_handler"
                                    v-if="
                                        item.readOnlyModuleList.indexOf(
                                            col.code
                                        ) < 0
                                    "
                                    @click="
                                        cellClick(
                                            item.postCode,
                                            index,
                                            col.code
                                        )
                                    "
                                >
                                    <span
                                        :class="{
                                            'el-icon-check':
                                                item.checkList[index2].checked,
                                            check_box_wrap: true,
                                        }"
                                    ></span>
                                </td>
                                <td v-else>
                                    <span
                                        class="td_disabled el-icon-circle-close"
                                    ></span>
                                </td>
                            </template>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div
            class="talent_raview_btn_wrap align_center marginT_30"
            v-if="isEdit"
        >
            <el-button class="page_confirm_btn" type="primary" @click="prev()"
                >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="next()"
                >下一步</el-button
            >
        </div>
    </div>
</template>

<script>
    import {
        getRelationInfo,
        getEvalRelation,
        saveEvalRelation,
        getPostModule,
        saveUserObjectRelation,
        exportRelation,
        importRelation,
        getEvalPostObject,
    } from "../../../request/api";
    import treeCompRadio from "@/components/talent/treeComps/treeCompRadio";
    export default {
        name: "relationshipBetween",
        props: ["evalId", "isEdit"],
        components: {
            treeCompRadio,
        },
        data() {
            return {
                loading: false,
                disFlag: true,
                weightList: [
                    {
                        name: "自评",
                        value: "",
                    },
                    {
                        name: "上级",
                        value: "",
                    },
                    {
                        name: "下级",
                        value: "",
                    },
                    {
                        name: "同级",
                        value: "",
                    },
                    {
                        name: "协同岗位",
                        value: "",
                    },
                ],
                sameLevelVal: "",
                upLevelVal: "",
                lowerLevelVal: "",
                searchConfirmList: [],
                defaultCheckedKeys: [],
                checkedPostCode: "",
                tableData: {
                    tableTitle: [
                        {
                            label: "岗位关系",
                            canCheck: false,
                            childrenLabels: [
                                {
                                    label: "序号",
                                    prop: "index",
                                    canCheck: false,
                                },
                                {
                                    label: "参评人",
                                    prop: "postName",
                                    canCheck: false,
                                },
                                {
                                    label: "关系",
                                    prop: "relationName",
                                    canCheck: false,
                                },
                            ],
                        },
                        {
                            label: "能力分类",
                            canCheck: true,
                            childrenLabels: [],
                        },
                    ],
                    data: [],
                },
                moduleList: [],
                pageData: [],
            };
        },

        created() {
            this.getPostModuleFun();
            // this.getEvalPostObjectFun();
            this.getRelationInfoFun();
        },
        methods: {
            switchDisFlag(type) {
                if (type == 0) {
                    this.disFlag = !this.disFlag;
                }
                if (type == 1) {
                    this.saveEvalRelationFun();
                }
            },
            checkedLevel(row, index) {
                if (!row.selected) {
                    this.$set(this.weightList[index], "selected", true);
                    if (this.weightList[index].relationScopeList.length > 0) {
                        this.weightList[index].checkedVal = this.weightList[
                            index
                        ].relationScopeList[0].relationScope;
                        this.searchConfirmList.push(
                            JSON.parse(JSON.stringify(this.weightList[index]))
                        );
                    } else {
                        this.searchConfirmList.push({
                            selected: true,
                            checkedVal: true,
                            relationScopeList: [
                                {
                                    relationScope: true,
                                    relationScopeName: row.relationTypeName,
                                },
                            ],
                            relationType: row.relationType,
                            relationTypeName: row.relationTypeName,
                        });
                    }
                } else {
                    this.$set(this.weightList[index], "selected", false);
                    this.searchConfirmList.some((item, idx) => {
                        if (item.relationType == row.relationType) {
                            this.searchConfirmList.splice(idx, 1);
                        }
                    });
                }
                // console.log(this.searchConfirmList)
            },
            clickCallback(code) {
                console.log(code);
                if(code == this.checkedPostCode) return
                this.checkedPostCode = code;
                this.getEvalRelationFun(code.split(",")[0], code.split(",")[1]);

                this.pageData.some((row) => {
                    if (row.code == code) {
                        row["flag"] = true;
                    }
                });
            },
            cellClick(postCode, index, moduleCode) {
                let rowData = this.tableData.data[index];
                // console.log(rowData)
                if (!rowData.userCount) {
                    this.$msg.warning("当前岗位无人员，请先配置人员！");
                    return;
                }
                rowData.checkList.some((item, index) => {
                    if (item.moduleCode == moduleCode) {
                        this.saveUserObjectRelationFun(rowData, item);
                    }
                });
            },
            async getPostModuleFun() {
                await getPostModule({
                    evalId: this.evalId,
                }).then((res) => {
                    // console.log(res)
                    this.moduleList = res;
                    this.tableData.tableTitle[1].childrenLabels = res.map(
                        (item) => {
                            return {
                                label: `${item.moduleName}<br/>（词典数：${
                                    item.count || 0
                                }）`,
                                code: item.moduleCode,
                            };
                        }
                    );
                });
            },
            async getRelationInfoFun() {
                await getRelationInfo({
                    evalId: this.evalId,
                }).then((res) => {
                    // console.log(res)
                    this.weightList = res;
                    res.forEach((item) => {
                        if (item.selected) {
                            if (item.relationScopeList.length > 0) {
                                let obj = JSON.parse(JSON.stringify(item));
                                obj.relationScopeList.some((data) => {
                                    if (data.selected) {
                                        obj.checkedVal = data.relationScope;
                                    }
                                });
                                this.searchConfirmList.push(obj);
                            } else {
                                this.searchConfirmList.push({
                                    selected: true,
                                    checkedVal: true,
                                    relationScopeList: [
                                        {
                                            relationScope: true,
                                            relationScopeName:
                                                item.relationTypeName,
                                        },
                                    ],
                                    relationType: item.relationType,
                                    relationTypeName: item.relationTypeName,
                                });
                            }
                        }
                    });
                    // console.log(this.searchConfirmList)
                    // this.getEvalRelationFun();
                    this.getEvalPostObjectFun();
                });
            },
            saveEvalRelationFun() {
                // console.log(this.searchConfirmList);
                if (this.searchConfirmList.length == 0) {
                    this.$msg.warning("请选择评价关系！");
                    return;
                }
                let params = this.searchConfirmList.map((item) => {
                    let weight = 0;
                    this.weightList.some((data) => {
                        if (item.relationType == data.relationType) {
                            weight = data.weight;
                        }
                    });
                    return {
                        evalId: this.evalId,
                        relationScope:
                            item.checkedVal == true ? "" : item.checkedVal,
                        relationType: item.relationType,
                        weight: weight,
                    };
                });
                let count = 0;
                let flag = params.some((item) => {
                    if (Number(item.weight) == 0) {
                        // console.log(Number(item.weight))
                        return true;
                    }
                    count += Number(item.weight);
                });
                // console.log(count);
                if (flag || count != 100) {
                    this.$msg.warning("评价关系权重比例输入错误！");
                    return;
                }
                saveEvalRelation(params).then((res) => {
                    // console.log(res)
                    if (res.code == 200) {
                        this.$msg.success("保存成功！");
                        this.disFlag = !this.disFlag;
                        this.tableData.data = [];
                        this.getEvalRelationFun();
                    }
                });
            },
            // 获取测评对象数据
            getEvalRelationFun(objPostCode, objectId) {
                let params = {
                    coop: false,
                    self: false,
                    evalId: this.evalId,
                    // objPostCode:this.checkedPostCode,
                    peer: "",
                    sub: "",
                    sup: "",
                    objectId: this.checkedPostCode.split(',')[1],
                    objPostCode: this.checkedPostCode.split(',')[0],
                };
                this.searchConfirmList.forEach((item) => {
                    if (item.relationType == "S") {
                        params.self = item.checkedVal;
                    }
                    if (item.relationType == "O") {
                        params.coop = item.checkedVal;
                    }
                    if (item.relationType == "P") {
                        params.peer = item.checkedVal;
                    }
                    if (item.relationType == "U") {
                        params.sup = item.checkedVal;
                    }
                    if (item.relationType == "B") {
                        params.sub = item.checkedVal;
                    }
                });
                // console.log(params)
                getEvalRelation(params).then((res) => {
                    console.log(res);
                    let relationPostList = res.map((item, index) => {
                        return {
                            index: index + 1,
                            userName: item.userName,
                            userId: item.userId,
                            postName: item.postName,
                            postCode: item.postCode,
                            relationName: item.relationName,
                            relationType: item.relationType,
                            readOnlyModuleList: item.readOnlyModuleList,
                            userCount: item.userCount,
                            checkList: this.moduleList.map((data) => {
                                let checked = false;
                                if (
                                    item.postModuleList.indexOf(data.moduleCode) >
                                    -1
                                ) {
                                    checked = true;
                                }
                                return {
                                    moduleCode: data.moduleCode,
                                    moduleName: data.moduleName,
                                    checked,
                                };
                            }),
                        };
                    });
                    // let code = objPostCode + "," + objectId;
                    // let code = this.checkedPostCode;
                    this.pageData.map((item) => {
                        if (item.code == this.checkedPostCode) {
                            item["relationPostList"] = relationPostList;
                        }
                    });
                    // 获取评价关系数据
                    // this.defaultCheckedKeys = [];
                    // this.defaultCheckedKeys[0] = code;
                    // this.checkedPostCode = code;
                    this.tableData.data = relationPostList;
                });
            },
            // 获取评价关系数据
            getEvalPostObjectFun() {
                getEvalPostObject({ evalId: this.evalId }).then((res) => {
                    console.log(res);
                    this.pageData = res.map((row) => {
                        return {
                            value: row.objectName,
                            code: row.objPostCode + "," + row.objectId,
                            relationPostList: null,
                            flag: false,
                        };
                    });
                    let objectId = res[0]["objectId"];
                    let objPostCode = res[0]["objPostCode"];
                    this.pageData[0]["flag"] = true;
                    // this.getEvalRelationFun(objPostCode, objectId);
                });
            },
            saveUserObjectRelationFun(rowData, item) {
                // console.log(rowData)
                // console.log(item)
                let params = {
                    evalId: this.evalId,
                    moduleCode: item.moduleCode,
                    objPostCode: this.checkedPostCode.split(",")[0],
                    objectId: this.checkedPostCode.split(",")[1],
                    userId: rowData.userId,
                    postCode: rowData.postCode,
                    relationType: rowData.relationType,
                    select: !item.checked,
                };
                // console.log(params)
                saveUserObjectRelation([params]).then((res) => {
                    // console.log(res);
                    if (res.code == 200) {
                        item.checked = !item.checked;
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            //导出模板
            exportTemplate() {
                exportRelation({
                    evalId: this.evalId,
                }).then((res) => {
                    // console.log(res)
                    const blob = new Blob([res]);
                    const elink = document.createElement("a");
                    elink.download = "评价关系表.xlsx";
                    elink.style.display = "none";
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    URL.revokeObjectURL(elink.href); // 释放URL 对象
                    document.body.removeChild(elink);
                });
            },
            //导入数据
            fileChange(e) {
                // console.log(e)
                let formData = new FormData();
                let file = e.target.files[0];
                formData.append("file", file);
                formData.append("evalId", this.evalId);
                this.$refs.file.value = "";
                this.loading = true;
                importRelation(formData).then((res) => {
                    this.loading = false;
                    // console.log(res)
                    if (res.code == 200) {
                        this.$msg.success("上传成功");
                        this.getEvalRelationFun();
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },

            prev: function () {
                this.$emit("prevStep");
            },
            next: function () {
                // for (let i = 0; i < this.pageData.length; i++) {
                //     if (!this.pageData[i].flag) {
                //         this.$msg.warning(
                //             `${this.pageData[i].value}未选择能力词典！`
                //         );
                //         return;
                //     }
                //     for (let j = 0; j < this.moduleList.length; j++) {
                //         let checkedLength = 0;
                //         for (
                //             let k = 0;
                //             k < this.pageData[i].relationPostList.length;
                //             k++
                //         ) {
                //             let row = this.pageData[i].relationPostList[k];
                //             let rowCheckedLength = 0;
                //             for (let m = 0; m < row.checkList.length; m++) {
                //                 if (
                //                     row.checkList[m].checked ||
                //                     row.readOnlyModuleList.indexOf(
                //                         row.checkList[m].moduleCode
                //                     ) > -1
                //                 ) {
                //                     rowCheckedLength++;
                //                 }
                //             }
                //             if (rowCheckedLength == 0) {
                //                 this.$msg.warning(
                //                     `${this.pageData[i].value}-${row.postName}未选择能力词典！`
                //                 );
                //                 return;
                //             }
                //             if (
                //                 row.checkList[j].checked ||
                //                 row.readOnlyModuleList.indexOf(
                //                     row.checkList[j].moduleCode
                //                 ) > -1
                //             ) {
                //                 checkedLength++;
                //             }
                //         }
                //         if (checkedLength == 0) {
                //             this.$msg.warning(
                //                 `${this.pageData[i].value}的${this.moduleList[j].moduleName}未选择！`
                //             );
                //             return;
                //         }
                //     }
                // }
                this.$emit("nextStep");
            },
        },
    };
</script>

<style scoped lang="scss">
    .level_count {
        color: #f00;
    }
    .relationship_wrap {
        width: 100%;
        .level_wrap {
            .level_wrap_ul {
                width: 900px;
                height: 70px;
                li {
                    height: 62px;
                    margin-right: 10px;
                    border: 1px solid #e5e5e5;
                    cursor: pointer;
                    span {
                        display: block;
                        text-align: center;
                        line-height: 30px;
                        background: #EBF4FF;
                    }
                    .border_blue {
                        .el-input__inner {
                            border: 1px solid #0099FF;
                        }
                    }
                    .el-input__inner {
                        border-radius: 0;
                        /*border-left: none;*/
                        /*border-bottom: none;*/
                    }
                    .el-input-group__append {
                        border-radius: 0;
                        margin: 0;
                        /*border-right: none;*/
                        /*border-bottom: none;*/
                    }
                    .el-input.is-disabled {
                        .el-input__inner {
                            color: #212121;
                            background: #fff;
                        }
                    }
                    &.active {
                        border: 1px solid #0099FF;
                        span {
                            background: #0099FF;
                            color: #fff;
                        }
                    }
                }
            }
            .control {
                /*margin-top: 30px;*/
                margin-left: 10px;
                cursor: pointer;
                .icon_check {
                    font-size: 24px;
                    font-weight: bold;
                    color: #0099FF;
                    span {
                        font-size: 14px;
                        font-weight: normal;
                    }
                }
            }
        }
        .search_wrap {
            margin-top: 20px;
            .search_title {
                height: 30px;
                line-height: 30px;
                background: #EBF4FF;
                padding-left: 10px;
            }
            .search_main {
                padding: 10px;
                min-height: 80px;
                border: 1px solid #e5e5e5;
                .search_item {
                    line-height: 34px;
                    .search_label {
                        width: 60px;
                        text-align: center;
                        height: 24px;
                        line-height: 24px;
                        margin-top: 5px;
                        padding: 0 4px;
                        margin-right: 20px;
                        background: #EBF4FF;
                    }
                    .el-radio-group {
                        span {
                            line-height: 34px;
                        }
                    }
                }
            }
        }
        .relationship_main {
            margin-top: 20px;
            .post_left {
                width: 250px;
                .tree_box {
                    border: 1px solid #e5e5e5;
                    height: 300px;
                    .tree_title {
                        height: 30px;
                        line-height: 30px;
                        background: #EBF4FF;
                        padding: 0 10px;
                    }
                    .tree_main {
                        height: 270px;
                        padding: 10px;
                        overflow-y: auto;
                    }
                }
            }
            .relationship_right {
                width: 970px;
                padding-left: 20px;
                table {
                    width: 100%;
                    border: 1px solid #e5e5e5;
                    border-spacing: 0;
                    thead {
                        tr {
                            height: 45px;
                            background: #f4f4f4;
                            text-align: center;
                            td {
                                border-left: 1px solid #e5e5e5;
                                border-top: 1px solid #e5e5e5;
                                &:first-child {
                                    border-left: none;
                                }
                            }
                            &:first-child {
                                td {
                                    border-top: none;
                                }
                            }
                        }
                    }
                    tbody {
                        height: 465px;
                        overflow-y: auto;
                        tr {
                            height: 45px;
                            &:nth-child(even) {
                                background: #f4f4f4;
                            }
                            td {
                                border-left: 1px solid #e5e5e5;
                                border-top: 1px solid #e5e5e5;
                                &.can_handler {
                                    cursor: pointer;
                                }
                                &:first-child {
                                    border-left: none;
                                }
                                .td_disabled {
                                    font-size: 24px;
                                    color: #bbb;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .el-radio__input.is-checked .el-radio__inner {
        background: #0099FF;
        border-color: #0099FF;
    }
     .table_wrap tbody td {
        padding: 0 !important;
    }

     .table_wrap tbody .cell {
        padding: 0;
        height: 100%;
        cursor: pointer;
    }

    .check_box_wrap {
        color: #0099fd;
        font-weight: 700;
        font-size: 24px;
    }
    .upload_wrap {
        position: relative;
        width: 80px;
        height: 30px;
        margin-left: 10px;
        cursor: pointer;
    }
    .import_btn {
        font-size: 0;
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .el-icon-loading:before {
        font-size: 24px;
    }
</style>