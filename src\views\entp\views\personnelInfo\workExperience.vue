<template>
  <div class="edu_info_wrap bg_write">
    <div class="page_main_title">工作履历</div>
    <div class="page_section clearfix">
      <div class="oper_btn_wrap">
        <el-button type="primary" class="page_add_btn" @click="addItem">新增</el-button>
      </div>
      <div class="edu_info_center">
        <div class="edu_info_header">
          <div class="item name">公司名称</div>
          <div class="item long">开始日期</div>
          <div class="item long">结束日期</div>
          <div class="item">岗位职层</div>
          <div class="item">最高职层</div>
          <div class="item">岗位类型</div>
          <div class="item">业务领域</div>
          <div class="item short">
            同岗位

            <el-tooltip
              class="item_tootip"
              effect="dark"
              content="在其他公司从事过与当前岗位一样或相近的岗位"
              placement="top"
            >
              <i class="el-icon-question icon_color"></i>
            </el-tooltip>
          </div>
          <div class="item short">
            同行业
            <el-tooltip
              class="item_tootip"
              effect="dark"
              content="其他工作的公司与当前公司属于同一行业或相近行业"
              placement="top"
            >
              <i class="el-icon-question icon_color"></i>
            </el-tooltip>
          </div>
          <div class="item item_icon_wrap">操作</div>
        </div>
        <div class="edu_info_mmain">
          <work-item :workData="workData" @deleteItem="deleteItem"></work-item>
          <div class="pagination_wrap">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="page.current"
              :page-sizes="[10, 20, 50, 100, 200]"
              :page-size="page.size"
              layout="total, sizes, prev, pager, next, jumper"
              :total="page.total"
            ></el-pagination>
          </div>
          <div class="align_center paddT_20" v-if="workData.length > 0">
            <el-button type="primary" class="page_confirm_btn" @click="submit">确认</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import workItem from './components/workExperienceItem.vue'
import { getExperience, addExperience, delExperience, getCompanyInfo } from '../../request/api'
import { useUserStore } from '@/stores/modules/user'
import { objHasEmpty, deepClone } from '@/utils/utils'

const userStore = useUserStore()
const userId = computed(() => userStore.userInfo.userId)
const companyId = computed(() => userStore.userInfo.companyId)

const submitFlag = ref(true)
const workData = ref([])
const page = reactive({
  current: 1,
  size: 10,
  total: 0
})

function deleteItem(item, index) {
  if (!Object.prototype.hasOwnProperty.call(item, 'experienceId')) {
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        workData.value.splice(index, 1)
        ElMessage({ type: 'success', message: '删除成功!' })
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消删除' })
      })
  } else {
    let experienceId = item.experienceId
    ElMessageBox.confirm('确认删除此条信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        delExperience({ experienceId, userId: userId.value }).then(res => {
          if (res.code == '200') {
            page.current = 1
            getExperienceData()
            ElMessage.success(res.msg)
          } else {
            ElMessage.error(res.msg)
          }
        })
      })
      .catch(() => {
        ElMessage({ type: 'info', message: '已取消删除' })
      })
  }
}

function addItem() {
  let obj = workData.value[workData.value.length - 1]
  let addObj = {
    companyName: '',
    jobLevelCode: '',
    highestJobLevel: '',
    jobClassCode: '',
    bizDomainCode: '',
    postRelated: '',
    industryRelated: '',
    beginDate: '',
    endDate: '',
    userId: userId.value
  }
  if (!obj) {
    workData.value.push(addObj)
    return
  }
  if (checkData(workData.value)) {
    ElMessage({ message: '请完善当前信息后新增！', type: 'warning' })
    return
  }
  workData.value.push(addObj)
}

function submit() {
  if (!submitFlag.value) return
  if (checkData(workData.value)) {
    ElMessage.warning('请完善信息后提交！')
    return
  }
  if (workData.value.length == 0) {
    ElMessage.warning('请新增后提交！')
    return
  }
  submitFlag.value = false
  let backupData = deepClone(workData.value)
  backupData.forEach(obj => {
    if (typeof obj.jobClassCode !== 'string' && Array.isArray(obj.jobClassCode)) {
      obj.jobClassCode = obj.jobClassCode[obj.jobClassCode.length - 1]
    }
    if (typeof obj.jobLevelCode !== 'string' && Array.isArray(obj.jobLevelCode)) {
      obj.jobLevelCode = obj.jobLevelCode[obj.jobLevelCode.length - 1]
    }
  })
  let params = backupData
  addExperience(params).then(res => {
    if (res.code == 200) {
      ElMessage.success(res.msg)
      submitFlag.value = true
      getExperienceData()
    } else {
      submitFlag.value = true
      ElMessage.error(res.msg)
    }
  })
}

function getExperienceData() {
  getExperience({
    current: page.current,
    size: page.size,
    userId: userId.value
  }).then(res => {
    workData.value = []
    if (res.code == 200) {
      if (res.data.length > 0) {
        workData.value = res.data.map(item => ({
          companyName: item.companyName,
          jobLevelCode: item.jobLevelCode,
          highestJobLevel: item.highestJobLevel,
          jobClassCode: item.jobClassCode,
          bizDomainCode: item.bizDomainCode,
          postRelated: item.postRelated,
          industryRelated: item.industryRelated,
          beginDate: item.beginDate,
          endDate: item.endDate,
          userId: item.userId,
          experienceId: item.experienceId
        }))
      } else {
        getCompanyInfo({ companyId: companyId.value }).then(res => {
          workData.value.push({
            companyName: res.companyName,
            jobLevelCode: '',
            jobClassCode: '',
            bizDomainCode: '',
            postRelated: '',
            industryRelated: '',
            beginDate: userStore.userInfo.currentEmpDate,
            endDate: '',
            userId: userId.value
          })
        })
      }
      Object.assign(page, res.page)
    } else {
      page.current = 1
      page.size = 10
      page.total = 0
      ElMessage.error('获取数据失败!')
    }
  })
}

function checkData(data) {
  for (let index = 0; index < data.length; index++) {
    const obj = data[index]
    if (objHasEmpty(obj, [], ['endDate'])) {
      return true
    }
  }
  return false
}

function handleCurrentChange(curPage) {
  page.current = curPage
  getExperienceData()
}
function handleSizeChange(curSize) {
  page.size = curSize
  getExperienceData()
}

watch(
  userId,
  val => {
    if (val) {
      getExperienceData()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.edu_info_header {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  background: #f4f4f4;
  color: #525e6c;
  font-size: 14px;
  padding: 0 16px 0 50px;
  height: 45px;

  .item {
    /*flex: 1;*/
    position: relative;
    padding: 0 4px;
    line-height: 45px;
    text-align: left;
    width: 15%;

    &.name {
      width: 20%;
    }
    &.short {
      width: 7%;
    }
    .item_tootip {
      position: absolute;
      top: 5px;
      cursor: pointer;
      &:hover {
        color: #0099fd;
      }
    }
  }

  .item_icon_wrap {
    text-align: center;
    width: 5%;
  }
}

:deep(.el-date-editor) {
  .el-range-input {
    height: 26px;
  }
}
</style>
