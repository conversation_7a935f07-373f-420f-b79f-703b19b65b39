<template>
    <div class="pie_chart_main">
        <div class :id="id" :style="styleObj"></div>
    </div>
</template>
 
<script>
export default {
    name: "pieChart",
    /**
     isPercent : 是否是百分比数字，默认false ， true则在数字后面拼接 “ % ”
     isAnnular : 是否是圆环状饼图，默认false ， true则是圆环状 环状区间 30% ~ 70%;
    **/ 
    props: ["chartData", "width", "height","isPercent","isAnnular"],
    components: {},
    data() {
        return {
            id: "",
            styleObj: {
                width: this.width + "px",
                height: this.height + "px"
            }
        };
    },
    watch: {
        chartData: {
            handler() {
                this.init(this.chartData);
            },
            deep: true
        }
    },
    created() {},
    mounted() {
        this.$nextTick(function() {
            console.log(this.chartData);

            if (this.chartData.length == 0) {
                return;
            }
            this.init(this.chartData);
        });
    },
    methods: {
        init(chartData) {
            let id = this.$util.createRandomId();
            this.id = id;
            this.$nextTick(() => {
                this.toDraw(id, chartData);
            });
        },
        toDraw(id, chartData) {
            let myChart = this.$EC.init(document.getElementById(id));

            if (chartData.length == 0) {
                myChart.clear();
                return;
            }
            let percentSymbol = this.isPercent ? '%' : '';
            let radius1 = this.isAnnular ? '30%' : '0';
            let option = {
                tooltip: {
                    trigger: "item",
                    formatter: `{b}: {c}${percentSymbol}`
                },
                legend: {
                    right: 10
                },
                series: [
                    {
                        type: "pie",
                        radius: [radius1, "70%"],
                        avoidLabelOverlap: false,
                        label: {
                            position: "inside",
                            formatter: `{c}${percentSymbol}`
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: "30",
                                fontWeight: "bold"
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: chartData
                    }
                ]
            };
            myChart.setOption(option);
        }
    }
};
</script>
 
<style scoped lang="scss">
</style>