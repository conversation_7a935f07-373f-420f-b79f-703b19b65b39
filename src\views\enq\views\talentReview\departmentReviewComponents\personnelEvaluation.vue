<template>
  <div class="task_confirmation_main marginT_30">
    <div class="page_second_title">人员评价</div>
    <div class="personnel_info marginT_20">
      <div>
        <span>人员</span>
        <span>{{ confirmNum }} /</span>
        <span>{{ personnelData.length }}</span>
      </div>
      <div class="flex_row_start">
        <span class="name">{{ personnelName }}</span>
        <span class="department">{{ personnelDepartment }}</span>
        <div class="post_list_wrap flex_row_start">
          <div
            class="post_list"
            :class="{ active: personnelPost == item.postCode }"
            @click="changePost(item.postCode)"
            v-for="item in postOptions"
            :key="item.postCode"
          >
            {{ item.postName }}
          </div>
        </div>
      </div>
    </div>
    <div class="department_main">
      <div class="personnel_item_wrap">
        <div
          class="personnel_item"
          v-for="(item, index) in personnelData"
          :class="{ completed: item.confirmStatus == 'Y', curr: currIndex == index }"
          :key="index"
          @click="selectPersonnel(index)"
        >
          <span>{{ item.userName }}</span>
          <i class="el-icon-check" v-if="item.confirmStatus == 'Y'"></i>
          <i class="icon disc" v-else></i>
        </div>
      </div>
      <div class="personnel_form_wrap clearfix">
        <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="100px" class="demo-ruleForm form_main">
          <el-form-item label="绩效表现" prop="kpiRank">
            <el-radio-group v-model="ruleForm.kpiRank" size="small">
              <el-radio-button v-for="item in optionsCfg.kpiRank" :key="item.dictCode" :label="item.dictCode">
                {{ item.codeName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="核心能力" prop="competenceRank">
            <el-radio-group v-model="ruleForm.competenceRank" size="small">
              <el-radio-button v-for="item in optionsCfg.competenceRank" :key="item.dictCode" :label="item.dictCode">
                {{ item.codeName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="发展潜力" prop="developmentPotential">
            <el-radio-group v-model="ruleForm.developmentPotential" size="small">
              <el-radio-button
                v-for="item in optionsCfg.developmentPotential"
                :key="item.dictCode"
                :label="item.dictCode"
              >
                {{ item.codeName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="人才分类" prop="talentClass">
            <el-radio-group v-model="ruleForm.talentClass" size="small">
              <el-radio-button v-for="item in optionsCfg.talentClass" :key="item.dictCode" :label="item.dictCode">
                {{ item.codeName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="晋升可能" prop="promotionPossibility">
            <el-radio-group v-model="ruleForm.promotionPossibility" size="small">
              <el-radio-button
                v-for="item in optionsCfg.promotionPossibility"
                :key="item.dictCode"
                :label="item.dictCode"
              >
                {{ item.codeName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="离职风险" prop="retentionRisk">
            <el-radio-group v-model="ruleForm.retentionRisk" size="small">
              <el-radio-button v-for="item in optionsCfg.retentionRisk" :key="item.dictCode" :label="item.dictCode">
                {{ item.codeName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="离职影响" prop="dimissionImpact">
            <el-radio-group v-model="ruleForm.dimissionImpact" size="small">
              <el-radio-button v-for="item in optionsCfg.dimissionImpact" :key="item.dictCode" :label="item.dictCode">
                {{ item.codeName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="内部可替代" prop="innerSubstitution">
            <el-radio-group v-model="ruleForm.innerSubstitution" size="small">
              <el-radio-button v-for="item in optionsCfg.innerSubstitution" :key="item.dictCode" :label="item.dictCode">
                {{ item.codeName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="外部可替代" prop="externalSubstitution">
            <el-radio-group v-model="ruleForm.externalSubstitution" size="small">
              <el-radio-button
                v-for="item in optionsCfg.externalSubstitution"
                :key="item.dictCode"
                :label="item.dictCode"
              >
                {{ item.codeName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="离职可能性" prop="dimissionPossibility">
            <el-radio-group v-model="ruleForm.dimissionPossibility" size="small">
              <el-radio-button
                v-for="item in optionsCfg.dimissionPossibility"
                :key="item.dictCode"
                :label="item.dictCode"
              >
                {{ item.codeName }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="能否继任" prop="successionPossibility">
            <el-select v-model="ruleForm.successionPossibility" size="small" placeholder="">
              <el-option
                v-for="item in optionsCfg.successionPossibility"
                :key="item.dictCode"
                :value="item.dictCode"
                :label="item.codeName"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="个人优势" prop="strength">
            <el-input type="textarea" v-model="ruleForm.strength"></el-input>
          </el-form-item>
          <el-form-item label="待发展" prop="weakness">
            <el-input type="textarea" v-model="ruleForm.weakness"></el-input>
          </el-form-item>
          <el-form-item>
            <div class="paddT_12">
              <el-button class="page_add_btn" type="primary" @click="submitForm(ruleFormRef)">确定</el-button>
              <el-button class="page_clear_btn" @click="resetForm(ruleFormRef)" size="small">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
        <div class="matrix_chart_wrap">
          <div class="matrix_chart">
            <div class="matrix_head">
              <div class="title">核心能力</div>
              <div class="flex_row_start border">
                <div class="item" v-for="item in matrixCompetenceRank" :key="item.dictCode">{{ item.codeName }}</div>
              </div>
            </div>
            <div class="clearfix">
              <div class="matrix_aside">
                <div class="matrix_aside_head flex_row_start">
                  <div class="title">绩效指标</div>
                  <div class="flex_col_start border">
                    <div class="item" v-for="item in optionsCfg.kpiRank" :key="item.dictCode">{{ item.codeName }}</div>
                  </div>
                </div>
              </div>
              <div class="matrix_main">
                <div
                  class="matrix_row"
                  :class="'matrix_row_' + (index + 1)"
                  v-for="(item, index) in matrixData1"
                  :key="index"
                >
                  <div class="item" :class="'item_' + list.key" v-for="list in item" :key="list.key">
                    <div class="fs14 marginB_16">{{ matrixTextObj[list.key] }}</div>
                    <el-popover
                      v-if="list.list.length > 0"
                      class="popover_dom"
                      placement="top-start"
                      title="人员"
                      :width="200"
                      trigger="click"
                    >
                      <template #default>
                        <div>
                          <span v-for="i in list.list" :key="i">{{ i }}、</span>
                        </div>
                      </template>
                      <template #reference>
                        <div>{{ list.size ? list.size + ' 人' : '' }}</div>
                      </template>
                    </el-popover>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="matrix_chart small">
            <div class="matrix_head">
              <div class="title">商业风险</div>
              <div class="flex_row_start border">
                <div class="item" v-for="item in optionsCfg.dimissionImpact" :key="item.dictCode">
                  {{ item.codeName }}
                </div>
              </div>
            </div>
            <div class="clearfix">
              <div class="matrix_aside">
                <div class="matrix_aside_head flex_row_start">
                  <div class="title">离职风险</div>
                  <div class="flex_col_start border">
                    <div class="item" v-for="item in optionsCfg.retentionRisk" :key="item.dictCode">
                      {{ item.codeName }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="matrix_main">
                <div
                  class="matrix_row"
                  :class="'matrix_row_' + (index + 1)"
                  v-for="(item, index) in matrixData2"
                  :key="index"
                >
                  <div class="item" v-for="list in item" :class="'item_' + list.key" :key="list.key">
                    <el-popover
                      v-if="list.list.length > 0"
                      class="popover_dom"
                      placement="top-start"
                      title="人员"
                      :width="200"
                      trigger="click"
                    >
                      <template #default>
                        <div>
                          <span v-for="i in list.list" :key="i">{{ i }}、</span>
                        </div>
                      </template>
                      <template #reference>
                        <div>{{ list.size ? list.size + ' 人' : '' }}</div>
                      </template>
                    </el-popover>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="matrix_chart small">
            <div class="matrix_head">
              <div class="title">核心能力</div>
              <div class="flex_row_start border">
                <div class="item">高</div>
                <div class="item">中</div>
                <div class="item">低</div>
              </div>
            </div>
            <div class="clearfix">
              <div class="matrix_aside">
                <div class="matrix_aside_head flex_row_start">
                  <div class="title">发展潜力</div>
                  <div class="flex_col_start border">
                    <div class="item" v-for="item in optionsCfg.developmentPotential" :key="item.dictCode">
                      {{ item.codeName }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="matrix_main">
                <div
                  class="matrix_row"
                  :class="'matrix_row_' + (index + 1)"
                  v-for="(item, index) in matrixData3"
                  :key="index"
                >
                  <div class="item" :class="'item_' + list.key" v-for="list in item" :key="list.key">
                    <el-popover
                      v-if="list.list.length > 0"
                      class="popover_dom"
                      placement="top-start"
                      title="人员"
                      :width="200"
                      trigger="click"
                    >
                      <template #default>
                        <div>
                          <span v-for="i in list.list" :key="i">{{ i }}、</span>
                        </div>
                      </template>
                      <template #reference>
                        <div>{{ list.size ? list.size + ' 人' : '' }}</div>
                      </template>
                    </el-popover>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="oper_btn_wrap align_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import {
  getDeptUserPost,
  getUserEval,
  saveUserEval,
  getPostByLikeName,
  kpiCapablityInterval,
  retentionInterval,
  developmentCapabilityInterval
} from '../../../request/api'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  orgCode: String
})

const emit = defineEmits(['prevStep', 'nextStep'])

const userStore = useUserStore()

const matrixTextObj = {
  AA: '明星人才',
  AB: '核心人才',
  AC: '关注人才',
  BA: '核心人才',
  BB: '骨干人才',
  BC: '关注人才',
  CA: '待提升人才',
  CB: '待提升人才',
  CC: '待优化人才'
}

const ruleFormRef = ref(null)
const savaFlag = ref(true)
const optionsCfg = ref({})
const currIndex = ref(0)
const personnelName = ref('')
const personnelDepartment = ref('')
const personnelPost = ref('')
const postOptions = ref([])
const confirmNum = ref(0)
const personnelData = ref([])
const loading = ref(false)
const expectationPostOptions = ref([])
const matrixCompetenceRank = ref([])
const matrixData1 = ref([])
const matrixData2 = ref([])
const matrixData3 = ref([])
const userId = ref('')

const ruleForm = reactive({
  kpiRank: '',
  competenceRank: '',
  developmentPotential: '',
  talentClass: '',
  promotionPossibility: '',
  retentionRisk: '',
  dimissionImpact: '',
  innerSubstitution: '',
  externalSubstitution: '',
  dimissionPossibility: '',
  successionPossibility: '',
  strength: '',
  weakness: ''
})

const rules = {
  kpiRank: [{ required: true, message: '请选择绩效表现', trigger: 'change' }],
  competenceRank: [{ required: true, message: '请选择核心能力', trigger: 'change' }],
  developmentPotential: [{ required: true, message: '请选择发展潜力', trigger: 'change' }],
  talentClass: [{ required: true, message: '请选择人才分类', trigger: 'change' }],
  promotionPossibility: [{ required: true, message: '请选择晋升可能', trigger: 'change' }],
  retentionRisk: [{ required: true, message: '请选择离职风险', trigger: 'change' }],
  dimissionImpact: [{ required: true, message: '请选择离职影响', trigger: 'change' }],
  innerSubstitution: [{ required: true, message: '请选择内部可替代', trigger: 'change' }],
  externalSubstitution: [{ required: true, message: '请选择外部可替代', trigger: 'change' }],
  dimissionPossibility: [{ required: true, message: '请选择离职可能性', trigger: 'change' }],
  successionPossibility: [{ required: true, message: '请选择能否继任', trigger: 'change' }],
  strength: [{ required: true, message: '请填写个人优势', trigger: 'blur' }],
  weakness: [{ required: true, message: '请填写待发展', trigger: 'blur' }]
}

onMounted(async () => {
  const docList = [
    'KPI_RANK',
    'COMPETENCE_RANK',
    'DEVELOPMENT_POTENTIAL',
    'TALENT_CLASS',
    'PROMOTION_POSSIBILITY',
    'RETENTION_RISK',
    'DIMISSION_IMPACT',
    'INNER_SUBSTITUTION',
    'EXTERNAL_SUBSTITUTION',
    'EXPECTATION_CYCLE',
    'DIMISSION_POSSIBILITY',
    'SUCCESSION_POSSIBILITY'
  ]

  const res = await window.$getDocList(docList)
  optionsCfg.value = {
    kpiRank: res.KPI_RANK,
    competenceRank: res.COMPETENCE_RANK,
    developmentPotential: res.DEVELOPMENT_POTENTIAL,
    talentClass: res.TALENT_CLASS,
    promotionPossibility: res.PROMOTION_POSSIBILITY,
    retentionRisk: res.RETENTION_RISK,
    dimissionImpact: res.DIMISSION_IMPACT,
    innerSubstitution: res.INNER_SUBSTITUTION,
    externalSubstitution: res.EXTERNAL_SUBSTITUTION,
    dimissionPossibility: res.DIMISSION_POSSIBILITY,
    successionPossibility: res.SUCCESSION_POSSIBILITY
  }
  matrixCompetenceRank.value = window.$util.deepClone(res.COMPETENCE_RANK)

  getDeptUserPostFun()
  getMatrixData()
})

const getDeptUserPostFun = async (refreshFlag = false) => {
  const params = {
    enqId: props.enqId,
    orgCode: props.orgCode,
    moduleId: 'D08'
  }

  try {
    const res = await getDeptUserPost(params)
    if (res.code == 200) {
      confirmNum.value = 0
      personnelData.value = res.data
      personnelData.value.forEach(item => {
        if (item.confirmStatus == 'Y') {
          confirmNum.value++
        }
      })

      if (!refreshFlag) {
        const data = personnelData.value[currIndex.value]
        postOptions.value = data.postNameList
        userId.value = data.userId
        personnelName.value = data.userName
        personnelDepartment.value = data.orgName
        personnelPost.value = data.postNameList[0].postCode
      }
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const getUserEvalFun = async () => {
  const params = {
    enqId: props.enqId,
    userId: userId.value,
    postCode: personnelPost.value
  }

  try {
    const res = await getUserEval(params)
    if (res.code == 200) {
      ElMessage.success(res.msg)
      const data = res.data
      if (data) {
        Object.assign(ruleForm, data)
      } else {
        resetForm(ruleFormRef.value)
      }
    } else {
      ElMessage.error(res.msg)
      resetForm(ruleFormRef.value)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const saveUserEvalFun = async prevStep => {
  const params = {
    ...ruleForm,
    enqId: props.enqId,
    userId: userId.value,
    postCode: personnelPost.value
  }

  try {
    const res = await saveUserEval(params)
    if (res.code == 200) {
      ElMessage.success(res.msg)
      if (prevStep) {
        emit('prevStep')
      }
      getDeptUserPostFun(true)
      getMatrixData()
      savaFlag.value = true
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('保存数据失败')
  }
}

const selectPersonnel = index => {
  currIndex.value = index
  expectationPostOptions.value = []
}

const submitForm = async formEl => {
  if (!formEl) return

  try {
    await formEl.validate()
    if (savaFlag.value) {
      savaFlag.value = false
      saveUserEvalFun()
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const resetForm = formEl => {
  if (!formEl) return
  formEl.resetFields()
}

const getMatrixData = () => {
  kpiCapablityIntervalFun()
  retentionIntervalFun()
  developmentCapabilityIntervalFun()
}

const kpiCapablityIntervalFun = async () => {
  try {
    const res = await kpiCapablityInterval({
      enqId: props.enqId,
      orgCode: props.orgCode
    })
    matrixData1.value = res
  } catch (error) {
    console.error(error)
  }
}

const retentionIntervalFun = async () => {
  try {
    const res = await retentionInterval({
      enqId: props.enqId,
      orgCode: props.orgCode
    })
    matrixData2.value = res
  } catch (error) {
    console.error(error)
  }
}

const developmentCapabilityIntervalFun = async () => {
  try {
    const res = await developmentCapabilityInterval({
      enqId: props.enqId,
      orgCode: props.orgCode
    })
    matrixData3.value = res
  } catch (error) {
    console.error(error)
  }
}

const changePost = postCode => {
  personnelPost.value = postCode
  expectationPostOptions.value = []
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submitForm(ruleFormRef.value, 'prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') {
        emit('prevStep')
      }
    })
}

const nextBtn = () => {
  for (const item of personnelData.value) {
    if (item.confirmStatus == 'N') {
      ElMessage.error(`请完善人员：${item.userName}的评价后再次提交！`)
      return
    }
  }
  emit('nextStep')
}

watch(
  () => currIndex.value,
  async val => {
    const data = personnelData.value[val]
    userId.value = data.userId
    postOptions.value = data.postNameList
    personnelName.value = data.userName
    personnelDepartment.value = data.orgName
    personnelPost.value = data.postNameList[0].postCode
    await getUserEvalFun()
  }
)

watch(
  () => personnelPost.value,
  async () => {
    await getUserEvalFun()
  }
)
</script>

<style scoped lang="scss">
.personnel_info {
  // text-align: right;
  // background: #f4f4f4;
  // line-height: 45px;
  // margin-bottom: 16px;
  // padding: 0 16px;
  font-weight: bold;
  background: #f4f4f4;
  line-height: 25px;
  margin-bottom: 16px;
  padding: 12px 16px;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  .department {
    // color: #449CFF;
    // border: 1px solid #449CFF;
    // padding: 2px 5px;
    // border-radius: 3px;
    // margin: 0 16px;
    color: #449cff;
    border: 1px solid #449cff;
    padding: 1px 6px;
    line-height: 21px;
    border-radius: 3px;
    margin: 0 16px;
    background: #fff;
  }
  .inline_b {
    width: auto;
  }
  .post_list_wrap {
    .post_list {
      cursor: pointer;
      margin-right: 16px;
      &.active {
        color: #ffc000;
        // color: #0099FF;
      }
    }
  }
}

.personnel_item_wrap {
  width: 120px;
  float: left;
  margin-right: 16px;

  .personnel_item {
    line-height: 30px;
    padding: 0 8px;
    color: #525e6c;
    font-size: 14px;
    background: #f8f8f8;
    margin-bottom: 5px;
    font-weight: bold;
    cursor: pointer;

    &.completed {
      color: #0099fd;
      background: #eef5fb;

      .icon {
        display: block;
      }
    }

    &.curr {
      background: #0099fd;
      color: #fff;

      .icon {
        display: block;
        color: #fff;

        &.disc {
          background: #fff;
        }
      }
    }

    .icon {
      display: none;
      float: right;
      font-weight: bold;
      line-height: 30px;
      text-align: center;
      color: #0099fd;

      &.disc {
        width: 8px;
        height: 8px;
        margin: 10px 4px 0 auto;
        border-radius: 50%;
        background: #ffc000;
      }
    }
  }
}

.personnel_form_wrap {
  overflow: hidden;

  .form_main {
    float: left;
    width: 510px;
    display: flex;
    flex-flow: row wrap;
    justify-content: flex-start;
  }
}

.matrix_chart_wrap {
  overflow: hidden;
  margin-left: 20px;
  float: right;
}

.matrix_chart {
  width: 480px;
  /*float: left;*/
  margin-right: 6px;
  margin-bottom: 16px;

  &.small {
    width: 240px;
    float: left;
    margin-top: 20px;

    .matrix_head {
      line-height: 20px;

      .title {
        height: 20px;
        padding-left: 40px;
      }

      .flex_row_start {
        height: 20px;
        margin-left: 40px;
      }
    }

    .matrix_aside {
      width: 40px;
      height: 190px;

      .title {
        width: 20px;
        height: calc(100% + 40px);
        margin-top: -40px;
      }

      .flex_col_start {
        width: 20px;
      }

      .item {
        line-height: 60px;
      }
    }

    .matrix_main {
      .matrix_row {
        &_1 {
          .item_1 {
            background-color: #e28d80;
          }

          .item_2 {
            background-color: #719dd5;
          }

          .item_3 {
            background-color: #a3d0f3;
          }
        }

        &_2 {
          .item_1 {
            background-color: #719dd5;
          }

          .item_2,
          .item_3 {
            background-color: #a3d0f3;
          }
        }

        &_3 {
          .item_1,
          .item_2 {
            background-color: #a3d0f3;
          }

          .item_3 {
            background-color: #dddee3;
          }
        }
      }

      .item {
        width: 66px;
        height: 63px;
      }
    }
  }

  .matrix_head {
    width: 100%;
    // padding-left: 45px;
    // margin-left: 45px;
    text-align: left;
    line-height: 30px;

    .title {
      height: 30px;
      background: #fbfbfb;
      padding-left: 100px;
    }

    .flex_row_start {
      height: 30px;
      margin-left: 100px;

      &.border {
        border-bottom: 1px solid #f6f6f6;
      }
    }

    .item {
      flex: 1;
      text-align: center;
    }
  }

  .matrix_aside {
    float: left;
    width: 100px;
    height: 230px;
    text-align: center;

    .matrix_aside_head {
      height: 100%;
    }

    .title {
      height: calc(100% + 50px);
      padding: 30px 10px 0 5px;
      width: 30px;
      background: #fbfbfb;
      margin-top: -50px;
    }

    .flex_col_start {
      height: 100%;
      width: 70px;

      &.border {
        border-right: 1px solid #f6f6f6;
      }
    }

    .item {
      flex: 1;
      line-height: 45px;
    }
  }

  .matrix_main {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .matrix_row {
      width: 100%;
      display: flex;
      flex: row nowrap;
      // &_1 {
      //     .item_1,
      //     .item_2 {
      //         background-color: #e28d80;
      //     }
      //     .item_3 {
      //         background-color: #719dd5;
      //     }
      //     .item_4,
      //     .item_5 {
      //         background-color: #bed269;
      //     }
      // }

      // &_2 {
      //     .item_1 {
      //         background-color: #e28d80;
      //     }
      //     .item_2,
      //     .item_3 {
      //         background-color: #719dd5;
      //     }
      //     .item_4,
      //     .item_5 {
      //         background-color: #bed269;
      //     }
      // }

      // &_3 {
      //     .item_1,
      //     .item_2 {
      //         background-color: #719dd5;
      //     }
      //     .item_3 {
      //         background-color: #a3d0f3;
      //     }
      //     .item_4,
      //     .item_5 {
      //         background-color: #bed269;
      //     }
      // }
      // &_4 {
      //     .item_1,
      //     .item_2,
      //     .item_3 {
      //         background-color: #a3d0f3;
      //     }
      //     .item_4 {
      //         background-color: #bed269;
      //     }
      //     .item_5 {
      //         background-color: #dddee3;
      //     }
      // }

      // &_5 {
      //     .item_1,
      //     .item_2,
      //     .item_3 {
      //         background-color: #a3d0f3;
      //     }
      //     .item_4,
      //     .item_5 {
      //         background-color: #dddee3;
      //     }
      // }
    }

    .item {
      flex: 1;
      // width: 125px;
      height: 76px;
      margin: 0 1px 1px 0;
      padding: 2px;
      color: #fff;
      font-size: 16px;
      overflow: hidden;
      &_AA {
        background-color: #e28d80;
      }
      &_AB {
        background-color: #719dd5;
      }
      &_AC {
        background-color: #a3d0f3;
      }
      &_BA {
        background-color: #719dd5;
      }
      &_BB {
        background-color: #bed269;
      }
      &_BC {
        background-color: #a3d0f3;
      }
      &_CA {
        background-color: #a3d0f3;
      }
      &_CB {
        background-color: #a3d0f3;
      }
      &_CC {
        background-color: #dddee3;
      }
      // 矩阵下方 三个颜色样式
      &_HA,
      &_HH {
        background-color: #e28d80;
      }
      &_HB,
      &_HM {
        background-color: #719dd5;
      }
      &_HC,
      &_HL {
        background-color: #bed269;
      }
      &_MA,
      &_MH {
        background-color: #719dd5;
      }
      &_MB,
      &_MM {
        background-color: #bed269;
      }
      &_MC,
      &_ML {
        background-color: #bed269;
      }
      &_LA,
      &_LH {
        background-color: #bed269;
      }
      &_LB,
      &_LM {
        background-color: #bed269;
      }
      &_LC,
      &_LL {
        background-color: #a3d0f3;
      }
      // &.level_1 {
      //     background-color: #e28d80;
      // }

      // &.level_2 {
      //     background-color: #719dd5;
      // }

      // &.level_3 {
      //     background-color: #bed269;
      // }

      // &.level_4 {
      //     background-color: #a3d0f3;
      // }

      // &.level_5 {
      //     background-color: #dddee3;
      // }
    }
  }
}

.el-form-item {
  width: 100%;
  margin-bottom: 14px;
}

.el-form-item__content {
  line-height: 30px;
}

.el-form-item__label {
  line-height: 30px;
}

.el-input__inner {
  width: 100%;
}

.el-form-item__error {
  padding-top: 2px;
}

.el-textarea {
  padding-top: 10px;
  width: 100%;
}

.el-select {
  width: 100%;
}

.el-radio-group {
  display: flex;

  label {
    flex: 1;

    span {
      width: 100%;
    }
  }
}

.popover_dom {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.el-popover__reference {
  width: 100%;
  height: 100%;
}
</style>
