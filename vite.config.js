import { defineConfig, loadEnv } from 'vite'
import { createPlugins } from './vite/plugins'
import path from 'path'
import tailwindcss from '@tailwindcss/vite'
import vueJsx from '@vitejs/plugin-vue-jsx'

export default defineConfig(({ mode, command }) => {
  console.log(mode)

  const env = loadEnv(mode, process.cwd(), '')
  return {
    base: './',
    mode: mode || 'development',
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/variables.scss";@import "@/styles/mixins.scss";`
        }
      }
    },
    plugins: [tailwindcss(), vueJsx(), ...createPlugins(env, command == 'build')],
    server: {
      open: true,
      host: '0.0.0.0',
      port: Number(env.VITE_APP_PORT) || 8080,
      hmr: true,
      proxy: {
        [env.VITE_API_PREFIX]: {
          target: env.VITE_APP_API_DOMAIN,
          changeOrigin: true,
          rewrite: path => {
            console.log(path)

            // path.replace(/^\/api/, '')
            return path
          }
        }
      }
    },
    //打包配置
    build: {
      outDir: env.VITE_APP_OUT_PUTDIR || 'dist',
      sourcemap: false,
      // esbuild 打包更快，但是不能去除 console.log
      minify: 'terser',
      chunkSizeWarningLimit: 500,
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      },
      rollupOptions: {
        // output: {
        //   // Static resource classification and packaging
        //   chunkFileNames: 'assets/js/[name]-[hash].js',
        //   entryFileNames: 'assets/js/[name]-[hash].js',
        //   assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
        // }
        output: {
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString()
            }
          },
          chunkFileNames: chunkInfo => {
            const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/') : []
            const fileName = facadeModuleId[facadeModuleId.length - 2] || '[name]'
            return `js/${fileName}/[name].[hash].js`
          }
        }
      }
    }
  }
})
