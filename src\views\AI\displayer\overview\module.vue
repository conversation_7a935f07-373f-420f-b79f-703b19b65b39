<template>
  <div class="main_detail">
    <tableList :tableData="tableData" @detail="toDetail"></tableList>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import tableList from './tableList.vue'

const route = useRoute()
const emit = defineEmits(['setModuleName'])

const modelId = ref(null)
const diagnosticType = ref(null)
// name: '管理需求计划',
//       value: 52,
//       changeType: 1,
//       changeScore: 2.5,
//       A: 42,
//       B: 42,
//       C: 42,
//       D: 42,
//       E: 42,
//       AList: [
//         {
//           name: '端到端闭环',
//           value: 44
const tableData = ref({
  columns: [
    {
      name: '流程赋能',
      code: 'A'
    },
    {
      name: '组织赋能',
      code: 'B'
    },
    {
      name: '人岗赋能',
      code: 'C'
    },
    {
      name: '数字化赋能',
      code: 'D'
    },
    {
      name: 'AI赋能',
      code: 'E'
    }
  ],
  list: [
    {
      name: '管理计划策略',
      A: 56.2,
      B: 54.1,
      C: 56.1,
      D: 33.3,
      E: 33.0,
      value: 45.9,
      changeType: 1,
      changeScore: 2.5,
      AList: [
        {
          name: '流程端到端',
          value: 54.8
        },
        {
          name: '输入输出文档',
          value: 56.0
        },
        {
          name: '业务KPI',
          value: 59.0
        },
        {
          name: '业务规则',
          value: 55.1
        }
      ],
      BList: [
        {
          name: '岗位角色职责',
          value: 55.3
        },
        {
          name: '岗位协同 RACI',
          value: 54.9
        },
        {
          name: '组织岗位 KPI',
          value: 54.0
        },
        {
          name: '组织设置',
          value: 52.0
        }
      ],
      CList: [
        {
          name: '能力培训',
          value: 55.4
        },
        {
          name: '人员动力',
          value: 56.1
        },
        {
          name: '人员能力评估',
          value: 58.4
        },
        {
          name: '人员能力要求',
          value: 54.3
        }
      ],
      DList: [
        {
          name: '数据治理',
          value: 43.1
        },
        {
          name: '系统赋能',
          value: 41.9
        },
        {
          name: '系统改善规划',
          value: 48.0
        },
        {
          name: '自动化与智能化设备',
          value: 0.0
        }
      ],
      EList: [
        {
          name: '数据驱动决策',
          value: 35.2
        },
        {
          name: '算法赋能',
          value: 32.8
        },
        {
          name: '业务模型优化',
          value: 32.0
        },
        {
          name: '智能风险预警',
          value: 32.6
        },
        {
          name: '自动化流程优化',
          value: 32.6
        }
      ]
    },
    {
      name: '管理订单策略',
      A: 56.4,
      B: 54.0,
      C: 56.7,
      D: 32.2,
      E: 32.3,
      value: 45.7,
      changeType: 1,
      changeScore: 2.5,
      AList: [
        {
          name: '流程端到端',
          value: 54.8
        },
        {
          name: '输入输出文档',
          value: 56.0
        },
        {
          name: '业务KPI',
          value: 59.0
        },
        {
          name: '业务规则',
          value: 55.1
        }
      ],
      BList: [
        {
          name: '岗位角色职责',
          value: 55.3
        },
        {
          name: '岗位协同 RACI',
          value: 54.9
        },
        {
          name: '组织岗位 KPI',
          value: 54.0
        },
        {
          name: '组织设置',
          value: 52.0
        }
      ],
      CList: [
        {
          name: '能力培训',
          value: 55.4
        },
        {
          name: '人员动力',
          value: 56.1
        },
        {
          name: '人员能力评估',
          value: 58.4
        },
        {
          name: '人员能力要求',
          value: 54.3
        }
      ],
      DList: [
        {
          name: '数据治理',
          value: 43.1
        },
        {
          name: '系统赋能',
          value: 41.9
        },
        {
          name: '系统改善规划',
          value: 48.0
        },
        {
          name: '自动化与智能化设备',
          value: 0.0
        }
      ],
      EList: [
        {
          name: '数据驱动决策',
          value: 35.2
        },
        {
          name: '算法赋能',
          value: 32.8
        },
        {
          name: '业务模型优化',
          value: 32.0
        },
        {
          name: '智能风险预警',
          value: 32.6
        },
        {
          name: '自动化流程优化',
          value: 32.6
        }
      ]
    },
    {
      name: '管理订单全链路',
      A: 55.1,
      B: 55.6,
      C: 55.5,
      D: 31.9,
      E: 31.8,
      value: 45.3,
      changeType: -1,
      changeScore: 1.5,
      AList: [
        {
          name: '流程端到端',
          value: 54.8
        },
        {
          name: '输入输出文档',
          value: 56.0
        },
        {
          name: '业务KPI',
          value: 59.0
        },
        {
          name: '业务规则',
          value: 55.1
        }
      ],
      BList: [
        {
          name: '岗位角色职责',
          value: 55.3
        },
        {
          name: '岗位协同 RACI',
          value: 54.9
        },
        {
          name: '组织岗位 KPI',
          value: 54.0
        },
        {
          name: '组织设置',
          value: 52.0
        }
      ],
      CList: [
        {
          name: '能力培训',
          value: 55.4
        },
        {
          name: '人员动力',
          value: 56.1
        },
        {
          name: '人员能力评估',
          value: 58.4
        },
        {
          name: '人员能力要求',
          value: 54.3
        }
      ],
      DList: [
        {
          name: '数据治理',
          value: 43.1
        },
        {
          name: '系统赋能',
          value: 41.9
        },
        {
          name: '系统改善规划',
          value: 48.0
        },
        {
          name: '自动化与智能化设备',
          value: 0.0
        }
      ],
      EList: [
        {
          name: '数据驱动决策',
          value: 35.2
        },
        {
          name: '算法赋能',
          value: 32.8
        },
        {
          name: '业务模型优化',
          value: 32.0
        },
        {
          name: '智能风险预警',
          value: 32.6
        },
        {
          name: '自动化流程优化',
          value: 32.6
        }
      ]
    },
    {
      name: '管理订单执行',
      A: 54.3,
      B: 55.0,
      C: 54.5,
      D: 34.0,
      E: 32.3,
      value: 45.4,
      changeType: -1,
      changeScore: 3.5,
      AList: [
        {
          name: '流程端到端',
          value: 54.8
        },
        {
          name: '输入输出文档',
          value: 56.0
        },
        {
          name: '业务KPI',
          value: 59.0
        },
        {
          name: '业务规则',
          value: 55.1
        }
      ],
      BList: [
        {
          name: '岗位角色职责',
          value: 55.3
        },
        {
          name: '岗位协同 RACI',
          value: 54.9
        },
        {
          name: '组织岗位 KPI',
          value: 54.0
        },
        {
          name: '组织设置',
          value: 52.0
        }
      ],
      CList: [
        {
          name: '能力培训',
          value: 55.4
        },
        {
          name: '人员动力',
          value: 56.1
        },
        {
          name: '人员能力评估',
          value: 58.4
        },
        {
          name: '人员能力要求',
          value: 54.3
        }
      ],
      DList: [
        {
          name: '数据治理',
          value: 43.1
        },
        {
          name: '系统赋能',
          value: 41.9
        },
        {
          name: '系统改善规划',
          value: 48.0
        },
        {
          name: '自动化与智能化设备',
          value: 0.0
        }
      ],
      EList: [
        {
          name: '数据驱动决策',
          value: 35.2
        },
        {
          name: '算法赋能',
          value: 32.8
        },
        {
          name: '业务模型优化',
          value: 32.0
        },
        {
          name: '智能风险预警',
          value: 32.6
        },
        {
          name: '自动化流程优化',
          value: 32.6
        }
      ]
    },
    {
      name: '管理工序计划',
      A: 54.3,
      B: 54.1,
      C: 55.4,
      D: 33.1,
      E: 31.7,
      value: 45.0,
      changeType: 1,
      changeScore: 2.5,
      AList: [
        {
          name: '流程端到端',
          value: 54.8
        },
        {
          name: '输入输出文档',
          value: 56.0
        },
        {
          name: '业务KPI',
          value: 59.0
        },
        {
          name: '业务规则',
          value: 55.1
        }
      ],
      BList: [
        {
          name: '岗位角色职责',
          value: 55.3
        },
        {
          name: '岗位协同 RACI',
          value: 54.9
        },
        {
          name: '组织岗位 KPI',
          value: 54.0
        },
        {
          name: '组织设置',
          value: 52.0
        }
      ],
      CList: [
        {
          name: '能力培训',
          value: 55.4
        },
        {
          name: '人员动力',
          value: 56.1
        },
        {
          name: '人员能力评估',
          value: 58.4
        },
        {
          name: '人员能力要求',
          value: 54.3
        }
      ],
      DList: [
        {
          name: '数据治理',
          value: 43.1
        },
        {
          name: '系统赋能',
          value: 41.9
        },
        {
          name: '系统改善规划',
          value: 48.0
        },
        {
          name: '自动化与智能化设备',
          value: 0.0
        }
      ],
      EList: [
        {
          name: '数据驱动决策',
          value: 35.2
        },
        {
          name: '算法赋能',
          value: 32.8
        },
        {
          name: '业务模型优化',
          value: 32.0
        },
        {
          name: '智能风险预警',
          value: 32.6
        },
        {
          name: '自动化流程优化',
          value: 32.6
        }
      ]
    },

    {
      name: '管理库存',
      A: 54.5,
      B: 54.8,
      C: 55.7,
      D: 33.2,
      E: 32.8,
      value: 45.6,
      changeType: null,
      changeScore: 0,
      AList: [
        {
          name: '流程端到端',
          value: 54.8
        },
        {
          name: '输入输出文档',
          value: 56.0
        },
        {
          name: '业务KPI',
          value: 59.0
        },
        {
          name: '业务规则',
          value: 55.1
        }
      ],
      BList: [
        {
          name: '岗位角色职责',
          value: 55.3
        },
        {
          name: '岗位协同 RACI',
          value: 54.9
        },
        {
          name: '组织岗位 KPI',
          value: 54.0
        },
        {
          name: '组织设置',
          value: 52.0
        }
      ],
      CList: [
        {
          name: '能力培训',
          value: 55.4
        },
        {
          name: '人员动力',
          value: 56.1
        },
        {
          name: '人员能力评估',
          value: 58.4
        },
        {
          name: '人员能力要求',
          value: 54.3
        }
      ],
      DList: [
        {
          name: '数据治理',
          value: 43.1
        },
        {
          name: '系统赋能',
          value: 41.9
        },
        {
          name: '系统改善规划',
          value: 48.0
        },
        {
          name: '自动化与智能化设备',
          value: 0.0
        }
      ],
      EList: [
        {
          name: '数据驱动决策',
          value: 35.2
        },
        {
          name: '算法赋能',
          value: 32.8
        },
        {
          name: '业务模型优化',
          value: 32.0
        },
        {
          name: '智能风险预警',
          value: 32.6
        },
        {
          name: '自动化流程优化',
          value: 32.6
        }
      ]
    },
    {
      name: '管理物料需求计划',
      A: 54.6,
      B: 55.6,
      C: 55.4,
      D: 33.8,
      E: 32.8,
      value: 45.8,
      changeType: 1,
      changeScore: 2.5,
      AList: [
        {
          name: '流程端到端',
          value: 54.8
        },
        {
          name: '输入输出文档',
          value: 56.0
        },
        {
          name: '业务KPI',
          value: 59.0
        },
        {
          name: '业务规则',
          value: 55.1
        }
      ],
      BList: [
        {
          name: '岗位角色职责',
          value: 55.3
        },
        {
          name: '岗位协同 RACI',
          value: 54.9
        },
        {
          name: '组织岗位 KPI',
          value: 54.0
        },
        {
          name: '组织设置',
          value: 52.0
        }
      ],
      CList: [
        {
          name: '能力培训',
          value: 55.4
        },
        {
          name: '人员动力',
          value: 56.1
        },
        {
          name: '人员能力评估',
          value: 58.4
        },
        {
          name: '人员能力要求',
          value: 54.3
        }
      ],
      DList: [
        {
          name: '数据治理',
          value: 43.1
        },
        {
          name: '系统赋能',
          value: 41.9
        },
        {
          name: '系统改善规划',
          value: 48.0
        },
        {
          name: '自动化与智能化设备',
          value: 0.0
        }
      ],
      EList: [
        {
          name: '数据驱动决策',
          value: 35.2
        },
        {
          name: '算法赋能',
          value: 32.8
        },
        {
          name: '业务模型优化',
          value: 32.0
        },
        {
          name: '智能风险预警',
          value: 32.6
        },
        {
          name: '自动化流程优化',
          value: 32.6
        }
      ]
    },
    {
      name: '管理需求计划',
      A: 54.0,
      B: 54.9,
      C: 54.7,
      D: 33.6,
      E: 32.3,
      value: 45.3,
      changeType: 1,
      changeScore: 1.5,
      AList: [
        {
          name: '流程端到端',
          value: 54.8
        },
        {
          name: '输入输出文档',
          value: 56.0
        },
        {
          name: '业务KPI',
          value: 59.0
        },
        {
          name: '业务规则',
          value: 55.1
        }
      ],
      BList: [
        {
          name: '岗位角色职责',
          value: 55.3
        },
        {
          name: '岗位协同 RACI',
          value: 54.9
        },
        {
          name: '组织岗位 KPI',
          value: 54.0
        },
        {
          name: '组织设置',
          value: 52.0
        }
      ],
      CList: [
        {
          name: '能力培训',
          value: 55.4
        },
        {
          name: '人员动力',
          value: 56.1
        },
        {
          name: '人员能力评估',
          value: 58.4
        },
        {
          name: '人员能力要求',
          value: 54.3
        }
      ],
      DList: [
        {
          name: '数据治理',
          value: 43.1
        },
        {
          name: '系统赋能',
          value: 41.9
        },
        {
          name: '系统改善规划',
          value: 48.0
        },
        {
          name: '自动化与智能化设备',
          value: 0.0
        }
      ],
      EList: [
        {
          name: '数据驱动决策',
          value: 35.2
        },
        {
          name: '算法赋能',
          value: 32.8
        },
        {
          name: '业务模型优化',
          value: 32.0
        },
        {
          name: '智能风险预警',
          value: 32.6
        },
        {
          name: '自动化流程优化',
          value: 32.6
        }
      ]
    },
    {
      name: '管理主计划',
      A: 54.9,
      B: 55.1,
      C: 54.8,
      D: 33.8,
      E: 33.0,
      value: 45.7,
      changeType: -1,
      changeScore: 2.5,
      AList: [
        {
          name: '流程端到端',
          value: 54.8
        },
        {
          name: '输入输出文档',
          value: 56.0
        },
        {
          name: '业务KPI',
          value: 59.0
        },
        {
          name: '业务规则',
          value: 55.1
        }
      ],
      BList: [
        {
          name: '岗位角色职责',
          value: 55.3
        },
        {
          name: '岗位协同 RACI',
          value: 54.9
        },
        {
          name: '组织岗位 KPI',
          value: 54.0
        },
        {
          name: '组织设置',
          value: 52.0
        }
      ],
      CList: [
        {
          name: '能力培训',
          value: 55.4
        },
        {
          name: '人员动力',
          value: 56.1
        },
        {
          name: '人员能力评估',
          value: 58.4
        },
        {
          name: '人员能力要求',
          value: 54.3
        }
      ],
      DList: [
        {
          name: '数据治理',
          value: 43.1
        },
        {
          name: '系统赋能',
          value: 41.9
        },
        {
          name: '系统改善规划',
          value: 48.0
        },
        {
          name: '自动化与智能化设备',
          value: 0.0
        }
      ],
      EList: [
        {
          name: '数据驱动决策',
          value: 35.2
        },
        {
          name: '算法赋能',
          value: 32.8
        },
        {
          name: '业务模型优化',
          value: 32.0
        },
        {
          name: '智能风险预警',
          value: 32.6
        },
        {
          name: '自动化流程优化',
          value: 32.6
        }
      ]
    }
  ]
})

const router = useRouter()
const toDetail = (item, index) => {
  console.log('item :>> ', item)
  emit('setModuleName', item.name)
  router.push(
    `/AI/displayer/overview/componentGroup?modelId=${modelId.value}&diagnosticType=${diagnosticType.value}&moduleCode=${item.code}`
  )
}

// 初始化
console.log('route.query :>> ', route.query)
let { modelId: mId, diagnosticType: dType } = route.query
modelId.value = mId
diagnosticType.value = dType

// 监听路由变化
watch(
  () => route.query,
  value => {
    console.log('value :>> ', value)
    let { modelId: mId, diagnosticType: dType } = value
    modelId.value = mId
    diagnosticType.value = dType
  }
)
</script>

<style lang="scss" scoped></style>
