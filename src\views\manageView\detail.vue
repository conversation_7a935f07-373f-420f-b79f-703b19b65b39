<script setup>
import simplenessTable from '@/components/table/simplenessTable.vue'
import { manageViewDetail } from '@/assets/data/manageView.js'
defineOptions({ name: 'manageViewDetail' })
const route = useRoute()
const id = route.query.id
console.log('id', id)
const detailData = manageViewDetail[id]
console.log(detailData)
const columns = ref([
  {
    prop: 'role',
    label: '用户角色',
    width: '160'
  },
  {
    prop: 'scene',
    label: '场景',
    width: '350'
  },
  {
    prop: 'feature',
    label: '使用功能',
    width: '160'
  },
  {
    prop: 'procedure',
    label: '操作步骤'
  }
])

const router = useRouter()
const toAppDesc = item => {
  console.log('toAppDesc', item)
  router.push({ path: '/manageView/detail/desc', query: { id: item.id, title: item.title } })
}
</script>
<template>
  <div class="view-detail-wrap">
    <div class="page-title-line">应用定位</div>
    <div class="detail-location">{{ detailData.location }}</div>
    <div class="page-title-line">典型用户与场景</div>
    <simplenessTable class="table" :columns="columns" :data="detailData.scene"></simplenessTable>
    <div class="page-title-line">应用一览</div>
    <div class="detail-app-view">
      <div class="view-item" @click="toAppDesc(item)" v-for="item in detailData.overview" :key="item.id">
        <div class="item-title">{{ item.title }}</div>
        <div class="item-desc">{{ item.desc }}</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.view-detail-wrap {
  .page-title-line {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .detail-location {
    font-size: 16px;
    color: #3d3d3d;
    line-height: 24px;
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 16px;
    margin-bottom: 30px;
  }
  .table {
    margin-bottom: 30px;
  }
  .detail-app-view {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 20px;
    .view-item {
      width: calc(20% - 20px);
      padding: 20px 20px 10px;
      box-sizing: border-box;
      background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), rgba(255, 255, 255, 0.5);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      cursor: pointer;
      &:hover {
        box-shadow: 0px 0px 10px 0px rgba(83, 172, 255, 0.5);
        border-color: #53acff;
        background-image: url('@/assets/imgs/bg-app-overview.webp');
      }
      .item-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 6px;
        color: #333;
      }
      .item-desc {
        font-size: 14px;
        color: #666;
        line-height: 24px;
      }
    }
  }
}
</style>
