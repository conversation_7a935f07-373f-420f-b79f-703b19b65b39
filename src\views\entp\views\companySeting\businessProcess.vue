<template>
  <div class="business_precess_wrap bg_write">
    <div class="page_main_title">业务流程设置</div>
    <div class="business_precess_center clearfix">
      <div class="page_section">
        <div class="oper_btn_wrap">
          <el-button class="page_add_btn" type="primary" @click="addBizProcess">新增</el-button>
        </div>
        <table-component
          :tableData="tableData"
          :needIndex="needIndex"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        >
          <template #oper>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button @click="edit(scope.$index, tableData.data)" type="primary" link class="icon_edit">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button class="color_danger icon_del" @click="deleteRow(scope.$index, tableData.data)" link>
                  <el-icon><Delete /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </template>
        </table-component>
      </div>
    </div>

    <el-dialog :title="operateSign ? '新建业务流程' : '修改业务流程'" v-model="dialogShow" @close="dialogCancel">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="流程名称" prop="bizProcessName">
          <el-input v-model="form.bizProcessName" />
        </el-form-item>
        <el-form-item label="流程编码" v-if="!operateSign">
          <el-input v-model="form.bizProcessCode" disabled />
        </el-form-item>
        <el-form-item label="层级" v-if="!operateSign">
          <el-input v-model="form.layerNo" disabled />
        </el-form-item>
        <el-form-item class="tree_wrap" label="上级流程">
          <el-cascader
            :disabled="!operateSign"
            :options="treeData"
            v-model="form.parentProcessCode"
            placeholder="请选择"
            :change-on-select="true"
            :props="{
              label: 'value',
              value: 'code',
              expandTrigger: 'hover'
            }"
            @change="val => handleItemChange(val, 'parentProcessTree')"
            clearable
          />
        </el-form-item>
        <el-form-item class="tree_wrap" label="前置流程">
          <el-cascader
            :options="treeData"
            v-model="form.prevProcessCode"
            placeholder="请选择"
            :change-on-select="true"
            :props="{
              label: 'value',
              value: 'code',
              expandTrigger: 'hover'
            }"
            @change="val => handleItemChange(val, 'prevProcessTree')"
            clearable
          />
        </el-form-item>
        <el-form-item label="流程描述">
          <el-input type="textarea" v-model="form.bizProcessDesc" :autosize="{ minRows: 4, maxRows: 8 }" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="page_clear_btn" @click="dialogCancel">取 消</el-button>
          <el-button class="page_add_btn" type="primary" @click="dialogSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Edit, Delete } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/modules/user'
import {
  createProcess,
  getProcessInfo,
  getProcessList,
  getProcessTree,
  updateProcess,
  deleteProcess
} from '../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent.vue'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

const formRef = ref(null)
const needIndex = ref(true)
const dialogShow = ref(false)
const operateSign = ref(true)
const treeData = ref([])

const tableData = reactive({
  columns: [
    { label: '流程编码', prop: 'bizProcessCode' },
    { label: '流程名称', prop: 'bizProcessName' },
    { label: '流程描述', prop: 'bizProcessDesc', width: '350' },
    { label: '层级', prop: 'layerNo' },
    { label: '上级流程', prop: 'parentProcessName' },
    { label: '前置流程', prop: 'prevProcessName' }
  ],
  data: [],
  page: { total: 0, current: 1, size: 10 }
})

const form = reactive({
  bizProcessCode: '',
  bizProcessName: '',
  bizProcessDesc: '',
  parentProcessCode: '',
  prevProcessCode: '',
  layerNo: ''
})

const rules = {
  bizProcessName: [{ required: true, message: '请填写流程名称', trigger: 'blur' }],
  parentProcessCode: [{ required: true, message: '请选择上级流程', trigger: 'change' }]
}

const parentProcessCodeCopy = ref('')
const prevProcessCodeCopy = ref('')

const getProcessListFun = async () => {
  try {
    const res = await getProcessList({
      companyId: companyId.value,
      current: tableData.page.current,
      size: tableData.page.size
    })
    if (res.code == 200) {
      if (res.data.length > 0) {
        tableData.data = res.data.map(item => ({
          bizProcessCode: item.bizProcessCode,
          bizProcessName: item.bizProcessName,
          bizProcessDesc: item.bizProcessDesc,
          layerNo: item.layerNo,
          parentProcessName: item.parentProcessName,
          prevProcessName: item.prevProcessName
        }))
      } else {
        tableData.data = []
      }
      tableData.page.total = res.total
    } else {
      tableData.data = []
      tableData.page = { total: 0, current: 1, size: 10 }
    }
  } catch (error) {
    console.error('获取流程列表失败:', error)
    ElMessage.error('获取流程列表失败')
  }
}

const getProcessTreeFun = async () => {
  try {
    const res = await getProcessTree({ companyId: companyId.value })
    if (res.code == 200) {
      treeData.value = res.data || []
    } else {
      treeData.value = []
    }
  } catch (error) {
    console.error('获取流程树失败:', error)
    ElMessage.error('获取流程树失败')
  }
}

const addBizProcess = () => {
  operateSign.value = true
  dialogShow.value = true
  Object.assign(form, {
    bizProcessCode: '',
    bizProcessName: '',
    bizProcessDesc: '',
    parentProcessCode: '',
    prevProcessCode: '',
    layerNo: ''
  })
}

const edit = async (index, rows) => {
  operateSign.value = false
  dialogShow.value = true
  try {
    const res = await getProcessInfo({
      companyId: companyId.value,
      bizProcessCode: rows[index].bizProcessCode
    })
    if (res.code == 200) {
      Object.assign(form, {
        bizProcessCode: res.data.bizProcessCode,
        bizProcessName: res.data.bizProcessName,
        bizProcessDesc: res.data.bizProcessDesc,
        parentProcessCode: res.data.parentProcessCode,
        prevProcessCode: res.data.prevProcessCode,
        layerNo: res.data.layerNo
      })
      parentProcessCodeCopy.value = res.data.parentProcessCode
      prevProcessCodeCopy.value = res.data.prevProcessCode
    }
  } catch (error) {
    console.error('获取流程详情失败:', error)
    ElMessage.error('获取流程详情失败')
  }
}

const deleteRow = async (index, rows) => {
  try {
    await ElMessageBox.confirm('确认删除该流程?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    const res = await deleteProcess({
      companyId: companyId.value,
      bizProcessCode: rows[index].bizProcessCode
    })
    if (res.code == 200) {
      ElMessage.success(res.msg)
      await getProcessListFun()
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除流程失败:', error)
      ElMessage.error('删除流程失败')
    }
  }
}

const handleItemChange = (val, type) => {
  if (type == 'parentProcessTree') {
    form.parentProcessCode = val[val.length - 1] || ''
  } else if (type == 'prevProcessTree') {
    form.prevProcessCode = val[val.length - 1] || ''
  }
}

const handleSizeChange = val => {
  tableData.page.size = val
  getProcessListFun()
}

const handleCurrentChange = val => {
  tableData.page.current = val
  getProcessListFun()
}

const dialogCancel = () => {
  dialogShow.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const dialogSubmit = async () => {
  if (!formRef.value) return
  try {
    await formRef.value.validate()
    const params = {
      companyId: companyId.value,
      bizProcessCode: form.bizProcessCode,
      bizProcessName: form.bizProcessName,
      bizProcessDesc: form.bizProcessDesc,
      parentProcessCode: form.parentProcessCode,
      prevProcessCode: form.prevProcessCode
    }
    const res = operateSign.value ? await createProcess(params) : await updateProcess(params)
    if (res.code == 200) {
      ElMessage.success(res.msg)
      dialogShow.value = false
      await getProcessListFun()
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存流程失败:', error)
      ElMessage.error('保存流程失败')
    }
  }
}

onMounted(async () => {
  await Promise.all([getProcessListFun(), getProcessTreeFun()])
})
</script>

<style scoped>
.business_precess_wrap {
}

.business_precess_center {
  margin-top: 20px;
}

.icon_edit {
  margin-right: 10px;
}

.icon_edit,
.icon_del {
  font-size: 16px;
}

.tree_wrap :deep(.el-cascader) {
  width: 100%;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  width: 100%;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style>
