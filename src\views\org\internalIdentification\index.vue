<script setup>
import SectionTab from "../components/sectionTab.vue";
const router = useRouter();
const route = useRoute();
const sectionTabList = ref([
  {
    name: "人才盘点服务",
    code: 1,
    path: "/org/internalIdentification/talentInventoryS",
  },
  {
    name: "人才测评服务",
    code: 2,
    path: "/org/internalIdentification/talentAssessmentS",
  },
]);
const sectionTabCheckSign = ref(1);
const checkSecTab = (c) => {
  router.push(sectionTabList.value[c - 1].path);
  sectionTabCheckSign.value = c;
};
const initActMenu = (t) => {
  t.forEach((e) => {
    if (route.path.indexOf(e.path) > -1) {
      sectionTabCheckSign.value = e.code;
    }
  });
};
onMounted(() => {
  initActMenu(sectionTabList.value);
});
</script>
<template>
  <div class="index_wrap">
    <div class="v_tab_wrap">
      <SectionTab
        :sectionTabList="sectionTabList"
        :sectionTabCheckSign="sectionTabCheckSign"
        @checkSecTab="checkSecTab"
      ></SectionTab>
    </div>
    <div class="content-mian">
      <router-view></router-view>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.index_wrap {
}
</style>
