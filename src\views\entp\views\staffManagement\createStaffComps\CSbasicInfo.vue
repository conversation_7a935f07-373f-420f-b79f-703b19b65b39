<template>
  <div class="basic_info_wrap">
    <div class="from_wrap clearfix">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="85px" class="demo-ruleForm clearfix">
        <div class="page_second_title">基础信息</div>
        <div class="flex_row_wrap_start marginT_16">
          <el-form-item class="form_item" label="员工编码" prop="employeeCode">
            <el-input v-model.trim="ruleForm.employeeCode"></el-input>
          </el-form-item>
          <el-form-item class="form_item" label="姓名" prop="userName">
            <el-input v-model.trim="ruleForm.userName"></el-input>
          </el-form-item>
          <el-form-item class="form_item" label="性别" prop="gender">
            <el-radio-group v-model="ruleForm.gender">
              <el-radio v-for="item in genderOptions" :key="item.dictCode" :label="item.dictCode">{{
                item.codeName
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="form_item" label="出生日期" prop="birthday">
            <el-date-picker
              v-model="ruleForm.birthday"
              type="date"
              value-format="YYYY-MM-DD"
              :picker-options="DatepickerOptions"
              placeholder="选择日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item class="form_item" label="邮箱" prop="email">
            <el-input v-model.trim="ruleForm.email"></el-input>
          </el-form-item>
          <el-form-item class="form_item" label="入职日期" prop="currentEmpDate">
            <el-date-picker
              v-model="ruleForm.currentEmpDate"
              type="date"
              value-format="YYYY-MM-DD"
              :picker-options="DatepickerOptions"
              placeholder="选择日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item class="form_item" label="手机号码" prop="phoneNumber">
            <el-input v-model.trim="ruleForm.phoneNumber" maxlength="11"></el-input>
          </el-form-item>
          <el-form-item class="form_item" label="籍贯" prop="nativePlace">
            <el-cascader
              :options="cityInfo"
              v-model="ruleForm.nativePlace"
              :change-on-select="true"
              :clearable="true"
              :filterable="true"
              :props="{ expandTrigger: 'hover' }"
              @change="handleChange($event, 'nativePlace')"
            ></el-cascader>
          </el-form-item>
          <el-form-item class="form_item" label="家庭所在地" prop="homePlace">
            <el-cascader
              :options="cityInfo"
              v-model="ruleForm.homePlace"
              :change-on-select="true"
              :clearable="true"
              :filterable="true"
              :props="{ expandTrigger: 'hover' }"
              @change="handleChange($event, 'homePlace')"
            ></el-cascader>
          </el-form-item>
          <el-form-item class="form_item" label="现常住地" prop="residencePlace">
            <el-cascader
              :options="cityInfo"
              v-model="ruleForm.residencePlace"
              :change-on-select="true"
              :clearable="true"
              :filterable="true"
              :props="{ expandTrigger: 'hover' }"
              @change="handleChange($event, 'residencePlace')"
            ></el-cascader>
          </el-form-item>
          <el-form-item class="form_item" label="民族" prop="nationalityCode">
            <el-select v-model="ruleForm.nationalityCode" filterable clearable>
              <el-option
                v-for="item in nationalityOptions"
                :label="item.codeName"
                :value="item.dictCode"
                :key="item.dictCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="form_item" label="婚姻状况" prop="maritalStatus">
            <el-select v-model="ruleForm.maritalStatus">
              <el-option
                v-for="item in maritalOptions"
                :label="item.codeName"
                :value="item.dictCode"
                :key="item.dictCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="form_item" label="证件号" prop="idNumber">
            <el-input v-model.trim="ruleForm.idNumber"></el-input>
          </el-form-item>
        </div>
        <div v-for="(item, index) in ruleForm.postJson" :key="index">
          <!-- 主要岗位 -->
          <div v-if="item.primary == 'Y'">
            <div class="page_second_title">
              主要岗位
              <span class="fs14 pointer" v-if="index > 0" @click="deletePost(index)">
                <i class="el-icon-delete"></i>删除
              </span>
            </div>
            <div class="flex_row_wrap_start marginT_16">
              <el-form-item
                class="form_item"
                label="所属部门"
                :prop="'postJson.' + index + '.orgCode'"
                :rules="morePostRules.notEmpty"
              >
                <el-cascader
                  v-model="item.orgCode"
                  :props="{
                    value: 'code',
                    label: 'value',
                    checkStrictly: 'true'
                  }"
                  clearable
                  :options="orgOptions"
                  :show-all-levels="false"
                  @change="orgChange(index)"
                ></el-cascader>
              </el-form-item>
              <el-form-item
                class="form_item"
                label="岗位名称"
                :prop="'postJson.' + index + '.postCode'"
                :rules="morePostRules.notEmpty"
              >
                <!-- <el-cascader v-model="item.postCode" :props="{value:'code',label:'value',checkStrictly:'true'}" clearable
                                :options="postOptions[index]" :show-all-levels="false" @change="postChange(index)"></el-cascader>-->
                <el-select v-model="item.postCode" placeholder="请选择" clearable @change="postChange">
                  <el-option
                    v-for="item in postOptions[index]"
                    :key="item.postCode"
                    :label="item.postName"
                    :value="item.postCode"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="form_item" label="直接上级" prop="nationalityCode">
                <!-- <el-input v-model="ruleForm.supName"></el-input> -->
                <el-select
                  v-model="ruleForm.supName"
                  filterable
                  remote
                  :remote-method="getCandidateFun"
                  @change="candidateChange"
                  :loading="loading"
                  clearable
                  placeholder="请输入人员名称进行查询"
                >
                  <el-option
                    v-for="opt in staffOptions"
                    :key="opt.dictCode"
                    :label="opt.codeName"
                    :value="opt.dictCode"
                  >
                    <span class="options_item">{{ opt.codeName }}</span
                    >-- <span class="options_item">{{ opt.orgName }}</span
                    >--
                    <span class="options_item">{{ opt.postName }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="align_left">
              <el-button type="primary" class="page_add_btn add_job_btn" @click="addPost">添加兼职岗位</el-button>
            </div>
          </div>
          <div v-else>
            <!-- 兼职岗位 -->
            <div class="page_second_title">
              兼职岗位{{ index }}
              <span class="fs14 pointer" v-if="index > 0" @click="deletePost(index)">
                <i class="el-icon-delete"></i>删除
              </span>
            </div>
            <div class="flex_row_wrap_start marginT_16">
              <el-form-item
                class="form_item"
                label="所属部门"
                :prop="'postJson.' + index + '.orgCode'"
                :rules="morePostRules.notEmpty"
              >
                <el-cascader
                  v-model="item.orgCode"
                  :props="{
                    value: 'code',
                    label: 'value',
                    checkStrictly: 'true',
                    expandTrigger: 'hover'
                  }"
                  clearable
                  :options="orgOptions"
                  :show-all-levels="false"
                  @change="orgChange(index)"
                ></el-cascader>
              </el-form-item>
              <el-form-item
                class="form_item"
                label="岗位名称"
                :prop="'postJson.' + index + '.postCode'"
                :rules="morePostRules.postCodeVerifi"
              >
                <!-- <el-cascader v-model="item.postCode" :props="{value:'code',label:'value',checkStrictly:'true'}" clearable
                                :options="postOptions[index]" :show-all-levels="false" @change="postChange(index)"></el-cascader>-->
                <el-select v-model="item.postCode" placeholder="请选择" clearable>
                  <el-option
                    v-for="item in postOptions[index]"
                    :key="item.postCode"
                    :label="item.postName"
                    :value="item.postCode"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="page_second_title">角色与密码</div>
        <div class="flex_row_wrap_start">
          <el-form-item class="form_item" label="角色" prop="roleIds">
            <el-select v-model="ruleForm.roleIds" multiple>
              <el-option
                v-for="item in roleOptions"
                :key="item.roleId"
                :label="item.roleName"
                :value="item.roleId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="form_item" label="密码" prop="userPasswd">
            <el-input
              placeholder="请输入密码"
              v-model="ruleForm.userPasswd"
              :disabled="!passwordSwitch"
              type="password"
              @focus="selectText($event)"
            >
              <template v-slot:suffix>
                <el-switch
                  class
                  v-if="this.type == 'edit'"
                  v-model="passwordSwitch"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                ></el-switch>
              </template>
            </el-input>
          </el-form-item>
        </div>
      </el-form>
      <div class="align_center">
        <el-button
          class="page_confirm_btn"
          type="primary"
          v-if="type == 'create'"
          @click="continueCreateStaff('ruleForm')"
          >保存并继续添加</el-button
        >
        <el-button class="page_confirm_btn" type="primary" @click="submitForm('ruleForm')">确认</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { regionData } from 'element-china-area-data'
import {
  createStaff,
  getStaffInfo,
  editStaff,
  checkEmployeeCode,
  checkPhoneNumber,
  checkEmail,
  getOrgDeptTree,
  getcompanyRoleList,
  getPostList,
  getSupName,
  principal
} from '../../../request/api'
import { mapMutations } from 'vuex'
export default {
  name: 'CSbasicInfo',
  props: ['disabledTabs', 'creatOrgCode'],
  data() {
    let postCodeVerifiFun = (rule, value, callback) => {
      this.postCodeArr.length == [...new Set(this.postCodeArr)].length
        ? callback()
        : callback(new Error('不能选择相同岗位！'))
    }
    let validatePhone = (rule, value, callback) => {
      if (value == '') {
        callback(new Error('手机号不可为空'))
      } else {
        if (value !== '') {
          let reg = /^1[3456789]\d{9}$/
          if (!reg.test(value)) {
            callback(new Error('请输入有效的手机号码'))
          } else {
            checkPhoneNumber({
              phoneNumber: value,
              userId: this.staffId ? this.staffId : 0
            }).then(res => {
              console.log(res)
              if (res.code == '200') {
                callback()
              } else {
                callback(new Error(res.msg))
              }
            })
          }
        }
      }
    }
    let validateEmail = (rule, value, callback) => {
      if (value == '') {
        // callback(new Error("请正确填写邮箱"));
        callback()
      } else {
        if (value !== '') {
          checkEmail({
            email: value,
            userId: this.staffId ? this.staffId : 0
          }).then(res => {
            console.log(res)
            if (res.code == '200') {
              callback()
            } else {
              callback(new Error(res.msg))
            }
          })

          // var reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
          // if (!reg.test(value)) {
          //     callback(new Error("请输入有效的邮箱"));
          // } else {
          //     checkEmail({
          //         email: value,
          //         userId: this.staffId ? this.staffId : 0,
          //     }).then((res) => {
          //         console.log(res);
          //         if (res.code == "200") {
          //             callback();
          //         } else {
          //             callback(new Error(res.msg));
          //         }
          //     });
          // }
        }
      }
    }
    let validateIdNum = (rule, value, callback) => {
      let regCard = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      let passport = /^[a-zA-Z0-9]{3,21}$/
      let reg = /^[0-9a-zA-Z]+$/
      if (reg.test(value) && value.length < 19) {
        callback()
      } else {
        callback(new Error('证件号码不合法'))
      }
      // if (escape(value).indexOf("%u") < 0) {
      //     // 没有中文
      //     callback();
      // } else {
      //     // 包含中文
      //     callback(new Error('请勿输入中文'));
      // }
    }
    let validateEmployeeCode = (rule, value, callback) => {
      if (value == '') {
        callback(new Error('员工编码不可为空'))
      } else {
        checkEmployeeCode({
          employeeCode: value,
          userId: this.staffId ? this.staffId : 0
        }).then(res => {
          console.log(res)
          if (res.code == '200') {
            callback()
          } else {
            callback(new Error(res.msg))
          }
        })
        //   if (value !== "") {
        //       var reg = /^1[3456789]\d{9}$/;
        //       if (!reg.test(value)) {
        //           callback(new Error("请输入有效的手机号码"));
        //       }else{
        // 	checkPhoneNumber({phoneNumber:value}).then(res => {
        // 		console.log(res);
        // 		if (res.code == "200") {
        // 			callback();
        // 		} else{
        // 			callback(new Error(res.msg));
        // 		}
        // 	})
        // }
        //   }
      }
    }
    return {
      cityInfo: regionData,
      nationalityOptions: [],
      maritalOptions: [],
      genderOptions: [],
      type: 'create',
      staffId: null,
      staffOptions: [],
      postOptions: [],
      orgOptions: [],
      loading: false,
      postCodeArr: [],
      passwordSwitch: true,
      DatepickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
          // 这里就是设置当天后的日期不能被点击;
        }
      },
      roleOptions: [
        {
          label: '管理员',
          value: 1
        },
        {
          label: '普通用户',
          value: 2
        },
        {
          label: '经理',
          value: 3
        }
      ],
      ruleForm: {
        employeeCode: '',
        userName: '',
        gender: '',
        birthday: '',
        email: '',
        phoneNumber: '',
        currentEmpDate: '',
        postJson: [
          {
            orgCode: '',
            postCode: '',
            primary: 'Y'
          }
        ],
        supName: '',
        superiorId: '',
        roleIds: [],
        userPasswd: '',
        nativePlace: '',
        homePlace: '',
        residencePlace: '',
        nationalityCode: '',
        maritalStatus: '',
        idNumber: ''
      },
      rules: {
        employeeCode: [
          {
            required: true,
            trigger: 'blur',
            validator: validateEmployeeCode
          }
        ],
        userName: [
          {
            required: true,
            message: '请输入姓名',
            trigger: 'blur'
          }
        ],
        gender: [
          {
            required: true,
            message: '请选择性别',
            trigger: 'change'
          }
        ],
        birthday: [
          {
            required: false,
            message: '请选择日期',
            trigger: 'change'
          }
        ],
        email: [
          {
            required: false,
            trigger: 'blur',
            validator: validateEmail
          }
        ],
        phoneNumber: [
          {
            required: true,
            trigger: 'blur',
            validator: validatePhone
          }
        ],
        idNumber: [
          {
            required: false,
            trigger: 'blur'
            // validator: validateIdNum,
          }
        ],
        orgCode: [
          {
            required: true,
            message: '请选择部门',
            trigger: 'change'
          }
        ],
        postCode: [
          {
            required: true,
            message: '请选择岗位名称',
            trigger: 'change'
          }
        ],
        currentEmpDate: [
          {
            required: false,
            message: '请选择入职时间',
            trigger: 'change'
          }
        ],
        roleIds: [
          {
            required: true,
            message: '请选择角色',
            trigger: 'change'
          }
        ],
        userPasswd: [
          {
            required: true,
            message: '请填写密码',
            trigger: 'change'
          }
        ]
      },
      morePostRules: {
        notEmpty: [
          {
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ],
        postCodeVerifi: [
          {
            trigger: 'change',
            validator: postCodeVerifiFun
          }
        ]
      }
    }
  },
  computed: {
    userId() {
      return this.$store.state.userInfo.userId
    },
    companyId() {
      return this.$store.state.userInfo.companyId
    }
  },
  methods: {
    submitForm(formName, flag) {
      // flag 标识是新增状态下否继续添加员工
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.type == 'edit') {
            this.ruleForm.userId = this.staffId
          }
          // 复制this.tuleForm
          let params = Object.assign({}, this.ruleForm, {
            postJson: JSON.stringify(this.ruleForm.postJson).replace(/\"/g, "'")
          })
          params.roleIds = params.roleIds.join(',')
          if (this.type == 'edit') {
            // 编辑员工
            this.editStaffInfo(params)
          } else {
            this.createStaff(params, flag)
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    continueCreateStaff(ruleForm) {
      this.submitForm(ruleForm, true)
    },
    handleChange(code, type) {
      this.ruleForm[type] = code[code.length - 1]
    },
    orgChange(index) {
      // 取级联选择器 最后一个值
      let row = this.ruleForm.postJson[index]
      if (row['orgCode'].length == 0) {
        this.ruleForm.postJson[index]['postCode'] = ''
        return
      }
      let orgCode = row['orgCode'][row['orgCode'].length - 1]
      this.ruleForm.postJson[index]['orgCode'] = orgCode
      this.ruleForm.postJson[index]['postCode'] = ''
      this.getPostListFun(index, orgCode)
    },
    getPostListFun(index, orgCode) {
      getPostList({
        orgCode: orgCode,
        type: 'direct'
      }).then(res => {
        this.$set(this.postOptions, index, res.data)
        // this.$forceUpdate();
      })
    },
    postChange(postCode) {
      // 主要岗位变化
      this.primaryPostCode = postCode
      // this.getSupNameFun(postCode);
    },
    getSupNameFun(postCode) {
      getSupName({ postCode: postCode }).then(res => {
        console.log(res)
        if (res.code == '200' && res.data) {
          this.ruleForm.supName = this.$set(this.ruleForm, 'supName', res.data.superiorName)
          this.$set(this.ruleForm, 'superiorId', res.data.superiorId)
        } else {
          this.$set(this.ruleForm, 'supName', '')
          this.$set(this.ruleForm, 'superiorId', '')
        }
      })
    },
    getCandidateFun(val) {
      if (val !== '') {
        this.loading = true
        principal({
          userName: val
        }).then(res => {
          this.loading = false
          if (res.code == 200) {
            if (res.data && res.data.length > 0) {
              this.staffOptions = res.data.map(item => {
                return {
                  codeName: item.userName,
                  dictCode: item.userId,
                  postName: item.postName,
                  orgName: item.orgName
                }
              })
            } else {
              this.staffOptions = []
            }
          }
        })
      } else {
        this.staffOptions = []
      }
    },
    candidateChange(id) {
      this.$set(this.ruleForm, 'superiorId', id)
    },
    addPost() {
      let obj = {
        orgCode: '',
        postCode: '',
        primary: 'N'
      }
      let lastObj = this.ruleForm.postJson[this.ruleForm.postJson.length - 1]
      if (!lastObj.postCode) {
        this.$msg.warning('请完善岗位信息后添加！')
        return
      }
      this.ruleForm.postJson.push(obj)
    },
    deletePost(index) {
      this.ruleForm.postJson.splice(index, 1)
    },
    getStaffInfo() {
      let id = this.$route.query.userId
      this.staffId = id
      let params = {
        userId: id
      }
      getStaffInfo(params).then(res => {
        if (res.code == '200') {
          let data = res.data
          let ruleForm = this.ruleForm
          for (const key in ruleForm) {
            if (ruleForm.hasOwnProperty(key)) {
              ruleForm[key] = data[key] ? data[key] : ''
              if (key == 'postJson') {
                if (data[key].length == 0) {
                  ruleForm[key].push({
                    orgCode: '',
                    postCode: '',
                    primary: 'Y'
                  })
                }
                // ruleForm[key] = data[key].length == 0 ? data.key.push({orgCode: "",postCode: "",primary: "N"}) : data[key];
              }
            }
          }
          this.ruleForm = ruleForm
          let postJson = ruleForm.postJson
          postJson.forEach((item, index) => {
            let orgCode = item.orgCode
            if (orgCode) {
              this.getPostListFun(index, orgCode)
            }
          })
        }
      })
    },
    createStaff(params, flag) {
      // flag | false  标识是否继续添加员工
      let that = this
      createStaff(params).then(res => {
        if (res.code == '200') {
          this.$msg.success(res.msg)
          if (flag) {
            this.$refs['ruleForm'].resetFields()
          } else {
            this.$store.commit('setStaffId', res.data)
            this.$emit('changeDisable', false)
          }

          // setTimeout(()=>{
          // that.$emit("changeTabs",'CSeduInfo');
          // },1500)
        } else {
          this.$msg.error(res.msg)
        }
      })
    },
    editStaffInfo(params) {
      editStaff(params).then(res => {
        if (res.code == '200') {
          this.$msg.success(res.msg)
        } else {
          this.$msg.error(res.msg)
        }
      })
    },
    selectText(e) {
      e.currentTarget.select()
    },
    getOrgTreeFun() {
      getOrgDeptTree({
        companyId: this.companyId
      }).then(res => {
        if (res.code == 200) {
          this.orgOptions = res.data.length > 0 ? this.$util.formatterData(res.data) : []
        } else {
          this.orgOptions = []
        }
      })
    }
  },
  created() {
    this.getOrgTreeFun()
    // 跟据路由信息判断是编辑还是新增
    this.type = this.$route.name == 'editStaff' ? 'edit' : 'create'
    if (this.type == 'edit') {
      console.log('编辑')
      // 编辑页面下默认密码框 disabled
      this.passwordSwitch = false
      this.getStaffInfo()
    } else {
      // 新增
      this.ruleForm.postJson = [
        {
          orgCode: this.creatOrgCode,
          postCode: '',
          primary: 'Y'
        }
      ]
    }
    this.$getDocList(['NATIONALITY_CODE', 'MARITAL_STATUS', 'GENDER']).then(res => {
      this.nationalityOptions = res.NATIONALITY_CODE
      this.maritalOptions = res.MARITAL_STATUS
      this.genderOptions = res.GENDER
    })
    getcompanyRoleList().then(res => {
      console.log(res)
      if (res.code == '200') {
        this.roleOptions = res.data
      }
    })
  },
  mounted() {},
  watch: {
    'ruleForm.postJson': {
      handler: function (value, oVlaue) {
        this.postCodeArr = []
        value.forEach(item => {
          this.postCodeArr.push(item.postCode)
        })
      },
      deep: true
    },
    postOptions: {
      handler: function (value, oValue) {
        this.postOptions = value
      },
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">
.page_second_title {
  margin: 10px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
}
.basic_info_wrap {
  overflow: hidden;
}
.add_job_btn {
  width: 150px;
  margin-bottom: 20px;
}

.demo-ruleForm {
  border-bottom: 1px solid #e9eaeb;
  margin-bottom: 20px;
  align-items: flex-start;

  .form_item {
    width: 50%;
  }

  .el-input__inner {
    width: 280px;
  }
}
</style>
