<template>
  <el-menu-item
    v-if="(!item.children || !item.children.length) && !item.meta?.hidd"
    :index="item.path"
    @click="menuChange(item.path)"
  >
    <el-icon v-if="item.ico">
      <img :src="router.currentRoute.value.path.indexOf(item.path) !== -1 ? item.ico_ac : item.ico" alt="" />
    </el-icon>
    <span>{{ item.meta.name }}</span>
  </el-menu-item>

  <el-sub-menu v-else-if="!item.meta?.hidd" :index="item.path">
    <template #title>
      <el-icon v-if="item.ico">
        <img :src="router.currentRoute.value.path.indexOf(item.path) !== -1 ? item.ico_ac : item.ico" alt="" />
      </el-icon>
      <span>{{ item.meta?.name }}</span>
    </template>

    <template v-for="child in item.children" :key="child.path">
      <MenuItem v-if="!child.meta?.hidd" :item="child" />
    </template>
  </el-sub-menu>
</template>

<script setup>
import { useRouter } from 'vue-router'

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

const router = useRouter()

function menuChange(path) {
  router.push(path)
}
</script>

<style lang="scss" scoped>
:deep(.el-menu-item) {
  &.is-active {
    background: rgba(134, 171, 211, 0.1);
    border-radius: 10px;
    color: #53a9f9;
  }
}

:deep(.el-sub-menu) {
  .el-sub-menu__title {
    &:hover {
      background: rgba(134, 171, 211, 0.1);
      border-radius: 10px;
    }
  }
}

:deep(.el-menu) {
  border-right: none;
}

:deep(.el-icon) {
  img {
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }
}
</style>
