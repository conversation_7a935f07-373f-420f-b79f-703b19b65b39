<template>
    <div class="report_section edu_info_wrap">
        <div class="clearfix">
            <div class="page_second_title">
                <span>工作履历</span>
            </div>
            <div class="edu_info_center marginT_16">
                <div class="edu_info_header">
                    <div class="item long">公司名称</div>
                    <div class="item long">开始日期</div>
                    <div class="item long">结束日期</div>
                    <div class="item">岗位职层</div>
                    <div class="item">岗位类型</div>
                    <div class="item">业务领域</div>
                    <div class="item short">同岗位</div>
                    <div class="item short">同行业</div>
                </div>
                <div class="edu_info_mmain">
                    <div
                        class="edu_info_item"
                        v-for="(item, index) in workData"
                        :key="item.id"
                    >
                        <div class="item long">{{ item.companyName }}</div>
                        <div class="item long">
                            {{ item.beginDate | removeTime }}
                        </div>
                        <div class="item long">
                            {{ item.endDate | removeTime }}
                        </div>
                        <div class="item">
                            {{ item.jobLevelCode }}
                        </div>
                        <div class="item">
                            {{ item.jobClassCode }}
                        </div>
                        <div class="item">
                            {{ item.bizDomainCode }}
                        </div>
                        <div class="item short">
                            {{ item.postRelated }}
                        </div>
                        <div class="item short">{{ item.industryRelated }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
 
<script>
    import {
        getUserExperiences,
        getCmpyBizDomain,
        getCmpyJobClass,
        getCmpyJobLevel,
    } from "../../../../request/api";
    export default {
        name: "userRWorkExperience",
        props: ["nextBtnText", "enqId", "userId"],
        components: {},
        data() {
            return {
                yesOrNo: [],
                bizDomainOption: [],
                jobClassOption: [],
                jobLevelOption: [],
                workData: [],
            };
        },
        created() {
            this.getExperienceData();
        },
        filters: {
            removeTime: function (val) {
                return val ? val.split(" ")[0] : " ";
            },
        },
        methods: {
            getExperienceData() {
                getUserExperiences({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.workData = res.data;
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .edu_info_header,
    .edu_info_mmain {
        .item {
            width: 11%;

            &.short {
                width: 6%;
            }
            &.long {
                width: 15%;
            }
            &.long2 {
                width: 30%;
            }
        }
        .item_icon_wrap {
            text-align: center;
            width: 5%;
        }
    }
</style>