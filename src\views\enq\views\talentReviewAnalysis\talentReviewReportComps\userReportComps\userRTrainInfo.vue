<template>
    <div class="report_section edu_info_wrap performance_info_main">
        <div class="clearfix">
            <div class="page_second_title">
                <span>参与过的培训</span>
            </div>
            <div class="edu_info_center marginT_16">
                <div class="edu_info_header">
                    <div class="item">培训名称</div>
                    <div class="item">培训日期</div>
                    <div class="item">课程类型</div>
                    <div class="item">培训方式</div>
                </div>
                <div class="edu_info_mmain">
                    <div
                        class="edu_info_item"
                        v-for="item in trainData"
                        :key="item.id"
                    >
                        <div class="item">{{ item.trainingName }}</div>
                        <div class="item">
                            {{ item.trainingDate | removeTime }}
                        </div>
                        <div class="item">
                            {{ item.trainingCourse }}
                        </div>
                        <div class="item">
                            {{ item.trainingType }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { getEnqUserTraining } from "../../../../request/api";
    export default {
        name: "userRTrainInfo",
        props: ["nextBtnText", "enqId", "userId"],
        components: {},
        data() {
            return {
                trainData: [],
            };
        },
        created() {
            this.getEnqUserTrainingFun();
        },
        filters: {
            removeTime: function (val) {
                return val ? val.split(" ")[0] : " ";
            },
        },
        methods: {
            getEnqUserTrainingFun() {
                getEnqUserTraining({
                    enqId: this.enqId,
                    userId: this.userId,
                }).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.trainData = res.data;
                    } else {
                        this.$message.error("获取数据失败!");
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    // .performance_info_main {
    //     /*width: 60%;*/
    // }
    .edu_info_wrap {
        margin-bottom: 16px;
    }
    .edu_info_center {
        .item {
            // flex: 1;
            width: 25%;
        }
        .item_icon_wrap {
            text-align: center;
            width: 10%;
        }
    }
</style>
