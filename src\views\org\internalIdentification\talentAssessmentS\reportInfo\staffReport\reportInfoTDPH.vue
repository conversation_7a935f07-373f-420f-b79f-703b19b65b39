<script setup>
import Table from "@/components/table/simplenessTable.vue";
// 11
const columns = ref([
  {
    label: "二级能力",
    prop: "a",
  },
  {
    label: "同小组合作",
    prop: "b",
  },
  {
    label: "单部门合作",
    prop: "c",
  },
  {
    label: "跨部门协同",
    prop: "d",
  },

  {
    label: "战略级协同",
    prop: "e",
  },
  {
    label: "生太级协同",
    prop: "f",
  },
]);
const data = ref([
  {
    a: "市场分析与战略规划",
    b: "15人",
    c: "15人",
    d: "15人",
    e: "15人",
  },
  {
    a: "战略解码与目标分解",
    b: "15人",
    c: "15人",
    d: "15人",
    e: "15人",
  },
]);

const columns2 = ref([
  {
    label: "团队协作偏好类型",
    prop: "e",
  },
  {
    label: "三级能力",
    prop: "a",
  },
  // {
  //   label: "人数",
  //   prop: "b",
  // },
  {
    label: "能力得分",
    prop: "c",
  },
  {
    label: "团队协作偏好对能力的影响",
    prop: "d",
    width: 460,
  },
]);
const data2 = ref([
  {
    a: "经验依赖型",
    b: "12",
    c: "36",
    d: "以过往成功或失败案例为决策核心依据，通过类比历史情境解决当下问题",
    e: "同小组合作",
    f: "面对新业务或复杂场景时，易因路径依赖导致资源错配与目标偏差，阻碍战略转型与创新突破。",
    g: "0",
    h: "2024-09-26 09:59:59",
    k: "100",
  },
]);

const columns3 = ref([
  {
    label: "改进方向",
    prop: "a",
    width: 150,
  },
  {
    label: "核心措施",
    prop: "b",
  },
  {
    label: "时间安排",
    prop: "c",
    width: 150,
  },
]);
const data3 = ref([
  {
    a: "主动明确分工",
    b: "1. 主动梳理数据收集全流程环节（需求确认、渠道开拓、信息采集、清洗整合），结合自身优势（如擅长线上调研 / 线下访谈）向组长提出分工建议，明确个人负责的具体数据类型（如专注东南亚客户消费频次数据采集）；2. 制作个人任务清单同步至小组，标注数据交付标准（如样本量≥200、字段完整率≥95%）及时间节点，主动接受成员监督。",
    c: "1 周内",
    e: "同小组合作",
  },
]);

onMounted(() => {});
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">团队协作偏好</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        通过人才测评数据构建团队协作偏好模型，精准诊断团队在协作模式、沟通效率、角色分工等方面的优势与不足。基于分析结果，针对性优化协作机制（如跨部门流程、信息共享渠道、冲突解决规则），提升团队协同效能，有效减少因分工模糊、沟通壁垒或权责不清导致的执行低效与资源浪费，助力团队高效协同推进业务目标落地。
      </div>
    </div>
    <div class="info_section_wrap three_seven_wrap justify-between">
      <div class="l_wrap">
        <div class="page-title-line">团队协作偏好分布</div>
        <div class="chart_box"></div>
      </div>
      <div class="r_wrap">
        <div class="page-title-line">不同能力下的团队协作偏好分布</div>
        <Table
          :roundBorder="false"
          :columns="columns"
          :data="data"
          headerColor
          showIndex
        >
        </Table>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">
        各项团队协作偏好分析（市场分析与战略规划）
      </div>
      <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor>
      </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">
        各项团队协作偏好的改善建议（同小组合作-市场数据收集与整合）
      </div>
      <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor>
      </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../../style/common.scss";
@import "./common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
