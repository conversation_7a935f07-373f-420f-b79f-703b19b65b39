<script setup>
import Table from '@/components/table/simplenessTable.vue'
// 11
const columns7 = ref([
  {
    label: '二级能力',
    prop: 'a'
  },
  {
    label: '高投入高回报',
    prop: 'b'
  },
  {
    label: '低投入高风险',
    prop: 'c'
  },
  {
    label: '性价比平衡',
    prop: 'd'
  },

  {
    label: '低投入低回报',
    prop: 'e'
  },
  {
    label: '高投入低回报',
    prop: 'f'
  }
])
const data7 = ref([
  {
    a: '市场分析与战略规划',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  },
  {
    a: '战略解码与目标分解',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  }
])

const columns8 = ref([
  {
    label: '成本效益类型',
    prop: 'e'
  },
  {
    label: '三级能力',
    prop: 'a'
  },
  {
    label: '人数',
    prop: 'b'
  },
  {
    label: '能力得分',
    prop: 'c'
  },
  {
    label: '潜在风险描述',
    prop: 'd',
    width: 460
  }
])
const data8 = ref([
  {
    a: '经验依赖型',
    b: '12',
    c: '36',
    d: '以过往成功或失败案例为决策核心依据，通过类比历史情境解决当下问题',
    e: '高投入高回报',
    f: '面对新业务或复杂场景时，易因路径依赖导致资源错配与目标偏差，阻碍战略转型与创新突破。',
    g: '0',
    h: '2024-09-26 09:59:59',
    k: '100'
  }
])

const columns9 = ref([
  {
    label: '成本效益类型',
    prop: 'e',
    width: 150
  },
  {
    label: '三级能力',
    prop: 'a',
    width: 150
  },
  {
    label: '人员',
    prop: 'b'
  },
  {
    label: '改善建议',
    prop: 'c'
  }
])
const data9 = ref([
  {
    a: '经验依赖型 （12人）',
    b: '王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）',
    c: '1.划清经验边界：填写《经验适用边界清单》，标注差异点与潜在风险，避免盲目复制。 2.反经验训练：每月推演 “非惯性做法”，输出《反经验行动清单》，强制尝试新策略。3. 新场景探索：分配 10% 时间接触跨界案例，撰写《旧经验失效分析》，打破路径依赖。',
    e: '高投入高回报'
  }
])

const columns10 = ref([
  {
    label: '二级能力',
    prop: 'a',
    width: 260
  },
  {
    label: '保守型',
    prop: 'b'
  },
  {
    label: '冒险型',
    prop: 'c'
  },
  {
    label: '折中型',
    prop: 'd'
  },

  {
    label: '回避型',
    prop: 'e'
  }
])
const data10 = ref([
  {
    a: '市场分析与战略规划',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  },
  {
    a: '战略解码与目标分解',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  }
])

const columns11 = ref([
  {
    label: '决策模式',
    prop: 'a',
    width: 260
  },
  {
    label: '人数',
    prop: 'b'
  },
  {
    label: '能力得分',
    prop: 'c'
  },
  {
    label: '典型行为特征',
    prop: 'd'
  },

  {
    label: '优势能力领域',
    prop: 'e'
  },
  {
    label: '致命短板',
    prop: 'f'
  }
])
const data11 = ref([
  {
    a: '保守型',
    b: '15',
    c: '36',
    d: '反复验证风险，偏好成熟方案 ',
    e: '风险评估、流程合规',
    f: ' 决策缓慢，错失先机'
  },
  {
    a: '冒险型',
    b: '15',
    c: '15',
    d: '聚焦高收益，忽视潜在风险 ',
    e: '机会捕捉、创新突破',
    f: '资源浪费、风险失控'
  },
  {
    a: '折中型',
    b: '15',
    c: '15',
    d: '权衡利弊，追求 “安全与收益平衡” ',
    e: '资源匹配、策略微调',
    f: '缺乏主见，易陷入折中主义'
  },
  {
    a: '回避型',
    b: '15',
    c: '15',
    d: '拖延决策，依赖他人或集体决策',
    e: '无明显优势',
    f: '责任推诿，团队效率拖累'
  }
])

const columns12 = ref([
  {
    label: '决策模式',
    prop: 'a',
    width: 150
  },
  {
    label: '人员',
    prop: 'b'
  },
  {
    label: '改善建议',
    prop: 'c'
  }
])
const data12 = ref([
  {
    a: '保守型 （12人）',
    b: '王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）',
    c: '1.划清经验边界：填写《经验适用边界清单》，标注差异点与潜在风险，避免盲目复制。 2.反经验训练：每月推演 “非惯性做法”，输出《反经验行动清单》，强制尝试新策略。 3.新场景探索：分配 10% 时间接触跨界案例，撰写《旧经验失效分析》，打破路径依赖。'
  }
])

onMounted(() => {})
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">成本收益偏好</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        通过人才测评数据构建成本收益偏好模型，精准诊断团队在资源投入、风险收益匹配、执行效能等方面的优势与不足。基于分析结果，针对性优化资源分配机制，提升投入产出效率，有效减少因资源错配、风险忽视或低效执行导致的成本浪费与收益流失，助力企业以科学成本策略推进业务目标高效落地。
      </div>
    </div>
    <div class="info_section_wrap three_seven_wrap justify-between">
      <div class="l_wrap">
        <div class="page-title-line">成本效益人数分布</div>
        <div class="chart_box"></div>
      </div>
      <div class="r_wrap">
        <div class="page-title-line">不同能力下的团队协作人数分布</div>
        <Table :roundBorder="false" :columns="columns7" :data="data7" headerColor showIndex> </Table>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">各项协同场景分析（市场分析与战略规划）</div>
      <Table :roundBorder="false" :columns="columns8" :data="data8" headerColor> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">改善建议</div>
      <Table :roundBorder="false" :columns="columns9" :data="data9" headerColor> </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../../../../style/common.scss';
@import './common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
