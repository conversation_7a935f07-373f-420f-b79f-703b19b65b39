<script setup>
const gnjj = ref([
  {
    title: '行业典型指标',
    info: '提供行业核心指标库，可查看指标定义、计算方法及趋势，AI 分析影响因素并给出管理建议，助您快速掌握指标特性与优化方向；'
  },
  {
    title: '典型岗位承接',
    info: '展示各岗位需承接的指标，包括指标概述、承接逻辑及考核建议，支持 AI 解读岗位指标责任，清晰定位关键岗位的指标关联与考核重点。'
  },
  {
    title: '指标对应能力',
    info: '挖掘指标背后的核心能力，提供能力提升路径，AI 解读为改善指标需强化的能力项，助力明确能力建设方向与行动措施。'
  }
])
const appList = [
  {
    path: '/org/entry',
    title: '快速入门',
    desc: '了解诊断场景，查看诊断模型，了解诊断步骤以及诊断报告'
  },
  {
    path: '/org/externalbenchmarking',
    title: '外部对标：组织效能对标',
    desc: '开展组织效能全方位对标'
  },
  {
    path: '/org/internalIdentification',
    title: '内部识别：人才盘点与测评',
    desc: '发起人才盘点与人才测评服务'
  },
  {
    path: '/org/result',
    title: '看结果：指标视角看效能',
    desc: '从指标达成视角看效能'
  },
  {
    path: '/org/ability',
    title: '看能力：能力视角看效能',
    desc: '从人员能力视角看效能'
  },
  {
    path: '/org/execute',
    title: '看执行：任务视角看效能',
    desc: '从关键任务执行情况看效能'
  },
  {
    path: '/org/support/index',
    title: '看支撑：组织与人员支撑',
    desc: '从指标达成视角看效能'
  }
]

const router = useRouter()
const jump = item => {
  router.push(item.path)
}
</script>
<template>
  <div class="org_home_wrap page-container">
    <div class="page-title-line">应用定位</div>
    <div class="location border p-[16px] text-[16px] mb-[8px]">
      集洞察、诊断与提升于一体的智能服务模块，提供组织效能入门认知、评估模型查阅及报告解读；支持效能对标分析，结合人才盘点与能力评估诊断人才效能；从指标、能力、任务视角构建三维评估体系，依托
      AI 生成分析结论与提升举措；可视化呈现组织与人才画像；通过 AI
      实现瓶颈识别、趋势预测等，生成提升行动项并追踪效果，形成全流程管理闭环，为效能升级提供数据驱动指引。
    </div>
    <div class="page-title-line">功能导航</div>
    <div class="location border p-[20px] text-[16px] mb-[8px]">
      <!-- <div class="flow_wrap"></div> -->
      <div class="img img_05"></div>
      <div class="h-[1px] bg-[#d8d8d8] mb-[5px]"></div>
      <div class="page-title-icon">
        <SvgIcon name="indicator-scene-icon"></SvgIcon>
        <div class="text">了解组织效能</div>
      </div>
      <div class="gnjj_content_wrap">
        组织效能是组织以最优资源配置实现战略目标的综合能力体现，其本质在于平衡短期业绩达成（如KPI完成率、关键任务交付）与长期能力建设（涵盖流程效率、组织协同、人才梯队、数字化水平及人员动力与敬业度）的动态耦合。高效能组织呈现"三柱均衡"：目标精准拆解确保战略落地，精益流程与智能工具（如AI预测）支撑敏捷执行，人才活力引擎（动力×敬业度）提供持续动能。以某12人销售团队为例：72.4分的效能虽显示85%关键任务攻坚能力，但71%的KPI达成率暴露可持续危机，根源在于断裂的流程能力（55分）与衰竭的人员活力（动力56分+敬业度57分）——这警示我们，忽略流程优化与人性激励的组织，终将透支未来竞争力。卓越效能最终指向以最小资源耗损创造最大客户价值与员工成就感，成为企业穿越周期的核心壁垒。
      </div>
    </div>
    <div class="page-title-line">应用入口</div>
    <div class="app-wrap">
      <div class="app-list border" @click="jump(item)" v-for="item in appList">
        <div class="app-title">{{ item.title }}</div>
        <div class="app-desc">{{ item.desc }}</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.org_home_wrap {
  .justify-start {
    display: flex;
    justify-content: flex-start;
  }
  .location {
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    .img {
      width: 100%;
      height: 140px;
      margin: 0 auto 25px;
      background: url('@/assets/imgs/org/img_04.png') no-repeat center;
      background-size: 100% 100%;
    }
    .gnjj_content_wrap {
      .item_wrap {
        margin-bottom: 12px;
        position: relative;
        .icon {
          position: absolute;
          top: 8px;
          width: 11px;
          height: 11px;
          background: url('@/assets/imgs/indicator/icon_06.png') no-repeat center center;
          background-size: 100% 100%;
        }
        .title {
          margin: 0 10px 0 21px;
          color: #40a0ff;
          white-space: nowrap;
        }
      }
    }
  }
  .border {
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
  }
  .bg-gradient {
    background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), #ffffff;
  }
  .app-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    .app-list {
      width: calc(20% - 16px);
      padding: 20px;
      cursor: pointer;
      background: url('../../assets/imgs/indicator/img_04.png') no-repeat center center;
      background-size: 100% 100%;
      border-color: transparent;
      &:hover {
        background: url('../../assets/imgs/indicator/img_05.png') no-repeat center center;
        background-size: 100% 100%;
        border-color: #53a9f9;
        .app-title {
          color: #53a9f9;
        }
      }
      .app-title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        line-height: 20px;
        margin-bottom: 6px;
        .app-desc {
          font-size: 14px;
          color: #666666;
          line-height: 22px;
        }
      }
    }
  }
}
</style>
