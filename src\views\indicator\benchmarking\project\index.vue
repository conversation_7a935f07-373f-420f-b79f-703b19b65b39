<template>
  <div class="page-container">
    <div class="title-card">
      <div class="page-title-line">对标项目信息</div>
      <el-button type="primary" :icon="Plus" @click="goAdd">新增对标项目</el-button>
    </div>
    <div class="info-list">
      <div class="item" v-for="item in 5" :key="item">
        <div class="name-card">
          <div class="name">人效对标（2025-03-06）</div>
          <div class="tag-list">
            <span class="tag blue">经营效益与质量</span>
            <span class="tag blue">盈利能力与质量</span>
            <span class="tag blue">人力资源效率</span>
            <span class="tag">人力成本控制</span>
            <span class="tag">组织效率与人员结构</span>
            <span class="tag">可持续发展能力</span>
          </div>
        </div>
        <div class="time">对标日期：2025-03-06</div>
        <div class="operate-card">
          <el-button type="primary" class="btn">维护指标信息</el-button>
          <el-button type="primary" class="btn">查看对标报告</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { Plus } from '@element-plus/icons-vue'
defineOptions({ name: 'benchmarkingProject' })
const router = useRouter()
const goAdd = () => {
  router.push({ path: '/indicator/benchmarking/add' })
}
</script>
<style lang="scss" scoped>
.page-container {
  .title-card {
    @include flex-center(row, space-between, center);
    margin-bottom: 10px;
    .page-title-line {
      margin-bottom: 0;
    }
  }
  .info-list {
    .item {
      @include flex-center(row, space-between, stretch);
      height: 110px;
      padding: 23px 20px;
      margin-bottom: 20px;
      background: #ffffff;
      border-radius: 5px;
      border: 1px solid #c6dbf3;
      .name-card {
        padding-right: 232px;
        border-right: 1px solid #d8d8d8;
        .name {
          padding-bottom: 16px;
          font-size: 18px;
          color: #3d3d3d;
          font-weight: 500;
        }
        .tag-list {
          .tag {
            display: inline-block;
            padding: 6px 10px;
            line-height: 12px;
            margin-right: 20px;
            background: #e3f1ff;
            border-radius: 5px 5px 5px 5px;
            font-size: 14px;
            color: #40a0ff;
            &:last-child {
              margin-right: 0;
            }
            &.blue {
              color: #fff;
              background-color: #40a0ff;
            }
          }
        }
      }
      .time {
        @include flex-center(row, center, center);
        padding-right: 46px;
        border-right: 1px solid #d8d8d8;
        font-size: 14px;
        color: #3d3d3d;
      }
      .operate-card {
        @include flex-center(row, center, center);
        .btn {
          width: 120px;
          height: 36px;
        }
      }
    }
  }
}
</style>
