<template>
    <div class="model_mateInfo_target_wrap marginT_30">
        <div class="flex_row_end padd_LR_16" v-if="type != 'show'">
            <el-button
                class="page_add_btn"
                type="primary"
                plain
                @click="exportTemplate"
                >导出模板</el-button
            >
            <div class="upload_wrap">
                <el-button
                    class="page_add_btn"
                    type="primary"
                    @click="importTemplate"
                    >导入数据</el-button
                >
                <input
                    ref="file"
                    type="file"
                    class="form-control import_btn"
                    @change="fileChange"
                />
            </div>
        </div>
        <div
            class="loading_style"
            v-loading.fullscreen.lock="loading"
            element-loading-text="上传中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.5)"
        ></div>
        <div class="model_mateInfo_target_main page_section">
            <div class="table_header_warp">
                <div class="table_header_item ability">能力目标值</div>
                <div class="table_header_item" :style="{ width: tableColumns.length * 100 + 'px' }">岗位</div>
            </div>
            <div
                class="vue-fast-table"
                ref="table"
                v-bind:class="scrollDirection"
            >
                <div class="table-head">
                    <div
                        class="module"
                        v-bind:style="{
                            transform: 'translateX(' + scrollLeft + 'px)',
                        }"
                        v-for="(item, index) in tableColumns"
                        v-bind:key="index"
                    >
                        <table
                            cellspacing="0"
                            cellpadding="0"
                            class="table_dom"
                            style="
                                border-collapse: collapse;
                                border-color: #ebeef5;
                            "
                        >
                            <thead>
                                <tr>
                                    <td>{{ item.post_name }}</td>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
                <div class="table-body" @scroll="tableScroll">
                    <div class="content">
                        <div
                            class="row"
                            v-for="(p_item, p_index) in tableData"
                            v-bind:key="p_index"
                        >
                            <table
                                cellspacing="0"
                                cellpadding="0"
                                border="1"
                                class="table_dom"
                                style="
                                    border-collapse: collapse;
                                    border-color: #EBEEF5;
                                "
                                :style="{ width: tableColumns.length * 100 + 'px' }"
                            >
                                <tbody>
                                    <tr class="table_tr">
                                        <td
                                            v-if="p_item[post.post_code]"
                                            v-for="(
                                                post, d_index
                                            ) in tableColumns"
                                            v-bind:key="d_index"
                                            @click="
                                                showIpt(
                                                    p_index,
                                                    post.post_code,
                                                    $event
                                                )
                                            "
                                        >
                                            <div
                                                class="td_span"
                                                v-if="
                                                    !p_item[post.post_code][
                                                        'showIpt'
                                                    ]
                                                "
                                            >
                                                {{
                                                    p_item[post.post_code][
                                                        "expected_score"
                                                    ]
                                                }}
                                            </div>
                                            <input
                                                class="table_ipt"
                                                type="number"
                                                @blur="
                                                    hiddenIpt(
                                                        p_index,
                                                        post.post_code,
                                                        $event
                                                    )
                                                "
                                                v-if="
                                                    p_item[post.post_code][
                                                        'showIpt'
                                                    ]
                                                "
                                                v-model="
                                                    p_item[post.post_code][
                                                        'expected_score'
                                                    ]
                                                "
                                                v-focus="
                                                    p_item[post.post_code][
                                                        'focus'
                                                    ]
                                                "
                                            />
                                        </td>
                                        <td v-else>
                                            <div class="td_span"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="table-fix-cloumns">
                    <div class="module fix-left-top">
                        <table
                            class="fix_col_wrap table_dom"
                            cellspacing="0"
                            cellpadding="0"
                            border="1"
                            style="border-collapse: collapse"
                        >
                            <div class="fix_col_item index">序号</div>
                            <div class="fix_col_item">能力分类</div>
                            <div class="fix_col_item">能力词典</div>
                        </table>
                    </div>
                    <div
                        class="module"
                        v-bind:style="{
                            transform: 'translateY(' + scrollTop + 'px)',
                        }"
                    >
                        <table
                            class="fix_col_table table_dom"
                            cellspacing="0"
                            cellpadding="0"
                            border="1"
                            style="border-collapse: collapse"
                        >
                            <thead>
                                <tr
                                    v-for="(item, index) in tableData"
                                    v-bind:key="index"
                                >
                                    <td class="">
                                        <div class="fix_col_td index">
                                            {{ index + 1 }}
                                        </div>
                                    </td>
                                    <td class="">
                                        <div
                                            class="fix_col_td"
                                            :title="item.parentModuleName"
                                        >
                                            {{ item.parentModuleName }}
                                        </div>
                                    </td>
                                    <td class="">
                                        <div
                                            class="fix_col_td"
                                            :title="item.module_name"
                                        >
                                            {{ item.module_name }}
                                        </div>
                                    </td>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="align_center marginT_30" v-if="type != 'show'">
            <el-button class="page_confirm_btn" type="primary" @click="prev()"
                >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="next()"
                >下一步</el-button
            >
        </div>
    </div>
</template>

<script>
    import {
        getConfirmModuleTarget,
        setConfirmModuleTarget,
        downloadExcelFile,
        importData,
        confirmModuleTargetNextStep,
    } from "../../../request/api";

    export default {
        name: "modelMateInfoTarget",
        components: {},
        props: ["modelId", "buildId", "type"],
        data() {
            return {
                scrollLeft: 0,
                scrollTop: 0,
                scrollDirection: "",
                projectArray: [],
                curModule: 0,
                curRow: 0,
                tdScore: null,
                originProjectData: [],
                projectData: [],
                // tableColumns: [],
                loading: false,
                tableColumnsStick: [
                    {
                        label: "序号",
                        prop: "index",
                        isFixed: false,
                        width: 50,
                    },
                    {
                        label: "能力分类",
                        prop: "parentModuleName",
                        isFixed: false,
                        width: 80,
                    },
                    {
                        label: "能力词典",
                        prop: "moduleName",
                        isFixed: false,
                        width: 80,
                    },
                ],
                tableColumns: [],
                tableData: [],
            };
        },
        directives: {
            focus: {
                // 指令的定义
                inserted: function (el) {
                    el.focus();
                },
            },
        },
        created() {
            this.getConfirmModuleTargetFun();
        },
        methods: {
            prev() {
                this.$emit("prevStep");
            },
            next() {
                // console.log(this.tableData)
                // this.setConfirmModuleTargetFun();
                this.confirmModuleTargetNextStepFun();
            },
            cellClickFun(row, column, cell) {
                // console.log(row);
                // console.log(column);
            },
            tableScroll(e) {
                const scrollLeft = e.target.scrollLeft;
                const scrollTop = e.target.scrollTop;
                if (scrollLeft == this.scrollLeft) {
                    this.scrollDirection = "vertical";
                } else {
                    this.scrollDirection = "horizontal";
                }
                this.curModule = parseInt(
                    scrollLeft / (this.tableColumns.length * 100)
                );
                this.curRow = parseInt(scrollTop / 46);
                this.scrollLeft = -scrollLeft;
                this.scrollTop = -scrollTop;
            },
            showIpt(p_index, postCode, e) {
                // let obj = this.$util.deepClone(this.tableData[p_index][postCode]);
                // this.tdScore = obj['expected_score'];
                if (this.type == "show") {
                    return;
                }
                this.tableData[p_index][postCode]["showIpt"] = true;
                this.tableData[p_index][postCode]["focus"] = true;
            },
            hiddenIpt(p_index, postCode, e) {
                // let score = this.tableData[p_index][postCode]['expected_score'];
                this.tableData[p_index][postCode]["showIpt"] = false;
                this.tableData[p_index][postCode]["focus"] = false;
                // if(this.tdScore == score){
                // return
                // }
                let tdData = this.tableData[p_index][postCode];
                let obj = {
                    evalId: tdData.eval_id,
                    expectedScore: tdData.expected_score,
                    moduleCode: tdData.module_code,
                    postCode: postCode,
                };
                let params = [obj];
                setConfirmModuleTarget(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            getConfirmModuleTargetFun() {
                getConfirmModuleTarget({
                    buildId: this.buildId,
                    modelId: this.modelId,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.tableColumns = res.data.postMap;
                        res.data.modelList.map((item, index) => {
                            for (const key in item) {
                                if (item.hasOwnProperty(key)) {
                                    const element = item[key];
                                    if (typeof element == "object") {
                                        element["showIpt"] = false;
                                        element["focus"] = false;
                                    }
                                }
                            }
                        });
                        this.tableData = res.data.modelList;
                        this.$nextTick(() => {
                            // this.$refs.table.doLayout();
                        });
                    }
                });
            },
            setConfirmModuleTargetFun() {
                let evalPostModuleRequestList = [];
                for (let i = 0; i < this.tableData.length; i++) {
                    let row = this.tableData[i];
                    for (let j = 0; j < row.list.length; j++) {
                        evalPostModuleRequestList.push({
                            evalId: row.list[j].evalId,
                            expectedScore: row.list[j].expectedScore,
                            moduleCode: row.list[j].moduleCode,
                            postCode: row.list[j].postCode,
                        });
                    }
                }
                setConfirmModuleTarget(evalPostModuleRequestList).then((res) => {
                    if (res.code == 200) {
                        this.confirmModuleTargetNextStepFun();
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            //导出模板
            exportTemplate() {
                downloadExcelFile({
                    buildId: this.buildId,
                    modelId: this.modelId,
                }).then((res) => {
                    // console.log(res)
                    const blob = new Blob([res]);
                    const elink = document.createElement("a");
                    elink.download = "能力目标.xlsx";
                    elink.style.display = "none";
                    elink.href = URL.createObjectURL(blob);
                    document.body.appendChild(elink);
                    elink.click();
                    URL.revokeObjectURL(elink.href); // 释放URL 对象
                    document.body.removeChild(elink);
                });
            },
            //导入数据
            importTemplate() {},
            fileChange(e) {
                // console.log(e)
                let formData = new FormData();
                let file = e.target.files[0];
                formData.append("multfile", file);
                formData.append("evalId", this.buildId);
                this.$refs.file.value = "";
                this.loading = true;
                importData(formData).then((res) => {
                    this.loading = false;
                    // console.log
                    if (res.code == 200) {
                        this.$msg.success("上传成功");
                        this.getConfirmModuleTargetFun();
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            confirmModuleTargetNextStepFun() {
                confirmModuleTargetNextStep({
                    evalId: this.buildId,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$emit("nextStep");
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
        },
    };
</script>

<style scoped lang="scss">
    .model_mateInfo_target_wrap {
        .model_mateInfo_target_main {
            .table_header_warp {
                // border: 1px solid #ebeef5;
                // border-bottom: none;
                .table_header_item {
                    line-height: 45px;
                    text-align: center;
                    overflow: hidden;
                    border-bottom: 1px solid #EBEEF5;
                    max-width: calc(100% - 250px);
                    background: #f4f4f4;
                    &.ability {
                        float: left;
                        width: 250px;
                        border-right: 1px solid #EBEEF5;
                        border-bottom: none;
                    }
                }
            }
        }
    }
    .upload_wrap {
        position: relative;
        width: 80px;
        height: 30px;
        margin-left: 10px;
        cursor: pointer;
    }
    .import_btn {
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }
    .el-input__inner {
        padding: 0;
        text-align: center;
    }
    .vue-fast-table {
        overflow: hidden;
        position: relative;
        td {
            background: #fff;
            height: 46px;
            width: 100px;
            .td_span {
                width: 99px;
                height: 46px;
                line-height: 46px;
                text-align: center;
                // border: 1px solid #ddd;
                box-sizing: border-box;
            }
            .table_ipt {
                width: 90%;
                line-height: 30px;
                height: 30px;
                margin-left: 5%;
            }
        }
        &.vertical {
            .table-head {
                z-index: 1;
            }
        }
        input {
            width: 50px;
            height: 16px;
            box-sizing: border-box;
        }
    }
    .table-head {
        position: relative;
        display: -webkit-box;
        margin-left: 249px;
        // border-right: 1px solid #ccc;
        .module {
            height: 46px;
            td {
                width: 99px;
                text-align: center;
                border-right: 1px solid #EBEEF5;
                border-bottom: 1px solid #EBEEF5;
                // border:none;
                // border-right: none;
                box-sizing: border-box;
            }
        }
    }
    .table-body {
        display: -webkit-box;
        overflow: auto;
        height: 160px;
        height: 510px;
        margin-left: 249px;
        .module {
            td {
                width: 100px;
                text-align: center;
                // border: 1px solid #ccc;
                box-sizing: border-box;
            }
        }
    }
    .table-fix-cloumns {
        position: absolute;
        top: 0;
        left: 0;
        width: 250px;
        padding-top: 46px;
        .module {
            td {
                width: 100px;
                height: 46px;
                text-align: center;
                // border: 1px solid #ccc;
                box-sizing: border-box;
            }
        }
        .fix-left-top {
            position: absolute;
            top: 0;
            left: 0;
            width: 250px;
            height: 46px;
            z-index: 1;
            line-height: 46px;
        }
    }
    input {
        // display: none;
    }
    .fix_col_table {
        width: 100px;
        border-color: #EBEEF5;
        border-top:none;
        border-left:none;
        tr {
            td {
                background: #f4f4f4;
            }
            &:nth-of-type(2n) {
                td {
                    background: #fff;
                }
            }
        }
        .fix_col_td {
            width: 98px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2; /* 可以显示的行数，超出部分用...表示*/
            -webkit-box-orient: vertical;
            &.index {
                width: 50px;
            }
        }
    }
    .row {
        height: 46px;
        &:nth-of-type(2n) td {
            background: #fff;
        }
        td {
            background: #f4f4f4;
            border-color: #EBEEF5;
        }
    }

    // *******************************
    .fix_col_wrap {
        width: 250px;
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-around;
        text-align: center;
        background: #fff;
        border-color: #EBEEF5;
        .fix_col_item {
            width: 100px;
            flex: 2;
            border-right: 1px solid #EBEEF5;
            &.index {
                flex: 1;
            border-left: 1px solid #EBEEF5;
            }
        }
    }
    .table_dom {
        border:none;
    }
</style>