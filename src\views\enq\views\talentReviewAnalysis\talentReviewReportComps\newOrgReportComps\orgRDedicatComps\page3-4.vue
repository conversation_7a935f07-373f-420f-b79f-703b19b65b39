<template>
  <div class="report_page" id="page1">
    <div class="report_section">
      <div class="page_second_title">敬业度和满意度差距分析</div>
      <div class="report_section_content clearfix">
        <tableComp
          :tableData="gapAnalysisList"
          :overflowTooltip="false"
          :needPagination="false"
          :border="true"
        ></tableComp>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">敬业度与满意度交叉人群分析</div>
      <div class="report_section_content clearfix">
        <div class="matrix_box clearfix">
          <div class="y_axis fl">满意度</div>
          <div class="matrix_main clearfix">
            <div
              class="matrix_item item_a"
              :class="'item_' + index"
              :key="item.key"
              v-for="(item, index) in crossPopulationMaps.data"
            >
              <div class="title">{{ item.key }}</div>
              <div class="text">{{ item.desc }}</div>
              <div class="text">{{ item.pct }}%，{{ item.number }}人</div>
            </div>
            <div class="x_axis">敬业度</div>
          </div>
        </div>
        <div class="explain_text">
          <tableComp
            :tableData="crossPopulationMaps"
            :overflowTooltip="false"
            :needPagination="false"
            :border="true"
          ></tableComp>
        </div>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">组织与人员敬业度分析</div>
      <div class="report_section_content clearfix">
        <div class="org_engagement_chart">
          <div class="chart_title page_third_title">按部门分析</div>
          <div id="jydOrgChart" class="chart_box"></div>
        </div>
      </div>
    </div>
    <div class="report_section">
      <div class="report_section_content clearfix">
        <!-- <tableComp :tableData="jydOrgListData" :needPagination="false" :border="true"></tableComp> -->
        <el-table ref="multipleTable" :data="jydOrgList">
          <el-table-column
            prop="name"
            label="名称"
            width="300"
          ></el-table-column>
          <el-table-column
            v-for="col in orgList"
            :prop="col.orgCode"
            :key="col.orgCode"
            :label="col.orgName"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
 
<script>
import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js";
import {
  jfAnalysisPage,
  getOrgUserEngagement,
} from "../../../../../request/api.js";
import tableComp from "@/components/talent/tableComps/tableComponent";
const mapDesc = {
  A: {
    desc1: "敬业、满意",
    desc2: "保持现状",
    proposal:
      "该群体属于敬业型员工，员工因为对企业现状满意故表现出敬业的状态，该群体是企业长期发展的保障，企业需要保留该部分员工。",
  },
  B: {
    desc1: "不敬业、满意",
    desc2: "士气激励",
    proposal:
      "该群体属于安逸型员工，员工认可目前的激励方式，但是不愿意付出更多努力为企业工作，企业需加强惩罚机制，转变此群体的观念，使其成为敬业的员工。",
  },
  C: {
    desc1: "敬业、不满意",
    desc2: "防止员工流失",
    proposal:
      "该群体属于自我驱动型员工，员工可能因为对企业未来抱着憧憬或者希望自己有更好的发展平台而努力工作，但是他们对现状并不满意，企业应提高其满意度防止流失风险。",
  },
  D: {
    desc1: "不敬业、不满意",
    desc2: "提升满意度",
    proposal:
      "该群体属于激发型员工，员工因为对企业不满意所以不敬业，过多的员工对公司存在负面情绪将成为公司发展的障碍。因此，如何更好地发挥工的作用，通过提高其满意度来提升其敬业度水平是企业下一步关注的问题。",
  },
};
export default {
  name: "",
  props: ["enqId", "orgCode", "isPdf", "userId"],
  components: { tableComp },
  data() {
    return {
      mapDesc: mapDesc,
      pageData: "",
      gapAnalysisList: {
        columns: [
          {
            label: "部门",
            prop: "name",
            width: 150,
          },
          {
            label: "人数",
            prop: "userNum",
            width: 150,
          },
          {
            label: "满意度",
            prop: "myd",
            width: 150,
          },
          {
            label: "敬业度",
            prop: "jyd",
            width: 150,
          },
          {
            label: "差距",
            prop: "difference",
            width: 150,
          },
          {
            label: "类型",
            prop: "gradeName",
            width: 200,
          },
          {
            label: "差距说明",
            prop: "gradeDesc",
          },
        ],
        data: [],
      },
      crossPopulationMaps: {
        columns: [
          {
            label: "区域",
            prop: "key",
            width: 180,
          },
          {
            label: "人数",
            prop: "number",
            width: 80,
          },
          {
            label: "满意度",
            prop: "myScore",
            width: 80,
          },
          {
            label: "敬业度",
            prop: "jyScore",
            width: 80,
          },
          {
            label: "建议",
            prop: "pct",
          },
        ],
        data: [],
      },
      jydOrgChart: {
        data: [],
      },
      jydOrgList: [],
      orgList: [],
    };
  },
  created() {},
  mounted() {
    this.jfAnalysisPageFun();
    this.getOrgEngagementFun();
  },
  methods: {
    getOrgEngagementFun() {
      getOrgEngagement({
        enqId: this.enqId,
        orgCode: this.orgCode,
        userId: this.userId,
      }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.pageData = res.data;
          // console.log(this.pageData)
          this.initData();
        }
      });
    },
    initData() {
      if (this.pageData) {
        // 敬业度和满意度差距分析
        this.gapAnalysisList.data = this.pageData.gapAnalysisList;
        // 敬业度与满意度交叉人群分析
        this.crossPopulationMaps.data = this.pageData.crossPopulationMaps;
      }
    },
    // // 组织与人员敬业度分析
    // getOrgUserEngagementFun(){
    //     getOrgUserEngagement({
    //         enqId:this.enqId,
    //         orgCode:this.orgCode,
    //     }).then(res=>{
    //         console.log(res)
    //         if(res.code == 200){
    //             // 按部门分析
    //             this.jydOrgChart.data = res.data.orgAnalysis.map(item=>{
    //                 return{
    //                     name:item.name,
    //                     value:item.val
    //                 }
    //             })
    //             echartsRenderPage("jydOrgChart","XBar",'1100',"280",this.jydOrgChart);
    //             this.jydOrgListData.data = res.data.jydOrgList
    //         }
    //     })
    // },
    initChart() {
      echartsRenderPage("jydOrgChart", "XBar", null, "280", this.jydOrgChart);
    },
    jfAnalysisPageFun() {
      let params = {
        enqId: this.enqId,
        orgCode: this.orgCode,
        number: "3",
      };
      jfAnalysisPage(params).then((res) => {
        console.log(res);
        if (res.code == "200") {
          let data = res.data.jfAnalysisPage3;
          // this.$set(
          //     this.gapAnalysisList,
          //     "data",
          //     data.gapAnalysisList
          // );
          // data.crossPopulationMaps.map((item) => {
          //     let desc1 = this.mapDesc[item.key]["desc1"];
          //     let desc2 = this.mapDesc[item.key]["desc2"];
          //     let proposal = this.mapDesc[item.key]["proposal"];
          //     item.key = item.key + "." + desc1;
          //     item["desc"] = desc2;
          //     item["proposal"] = proposal;
          // });
          // this.$set(
          //     this.crossPopulationMaps,
          //     "data",
          //     data.crossPopulationMaps
          // );

          data.jydOrgChart.map((item) => {
            item["value"] = item.val;
          });
          this.$set(this.jydOrgChart, "data", data.jydOrgChart);
          this.jydOrgList = this.dotToline(data.jydOrgList, "key");
          this.orgList = this.dotToline(data.orgList, "value", "orgCode");
          this.initChart();
        }
      });
    },
    dotToline(param, type, valueKey) {
      if (Array.isArray(param)) {
        if (param.length == 0) {
          return;
        }
        param.forEach((item) => {
          if (typeof item == "object") {
            for (const key in item) {
              if (item.hasOwnProperty(key)) {
                if (type == "key") {
                  let newKey = key.split(".").join("-");
                  item[newKey] = item[key];
                } else if (type == "value") {
                  let val = item[valueKey];
                  item[valueKey] = val.split(".").join("-");
                }
                // delete item[key];
              }
            }
          }
        });
        return param;
      }
    },
  },
  watch: {
    // pageData(val) {
    //   this.initData();
    // },
  },
};
</script>
 
<style scoped lang="scss">
.dedicated_main {
  height: 420px;
  overflow-y: auto;
}
.report_section {
  margin-bottom: 32px;
}
.matrix_box {
  position: relative;
  float: left;
  width: 384px;
  height: 266px;
  // background: darkkhaki;
  margin-right: 32px;
  .y_axis {
    width: 20px;
    height: 100%;
    text-align: center;
    border-right: 1px solid #44546a;
    color: #212121;
    // margin-right: 4px;
  }
  .matrix_main {
    float: left;
    width: 360px;
    // border-bottom: 1px solid #eee;
    display: flex;
    flex-flow: row-reverse wrap;
    .matrix_item {
      color: #fff;
      font-size: 14px;
      line-height: 22px;
      width: 175px;
      height: 115px;
      margin-bottom: 6px;
      padding: 29px 0 0 35px;
      float: left;
      &.item_0 {
        background-color: #bed269;
      }
      &.item_1 {
        background-color: #e28d80;
        margin-right: 7px;
      }
      &.item_2 {
        background-color: #719dd5;
      }
      &.item_3 {
        background-color: #dddee3;
        color: #a0a0a0;
        margin-right: 7px;
      }
      .title {
        font-size: 16px;
      }
    }
    .x_axis {
      width: calc(100% + 24px);
      float: left;
      border-top: 1px solid #44546a;
      margin-left: -24px;
      text-align: right;
    }
  }
}
.org_engagement_chart {
  width: 100%;
  // height: 260px;
}
.explain_text {
  overflow: hidden;
  color: #212121;
  line-height: 24px;
}
</style>