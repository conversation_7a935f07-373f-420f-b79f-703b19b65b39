<template>
  <el-dialog title="选择职责" v-model="dialogVisible" @close="$emit('update:showDA', false)" width="70%" center>
    <div class="page_section">
      <div class="page_section_aside">
        <div class="aside_tree_title flex_row_between">
          <div class="overflow_elps tree_title">业务领域</div>
        </div>
        <div class="aside_tree_list">
          <tree-comp-checkbox
            :treeData="treeData"
            v-model="defaultCheckedKeys"
            @node-click-callback="nodeClickCallback"
          />
        </div>
      </div>
      <div class="page_section_main page_shadow">
        <table-component
          :tableData="dutyTableData"
          :needIndex="false"
          :needPagination="false"
          :checkSelection="checkSelection"
          :selectionStatus="true"
          @selectionChange="selectionChange"
          @curSelectInfo="curSelectInfo"
        />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer align_right">
        <el-button class="page_clear_btn" @click="cancel">取 消</el-button>
        <el-button class="page_add_btn" type="primary" @click="submitBtn">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'
import { getDomainList, getOrgRespAllList, setOrgResp, setOrgRespList, requestAll } from '../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import treeCompCheckbox from '@/components/talent/treeComps/treeCompCheckbox copy.vue'

// Props定义
const props = defineProps({
  showDA: {
    type: Boolean,
    default: false
  },
  checkedId: {
    type: String,
    required: true
  }
})

// Emits定义
const emit = defineEmits(['update:showDA', 'setDutySign'])

// Store
const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

// 响应式状态
const dialogVisible = computed({
  get: () => props.showDA,
  set: value => emit('update:showDA', value)
})

const treeData = ref([])
const defaultCheckedKeys = ref([])
const dutyTableAllData = ref({})
const dutyTableData = ref({
  columns: [
    {
      label: '职责名称',
      prop: 'respName'
    },
    {
      label: '职责描述',
      prop: 'respDesc'
    }
  ],
  data: []
})
const checkSelection = ref([])
const checkDutyList = ref([])
const checkedDataList = ref([])

// 方法定义
const getDomainListFun = async () => {
  try {
    const res = await getDomainList({
      companyId: companyId.value,
      rStatus: ''
    })

    if (res.code == 200 && res.data.length > 0) {
      treeData.value = res.data.map(item => ({
        code: item.bizDomainCode,
        value: item.bizDomainName
      }))
    } else {
      treeData.value = []
    }
  } catch (error) {
    console.error('获取业务领域失败:', error)
    ElMessage.error('获取业务领域失败')
  }
}

const nodeClickCallback = (val, curCheckCode) => {
  selectBizDom(val, curCheckCode)
}

const setOrgRespListFun = async () => {
  try {
    const res = await setOrgRespList({
      orgCode: props.checkedId
    })

    if (res.code == 200) {
      const backData = res.data
      const backDataKey = Object.keys(backData)
      const backDataList = Object.values(backData)
      defaultCheckedKeys.value = backDataKey.join(',')
      checkedDataList.value = backDataList.flat()
      await getListData(backDataKey)
    }
  } catch (error) {
    console.error('获取组织职责列表失败:', error)
    ElMessage.error('获取组织职责列表失败')
  }
}

const getListData = async backDataKey => {
  try {
    const requireArr = backDataKey.map(key => getOrgRespAllList({ bizDomainCode: key }))

    const arr = await requestAll(requireArr)

    dutyTableData.value.data = []
    arr.forEach((response, index) => {
      dutyTableData.value.data = dutyTableData.value.data.concat(response.data)
      dutyTableAllData.value[backDataKey[index]] = response.data
    })

    checkList()
  } catch (error) {
    console.error('获取列表数据失败:', error)
    ElMessage.error('获取列表数据失败')
  }
}

const selectBizDom = async (bizDomainCheck, bizDomainCode) => {
  const bizDomainCheckList = bizDomainCheck
  dutyTableData.value.data = []

  for (const code of bizDomainCheckList) {
    if (dutyTableAllData.value[code]) {
      dutyTableData.value.data = dutyTableData.value.data.concat(dutyTableAllData.value[code])
    } else {
      try {
        const res = await getOrgRespAllList({ bizDomainCode })
        if (res.code == 200) {
          dutyTableAllData.value[code] = res.data
          dutyTableData.value.data = dutyTableData.value.data.concat(res.data)
        }
      } catch (error) {
        console.error('获取职责列表失败:', error)
        ElMessage.error('获取职责列表失败')
      }
    }
  }

  checkList()
}

const checkList = () => {
  const selection = []
  for (const checkedItem of checkedDataList.value) {
    const found = dutyTableData.value.data.find(item => item.respCode == checkedItem.respCode)
    if (found) {
      selection.push(found)
    }
  }
  checkSelection.value = selection
}

const selectionChange = selection => {
  checkDutyList.value = selection
}

const curSelectInfo = info => {
  // 处理当前选择的行信息
  console.log('当前选择:', info)
}

const cancel = () => {
  dialogVisible.value = false
}

const submitBtn = async () => {
  if (!checkDutyList.value.length) {
    ElMessage.warning('请选择职责！')
    return
  }

  try {
    const res = await setOrgResp({
      orgCode: props.checkedId,
      respList: checkDutyList.value
    })

    if (res.code == 200) {
      ElMessage.success(res.msg)
      dialogVisible.value = false
      emit('setDutySign', true)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('设置职责失败:', error)
    ElMessage.error('设置职责失败')
  }
}

// 生命周期钩子
onMounted(() => {
  getDomainListFun()
  if (props.checkedId) {
    setOrgRespListFun()
  }
})
</script>

<style scoped>
.page_section {
  display: flex;
  height: 500px;
}

.page_section_aside {
  width: 280px;
  border-right: 1px solid #dcdfe6;
  margin-right: 20px;
}

.aside_tree_title {
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.tree_title {
  font-weight: bold;
  color: #303133;
}

.aside_tree_list {
  height: calc(100% - 41px);
  overflow-y: auto;
  padding: 10px;
}

.page_section_main {
  flex: 1;
  overflow-y: auto;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

:deep(.el-dialog__header) {
  background-color: var(--color-tagbg);
  padding: 15px 20px;
}
</style>
