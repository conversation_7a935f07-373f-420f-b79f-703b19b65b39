<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">本岗位工作时长</div>
          <div class="content_item_content" id="post_work"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">最近晋升日期</div>
          <div class="content_item_content" id="latest_promotion"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">理想岗位-TOP岗位类型</div>
          <div class="content_item_content" id="job_class"></div>
        </div>
      </div>
      <div class="content_item el-col-16">
        <div class="content_item_main">
          <div class="content_item_title">理想岗位-TOP岗位</div>
          <div class="content_item_content" id="expectation_post"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            岗位工作明细
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { postWork, queryPostWorkList, exportData } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')

const postWorkData = reactive({
  data: []
})

const latestPromotion = reactive({
  data: []
})

const expectationJobClass = reactive({
  data: []
})

const expectationPost = reactive({
  data: []
})

const filterData = ref([])
const page = ref(1)
const size = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '一级组织',
      prop: 'onLevelName'
    },
    {
      label: '二级组织',
      prop: 'twoLevelName'
    },
    {
      label: '三级组织',
      prop: 'threeLevelName'
    },
    {
      label: '四级组织',
      prop: 'fourLevelName'
    },
    {
      label: '姓名',
      prop: 'userName'
    },
    {
      label: '岗位',
      prop: 'postName'
    },
    {
      label: '本岗位工作时长',
      prop: 'currentPostAge',
      width: 120
    },
    {
      label: '最近晋升日期',
      prop: 'lastPromotionDate',
      width: 120
    },
    {
      label: '目标岗位',
      prop: 'targetPostName'
    },
    {
      label: '预计周期',
      prop: 'code_name'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('post_work', 'YBar', '350', '250', postWorkData)
  echartsRenderPage('latest_promotion', 'YBar', '350', '250', latestPromotion)
  echartsRenderPage('job_class', 'YBar', '220', '250', expectationJobClass)
  echartsRenderPage('expectation_post', 'XBar', '470', '250', expectationPost)
}

const postWorkFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await postWork(params)
    if (res.code == 200) {
      const data = res.data
      postWorkData.data = window.$util.addPercentSign(data.postWork, 'value')
      latestPromotion.data = window.$util.addPercentSign(data.latestPromotion, 'value')
      data.expectationJobClass = data.expectationJobClass || []
      expectationJobClass.data = window.$util.addPercentSign(data.expectationJobClass, 'value')
      expectationPost.data = data.expectationPost
      initChart()
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  page.value = 1
  postWorkFun()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryPostWorkList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error(error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'c'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '岗位工作明细')
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  postWorkFun()
  getTableData()
})
</script>

<style scoped lang="scss"></style>
