<template>
    <div class="org_report_main" :class="{'marginB_16':isPdf}">
        <div class="page_second_title">基本信息</div>
        <div class="basic_info_wrap flex_row_start">
            <div class="basic_info_item">
                <div class="title">组织名称</div>
                <div class="text">{{basicInfo.orgName}}</div>
            </div>
            <div class="basic_info_item">
                <div class="title">上级组织</div>
                <div class="text">{{basicInfo.parentOrgName || '--'}}</div>
            </div>
            <div class="basic_info_item">
                <div class="title">所属公司</div>
                <div class="text">{{basicInfo.companyName}}</div>
            </div>
            <div class="basic_info_item">
                <div class="title">组织负责人</div>
                <div class="text">{{basicInfo.orgLeaderName}}</div>
            </div>
            <div class="basic_info_item">
                <div class="title">岗位数量</div>
                <div class="text">{{basicInfo.postCount}}</div>
            </div>
            <div class="basic_info_item">
                <div class="title">员工数量</div>
                <div class="text">{{basicInfo.userCount}}</div>
            </div>
            <div class="basic_info_item">
                <div class="title">近一年入职人员</div>
                <div class="text">{{basicInfo.oneYearUserCount || '--'}}</div>
            </div>

            <!-- <div class="basic_info_item">
                <div class="title">组织层级</div>
                <div class="text">{{basicInfo.orgLevelName}}</div>
            </div> -->
        </div>
        <!-- <div class="page_second_title">职责模块</div>
        <div class="duty_wrap">
            <tableComps :needPagination="false" :overflowTooltip="false" :tableData="tableData"></tableComps>
        </div> -->
    </div>
</template>
 
<script>
    import { getOrgReportBasicInfo } from "../../../../request/api";
    import tableComps from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "orgRBasicInfo",
        props: ["enqId", "orgCode","isPdf"],
        components: { tableComps },
        data() {
            return {
                basicInfo: {},
                tableData: {
                    columns: [
                        {
                            label: "职责模块",
                            prop: "respName",
                            width: 200,
                        },
                        {
                            label: "职责描述",
                            prop: "respDesc",
                        },
                    ],
                    data: [],
                },
            };
        },
        created() {
            console.log("basic render created");
            this.getOrgReportBasicInfoFun();
        },
        mounted() {
            console.log("basic render mounted");
        },
        methods: {
            getOrgReportBasicInfoFun() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getOrgReportBasicInfo(params).then((res) => {
                    if (res.code == "200") {
                        this.basicInfo = res.data;
                        this.$set(this.tableData, "data", res.data.respInfoList);
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .basic_info_wrap {
        padding-left: 16px;
        margin-bottom: 32px;
        flex-flow: row wrap;
        .basic_info_item {
            width: 25%;
            margin-bottom: 32px;
            .title {
                font-size: 14px;
                color: #0099fd;
                font-weight: bold;
                margin-bottom: 8px;
            }
            .text {
                font-size: 12px;
                color: #212121;
            }
        }
    }
    .duty_wrap {
        .duty_name {
            flex: 1;
            width: 200px;
            margin-right: 20px;
        }
        .duty_desc {
            flex: 10;
        }
        .duty_head {
        }
    }
</style>