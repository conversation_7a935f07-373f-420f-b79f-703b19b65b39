<template>
    <div class="talent_analysis_wrap bg_write">
        <div class="page_main_title">
            <div class="goback_geader" @click="$util.goback()">
                <i class="el-icon-arrow-left"></i>返回
            </div>
            人才区分分析
        </div>
        <div class="">
            <!-- <div class="page_second_title">
                测评项目
                <span class="fr">{{ evalName }}{{ reportGenTime }}</span>
            </div> -->
            <div class="page_section talent_analysis_center clearfix">
                <!-- <div class="talent_analysis_aside">
                    <tree-comp-radio
                        :treeData="treeData"
                        @clickCallback="treeClick"
                    ></tree-comp-radio>
                </div> -->
                <div class="talent_analysis_main">
                    <div class="export_btn">
                        <el-button type="primary" size="mini" @click="exportExcel">导出全部</el-button>
                    </div>
                    <tabs-diffent-pane
                        :tabsData="tabsData"
                        :isDefaultTheme="true"
                        @tabsChange="tabsChange"
                    ></tabs-diffent-pane>
                    <div class="" v-if="!this.$route.query.evalId">
                        <div class="eval_wrap flex_row_wrap_start">
                            <div
                                class="eval_item"
                                @click="changeEval(item.evalId)"
                                v-for="item in projectList"
                                :class="{ select: evalId == item.evalId }"
                            >
                                {{ item.evalName }}
                            </div>
                        </div>
                        <coustom-pagination
                            class="paddT_12"
                            :total="total"
                            @pageChange="pageChange"
                        ></coustom-pagination>
                    </div>
                    <component
                        :orgCode="orgCode"
                        :evalId="evalId"
                        :is="tabsPaneComp[compName]"
                    ></component>
                </div>
            </div>
        </div>
    </div>
</template>
 
<script>
    import {
        wholeComprehensiveScore,
        getEvalInfo,
        evaluationReportList,
        allExportData
    } from "../../../../request/api";
    import { getOrgDeptTree } from "@/views/entp/request/api";
    import tabsDiffentPane from "@/components/talent/tabsComps/tabsDiffentPane";
    import TreeCompRadio from "@/components/talent/treeComps/treeCompRadio.vue";
    import CoustomPagination from "@/components/talent/paginationComps/coustomPagination.vue";

    const compObj = {
        employeeMatchingQuadrant: (resolve) =>
            require(["./employeeMatchingQuadrant"], resolve),
        employeeDevelopmentArea: (resolve) =>
            require(["./employeeDevelopmentArea"], resolve),
        personnelPerformanceMatrix: (resolve) =>
            require(["./personnelPerformanceMatrix"], resolve),
    };

    export default {
        name: "talentDifferentiationAnalysisHome",
        props: [],
        components: {
            tabsDiffentPane,
            TreeCompRadio,
            CoustomPagination,
        },
        data() {
            return {
                current: 1,
                size: 10,
                evalId: this.$route.query.evalId,
                projectList: [],
                total: 0,
                currPage: 1,
                pageSize: 10,
                orgCode: "",
                evalName: "",
                reportGenTime: "",
                compName: "",
                tabsPaneComp: compObj,
                treeData: [],
                tabsData: [
                    {
                        label: "员工岗能匹配四象限",
                        name: "employeeMatchingQuadrant",
                    },
                    {
                        label: "员工岗能发展区域归集",
                        name: "employeeDevelopmentArea",
                    },
                    {
                        label: "人员绩效矩阵",
                        name: "personnelPerformanceMatrix",
                    },
                ],
            };
        },
        created() {
            this.compName = this.tabsData[0].name;
            // this.getTreeData();
            if (!this.$route.query.evalId) {
                this.evaluationReportListFun();
            } else {
                this.getEvalInfoFun();
            }
        },
        methods: {
            tabsChange(data) {
                this.compName = data.name;
            },
            getTreeData() {
                getOrgDeptTree().then((res) => {
                    console.log(res);
                    this.treeData = res;
                });
            },
            treeClick(code) {
                console.log(code);
                this.orgCode = code;
            },
            getEvalInfoFun() {
                getEvalInfo({ evalId: this.evalId }).then((res) => {
                    console.log(res);
                    this.evalName = res.evalName;
                    this.reportGenTime = res.reportGenTime;
                });
            },
            evaluationReportListFun() {
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                };
                evaluationReportList(params).then((res) => {
                    console.log(res);
                    if (res.code == "200" && res.data) {
                        this.projectList = res.data;
                        this.total = res.total;
                        this.evalId = this.projectList[0].evalId;
                        // this.getEvalInfoFun();
                    }
                });
            },
            changeEval(id) {
                this.evalId = id;
                // this.getEvalInfoFun();
            },
            pageChange(size, page) {
                this.pageSize = size;
                this.currPage = page;
                this.evaluationReportListFun();
            },
            exportExcel(){
                let params = {
                    evalId:this.evalId,
                    orgCode:this.orgCode,
                    type:'different'
                }
                allExportData(params).then(res => {
                    console.log(res);
                    this.$exportDownload(res.data,'人才区分分析');
                })
            }
        },
    };
</script>
 
<style scoped lang="scss">
    .page_second_title {
        margin-left: 16px;
    }
    .talent_analysis_center {
        .talent_analysis_aside {
            width: 200px;
            float: left;
            max-height: 500px;
            margin-right: 16px;
        }
        .talent_analysis_main {
            position: relative;
            overflow-x: hidden;
            .export_btn{
                position: absolute;
                right: 0;
                top:6px;
                z-index: 99;
            }
        }
    }
    .eval_wrap {
        .eval_item {
            background-color: #e5f7fd;
            border: 1px solid #00b0f0;
            line-height: 28px;
            margin-right: 16px;
            padding: 0 8px;
            margin-bottom: 8px;
            width: calc(25% - 16px);
            cursor: pointer;
            &.select {
                background-color: #00b0f0;
                color: #fff;
            }
        }
    }
</style>