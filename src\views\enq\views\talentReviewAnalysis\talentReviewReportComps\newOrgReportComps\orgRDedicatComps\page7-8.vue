<template>
  <div class="report_page" id="page1">
    <div class="report_section">
      <div class="page_second_title">各职层敬业度和驱动因素</div>
      <div class="report_section_content clearfix">
        <el-table ref="multipleTable" :data="tableData">
          <el-table-column
            prop="name"
            label="驱动因素"
            width="300"
          ></el-table-column>
          <el-table-column
            v-for="col in tableCloumns"
            :prop="col.jobLevelCode"
            :key="col.jobLevelCode"
            :label="col.jobLevelName"
          ></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="report_section">
      <div class="page_second_title">各层级得分差异分析</div>
      <div class="report_section_content clearfix">
        <div class="chart_box" id="job_level_chart"></div>
      </div>
    </div>
    <!-- <div class="report_section">
            <div class="page_second_title">参考示意说明</div>
            <div class="report_section_content clearfix">
                <div class="eg_chart_box" id="eg_chart_1">
                </div>
                <div class="explain_text">
                    <div class="explain_title">形态一：层级越高的员工，敬业度越高</div>
                    <div
                        class
                    >代表含义：这种情况经常出现在快速发展的企业，敬业度自上而下传递当中出现较大的损耗，中层管理人员的高敬业度水平难以传达到基层，建议此类型企业在业务快速拓展的同时，也需要进一步提高内部管理能力。</div>
                </div>
            </div>
            <div class="report_section_content clearfix">
                <div class="eg_chart_box" id="eg_chart_2">
                </div>
                <div class="explain_text">
                    <div class="explain_title">形态二：层级越高的员工，敬业度越低</div>
                    <div
                        class
                    >代表含义：上下级利润分配不均：普通员工认为自己的付出得到企业的认可，对此表示感激并且愿意为企业努力工作。但高管的股权和普通员工的差距不明显，他们认为自己的付出和获得不成正比，影响其工作积极性。-  同一层级分配不均：企业可能对核心部门的高管投入较大，导致其他部门高管认为不公平。</div>
                </div>
            </div>
            <div class="report_section_content clearfix">
                <div class="eg_chart_box" id="eg_chart_3">
                </div>
                <div class="explain_text">
                    <div class="explain_title">形态三：层级越高的员工，敬业度越低</div>
                    <ul>
                        <li>代表含义：</li>
                        <li class="line">-  基层管理人员敬业度最低：</li>
                        <li class="dot">对薪酬福利不满，该群体的薪酬福利水平较低，工作较为繁琐，是企业日常工作承上启下的重要群体，企业需要考虑基层管理者的全面回报体系，对比外部市场，看所付出和回报之间是否有较大差距。</li>
                        <li class="line">-  中层管理人员敬业度最低：</li>
                        <li class="dot">工作压力过大：这种情况一般出现在快速发展的企业，人员提拔过快，中层的能力和职责不匹配，导致中层工作压力过大，敬业度较低。</li>
                        <li class="dot">对全面回报不满：中层具备一定工作经验，但薪酬、福利、职业发展机会和其岗位不匹配，导致敬业度较低。</li>
                        <li class="dot">对资源流程不满：中层对当前的工作和公司非常熟悉，公司的资源、流程配置无法有效支持其工作，导致敬业度较低。</li>
                        
                    </ul>
                </div>
            </div>
        </div> -->
  </div>
</template>
 
<script>
import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js";
import { jfAnalysisPage } from "../../../../../request/api.js";
import tableComp from "@/components/talent/tableComps/tableComponent";
export default {
  name: "",
  props: ["enqId", "orgCode", "isPdf", "userId"],
  components: { tableComp },
  data() {
    return {
      tableData: [],
      tableCloumns: [],
      mydJobLevelChart: {
        data: [],
      },
      egChartData1: {
        data: [
          {
            name: "总经理",
            value: "87",
          },
          {
            name: "副总",
            value: "80",
          },
          {
            name: "总监",
            value: "65",
          },
          {
            name: "经理",
            value: "60",
          },
          {
            name: "主管",
            value: "55",
          },
          {
            name: "员工",
            value: "50",
          },
        ],
      },
      egChartData2: {
        data: [
          {
            name: "总经理",
            value: "74",
          },
          {
            name: "副总",
            value: "77",
          },
          {
            name: "总监",
            value: "78",
          },
          {
            name: "经理",
            value: "81",
          },
          {
            name: "主管",
            value: "83",
          },
          {
            name: "员工",
            value: "87",
          },
        ],
      },
      egChartData3: {
        data: [
          {
            name: "总经理",
            value: "87",
          },
          {
            name: "副总",
            value: "85",
          },
          {
            name: "总监",
            value: "80",
          },
          {
            name: "经理",
            value: "64",
          },
          {
            name: "主管",
            value: "62",
          },
          {
            name: "员工",
            value: "67",
          },
        ],
      },
    };
  },
  created() {
    this.jfAnalysisPageFun();
  },
  mounted() {},
  methods: {
    initChart() {
      echartsRenderPage(
        "job_level_chart",
        "XBar",
        null,
        "280",
        this.mydJobLevelChart
      );
      // echartsRenderPage(
      //     "eg_chart_1",
      //     "XBar",
      //     "350",
      //     "230",
      //     this.egChartData1
      // );
      // echartsRenderPage(
      //     "eg_chart_2",
      //     "XBar",
      //     "350",
      //     "230",
      //     this.egChartData2
      // );
      // echartsRenderPage(
      //     "eg_chart_3",
      //     "XBar",
      //     "350",
      //     "230",
      //     this.egChartData3
      // );
    },
    jfAnalysisPageFun() {
      let params = {
        enqId: this.enqId,
        orgCode: this.orgCode,
        userId: this.userId,
        number: "7",
      };
      jfAnalysisPage(params).then((res) => {
        console.log(res);
        if (res.code == "200") {
          let data = res.data.jfAnalysisPage7;
          data.mydJobLevelChart.map((item) => {
            item["value"] = item.val;
          });
          this.$set(this.mydJobLevelChart, "data", data.mydJobLevelChart);
          this.tableData = this.dotToline(data.mydJobLevelList, "key");
          this.tableCloumns = this.dotToline(
            data.jobLevels,
            "value",
            "jobLevelCode"
          );
          this.$nextTick(() => {
            this.initChart();
          });
        }
      });
    },
    dotToline(param, type, valueKey) {
      if (Array.isArray(param)) {
        if (param.length == 0) {
          return;
        }
        param.forEach((item) => {
          if (typeof item == "object") {
            for (const key in item) {
              if (item.hasOwnProperty(key)) {
                if (type == "key") {
                  let newKey = key.split(".").join("-");
                  item[newKey] = item[key];
                } else if (type == "value") {
                  let val = item[valueKey];
                  item[valueKey] = val.split(".").join("-");
                }
                // delete item[key];
              }
            }
          }
        });
        return param;
      }
    },
  },
};
</script>
 
<style scoped lang="scss">
.dedicated_main {
  height: 420px;
  overflow-y: auto;
}
.report_section_content {
  margin-bottom: 16px;
}
.chart_box {
  // float: left;
  // width: 230px;
  // height: 140px;
  /*background: darkkhaki;*/
  // margin-right: 32px;
}
.eg_chart_box {
  float: left;
  width: 350px;
  height: 200px;
  /*background: darkkhaki;*/
  margin-right: 32px;
  overflow: hidden;
}
.line {
  line-height: 28px;
}
.dot {
  list-style: inside disc;
}
</style>