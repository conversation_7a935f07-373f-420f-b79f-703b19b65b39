<template>
    <div class="employee_matching_quadrant_wrap">
        <div class="employee_matching_quadrant_main">
            <div class="echart_analysis_wrap">
                <p class="title page_third_title">分析图</p>
                <div class="echart_analysis_main flex_row_betweens">
                    <div class="echart_analysis_left">
                        <p class="title">岗位核心能力正负项偏离说明</p>
                        <div><span>正向偏离：</span>评估实际得分大于目标值</div>
                        <div><span>负向偏离：</span>评估实际得分大于目标值</div>
                        <div class="decript_img">
                            <img src="../../../../../../../public/images/matrix_bg.png" alt="" srcset="">
                        </div>
                        <div>
                            <span>A区域：</span
                            >正向偏离目标要求，且该能力锁的实际能力值高于50分
                        </div>
                        <div>
                            <span>B区域：</span
                            >正向偏离目标要求，且该能力锁的实际能力值高于50分
                        </div>
                        <div>
                            <span>C区域：</span
                            >正向偏离目标要求，且该能力锁的实际能力值高于50分
                        </div>
                        <div>
                            <span>D区域：</span
                            >正向偏离目标要求，且该能力锁的实际能力值高于50分
                        </div>
                    </div>
                    <div class="echart_analysis_right">
                        <div class="chart_box" id="chart_box"></div>
                    </div>
                </div>
            </div>
            <div class="table_area_wrap">
                <table-component
                    :tableData="tableData"
                    :needIndex="true"
                    :loading="loading"
                    @handleSizeChange="handleSizeChange"
                    @handleCurrentChange="handleCurrentChange"
                ></table-component>
            </div>
        </div>
    </div>
</template>
 
<script>
    import { personDeviationMatrix,personDeviationMatrixTable } from "../../../../request/api";
    // import {echartsRenderPage} from "../../../../../../../public/js/echartsimg/echartsToImg";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import quadrantScatterPlot from "@/components/echartsComps/scatterPlot/quadrantScatterPlot";
    export default {
        name: "employeeMatchingQuadrant",
        props: ["orgCode", "evalId"],
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading: false,
                chartData:{
                    data:[],
                    legend:[]
                },
                tableData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "org_name",
                        },
                        {
                            label: "岗位",
                            prop: "post_name",
                        },
                        {
                            label: "姓名",
                            prop: "name",
                        },
                        {
                            label: "综合得分",
                            prop: "overallScore",
                        },
                        {
                            label: "偏离度",
                            prop: "devDegree",
                        },
                        {
                            label: "所属区域",
                            prop: "matrix",
                        },
                    ],
                    data: [
                    ],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        components: {
            tableComponent,
        },
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                        this.getTableData();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                        this.getTableData();
                    }
                },
            },
        },
        methods: {
            getData() {
                this.loading = true;
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                personDeviationMatrix(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.chartData, "data", res.data.chartData);
                        this.$set(this.chartData, "legend", res.data.legend);
                        echartsRenderPage('chart_box','Quadrant',600,500,this.chartData)
                        this.loading = false;
                    } else {
                        this.loading = false;
                    }
                });
            },
            handleSizeChange(size){
                this.pageSize = size;
                this.getTableData();
            },
            handleCurrentChange(current){
                this.currPage = current;
                this.getTableData();
            },
            getTableData() {
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                    type:''
                };
                personDeviationMatrixTable(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .employee_matching_quadrant_wrap {
        .employee_matching_quadrant_main {
            .echart_analysis_wrap {
                .echart_analysis_main {
                    .echart_analysis_left {
                        padding: 10px;
                        width: 30%;
                        background: #F7F7F7;
                        .title {
                            font-weight: 700;
                            line-height: 30px;
                        }
                        div {
                            margin: 0 0 8px 0;
                            span {
                                font-weight: 700;
                            }
                        }
                        .decript_img {
                            width: 240px;
                            height: 200px;
                            img{
                                width: 100%;
                            }
                        }
                    }
                    .echart_analysis_right {
                        // width: 400px;
                        width: 70%;
                    }
                }
            }
            .table_area_wrap {
                margin: 10px 0 0 0;
            }
        }
    }
</style>