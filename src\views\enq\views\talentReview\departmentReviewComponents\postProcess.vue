<template>
  <div class="post_process_main">
    <div class="departmen_main">
      <div class="page_second_title marginT_30">岗位参与流程</div>
      <div class="department_main_title marginT_20">
        <!-- <div class="">
                    <div class="title fl">流程分类</div>
                    <div class="fl">
                        <el-select v-model="processType" size="mini" placeholder="请选择">
                            <el-option label="供应链" value="供应链"></el-option>
                            <el-option label="供应链" value="供应链"></el-option>
                            <el-option label="供应链" value="供应链"></el-option>
                            <el-option label="供应链" value="供应链"></el-option>
                        </el-select>
                    </div>
                </div> -->
        <div class="rasic_text clearfix">
          <div class="fl main_color">请点击右侧RASIC中的一项，为岗位设置相关的职责</div>
          <div class="rasic flex_row_start fr">
            <div
              class="item"
              @click="changeRASIC(item)"
              :class="{ active: item == activeRASIC }"
              :key="index"
              v-for="(item, index) in rasic"
            >
              {{ item }}
            </div>
          </div>
          <div class="fr">了解RASIC</div>
          <el-icon class="fr"><QuestionFilled /></el-icon>
          <div class="rasic_desc main_color fr align_right">{{ activeRASIC }}:{{ rasicDesc }}</div>
        </div>
      </div>
      <div class="post_process_table marginT_16">
        <el-table :data="tableData" @cell-click="setCellStyle">
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column prop="bizProcessCode" label="流程编码" width="100"></el-table-column>
          <el-table-column prop="bizProcessName" label="流程名称" width="160"></el-table-column>
          <el-table-column prop="layerNo" width="50px" label="层级" :formatter="formatterLayerNoFun"></el-table-column>
          <el-table-column prop="prevProcessName" label="前置流程" width="150"></el-table-column>
          <el-table-column
            width="40"
            class="post_column"
            :index="index"
            class-name="post_column"
            v-for="(item, index) in postColumns"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
          >
            <template #default="scope" v-if="showSlot">
              <div class="post_ipt" :class="{ active: scope.row[item.prop] !== '' }">
                {{ tableData[scope.$index][item.prop] }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="marginT_30 align_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'
import { getPostProcess, saveEnqBizProcess } from '../../../request/api'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  orgCode: String
})

const emit = defineEmits(['prevStep', 'nextStep'])

const showSlot = ref(true)
const postCodeArr = ref([])
const processType = ref('')
const activeRASIC = ref('R')
const resData = ref([])
const rasic = ['R', 'A', 'S', 'I', 'C']
const rasicObj = {
  R: '主要负责，负责启动某一工作，并确保该工作的顺利完成',
  A: '审核、审批，审核以批准或否决',
  S: '协助、支持，为某项工作提供支持和协助',
  I: '被告知、知晓，某项工作被告知，但没有直接影响力',
  C: '咨询、反馈，为工作提供咨询和反馈意见，但没有直接影响'
}
const rasicDesc = ref('')
const tableData = ref([])
const postColumns = ref([])

onMounted(() => {
  rasicDesc.value = rasicObj[activeRASIC.value]
  getPostProcessFun()
})

const changeRASIC = val => {
  activeRASIC.value = val
  rasicDesc.value = rasicObj[activeRASIC.value]
}

const setCellStyle = (row, column) => {
  if (column.index !== undefined) {
    // 针对动态渲染表头添加了index属性，用来判断是否点击了可操作区域（勾选区域）
    showSlot.value = false
    const index = row.index
    const postCode = column.property
    const rasicValue = tableData.value[index][postCode]

    tableData.value[index][postCode] = rasicValue == activeRASIC.value ? '' : activeRASIC.value
    showSlot.value = true
  }
}

const saveEnqBizProcessFun = async stepType => {
  try {
    const params = tableData.value.map(item => {
      const mapList = postCodeArr.value
        .filter(code => item[code])
        .map(code => ({
          postCode: code,
          rasic: item[code]
        }))

      return {
        bizProcessCode: item.bizProcessCode,
        enqId: item.enqId,
        orgCode: item.orgCode,
        mapList
      }
    })

    const res = await saveEnqBizProcess(params)

    if (res.code == 200) {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('保存失败')
  }
}

const getPostProcessFun = async () => {
  try {
    const res = await getPostProcess({
      enqId: props.enqId,
      orgCode: props.orgCode
    })

    if (res.code == 200) {
      const data = res.data

      postCodeArr.value = data.enqPostInfoList.map(item => item.post_code)
      postColumns.value = data.enqPostInfoList.map(item => ({
        label: item.post_name,
        prop: item.post_code
      }))

      resData.value = data.enqOrgProcessPostList
      formatterData()
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const formatterData = () => {
  // 取出columns的code
  const codeArr = postColumns.value.map(item => item.prop)

  tableData.value = resData.value.map((item, index) => {
    const newItem = {
      ...item,
      index
    }

    // 初始化所有postCode对应的值为空字符串
    codeArr.forEach(code => {
      newItem[code] = ''
    })

    // 设置已选中的值
    item.enqOrgProcessPostList.forEach(list => {
      newItem[list.postCode] = list.rasic
    })

    return newItem
  })
}

const formatterLayerNoFun = obj => {
  const levelMap = {
    1: '一級',
    2: '二級',
    3: '三級',
    4: '四級',
    5: '五級',
    6: '六級'
  }
  return levelMap[obj.layerNo] || ''
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      saveEnqBizProcessFun('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') {
        emit('prevStep')
      }
    })
}

const nextBtn = () => {
  saveEnqBizProcessFun('nextStep')
}
</script>

<style scoped lang="scss">
.department_main_title {
  line-height: 40px;
  display: flex;
  justify-content: flex-end;
  .title {
    /*font-weight: bold;*/
    margin-right: 10px;
  }

  .rasic_text {
    line-height: 30px;
    width: 100%;
    .rasic_desc {
      width: 100%;
    }

    .icon {
      line-height: 30px;
    }

    .rasic {
      padding-left: 10px;
      padding-top: 3px;

      .item {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        background: #e5e5e5;
        line-height: 24px;
        text-align: center;
        margin-right: 4px;
        color: #fff;
        cursor: pointer;

        &.active {
          background: #0099fd;
        }
      }
    }
  }
}

.post_column {
  margin-right: 4px;
}

.post_column.active {
  .post_ipt {
    background: #0099fd;
    color: #fff;
    text-align: center;
    font-weight: bold;
    line-height: 28px;
  }
}

.post_ipt {
  width: 30px;
  height: 30px;
  border: 1px solid #ebeef5;
  cursor: pointer;
  color: #000;
  text-align: center;
  line-height: 30px;
  margin-right: 3px;

  &:hover {
    background: #b3e0fd;
  }

  &.active {
    background: #0099fd;
    color: #fff;
  }
}

.el-table th > .cell {
  padding-left: 7px;
  padding-right: 7px;
  margin-right: 4px;
}

.el-table__row .cell {
  padding: 0;
}
.el-table--enable-row-transition {
  .el-table__body {
    tr {
      td {
        &:first-child {
          padding-left: 15px;
        }
        &:nth-child(4) {
          padding-left: 15px;
        }
      }
    }
  }
}
.el-table__row {
  height: 45px;
}
.el-table__header-wrapper {
  background-color: #f4f4f4;
}
</style>
