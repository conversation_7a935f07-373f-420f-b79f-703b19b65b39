<template>
    <div class="chart_main">
        <div class="_chart" :id="id" :style="styleObj"></div>
    </div>
</template>
 
<script>
export default {
    name: "graphChartComponent",
    props: ["chartData", "width", "height"],
    components: {},
    data() {
        return {
            id: null,
            styleObj: {
                width: this.width + "px",
                height: this.height + "px"
            }
        };
    },
    watch: {
        chartData: function() {
            this.init(this.chartData);
        }
    },
    mounted() {
        if (this.chartData.length == 0) {
            return;
        }
        this.init(this.chartData);
    },
    methods: {
        init(chartData) {
            let id = this.$util.createRandomId();
            this.id = id;
            this.$nextTick(() => {
                this.toDraw(id, chartData);
            });
        },
        toDraw(id, chartData) {
            let _this = this;
            let myChart = this.$EC.init(document.getElementById(id));
            if (chartData.length == 0) {
                myChart.clear();
                return;
            }
            let options = {
                tooltip: {
                    show:false
                },
                animationDurationUpdate: 1500,
                series: [
                    {
                        type: 'graph',
                        layout: 'force',
                        symbolSize: 55,
                        focusNodeAdjacency: true,
                        categories: [
                            {
                                itemStyle: {
                                    normal: {
                                        color: "#0d7ff6",
                                    }
                                }
                            }, 
                            {
                                itemStyle: {
                                    normal: {
                                        color: "#409EFF",
                                    }
                                }
                            }, 
                        ],
                        label: {
                            normal: {
                                show: true,
                                textStyle: {
                                    fontSize: 12,
                                    textBorderColor:'transparent',
                                    color:'#333'
                                },
                            }
                        },
                        force: {
                            repulsion: 600,
                            initLayout:'circular',
                            layoutAnimation :false,
                        },
                        edgeSymbolSize: [4, 50],
                        edgeLabel: {
                            normal: {
                                show: false,
                            }
                        },
                        data:chartData.data,
                        links:chartData.links,
                        lineStyle: {
                            normal: {
                                opacity: 0.9,
                                width: 1,
                                curveness: 0,
                                // color:'#333'
                            }
                        }
                    }
                ]
            };
            myChart.setOption(options);
        }
    }
};
</script>
 
<style scoped lang="scss">
.chart_main {
    width: 100%;
    height: 100%;
    ._chart {
        width: 100%;
        height: 100%;
    }
}
</style>