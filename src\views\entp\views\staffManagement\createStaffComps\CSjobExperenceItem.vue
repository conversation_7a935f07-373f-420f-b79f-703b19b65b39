<template>
    <div>
        <div class="edu_info_item" v-for="(item, index) in workData" :key="item.id">
            <el-input class="item long" v-model="item.companyName" placeholder="填写公司名称"></el-input>
            <el-date-picker
                class="item long"
                :title="item.beginDate"
                value-format="YYYY-MM-DD"
                v-model="item.beginDate"
                type="date"
                placeholder="选择入职日期"
            ></el-date-picker>
            <el-date-picker
                class="item long"
                :title="item.endDate"
                value-format="YYYY-MM-DD"
                v-model="item.endDate"
                @change="endDateChange(item.endDate,item)"
                type="date"
                placeholder="选择离职日期"
            ></el-date-picker>
            <el-cascader
                class="item"
                :options="jobLevelOption"
                v-model="item.jobLevelCode"
                :change-on-select="true"
                :clearable="true"
                :filterable="true"
                :show-all-levels="false"
                :props="{value:'code',label:'value',expandTrigger:'hover'}"
                @change="handleChange($event,'jobLevelCode')"
            ></el-cascader>
            <el-cascader
                class="item short"
                :options="jobClassOption"
                v-model="item.jobClassCode"
                :change-on-select="true"
                :clearable="true"
                :filterable="true"
                :show-all-levels="false"
                :props="{value:'code',label:'value',expandTrigger:'hover'}"
                @change="handleChange($event,'jobClassCode')"
            ></el-cascader>
            <el-select class="item short" v-model="item.bizDomainCode" placeholder>
                <el-option
                    v-for="item in bizDomainOption"
                    :key="item.bizDomainCode"
                    :label="item.bizDomainName"
                    :value="item.bizDomainCode"
                ></el-option>
            </el-select>
            <el-select class="item short" v-model="item.postRelated" placeholder>
                <el-option
                    v-for="item in yesOrNo"
                    :key="item.dictCode"
                    :label="item.codeName"
                    :value="item.dictCode"
                ></el-option>
            </el-select>
            <el-select class="item short" v-model="item.industryRelated" placeholder>
                <el-option
                    v-for="item in yesOrNo"
                    :key="item.dictCode"
                    :label="item.codeName"
                    :value="item.dictCode"
                ></el-option>
            </el-select>
            <div class="item item_icon_wrap">
                <i class="item_icon el-icon-delete icon_del" @click="deleteItem(item,index)"></i>
            </div>
        </div>
    </div>
</template>

<script>
    import {
        jobLevelTree,
        getJobClassTree,
        getDomainList,
    } from "../../../request/api";
    export default {
        name: "workExperienceItem",
        props: {
            workData: {
                type: Array,
                default: function () {
                    return [
                        {
                            id: "1",
                            companyName: "公司名字",
                            beginDate: "2019-7-15",
                            endDate: "2019-7-15",
                            postLevel: "主管级",
                            postType: "团队管理",
                            businessArea: "销售",
                            post: "是",
                            industry: "否",
                        },
                    ];
                },
            },
        },
        created() {
            this.$getDocList(["QUALIFICATION", "YES_NO"]).then((res) => {
                console.log(res);
                this.yesOrNo = res.YES_NO;
            });
            this.jobLevelTreeFun();
            this.getJobClassTreeFun();
            this.getDomainListFun();
        },
        data() {
            return {
                bizDomainOption: [],
                jobClassOption: [],
                jobLevelOption: [],
                yesOrNo: [],
            };
        },
        computed: {
            companyId() {
                return this.$store.state.userInfo.companyId;
            },
        },
        methods: {
            jobLevelTreeFun() {
                jobLevelTree().then((res) => {
                    this.jobLevelOption = this.$util.formatterData(res);
                });
            },
            getJobClassTreeFun() {
                getJobClassTree().then((res) => {
                    this.jobClassOption = this.$util.formatterData(res);
                });
            },
            getDomainListFun() {
                let params = {
                    companyId: this.companyId,
                    rStatus: "Y",
                };
                getDomainList(params).then((res) => {
                    if (res.code == "200") {
                        this.bizDomainOption = res.data;
                    }
                });
            },
            deleteItem(item, index) {
                this.$emit("deleteItem", item, index);
            },
            handleChange(codeArr, type) {
                console.log(codeArr);
            },
            endDateChange(val, rowData) {
                let data = this.$util.deepClone(rowData);
                if (val) {
                    if (!this.$util.compareDate(data.endDate, data.beginDate)) {
                        this.$msg.warning("结束日期需大于开始日期");
                        rowData.endDate = "";
                    }
                }
            },
        },
    };
</script>

<style scoped lang="scss">
    .edu_info_item {
        .item {
            // flex: 1;
            width: 15%;

            &.item2 {
                width: 30%;
            }
            &.short {
                width: 10%;
            }
        }

        .item_icon_wrap {
            text-align: center;
            width: 5%;

            .item_icon {
                cursor: pointer;
            }
        }
    }
    .el-input--prefix .el-input__inner {
        padding-left: 20px;
    }
	.el-input--suffix .el-input__inner{
		padding-right: 20px;
	}
    .el-input__inner{
        // padding:0 5px;
        /*padding-right: 5px;*/
        /*padding-left: 8px;*/
    }
    .el-date-editor .el-range-separator {
        width: auto;
        line-height: 30px;
    }
</style>
