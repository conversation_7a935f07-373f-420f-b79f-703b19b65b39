<template>
    <div class="org_report_main" :class="{ marginB_16: isPdf }">
        <slot></slot>
        <div class="page_second_title">人才继任</div>
        <el-row :gutter="16">
            <el-col :span="col.elSpan" v-for="col in chartDom" :key="col.title">
                <div class="item_title">{{ col.title }}</div>
                <div class="chart_box" :id="col.chartDomId"></div>
            </el-col>
            <el-col :span="24" v-for="list in listArr" :key="list.title">
                <listComp
                    :options="list"
                    :isPdf="isPdf"
                    :enqId="enqId"
                    :orgCode="orgCode"
                />
            </el-col>
        </el-row>
    </div>
</template>
 
<script>
    import { echartsRenderPage } from "../../../../../../../public/js/echartsimg/echartsToImg.js";
    // import {
    //     orgPersonalDeve,
    //     orgTalentDeve,
    //     performanceMatrix,
    //     potentialMatrix,
    // } from "../../../../request/api";
    import {
        getTalentSuccession,
        getPostSuccessionList,
        getTalentSuccessionList,
        getOrgSuccessionList,
    } from "../../../../request/api.js";

    import tableComps from "@/components/talent/tableComps/tableComponent";
    import talentClassifyMatrix from "@/components/talent/common/talentClassifyMatrix";
    import talentMatrix from "@/components/talent/common/talentMatrix";
    import listComp from "./components/listComp.vue";
    export default {
        name: "orgRSuccession",
        props: ["enqId", "orgCode", "isPdf"],
        components: { tableComps, talentClassifyMatrix, talentMatrix, listComp },
        data() {
            return {
                size: 10,
                current: 1,
                kpiRankOption: [],
                competenceRankOptions: [],
                developmentOptions: [],
                developmentCapability: [],
                kpiCapablity: {},
                chartDom: [
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "人才继任整体周期情况",
                        elSpan: 8,
                        chartHeight: "200",
                        chartType: "YBar",
                        dataKey: "getTalentSuccession",
                    },
                    {
                        chartDomId: this.$util.createRandomId(),
                        title: "下级组织人才继任就绪度",
                        elSpan: 16,
                        chartType: "XBar",
                        dataKey: "getTalentSuccessionReady",
                    },
                ],
                listArr: [
                    {
                        title: "组织继任详情",
                        ajaxUrl: getOrgSuccessionList,
                        columns: [
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                            // {
                            //     label: "岗位族群",
                            //     prop: "parentJobClassName",
                            // },
                            // {
                            //     label: "岗位序列",
                            //     prop: "jobClassName",
                            // },
                            // {
                            //     label: "岗位名称",
                            //     prop: "postName",
                            // },
                            {
                                label: "半年以下",
                                prop: "one",
                            },
                            {
                                label: "半年到1年",
                                prop: "two",
                            },
                            {
                                label: "1到3年",
                                prop: "three",
                            },
                            {
                                label: "3到5年",
                                prop: "four",
                            },
                            {
                                label: "5到10年",
                                prop: "five",
                            },
                            {
                                label: "10年以上",
                                prop: "six",
                            },
                        ],
                    },
                    {
                        title: "人才继任详情",
                        ajaxUrl: getTalentSuccessionList,
                        columns: [
                            {
                                label: "姓名",
                                prop: "userName",
                            },
                            {
                                label: "部门",
                                prop: "orgName",
                            },
                             {
                                label: "岗位名称",
                                prop: "postName",
                            },
                            {
                                label: "岗位族群",
                                prop: "parentJobClassName",
                            },
                            {
                                label: "岗位序列",
                                prop: "jobClassName",
                            },
                           
                            {
                                label: "人才类别",
                                prop: "talentType",
                            },
                            {
                                label: "上级姓名",
                                prop: "superiorName",
                            },
                            {
                                label: "预计继任周期",
                                prop: "expectationCycle",
                            },
                        ],
                    },
                ],
            };
        },
        created() {
            // let docList = ["KPI_RANK", "COMPETENCE_RANK", "DEVELOPMENT_POTENTIAL"];
            // this.$getDocList(docList).then((res) => {
            //     this.kpiRankOption = this.$util.deepClone(res.KPI_RANK).reverse();
            //     this.competenceRankOptions = res.COMPETENCE_RANK;
            //     this.developmentOptions = res.DEVELOPMENT_POTENTIAL;
            // });
            // this.getData();
            // this.orgRiskDetailsFn();
            // this.performanceMatrixFn();
            // this.potentialMatrixFn();
        },
        mounted() {
            this.getTalentSuccessionFun();
            // this.getPostSuccessionListFun()
            // this.getTalentSuccessionListFun()
        },
        methods: {
            getTalentSuccessionFun() {
                getTalentSuccession({
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.initChart(res.data);
                    }
                });
            },
            initChart(data) {
                this.chartDom.forEach((chart) => {
                    let chartData = {
                        data: data[chart.dataKey],
                        padding: 155

                    };
                    let w = chart.width || null;
                    let h = chart.height || null;

                    echartsRenderPage(
                        chart.chartDomId,
                        chart.chartType,
                        w,
                        h,
                        chartData
                    );
                });
            },
            getPostSuccessionListFun() {
                let params = {
                    size: this.tablePostData.page.size,
                    current: this.tablePostData.page.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getPostSuccessionList(params).then((res) => {
                    if (res.code == 200) {
                        this.tablePostData.data = res.data;
                        this.tablePostData.page = res.page;
                    }
                });
            },
            handleCurrentPChange(current) {
                this.tablePostData.page.current = current;
                this.getPostSuccessionListFun();
            },
            handleSizePChange(size) {
                this.tablePostData.page.size = size;
                this.getPostSuccessionListFun();
            },

            getTalentSuccessionListFun() {
                let params = {
                    size: this.tableStaffData.page.size,
                    current: this.tableStaffData.page.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                getTalentSuccessionList(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.tableStaffData.data = res.data;
                        this.tableStaffData.page = res.page;
                    }
                });
            },
            handleCurrentTChange(current) {
                this.tableStaffData.page.current = current;
                this.getTalentSuccessionListFun();
            },
            handleSizeTChange(size) {
                this.tableStaffData.page.size = size;
                this.getTalentSuccessionListFun();
            },

            // -----

            getData() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgTalentDeve(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.initChart(res.data);
                    }
                });
            },
            orgRiskDetailsFn() {
                let params = {
                    size: this.size,
                    current: this.current,
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                orgPersonalDeve(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    }
                });
            },
            // 能力绩效矩阵
            performanceMatrixFn() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                performanceMatrix(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.kpiCapablity = res.data;
                    }
                });
            },
            // 能力潜力矩阵
            potentialMatrixFn() {
                let params = {
                    enqId: this.enqId,
                    orgCode: this.orgCode,
                };
                potentialMatrix(params).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        this.developmentCapability = res.data;
                    }
                });
            },
        },
    };
</script>
 
<style scoped lang="scss">
</style>