<template>
  <div class="edu_info_wrap performance_info_main">
    <div class="clearfix">
      <div class="edu_info_center marginT_16">
        <div class="edu_info_header">
          <div class="item">工作内容</div>
          <div class="item">工作频率</div>
          <div class="item">工作次数</div>
          <div class="item">单次工作时长(小时)</div>
          <div class="item">
            <div class="item_div">月度工作</div>
            <div class="item_div time">
              <div>时长</div>
              <div>占比</div>
            </div>
          </div>
        </div>
        <div class="edu_info_mmain">
          <personnelWorkEvaluationSelectList :workData="workData"></personnelWorkEvaluationSelectList>
          <!-- <div class="align_center marginT_30">
            <el-button class="page_confirm_btn" type="primary" @click="prevStep">确定</el-button>
          </div>-->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
import personnelWorkEvaluationSelectList from './personnelWorkEvaluationSelectList.vue'

const props = defineProps({
  workData: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped lang="scss">
.performance_info_main {
  /*width: 60%;*/
}

.edu_info_wrap {
  margin-bottom: 16px;
}
.bgc {
  background-color: #fff;
  // height: 60px;
  margin-bottom: 10px;
  padding: 0;
}
.align_right {
  padding: 0 15px;
}
.edu_info_header {
  margin-bottom: 10px;
  .item {
    // flex: 1;
    width: 25%;
    padding: 0;
    text-align: center;
    .total {
      border: 1px solid #ccc;
      padding: 0 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .item_div {
        // height: 50%;
        // height: 30px;
        // line-height: 30px;
      }
      .time_total {
        color: #449cff;
        font-weight: bold;
      }
    }
    .item_div {
      height: 50%;
      height: 22.5px;
      line-height: 22.5px;
    }
    .time {
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
  }
  .item_icon_wrap {
    text-align: center;
    width: 10%;
  }
}
</style>
