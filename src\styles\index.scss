// 全局样式
:root {
  font-family: 'Source <PERSON>', 'Source <PERSON>', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-size: $--font-size-base;
  color-scheme: light dark;
  color: #3d3d3d;
  background-color: #fff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* 全局样式 */
* {
  box-sizing: border-box;
}
body {
  margin: 0;
  padding: 0;
  font-family: 'Source Han Sans', 'Source Han Sans', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

.el-button {
  border-radius: $--border-radius-base;
}

// 页面标题，带有左侧竖型线
.page-title-line {
  position: relative;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
  line-height: 20px;
  margin-bottom: 20px;
  padding-left: 10px;
  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 5px;
    height: 18px;
    background-image: url('@/assets/imgs/icon-title-line.webp');
    background-size: 100% 100%;
    background-position: center center;
    background-repeat: no-repeat;
    transform: translateY(-50%);
  }
}
// 弹窗位置AI解读
.ai-dialog {
  position: absolute !important;
  right: 20px !important;
}

.marginB_20 {
  margin-bottom: 20px;
}

.main-color {
  color: #1890ff;
}

.flex-box {
  display: flex;
  gap: 15px;
  &.column {
    flex-direction: column;
  }
}

.page-title-icon {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  .text {
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 20px;
  }
}
// 滚动条样式
/*chrome滚动条样式*/
::-webkit-scrollbar {
  /*滚动条整体部分，其中的属性有width,height,background,border（就和一个块级元素一样）等。*/
  width: 7px;
  height: 7px;
}
::-webkit-scrollbar-button {
  /*滚动条两端的按钮。可以用display:none让其不显示，也可以添加背景图片，颜色改变显示效果。*/
  display: none;
}

::-webkit-scrollbar-track-piece {
  /*内层轨道，滚动条中间部分（除去）。*/
  background: #eee;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb {
  /*滚动条里面可以拖动的那部分*/
  background: #ddd;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  /*滚动条里面可以拖动的那部分*/
  background: #ddd;
}
::-webkit-scrollbar-corner {
  /*边角*/
  background: #ddd;
}

.pointer {
  cursor: pointer;
}
