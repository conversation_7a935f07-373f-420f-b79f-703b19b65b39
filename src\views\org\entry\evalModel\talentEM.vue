<script setup>
import SectionTab from "../../components/sectionTab.vue";
const router = useRouter();
const route = useRoute();
const topTabList = ref([
  {
    name: "专业能力（一级）",
    code: 1,
  },
  {
    name: "管理能力（一级）",
    code: 2,
  },
]);
const topTabChekcSign = ref(1);

const sectionTabCheckSign = ref(1);
const sectionTabList = ref([
  {
    name: "【GTMB】销售营销2B",
    code: 1,
  },
  {
    name: "【GTMC】销售营销2C",
    code: 2,
  },
  {
    name: "【PLM】产品全生命周期管理",
    code: 3,
  },
  {
    name: "【SCM-P&O】供应链 计划订单",
    code: 4,
  },
  {
    name: "【SCM-P&S】供应链采购与供应商管理",
    code: 5,
  },
  {
    name: "【SCM-M&PL】供应链制造与厂内物流",
    code: 6,
  },
  {
    name: "【FPD】成套设备完美交付",
    code: 7,
  },
  {
    name: "【E2E-QM】端到端质量管理",
    code: 8,
  },
  {
    name: "【ABC】全业务链成本优化",
    code: 9,
  },
  {
    name: "【DGA】目标落地论证",
    code: 10,
  },
  {
    name: "【S&OP】销售与业务协同",
    code: 11,
  },
  {
    name: "【O&PM】组织与人才管理",
    code: 12,
  },
  {
    name: "【B&FM】预算与财务管理",
    code: 13,
  },
  {
    name: "【PS&D】流程系统与数字化",
    code: 14,
  },
]);

const sectionTab2CheckSign = ref(1);
const sectionTab2List = ref([
  {
    name: "管理计划策略",
    code: 1,
  },
  {
    name: "管理需求计划",
    code: 2,
  },
  {
    name: "管理主计划",
    code: 3,
  },
  {
    name: "管理工序计划",
    code: 4,
  },
  {
    name: "管理物料需求计划",
    code: 5,
  },
  {
    name: "管理库存",
    code: 6,
  },
  {
    name: "管理订单策略",
    code: 7,
  },
  {
    name: "管理订单执行",
    code: 8,
  },
  {
    name: "管理订单全链路",
    code: 9,
  },
]);
const dxwt = ref([
  {
    title: "需求计划盲目性：",
    info: " 未按渠道（直营 / 分销 / 电商）、客户类型（B 端 / C 端）细分需求对象，导致预测与实际订单脱节，旺季断货、淡季积压； ",
  },
  {
    title: "产品分群粗放：",
    info: "未按生命周期（新品 / 成熟品 / 淘汰品）、销量规模（爆款 / 常规款 / 长尾款）制定差异化策略，资源平均分配，爆款缺货率高、长尾品库存周转慢；",
  },
  {
    title: "库存与订单割裂：",
    info: "未建立预测订单与实际库存的冲减规则，促销期订单激增时无法快速拉通库存，或因安全库存设置不合理导致周转成本高企；",
  },
  {
    title: "优先级混乱：",
    info: "紧急订单、战略客户订单与常规订单无明确排序，交付履约率波动大，客户满意度下降；",
  },
  {
    title: "分配策略僵化：",
    info: "未结合产能、物流半径、渠道贡献度动态调整分配方案，引发渠道冲突或资源浪费。",
  },
]);
const gljz = ref([
  {
    title: "需求精准化：",
    info: "通过细分需求计划对象，匹配市场真实需求，预测准确率提升 20%-30%，减少牛鞭效应影响；",
  },
  {
    title: "库存高效化：",
    info: "基于产品分群与库存冲减规则，库存周转率提升 15%-20%，滞销品库存占比下降 10% 以上，释放现金流；",
  },
  {
    title: "资源最优化：",
    info: "明确需求优先级与分配策略，战略客户订单履约率提升至 95% 以上，紧急订单响应周期缩短 30%，增强渠道粘性；",
  },
  {
    title: "决策数据化：",
    info: "建立标准化规则体系，减少人为干预导致的决策偏差，支撑产销平衡、产能规划等中长期战略，降低供应链综合成本 5%-8%；",
  },
  {
    title: "协同高效化：",
    info: "跨部门（市场、生产、物流、销售）基于统一规则协作，缩短计划制定周期，提升供应链敏捷性以应对市场波动。",
  },
]);
const nlxw = ref([
  {
    title: "制定需求计划对象：",
    info: "明确需求计划覆盖的业务单元（如不同渠道、客户等级、产品系列），结合历史销售数据、市场趋势、促销活动等，区分刚性需求（如直营渠道战略客户）与弹性需求（如分销渠道常规订单），为差异化预测提供依据，避免 “一刀切” 式需求管理。 ",
  },
  {
    title: "制定产品分群规则：",
    info: "按产品特性（如销量、毛利、生命周期、市场定位）划分品类（如核心爆款、潜力新品、常规走量款、淘汰尾货），针对不同品类制定专属计划策略（如爆款高库存周转目标、新品试产试销机制、尾货清仓优先级），实现资源聚焦与风险分散。 ",
  },
  {
    title: "制定预测订单库存冲减规则：",
    info: "建立预测订单与实际库存的动态关联机制，明确预测偏差处理规则（如预测超量时自动触发安全库存预警，预测不足时启动紧急补货），确保实际订单执行时库存可快速响应，避免预测与履约 “两张皮”，降低库存冗余或断货风险。",
  },
  {
    title: "制定需求优先级规则：",
    info: "根据订单属性（如客户等级、订单紧急程度、战略匹配度）设定优先级排序标准（如战略客户订单＞促销活动订单＞常规订单，紧急插单需满足最低起订量或附加成本），确保有限资源优先保障高价值订单，提升整体交付效率与客户满意度。 ",
  },
  {
    title: "制定分配策略：",
    info: "结合产能分布、仓储布局、渠道贡献度及物流成本，制定库存分配规则（如区域仓优先满足本地订单、中央仓统筹全国调拨、滞销品优先分配给清仓渠道），平衡各渠道利益，避免因分配不均引发渠道冲突，同时降低运输与仓储成本。",
  },
]);

const topTabChekc = (c) => {
  topTabChekcSign.value = c;
};
const checkSecTab = (c) => {
  sectionTabCheckSign.value = c;
};
const checkSecTab2 = (c) => {
  sectionTab2CheckSign.value = c;
};
</script>
<template>
  <div class="talentEM_wrap">
    <div class="top_tab justify-start">
      <div
        class="item_wrap"
        :class="{ top_tab_act: topTabChekcSign == it.code }"
        v-for="it in topTabList"
        @click="topTabChekc(it.code)"
      >
        <span class="bot_line"></span>
        {{ it.name }}
      </div>
    </div>
    <div class="">
      <SectionTab
        :sectionTabList="sectionTabList"
        :sectionTabCheckSign="sectionTabCheckSign"
        :itemWidth="'13.2%'"
        @checkSecTab="checkSecTab"
      ></SectionTab>
    </div>

    <div class="top_tab">
      <div class="item_wrap top_tab_act">专业能力（二级）</div>
    </div>
    <div class="">
      <SectionTab
        :sectionTabList="sectionTab2List"
        :sectionTabCheckSign="sectionTab2CheckSign"
        :itemWidth="'13.2%'"
        @checkSecTab="checkSecTab2"
      ></SectionTab>
    </div>

    <div class="page-title-line">能力定义</div>
    <div class="section_box_wrap">
      管理计划策略能力，是指供应链体系中，基于市场需求、产品特性、渠道结构及库存目标，系统性制定需求计划、资源分配规则及库存调控策略的核心能力。要求具备数据建模、跨部门协同和动态决策能力，能通过分群管理、优先级排序、库存冲减等规则设计，平衡供需关系，降低供应链成本，提升订单交付效率。核心包括精准定义需求计划对象、科学划分产品品类、合理制定库存与订单匹配规则，确保资源向高价值场景倾斜，支撑产销协同与战略目标落地。
    </div>

    <div class="page-title-line">缺乏该能力的的典型问题</div>
    <div class="section_box_wrap dot_content_wrap">
      <div class="item_wrap" v-for="item in dxwt">
        <span class="icon"></span>
        <span class="title">{{ item.title }}</span>
        <span class="info">{{ item.info }}</span>
      </div>
    </div>
    <div class="page-title-line">提升该能力对管理的价值</div>
    <div class="section_box_wrap dot_content_wrap">
      <div class="item_wrap" v-for="item in gljz">
        <span class="icon"></span>
        <span class="title">{{ item.title }}</span>
        <span class="info">{{ item.info }}</span>
      </div>
    </div>
    <div class="page-title-line">主要的能力行为</div>
    <div class="section_box_wrap dot_content_wrap">
      <div class="item_wrap" v-for="item in nlxw">
        <span class="icon"></span>
        <span class="title">{{ item.title }}</span>
        <span class="info">{{ item.info }}</span>
      </div>
    </div>

    <div class="btn_wrap justify-end">
      <div class="btn">加入评估模块</div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.justify-end {
  display: flex;
  justify-content: flex-end;
}
.talentEM_wrap {
  .top_tab {
    margin-bottom: 20px;
    .item_wrap {
      margin-right: 24px;
      position: relative;
      color: #666666;
      cursor: pointer;
      .bot_line {
        position: absolute;
        bottom: -10px;
        left: 0;
        right: 0;
        margin: 0 auto;
        width: 30px;
        height: 4px;
        background: transparent;
        border-radius: 2px 2px 2px 2px;
      }
    }
    .top_tab_act {
      color: #333;
      font-weight: 600;
      .bot_line {
        background: #40a0ff;
      }
    }
  }
  .btn {
    margin-left: 20px;
    width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    color: #fff;
    background: #40a0ff;
    border-radius: 6px 6px 6px 6px;
    cursor: pointer;
  }
}
</style>
