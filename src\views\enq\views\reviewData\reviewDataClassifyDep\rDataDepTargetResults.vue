<template>
  <div class="job_grade_wrap rDataDep_target_results_wrap bg_write">
    <div class="page_main_title flex_row_betweens">
      <div class="title flex_row_start">
        <p>盘点数据查看</p>
        <div class="check_title" v-if="enqName"><span>/</span>{{ enqName }}</div>
        <div class="check_title"><span>/</span>部门盘点数据-目标与结果</div>
      </div>
      <div class="goback_geader" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section">
      <div class="page_section job_grade_center clearfix">
        <div class="filter_bar_wrap">
          <div class="flex_row_start">
            <div class="filter_item title">筛选</div>
            <div class="filter_item">
              <el-input v-model="userName" placeholder="按姓名模糊查询" clearable></el-input>
            </div>
            <div class="filter_item">
              <el-input v-model="objectiveName" placeholder="按目标名称模糊查询" clearable></el-input>
            </div>
            <div class="filter_item">
              <el-input v-model="resultName" placeholder="按结果名称模糊查询" clearable></el-input>
            </div>
            <div class="filter_item">
              <el-button class="page_add_btn" type="primary" @click="getTargetDataFun">查询</el-button>
            </div>
          </div>
          <div class="filter_item">
            <el-button class="page_add_btn" type="primary" @click="exportData">导出</el-button>
          </div>
        </div>
        <table-component :needIndex="true" :tableData="tableData" :needPagination="false"> </table-component>
      </div>
    </div>
  </div>
</template>

<script>
import tableComponent from '@/components/talent/tableComps/tableComponent'
import { getSuperiorObjective, exportDataConfirm, exportListDownload } from '../../../request/api'
export default {
  name: 'rDataDepTargetResults',
  components: {
    tableComponent
  },

  props: [''],
  data() {
    return {
      enqId: this.$route.query.enqId,
      enqName: this.$route.query.enqName,
      userName: '',
      objectiveName: '',
      resultName: '',
      tableData: {
        columns: [
          {
            label: '一级组织',
            prop: 'oneLevelName',
            fixed: true
          },
          {
            label: '二级组织',
            prop: 'twoLevelName',
            fixed: true
          },
          {
            label: '三级组织',
            prop: 'threeLevelName',
            fixed: true
          },
          {
            label: '四级组织',
            prop: 'fourLevelName',
            fixed: true
          },
          {
            label: '姓名',
            prop: 'userName',
            fixed: true
          },
          {
            label: '岗位',
            prop: 'postName',
            fixed: true
          },
          {
            label: '目标名称',
            prop: 'objectiveName'
          },
          {
            label: '目标权重',
            prop: 'objectiveWeight'
          },
          {
            label: '目标自评',
            prop: 'objectiveSelfScore'
          },
          {
            label: '目标上级评价',
            prop: 'objectiveSupScore'
          },
          {
            label: '目标领导评价',
            prop: 'objectiveLeaderScore'
          },
          {
            label: '目标综合得分',
            prop: 'objectiveOverallScore'
          },
          {
            label: '关键结果',
            prop: 'resultName'
          },
          {
            label: '结果权重',
            prop: 'resultWeight'
          },
          {
            label: '结果自评',
            prop: 'resultSelfScore'
          },
          {
            label: '结果上级评价',
            prop: 'resultSupScore'
          },
          {
            label: '结果领导评价',
            prop: 'resultLeaderScore'
          },
          {
            label: '结果综合得分',
            prop: 'resultOverallScore'
          },
          {
            label: '人员综合得分',
            prop: 'objectiveScore'
          }
        ],
        data: [],
        page: {
          current: 1,
          size: 10,
          total: 0
        }
      }
    }
  },
  mounted() {
    this.getSuperiorObjectiveFun()
  },
  methods: {
    getSuperiorObjectiveFun() {
      getSuperiorObjective({
        enqId: this.enqId,
        userName: this.userName,
        objectiveName: this.objectiveName,
        resultName: this.resultName
      }).then(res => {
        if (res.code == 200) {
          this.tableData.data = res.data
        }
      })
    },
    getTargetDataFun() {
      this.getSuperiorObjectiveFun()
    },
    exportData() {
      this.exportDataConfirmFun()
    },
    exportDataConfirmFun() {
      exportDataConfirm({
        enqId: this.enqId,
        type: 'b'
      }).then(res => {
        this.exportDownloadFun(res)
      })
    },
    exportDownloadFun(res) {
      const blob = new Blob([res])
      const elink = document.createElement('a')
      elink.download = this.enqName + '-部门盘点数据-目标与结果列表.xlsx'
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    }
  }
}
</script>

<style scoped lang="scss"></style>
