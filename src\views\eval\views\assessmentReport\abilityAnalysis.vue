<template>
  <div class="ability_analysis_wrap bg_write">
    <div class="page_main_title">能力分析</div>
    <div class="page_section">
      <div class="page_section_title">评估项目</div>
      <div class="ability_analysis_center clearfix">
        <div class="ability_analysis_aside">
          <div class="page_third_title">评估项目</div>
          <div class="project_assessment_wrap">
            <div
              class="project_assessment_item"
              :class="{ active: evalId == item.evalId }"
              @click="changeProject(item.evalId)"
              v-for="item in projectList"
              :key="item.evalId"
            >
              <div class="project_name">{{ item.evalName }}</div>
              <div class="project_date">
                {{ item.reportGenTime | removeTime }}
              </div>
            </div>
          </div>
        </div>
        <div class="ability_analysis_main">
          <div class="page_third_title">分析主题</div>
          <ul class="analysis_theme_wrap">
            <li class="analysis_theme_item flex_row_between" v-for="(item, index) in anaysTheme">
              <div class="icon el-icon-s-data"></div>
              <div class="theme_name">{{ item.themeName }}</div>
              <div class="introduce">
                {{ item.themeIntroduce }}
              </div>
              <div class="btn">
                <el-button type="primary" plain size="mini" @click="showReport(item.path, index)">查看</el-button>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <coustomPagination :total="total" @pageChange="pageChange"></coustomPagination>
    </div>
  </div>
</template>

<script>
import { evaluationReportList } from '../../request/api'
import coustomPagination from '@/components/talent/paginationComps/coustomPagination.vue'

export default {
  name: 'abilityAnalysis',
  components: { coustomPagination },
  data() {
    return {
      evalId: '',
      total: 0,
      currPage: 1,
      pageSize: 10,
      projectList: [],
      anaysTheme: [
        {
          themeName: '综合得分',
          key: 'talentNumber',
          themeIntroduce: '综合能力得分、各模块能力得分以及能力词典的得分的综合分析',
          path: '/talentAssessment/assessmentReport/synthesisScoreAnalysis'
        },
        {
          themeName: '能力匹配度',
          key: 'talentNumber',
          themeIntroduce: '综合能力与能力匹配度分析',
          path: '/talentAssessment/assessmentReport/abilityMatchingAnalysis'
        },
        {
          themeName: '能力排名',
          key: 'talentNumber',
          themeIntroduce: '综合能力得分、能力匹配度等综合能力的排名分析',
          path: '/talentAssessment/assessmentReport/abilityRankAnalysis'
        },
        {
          themeName: '能力长短板',
          key: 'talentNumber',
          themeIntroduce: '针对核心能力短板进行全方位分析',
          path: '/talentAssessment/assessmentReport/capabilityLengthPlateAnalysis'
        },
        {
          themeName: '评价关系',
          key: 'talentNumber',
          themeIntroduce: '360度人员评价结果',
          path: '/talentAssessment/assessmentReport/evalRelationshipAnalysis'
        },
        {
          themeName: '人才区分',
          key: 'talentNumber',
          themeIntroduce: '通过分析员工的关键业绩指标，测评人员的业绩质量',
          path: '/talentAssessment/assessmentReport/talentDifferentiationAnalysis'
        }
      ]
    }
  },
  created() {
    this.evaluationReportListFun()
  },
  methods: {
    changeProject(id) {
      this.evalId = id
    },
    showReport(path, index) {
      this.$router.push({ path: path, query: { evalId: this.evalId } })
    },
    pageChange(size, current) {
      this.pageSize = size
      this.currPage = current
      this.evaluationReportListFun()
    },
    evaluationReportListFun() {
      let params = {
        current: this.currPage,
        size: this.pageSize
      }
      evaluationReportList(params).then(res => {
        console.log(res)
        if (res.code == '200' && res.data) {
          this.projectList = res.data
          this.evalId = res.data[0].evalId
          this.total = res.total
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.ability_analysis_wrap {
  .ability_analysis_aside {
    float: left;
    width: 350px;
    margin-right: 16px;
    .project_assessment_wrap {
      overflow-y: auto;
      .project_assessment_item {
        display: flex;
        justify-content: space-between;
        padding: 0 16px;
        line-height: 36px;
        border: 1px solid #e5f0f9;
        margin-bottom: 6px;
        cursor: pointer;
        font-weight: bold;
        &.active {
          background: #0099fd;
          color: #fff;
          border-color: #0099fd;
        }
      }
    }
  }
  .ability_analysis_main {
    overflow: hidden;
    .analysis_theme_wrap {
      .analysis_theme_item {
        padding: 10px 16px;
        margin-bottom: 6px;
        border: 1px solid #e5f0f9;

        .icon {
          margin-right: 10px;
          color: #0099fd;
          font-size: 20px;
        }
        .theme_name {
          width: 150px;
          font-weight: bold;
          margin-right: 10px;
        }
        .introduce {
          line-height: 25px;
          width: 400px;
          margin-right: 10px;
        }
        .btn {
          width: 100px;
          text-align: right;
        }
      }
    }
    .pagination_wrap {
      width: 100%;
      .el-pagination__jump {
        margin: 0;
      }
    }
  }
}
</style>
