<template>
  <div class="review_quality_details_wrap">
    <div class="page_main_title flex_row_betweens">
      <div class="title flex_row_start">
        <p>盘点质量</p>
        <div class="check_title" v-if="enqName"><span>/</span>{{ enqName }}</div>
      </div>
      <div class="go_back_btn" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section review_progress_details_center clearfix">
      <tabsChangeData :tabsData="tabsData" :activeName="'department'" :handleClick="tabsClick"></tabsChangeData>
      <div class="department_level_wrap">
        <span>显示部门层级</span>
        <!-- <el-select v-model="departmentLevel" >
                    <el-option label="一级" value="一级"></el-option>
                    <el-option label="二级" value="二级"></el-option>
                </el-select> -->
        <el-select v-model="layerNo" clearable placeholder="请选择" >
          <el-option
            v-for="item in layerNoOption"
            :key="item.codeName"
            :label="item.codeName"
            :value="item.dictCode"
          ></el-option>
        </el-select>
      </div>
      <tableComponent
        :tableData="tableData"
        :needIndex="true"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      >
        <template v-slot:oper v-if="showDepartmentLevel">
          <el-table-column label="整体信息完整度" width="250">
            <template v-slot="scope">
              <div class="completion_rate flex_row_between">
                <div class="bar_wrap">
                  <div
                    class="bar_progress"
                    :class="
                      scope.row.qualityCompletionRate < 50
                        ? 'bg_low'
                        : scope.row.qualityCompletionRate < 70
                          ? 'bg_normal'
                          : scope.row.qualityCompletionRate < 90
                            ? 'bg_middle'
                            : 'bg_high'
                    "
                    :style="{ width: scope.row.qualityCompletionRate + '%' }"
                  ></div>
                </div>
                <div
                  class="completion_rate_num"
                  :class="
                    scope.row.qualityCompletionRate < 50
                      ? 'color_low'
                      : scope.row.qualityCompletionRate < 70
                        ? 'color_normal'
                        : scope.row.qualityCompletionRate < 90
                          ? 'color_middle'
                          : 'color_high'
                  "
                >
                  {{ scope.row.qualityCompletionRate }}%
                </div>
              </div>
            </template>
          </el-table-column>
        </template>
      </tableComponent>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getOrgLayNo, getOrgQuality, getOrgDetails, getUserDetails } from '../../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import tabsChangeData from '@/components/talent/tabsComps/tabsChangeData'

const route = useRoute()
const enqId = route.query.enqId
const enqName = route.query.enqName
const layerNo = ref('')
const layerNoOption = ref([])
const key = ref('department')
const departmentLevel = ref('一级')
const showDepartmentLevel = ref(true)

const tabsData = [
  {
    id: 1,
    label: '部门整体质量',
    name: 'department'
  },
  {
    id: 2,
    label: '部门质量明细',
    name: 'departmentDetails'
  },
  {
    id: 3,
    label: '个人质量明细',
    name: 'personnelDetails'
  }
]

const tableData = reactive({})

const departmentData = reactive({
  columns: [
    {
      label: '部门名称',
      prop: 'orgName'
    },
    {
      label: '部门负责人',
      prop: 'userName',
      className: 'align_center'
    },
    {
      label: '盘点人数',
      prop: 'inventoryNum',
      className: 'align_center'
    },
    {
      label: '完整',
      prop: 'completeNum',
      className: 'align_center completed'
    },
    {
      label: '较完整',
      prop: 'relativelyCompleteNum',
      className: 'align_center incomplete'
    },
    {
      label: '不完整',
      prop: 'incompleteNum',
      className: 'align_center not_login'
    }
  ],
  data: [],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})

const departmentDetailsData = reactive({
  columns: [
    {
      label: '部门名称',
      prop: 'orgName'
    },
    {
      label: '部门负责人',
      prop: 'userName',
      className: 'align_center'
    },
    {
      label: '部门职责分配',
      prop: 'orgResp',
      className: 'align_center'
    },
    {
      label: '部门参与流程',
      prop: 'orgBizProcess',
      className: 'align_center not_login'
    },
    // {
    //     label: "部门岗位信息",
    //     prop: "requiredMaintain",
    //     className: "align_center "
    // },
    {
      label: '岗位参与流程',
      prop: 'postBizProcess',
      className: 'align_center'
    },
    // {
    //     label: "岗位编制与需求",
    //     prop: "maintained",
    //     className: "align_center"
    // },
    {
      label: '人员评价',
      prop: 'evaluation',
      className: 'align_center not_login'
    },
    {
      label: '人员校准',
      prop: 'calibration',
      className: 'align_center not_login'
    },
    {
      label: '培训计划',
      prop: 'training',
      className: 'align_center last_date'
    }
  ],
  data: [],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})

const personnelDetailsData = reactive({
  columns: [
    {
      label: '部门名称',
      prop: 'orgName'
    },
    {
      label: '姓名',
      prop: 'userName',
      className: 'align_center'
    },
    // {
    //     label: "基本信息",
    //     prop: "post",
    //     className: "align_center"
    // },
    {
      label: '工作活动',
      prop: 'activity',
      className: 'align_center'
    },
    {
      label: '教育信息',
      prop: 'education',
      className: 'align_center'
    },
    {
      label: '工作履历',
      prop: 'experience',
      className: 'align_center'
    },
    {
      label: '绩效信息',
      prop: 'kpi',
      className: 'align_center'
    },
    {
      label: '培训信息',
      prop: 'training',
      className: 'align_center'
    },
    {
      label: '获奖信息',
      prop: 'award',
      className: 'align_center'
    },
    {
      label: '个人规划',
      prop: 'development',
      className: 'align_center'
    }
  ],
  data: [],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})

const tabsClick = val => {
  key.value = val
  if (val == 'department') {
    showDepartmentLevel.value = true
    Object.assign(tableData, departmentData)
    getOrgQualityFun()
  } else if (val == 'departmentDetails') {
    showDepartmentLevel.value = false
    Object.assign(tableData, departmentDetailsData)
    getOrgDetailsFun()
  } else if (val == 'personnelDetails') {
    showDepartmentLevel.value = false
    Object.assign(tableData, personnelDetailsData)
    getUserDetailsFun()
  }
}

const handleSizeChange = val => {
  tableData.page.size = val
  if (key.value == 'department') {
    getOrgQualityFun()
  } else if (key.value == 'departmentDetails') {
    getOrgDetailsFun()
  } else if (key.value == 'personnelDetails') {
    getUserDetailsFun()
  }
}

const handleCurrentChange = val => {
  tableData.page.current = val
  if (key.value == 'department') {
    getOrgQualityFun()
  } else if (key.value == 'departmentDetails') {
    getOrgDetailsFun()
  } else if (key.value == 'personnelDetails') {
    getUserDetailsFun()
  }
}

const getOrgQualityFun = async () => {
  const res = await getOrgQuality({
    enqId: enqId,
    current: tableData.page.current,
    size: tableData.page.size,
    layerNo: layerNo.value
  })
  if (res.code == 200) {
    tableData.data = res.data
    tableData.page = res.page
  }
}

const getOrgDetailsFun = async () => {
  const res = await getOrgDetails({
    enqId: enqId,
    current: tableData.page.current,
    size: tableData.page.size,
    layerNo: layerNo.value
  })
  if (res.code == 200) {
    tableData.data = res.data
    tableData.page = res.page
  }
}

const getUserDetailsFun = async () => {
  const res = await getUserDetails({
    enqId: enqId,
    current: tableData.page.current,
    size: tableData.page.size,
    layerNo: layerNo.value
  })
  if (res.code == 200) {
    tableData.data = res.data
    tableData.page = res.page
  }
}

const getOrgLayNoFun = async () => {
  const res = await getOrgLayNo()
  if (res.code == 200) {
    layerNoOption.value = res.data
  }
}

onMounted(() => {
  getOrgLayNoFun()
  Object.assign(tableData, departmentData)
  getOrgQualityFun()
})
</script>

<style scoped lang="scss">
.review_quality_details_wrap {
  .page_main_title {
    .title {
      p {
      }
      .check_title {
        span {
          display: inline-block;
          margin: 0 6px;
        }
      }
    }
    .go_back_btn {
      padding-right: 20px;
      color: #0099FF;
      cursor: pointer;
      font-weight: normal;
      font-size: 14px;
    }
  }
}
.department_level_wrap {
  margin: 8px 0 20px;
  span {
    display: inline-block;
    margin: 0 8px 0 0;
  }
}
.el-table .align_center {
  text-align: center;
  // font-weight: bold;
  &.completed {
    color: #70da88;
  }
  &.incomplete {
    color: #fbb62d;
  }
  &.not_login {
    color: #0099ff;
  }
}

.el-table__body .last_date {
  font-size: 12px;
}
.completion_rate {
  .bar_wrap {
    width: calc(100% - 60px);
    height: 18px;
    background: #ebf4ff;
    position: relative;
    padding-top: 5px;
    .bar_progress {
      background: #70da88;
      height: 8px;
      width: 50%;
      &.bg_high {
        background: #70da88;
      }
      &.bg_middle {
        background: #0099ff;
      }
      &.bg_normal {
        background: #fbb62d;
      }
      &.bg_low {
        background: #ec6941;
      }
    }
  }
  .completion_rate_num {
    font-weight: bold;
    &.not_login {
      color: #ff6d6d;
    }
    &.color_high {
      color: #00b050;
    }
    &.color_middle {
      color: #0099ff;
    }
    &.color_normal {
      color: #fbb62d;
    }
    &.color_low {
      color: #ec6941;
    }
  }
}
</style>
