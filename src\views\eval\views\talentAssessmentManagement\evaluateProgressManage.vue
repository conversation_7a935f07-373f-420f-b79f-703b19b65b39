<template>
    <div class="assess_progress_manage_info_wrap bg_write" v-if="showPage">
        <div class="page_main_title">
            人员评价进度管理--{{ enqName }}
            <div class="goback_geader" @click="$util.goback()">
                <i class="el-icon-arrow-left"></i>返回
            </div>
        </div>
        <div class="page_section">
            <!-- <tabsChangeData
                    :tabsData="tabsData"
                    :activeName="tabsData[tabIndex].name"
                     :handleClick="changeTabs">
            </tabsChangeData> -->
            <div class="department_level_wrap">
                <span class="select_title">部门：</span>
       
                <el-cascader
                    class="filter_item"
                    :options="orgTree"
                    v-model="orgCode"
                    placeholder="请选择"
                    :change-on-select="true"
                    :show-all-levels="false"
                    :props="{
                        label: 'name',
                        value: 'code',
                        expandTrigger: 'hover',
                    }"
                    @change="(val) => orgTreeClick(val)"
                    clearable
                >
                </el-cascader>
                <span class="select_title">人员：</span>
                <el-input
                    class="filter_item"
                    v-model="userName"
                    placeholder="请输入人员名称"
                ></el-input>
                <span class="select_title">状态：</span>
                <el-select
                    class="filter_item marginR_16"
                    v-model="status"
                    clearable
                    placeholder="请选择"
                >
                    <el-option
                        v-for="item in statusOption"
                        :key="item.dictCode"
                        :label="item.codeName"
                        :value="item.dictCode"
                    >
                    </el-option>
                </el-select>
                <el-button
                    @click="searchFun"
                    type="primary"
                    
                    >查询</el-button
                >
                <el-button
                    @click="exportExcel"
                    type="primary"
                    
                    >导出</el-button
                >
            </div>
            <tableComponent
                v-if="tableData.data.length > 0"
                :tableData="tableData"
                :needIndex="true"
                @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange"
            >
                <template v-slot:oper>
                    <el-table-column label="完成率" width="150px">
                        <template slot-scope="scope">
                            <div class="completion_rate flex_row_between">
                                <div class="bar_wrap">
                                    <div
                                        class="bar_progress"
                                        :class="
                                            scope.row.completePercentage < 50
                                                ? 'bg_low'
                                                : scope.row.completePercentage <
                                                  70
                                                ? 'bg_normal'
                                                : scope.row.completePercentage <
                                                  90
                                                ? 'bg_middle'
                                                : 'bg_high'
                                        "
                                        :style="{
                                            width:
                                                scope.row.completePercentage +
                                                '%',
                                        }"
                                    ></div>
                                </div>
                                <div
                                    class="completion_rate_num"
                                    :class="
                                        scope.row.completePercentage < 50
                                            ? 'color_low'
                                            : scope.row.completePercentage < 70
                                            ? 'color_normal'
                                            : scope.row.completePercentage < 90
                                            ? 'color_middle'
                                            : 'color_high'
                                    "
                                >
                                    {{ scope.row.completePercentage }}%
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                </template>

                <template
                    v-slot:oper2
                    v-if="evalStatus == 0 || evalStatus == 1 || evalStatus == 2"
                >
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <div class="btn_group">
                                <el-button
                                    v-if="scope.row.evalUserStatus == '9'"
                                    type="primary"
                                    class=""
                                    size="mini"
                                    @click="backEval(scope.row)"
                                    >退回</el-button
                                >
                                <el-button
                                    v-if="scope.row.evalUserStatus != 'X'"
                                    type="primary"
                                    class=""
                                    size="mini"
                                    @click="abolishEval(scope.row)"
                                    >作废</el-button
                                >
                            </div>
                        </template>
                    </el-table-column>
                </template>
            </tableComponent>
        </div>
    </div>
</template>

<script>
    import tabsChangeData from "@/components/talent/tabsComps/tabsChangeData";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import {
        getUserSchedule,
        userReturn,
        userInvalid,
        getEvalInfo,
        scheduleTree,
        exportData,
    } from "../../request/api";
    import {exportDownload} from "@/views/entp/request/api"

    export default {
        name: "evaluateProgressManage",
        components: {
            tabsChangeData,
            tableComponent,
        },
        data() {
            return {
                showPage: true,
                evalId: this.$route.query.evalId,
                enqName: "",
                evalStatus: null,
                orgTree:[],
                orgCode: null,
                userName: "",
                status: "",
                statusOption: [],
                pageCurrent: 1,
                pageSize: 10,
                tableData: {
                    columns: [
                        {
                            label: "部门名称",
                            prop: "orgName",
                            width: 120,
                        },
                        {
                            label: "姓名",
                            prop: "userName",
                            width: 80,
                            className: "align_center",
                        },
                        {
                            label: "岗位",
                            prop: "postName",
                            // width: 120,
                            className: "align_center",
                        },
                        {
                            label: "状态",
                            prop: "evalUserStatusText",
                            width: 70,
                            className: "align_center",
                        },
                        {
                            label: "需评价",
                            prop: "totalItemCount",
                            width: 70,
                            className: "align_center incomplete",
                        },
                        {
                            label: "已评价",
                            prop: "submitCount",
                            width: 70,
                            className: "align_center completed",
                        },
                        {
                            label: "未评价",
                            prop: "unSubmitCount",
                            width: 70,
                            className: "align_center not_login",
                        },
                        {
                            label: "用时",
                            prop: "elapsedTime",
                            width: 70,
                            className: "align_center",
                        },
                        {
                            label: "最后登录时间",
                            prop: "lastLoginTime",
                            // width: 120,
                            className: "align_center last_date",
                        },
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        created() {
            //获取测评状态
            getEvalInfo({
                evalId: this.evalId,
            }).then((res) => {
                console.log(res);
                this.evalStatus = res.evalStatus;
                this.enqName = res.enqName;
            });
            this.$getDocList(["EVAL_USER_STATUS"]).then((res) => {
                console.log(res);
                this.statusOption = res.EVAL_USER_STATUS;
            });
            this.getUserScheduleFun();
            this.scheduleTreeFun();
        },
        methods: {
            //切换页容量
            handleSizeChange(val) {
                this.pageSize = val;
                this.getUserScheduleFun();
            },
            //分页
            handleCurrentChange(val) {
                this.pageCurrent = val;
                this.getUserScheduleFun();
            },
            searchFun(){
                this.pageCurrent = 1;
                this.getUserScheduleFun();
            },
            getUserScheduleFun() {
                getUserSchedule({
                    evalId: this.evalId,
                    status: this.status,
                    orgCode: this.orgCode,
                    userName: this.userName,
                    current: this.pageCurrent,
                    size: this.pageSize,
                }).then((res) => {
                    console.log(res);
                    if (res.code == 200) {
                        // this.tableData = res.data;
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                    } else {
                        this.$msg.warning(res.msg);
                    }
                });
            },
            backEval(row) {
                this.$confirm("确认退回吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.$confirm("是否清除答题？", "提示", {
                            confirmButtonText: "是",
                            cancelButtonText: "否",
                            type: "warning",
                        })
                            .then(() => {
                                userReturn({
                                    evalId: this.evalId,
                                    userId: row.userId,
                                    isClear: "true",
                                }).then((res) => {
                                    console.log(res);
                                    if (res.code == 200) {
                                        this.getUserScheduleFun(this.tabIndex);
                                    }
                                });
                            })
                            .catch(() => {
                                userReturn({
                                    evalId: this.evalId,
                                    userId: row.userId,
                                    isClear: "false",
                                }).then((res) => {
                                    console.log(res);
                                    if (res.code == 200) {
                                        this.getUserScheduleFun(this.tabIndex);
                                    }
                                });
                            });
                    })
                    .catch(() => {});
            },
            abolishEval(row) {
                this.$confirm("确认作废吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        userInvalid({
                            evalId: this.evalId,
                            userId: row.userId,
                        }).then((res) => {
                            console.log(res);
                            if (res.code == 200) {
                                this.getUserScheduleFun(this.tabIndex);
                            }
                        });
                    })
                    .catch(() => {});
            },
            scheduleTreeFun() {
                scheduleTree({ evalId: this.evalId}).then(
                    (res) => {
                        console.log(res);
                        this.orgTree = this.$util.formatterData(res);
                    }
                );
            },
            orgTreeClick(val){
                console.log(val);
                this.orgCode = val[val.length-1];
                console.log(this.orgCode);
            },
            exportExcel(){
                let params = {
                    evalId:this.evalId,
                    orgCode:this.orgCode,
                    status:this.status,
                    userName:this.userName
                }
                exportData(params).then(res => {
                    console.log(res);
                    if(res.code == 200){
                        this.exportDownloadFun(res.data)
                    }else{
                        this.$msg.warning(res.msg)
                    }
                })
            },
            exportDownloadFun(val){
            exportDownload({
                fileName:val
            }).then(res=>{
                const blob = new Blob([res])
                const elink = document.createElement('a')
                elink.download = this.enqName+"--测评进度.xlsx";
                elink.style.display = 'none'
                elink.href = URL.createObjectURL(blob)
                document.body.appendChild(elink)
                elink.click()
                URL.revokeObjectURL(elink.href) // 释放URL 对象
                document.body.removeChild(elink)
            })
        },
        },
    };
</script>

<style scoped lang="scss">
    .assess_progress_manage_info_wrap {
        .department_level_wrap {
            margin: 10px 0 20px 0;

            .select_title {
                display: inline-block;
                width: 60px;
                text-align: center;
            }
            .filter_item {
                width: auto;
            }

             .el-input__inner {
                height: 35px;
                line-height: 35px;
            }

             .el-input__suffix {
                display: flex;
                align-items: center;
            }
        }

         .el-table {
            margin: 10px 0 0 0;

            .has-gutter {
                tr th {
                    background: #f2f8ff;
                }
            }

            .align_center {
                text-align: center;
                /*font-weight: bold;*/

                &.completed {
                    color: #00b050;
                }

                &.incomplete {
                    color: #ffc000;
                }

                &.not_login {
                    color: #00b0f0;
                }

                &.status {
                }
            }
        }

         .el-table__body .last_date {
            font-size: 12px;
        }

        .completion_rate {
            .bar_wrap {
                width: calc(100% - 40px);
                height: 18px;
                background: #EBF4FF;
                position: relative;
                padding-top: 5px;

                .bar_progress {
                    background: #00b050;
                    height: 8px;
                    width: 50%;

                    &.bg_high {
                        background: #00b050;
                    }

                    &.bg_middle {
                        background: #00b0f0;
                    }

                    &.bg_normal {
                        background: #ffc000;
                    }

                    &.bg_low {
                        background: #ff8181;
                    }
                }
            }

            .completion_rate_num {
                font-weight: bold;

                &.not_login {
                    color: #ff6d6d;
                }

                &.color_high {
                    color: #00b050;
                }

                &.color_middle {
                    color: #00b0f0;
                }

                &.color_normal {
                    color: #ffc000;
                }

                &.color_low {
                    color: #ff8181;
                }
            }
        }
    }
</style>