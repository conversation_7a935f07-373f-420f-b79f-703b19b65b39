<script setup>
defineOptions({ name: 'overview' })
const emits = defineEmits(['next'])
const route = useRoute()
const showModule = computed(() => {
  return route.path == '/AI/displayer/overview/module'
})
const activeIndex = ref('3')
const coreList = ref([
  {
    title: '【GTMB】销售营销2B'
  },
  {
    title: '【GTMC】销售营销2C'
  },
  {
    title: '【PLM】产品全生命周期管理'
  },
  {
    title: '【SCM-P&O】供应链 计划订单'
  },
  {
    title: '【SCM-P&S】供应链采购与供应商管理'
  },
  {
    title: '【SCM-M&PL】供应链制造与厂内物流'
  },
  {
    title: '【FPD】成套设备完美交付'
  },
  {
    title: '【E2E-QM】端到端质量管理'
  },
  {
    title: '【ABC】全业务链成本优化'
  },
  {
    title: '【DGA】目标落地论证'
  },
  {
    title: '【S&OP】销售与业务协同'
  },
  {
    title: '【O&PM】组织与人才管理'
  },
  {
    title: '【B&FM】预算与财务管理'
  },
  {
    title: '【PS&D】流程系统与数字化'
  }
])
const router = useRouter()
const activeAbility = ref([
  '【SCM-P&O】供应链计划订单',
  '管理计划策略',
  '管理需求计划策略',
  '制定需求计划对象',
  '流程赋能'
])
const AbilityList = ref([
  {
    title: '能力板块',
    data: [{ name: '【SCM-P&O】供应链计划订单', score: 48 }]
  },
  {
    title: '能力模块',
    data: [
      {
        name: '管理计划策略',
        score: 45.9
      },
      {
        name: '管理需求计划',
        score: 45.3
      },
      {
        name: '管理主计划',
        score: 45.7
      },
      {
        name: '管理工序计划',
        score: 45.0
      },
      {
        name: '管理物料需求计划',
        score: 45.8
      },
      {
        name: '管理库存',
        score: 45.6
      },
      {
        name: '管理订单策略',
        score: 45.9
      },
      {
        name: '管理订单执行',
        score: 45.4
      },
      {
        name: '管理订单全链路',
        score: 45.3
      }
    ]
  },
  {
    title: '能力组件群',
    data: [
      {
        name: '管理需求计划策略',
        score: 46.2
      },
      {
        name: '管理物料需求计划策略',
        score: 45.5
      }
    ]
  },
  {
    title: '能力组件',
    data: [
      {
        name: '制定需求计划对象',
        score: 45.4
      },
      {
        name: '制定产品分群规则',
        score: 46.1
      },
      {
        name: '制定预测订单库存冲减规则',
        score: 46.1
      },
      {
        name: '制定需求优先级规则',
        score: 46.0
      },
      {
        name: '制定分配策略',
        score: 47.5
      }
    ]
  },
  {
    title: '能力解码类型',
    data: [
      {
        name: '流程赋能',
        score: 56.8
      },
      {
        name: '组织赋能',
        score: 56.5
      },
      {
        name: '人岗赋能',
        score: 56.0
      },
      {
        name: '数字化赋能',
        score: 30.0
      },
      {
        name: 'AI赋能',
        score: 31.4
      }
    ]
  }
])

const onDecode = item => {
  router.push({ path: '/AI/analysis/decode/detail', query: { name: item.name } })
}
</script>
<template>
  <div v-show="true">
    <div class="text-3 font-bold color-[#3d3d3d] mb-[12px]">点击要解码的能力 ：</div>
    <div class="core-list">
      <div
        class="list"
        :class="{ active: activeIndex == index }"
        v-for="(list, index) in coreList"
        @click="activeIndex = index"
      >
        {{ list.title }}
      </div>
    </div>
  </div>
  <div class="page-title-line">查看能力解码 ：</div>
  <div class="ability-chart">
    <div class="column" v-for="(col, index) in AbilityList">
      <div class="column-title">{{ col.title }}</div>
      <div class="column-content">
        <div class="column-item" :class="{ active: activeAbility.includes(item.name) }" v-for="item in col.data">
          <div class="line"></div>
          <div class="decode-btn" v-if="index > 1" @click="onDecode(item)">解码</div>
          <div class="item-name">{{ item.name }}</div>
          <div class="item-score">
            <div class="bar" :style="{ width: item.score + '%' }"></div>
            <div class="score">{{ item.score }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.core-list {
  display: flex;
  flex-flow: row wrap;
  gap: 10px;
  margin-bottom: 30px;
  .list {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333333;
    width: calc((100% - 60px) / 7);
    height: 35px;
    padding: 0px 8px;
    border-radius: 5px 5px 5px 5px;
    line-height: 18px;
    background: linear-gradient(347deg, #e2f3ff 0%, rgba(237, 248, 255, 0.1) 99%);
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #c6dbf3;
    text-align: center;
    cursor: pointer;
    &:hover,
    &.active {
      border: 1px solid #53acff;
      color: #40a0ff;
      box-shadow: 0px 0px 10px 0px rgba(124, 182, 237, 0.5);
    }
  }
}

.ability-chart {
  display: flex;
  align-items: stretch;
  justify-content: flex-start;
  gap: 13px;
  .column {
    width: calc((100% - 52px) / 5);
    background-color: #fff;
    border-radius: 8px 8px 8px 8px;
    padding: 9px;
    &:first-child {
      .line {
        display: none;
      }
      .column-item {
        &.active {
          border: 2px solid #c7e1fa !important;
        }
        &::before {
          display: none;
        }
      }
    }
    &:last-child {
      .column-item {
        &::after {
          display: none;
        }
      }
    }
    .column-title {
      font-size: 14px;
      color: #333333;
      line-height: 40px;
      background: #d9ecff;
      border-radius: 8px 8px 8px 8px;
      text-align: center;
      margin-bottom: 28px;
      font-weight: bold;
    }
    .column-content {
      height: calc(100% - 70px);
      display: flex;
      flex-flow: column;
      align-items: center;
      // justify-content: center;
      gap: 14px;
      .column-item {
        position: relative;
        width: 100%;
        padding: 16px;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 2px solid #c7e1fa;
        z-index: 2;
        &::before {
          content: '';
          position: absolute;
          top: 50%;
          right: calc(100% + 2px);
          width: 16px;
          height: 2px;
          background-color: #cae5ff;
          z-index: 1;
        }

        &.active {
          border-color: #40a0ff;
          &::after {
            content: '';
            position: absolute;
            left: calc(100% + 2px);
            top: 50%;
            width: 16px;
            height: 2px;
            background-color: #cae5ff;
            z-index: 1;
          }
          .decode-btn {
            display: block;
            cursor: pointer;
          }
        }
        &:last-child {
          .line {
            bottom: 50%;
            top: inherit;
          }
        }
        &:first-child {
          .line {
            top: 50%;
          }
        }
        .line {
          position: absolute;
          top: -7px;
          left: -18px;
          height: calc(100% + 18px);
          width: 2px;
          background-color: #cae5ff;
          z-index: 1;
        }
        .decode-btn {
          display: none;
          position: absolute;
          right: 8px;
          top: 8px;
          width: 50px;
          line-height: 20px;
          background: #d9ecff;
          border-radius: 5px 5px 5px 5px;
          text-align: center;
          font-size: 14px;
          color: #40a0ff;
        }
        .item-name {
          font-weight: 500;
          font-size: 16px;
          color: #3d3d3d;
          line-height: 20px;
          margin-bottom: 20px;
          cursor: pointer;
        }
        .item-score {
          width: calc(100% - 30px);
          position: relative;
          background: #e9edf0;
          height: 12px;
          border-radius: 12px;
          .bar {
            height: 100%;
            top: 0;
            left: 0;
            background: #8fecf6;
            border-radius: 12px;
          }
          .score {
            position: absolute;
            left: calc(100% + 10px);
            top: 0;
            font-weight: 500;
            font-size: 16px;
            color: #40a0ff;
            margin-top: -6px;
          }
        }
      }
    }
  }
}
</style>
