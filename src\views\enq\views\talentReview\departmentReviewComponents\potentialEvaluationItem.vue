<template>
  <div>
    <div class="edu_info_item" v-for="(item, index) in eduInfoData" :key="item.id || index">
      <div class="item item_icon_wrap">{{ index + 1 }}</div>
      <div class="item item_icon_wrap">{{ item.userName }}</div>
      <div class="item item_icon_wrap">{{ item.QLPGWS }}</div>
      <div class="item item_icon_wrap">{{ item.QLPZZG }}</div>
      <div class="item item_icon_wrap">{{ item.QLPFZL }}</div>
      <div class="item item_icon_wrap">{{ item.QLPJSK }}</div>
      <div class="item item_icon_wrap">{{ item.QLPJZG }}</div>
      <el-select class="item" v-model="item.actualPotentialGrade" placeholder="请选择">
        <el-option
          v-for="option in qualificationOptions"
          :label="option.codeName"
          :value="option.dictCode"
          :key="option.dictCode"
        ></el-option>
      </el-select>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/modules/user'
const userStore = useUserStore()
const props = defineProps({
  eduInfoData: {
    type: Array,
    default: () => [{}]
  }
})

const qualificationOptions = ref([])

onMounted(() => {
  userStore.getDocList(['ACTUAL_GRADE']).then(res => {
    qualificationOptions.value = res.ACTUAL_GRADE
  })
})
</script>

<style scoped lang="scss">
.edu_info_item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  padding: 8px 16px;

  .item {
    width: 13%;
  }

  .item_icon_wrap {
    text-align: center;
    width: 12%;

    .item_icon {
      font-size: 20px;
      color: #0099fd;
      cursor: pointer;
    }
  }
}
</style>
