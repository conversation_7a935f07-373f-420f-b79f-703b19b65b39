<template>
  <div>
    <el-dialog title="业务流程编辑" v-model="dialogVisible" :close-on-click-modal="false" @close="cancel">
      <el-form ref="formRef" :model="form" label-width="80px">
        <el-form-item label="流程编码">
          <el-input v-model="form.processCode" />
        </el-form-item>
        <el-form-item label="流程名称">
          <el-input v-model="form.processName" />
        </el-form-item>
        <el-form-item label="流程描述">
          <el-input type="textarea" v-model="form.processdesc" :autosize="{ minRows: 2, maxRows: 4 }" />
        </el-form-item>
        <el-form-item label="层级">
          <el-select v-model="form.level" placeholder="请选择层级">
            <el-option label="区域一" value="shanghai" />
            <el-option label="区域二" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="上级流程">
          <el-input v-model="form.supProcess" />
        </el-form-item>
        <el-form-item label="前置流程">
          <el-input v-model="form.prevProcess" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'

// Props
const props = defineProps({
  dialogShow: {
    type: Boolean,
    default: false
  },
  businessData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:dialogShow', 'submit', 'cancel'])

// Refs
const formRef = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.dialogShow,
  set: value => emit('update:dialogShow', value)
})

// 响应式状态
const form = reactive({
  processCode: '',
  processName: '',
  processdesc: '',
  level: '',
  supProcess: '',
  prevProcess: ''
})

// 方法
const confirm = () => {
  emit('submit', form)
}

const cancel = () => {
  emit('cancel')
}

// 监听业务数据变化
watch(
  () => props.businessData,
  newVal => {
    if (newVal) {
      Object.keys(form).forEach(key => {
        form[key] = newVal[key] || ''
      })
    }
  },
  { deep: true }
)
</script>

<style scoped>
.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style>
