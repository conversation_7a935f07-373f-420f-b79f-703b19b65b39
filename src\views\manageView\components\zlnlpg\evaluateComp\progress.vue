<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'
defineOptions({ name: 'progressCom' })
const percentage = ref(100) // 进度百分比
const radius = 45 // SVG圆环半径
const circumference = 2 * Math.PI * radius // 圆周长计算

const countList = ref([
  {
    label: '参评人数',
    num: 21
  },
  {
    label: '已完成',
    num: 21
  },
  {
    label: '进行中',
    num: 0
  },
  {
    label: '未开始',
    num: 0
  }
])
const type = ref('org') // 组织/部门
const changeType = val => {
  type.value = val
}
const orgColumns = ref([
  {
    label: '序号',
    type: 'index',
    width: 50
  },
  {
    label: '一级组织',
    prop: 'orgName'
  },
  {
    label: '二级组织',
    prop: 'orgName2'
  },
  {
    label: '三级组织',
    prop: 'orgName3'
  },
  {
    label: '四级组织',
    prop: 'orgName4'
  },
  {
    label: '部门负责人',
    prop: 'leader',
    width: 100
  },
  {
    label: '参评人数',
    prop: 'evalNum'
  },
  {
    label: '已完成',
    prop: 'completed',
    width: 60
  },
  {
    label: '未完成',
    prop: 'unfinished',
    width: 60
  },
  {
    label: '未开始',
    prop: 'notStart',
    width: 60
  }
])
const orgData = ref([
  {
    orgName: 'H公司',
    orgName2: '',
    orgName3: '供应链计划管理',
    orgName4: '',
    leader: '',
    evalNum: 21,
    completed: 21,
    unfinished: 0,
    notStart: 0,
    rate: 100
  }
])

const personalColumns = ref([
  {
    label: '序号',
    type: 'index',
    width: 50
  },
  {
    label: '部门',
    prop: 'orgName'
  },
  {
    label: '姓名',
    prop: 'userName'
  },
  {
    label: '岗位',
    prop: 'postName'
  },
  {
    label: '状态',
    prop: 'status'
  },
  {
    label: '需评价',
    prop: 'appraiseNum',
    width: 60
  },
  {
    label: '已评价',
    prop: 'appraised',
    width: 60
  },
  {
    label: '未评价',
    prop: 'unappraise',
    width: 60
  },
  {
    label: '最后登录时间',
    prop: 'lastDate'
  }
])
const personalData = ref([
  {
    orgName: '供应链计划管理部',
    userName: '王海峰',
    postName: '计划主计划部经理',
    status: '已完成',
    appraiseNum: 39,
    appraised: 0,
    unappraise: 0,
    lastDate: '2024-09-25 10:00:00',
    rate: 100
  },
  {
    orgName: '供应链计划管理部',
    userName: '王海峰',
    postName: '计划主计划部经理',
    status: '已完成',
    appraiseNum: 39,
    appraised: 0,
    unappraise: 0,
    lastDate: '2024-09-25 10:00:00',
    rate: 100
  },
  {
    orgName: '供应链计划管理部',
    userName: '王海峰',
    postName: '计划主计划部经理',
    status: '已完成',
    appraiseNum: 39,
    appraised: 0,
    unappraise: 0,
    lastDate: '2024-09-25 10:00:00',
    rate: 100
  }
])
</script>

<template>
  <div class="progress-wrap">
    <div class="progress-title">战略绩效闭环运营—多人测评进度</div>
    <div class="progress-content">
      <div class="content-main">
        <div class="progress-head">
          <div class="eval-info">
            <div class="name">计划订单管理测评 <span class="type">整体能力诊断</span></div>
            <div class="info">
              <div class="date">2024-09-25 ~ 2024-09-26</div>
              <div class="auth">H公司管理员</div>
              <div class="account">hxadmin</div>
            </div>
          </div>
          <div class="percentage">
            <svg class="progress-ring" width="74" height="74" viewBox="0 0 100 100">
              <!-- 背景环 -->
              <circle
                class="ring-bg"
                cx="50"
                cy="50"
                :r="radius"
                fill="transparent"
                stroke-width="10"
                stroke="#e6e6e6"
              />
              <!-- 进度环 -->
              <circle
                class="ring-progress"
                cx="50"
                cy="50"
                :r="radius"
                fill="transparent"
                stroke-width="10"
                stroke="#409eff"
                :stroke-dasharray="circumference"
                :stroke-dashoffset="circumference * (1 - percentage / 100)"
              />
              <!-- 中心文字 -->
              <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" class="ring-text">
                {{ percentage }}%
              </text>
            </svg>
          </div>
          <div class="progress-count">
            <div class="count-item" v-for="item in countList" :key="item.label">
              <div class="label">{{ item.label }}</div>
              <div class="num">
                <b>{{ item.num }}</b
                >人
              </div>
            </div>
          </div>
        </div>
        <div class="progress-main">
          <div class="tab-wrap">
            <div class="tab-item" :class="{ active: type == 'org' }" @click="changeType('org')">部门进度</div>
            <div class="tab-item" :class="{ active: type == 'personal' }" @click="changeType('personal')">个人进度</div>
          </div>
          <div class="table-wrap">
            <div v-show="type == 'org'">
              <div class="btn-wrap marginB_20">
                <div class="btn">部门进度统计</div>
              </div>
              <simplenessTable :columns="orgColumns" :data="orgData">
                <template v-slot>
                  <el-table-column prop="oper" label="完成率" width="100px">
                    <template v-slot="scope">
                      <div class="rate-wrap">
                        <div class="rate-bar">
                          <div class="bar" :style="{ width: scope.row.rate + '%' }"></div>
                        </div>
                        <div class="rate-text">{{ scope.row.rate + '%' }}</div>
                      </div>
                    </template>
                  </el-table-column>
                </template>
              </simplenessTable>
            </div>
            <div v-show="type == 'personal'">
              <div class="filter-wrap marginB_20">
                <div class="filter-item">
                  <div class="filter-item-label">部门</div>
                  <div class="filter-item-content">
                    <el-select v-model="filterValue" placeholder="请选择">
                      <!-- <el-option label="全部" value="all"></el-option>
                      <el-option label="已完成" value="completed"></el-option>
                      <el-option label="未完成" value="uncompleted"></el-option> -->
                    </el-select>
                  </div>
                </div>
                <div class="filter-item">
                  <div class="filter-item-label">人员</div>
                  <div class="filter-item-content">
                    <ElInput placeholder="请输入"></ElInput>
                  </div>
                </div>
                <div class="filter-item">
                  <div class="filter-item-label">状态</div>
                  <div class="filter-item-content">
                    <el-select v-model="filterValue" placeholder="请选择">
                      <el-option label="全部" value="all"></el-option>
                      <el-option label="已完成" value="completed"></el-option>
                      <el-option label="未完成" value="uncompleted"></el-option>
                    </el-select>
                  </div>
                </div>
                <div class="filter-item">
                  <div class="btn-wrap">
                    <div class="btn">查询</div>
                    <div class="btn">导出</div>
                  </div>
                </div>
              </div>
              <simplenessTable :columns="personalColumns" :data="personalData">
                <template v-slot>
                  <el-table-column prop="oper" label="完成率" width="100px">
                    <template v-slot="scope">
                      <div class="rate-wrap">
                        <div class="rate-bar">
                          <div class="bar" :style="{ width: scope.row.rate + '%' }"></div>
                        </div>
                        <div class="rate-text">{{ scope.row.rate + '%' }}</div>
                      </div>
                    </template>
                  </el-table-column>
                </template>
              </simplenessTable>
            </div>
          </div>
        </div>
      </div>
      <div class="aside">
        <div class="head">还可继续邀请 <b>10人</b></div>
        <div class="aside-content">
          <div class="href">
            <div class="title">测评链接：</div>
            <div class="text">https://ceping.xiyiqq.com/710350</div>
          </div>
          <div class="btn">一键复制</div>
          <div class="qrcode">二维码</div>
          <div class="btn">下载</div>
          <div class="tip">您可以复制链接或下载二维码，分享至微信群</div>
          <div class="btn w-auto">下载测评进度信息，督促相关人员加快进度</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.progress-wrap {
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
  padding: 20px;
  .progress-title {
    font-weight: 500;
    font-size: 16px;
    color: #53a9f9;
    line-height: 16px;
    margin-bottom: 20px;
  }
}
.progress-content {
  display: flex;
  align-items: stretch;
  .content-main {
    flex: 1;
    .progress-head {
      display: flex;
      align-items: center;
      padding: 14px 26px 10px;
      background: #ebf5ff;
      border-radius: 5px 5px 5px 5px;
      .eval-info {
        .name {
          font-weight: 600;
          font-size: 16px;
          color: #3d3d3d;
          line-height: 20px;
          margin-bottom: 10px;
          .type {
            line-height: 17px;
            color: #fff;
            font-size: 12px;
            padding: 4px 8px;
            background: #40a0ff;
            border-radius: 17px;
            font-weight: normal;
          }
        }
        .info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 10px;
          font-weight: 400;
          font-size: 14px;
          color: #acacac;
        }
      }
    }
    .progress-main {
      padding-top: 10px;
      .tab-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 20px;
        margin-bottom: 13px;
        border-bottom: 2px solid #d8d8d8;
        .tab-item {
          padding-top: 14px;
          padding-bottom: 14px;
          border-bottom: 4px solid transparent;
          margin-bottom: -1px;
          cursor: pointer;
          &.active {
            border-bottom: 4px solid #40a0ff;
            color: #40a0ff;
          }
        }
      }
      .table-wrap {
        .btn-wrap {
          display: flex;
          gap: 20px;
          .btn {
            min-width: 92px;
            line-height: 30px;
            background: #40a0ff;
            border-radius: 3px 3px;
            font-size: 12px;
            color: #ffffff;
            text-align: center;
          }
        }
      }

      .rate-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .rate-bar {
          flex: 0 0 60px;
          position: relative;
          width: 100px;
          height: 7px;
          background: #d8d8d8;
          border-radius: 7px;
          .bar {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            border-radius: 7px;
            background: #40a0ff;
          }
          .rate-text {
            font-size: 12px;
            color: #3d3d3d;
          }
        }
      }
    }
  }
}
.progress-head {
  display: flex;
}
.progress-count {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  text-align: center;
  .count-item {
    .label {
      line-height: 24px;
      background: #cbe4fd;
      border-radius: 42px;
      font-weight: 400;
      font-size: 14px;
      color: #40a0ff;
      padding: 0 12px;
      margin-bottom: 17px;
    }
    .num {
      font-size: 12px;
      color: #3d3d3d;
      b {
        font-weight: 500;
        font-size: 30px;
        color: #40a0ff;
      }
    }
  }
}
.aside {
  flex: 0 0 350px;
  height: 100%;
  margin-left: 26px;
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 35px;
    background: #eff4f9;
    border-radius: 5px;
    font-size: 14px;
    color: #40a0ff;
    padding: 0 14px;
    margin-bottom: 19px;
  }
  .aside-content {
    background: #f7f7f7;
    border-radius: 5px 5px 5px 5px;
    padding: 18px 13px 1px;
    .href {
      .title {
        font-weight: 400;
        font-size: 14px;
        color: #3d3d3d;
        margin-bottom: 17px;
      }
      .text {
        line-height: 35px;
        background: #ffffff;
        border-radius: 35px;
        border: 1px solid #40a0ff;
        padding: 0 15px;
        color: #40a0ff;
        margin-bottom: 22px;
      }
    }
    .btn {
      width: 152px;
      line-height: 30px;
      background: #40a0ff;
      border-radius: 3px 3px 3px 3px;
      margin: 0 auto;
      text-align: center;
      color: #fff;
      &.w-auto {
        width: auto;
      }
    }
    .qrcode {
      width: 162px;
      height: 162px;
      background: #d8d8d8;
      border-radius: 0px 0px 0px 0px;
      font-size: 20px;
      color: #3d3d3d;
      line-height: 162px;
      text-align: center;
      margin: 0 auto 27px;
    }
    .tip {
      font-weight: 400;
      font-size: 14px;
      color: #3d3d3d;
      text-align: center;
      margin-bottom: 29px;
    }
  }
}
.percentage {
  padding: 0 32px;
  border-left: 1px solid #cadef1;
  border-right: 1px solid #cadef1;
  box-sizing: content-box;
  margin: 0 37px;

  .progress-ring {
    circle {
      transition: stroke-dashoffset 0.5s ease-in-out;
      transform: rotate(-90deg);
      transform-origin: 50% 50%;
    }

    .ring-text {
      font-size: 24px;
      fill: #409eff;
      font-weight: bold;
    }
  }
}

// 筛选框
.filter-wrap {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 10px;
  .filter-item {
    display: flex;
    align-items: center;
    justify-content: start;
    .filter-item-label {
      font-size: 14px;
      color: #3d3d3d;
      margin-right: 10px;
    }
    .filter-item-content {
      .el-select {
        width: 150px;
      }
    }
  }
}
</style>
