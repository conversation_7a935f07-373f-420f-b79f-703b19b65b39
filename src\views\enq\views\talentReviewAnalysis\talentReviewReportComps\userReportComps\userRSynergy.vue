<template>
  <div class="report_section userR_synergy_wrap">
    <div class="userR_synergy_main">
      <div class="page_second_title">
        <span>协同网络</span>
      </div>
      <div class="top_wrap flex_row_betweens">
        <div class="desc_info_wrap">
          <p class="primary_title">日常工作中打交道的人员及协同关系</p>
          <p class="title">协同网络类型</p>
          <p class="info_item">
            1、工作网络：与他们交流信息，作为日常工作的一部分。
          </p>
          <p class="info_item">2、创新网络：与谁合作或启动新想法。</p>
          <p class="info_item">
            3、社交网络：与员工建立友好关系，无论在工作或下班，知道员工发生了何种事情。
          </p>
          <p class="info_item">4、学习网络：员工与谁合作改进现有流程或方法。</p>
          <p class="info_item">
            5、专业网络：员工转向专业化或就工作相关问题提供建议。
          </p>
          <p class="info_item">6、战略网络：员工向谁寻求未来的建议</p>
        </div>
        <div class="right_wrap">
			<el-table class="table_wrap"  
				
				:data="tableData.data"
				@cell-click="cellClick"
				>
					<template v-for="item in tableData.tableTitle">
						<el-table-column 
							v-if="!item.canCheck"
							:label="item.label"
							:prop="item.prop"
							:key="item.code"
							:width="item.width"
							align="center"
						>
						</el-table-column> 
						<el-table-column 
							v-if="item.canCheck"
							class-name="check_column"
							:label="item.label"
							:prop="item.prop"
							:key="item.code"
							:width="item.width"
							align="center"
						>
							<template slot-scope="scope">
								<span :class="{ 'el-icon-check':scope.row[item.prop],'el-icon-disabled_check': !scope.row[item.disabledSign],'check_box_wrap':true}" ></span>
							</template>
						</el-table-column> 
					</template>
			</el-table>
        
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 协同网络
export default {
  name: "userRSynergy",
  props: ["nextBtnText", "enqId", "userId", "postCode"],
  components: {

    
  },
  data() {
    return {
      tableData: {
        tableTitle: [
          {
            label: "姓名",
            prop: "name",
            width: "",
            canCheck: false, 
          },
          {
            label: "部门",
            prop: "org",
            width: "",
            canCheck: false,
          },
          {
            label: "岗位",
            prop: "post",
            width: "",
            canCheck: false,
          },
		      {
            label: "工作",
            prop: "work",
            width: "50",
            canCheck: true,
          },
		      {
            label: "创新",
            prop: "innovate",
            width: "50",
            canCheck: true,
          },
		      {
            label: "社交",
            prop: "social",
            width: "50",
            canCheck: true,
          },
		      {
            label: "学习",
            prop: "study",
            width: "50",
            canCheck: true,
          },
		      {
            label: "专业",
            prop: "major",
            width: "60",
            canCheck: true,
          },
		      {
            label: "战略",
            prop: "strategy",
            width: "50",
            canCheck: true,
          },
         
        ],
        data: [
          {
            name: "姓名",
            target: "55天",
            realPerformance: "88",
            work:true,
            innovate:true,
            social:true,
            study:false,
            major:false,
            strategy:false
          },
        ],
      },
    };
  },
  created() {},
  computed: {},
  mounted() {},
  methods: {
	cellClick(row,column,cell,event){
		// console.table(event)
		// console.table(cell)
		// console.log(row)
		// console.log(column)
		let tableHeadList = this.tableData.tableTitle
		for(let i = 0;i<tableHeadList.length;i++ ){
			if(tableHeadList[i].prop == column.property){
				if(tableHeadList[i].canCheck){
					// 可以勾选
					// console.log(row[tableHeadList[i].prop])
					row[tableHeadList[i].prop] = !row[tableHeadList[i].prop]
				}
			}
		}
	},



  },
};
</script>
<style scoped lang="scss">
.userR_synergy_wrap {
  padding: 0 10px;
  height: 480px;
  overflow: auto;
  pointer-events: auto;
  .userR_synergy_main {
    .page_second_title {
      margin: 20px 0 15px;
    }
    .top_wrap {
      .desc_info_wrap {
        width: 39%;
        .primary_title {
          height: 40px;
        }
        .title {
        }
        .info_item {
          line-height: 25px;
        }
      }
      .right_wrap {
        width: 59%;
		 .table_wrap tbody{
			.el-icon-check{
				color: #0099FF;
				font-weight: 700;

			}
        }
      }
    }
  }
}
</style>