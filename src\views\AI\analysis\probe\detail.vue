<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import SimplenessTable from '@/components/table/simplenessTable.vue'
import Table from '../../../indicator/components/table.vue'
// import dna from "./dna.vue";
// import org from "./org.vue";
defineOptions({ name: 'probeDetail' })

const activeAside = ref(1)
const asideNav = ref([
  {
    name: '按根因类型探查',
    code: 1
  },
  {
    name: '按组织探查',
    code: 2
  }
])
const dataChart = ref([
  {
    name: '缺工具',
    value: 37.5
  },
  {
    name: '缺能力',
    value: 28
  },
  {
    name: '缺标准',
    value: 62
  },
  {
    name: '缺岗位',
    value: 41
  },
  {
    name: '缺数据',
    value: 19
  },
  {
    name: '缺制度',
    value: 19
  },
  {
    name: '缺系统',
    value: 19
  },
  {
    name: '缺考评',
    value: 19
  }
])
const columns1 = ref([
  {
    label: '根因类型',
    prop: 'name',
    width: 80
  },
  {
    label: '根因定义',
    prop: 'action',
    width: 160
  },
  {
    label: '占比分布',
    prop: 'personal',
    align: 'center',
    width: 90
  },
  {
    label: '影响系数',
    prop: 'result',
    align: 'center'
  },
  {
    label: '影响分析',
    prop: 'value',
    minWidth: 500
  }
])
const data1 = ref([
  {
    name: '缺标准',
    action: '无明确执行规范或质量要求',
    personal: '10%',
    result: '中',
    value:
      '需求计划对象的定义、分类及筛选无明确规范，导致各业务单元对 “目标需求” 的理解存在差异，需求收集范围模糊，计划编制依据不统一，易引发后续生产计划与市场实际需求脱节。'
  },
  {
    name: '缺工具',
    action: '缺乏数字化工具支持关键动作',
    personal: '15%',
    result: '高',
    value:
      '缺乏数字化工具支持需求对象的动态筛选与优先级排序，依赖人工表格处理，数据更新滞后且易出错，难以实时关联市场数据（如促销活动、竞品动态），导致需求计划调整效率低下。'
  },
  {
    name: '缺数据',
    action: '关键数据缺失或质量不可控',
    personal: '20%',
    result: '极高',
    value:
      '关键数据（如历史需求波动、客户产能规划、供应链约束）缺失或质量不可控，导致需求计划对象识别偏差，资源分配过度集中或遗漏高潜力市场，库存积压与短缺风险同时增加。'
  },
  {
    name: '缺系统',
    action: '系统功能不支撑业务流程',
    personal: '25%',
    result: '极高',
    value:
      '系统功能无法支撑需求计划对象的全生命周期管理（如跨区域需求合并、多品类需求对冲），需人工拆分或合并数据，引发计划逻辑断层，导致生产排程与实际需求错配，订单履约率下降。'
  },
  {
    name: '缺制度',
    action: '无管理制度约束关键行为',
    personal: '15%',
    result: '高',
    value:
      '缺乏需求计划对象的责任界定与流程约束（如需求变更审批、跨部门协同时效），可能出现需求重复提报、责任主体模糊，导致计划修订频繁，供应链各环节响应混乱，整体协同效率降低。'
  },
  {
    name: '缺岗位',
    action: '无专职岗位负责核心环节',
    personal: '5%',
    result: '低',
    value:
      '无专职岗位统筹需求计划对象的动态维护与策略优化，由其他岗位兼任（如销售兼顾需求提报与对象管理），精力分散导致需求对象更新不及时，市场变化响应延迟，但短期可通过跨团队协作弥补。'
  },
  {
    name: '缺能力',
    action: '人员能力缺乏或支撑不足',
    personal: '5%',
    result: '低',
    value:
      '员工在需求对象数据分析（如 ABC 分类、需求弹性测算）能力不足，可能导致需求分级不合理、潜力市场识别不精准，但可通过定向培训快速提升，对整体计划流程影响有限。'
  },
  {
    name: '缺考评',
    action: '无绩效考核驱动目标达成',
    personal: '5%',
    result: '中',
    value:
      '缺乏需求计划对象管理的量化考核指标（如对象准确率、计划调整频次），员工对需求定义的严谨性重视不足，可能导致基础数据质量下降，长期影响计划体系可信度。'
  }
])

const columns2 = ref([
  {
    label: '能力名称',
    prop: 'name'
  },
  {
    label: '对能力的影响路径',
    prop: 'action',
    width: 220
  },
  {
    label: '影响程度',
    prop: 'personal',
    align: 'center'
  },
  {
    label: '改善优先级',
    prop: 'result',
    align: 'center'
  },
  {
    label: '改善建议',
    prop: 'value',
    minWidth: 500
  }
])
const data2 = ref([
  {
    name: '商机标准化流程',
    action: '数据缺标准→需求对象定义模糊→业务单元理解差异→需求收集范围混乱→计划编制依据不统一→产需匹配度下降 ',
    personal: '高',
    result: '高',
    value:
      '① 制定《需求计划对象管理规范》，明确客户类型（如 TOB/TOC）、产品品类、区域市场等分类标准；② 建立需求对象准入清单，规定必选属性（如历史订单量、市场潜力值）；③ 组织跨部门标准宣贯会，确保需求定义一致性'
  }
])

const columns3 = ref([
  {
    label: '组织名称',
    prop: 'organization'
  },
  {
    label: '举措',
    prop: 'measure'
  },
  {
    label: '关键行动',
    prop: 'keyActions',
    width: 420
  },
  {
    label: '建议责任人',
    prop: 'responsiblePerson'
  },
  {
    label: '优势',
    prop: 'advantage'
  },
  {
    label: '优先级',
    prop: 'priority',
    align: 'center'
  }
])
const data3 = ref([
  {
    id: 1,
    organization: '供应链计划管理部',
    measure: '建立需求计划对象准入标准体系',
    keyActions:
      '①明确需求计划对象的核心指标（如产品类型、市场区域、客户规模等）；②制定需求计划对象准入审核流程（包含数据完整性校验、业务相关性评估等环节）；③建立动态调整机制，根据市场变化和业务反馈定期优化准入标准。',
    responsiblePerson: '张涛（计划管理经理）',
    advantage: '《需求计划对象准入标准手册》',
    priority: '高'
  },
  {
    id: 2,
    organization: '营销管理部',
    measure: '制定需求计划对象管理操作规范',
    keyActions:
      '①编制《需求计划对象管理 SOP 手册》，覆盖对象筛选、数据收集、审核评估、纳入计划全流程；②明确各环节操作标准（如数据更新频率、审核时间节点、跨部门协作流程）；③定义不同类型需求计划对象的管理细则（如战略客户与普通客户的差异化管理要求）。',
    responsiblePerson: '李娜（营销流程专员）',
    advantage: '《需求计划对象管理操作规范》',
    priority: '中'
  },
  {
    id: 3,
    organization: '智能制造推进部',
    measure: '系统固化需求计划对象管理标准',
    keyActions:
      '①在供应链管理系统中嵌入需求计划对象准入校验规则（如未通过审核的对象不可纳入主生产计划）；②开发自动提醒功能（如临近数据更新时间推送待办任务、审核超期预警）；③建立标准化数据模板库（需求申请表单、审核报告模板、动态更新记录模板），系统自动关联业务节点。',
    responsiblePerson: '王强（系统实施工程师）',
    advantage: '《需求计划对象管理系统操作手册》',
    priority: '低'
  }
])

const orgTableRef = ref(null)
const columns4 = ref([
  {
    label: '组织',
    prop: 'department',
    align: 'center'
  },
  {
    label: '缺标准',
    prop: 'a',
    slot: 'aSlot'
  },
  {
    label: '缺工具',
    prop: 'b',
    slot: 'bSlot'
  },
  {
    label: '缺数据',
    prop: 'c',
    slot: 'cSlot'
  },
  {
    label: '缺系统',
    prop: 'd',
    slot: 'dSlot'
  },
  {
    label: '缺制度',
    prop: 'e',
    align: 'center',
    slot: 'eSlot'
  },
  {
    label: '缺岗位',
    prop: 'f',
    align: 'center',
    slot: 'fSlot'
  },
  {
    label: '缺能力',
    prop: 'g',
    align: 'center',
    slot: 'gSlot'
  },
  {
    label: '缺考评',
    prop: 'h',
    align: 'center',
    slot: 'hSlot'
  }
])
const data4 = ref([
  {
    id: 1,
    department: '采购部',
    a: 49,
    b: 70,
    c: 67,
    d: 58,
    e: 58,
    f: 65,
    g: 46,
    h: 65
  },
  {
    id: 2,
    department: '成都冰箱工厂',
    a: 63,
    b: 67,
    c: 47,
    d: 50,
    e: 68,
    f: 58,
    g: 61,
    h: 49
  },
  {
    id: 3,
    department: '电子商务部',
    a: 66,
    b: 48,
    c: 69,
    d: 61,
    e: 69,
    f: 64,
    g: 61,
    h: 61
  },
  {
    id: 4,
    department: '欧盟区产品部',
    a: 50,
    b: 45,
    c: 63,
    d: 53,
    e: 65,
    f: 52,
    g: 65,
    h: 49
  },
  {
    id: 5,
    department: '工艺部',
    a: 52,
    b: 59,
    c: 60,
    d: 67,
    e: 70,
    f: 46,
    g: 53,
    h: 67
  },
  {
    id: 6,
    department: '供应链计划管理部',
    a: 60,
    b: 58,
    c: 48,
    d: 63,
    e: 57,
    f: 52,
    g: 68,
    h: 67
  },
  {
    id: 7,
    department: 'H公司冰冷GTM部',
    a: 67,
    b: 67,
    c: 52,
    d: 47,
    e: 54,
    f: 57,
    g: 54,
    h: 58
  },
  {
    id: 8,
    department: '结构研发部',
    a: 70,
    b: 52,
    c: 68,
    d: 53,
    e: 54,
    f: 68,
    g: 61,
    h: 45
  },
  {
    id: 9,
    department: '经营与财务管理部',
    a: 66,
    b: 45,
    c: 49,
    d: 57,
    e: 57,
    f: 48,
    g: 64,
    h: 58
  },
  {
    id: 10,
    department: '冷柜研发部',
    a: 54,
    b: 56,
    c: 65,
    d: 67,
    e: 49,
    f: 52,
    g: 66,
    h: 57
  },
  {
    id: 11,
    department: '零售与用户运营部',
    a: 67,
    b: 66,
    c: 54,
    d: 50,
    e: 62,
    f: 46,
    g: 50,
    h: 47
  },
  {
    id: 12,
    department: '品牌与产品营销部',
    a: 70,
    b: 58,
    c: 47,
    d: 46,
    e: 69,
    f: 64,
    g: 64,
    h: 64
  },
  {
    id: 13,
    department: '胶州冰冷工厂',
    a: 58,
    b: 64,
    c: 62,
    d: 61,
    e: 68,
    f: 51,
    g: 64,
    h: 50
  },
  {
    id: 14,
    department: '渠道运营部',
    a: 51,
    b: 45,
    c: 69,
    d: 58,
    e: 61,
    f: 58,
    g: 46,
    h: 56
  },
  {
    id: 15,
    department: '全球产品经理部',
    a: 49,
    b: 69,
    c: 56,
    d: 56,
    e: 69,
    f: 62,
    g: 45,
    h: 57
  },
  {
    id: 16,
    department: '冰冷GTM部',
    a: 53,
    b: 45,
    c: 66,
    d: 65,
    e: 50,
    f: 57,
    g: 46,
    h: 57
  },
  {
    id: 17,
    department: '顺德冰冷工厂',
    a: 49,
    b: 61,
    c: 54,
    d: 52,
    e: 62,
    f: 46,
    g: 45,
    h: 60
  },
  {
    id: 18,
    department: '顺德研发部',
    a: 60,
    b: 46,
    c: 57,
    d: 51,
    e: 70,
    f: 46,
    g: 68,
    h: 57
  },
  {
    id: 19,
    department: '扬州冰箱工厂',
    a: 59,
    b: 56,
    c: 50,
    d: 67,
    e: 67,
    f: 59,
    g: 58,
    h: 56
  },
  {
    id: 20,
    department: '营销管理部',
    a: 46,
    b: 53,
    c: 56,
    d: 65,
    e: 68,
    f: 67,
    g: 47,
    h: 59
  },
  {
    id: 21,
    department: '用户服务部',
    a: 48,
    b: 67,
    c: 65,
    d: 49,
    e: 47,
    f: 53,
    g: 46,
    h: 58
  },
  {
    id: 22,
    department: '战略与变革管理部',
    a: 52,
    b: 52,
    c: 51,
    d: 66,
    e: 70,
    f: 53,
    g: 47,
    h: 54
  },
  {
    id: 23,
    department: '制造中心领导',
    a: 62,
    b: 46,
    c: 63,
    d: 53,
    e: 57,
    f: 56,
    g: 54,
    h: 51
  },
  {
    id: 24,
    department: '质量部',
    a: 62,
    b: 60,
    c: 68,
    d: 59,
    e: 57,
    f: 51,
    g: 49,
    h: 52
  },
  {
    id: 25,
    department: '智能制造推进部',
    a: 66,
    b: 48,
    c: 45,
    d: 48,
    e: 54,
    f: 62,
    g: 47,
    h: 64
  }
])

// onMounted(() => {
//   orgTableRef.value.simplenessTableRef.setCurrentRow(row)
// })
const columns5 = ref([
  {
    label: '组织名称',
    prop: 'department',
    align: 'center'
  },
  {
    label: '短板类型',
    prop: 'deficiencyType',
    align: 'center'
  },
  {
    label: '得分',
    prop: 'score',
    align: 'center'
  },
  {
    label: '主要管理影响',
    prop: 'managementImpact',
    width: 220
  },
  {
    label: '主要指标影响',
    prop: 'indicatorImpact',
    width: 220
  },
  {
    label: '影响程度',
    prop: 'impactLevel',
    align: 'center'
  },
  {
    label: '传导路径',
    prop: 'transmissionPath',
    width: 220
  },
  {
    label: '关键任务',
    prop: 'keyTask',
    align: 'center'
  }
])
const data5 = ref([
  {
    id: 1,
    department: '供应链计划管理部',
    deficiencyType: '缺标准',
    score: 60,
    managementImpact:
      '需求计划对象的定义边界不清晰，各部门对 “有效需求” 的理解存在差异，导致需求筛选标准不统一；跨部门协作时需反复对齐需求口径，协调成本增加 30% 以上；需求决策依赖个人经验判断，缺乏科学量化依据，导致资源错配风险升高。',
    indicatorImpact:
      '需求计划对象准确率（实际纳入计划的有效需求占比）下降至 60% 以下；需求重复提报率上升至 25% 以上；跨部门需求评审通过率从 85% 降至 60%。',
    impactLevel: '非常高',
    transmissionPath:
      '缺标准→需求对象定义模糊→部门理解差异→筛选流程混乱→跨部门协作低效→需求计划与实际业务目标偏离→资源浪费或需求遗漏',
    keyTask: 3
  },
  {
    id: 2,
    department: '供应链计划管理部',
    deficiencyType: '缺工具',
    score: 58,
    managementImpact:
      '需求数据采集依赖 Excel 手工录入，数据校验、分类耗时占比达 70%；缺乏智能评估工具，需求优先级排序需人工比对 10 + 维度数据，单需求处理时间延长至 4 小时以上；无法实时监控需求状态变化，需求响应延迟导致生产排程调整频次增加。',
    indicatorImpact:
      '单需求平均处理耗时从 2 小时延长至 4.5 小时；实时数据覆盖率从 90% 降至 40%；需求状态更新延迟导致的生产排程变更率上升至 15%。',
    impactLevel: '中',
    transmissionPath: '缺工具→手工操作→数据处理效率低下→评估过程冗长→需求状态监控缺失→生产计划灵活性下降→交付周期延长',
    keyTask: 3
  },
  {
    id: 3,
    department: '供应链计划管理部',
    deficiencyType: '缺数据',
    score: 48,
    managementImpact:
      '客户历史需求数据、市场趋势数据、产能约束数据缺失，导致需求计划对象的市场匹配度分析不完整；资源分配时缺乏数据支撑，出现高端产品产能过剩（过剩率达 20%）或民生产品供应不足（缺货率上升 15%）；风险预判依赖定性分析，需求波动应对滞后。',
    indicatorImpact:
      '需求与产能匹配度从 75% 降至 50%；需求计划变更率（因数据缺失导致的调整）上升至 30%；客户需求误判率（无效需求纳入计划的比例）从 8% 升至 25%。',
    impactLevel: '高',
    transmissionPath:
      '缺数据→需求对象基础信息不全→市场匹配度分析失真→资源分配策略偏差→产能浪费或短缺→客户满意度下降→订单流失风险增加',
    keyTask: 3
  },
  {
    id: 4,
    department: '供应链计划管理部',
    deficiencyType: '缺系统',
    score: 63,
    managementImpact:
      '需求计划对象管理依赖多套孤立系统，跨系统数据同步需人工导入，错误率达 15%；系统缺乏需求全生命周期管理功能，需求审核、分配、变更等环节流程卡顿，节点通过率降至 70%；数据追溯困难，问题定位时间延长至 8 小时以上。',
    indicatorImpact:
      '流程自动化率从 80% 降至 40%；数据不一致率（多系统数据偏差）上升至 20%；需求计划节点超时率（超过规定时间未完成的比例）从 5% 升至 30%。',
    impactLevel: '低',
    transmissionPath:
      '缺系统→功能断层→跨系统人工操作→数据误差积累→流程节点阻塞→需求计划执行效率低下→供应链响应速度下降',
    keyTask: 3
  },
  {
    id: 5,
    department: '供应链计划管理部',
    deficiencyType: '缺制度',
    score: 57,
    managementImpact:
      '需求计划对象的审核责任、更新频率、跨部门协作流程无明确制度约束，导致需求审核周期波动（从 24 小时到 72 小时不等）；需求优先级调整随意性强，战略级需求被延迟处理的概率达 40%；过程记录缺失，问题追责困难，责任纠纷频率增加 50%。',
    indicatorImpact:
      '需求审核通过率（符合质量要求的需求占比）从 90% 降至 70%；需求优先级调整合规率（按制度执行的比例）从 85% 降至 55%；跨部门协作投诉率从 10 次 / 月升至 30 次 / 月。',
    impactLevel: '高',
    transmissionPath:
      '缺制度→关键行为无约束→审核 / 调整流程随意→需求处理标准不统一→战略需求执行滞后→供应链整体协同效率下降',
    keyTask: 3
  },
  {
    id: 6,
    department: '供应链计划管理部',
    deficiencyType: '缺岗位',
    score: 52,
    managementImpact:
      '无专职岗位负责需求计划对象的准入审核与动态维护，导致新增需求漏审率达 25%；历史需求数据未及时归档清理，无效需求占比升至 40%；跨部门需求对接无专人跟进，需求传递失真率达 30%，关键信息遗漏导致的决策失误增加。',
    indicatorImpact:
      '需求计划对象漏审率（未审核即纳入计划的比例）从 5% 升至 30%；历史数据维护及时率从 95% 降至 60%；需求信息完整率（关键字段缺失比例）从 90% 降至 65%。',
    impactLevel: '高',
    transmissionPath:
      '缺岗位→核心环节无人专责→准入审核缺失→数据维护滞后→需求基础信息质量恶化→需求计划可靠性丧失→供应链计划失效风险升高',
    keyTask: 3
  },
  {
    id: 7,
    department: '供应链计划管理部',
    deficiencyType: '缺能力',
    score: 68,
    managementImpact:
      '人员缺乏需求预测模型、数据建模等专业技能，需求计划对象评估方案逻辑性不足，被业务部门驳回率达 40%；跨部门沟通时无法有效解读需求本质，导致需求转化偏差率达 35%；数据分析工具使用熟练度低，需求分析报告合格率从 85% 降至 50%。',
    indicatorImpact:
      '需求计划可行性报告通过率（一次评审通过的比例）从 80% 降至 50%；跨部门需求沟通效率（单需求澄清次数）从 2 次 / 个增至 5 次 / 个；数据分析报告质量缺陷率（错误 / 遗漏项）从 10% 升至 40%。',
    impactLevel: '高',
    transmissionPath:
      '缺能力→专业技能不足→评估方案缺陷→跨部门沟通低效→需求理解偏差→需求计划与实际业务需求脱节→资源投入回报率下降',
    keyTask: 3
  },
  {
    id: 8,
    department: '供应链计划管理部',
    deficiencyType: '缺考评',
    score: 67,
    managementImpact:
      '缺乏明确的绩效考核指标，员工对需求计划对象管理的核心目标（如准确率、处理时效）认知模糊，关键任务完成率仅 60%；激励机制缺失，员工主动优化需求管理流程的意愿低，低效操作习惯持续沿用，导致需求管理效能停滞不前。',
    indicatorImpact:
      '需求计划对象管理目标达成率（按计划完成的比例）从 90% 降至 65%；流程执行合规率（按标准流程操作的比例）从 85% 降至 60%；员工提出的流程优化建议量从 20 条 / 月降至 5 条 / 月。',
    impactLevel: '高',
    transmissionPath: '缺考评→目标与标准模糊→员工动力不足→关键任务执行不到位→需求管理体系运行僵化→供应链响应能力退化',
    keyTask: 3
  }
])

const columns6 = ref([
  {
    label: '组织名称',
    prop: 'organization'
  },
  {
    label: '举措',
    prop: 'initiative'
  },
  {
    label: '关键行动',
    prop: 'keyActions',
    width: 420
  },
  {
    label: '建议责任人',
    prop: 'responsiblePerson'
  },
  {
    label: '输出成果',
    prop: 'output'
  },
  {
    label: '优先级',
    prop: 'priority',
    align: 'center'
  }
])
const data6 = ref([
  {
    id: 1,
    organization: '供应链计划管理部',
    initiative: '建立需求计划对象准入标准体系',
    keyActions:
      '①明确需求计划对象核心指标（产品类型、市场区域、客户规模、需求紧急度等 8 + 维度）；②制定三级审核流程（业务部门初审、计划部门复审、跨部门评审会终审），嵌入预算匹配度、产能可行性自动校验规则；③建立季度动态调整机制，根据历史需求履约率、市场份额变化等数据优化准入阈值。',
    responsiblePerson: '张涛（计划管理经理）',
    output: '《需求计划对象准入标准手册》',
    priority: '高'
  },
  {
    id: 2,
    organization: '供应链计划管理部',
    initiative: '开发需求计划对象智能评估工具',
    keyActions:
      '①设计多维度评估模型（基于 ABC 分类法 + 层次分析法，包含需求价值度、风险等级、资源消耗系数等 12 项指标）；②集成 ERP、CRM、MES 系统数据接口，实现需求数据自动抓取与清洗；③开发可视化评估看板，实时显示需求计划对象的优先级排序、资源匹配建议及风险预警信息。',
    responsiblePerson: '郑浩（IT 开发经理）',
    output: '《需求评估工具操作指南》',
    priority: '中'
  },
  {
    id: 3,
    organization: '供应链计划管理部',
    initiative: '构建需求计划对象数据治理体系',
    keyActions:
      '①统一数据标准（定义 30 + 核心字段如需求编码规则、客户等级分类、产品生命周期阶段），发布《数据字典手册》；②部署数据质量监控平台，设置完整性（必填字段缺失率≤5%）、准确性（数据校验通过率≥95%）等 8 项监控指标，每日生成数据健康度报告；③启动历史数据清洗专项，3 个月内完成 10 万 + 条存量需求数据的去重、补全与标准化处理。',
    responsiblePerson: '许晴（数据治理专员）',
    output: '《需求数据治理实施方案》',
    priority: '高'
  },
  {
    id: 4,
    organization: '供应链计划管理部',
    initiative: '制定需求计划对象全流程管理制度',
    keyActions:
      '①编制《需求计划对象管理办法》，明确需求提报、审核、分配、变更、结案各环节的权责分工（如业务部门负责需求真实性审核，计划部门负责可行性评估）；②配套制定《跨部门协作流程规范》，定义需求传递时效（常规需求 48 小时内响应，紧急需求 2 小时内反馈）及接口标准（需附带产能需求、财务预算等 10 项附件）；③建立制度执行追踪机制，每季度开展流程合规性审计，违规率纳入部门 KPI 考核。',
    responsiblePerson: '杨明（供应链总监）',
    output: '《需求管理规章制度汇编》',
    priority: '中'
  },
  {
    id: 5,
    organization: '供应链计划管理部',
    initiative: '实施需求计划管理能力提升计划',
    keyActions:
      '①开发 “需求计划管理” 专项课程包（包含需求预测模型构建、数据可视化分析、跨部门沟通技巧等 6 门核心课程），每季度组织 2 次集中培训 + 月度线上答疑；②推行 “师徒制” 培养模式，安排资深计划经理与新人结对，3 个月内完成 10 + 个典型需求案例实操指导；③建立能力认证体系，通过笔试（60 分）+ 实操（需求评估方案设计）考核者授予中级计划管理资格，作为岗位晋升必备条件。',
    responsiblePerson: '郭亮（培训经理）',
    output: '《需求管理能力培训手册》',
    priority: '低'
  }
])

const getChartOpt = item => {
  return {
    xAxisData: item.map(i => i.name),
    yAxis: {
      show: false
    },
    xAxis: {
      show: true
    },
    grid: {
      left: 0,
      top: 10,
      right: 0,
      bottom: 20
    },
    series: [
      {
        data: item.map(i => i.value),
        type: 'bar',
        showBackground: true,
        barMaxWidth: 40,
        itemStyle: {
          color: '#40a0ff'
        },
        label: {
          show: true
        }
      }
    ]
  }
}

const checkSecTab = c => {
  activeAside.value = c
}
const clickRow = () => {}
const circleColor = v => {
  if (v < 50) {
    return 'red_bg'
  } else if (v <= 70 && v >= 50) {
    return 'yellow_bg'
  } else if (v > 70) {
    return 'green_bg'
  }
}
const router = useRouter()
const back = () => {
  router.back()
}
</script>
<template>
  <div class="probe-detail">
    <div class="title">
      <SvgIcon name="icon-back" class="pointer" @click="back" />
      <div class="back-text" @click="back">返回</div>
      <div class="title-name">根因探查（制定需求计划对象）</div>
    </div>
    <div class="detail-content">
      <div class="aside">
        <div
          class="s_tab_item"
          v-for="item in asideNav"
          :class="{ act: item.code == activeAside }"
          @click="checkSecTab(item.code)"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="content-main" v-if="activeAside == 1">
        <div class="page-title-line">根因类型占比分布</div>
        <div class="item-chart">
          <EChartsBar type="vertical" :options="getChartOpt(dataChart)"></EChartsBar>
        </div>
        <div class="page-title-line">根因类型分布分析</div>
        <div class="mar_b_30">
          <Table :roundBorder="false" :columns="columns1" :data="data1" headerColor showIndex>
            <template #default="scope">
              <el-table-column class="icon_wrap" min-width="60px">
                <el-button type="primary" plain round @click.prevent="clickRow(scope.$index)">详情</el-button>
              </el-table-column>
            </template>
          </Table>
        </div>
        <div class="page-title-line">根因类型能力分析（缺标准）</div>
        <div class="mar_b_30">
          <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor showIndex> </Table>
        </div>
        <div class="page-title-line">建议改善关键任务（缺标准）</div>
        <div class="mar_b_30">
          <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor showIndex> </Table>
        </div>
      </div>
      <div class="content-main" v-if="activeAside == 2">
        <div class="page-title-line">根因类型组织分布</div>
        <div class="mar_b_30">
          <Table
            :roundBorder="false"
            ref="orgTableRef"
            highlight-current-row
            :columns="columns4"
            :data="data4"
            headerColor
            showIndex
          >
            <template v-slot:aSlot="scope">
              <span class="circle" :class="circleColor(scope.row.a)">{{ scope.row.a }}</span>
            </template>
            <template v-slot:bSlot="scope">
              <span class="circle" :class="circleColor(scope.row.b)">{{ scope.row.b }}</span>
            </template>
            <template v-slot:cSlot="scope">
              <span class="circle" :class="circleColor(scope.row.c)">{{ scope.row.c }}</span>
            </template>
            <template v-slot:dSlot="scope">
              <span class="circle" :class="circleColor(scope.row.d)">{{ scope.row.d }}</span>
            </template>
            <template v-slot:eSlot="scope">
              <span class="circle" :class="circleColor(scope.row.e)">{{ scope.row.e }}</span>
            </template>
            <template v-slot:fSlot="scope">
              <span class="circle" :class="circleColor(scope.row.f)">{{ scope.row.f }}</span>
            </template>
            <template v-slot:gSlot="scope">
              <span class="circle" :class="circleColor(scope.row.g)">{{ scope.row.g }}</span>
            </template>
            <template v-slot:hSlot="scope">
              <span class="circle" :class="circleColor(scope.row.h)">{{ scope.row.h }}</span>
            </template>
            <template #default="scope">
              <el-table-column class="icon_wrap" min-width="60px">
                <el-button type="primary" plain round @click.prevent="clickRow(scope.$index)">详情</el-button>
              </el-table-column>
            </template>
          </Table>
        </div>
        <div class="page-title-line">根因组织分析（供应链计划管理部）</div>
        <div class="mar_b_30">
          <Table :roundBorder="false" :columns="columns5" :data="data5" headerColor showIndex> </Table>
        </div>
        <div class="page-title-line">建议改善关键任务（缺标准）</div>
        <div class="mar_b_30">
          <Table :roundBorder="false" :columns="columns6" :data="data6" headerColor showIndex> </Table>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.probe-detail {
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
  border-radius: 8px 8px 8px 8px;
  :deep(.el-table) {
    .el-icon {
      margin-right: 20px;

      font-size: 18px;
      color: #40a0ff;
      cursor: pointer;
    }
    .el-icon:hover {
      color: #3778ea;
    }
    .el-button {
      border-radius: 4px;
      height: 24px;
      border: 1px solid #40a0ff;
    }
    .circle {
      display: inline-block;
      width: 65px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      color: #fff;
      border-radius: 10px;
    }
    .red_bg {
      background: #a0f0f6;
    }
    .yellow_bg {
      background: #6acefc;
    }
    .green_bg {
      background: #6acefc;
    }
  }
  .mar_b_30 {
    margin-bottom: 30px;
  }
  .title {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #53a9f9;
    padding-bottom: 20px;
    border-bottom: 1px solid #d8d8d8;
    margin-bottom: 18px;
    .back-text {
      margin-left: 10px;
      cursor: pointer;
      font-size: 14px;
      color: #888888;
      margin-right: 10px;
    }
  }
  .detail-content {
    display: flex;
    gap: 20px;
    .aside {
      flex: 0 0 240px;
      .s_tab_item {
        margin-bottom: 15px;
        width: 233px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        color: #333;
        font-size: 14px;
        background: url('@/assets/imgs/indicator/img_01.png') no-repeat center center;
        background-size: 100% 100%;
        cursor: pointer;
        &.act {
          color: #40a0ff;
          background: url('@/assets/imgs/indicator/img_02.png') no-repeat center center;
          background-size: 100% 100%;
        }
      }
    }
  }
  .content-main {
    flex: 1;
    .item-chart {
      width: 100%;
      height: 200px;
    }
  }
}
</style>
