<template>
    <div class="training_activities_center clearfix">
        <div
            class="training_activities_item flex_row_between"
            v-for="(item,index) in listData"
            :key="item.enqId"
        >
            <div class="item_index">{{index+1}}</div>
            <div class="item_content_wrap">
                <div class="item_content flex_row_between">
                    <div class="item_content_list project_name">
                        <div class="list_title">项目名称</div>
                        <div class="list_text">{{item.enqName}}</div>
                    </div>
                    <div class="item_content_list">
                        <div class="list_title">开始日期</div>
                        <div class="list_text">
                            {{item.beginDate | removeTime}}
                        </div>
                    </div>
                    <div class="item_content_list">
                        <div class="list_title">结束日期</div>
                        <div class="list_text">
                            {{item.endDate | removeTime}}
                        </div>
                    </div>
                    <div class="item_content_list">
                        <div class="list_title">参与人数</div>
                        <div class="list_text">
                            <span class="list_num">{{item.total}}</span>人
                        </div>
                    </div>
                </div>
            </div>
            <div class="item_oper align_right">
                <el-button class="page_add_btn" type="primary"  v-link="'/talentReviewHome/talentReviewAnalysis/TRreport/projectView/showReport?enqId='+item.enqId">查看报告</el-button>
            </div>
        </div>
    </div>
</template>
 
<script>
export default {
    name: "reviewProgressManageList",
    props: {
        listData: Array
    },
    components: {},
    data() {
        return {};
    },
    methods: {},
    filters:{
        removeTime:function(val){
            return val ? val.split(" ")[0] : " ";
            // return val.split(" ")[0];
        }
    }
};
</script>
 
<style scoped lang="scss">
.training_activities_item {
    position: relative;
    padding: 16px 8px 16px 30px;
    margin-bottom: 8px;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    overflow: hidden;
    .item_index {
        width: 50px;
        font-weight: bold;
        font-size: 20px;
        color: #0099FF;
    }
    .item_content_wrap {
        width: 80%;
        padding: 0 8px;
        .item_content {
            padding-right: 200px;
            .item_content_list {
                color: #525e6c;
                &.project_name{
                    width: 25%;
                }
                .list_title {
                    font-weight: bold;
                    margin-bottom: 8px;
                }
                .list_num {
                    color: #0099FF;
                    font-weight: bold;
                    font-size: 16px;
                }
            }
        }
    }
}
</style>