<template>
  <div class="tree_wrap tree_comp_checkbox_wrap">
    <el-tree
      :data="treeData"
      show-checkbox
      :node-key="nodeKey"
      :default-expand-all="expandAll"
      check-strictly
      :default-checked-keys="defaultCheckedKeys"
      :default-expanded-keys="computedExpandedKeys"
      :expand-on-click-node="false"
      ref="treeCheckboxRef"
      :props="{ label: labelKey }"
      @node-click="nodeClickCallback"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span class="tree_node" :title="node.label">
            {{ node.label }}
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'

const props = defineProps({
  treeData: {
    type: Array,
    default: () => []
  },
  // 节点唯一key，点击节点需要返回的字段
  nodeKey: {
    type: String,
    default: 'code'
  },
  // 节点显示文本的字段
  labelKey: {
    type: String,
    default: 'value'
  },
  checkedAll: {
    type: Boolean,
    default: false
  },
  defaultCheckedKeys: {
    type: Array,
    default: () => []
  },
  // 默认展开的节点 nodeKey
  defaultExpandedKeys: {
    type: Array,
    default: () => []
  },
  // 默认展开所有的节点
  // 若需要 默认展开节点 请传入false
  expandAll: {
    type: Boolean,
    default: true
  },
  // 需要默认展开的层级,若使用此配置需把expandAll设为false
  // 默认3级
  expandedLevel: {
    type: Number,
    default: 3
  },
  // 点击父节点是否选中子节点，默认开启
  checkChildNode: {
    type: Boolean,
    default: true
  },
  // 复选且只能选择平级节点，默认不开启
  isLateralCheck: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['node-click-callback'])

// 响应式数据
const treeCheckboxRef = ref(null)
const maxexpandId = ref(1000)
const checkedId = ref([])
const checkedData = ref('')
const checkedCode = ref([])
const layerNo = ref('') // 已选择的层级
const expandedKeys = ref([...props.defaultExpandedKeys])

// 计算属性 - 动态展开的节点keys
const computedExpandedKeys = computed(() => {
  return expandedKeys.value
})

// 方法定义
const setCheckedData = () => {
  // console.log(props.defaultCheckedKeys[0]);
}

const getCheckedKeys = (curCheckCode) => {
  checkedCode.value = treeCheckboxRef.value.getCheckedKeys()
  // console.log(checkedCode.value, curCheckCode)
  emit('node-click-callback', checkedCode.value, curCheckCode)
}

const nodeClickCallback = (data, node, self) => {
  // console.log(data, node, self);
  if (props.isLateralCheck) {
    if (node.data.layerNo != layerNo.value) {
      treeCheckboxRef.value.setCheckedKeys([])
      layerNo.value = node.data.layerNo
    }
  }
  let checked = node.checked
  node.checked = !checked
  if (props.checkChildNode) {
    setChildNodesChecked(node, checked)
  }
  let curCheckCode = node.data.code
  getCheckedKeys(curCheckCode)
  // setTimeout(() => {
  // },500)
}

const setChildNodesChecked = (node, checked) => {
  if (node.childNodes.length > 0) {
    node.childNodes.forEach(item => {
      item.checked = !checked
      setChildNodesChecked(item, checked)
    })
  } else {
    return
  }
}

const setExpandKeys = () => {
  if (props.expandAll) return

  const getKeys = (data) => {
    data.forEach(item => {
      if (item.layerNo < props.expandedLevel + 1) {
        expandedKeys.value.push(item.code)
      }
      if (item.children && item.children.length) {
        getKeys(item.children)
      }
    })
  }

  getKeys(props.treeData)
}

// 监听器
watch(
  () => props.treeData,
  (val) => {
    console.log(1)
    if (val.length == 0) return
    setExpandKeys()
  }
)

// 生命周期
onMounted(() => {
  nextTick(() => {
    if (props.checkedAll) {
      treeCheckboxRef.value.setCheckedNodes(props.treeData)
    }
    console.log(props.defaultCheckedKeys)
    if (props.defaultCheckedKeys.length > 0) {
      // 调用组件默认触发回调函数
      emit('node-click-callback', props.defaultCheckedKeys, null)
    }
  })
})
</script>

<style scoped lang="scss">
.tree_wrap {
  width: 100%;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
/* :deep(.el-tree-node__expand-icon) {
  display: none;
} */
:deep(.el-tree-node__content > label.el-checkbox) {
  pointer-events: none;
}

.tree_comp_checkbox_wrap {
  :deep(.custom-tree-node) {
    width: 75%;
  }
  :deep(.tree_node) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
