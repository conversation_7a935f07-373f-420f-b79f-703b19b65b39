<template>
  <div class="tree_wrap tree_comp_checkbox_wrap">
    <el-tree
      :data="treeData"
      show-checkbox
      :node-key="nodeKey"
      :default-expand-all="expandAll"
      check-strictly
      :default-checked-keys="defaultCheckedKeys"
      :default-expanded-keys="defaultExpandedKeys"
      :expand-on-click-node="false"
      ref="treeCheckbox"
      :props="{ label: labelKey }"
      @node-click="nodeClickCallback"
    >
      <template v-slot="{ node, data }">
        <span class="custom-tree-node">
          <span class="tree_node" :title="node.label">
            {{ node.label }}
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script>
export default {
  name: 'treeCompCheckbox',
  props: {
    treeData: {
      type: Array,
      default: function () {
        return []
      }
    },
    // 节点唯一key，点击节点需要返回的字段
    nodeKey: {
      type: String,
      default: 'code'
    },
    // 节点显示文本的字段
    labelKey: {
      type: String,
      default: 'value'
    },
    checkedAll: {
      type: Boolean,
      default: false
    },
    defaultCheckedKeys: {
      type: Array,
      default: function () {
        return []
      }
    },
    // 默认展开的节点 nodeKey
    defaultExpandedKeys: {
      type: Array,
      default: function () {
        return []
      }
    },
    // 默认展开所有的节点
    // 若需要 默认展开节点 请传入false
    expandAll: {
      type: Boolean,
      default: true
    },
    // 需要默认展开的层级,若使用此配置需把expandAll设为false
    // 默认3级
    expandedLevel: {
      type: Number,
      default: 3
    },
    // 点击父节点是否选中子节点，默认开启
    checkChildNode: {
      type: Boolean,
      default: true
    },
    // 复选且只能选择平级节点，默认不开启
    isLateralCheck: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      maxexpandId: 1000,
      checkedId: [],
      checkedData: '',
      checkedCode: [],
      layerNo: '' //已选择的层级
    }
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      if (this.checkedAll) {
        this.$refs.treeCheckbox.setCheckedNodes(this.treeData)
      }
      console.log(this.defaultCheckedKeys)
      if (this.defaultCheckedKeys.length > 0) {
        // 调用组件默认触发回调函数
        this.$emit('node-click-callback', this.defaultCheckedKeys, null)
      }
    })
  },
  watch: {
    treeData: function (val) {
      console.log(1)
      if (val.length == 0) return
      this.setExpandKeys()
    }
  },
  methods: {
    setCheckedData() {
      // console.log(this.defaultCheckedKeys[0]);
    },
    getCheckedKeys(curCheckCode) {
      this.checkedCode = this.$refs.treeCheckbox.getCheckedKeys()
      // console.log( this.checkedCode, curCheckCode)
      this.$emit('node-click-callback', this.checkedCode, curCheckCode)
    },
    nodeClickCallback(data, node, self) {
      // console.log(data, node, self);
      if (this.isLateralCheck) {
        if (node.data.layerNo != this.layerNo) {
          this.$refs.treeCheckbox.setCheckedKeys([])
          this.layerNo = node.data.layerNo
        }
      }
      let checked = node.checked
      node.checked = !checked
      if (this.checkChildNode) {
        this.setChildNodesChecked(node, checked)
      }
      let curCheckCode = node.data.code
      this.getCheckedKeys(curCheckCode)
      // setTimeout(() => {
      // },500)
    },
    setChildNodesChecked(node, checked) {
      if (node.childNodes.length > 0) {
        node.childNodes.forEach(item => {
          item.checked = !checked
          this.setChildNodesChecked(item, checked)
        })
      } else {
        return
      }
    },
    setExpandKeys() {
      let that = this
      if (that.expandAll) return
      function getKeys(data) {
        data.forEach(item => {
          if (item.layerNo < that.expandedLevel + 1) {
            that.defaultExpandedKeys.push(item.code)
          }
          if (item.children && item.children.length) {
            getKeys(item.children)
          }
        })
      }

      getKeys(that.treeData)
    }
  }
}
</script>

<style scoped lang="scss">
.tree_wrap {
  width: 100%;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
:deep(.el-tree-node__expand-icon) {
  // display: none;
}
:deep(.el-tree-node__content > label.el-checkbox) {
  pointer-events: none;
}

.tree_comp_checkbox_wrap {
  :deep(.custom-tree-node) {
    width: 75%;
  }
  :deep(.tree_node) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
