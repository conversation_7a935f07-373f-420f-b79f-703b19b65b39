<template>
  <div class="tree_wrap tree_comp_checkbox_wrap">
    <el-tree
      :data="treeData"
      show-checkbox
      :node-key="nodeKey"
      :default-expand-all="expandAll"
      check-strictly
      :default-checked-keys="defaultCheckedKeys"
      :default-expanded-keys="computedExpandedKeys"
      :expand-on-click-node="false"
      ref="treeCheckboxRef"
      :props="{ label: labelKey }"
      @check="handleCheck"
      @node-click="nodeClickCallback"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span class="tree_node" :title="node.label">
            {{ node.label }}
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'

const props = defineProps({
  treeData: {
    type: Array,
    default: () => []
  },
  // 节点唯一key，点击节点需要返回的字段
  nodeKey: {
    type: String,
    default: 'code'
  },
  // 节点显示文本的字段
  labelKey: {
    type: String,
    default: 'value'
  },
  checkedAll: {
    type: Boolean,
    default: false
  },
  defaultCheckedKeys: {
    type: Array,
    default: () => []
  },
  // 默认展开的节点 nodeKey
  defaultExpandedKeys: {
    type: Array,
    default: () => []
  },
  // 默认展开所有的节点
  // 若需要 默认展开节点 请传入false
  expandAll: {
    type: Boolean,
    default: true
  },
  // 需要默认展开的层级,若使用此配置需把expandAll设为false
  // 默认3级
  expandedLevel: {
    type: Number,
    default: 3
  },
  // 点击父节点是否选中子节点，默认开启
  checkChildNode: {
    type: Boolean,
    default: true
  },
  // 复选且只能选择平级节点，默认不开启
  isLateralCheck: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['node-click-callback'])

// 响应式数据
const treeCheckboxRef = ref(null)
const maxexpandId = ref(1000)
const checkedId = ref([])
const checkedData = ref('')
const checkedCode = ref([])
const layerNo = ref('') // 已选择的层级
const expandedKeys = ref([...props.defaultExpandedKeys])

// 计算属性 - 动态展开的节点keys
const computedExpandedKeys = computed(() => {
  return expandedKeys.value
})

// 方法定义
const setCheckedData = () => {
  // console.log(props.defaultCheckedKeys[0]);
}

const getCheckedKeys = (curCheckCode) => {
  checkedCode.value = treeCheckboxRef.value.getCheckedKeys()
  // console.log(checkedCode.value, curCheckCode)
  emit('node-click-callback', checkedCode.value, curCheckCode)
}

// 处理复选框选中事件
const handleCheck = (data, checkInfo) => {
  // console.log('check event:', data, checkInfo)

  // 同级节点限制逻辑
  if (props.isLateralCheck) {
    if (data.layerNo != layerNo.value) {
      // 如果选择了不同层级的节点，清空之前的选择
      treeCheckboxRef.value.setCheckedKeys([])
      layerNo.value = data.layerNo
      // 重新选中当前节点
      nextTick(() => {
        treeCheckboxRef.value.setCheckedKeys([data[props.nodeKey]])
      })
    }
  }

  // 父子节点联动处理
  if (props.checkChildNode) {
    const currentCheckedKeys = checkInfo.checkedKeys
    const nodeKey = data[props.nodeKey]
    const isChecked = currentCheckedKeys.includes(nodeKey)

    if (isChecked) {
      // 选中时，同时选中所有子节点
      const childKeys = getAllChildKeysFromData(data)
      const newCheckedKeys = [...new Set([...currentCheckedKeys, ...childKeys])]
      treeCheckboxRef.value.setCheckedKeys(newCheckedKeys)
    } else {
      // 取消选中时，同时取消所有子节点
      const childKeys = getAllChildKeysFromData(data)
      const keysToRemove = [nodeKey, ...childKeys]
      const newCheckedKeys = currentCheckedKeys.filter(key => !keysToRemove.includes(key))
      treeCheckboxRef.value.setCheckedKeys(newCheckedKeys)
    }
  }

  // 触发回调
  nextTick(() => {
    const curCheckCode = data[props.nodeKey]
    getCheckedKeys(curCheckCode)
  })
}

const nodeClickCallback = (data, node, self) => {
  // console.log('node click:', data, node, self);

  // 非复选框区域的点击事件，手动切换复选框状态
  const currentCheckedKeys = treeCheckboxRef.value.getCheckedKeys()
  const nodeKey = data[props.nodeKey]
  const isCurrentlyChecked = currentCheckedKeys.includes(nodeKey)

  // 同级节点限制逻辑
  if (props.isLateralCheck) {
    if (data.layerNo != layerNo.value) {
      treeCheckboxRef.value.setCheckedKeys([])
      layerNo.value = data.layerNo
    }
  }

  if (isCurrentlyChecked) {
    // 当前已选中，点击取消选中
    if (props.checkChildNode) {
      // 取消选中时，同时取消所有子节点
      const childKeys = getAllChildKeysFromData(data)
      const keysToRemove = [nodeKey, ...childKeys]
      const newCheckedKeys = currentCheckedKeys.filter(key => !keysToRemove.includes(key))
      treeCheckboxRef.value.setCheckedKeys(newCheckedKeys)
    } else {
      // 只取消当前节点
      const newCheckedKeys = currentCheckedKeys.filter(key => key !== nodeKey)
      treeCheckboxRef.value.setCheckedKeys(newCheckedKeys)
    }
  } else {
    // 当前未选中，点击选中
    if (props.checkChildNode) {
      // 选中时，同时选中所有子节点
      const childKeys = getAllChildKeysFromData(data)
      const newCheckedKeys = [...new Set([...currentCheckedKeys, nodeKey, ...childKeys])]
      treeCheckboxRef.value.setCheckedKeys(newCheckedKeys)
    } else {
      // 只选中当前节点
      const newCheckedKeys = [...currentCheckedKeys, nodeKey]
      treeCheckboxRef.value.setCheckedKeys(newCheckedKeys)
    }
  }

  // 触发回调
  nextTick(() => {
    getCheckedKeys(nodeKey)
  })
}

// 获取所有子节点的keys (从树节点对象)
const getAllChildKeys = (node) => {
  const keys = []

  const collectKeys = (currentNode) => {
    if (currentNode.childNodes && currentNode.childNodes.length > 0) {
      currentNode.childNodes.forEach(child => {
        keys.push(child.data[props.nodeKey])
        collectKeys(child)
      })
    }
  }

  collectKeys(node)
  return keys
}

// 获取所有子节点的keys (从数据对象)
const getAllChildKeysFromData = (data) => {
  const keys = []

  const collectKeys = (item) => {
    if (item.children && item.children.length > 0) {
      item.children.forEach(child => {
        keys.push(child[props.nodeKey])
        collectKeys(child)
      })
    }
  }

  collectKeys(data)
  return keys
}

// 保留原方法以防其他地方使用（已废弃）
const setChildNodesChecked = (node, checked) => {
  if (node.childNodes.length > 0) {
    node.childNodes.forEach(item => {
      item.checked = !checked
      setChildNodesChecked(item, checked)
    })
  } else {
    return
  }
}

const setExpandKeys = () => {
  if (props.expandAll) return

  const getKeys = (data) => {
    data.forEach(item => {
      if (item.layerNo < props.expandedLevel + 1) {
        expandedKeys.value.push(item.code)
      }
      if (item.children && item.children.length) {
        getKeys(item.children)
      }
    })
  }

  getKeys(props.treeData)
}

// 监听器
watch(
  () => props.treeData,
  (val) => {
    console.log(1)
    if (val.length == 0) return
    setExpandKeys()
  }
)

// 生命周期
onMounted(() => {
  nextTick(() => {
    if (props.checkedAll) {
      treeCheckboxRef.value.setCheckedNodes(props.treeData)
    }
    console.log(props.defaultCheckedKeys)
    if (props.defaultCheckedKeys.length > 0) {
      // 调用组件默认触发回调函数
      emit('node-click-callback', props.defaultCheckedKeys, null)
    }
  })
})
</script>

<style scoped lang="scss">
.tree_wrap {
  width: 100%;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
/* :deep(.el-tree-node__expand-icon) {
  display: none;
} */
:deep(.el-tree-node__content > label.el-checkbox) {
  pointer-events: none;
}

.tree_comp_checkbox_wrap {
  :deep(.custom-tree-node) {
    width: 75%;
  }
  :deep(.tree_node) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
