<template>
  <div class="basic_box">
    <div>
      <selectCheckData
        :tableData="tableData"
        :type="type"
        :numLength="numLength"
        :max-height="500"
        @tableDataFn="tableDataFun"
      ></selectCheckData>
    </div>
    <div class="btn_box">
      <el-button type="primary" class="page_new_confirm_btn" @click="updateTargetBtn">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import selectCheckData from '@/components/talent/selectTableCheck/selectCheckDataNew.vue'

const props = defineProps({
  types: String,
  dataFrom: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['getChildData'])

const btnFlag = ref(true)
const type = ref('radio') // 单选
const numLength = ref(1) // 固定两个长度
const tableData = reactive({
  tableTitle: [],
  data: []
})
const kpiCodeList = ref([])
const checkTargetSignList = ref([])
const checkTargetDisabledSignList = ref([])
const checkArr = ref([])

watch(
  () => props.dataFrom,
  () => {
    console.log('watch', props.dataFrom)

    btnFlag.value = true
    relatePostKpiFun()
  },
  { deep: true, immediate: true }
)

function updateTargetBtn() {
  if (btnFlag.value) {
    checkArr.value = []
    for (let i = 0; i < tableData.data.length; i++) {
      checkArr.value.push({
        resultList: tableData.data[i].selectedCodes
      })
    }
    if (isFlag(checkArr.value)) {
      ElMessage.warning('评价内容不能为空!')
    } else {
      let paramList = []
      for (let i = 0; i < tableData.data.length; i++) {
        paramList.push({
          objectId: tableData.data[i].code,
          relationType: tableData.data[i].relationCode,
          optionNbr: tableData.data[i].selectedCodes
        })
      }
      btnFlag.value = false
      emit('getChildData', paramList)
    }
  }
}

// 判断是否有空数组
function isFlag(arr) {
  for (let i = 0; i < arr.length; i++) {
    if (!arr[i].resultList || arr[i].resultList.length == 0) {
      return true
    }
  }
  return false
}

function tableDataFun(data) {
  tableData.data = data
}

// 获取表头并初始化数据
function relatePostKpiFun() {
  tableData.tableTitle = []
  tableData.data = []
  kpiCodeList.value = []
  checkTargetSignList.value = []
  checkTargetDisabledSignList.value = []

  // 初始表头固定部分及表格数据
  if (props.types == 'quality' || props.types == 'performance') {
    tableData.tableTitle = [
      {
        label: '姓名',
        prop: 'name',
        canCheck: false,
        width: 80,
        fixed: 'left'
      }
    ]
  } else {
    tableData.tableTitle = [
      {
        label: '姓名',
        prop: 'index',
        canCheck: false,
        width: 80,
        fixed: 'left'
      }
    ]
  }

  // 表头数据
  if (props.dataFrom.resultList) {
    for (let i = 0; i < props.dataFrom.resultList.length; i++) {
      tableData.tableTitle.push({
        label: props.dataFrom.resultList[i].name,
        prop: 'targetName-' + (i + 1),
        canCheck: true,
        disabledSign: 'targetKpiCode-' + (i + 1),
        code: props.dataFrom.resultList[i].code,
        width: 140
      })
      checkTargetSignList.value.push('targetName-' + (i + 1))
      checkTargetDisabledSignList.value.push('targetKpiCode-' + (i + 1))
      kpiCodeList.value.push(props.dataFrom.resultList[i].code)
    }
  }

  // 表格数据
  if (props.dataFrom.itemList) {
    for (let j = 0; j < props.dataFrom.itemList.length; j++) {
      const row = {
        index: j + 1,
        name: props.dataFrom.itemList[j].name,
        code: props.dataFrom.itemList[j].code,
        relation: props.dataFrom.itemList[j].relation,
        relationCode: props.dataFrom.itemList[j].relationCode,
        selectedCodes: props.dataFrom.itemList[j].selectedCodes,
        kpiCodeList: kpiCodeList.value,
        optionalCodes: props.dataFrom.itemList[j].optionalCodes
      }
      // 将所有表格初始为未被选中 初始为可勾选
      for (let i = 0; i < kpiCodeList.value.length; i++) {
        row[checkTargetSignList.value[i]] = false
        row[checkTargetDisabledSignList.value[i]] = false
      }
      // 初始化不可点击表格
      if (row.optionalCodes && row.optionalCodes.length > 0) {
        for (let k = 0; k < row.optionalCodes.length; k++) {
          for (let h = 0; h < kpiCodeList.value.length; h++) {
            if (row.optionalCodes[k] == kpiCodeList.value[h]) {
              row[checkTargetDisabledSignList.value[h]] = true
            }
          }
        }
      }
      // 变更所有表格选中状态
      if (props.dataFrom.itemList[j].selectedCodes) {
        for (let k = 0; k < props.dataFrom.itemList[j].selectedCodes.length; k++) {
          let checkColumnn
          if (kpiCodeList.value.indexOf(props.dataFrom.itemList[j].selectedCodes[k]) !== -1) {
            checkColumnn = kpiCodeList.value.indexOf(props.dataFrom.itemList[j].selectedCodes[k])
            row['targetName-' + (checkColumnn + 1)] = true
          }
        }
      }
      tableData.data.push(row)
    }
  }
}
</script>

<style scoped lang="scss">
.basic_box {
  width: 100%;
  .btn_box {
    width: 100%;
    height: 50px;
    text-align: center;
  }
}
</style>
