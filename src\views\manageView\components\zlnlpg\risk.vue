<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'
defineOptions({ name: 'risk' })
const reportList = ref([
  { id: 1, title: '20250201 销售预测核心能力测评报告（单人测评）', expand: false },
  { id: 2, title: '20250201 销售竞争力测评报告（多人测评）', expand: true }
])

const toggleViewReport = item => {
  item.expand = !item.expand
}
const columns = ref([
  {
    label: '能力短板',
    prop: 'name'
  },
  {
    label: '关联的典型战略目标',
    prop: 'target'
  },
  {
    label: '影响概率（1~5分）',
    prop: 'probability'
  },
  {
    label: '影响周期',
    prop: 'period'
  }
])

const tableData = ref([
  {
    name: '采购招投标',
    target: '成本优化与供应商质量保障',
    probability: '4.5',
    period: '中期'
  },
  {
    name: '采购合同管理',
    target: '风险控制与合规经营',
    probability: '4.8',
    period: '长期'
  },
  {
    name: '采购结算',
    target: '现金流效率提升',
    probability: '4.0',
    period: '短期'
  },
  {
    name: '采购合规管理',
    target: '法律风险规避',
    probability: '4.7',
    period: '长期'
  },
  {
    name: '采购管理委员会',
    target: '决策质量与资源统筹能力',
    probability: '3.9',
    period: '中期'
  },
  {
    name: '采购询比价',
    target: '采购成本竞争力',
    probability: '4.2',
    period: '短期'
  },
  {
    name: '采购组织管理',
    target: '跨部门协同效率',
    probability: '3.5',
    period: '长期'
  }
])
const chooseTableData = ref(tableData.value[0])
const tableRef = ref(null)
const handCurrentChange = val => {
  console.log(val)
  chooseTableData.value = val
}

onMounted(() => {
  tableRef.value[0]?.simplenessTableRef?.setCurrentRow(tableData.value[0])
})

const shortColumns = ref([
  {
    label: '影响维度',
    prop: 'name'
  },
  {
    label: '量化指标',
    prop: 'index'
  },
  {
    label: '影响值',
    prop: 'value'
  }
])
const shortData = ref([
  {
    name: '采购成本',
    index: '单品类采购价高于行业基准比例',
    value: '+8%~12%'
  },
  {
    name: '交付稳定性',
    index: '供应商能力不足导致的交付延迟率',
    value: '平均延迟7.3天'
  },
  {
    name: '合规风险',
    index: '年度围标/串标事件数量',
    value: '2起/年'
  }
])

const longColumns = ref([
  {
    label: '影响维度',
    prop: 'name'
  },
  {
    label: '影响路径',
    prop: 'path'
  }
])

const longData = ref([
  {
    name: '市场竞争力',
    path: '成本劣势 → 产品定价竞争力下降 → 市场份额流失'
  },
  {
    name: '供应商生态',
    path: '固化供应商池 → 创新能力枯竭 → 技术迭代滞后'
  }
])
</script>
<template>
  <div class="report-page">
    <div class="head-title">查看如下能力测评对应的能力短板风险：</div>
    <div class="report-content">
      <div class="report-item" v-for="item in reportList" :key="item.id">
        <div class="item-head">
          <div class="title">{{ item.title }}</div>
          <div class="view-btn" @click="toggleViewReport(item)">{{ item.expand ? '收起' : '查看报告' }}</div>
        </div>
        <div class="report-main" v-if="item.expand">
          <div class="report-page-content">
            <div class="page-title-line">
              <span>主要的核心能力短板及对战略目标的影响</span>
              <div class="line"></div>
              <div class="main-color">选择可查看详情</div>
            </div>
            <SimplenessTable
              ref="tableRef"
              :highlightCurrentRow="true"
              @current-change="handCurrentChange"
              :roundBorder="false"
              :columns="columns"
              :data="tableData"
            >
            </SimplenessTable>
            <div class="page-title-line">
              <span>能力短板影响</span>
              <div class="line"></div>
              <div class="main-color">{{ chooseTableData.name }}</div>
            </div>
            <div class="section border">
              <div class="section-title">关联逻辑</div>
              <div class="section-desc">
                低效招标导致供应商选择偏离最优性价比，长期锁定低质供应商推高隐性成本（如售后维修、交付延迟）
              </div>
            </div>
            <div class="section border">
              <div class="section-title">典型表现</div>
              <div class="section-desc">招标流程冗长、评标标准模糊、供应商池固化、围标串标风险高、数字化工具缺失</div>
            </div>
            <div class="flex-box">
              <div class="section border">
                <div class="section-title">直接影响（短期-中期）</div>
                <SimplenessTable :columns="shortColumns" :data="shortData"></SimplenessTable>
              </div>
              <div class="section border">
                <div class="section-title">间接影响（长期）</div>
                <SimplenessTable :columns="longColumns" :data="longData"></SimplenessTable>
              </div>
            </div>
            <div class="section border">
              <div class="section-title">典型案例</div>
              <div class="section-desc">
                <b>成本维度：</b>某制造业企业因招标流程不透明，连续3年选择同一供应商，采购单价高于市场基准8-12%。
              </div>
              <div class="section-desc">
                <b>质量维度：</b>供应商技术能力不足导致设备故障率提升15%，年度维修成本增加300万元。
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.report-page {
  .head-title {
    font-size: 16px;
    color: #888888;
    line-height: 20px;
    margin-bottom: 16px;
  }
}
.report-content {
  .report-item {
    margin-bottom: 15px;

    .item-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 26px 28px;
      background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      margin-bottom: 15px;
      .title {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
      }
      .view-btn {
        font-size: 14px;
        color: #fff;
        cursor: pointer;
        line-height: 30px;
        background: #40a0ff;
        border-radius: 3px 3px 3px 3px;
        text-align: center;
        padding: 0 22px;
      }
    }
    .report-main {
      display: flex;
      flex-flow: row nowrap;
      gap: 18px;
      .report-page-content {
        flex: 1;
        background: #ffffff;
        box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
        border-radius: 8px 8px 8px 8px;
        padding: 0px 30px 20px;
        .title {
          font-weight: 500;
          font-size: 16px;
          color: #53a9f9;
          margin-bottom: 20px;
        }
        .page-title-line {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 20px;
          margin-top: 30px;
          .line {
            flex: 1;
            height: 1px;
            background: #e5e5e5;
          }
          .ai-btn {
            width: 73px;
            text-align: center;
            line-height: 30px;
            background: #e1f3ff;
            border-radius: 30px;
            font-weight: 500;
            font-size: 16px;
            color: #40a0ff;
            cursor: pointer;
          }
        }
      }
    }
  }
}
.section {
  flex: 1;
  padding: 20px;
  margin-bottom: 20px;
  &-title {
    font-weight: 600;
    font-size: 16px;
    color: #3d3d3d;
    line-height: 16px;
    margin-bottom: 12px;
  }
  &-desc {
    font-size: 14px;
    color: #666666;
    line-height: 28px;
  }
}
.border {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #c6dbf3;
}
</style>
