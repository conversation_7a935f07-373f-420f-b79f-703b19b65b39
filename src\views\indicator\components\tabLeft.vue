<script setup>
defineOptions({ name: 'tabLeft' })
const props = defineProps({
  title: {
    type: String,
    required: false
  },
  data: {
    type: Array,
    required: true
  },
  active: {
    type: String,
    required: true
  }
})
const emits = defineEmits(['tab-click'])

const handleOpen = (key, keyPath) => {
  console.log(key, keyPath, 111)
}
const handleClose = (key, keyPath) => {
  console.log(key, keyPath, 222)
}
const handleSelect = (key, keyPath) => {
  console.log(key, keyPath, '-------------->')
  emits('tab-click', { key, keyPath })
}
</script>
<template>
  <div class="tab-card">
    <div class="tab-title" v-if="title">{{ title }}</div>
    <el-menu
      class="el-menu-vertical-demo"
      background-color="transparent"
      :default-active="active"
      @open="handleOpen"
      @close="handleClose"
      @select="handleSelect"
    >
      <div class="" v-for="item in data" :key="item.code">
        <div v-if="item.children && item.children.length">
          <el-sub-menu :index="item.code">
            <template #title>
              <span>{{ item.name }}</span>
            </template>
            <el-menu-item :index="items.code" v-for="items in item.children" :key="items.code">
              <div :title="items.name" class="sub-item">{{ items.name }}</div>
            </el-menu-item>
          </el-sub-menu>
        </div>
        <el-menu-item :index="item.code" v-else>
          <div class="sub-menu" :title="item.name">
            {{ item.name }}
          </div>
        </el-menu-item>
      </div>
    </el-menu>
  </div>
</template>
<style lang="scss" scoped>
.tab-card {
  width: 100%;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  padding: 20px;
  background: linear-gradient(224deg, #d0e4f9 0%, rgba(195, 230, 255, 0.6) 100%);
  border-radius: 8px;
  .tab-title {
    padding-bottom: 20px;
    font-size: 14px;
    color: #333333;
  }
  :deep(.el-menu-vertical-demo) {
    border-right: none;
    .el-sub-menu .el-sub-menu__icon-arrow {
      display: none;
    }
    .el-sub-menu {
      width: 100%;
      &.is-active {
        .el-sub-menu__title {
          background: #83c1ff;
          color: #fff;
          border-color: #83c1ff;
        }
      }
      .el-menu-item {
        margin: 20px 0;
      }
    }
    .el-sub-menu__title {
      @include flex-center(row, center, center);
      width: 100%;
      height: 35px;
      padding: 0;
      margin-bottom: 10px;
      text-align: center;
      background: #f0f9ff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #a5c1dc;
      color: #6c757e;
    }
    .el-menu-item {
      height: auto;
      padding: 0;
      font-size: 14px;
      color: #6c757e;
      line-height: 20px;
      margin-bottom: 10px;
      .sub-item {
        width: 140px;
        text-align: center;
        @include text-ellipsis(1);
      }
      .sub-menu {
        // @include flex-center(row, center, center);
        width: 100%;
        height: 35px;
        line-height: 35px;
        padding: 0;
        text-align: center;
        background: #f0f9ff;
        border-radius: 5px 5px 5px 5px;
        border: 1px solid #a5c1dc;
        color: #6c757e;
        @include text-ellipsis(1);
      }
      &.is-active {
        color: #40a0ff;
        .sub-menu {
          background: #83c1ff;
          color: #fff;
          border-color: #83c1ff;
        }
      }
      &:hover {
        background-color: transparent;
        color: #40a0ff;
      }
    }
  }
}
</style>
