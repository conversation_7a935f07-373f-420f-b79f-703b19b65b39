<template>
  <div class="prior public">
    <div class="table-main">
      <el-table ref="tableDataRef" :data="tableData" highlight-current-row style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="name" label="指标名称" width="180" />
        <el-table-column prop="type" label="目标类别" width="180" />
        <el-table-column prop="time" label="目标期间" />
        <el-table-column prop="unit" label="指标单位" />
        <el-table-column prop="person" label="责任人" />
        <el-table-column prop="target" label="指标目标" />
        <el-table-column prop="practical" label="实际表现" />
        <el-table-column prop="rate" label="达成率" />
        <el-table-column prop="gap" label="差距" />
        <el-table-column>
          <template #default="scope">
            <div
              class="btn"
              @click="
                openAi(
                  `${scope.row.name}、${scope.row.type}、${scope.row.time}、指标目标${scope.row.target}、实际表现${scope.row.practical}`
                )
              "
            >
              AI解读
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 已选择 -->
    <div class="active">
      <div class="title">
        <div class="text">已选指标：</div>
        <div class="name">库存周转天数</div>
      </div>
      <div class="page-title-line">关联能力</div>
      <el-table ref="tableDataRef2" highlight-current-row :data="table1" :stripe="true" style="width: 100%">
        <el-table-column type="index" align="center" label="序号" width="80" />
        <el-table-column prop="one" label="所属一级能力" />
        <el-table-column prop="two" label="二级能力" />
        <el-table-column prop="dep" align="center" label="相关性" />
        <el-table-column prop="relevancy" label="关联逻辑" width="700" />
        <el-table-column prop="manifestation" align="center" label="能力表现" width="80">
          <template #default="scope">
            <div class="num" :style="{ background: scope.row.manifestation >= 60 ? '#40D476' : '#FF5E4C' }">
              {{ scope.row.manifestation }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="page-title-line">能力解码（库存动态分析）</div>
      <div class="chart-box h-[300px]">
        <EChartsBar :options="chartsOptions"></EChartsBar>
      </div>
      <div class="page-title-line">能力解码详情（主要影响）</div>
      <div class="decode">
        <div class="paragraph">
          <a>1、数据采集与分析脱节，影响库存状态实时监控：</a>
          流程端到端不闭环会导致库存数据采集、传输、分析各环节缺乏有效衔接，无法实时获取准确的库存出入库数据、在途库存数据及滞销
          / 畅销品动态，使得库存周转天数、安全库存阈值等关键指标计算失真，难以精准识别库存积压或短缺风险。
        </div>
        <div class="paragraph">
          <a>2、策略制定与执行断裂，导致库存优化措施失效：</a>
          缺乏闭环管理会使库存分析结果无法有效转化为采购计划调整、生产排期优化或促销策略制定，例如根据历史数据制定的补货策略在市场需求突变时得不到及时修正，导致采购量与实际需求偏离，引发库存过剩或断货，增加仓储成本与机会损失。
        </div>
        <div class="paragraph">
          <a>3、效果评估缺失，无法形成持续改进机制：</a>
          没有闭环的流程监控与复盘，难以对库存动态分析模型的有效性（如 ABC
          分类法、需求预测模型）及策略执行效果（如库存周转率提升率、缺货率下降目标）进行量化评估，导致库存管理问题重复发生，无法通过迭代优化适应市场变化，长期影响供应链响应速度与客户满意度。
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
const openAi = inject('openAi')
const table1 = [
  {
    one: '仓储物流管理',
    two: '库存动态分析',
    dep: '高',
    relevancy:
      '库存动态分析能力通过实时监控库存状态（数量、库龄、周转率），快速识别积压风险并触发调整动作。实时数据反馈机制可缩短信息滞后周期，减少因数据失真导致的过量备货，直接降低库存持有天数。',
    manifestation: '66'
  },
  {
    one: '供应链产销协同',
    two: '需求预测与精准化管理',
    dep: '极高',
    relevancy:
      '需求预测准确性是库存计划合理性的核心前提。精准预测通过匹配供应节奏与市场需求波动，避免因高估/低估需求导致的库存积压或短缺。动态需求管理模型（如滚动预测）可持续优化补货策略。',
    manifestation: '55'
  },
  {
    one: '供应链产销协同',
    two: 'CPFR（协同计划、预测与补货）',
    dep: '高',
    relevancy:
      '跨企业协同机制打破信息孤岛，通过共享需求计划、库存可视性数据，降低供应链牛鞭效应。协同补货协议可减少安全库存冗余，缩短从需求触发到补货执行的响应周期。',
    manifestation: '69'
  },
  {
    one: '数字化制造',
    two: '生产计划与柔性执行',
    dep: '中高',
    relevancy:
      '柔性生产模式（如小批量生产、动态排产）减少生产环节的在制品库存滞留。敏捷响应能力可压缩从订单接收到产品交付的周期，降低成品库存积压风险。',
    manifestation: '69'
  },
  {
    one: '仓储物流管理',
    two: '智能仓储技术应用',
    dep: '高',
    relevancy:
      '自动化仓储系统（如智能分拣、路径优化）加速库存流转效率。实时库存可视化与预警机制（如保质期监控）减少呆滞库存，优化库内操作对库存周转的负面影响。',
    manifestation: '52'
  },
  {
    one: '供应链产销协同',
    two: '供应链计划集成',
    dep: '中高',
    relevancy:
      '集成化计划系统打通销售、生产、采购全链路数据，实现全局库存策略对齐。通过产销协同平衡供需矛盾，避免因局部优化导致的整体库存冗余。',
    manifestation: '67'
  },
  {
    one: '仓储物流管理',
    two: '物流网络设计',
    dep: '中',
    relevancy:
      '多级仓储布局优化（如区域仓、前置仓）缩短补货半径，降低单点库存压力。动态路由规划减少在途库存时间，提升库存分布合理性。',
    manifestation: '64'
  },
  {
    one: '采购与供应商管理',
    two: '采购执行与合同管理',
    dep: '中',
    relevancy:
      '经济采购批量模型与供应商协同协议（如VMI）控制采购节奏，避免集中到货导致的库存峰值。合同条款优化（如分批发货）平滑库存波动曲线。',
    manifestation: '69'
  },
  {
    one: '供应链产销协同',
    two: '全链路成本优化',
    dep: '高',
    relevancy:
      '库存持有成本与缺货成本的平衡模型驱动精细化管理决策。通过SKU分级、库存水位动态调整，在保障服务水平前提下最小化资金占用。',
    manifestation: '52'
  },
  {
    one: '仓储物流管理',
    two: '物流数据分析与决策',
    dep: '中高',
    relevancy:
      '物流数据挖掘识别运输瓶颈（如高频延误线路），优化配送效率以缩短库存周转周期。数据驱动的库存策略调整（如安全库存动态计算）提升响应灵活性。',
    manifestation: '64'
  }
]
const tableData = [
  {
    name: '供应商交付准时率',
    type: '年度目标',
    time: '2024年',
    unit: '',
    person: '',
    target: '≥98%',
    practical: '95%',
    rate: '',
    gap: ''
  },
  {
    name: '库存周转天数',
    type: '年度目标',
    time: '2024年',
    unit: '',
    person: '',
    target: '≤80%',
    practical: '75%',
    rate: '',
    gap: ''
  },
  {
    name: '供应链协同指数',
    type: '年度目标',
    time: '2024年',
    unit: '',
    person: '',
    target: '≥85分',
    practical: '82.5分',
    rate: '',
    gap: ''
  },
  {
    name: '替代供应商储备率',
    type: '月度目标',
    time: '2025-04',
    unit: '',
    person: '',
    target: '≥30%',
    practical: '35%',
    rate: '',
    gap: ''
  },
  {
    name: '物流成本占比',
    type: '年度目标',
    time: '2025-04',
    unit: '',
    person: '',
    target: '≤4.5%',
    practical: '4.8%',
    rate: '',
    gap: ''
  }
]
const tableDataRef = ref(null)
const tableDataRef2 = ref(null)
onMounted(() => {
  tableDataRef.value.setCurrentRow(tableData[1])
  tableDataRef2.value.setCurrentRow(table1[0])
})

const chartData = ref([
  {
    name: '流程端到端闭环',
    value: '50'
  },
  {
    name: '输出文档',
    value: '55'
  },
  {
    name: '业务规则',
    value: '56'
  },
  {
    name: '业务 KPI',
    value: '48'
  },
  {
    name: '组织设置',
    value: '55'
  },
  {
    name: '岗位角色职责',
    value: '60'
  },
  {
    name: '岗位协同 RACI',
    value: '63'
  },
  {
    name: '组织岗位 KPI',
    value: '55'
  },
  {
    name: '人员动力',
    value: '54'
  },
  {
    name: '人员能力要求',
    value: '51'
  },
  {
    name: '人员能力评估',
    value: '49'
  },
  {
    name: '能力培训',
    value: '60'
  },
  {
    name: '系统赋能',
    value: '60'
  },
  {
    name: '数据治理',
    value: '65'
  },
  {
    name: '系统集成',
    value: '65'
  },
  {
    name: '系统改善及规划',
    value: '47'
  }
])

const chartsOptions = ref({
  xAxisData: chartData.value.map(item => item.name),
  xAxis: {
    axisLabel: {
      interval: 0, // 强制显示所有标签
      rotate: 45, // 标签倾斜度 -90 至 90 默认为0
      margin: 10, // 刻度标签与轴线之间的距离
      fontSize: 12 // 刻度标签的文字大小
    }
  },
  series: [
    {
      type: 'bar',
      data: chartData.value.map(item => item.value),
      showBackground: true,
      itemStyle: {
        color: '#53B8FF'
      },
      label: {
        show: true
      }
    }
  ]
})
</script>
<style lang="scss" scoped>
@import '../indicatorImprove.scss';
</style>
