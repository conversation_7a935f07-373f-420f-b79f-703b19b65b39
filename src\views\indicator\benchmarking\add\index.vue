<script setup>
defineOptions({ name: 'benchmarkingAdd' })
const stepList = ref([
  {
    name: '选择对标主题',
    step: 1
  },
  {
    name: '确认对标行业',
    step: 2
  },
  {
    name: '完善对标数据',
    step: 3
  },
  {
    name: '查看对标报告',
    step: 4
  }
])
const step = ref(1)

const next = () => {
  if (step.value > 3) {
    return
  }
  step.value += 1
}

const prev = () => {
  if (step.value < 2) {
    return
  }
  step.value -= 1
}

/**------------------------ 第一步 begin ------------------------------------- */
const themeList = [
  {
    name: '人力资源效能',
    id: 0
  },
  {
    name: '财务健康度',
    id: 1
  },
  {
    name: '运营效率',
    id: 2
  },
  {
    name: '研创新发投入与产出',
    id: 3
  },
  {
    name: '行业增长与市场竞争',
    id: 4
  },
  {
    name: '供应链管理对标',
    id: 5
  }
]
const themeId = ref(0)
const changeTheme = id => {
  themeId.value = id
}

const infoList = [
  {
    name: '人力资本回报率（HCROI）对标：',
    desc: '通过量化单位人力成本创造的利润或营收，直观反映薪酬投入与价值产出的匹配程度。企业可据此识别高成本低效能岗位，针对性优化薪酬结构、提升核心岗位效能，在控制人力成本的同时挖掘人效增长潜力，实现 “降本” 与 “增效” 的双向平衡。 '
  },
  {
    name: '人均产能增长对标：',
    desc: '聚焦人均营收 / 利润的年均增速，衡量组织扩张中的效率提升能力。若增速滞后于行业，企业可据此排查流程冗余、技能错配等问题，精准制定人员结构优化、自动化升级或产能规划策略，确保业务规模扩张与人力资源效率提升同步，避免 “规模不经济”。 '
  },
  {
    name: '人才结构健康度对标：',
    desc: '从年龄、学历、职级等维度评估人才梯队合理性，防范关键岗位断层或结构性冗余。企业可针对性制定校园招聘、内部培养或跨界引进计划，填补技术研发、管理储备等领域的人才缺口，确保人才结构与战略目标动态匹配，避免因 “人力断层” 导致的发展瓶颈。 '
  },
  {
    name: '人力成本对标：',
    desc: '通过薪酬费用占营收的比例，评估人力成本投入的合理性。若成本占比过高，企业可通过自动化改造、工时优化等手段控本；若显著低于行业均值，则需警惕人才投入不足对长期效能的影响，平衡短期成本控制与长期人才储备，保障组织持续发展能力。'
  },
  {
    name: '员工人均薪酬对标：',
    desc: '通过对比员工平均薪酬与行业水平，评估企业薪酬竞争力。薪酬显著低于市场时，可及时调整策略以吸引和保留人才；若薪酬高于行业却伴随人效低下，则需审视薪酬与绩效挂钩机制，推动薪酬资源向高价值岗位倾斜，在保障市场吸引力的同时避免成本浪费。 '
  },
  {
    name: '薪酬与业绩匹配度对标：',
    desc: '聚焦核心岗位薪酬与其业绩贡献的偏离度，避免 “高薪低效” 或激励不足。企业可据此优化绩效权重、实施差异化激励，使薪酬与关键指标（如利润、产能、技术突破）强关联，激发核心人才创造力，确保薪酬投入切实转化为业务增长动力。'
  }
]
/**------------------------ 第一步 end ------------------------------------- */

/**------------------------ 第二步 begin ------------------------------------- */
const activeName = ref('1')
const handleClick = tab => {
  activeName.value = tab.name
}

const industryOptions = [
  {
    value: '1',
    label: '电子',
    children: [
      {
        value: '1-1',
        label: '电子化学品'
      },
      {
        value: '1-2',
        label: '光学广电子'
      },
      {
        value: '1-3',
        label: '消费电子'
      }
    ]
  },
  {
    value: '2',
    label: '传媒',
    children: []
  },
  {
    value: '3',
    label: '电力设备',
    children: []
  },
  {
    value: '4',
    label: '房地产',
    children: []
  },
  {
    value: '5',
    label: '纺织服饰',
    children: []
  },
  {
    value: '6',
    label: '非银金融',
    children: []
  },
  {
    value: '7',
    label: '钢铁',
    children: []
  },
  {
    value: '8',
    label: '公用事业',
    children: []
  },
  {
    value: '9',
    label: '国防军工',
    children: []
  },
  {
    value: '10',
    label: '环保',
    children: []
  }
]

const companyList = [
  {
    companyName: '中芯国际', // 公司名称
    id: '1',
    industry: [
      {
        name: '电子', // 所属行业
        id: '1-1',
        children: [
          {
            name: '半导体', // 所属子行业
            id: '1-1-1'
          }
        ]
      }
    ]
  },
  {
    companyName: '上海兮易网络科技有限公司', // 公司名称
    id: '2',
    industry: [
      {
        name: '电力设备', // 所属行业
        id: '2-1',
        children: []
      }
    ]
  },
  {
    companyName: '青岛兮易信息技术有限公司', // 公司名称
    id: '3',
    industry: [
      {
        name: '电力设备', // 所属行业
        id: '3-1',
        children: [
          {
            name: '电池', // 所属子行业
            id: '3-1-1'
          }
        ]
      }
    ]
  },
  {
    companyName: '江苏兮易信息技术有限公司', // 公司名称
    id: '4',
    industry: [
      {
        name: '电力设备', // 所属行业
        id: '4-1',
        children: [
          {
            name: '电池', // 所属子行业
            id: '4-1-1'
          }
        ]
      }
    ]
  },
  {
    companyName: '宁德时代', // 公司名称
    id: '5',
    industry: [
      {
        name: '电力设备', // 所属行业
        id: '5-1',
        children: [
          {
            name: '电池', // 所属子行业
            id: '5-1-1'
          }
        ]
      }
    ]
  }
]
const companyId = ref('')
const companyInfo = ref({})
let company = ref('')
const changeSelect = (item, items, itemSon) => {
  console.log(item, items, itemSon)
  if (companyId.value == items.id || companyId.value == itemSon?.id) {
    companyId.value = ''
    company.value = ''
    companyInfo.value = {}
    return
  }
  if (itemSon) {
    companyId.value = itemSon.id
    company.value = `${items.name}/${itemSon.name}`
    companyInfo.value = item
    return
  }
  companyId.value = items.id
  company.value = items.name
  companyInfo.value = item
}
/**------------------------ 第二步 end ------------------------------------- */

/**------------------------ 第三步 begin ------------------------------------- */
const improveName = ref('1')
const handleImprove = tab => {
  improveName.value = tab.name
}

const rules = []
const form = ref({
  revenue: '',
  revenueNext: '',
  profit: '',
  profitNext: '',
  sum: '',
  sumNext: ''
})
/**------------------------ 第三步 end ------------------------------------- */
</script>

<template>
  <div class="page-container">
    <div class="page-title-line">新增对标项目</div>
    <div class="main-card">
      <div class="step-card">
        <div class="step" :class="{ active: step > index }" v-for="(item, index) in stepList" :key="item.step">
          <div class="text-card">
            <div class="number">{{ item.step }}</div>
            <div class="text">{{ item.name }}</div>
          </div>
          <div class="line" v-if="index !== 3"></div>
        </div>
      </div>
      <!-- 第一步 选择对标主题 -->
      <div class="step-1" v-show="step == 1">
        <div class="theme-title">
          <SvgIcon name="rhombus" class="rhombus"></SvgIcon>
          <span class="title">对标主题</span>
        </div>
        <div class="theme-card">
          <div class="left">
            <div
              class="theme-item"
              :class="{ active: themeId == item.id }"
              v-for="item in themeList"
              :key="item.id"
              @click="changeTheme(item.id)"
            >
              <span class="text">{{ item.name }}</span>
              <span class="true">√</span>
            </div>
          </div>
          <div class="right">
            <div class="base-card p-[20px] text-[14px]">
              <div class="base-title">基本概念</div>
              <div class="base-desc">
                人力资源效能对标是指企业将自身的人力资源效率相关指标与同行业其他企业、行业标杆企业或不同行业但具有类似业务模式的优秀企业进行对比，以明确自身在人力资源利用效率方面的水平、优势与不足，进而采取针对性措施加以改进和优化，提高人力资源价值创造能力的管理活动。
              </div>
            </div>
            <div class="base-card p-[20px] text-[14px]">
              <div class="base-title">对标内容</div>
              <div class="base-list" v-for="item in infoList" :key="item.name">
                <span class="bold">{{ item.name }}</span>
                <span class="desc">{{ item.desc }}</span>
              </div>
            </div>
            <el-button type="primary" class="btn">加入对标模块</el-button>
          </div>
        </div>
      </div>
      <div class="step-2" v-show="step == 2">
        <div class="theme-title">
          <SvgIcon name="rhombus" class="rhombus"></SvgIcon>
          <span class="title">对标行业</span>
          <span class="theme-tag">您可以用两种方式确认行业</span>
        </div>
        <div class="industry-card">
          <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="按行业名称检索" name="1">
              <div class="search-card center">
                <el-cascader
                  class="search-input"
                  placeholder="请输入行业名称"
                  :options="industryOptions"
                  filterable
                  clearable
                >
                </el-cascader>
              </div>
            </el-tab-pane>
            <el-tab-pane label="按典型公司名称检索" name="2">
              <div class="search-card">
                <el-input v-model="company" placeholder="请输入公司名称">
                  <template #append>
                    <el-button type="primary" class="search-btn">搜索</el-button>
                  </template>
                </el-input>
                <div class="company-list">
                  <div class="company-item" v-for="item in companyList" :key="item.id">
                    <div class="company-name">{{ item.companyName }}</div>
                    <div class="company-industry">
                      <span class="label">所属行业：</span>
                      <div class="list">
                        <div class="list-items" v-for="items in item.industry" :key="items.id">
                          <div class="list-one" @click="changeSelect(item, items)">
                            <span class="list-name">{{ items.name }}</span>
                            <span
                              class="list-select"
                              :class="{ active: companyId == items.id }"
                              v-if="items.children.length == 0"
                            >
                              <span v-if="companyId == items.id">√</span>
                            </span>
                          </div>
                          <div
                            class="list-one list-two"
                            v-for="itemSon in items.children"
                            :key="itemSon.id"
                            @click="changeSelect(item, items, itemSon)"
                          >
                            <span class="list-name">{{ itemSon.name }}</span>
                            <span class="list-select" :class="{ active: companyId == itemSon.id }">
                              <span v-if="companyId == itemSon.id">√</span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="step-2" v-show="step == 3">
        <div class="theme-title">
          <SvgIcon name="rhombus" class="rhombus"></SvgIcon>
          <span class="title">完善对标数据</span>
        </div>
        <div class="theme-desc">
          请如实维护如下信息，若您仅想查看目标行业的效能分析，不做企业对标，下列信息也可直接跳过，报告中将不再显示先关对标差距与企业效能日和提升改善的内容
        </div>
        <div class="improve-card">
          <el-tabs v-model="improveName" class="improve-tabs" @tab-click="handleImprove">
            <el-tab-pane label="经营业绩类信息" name="1"></el-tab-pane>
            <el-tab-pane label="成本类信息" name="2"></el-tab-pane>
            <el-tab-pane label="人员类信息" name="3"></el-tab-pane>
          </el-tabs>
          <div class="form-card">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="164px">
              <el-row :gutter="105">
                <el-col :span="24">
                  <div class="form-title">营业收入</div>
                  <div class="form-desc">
                    指企业在一定时期内，通过从事销售商品、提供劳务、让渡资产使用权等各种经营活动所取得的收入总和，一般企业：营业总收入
                    = 主营业务收入 + 其他业务收入，金融企业：营业总收入 = 营业收入 + 利息收入 + 已赚保费 +
                    手续费及佣金收入 。
                  </div>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="2023年（单位：万元）" prop="revenue">
                    <el-input v-model="form.revenue" />
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="2024年（单位：万元）" prop="revenueNext">
                    <el-input v-model="form.revenueNext" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <div class="line"></div>
                </el-col>
                <el-col :span="24">
                  <div class="form-title">营业利润</div>
                  <div class="form-desc">
                    营业利润是指企业从事生产经营活动中取得的利润，是企业利润的主要来源，营业利润 = 营业收入 - 营业成本 -
                    税金及附加 - 期间费用（销售/管理/研发/财务费用） + 其他收益。
                  </div>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="2023年（单位：万元）" prop="profit">
                    <el-input v-model="form.profit" />
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="2024年（单位：万元）" prop="profitNext">
                    <el-input v-model="form.profitNext" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <div class="line"></div>
                </el-col>
                <el-col :span="24">
                  <div class="form-title">利润总额</div>
                  <div class="form-desc">
                    利润总额是企业在一定时期内通过生产经营活动所实现的最终财务成果，是衡量企业经营业绩的重要经济指标；利润总额
                    = 营业利润 + 营业外收入 - 营业外支出。
                  </div>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="2023年（单位：万元）" prop="sum">
                    <el-input v-model="form.sum" />
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="2024年（单位：万元）" prop="sumNext">
                    <el-input v-model="form.sumNext" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <div class="line"></div>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>
      </div>
      <div class="step-2" v-show="step == 4">
        <div class="report-card">
          <img src="@/assets/imgs/Ai/brench_over.webp" alt="" class="report-img" />
          <div class="complete-text">恭喜您完成 <span>人效对标（20250225）</span></div>
          <div class="report-btn-card">
            <el-button class="report-btn see-btn">查看概要报告</el-button>
            <el-button type="primary" class="report-btn">下载全量报告</el-button>
          </div>
        </div>
      </div>
      <div class="btn-card" v-if="step !== 4">
        <el-button class="btn pre-btn" v-if="step > 1" @click="prev">上一步</el-button>
        <el-button type="primary" class="btn" @click="next">下一步</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-container {
  .page-title-line {
    margin-bottom: 16px;
  }
  .main-card {
    width: 100%;
    padding: 68px 20px 14px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
    border-radius: 8px;
    .step-card {
      @include flex-center(row, center, flex-center);
      .step {
        @include flex-center(row, center, flex-start);
        .text-card {
          .number {
            width: 40px;
            height: 40px;
            background: linear-gradient(180deg, #ebf0f4 0%, #ffffff 100%);
            box-shadow:
              0px 15px 24px 0px rgba(165, 186, 217, 0.3),
              inset 0px 1px 0px 0px #ffffff;
            border-radius: 50%;
            font-size: 20px;
            font-weight: 600;
            color: #acacac;
            text-align: center;
            line-height: 40px;
          }
          .text {
            position: relative;
            left: -24px;
            padding-top: 16px;
            font-size: 16px;
            color: #888888;
          }
        }
        .line {
          position: relative;
          left: -24px;
          width: 206px;
          height: 8px;
          margin-top: 16px;
          border-radius: 4px;
          background-color: #d8d8d8;
        }
        &.active {
          .text-card {
            .number {
              background: linear-gradient(180deg, #40a0ff 0%, #9fcfff 100%);
              box-shadow:
                0px 15px 24px 0px rgba(165, 186, 217, 0.3),
                inset 0px 1px 0px 0px #9fcfff;
              color: #fff;
            }
            .text {
              color: #40a0ff;
            }
          }
          .line {
            background: linear-gradient(63deg, #40a0ff 0%, rgba(64, 160, 255, 0.1) 97%);
          }
        }
      }
    }

    .theme-title {
      @include flex-center(row, flex-start, center);
      margin-top: 40px;
      margin-bottom: 16px;
      .rhombus {
        margin-right: 5px;
        font-size: 12px;
      }
      .title {
        font-size: 16px;
        font-weight: 600;
        color: #3d3d3d;
      }
      .theme-tag {
        padding: 4px 10px;
        margin-left: 10px;
        font-size: 14px;
        color: #6ab5ff;
        background-color: #e2f0ff;
        border-radius: 4px;
      }
    }
    .theme-desc {
      width: fit-content;
      padding: 8px 14px;
      margin-top: 18px;
      margin-bottom: 20px;
      font-size: 14px;
      color: #40a0ff;
      background: #eff4f9;
      border-radius: 5px;
    }

    // 第一步 选择对标主题
    .theme-card {
      @include flex-center(row, flex-start, flex-start);
      gap: 20px;
      .left {
        flex-shrink: 0;
        width: 206px;
        padding: 20px 10px;
        background: #ffffff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #c6dbf3;
        .theme-item {
          @include flex-center(row, space-between, center);
          width: 100%;
          height: 36px;
          padding: 0 10px;
          margin-bottom: 10px;
          font-size: 14px;
          color: #6c757e;
          background-color: #f3f3f3;
          border-radius: 5px;
          cursor: pointer;
          &:last-child {
            margin-bottom: 0;
          }
          .true {
            display: inline-block;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background-color: #bbbbbb;
            font-size: 12px;
            color: #fff;
            text-align: center;
            line-height: 14px;
          }
          &.active {
            background-color: #f0f9ff;
            color: #53a9f9;
            .true {
              background-color: #53a9f9;
            }
          }
        }
      }
      .right {
        flex: 1;
        .base-card {
          margin-bottom: 20px;
          border-radius: 8px;
          border: 1px solid #c6dbf3;
          color: #333333;
          .base-title {
            margin-bottom: 10px;
            font-weight: 600;
          }
          .base-desc {
            line-height: 25px;
          }
          .base-list {
            padding-left: 15px;
            position: relative;
            line-height: 25px;
            &::before {
              content: '●';
              position: absolute;
              font-size: 8px;
              margin-right: 6px;
              top: 0;
              left: 0;
            }
            .bold {
              font-weight: 600;
            }
          }
        }
      }
    }

    // 第二步 对标行业
    .industry-card {
      margin-top: 32px;
      :deep(.demo-tabs) {
        .el-tabs__nav-scroll {
          @include flex-center(row, center, center);
        }
        .el-tabs__nav-wrap {
          overflow: auto;
          &::after {
            height: 0;
          }
        }
        .el-tabs__active-bar {
          height: 0;
        }
        .el-tabs__item {
          font-size: 16px;
          font-weight: 600;
          padding: 0 36px;
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 14px;
            width: 1px;
            height: 15px;
            background-color: #d8d8d8;
          }
          &:nth-child(2) {
            &::before {
              width: 0;
            }
          }
          &:last-child {
            border-right: none;
          }
          &.is-active {
            &::after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 30px;
              height: 4px;
              border-radius: 2px;
              background-color: #40a0ff;
            }
          }
        }
      }
      .search-card {
        @include flex-center(column, flex-start, flex-start);
        min-height: 350px;
        padding: 0 80px;
        &.center {
          align-items: center;
        }
        :deep(.el-input) {
          width: 460px;
          height: 36px;
          margin: 0 auto;
        }
        .search-btn {
          width: 88px;
          height: 36px;
          background-color: #40a0ff;
          color: #fff;
          border-radius: 0px 4px 4px 0px;
        }

        .company-list {
          @include flex-center(row, flex-start, stretch);
          width: 100%;
          padding-top: 40px;
          flex-wrap: wrap;
          gap: 20px;
          .company-item {
            width: calc((100% - 60px) / 4);
            min-height: 130px;
            padding: 16px 15px 0;
            border-radius: 6px;
            border: 1px solid #c6dbf3;
            .company-name {
              padding-bottom: 6px;
              font-size: 16px;
              color: #333333;
              font-weight: 600;
            }
            .company-industry {
              @include flex-center(row, flex-start, flex-start);
              .label {
                flex-shrink: 0;
                width: 70px;
                font-size: 14px;
                color: #666666;
              }
              .list {
                flex: 1;
                .list-items {
                  .list-one {
                    @include flex-center(row, space-between, center);
                    width: 100%;
                    height: 32px;
                    padding: 0 10px;
                    margin-bottom: 6px;
                    background: #e6eff6;
                    border-radius: 4px;
                    color: #53b8ff;
                    font-size: 14px;
                    cursor: pointer;
                    .list-select {
                      display: inline-block;
                      width: 18px;
                      height: 18px;
                      text-align: center;
                      line-height: 18px;
                      background: #ffffff;
                      border: 1px solid #dddddd;
                      border-radius: 50%;
                      &.active {
                        background: #53b8ff;
                        color: #fff;
                        border-color: #53b8ff;
                      }
                    }
                  }
                  .list-two {
                    width: 95%;
                    transform: translateX(5%);
                  }
                }
              }
            }
          }
        }
      }
    }

    // 第三步 完善对标数据
    .improve-card {
      :deep(.improve-tabs) {
        .el-tabs__item {
          padding: 0 30px;
          margin-right: 40px;
          border-bottom: 3px solid transparent;
          &.is-active {
            border-bottom: 3px solid #40a0ff;
          }
        }
        .el-tabs__active-bar {
          height: 0;
        }
      }
      .form-card {
        padding-top: 15px;
        .form-title {
          padding-bottom: 18px;
          font-size: 18px;
          line-height: 18px;
          color: #3d3d3d;
        }
        .form-desc {
          padding-bottom: 31px;
          line-height: 24px;
          font-size: 14px;
          color: #40a0ff;
        }
        .line {
          width: 100%;
          height: 1px;
          margin-bottom: 30px;
          background-color: #d8d8d8;
        }
      }
    }

    // 第四步 完成
    .report-card {
      @include flex-center(column, flex-start, center);
      padding: 153px 0 223px;
      .report-img {
        width: 200px;
        margin-bottom: 50px;
      }
      .complete-text {
        padding-bottom: 40px;
        font-size: 24px;
        color: #3d3d3d;
        span {
          color: #40a0ff;
          font-weight: 600;
        }
      }
      .report-btn-card {
        .report-btn {
          width: 352px;
          height: 50px;
          font-size: 16px;
          border-radius: 25px;
        }
        .see-btn {
          margin-right: 44px;
          border-color: #40a0ff;
          color: #40a0ff;
        }
      }
    }

    .btn {
      width: 120px;
      height: 36px;
    }
    .pre-btn {
      border-color: #40a0ff;
      color: #40a0ff;
    }
    .btn-card {
      @include flex-center(row, center, center);
      padding-top: 40px;
    }
  }
}
</style>
