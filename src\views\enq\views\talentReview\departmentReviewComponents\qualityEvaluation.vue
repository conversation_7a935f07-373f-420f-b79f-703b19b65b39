<template>
  <div class="edu_info_wrap">
    <div class="clearfix">
      <div class="page_second_title marginT_8">
        <span>能力评价</span>
      </div>
      <div style="color: #0099ff; font-weight: 600">
        上级需要根据系统给出的各项维度分数，对下级人员进行等级评价，评价标准分为5档：S杰出（通常占10%），A一贯超出预期（30%），B符合预期（30%），C需要提高（20%），D不合格（10%）
      </div>
      <div class="btn_wrap align_right">
        <el-button class="page_add_btn" type="primary" @click="exportDownloadFun">下载详细数据</el-button>
      </div>

      <div class="edu_info_center marginT_16">
        <el-table :data="eduInfoData" style="width: 100%">
          <el-table-column type="index" label="序号" width="50" align="center" fixed="left"></el-table-column>
          <el-table-column prop="userName" label="姓名" align="center" fixed="left"></el-table-column>
          <el-table-column prop="S" label="自评" align="center" fixed="right"></el-table-column>
          <el-table-column prop="U" label="上级" align="center" fixed="right"></el-table-column>
          <el-table-column prop="P" label="同级" align="center" fixed="right"></el-table-column>
          <el-table-column prop="B" label="下级" align="center" fixed="right"></el-table-column>
          <el-table-column prop="qualityOverallScore" label="综合得分" align="center" fixed="right"></el-table-column>
          <el-table-column prop="qualityLevelSys" label="系统评级" align="center" fixed="right"></el-table-column>
          <el-table-column prop="actualQualityGrade" label="实际等级" width="120" align="center" fixed="right">
            <template #default="scope">
              <el-select class="item" v-model="scope.row.actualQualityGrade" placeholder="请选择" size="small">
                <el-option
                  v-for="(item, index) in qualificationOptions"
                  :label="item.codeName"
                  :value="item.dictCode"
                  :key="index"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
        <div class="edu_info_mmain">
          <div class="align_center marginT_30">
            <el-button
              class="page_confirm_btn"
              type="primary"
              @click="prevStep"
              v-show="currentIndex != currentFirstCode"
              >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="submit('nextStep')">{{ nextBtnText }}</el-button>
          </div>
        </div>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" title="查看他人评论" :close-on-click-modal="false" width="30%">
      <div class="commont_box">
        <div v-for="(item, index) in commontList" :key="index" class="commont">
          <div>评价人：{{ item.userName }}</div>
          <div>评语：{{ item.comment }}</div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { qualityEvalList, saveQualityEval, getComment, exportQualityEvalList } from '../../../request/api.js'
import { useUserStore } from '@/stores/index.js'
import { objHasEmpty } from '@/utils/utils.js'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  currentIndex: Number,
  currentFirstCode: Number
})
const userStore = useUserStore()
const emit = defineEmits(['prevStep', 'nextStep'])

const submitFlag = ref(true)
const qualificationOptions = ref([])
const eduInfoData = ref([])
const dialogVisible = ref(false)
const commontList = ref([])

onMounted(async () => {
  const res = await userStore.getDocList(['ACTUAL_GRADE'])
  qualificationOptions.value = res.ACTUAL_GRADE
  await getEducationData()
})

const exportDownloadFun = async () => {
  try {
    const res = await exportQualityEvalList({
      enqId: props.enqId,
      type: 'quality'
    })

    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '部门能力评价列表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  } catch (error) {
    console.error(error)
    ElMessage.error('导出失败')
  }
}

const submit = async stepType => {
  if (!submitFlag.value) return

  if (checkData(eduInfoData.value)) {
    ElMessage.warning('请完善数据后提交！')
    return
  }

  submitFlag.value = false

  try {
    const arr = eduInfoData.value.map(item => ({
      grade: item.actualQualityGrade,
      comments: item.qualityComments,
      userId: item.userId
    }))

    const res = await saveQualityEval({
      enqId: props.enqId,
      list: arr,
      type: 'quality'
    })

    if (res.code == 200) {
      ElMessage.success('保存成功!')
      submitFlag.value = true
      emit(stepType)
    } else {
      submitFlag.value = true
      ElMessage.error('保存失败!')
    }
  } catch (error) {
    console.error(error)
    submitFlag.value = true
    ElMessage.error('保存失败!')
  }
}

const getEducationData = async () => {
  try {
    const res = await qualityEvalList({
      enqId: props.enqId
    })

    if (res.code == 200) {
      eduInfoData.value = res.data
    } else {
      ElMessage.error('获取数据失败!')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败!')
  }
}

const checkData = data => {
  for (const obj of data) {
    if (objHasEmpty(obj, ['actualQualityGrade'])) {
      return true
    }
  }
  return false
}

const prevStep = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submit('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') {
        emit('prevStep')
      }
    })
}
</script>

<style scoped lang="scss">
.edu_info_wrap {
  margin-bottom: 16px;
}
.edu_info_header {
  .item {
    width: 9%;
    // padding-left: 15px;
  }

  .item_icon_wrap {
    text-align: center;
    width: 6%;
  }
}
.commont_box {
  height: 400px;
  overflow-y: auto;
  .commont {
    border-bottom: 1px solid #9bd3f6;
    div {
      padding: 10px 0;
    }
  }
}
.el-dialog__body {
  padding: 0 30px;
}
</style>
