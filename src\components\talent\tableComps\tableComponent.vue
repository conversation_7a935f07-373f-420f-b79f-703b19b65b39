<template>
  <div class="table_wrap">
    <el-table
      ref="multipleTable"
      v-loading="loading"
      v-bind="$attrs"
      :data="tableData.data"
      :cell-style="setCellStyle"
      :row-style="setRowStyle"
      :row-key="rowKey"
      :span-method="spanMethod"
      :size="size"
      :height="height"
      :max-height="maxHeight"
      :border="border"
      :show-summary="showSummary"
      :highlight-current-row="radioFlag"
      @current-change="handleRadioChange"
      @selection-change="selectChange"
      @select="selectRow"
      @select-all="selectAll"
      element-loading-text="加载中"
      element-loading-background="rgba(0, 0, 0, 0.3)"
    >
      <slot name="prefix"></slot>
      <el-table-column v-if="selectionStatus" :selectable="selectable" type="selection" width="50"></el-table-column>
      <el-table-column
        v-if="needIndex"
        type="index"
        label="序号"
        width="70"
        :index="indexMethod"
        :fixed="true"
      ></el-table-column>
      <TableColumns
        v-for="col in tableData.columns"
        :key="col.prop"
        :col-obj="col"
        :overflowTooltip="overflowTooltip"
      />
      <slot name="oper"></slot>
      <slot name="oper2"></slot>
    </el-table>
    <div class="pagination_wrap" v-if="needPagination">
      <el-pagination
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        :current-page="currentPage"
        :page-size="pageSize"
        @current-change="handleCurrentChange"
        :pager-count="5"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits, onMounted } from 'vue'
import TableColumns from './TableColumns.vue'

const props = defineProps({
  tableData: {
    type: Object,
    default: () => ({
      columns: [],
      data: [],
      page: { current: 1, size: 10, total: 0 }
    })
  },
  loading: {
    type: Boolean,
    default: false
  },
  needIndex: {
    type: Boolean,
    default: false
  },
  radioFlag: {
    type: Boolean,
    default: false
  },
  handleRadioChange: {
    type: Function,
    default: () => {}
  },
  setCellStyle: Function,
  setRowStyle: Function,
  spanMethod: Function,
  size: String,
  needPagination: {
    type: Boolean,
    default: true
  },
  selectionStatus: {
    type: Boolean,
    default: false
  },
  border: {
    type: Boolean,
    default: false
  },
  height: [String, Number],
  maxHeight: [String, Number],
  checkSelection: Array,
  overflowTooltip: {
    type: Boolean,
    default: true
  },
  rowKey: {
    type: String,
    default: ''
  },
  showSummary: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'selectionChange',
  'curSelectInfo',
  'selectAll',
  'handleSizeChange',
  'handleCurrentChange',
  'page'
])

const multipleTable = ref(null)
const checkList = ref([])
const Psize = ref(null)
const Ppage = ref(null)

const currentPage = computed(() => (props.tableData.page ? props.tableData.page.current : 1))
const pageSize = computed(() => (props.tableData.page ? props.tableData.page.size : 10))
const total = computed(() => (props.tableData.page ? props.tableData.page.total : 0))

watch(
  () => props.checkSelection,
  val => {
    setTimeout(() => {
      if (val && val.length > 0) {
        dataPlayback(val)
      } else {
        multipleTable.value && multipleTable.value.clearSelection()
      }
    })
  }
)

function selectable(row, index) {
  return row.isCheckSign !== true
}

function dataPlayback(val) {
  if (!multipleTable.value) return
  multipleTable.value.clearSelection()
  val.forEach(item => {
    multipleTable.value.toggleRowSelection(item, true)
  })
}

function selectChange(selection) {
  if (props.selectionStatus) {
    emit('selectionChange', selection)
  }
}
function selectRow(selection, row) {
  if (props.selectionStatus) {
    emit('curSelectInfo', selection, row)
  }
}
function selectAll(selection) {
  if (props.selectionStatus) {
    emit('selectAll', selection)
  }
}
function handleSizeChange(size) {
  emit('handleSizeChange', size)
  Psize.value = size
  Ppage.value = Ppage.value || currentPage.value
  emit('page', size, Ppage.value)
}
function handleCurrentChange(page) {
  emit('handleCurrentChange', page)
  Ppage.value = page
  Psize.value = Psize.value || pageSize.value
  emit('page', Psize.value, page)
}
function resetScroll() {
  if (multipleTable.value && multipleTable.value.bodyWrapper) {
    multipleTable.value.bodyWrapper.scrollLeft = 0
    multipleTable.value.bodyWrapper.scrollTop = 0
  }
}
function doLayout() {
  if (multipleTable.value) {
    multipleTable.value.doLayout()
  }
}
function indexMethod(index) {
  let current = props.tableData?.page?.current ?? 1
  let size = props.tableData?.page?.size ?? 10
  if (props.needPagination) {
    return index + 1 + (current - 1) * size
  } else {
    return index + 1
  }
}
</script>

<style lang="scss" scoped>
.table_wrap {
  width: 100%;
}
.el-table tr {
  height: 35px;
}
</style>
