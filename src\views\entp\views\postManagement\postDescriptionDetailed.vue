<template>
  <div class="post_desc_detailed_wrap bg_write">
    <div class="page_main_title">
      职位说明书
      <div class="goback_geader" @click="$util.goback()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class>
      <div class="page_section post_desc_detailed_center clearfix">
        <div class="post_desc_detailed_aside">
          <div class="post_desc_menu">
            <el-tabs tab-position="left" v-model="tabsDefault" @tab-click="tabClick">
              <el-tab-pane v-for="tab in tabsData" :label="tab.label" :name="tab.name">
                <component v-if="tab.name == tabsDefault" :is="tabsDefault" :jobCode="jobCode"></component>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import tabsDiffentPane from '@/components/talent/tabsComps/tabsDiffentPane'
import jobBasicInfo from './components/postDescriptionDetailedComponents/PDbasicInfo'
import postMainRelationship from './components/postDescriptionDetailedComponents/PDmainRelationship'
import postQualityRequirements from './components/postDescriptionDetailedComponents/PDqualityRequirements'
import postCapabilityRequirements from './components/postDescriptionDetailedComponents/PDcapabilityRequirements'
import postResponsibility from './components/postDescriptionDetailedComponents/PDresponsibility'
export default {
  name: 'postDescriptionDetailed',
  components: {
    tabsDiffentPane,
    jobBasicInfo,
    postMainRelationship,
    postQualityRequirements,
    postCapabilityRequirements,
    postResponsibility
  },
  data() {
    return {
      jobCode: this.$route.query.jobCode,
      tabsDefault: 'jobBasicInfo',
      tabsData: [
        {
          id: 1,
          label: '基本信息',
          name: 'jobBasicInfo'
        },
        // {
        //     id:2,
        //     label:"主要关系",
        //     name:"postMainRelationship",
        // },
        {
          id: 3,
          label: '素质要求',
          name: 'postQualityRequirements'
        },
        {
          id: 4,
          label: '能力要求',
          name: 'postCapabilityRequirements'
        },
        {
          id: 5,
          label: '主要职责',
          name: 'postResponsibility'
        }
      ]
    }
  },
  created() {},
  mounted() {},
  methods: {
    tabClick(tab, event) {
      this.tabsDefault = tab.name
    }
  }
}
</script>

<style scoped lang="scss">
.post_desc_detailed_wrap {
  .post_desc_detailed_aside {
    .el-input {
      width: auto;
    }
    .el-tabs--left .el-tabs__header.is-left {
      width: 240px;
      padding: 10px 32px 0 16px;
      border-right: 1px solid #e4e7ed;
      margin-right: 36px;
    }
    .el-tabs--left .el-tabs__item.is-left {
      text-align: center;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__item {
      text-align: center;
      background-color: #e4e7ed;
      margin-bottom: 16px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 500;
      color: #525e6c;
      border-radius: 4px;
      cursor: pointer;
    }
    .el-tabs__item.is-active {
      background-color: #0099ff;
      color: #fff;
    }
  }
}
</style>
