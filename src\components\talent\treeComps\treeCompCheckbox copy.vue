<template>
  <div class="tree_wrap tree_comp_checkbox_wrap">
    <el-tree
      :data="treeData"
      show-checkbox
      :node-key="nodeKey"
      :default-expand-all="expandAll"
      check-strictly
      :default-checked-keys="defaultCheckedKeys"
      :default-expanded-keys="defaultExpandedKeys"
      :expand-on-click-node="false"
      ref="treeCheckbox"
      :props="{ label: labelKey }"
      @node-click="nodeClickCallback"
    >
      <template #default="{ node }">
        <span class="custom-tree-node">
          <span class="tree_node" :title="node.label">
            {{ node.label }}
          </span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

const props = defineProps({
  treeData: {
    type: Array,
    default: () => []
  },
  nodeKey: {
    type: String,
    default: 'code'
  },
  labelKey: {
    type: String,
    default: 'value'
  },
  checkedAll: {
    type: Boolean,
    default: false
  },
  defaultCheckedKeys: {
    type: Array,
    default: () => []
  },
  defaultExpandedKeys: {
    type: Array,
    default: () => []
  },
  expandAll: {
    type: Boolean,
    default: true
  },
  expandedLevel: {
    type: Number,
    default: 3
  },
  checkChildNode: {
    type: Boolean,
    default: true
  },
  isLateralCheck: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['node-click-callback'])

const treeCheckbox = ref(null)
const checkedCode = ref([])
const layerNo = ref('')

const setCheckedData = () => {
  // 保留原有接口
}

const getCheckedKeys = curCheckCode => {
  checkedCode.value = treeCheckbox.value.getCheckedKeys()
  emit('node-click-callback', checkedCode.value, curCheckCode)
}

const setChildNodesChecked = (node, checked) => {
  if (node.childNodes.length > 0) {
    node.childNodes.forEach(item => {
      item.checked = !checked
      setChildNodesChecked(item, checked)
    })
  }
}

const nodeClickCallback = (data, node) => {
  if (props.isLateralCheck) {
    if (node.data.layerNo !== layerNo.value) {
      treeCheckbox.value.setCheckedKeys([])
      layerNo.value = node.data.layerNo
    }
  }
  let checked = node.checked
  node.checked = !checked
  if (props.checkChildNode) {
    setChildNodesChecked(node, checked)
  }
  let curCheckCode = node.data.code
  getCheckedKeys(curCheckCode)
}

const setExpandKeys = () => {
  if (props.expandAll) return
  function getKeys(data) {
    data.forEach(item => {
      if (item.layerNo < props.expandedLevel + 1) {
        props.defaultExpandedKeys.push(item.code)
      }
      if (item.children && item.children.length) {
        getKeys(item.children)
      }
    })
  }
  getKeys(props.treeData)
}

watch(
  () => props.treeData,
  val => {
    if (val.length == 0) return
    setExpandKeys()
  }
)

nextTick(() => {
  if (props.checkedAll && treeCheckbox.value) {
    treeCheckbox.value.setCheckedNodes(props.treeData)
  }
  if (props.defaultCheckedKeys.length > 0) {
    emit('node-click-callback', props.defaultCheckedKeys, null)
  }
})
</script>

<style scoped lang="scss">
.tree_wrap {
  width: 100%;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
:deep(.el-tree-node__expand-icon) {
  // display: none;
}
:deep(.el-tree-node__content > label.el-checkbox) {
  pointer-events: none;
}

.tree_comp_checkbox_wrap {
  :deep(.custom-tree-node) {
    width: 75%;
  }
  :deep(.tree_node) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
