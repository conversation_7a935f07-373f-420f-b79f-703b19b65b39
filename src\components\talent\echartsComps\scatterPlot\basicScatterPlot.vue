
// 基础散点图
<template>
    <div class="scatter_plot_main">
        <div :id="id" class="chart_dom" :style="styleObj"></div>
    </div>
</template>
 
<script>
export default {
    name: "basicScatterPlot",
    props: {
        chartData: {
            type: Array,
            default: function() {
                return [];
            }
        },
        width: {
            type: Number,
            default: 100
        },
        height: {
            type: Number,
            default: 100
        },
        chartConfig: {
            // 散点配置
            type: Object,
            default: function() {
                return {
                    xAxisName: "", //X轴名称
                    yAxisName: "", //Y轴名称
                    name: "name", // 散点的分类
                    x: "progress", //散点的x坐标对应数据的key
                    y: "quality", //散点的y坐标对应数据的key
                    size: "personnelNum", // 散点大小对应的key
                    valueFormatterKey: "" //加工数据值，value+ valueFormatterKey  eg: 10 + %    ==  10%
                };
            }
        }
    },
    components: {},
    data() {
        return {
            id: "",
            styleObj: {
                width: this.width + "px",
                height: this.height + "px"
            },
            legendData: [],
            chartConfigDefault: {
                // isPercent: true, //轴坐标是否是百分数
                xAxisName: "", //X轴名称
                yAxisName: "", //Y轴名称
                name: "name", // 散点的分类
                x: "progress", //散点的x坐标对应数据的key
                y: "quality", //散点的y坐标对应数据的key
                size: "personnelNum", // 散点大小对应的key
                valueFormatterKey: "" //加工数据值，value+ valueFormatterKey  eg: 10 + %    ==  10%
            },
            chartConfigRes: {},
            egData: [
                {
                    name: "销售一部",
                    personnelNum: 50,
                    quality: 72,
                    progress: 89.9
                },
                {
                    name: "销售二部",
                    personnelNum: 55,
                    quality: 55,
                    progress: 92
                }
            ]
        };
    },
    watch: {
        chartData: {
            handler() {
                this.init(this.chartData);
            },
            deep: true
        }
    },
    created() {
        // 合并配置项
        this.chartConfigRes = {
            ...this.chartConfigDefault,
            ...this.chartConfig
        };
    },
    mounted() {
        if (this.chartData.length == 0) {
            return;
        }
        this.init(this.chartData);
    },
    methods: {
        init(chartData) {
            let id = this.$util.createRandomId();
            this.id = id;
            let data = this.formatData(chartData);

            this.$nextTick(() => {
                this.toDraw(id, data);
            });
        },
        toDraw(id, data) {
            let _this = this;
            let myChart = this.$EC.init(document.getElementById(id));
            if (data.length == 0) {
                myChart.clear();
                return;
            }
            let options = {
                legend: {
                    // show:true,
                    // // type: "scroll",
                    // orient: "vertical",
                    // right: 30,
                    // top: 20,
                    // bottom: 20,
                    // data: ['销售一部'],
                },
                xAxis: {
                    name: _this.chartConfigRes.xAxisName,
                    nameLocation: "end",
                    nameGap: 25,
                    nameTextStyle: {
                        color: "#0099fd",
                        fontSize: 16,
                        fontWeight: "bold"
                    },
                    gridIndex: 0,
                    type: "value",
                    show: true,
                    // min: 0,
                    // max: 100,
                    nameLocation: "middle",
                    splitLine: {
                        lineStyle: {
                            type: "dashed"
                        }
                    },
                    axisLabel: {
                        formatter: function(val, index) {
                            return val + _this.chartConfigRes.valueFormatterKey;
                            // return _this.chartConfigRes.isPercent
                            //     ? val + "%"
                            //     : val;
                        }
                    },
                    scale: true
                },
                yAxis: {
                    name: _this.chartConfigRes.yAxisName,
                    nameLocation: "start",
                    nameGap: 30,
                    nameTextStyle: {
                        color: "#0099fd",
                        fontSize: 16,
                        fontWeight: "bold"
                    },
                    gridIndex: 0,
                    min: 0,
                    // max: 100,
                    show: true,
                    nameLocation: "middle",
                    splitLine: {
                        lineStyle: {
                            type: "dashed"
                        }
                    },
                    axisLabel: {
                        formatter: function(val, index) {
                            return val + _this.chartConfigRes.valueFormatterKey;
                            // return _this.chartConfigRes.isPercent
                            //     ? val + _this.chartConfigRes.valueFormatterKey
                            //     : val;
                        }
                    }
                    // scale: true
                },
                tooltip: {
                    formatter: function(params, callback) {
                        // let percent = _this.chartConfigRes.isPercent ? "%" : "";
                        let key = _this.chartConfigRes.valueFormatterKey;
                        let str = `<div>${params.data.name}</div>`;
                        str += `<div>${_this.chartConfigRes.xAxisName} : ${params.data.value[0]}${key}</div>`;
                        str += `<div>${_this.chartConfigRes.yAxisName} : ${params.data.value[1]}${key}</div>`;
                        return str;
                    }
                },
                series: [
                    {
                        type: "scatter",
                        label: {
                            normal: {
                                show: true,
                                formatter: "{b}",
                                color: "#fff",
                                textStyle: {
                                    fontSize: "12"
                                }
                            }
                        },
                        data: data
                    }
                ]
            };
            myChart.setOption(options);
        },
        formatData(data) {
            let result = [];
            let _this = this;
            this.chartData.forEach(item => {
                _this.legendData.push(item[this.chartConfigRes.name]);
                let obj = {
                    name: item[this.chartConfigRes.name],
                    value: [
                        item[this.chartConfigRes.x],
                        item[this.chartConfigRes.y]
                    ],
                    type: "scatter",
                    symbolSize: item[this.chartConfigRes.size],
                    label: {
                        normal: {
                            textStyle: {
                                fontSize: 13,
                                lineHeight: 17
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: this.$util.randomColor()
                        }
                    }
                };
                result.push(obj);
            });
            return result;
        }
    }
};
</script>
 
<style scoped lang="scss">
.scatter_plot_main {
    width: 100%;
    margin: 0 auto;
}
</style>