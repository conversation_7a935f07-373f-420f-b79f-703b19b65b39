<script setup>
defineOptions({ name: 'core' })
const navList = ref([
  {
    title: '【GTMB】销售营销2B',
    desc: 'Go-to-Market for B',
    aiText: '【GTMB】销售营销2B、 KPI 指标90%  核心能力64分 改善任务53%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '90%'
      },
      {
        title: '核心能力（得分：',
        score: '64分'
      },
      {
        title: '改善任务（进度：',
        score: '53%'
      }
    ],
    ai: true
  },
  {
    title: '【GTMC】销售营销2C',
    desc: 'Go-to-Market for C',
    aiText: '【GTMC】销售营销2C、 KPI 指标73%  核心能力59分 改善任务69%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '73%'
      },
      {
        title: '核心能力（得分：',
        score: '59分'
      },
      {
        title: '改善任务（进度：',
        score: '69%'
      }
    ],
    ai: true
  },
  {
    title: '【PLM】产品全生命周期管理',
    desc: 'Product Lifecycle Management',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标89%  核心能力62分 改善任务47%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '89%'
      },
      {
        title: '核心能力（得分：',
        score: '62分'
      },
      {
        title: '改善任务（进度：',
        score: '47%'
      }
    ],
    ai: true
  },
  {
    title: '【SCM-P&O】供应链 计划订单',
    desc: 'SCM-Plan & Order Management',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标81%  核心能力56分 改善任务54%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '81%'
      },
      {
        title: '核心能力（得分：',
        score: '56分'
      },
      {
        title: '改善任务（进度：',
        score: '54%'
      }
    ],
    ai: true
  },
  {
    title: '【SCM-P&S】供应链 采购与供应商管理',
    desc: 'SCM-Purchase & Supplier Management',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标89%  核心能力68分 改善任务46%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '89%'
      },
      {
        title: '核心能力（得分：',
        score: '68分'
      },
      {
        title: '改善任务（进度：',
        score: '46%'
      }
    ],
    ai: true
  },
  {
    title: '【SCM-M&PL】供应链 制造与厂内物流',
    desc: 'SCM-Manufacture & In-Plant-Logistics',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标70%  核心能力53分 改善任务73%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '70%'
      },
      {
        title: '核心能力（得分：',
        score: '53分'
      },
      {
        title: '改善任务（进度：',
        score: '73%'
      }
    ],
    ai: true
  },
  {
    title: '【FPD】成套设备完美交付',
    desc: 'Flawless Project Delivery',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标88%  核心能力68分 改善任务68%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '88%'
      },
      {
        title: '核心能力（得分：',
        score: '68分'
      },
      {
        title: '改善任务（进度：',
        score: '68%'
      }
    ],
    ai: true
  },
  {
    title: '【E2E-QM】端到端质量管理',
    desc: 'E2E Quality Management',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标75%  核心能力73分 改善任务56%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '75%'
      },
      {
        title: '核心能力（得分：',
        score: '73分'
      },
      {
        title: '改善任务（进度：',
        score: '56%'
      }
    ],
    ai: true
  },
  {
    title: '【ABC】全业务链成本优化',
    desc: 'Absolutely Best Costing',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标74%  核心能力53分 改善任务66%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '74%'
      },
      {
        title: '核心能力（得分：',
        score: '53分'
      },
      {
        title: '改善任务（进度：',
        score: '66%'
      }
    ],
    ai: true
  },
  {
    title: '【DGA】目标落地论证',
    desc: 'Demonstration of Goal Achievement',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标75%  核心能力70分 改善任务70%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '75%'
      },
      {
        title: '核心能力（得分：',
        score: '70分'
      },
      {
        title: '改善任务（进度：',
        score: '70%'
      }
    ],
    ai: true
  },
  {
    title: '【S&OP】销售与业务协同',
    desc: 'Sales & Operation Planning',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标77%  核心能力75分 改善任务75%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '77%'
      },
      {
        title: '核心能力（得分：',
        score: '75分'
      },
      {
        title: '改善任务（进度：',
        score: '75%'
      }
    ],
    ai: true
  },
  {
    title: '【O&PM】组织与人才管理',
    desc: 'Organization & People Management',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标90%  核心能力55分 改善任务56%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '90%'
      },
      {
        title: '核心能力（得分：',
        score: '55分'
      },
      {
        title: '改善任务（进度：',
        score: '56%'
      }
    ],
    ai: true
  },
  {
    title: '【B&FM】预算与财务管理',
    desc: 'Budget & Financial Management',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标75%  核心能力64分 改善任务49%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '75%'
      },
      {
        title: '核心能力（得分：',
        score: '64分'
      },
      {
        title: '改善任务（进度：',
        score: '49%'
      }
    ],
    ai: true
  },
  {
    title: '【PS&D】流程系统与数字化',
    desc: 'Process System & Digitization',
    aiText: '【PLM】产品全生命周期管理、 KPI 指标77%  核心能力47分 改善任务65%',
    i: [
      {
        title: 'KPI 指标 （达成：',
        score: '77%'
      },
      {
        title: '核心能力（得分：',
        score: '47分'
      },
      {
        title: '改善任务（进度：',
        score: '65%'
      }
    ],
    ai: true
  }
])
const openAi = inject('openAi')
</script>
<template>
  <div class="core_wrap">
    <div class="core_main justify-start">
      <div class="item_wrap" v-for="item in navList" :key="item.title" :class="{ bg: !item.i.length }">
        <div class="title_wrap justify-between">
          <div class="title">{{ item.title }}</div>
          <div class="btn" v-if="item.ai" @click="openAi(item.aiText)">AI快速解读</div>
        </div>
        <div class="desc">{{ item.desc }}</div>
        <div class="i_wrap">
          <div class="i_item" v-for="(i2, index) in item.i" :key="i2.title">
            <span
              class="circle"
              :class="{
                red_bg: index == 0,
                yellow_bg: index == 1,
                green_bg: index == 2
              }"
            ></span
            >{{ i2.title }}{{ i2.score + ')' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.core_wrap {
  .justify-start {
    display: flex;
    justify-content: flex-start;
  }
  .justify-between {
    display: flex;
    justify-content: space-between;
  }
  .core_main {
    flex-wrap: wrap;
    margin-left: -10px;
    gap: 20px;
  }
  .item_wrap {
    // margin: 0 10px 20px;
    padding: 20px;
    // width: 520px;
    width: 32%;
    height: 222px;
    background: url('@/assets/imgs/coreBusinessNav/img_01.png') no-repeat center;
    background-size: 100% 100%;
    font-size: 14px;
    &.bg {
      height: 108px;
      background: url('@/assets/imgs/coreBusinessNav/img_02.png') no-repeat center;
      background-size: 100% 100%;
    }
    .title_wrap {
      .title {
        font-size: 16px;
        font-weight: 600;
      }
      .btn {
        width: 92px;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        text-align: center;
        color: #40a0ff;
        border-radius: 78px 78px 78px 78px;
        border: 1px solid #40a0ff;
        cursor: pointer;
      }
      .btn:hover {
        color: #fff;
        background: #40a0ff;
      }
    }
    .desc {
      margin: 12px 0;

      color: #909090;
    }
    .i_wrap {
      .i_item {
        height: 36px;
        line-height: 36px;
        color: #666666;
      }
      .circle {
        margin-right: 10px;
        display: inline-block;
        width: 14px;
        height: 14px;
        border-radius: 50%;
      }
      .red_bg {
        background: #ff7d7d;
      }
      .yellow_bg {
        background: #f9e26e;
      }
      .green_bg {
        background: #95dc6b;
      }
    }
  }
  .item_wrap:hover {
    border: 1px solid #40a0ff;
    box-shadow: 0px 4px 10px 0px #cad5e1;
    border-radius: 8px 8px 8px 8px;
  }
}
</style>
