<template>
  <div class="main_detail">
    <tableList :tableData="tableData" @detail="toDetail" :hasBack="true" @back="back">
      <template v-slot:nameColumn>
        <el-table-column label="能力组件" prop="name" align="center" width="200">
          <template #default="scope">
            <div class="align_left">{{ scope.row['name'] }}</div>
            <div class="score_bar">
              <div
                class="content_bar"
                :style="{
                  width: `${scope.row['value']}%`
                  // background: `linear-gradient(-90deg,${setColor(scope.row['value'])})`
                }"
              ></div>
              <div class="text" :style="{ left: `calc(${scope.row['value']}% + 5px)` }">
                {{ scope.row['value'] }}
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="组件群" prop="name" width="200">
          <template #default="scope">
            <div class="align_left">{{ scope.row['name'] }}</div>
            <div class="score_bar">
              <div
                class="content_bar"
                :style="{
                  width: `${scope.row['value']}%`,
                  background: `linear-gradient(-90deg,${setColor(scope.row['value'])})`
                }"
              ></div>
              <div class="text" :style="{ left: `calc(${scope.row['value']}% + 5px)` }">
                {{ scope.row['value'] }}
              </div>
            </div>
          </template>
        </el-table-column> -->
      </template>
    </tableList>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import tableList from './tableList.vue'

const route = useRoute()
const emit = defineEmits(['setComponentName'])

const modelId = ref(null)
const diagnosticType = ref(null)
const moduleCode = ref(null)
const tableData = ref({
  columns: [
    {
      name: '流程赋能',
      code: 'A'
    },
    {
      name: '组织赋能',
      code: 'B'
    },
    {
      name: '人岗赋能',
      code: 'C'
    },
    {
      name: '数字化赋能',
      code: 'D'
    },
    {
      name: 'AI赋能',
      code: 'E'
    }
  ],
  list: [
    {
      name: '制定需求计划对象',
      A: 56.8,
      B: 56.5,
      C: 56.0,
      D: 30.0,
      E: 31.4,
      value: 45.4,
      changeType: 1,
      changeScore: 1.2,
      AList: [
        {
          name: '流程端到端',
          value: '55'
        },
        {
          name: '输入输出文档',
          value: '53'
        },
        {
          name: '业务规则',
          value: '54'
        },
        {
          name: '业务 KPI',
          value: '65'
        }
      ],
      BList: [
        {
          name: '组织设置',
          value: '55'
        },
        {
          name: '岗位角色职责',
          value: '63'
        },
        {
          name: '岗位协同 RACI',
          value: '59'
        },
        {
          name: '组织岗位 KPI',
          value: '49'
        }
      ],
      CList: [
        {
          name: '人员动力',
          value: '53'
        },
        {
          name: '人员能力要求',
          value: '51'
        },
        {
          name: '人员能力评估',
          value: '58'
        },
        {
          name: '能力培训',
          value: '62'
        }
      ],
      DList: [
        {
          name: '系统赋能',
          value: '36'
        },
        {
          name: '数据治理',
          value: '48'
        },
        {
          name: '系统改善规划',
          value: '36'
        },
        {
          name: '自动化与智能化设备',
          value: '0'
        }
      ],
      EList: [
        {
          name: '算法赋能',
          value: '35'
        },
        {
          name: '业务模型优化',
          value: '36'
        },
        {
          name: '数据驱动决策',
          value: '27'
        },
        {
          name: '自动化流程优化',
          value: '29'
        },
        {
          name: '智能风险预警',
          value: '30'
        }
      ]
    },
    {
      name: '制定产品分群规则',
      A: 56.3,
      B: 55.8,
      C: 58.5,
      D: 32.3,
      E: 31.6,
      value: 46.1,
      changeType: -1,
      changeScore: 2.5
    },
    {
      name: '制定预测订单库存冲减规则',
      A: 53.5,
      B: 55.3,
      C: 54.8,
      D: 31.8,
      E: 37.6,
      value: 46.1,
      changeType: 1,
      changeScore: 1.5
    },
    {
      name: '制定需求优先级规则',
      A: 53.3,
      B: 48.5,
      C: 60.3,
      D: 35.0,
      E: 35.4,
      value: 46.0,
      changeType: null,
      changeScore: 0
    },
    {
      name: '制定分配策略',
      A: 57.3,
      B: 57.8,
      C: 57.5,
      D: 38.5,
      E: 30.6,
      value: 47.5,
      changeType: -1,
      changeScore: 1.2
    }
  ]
})

const router = useRouter()
const toDetail = (item, index) => {
  console.log('item :>> ', item)
  emit('setComponentName', item.name)
  router.push(
    `/AI/displayer/overview/componentDetail?modelId=${modelId.value}&diagnosticType=${diagnosticType.value}&moduleCode=${item.code}`
  )
}

const back = () => {
  emit('setComponentName', '')
  router.back()
}

const setColor = score => {
  const scoreRanges = [
    '#B2FDFE, rgba(178,253,254,0.11)',
    '#83FDFE, rgba(131,253,254,0.11)',
    '#34EAFF, rgba(52,234,255,0.11)',
    '#3ED4FF, rgba(131,253,254,0.11)',
    '#22B5FA, rgba(34,181,250,0.11)',
    '#2589EC, rgba(37,137,236,0.11)',
    '#0D69C1, rgba(13,105,193,0.11)',
    '#08549B, rgba(8,84,155,0.11)',
    '#03396E, rgba(3,57,110,0.11)',
    '#032B4D, rgba(3,43,77,0.11)'
  ]
  let index = parseInt(score / 10) == 10 ? 9 : parseInt(score / 10)
  return scoreRanges[index]
}

// 初始化
console.log('route.query :>> ', route.query)
let { modelId: mId, diagnosticType: dType, moduleCode: mCode } = route.query
modelId.value = mId
diagnosticType.value = dType
moduleCode.value = mCode

// 监听路由变化
watch(
  () => route.query,
  value => {
    console.log('value :>> ', value)
    let { modelId: mId, diagnosticType: dType, moduleCode: mCode } = value
    modelId.value = mId
    diagnosticType.value = dType
    moduleCode.value = mCode
  }
)
</script>

<style lang="scss" scoped>
.score_bar {
  position: relative;
  width: calc(100% - 30px);
  height: 20px;
  background-color: #f5f7f7;
  margin-top: 10px;

  .content_bar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #85e5ff;
  }

  .text {
    position: absolute;
    left: 100%;
    top: -3px;
    color: #000;
    white-space: nowrap;
    font-size: 12px;
  }

  .name {
    width: 100%;
    text-align: left;
  }
}
.align_left {
  text-align: left;
}
</style>
