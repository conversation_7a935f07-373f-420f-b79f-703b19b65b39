<template>
	<div>
		<div class="edu_info_item" v-for="(item, index) in eduInfoData" :key="item.id">
			<el-input class="item school_name" v-model="item.graduateSchool" placeholder="填写院校名称"></el-input>
			<el-date-picker class="item" v-model="item.graduateDate" type="date" value-format="YYYY-MM-DD" placeholder="选择日期"></el-date-picker>
			<el-select class="item" v-model="item.qualification" placeholder>
				<el-option v-for="item in educationOption" :key="item.dictCode" :label="item.codeName" :value="item.dictCode"></el-option>
			</el-select>
			<el-select class="item" v-model="item.postRelated" placeholder>
				<el-option v-for="item in yesOrNo" :key="item.dictCode" :label="item.codeName" :value="item.dictCode"></el-option>
			</el-select>
			<el-select class="item" v-model="item.industryRelated" placeholder>
				<el-option v-for="item in yesOrNo" :key="item.dictCode" :label="item.codeName" :value="item.dictCode"></el-option>
			</el-select>
			<div class="item item_icon_wrap">
				<i class="item_icon el-icon-delete icon_del" @click="deleteItem(item,index)"></i>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: "eduInfoItem",
		props: {
			eduInfoData: {
				type: Array,
				default: function() {
					return [{
						id: "1",
						schoolName: "",
						graduationDate: "",
						education: "",
						post: "",
						industry: ""
					}];
				}
			}
		},
		created() {
			this.$getDocList(['QUALIFICATION', 'YES_NO']).then(res => {
				console.log(res);
				this.educationOption = res.QUALIFICATION;
				this.yesOrNo = res.YES_NO;
			})
		},
		data() {
			return {
				yesOrNo: [],
				educationOption: [],
			};
		},
		methods: {
			deleteItem(item, index) {
				this.$emit("deleteItem", item, index);
			}
		}
	};
</script>

<style scoped lang="scss">
	.edu_info_item {
		.item {
			width: 23%;
		}

		.item_icon_wrap {
			text-align: center;
			width: 10%;
		}
	}
</style>
