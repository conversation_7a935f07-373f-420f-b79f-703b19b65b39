<template>
  <div class="data_upload">
    <div class="page_second_title">绩效指标数据</div>
    <div class="table_list">
      <div class="search_box">
        <div class="form_div">
          <div class="title">筛选</div>
          <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item label>
              <el-input
                placeholder="按岗位名称进行检索"
                suffix-icon="el-icon-search"
                v-model="formInline.postName"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label>
              <el-input
                v-model="formInline.userName"
                suffix-icon="el-icon-search"
                placeholder="按姓名进行检索"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" class="page_add_btn" @click="getEnqPostFun()">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="btn_box">
          <el-button type="primary" class="page_add_btn btn_color" @click="exportDownloadFun()">导出</el-button>
          <el-button type="primary" class="page_add_btn" @click="importStaffDialog = true">导入</el-button>
        </div>
      </div>
      <div>
        <tableComponent
          :selectionStatus="false"
          :height="'320'"
          :needIndex="true"
          :tableData="tableData"
          :size="'mini'"
          border
          :needPagination="true"
          @pageChange="pageChange"
          @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange"
        ></tableComponent>
      </div>
      <div class="btn_wrap align_center marginT_30">
        <el-button class="page_confirm_btn btn" type="primary" @click="prevStep">上一步</el-button>
        <!-- <el-button class="page_confirm_btn btn" type="primary" @click="prevStep">提交</el-button> -->
      </div>
    </div>

    <!-- 导入弹窗 -->
    <el-dialog title="开始导入" v-model="importStaffDialog" width="800" :before-close="importStaffDialogBeforeClose">
      <div class="import_staff_wrap">
        <div class="import_staff_title">操作步骤:</div>
        <div class="oper_step">
          <p>1、下载《目标与绩效实际数据导入模板》。</p>
          <p>2、打开下载表，维护相应的数据。</p>
          <p>3、信息输入完毕，点击"选择文件"按钮，选择excel文档。</p>
          <p>4、点击"开始导入",导入中如有任何疑问，请致电010-86482868。</p>
        </div>
        <div class="downBtn" @click="exportDownloadFun">立即下载《目标与绩效实际数据导入模板》</div>
        <div class="upload_file_wrap">
          <el-input placeholder="请输入内容" v-model="fileName" readonly>
            <template v-slot:append>
              <label for="up" class="upload_label">
                选择文件
                <!-- <el-button type="primary">选择文件</el-button> -->
                <input
                  id="up"
                  style="display: none"
                  ref="file"
                  type="file"
                  class="form-control page_clear_btn"
                  @change="fileChange"
                />
              </label>
            </template>
          </el-input>
        </div>
        <!-- <div class="import_staff_title">当手机号重复时:</div>
                <div class="marginT_16">
                    <el-radio-group v-model="phoneType">
                        <el-radio label="Y">覆盖更新</el-radio>
                        <el-radio label="N">不导入</el-radio>
                    </el-radio-group>
        </div>-->
      </div>
      <template v-slot:footer>
        <div>
          <el-button class="page_clear_btn" @click="importStaffDialog = false">取 消</el-button>
          <el-button type="primary" class="page_add_btn" @click="importStaff">开始导入</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPerformancData, exportPerformancData, readPerformancData, updateEnqStatus } from '../../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'

// Props
const props = defineProps({
  getEnqId: Function,
  isEdit: Boolean
})

// Emits
const emit = defineEmits(['prevStep'])

// Router
const router = useRouter()

// 响应式数据
const submitStatus = ref(true) //防止多次调用保存接口
const enqId = ref(null)
const tableData = reactive({
  columns: [
    {
      label: '一级组织',
      prop: 'oneLevelName'
    },
    {
      label: '二级组织',
      prop: 'twoLevelName'
    },
    {
      label: '三级组织',
      prop: 'threeLevelName'
    },
    {
      label: '四级组织',
      prop: 'fourLevelName'
    },
    {
      label: '五级组织',
      prop: 'fiveLevelName'
    },
    {
      label: '姓名',
      prop: 'userName'
    },
    {
      label: '岗位',
      prop: 'postName'
    },
    {
      label: '上级',
      prop: 'superiorName'
    },
    {
      label: '指标',
      prop: 'kpiName'
    },
    {
      label: '目标',
      prop: 'kpiObjective'
    },
    {
      label: '评价标准',
      prop: 'evaluationStandard'
    },
    {
      label: '实际',
      prop: 'kpiActual'
    },
    {
      label: '目标权重',
      prop: 'weight'
    },
    {
      label: '系统评价',
      prop: 'overallMerit'
    }
  ],
  page: {
    total: 0,
    current: 1,
    size: 10
  },
  data: []
}) //全部
const formInline = reactive({
  userName: '',
  postName: ''
})
// 导入弹窗
const importStaffDialog = ref(false)
const fileName = ref('')
const uploadFile = ref(null)

// 方法
//下载模板
const exportDownloadFun = () => {
  let params = {
    enqId: enqId.value
  }
  exportPerformancData(params).then(res => {
    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '目标与绩效实际数据.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
    // this.$exportDownloadFile(res.data, '目标与绩效实际数据')
  })
}

//导入
const importStaffDialogBeforeClose = done => {
  fileName.value = ''
  document.getElementById('up').value = null
  done()
}

const fileChange = e => {
  let formData = new FormData()
  //把文件信息放入对象中
  let file = e.target.files[0]
  //把文件信息放入对象中
  formData.append('file', file)
  formData.append('enqId', enqId.value)
  fileName.value = file.name
  uploadFile.value = formData
}

const importStaff = () => {
  readPerformancData(uploadFile.value).then(res => {
    if (res.code == '200') {
      ElMessage.success(res.msg)
      uploadFile.value = null
      importStaffDialog.value = false
      getEnqPostFun()
    } else {
      ElMessage.error('模板文件格式不正确，请重新下载模板文件')
    }
  })
  importStaffDialog.value = true
}

//翻页
const handleCurrentChange = currentPage => {
  tableData.page.current = currentPage
  getEnqPostFun()
}

const handleSizeChange = size => {
  tableData.page.size = size
  getEnqPostFun()
}

const pageChange = (pageSize, currentPage) => {
  tableData.page.size = pageSize
  tableData.page.current = currentPage
  getEnqPostFun()
}

const goback = () => {
  router.push('/talentReviewHome/talentReview')
}

const prevStep = () => {
  emit('prevStep')
}

//查询
const getEnqPostFun = () => {
  getPerformancData({
    enqId: enqId.value,
    userName: formInline.userName,
    current: tableData.page.current,
    postName: formInline.postName,
    size: tableData.page.size
  }).then(res => {
    console.log(res)
    if (res.code == '200') {
      tableData.data = res.data
      tableData.page.total = res.total
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 生命周期
onMounted(() => {
  // 获取盘点id
  enqId.value = props.getEnqId()
  getEnqPostFun()
})
</script>

<style scoped lang="scss">
.table_list {
  //height: 400px;
  padding: 10px;

  position: relative;
  .search_box {
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .form_div {
      display: flex;
      height: 40px;
      .title {
        margin-right: 10px;
      }
    }
    .btn_box {
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 99;
      .btn_color {
        background-color: #666;
        border-color: #666;
      }
    }
  }
  .btn_wrap {
    .btn {
      width: 180px;
    }
  }
}

// 导入
.import_staff_wrap {
  .import_staff_title {
    color: #515c71;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .oper_step {
    line-height: 28px;
    color: #515c71;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .download_file {
    display: block;
    margin-bottom: 16px;
  }

  .downBtn {
    color: #449cff;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
  }
  .upload_file_wrap {
    margin-bottom: 16px;

    .upload_label {
      display: block;
      height: 28px;
      line-height: 28px;
      width: 100%;
      cursor: pointer;
    }
  }
}
</style>
