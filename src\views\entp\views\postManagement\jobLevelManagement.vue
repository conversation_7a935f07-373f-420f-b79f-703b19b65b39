<template>
  <div class="org_management_wrap bg_write">
    <div class="page_main_title">职层管理</div>
    <div class="page_section">
      <div class="org_management_center clearfix">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="tree_title">职层分类</div>
            <div class="btn_wrap">
              <el-button link size="mini" class="oper_btn" @click="addTreeNode">新增</el-button>
              <el-button link size="mini" class="oper_btn" @click="editTreeNode">修改</el-button>
              <el-button link size="mini" class="oper_btn" @click="deleteTreeNode">删除</el-button>
            </div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio
              :defaultCheckedKeys="defaultCheckedKeys"
              :needCheckedFirstNode="false"
              :canCancel="true"
              :treeData="treeData"
              @clickCallback="clickCallback"
            ></tree-comp-radio>
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <table-component
            :tableData="tableData"
            :needIndex="needIndex"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          >
            <template #oper>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button
                    @click.prevent="tableEdit(scope.$index, tableData.data)"
                    icon="el-icon-edit"
                    link
                    class="icon_edit"
                  ></el-button>
                  <el-button
                    class="color_danger icon_del"
                    @click.prevent="tableDeleteRow(scope.$index, tableData.data)"
                    link
                    icon="el-icon-delete"
                  ></el-button>
                </template>
              </el-table-column>
            </template>
          </table-component>
        </div>
      </div>
      <jobLevelTreePopUp
        v-model:show="show"
        :checkedId="checkedId"
        :popupTitle="popupTitle"
        :isDeleteSign="isDeleteSign"
        :tebleEditId="tebleEditId"
      ></jobLevelTreePopUp>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import jobLevelTreePopUp from '../tPopUpComps/jobLevelTreePopUp'
import { jobLevelTree, searchJobLevel, deleteJobLevel } from '../../request/api'

const needIndex = ref(true)
const show = ref(false)
const popupTitle = ref('')
const isLastNodeSign = ref('')
const checkedId = ref('')
const treeData = ref([])
const defaultCheckedKeys = ref([])
const tableData = reactive({
  columns: [
    {
      label: '职层编码',
      prop: 'jobLevelCode'
    },
    {
      label: '职层名称',
      prop: 'jobLevelName'
    },
    {
      label: '职层分类',
      prop: 'jobClassify'
    },
    {
      label: '职层说明',
      prop: 'jobLevelDesc'
    }
  ],
  data: [],
  page: {
    total: 0,
    current: 1,
    size: 10
  }
})
const selectJobLevelCode = ref('')
const parenJobLevelCode = ref('')
const jobLevelCodes = ref('')
const tableDeleteId = ref('')
const tebleEditId = ref('')
const isDeleteSign = ref(false)

onMounted(() => {
  jobLevelTreeFun()
  searchJobLevelFun()
})

function jobLevelTreeFun() {
  jobLevelTree({}).then(res => {
    if (res.length > 0) {
      treeData.value = res
    } else {
      treeData.value = []
    }
  })
}
function clickCallback(val, isLastNode) {
  checkedId.value = val
  isLastNodeSign.value = isLastNode
  if (!isLastNode) {
    parenJobLevelCode.value = checkedId.value
    jobLevelCodes.value = ''
  } else {
    jobLevelCodes.value = checkedId.value
    parenJobLevelCode.value = ''
  }
  tableData.page.current = 1
  searchJobLevelFun()
}
function searchJobLevelFun() {
  searchJobLevel({
    parenJobLevelCode: parenJobLevelCode.value,
    jobLevelCodes: jobLevelCodes.value,
    current: tableData.page.current,
    size: tableData.page.size
  }).then(res => {
    if (res.code == 200) {
      if (res.data.length > 0) {
        tableData.data = res.data.map(item => {
          return {
            jobLevelCode: item.jobLevelCode,
            jobLevelName: item.jobLevelName,
            jobClassify: item.dataType,
            jobLevelDesc: item.jobLevelDesc,
            sortNbr: item.sortNbr
          }
        })
        tableData.page.total = res.total
      } else {
        tableData.data = []
        tableData.page.current = 1
      }
    } else {
      tableData.data = []
      tableData.page.current = 1
    }
  })
}
function addTreeNode() {
  popupTitle.value = true
  show.value = true
}
function editTreeNode() {
  tebleEditId.value = ''
  if (!checkedId.value) {
    ElMessage.warning('请选择修改职层！')
    return
  }
  popupTitle.value = false
  show.value = true
}
function tableEdit(index, rows) {
  tebleEditId.value = rows[index].jobLevelCode
  popupTitle.value = false
  show.value = true
}
function deleteTreeNode() {
  if (!checkedId.value) {
    ElMessage.warning('请选择删除职层！')
    return
  } else {
    ElMessageBox.confirm('确认删除?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        deleteJobLevelFun(checkedId.value)
      })
      .catch(() => {})
  }
}
function tableDeleteRow(index, rows) {
  tableDeleteId.value = rows[index].jobLevelCode
  ElMessageBox.confirm('确认删除?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      deleteJobLevelFun(tableDeleteId.value)
    })
    .catch(() => {})
}
function deleteJobLevelFun(code) {
  isDeleteSign.value = false
  deleteJobLevel({
    jobLevelCode: code
  }).then(res => {
    if (res.code == 200) {
      isDeleteSign.value = true
      if (tableDeleteId.value == checkedId.value || isLastNodeSign.value) {
        checkedId.value = ''
        parenJobLevelCode.value = ''
        defaultCheckedKeys.value = []
      }
      jobLevelCodes.value = ''
      jobLevelTreeFun()
      searchJobLevelFun()
      ElMessage.success(res.msg)
    } else {
      ElMessage.warning(res.msg)
    }
  })
}
function handleSizeChange(val) {
  tableData.page.size = val
  searchJobLevelFun()
}
function handleCurrentChange(val) {
  tableData.page.current = val
  searchJobLevelFun()
}
watch(show, val => {
  if (!val) {
    jobLevelTreeFun()
    searchJobLevelFun()
  }
})
</script>

<style scoped lang="scss"></style>
