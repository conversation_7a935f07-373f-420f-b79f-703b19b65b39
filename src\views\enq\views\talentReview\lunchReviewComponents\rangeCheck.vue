<template>
  <div class="clearfix" :class="{ disable: !isEdit }">
    <div class="marginT_30">
      <div class="page_second_title">选择人员</div>
      <div class="marginT_20 clearfix">
        <div class="selection_range_left clearfix">
          <div class="filter_tree_wrap">
            <div class="selection_range_title">部门</div>
            <div class="tree_list">
              <tree-comp-checkbox
                :treeData="cmpyOrgInfoList"
                @node-click-callback="nodeClickCallback"
              ></tree-comp-checkbox>
            </div>
          </div>
        </div>

        <div class="filter_table_wrap">
          <div class="selection_range_title">盘点信息</div>
          <div class="table_list">
            <div class="btn_box" v-if="isEdit">
              <el-button type="primary" class="page_add_btn" @click="exportDownloadFun">导出</el-button>
            </div>
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <el-tab-pane label="未设置上级人员" name="first">
                <tableComponent
                  :selectionStatus="false"
                  :checkSelection="checkSelection"
                  :height="'300'"
                  :needIndex="true"
                  :tableData="tableData1"
                  :size="'mini'"
                  border
                  :needPagination="true"
                  @pageChange="pageChange"
                  @handleSizeChange="handleSizeChange"
                  @handleCurrentChange="handleCurrentChange"
                ></tableComponent>
              </el-tab-pane>
              <el-tab-pane label="未设置部门负责人" name="second">
                <tableComponent
                  :selectionStatus="false"
                  :checkSelection="checkSelection"
                  :height="'300'"
                  :needIndex="true"
                  :tableData="tableData"
                  :size="'mini'"
                  border
                  :needPagination="true"
                  @pageChange="pageChange"
                  @handleSizeChange="handleSizeChange"
                  @handleCurrentChange="handleCurrentChange"
                ></tableComponent>
              </el-tab-pane>
              <el-tab-pane label="未设置职位" name="three">
                <tableComponent
                  :selectionStatus="false"
                  :checkSelection="checkSelection"
                  :height="'300'"
                  :needIndex="true"
                  :tableData="tableData2"
                  :size="'mini'"
                  border
                  :needPagination="true"
                  @pageChange="pageChange"
                  @handleSizeChange="handleSizeChange"
                  @handleCurrentChange="handleCurrentChange"
                ></tableComponent>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>

    <div class="clearfix"></div>
    <div class="btn_wrap align_center marginT_30" v-if="isEdit">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="submit">下一步</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getFilter,
  getActivePost,
  saveEnqRange,
  checkEnqScope,
  checkEnqLoser,
  exportEnqSubUserInfoData
} from '../../../request/api'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import asideFilterCheckbox from './asideFilterCheckbox'
import treeCompCheckbox from '@/components/talent/treeComps/treeCompCheckbox'

const props = defineProps({
  getEnqId: {
    type: Function,
    required: true
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['prevStep', 'nextStep'])

const submitStatus = ref(true)
const enqId = ref(null)
const listPostCode = ref([])
const enqUserCount = ref(0)
const checkedJobClassCode = ref([])
const checkedJobLevelCode = ref([])
const checkedOrgCode = ref([])
const choosePostAllStatus = ref(true)
const resPostList = ref([])
const checkedPostList = ref([])
const checkedPostCodeArr = ref([])
const cmpyJobClassList = ref([])
const cmpyJobLevelList = ref([])
const cmpyOrgInfoList = ref([])
const activeName = ref('first')
const checkSelection = ref([])

const tableData2 = ref({
  columns: [
    { label: '一级组织', prop: 'oneLevelName' },
    { label: '二级组织', prop: 'twoLevelName' },
    { label: '三级组织', prop: 'threeLevelName' },
    { label: '四级组织', prop: 'fourLevelName' },
    { label: '五级组织', prop: 'fiveLevelName' },
    { label: '岗位', prop: 'postName' },
    { label: '职位', prop: 'jobName' }
  ],
  page: {
    total: 0,
    current: 1,
    size: 10
  },
  data: []
})

const tableData = ref({
  columns: [
    { label: '一级组织', prop: 'oneLevelName' },
    { label: '二级组织', prop: 'twoLevelName' },
    { label: '三级组织', prop: 'threeLevelName' },
    { label: '四级组织', prop: 'fourLevelName' },
    { label: '五级组织', prop: 'fiveLevelName' },
    { label: '部门负责人', prop: 'orgLeaderName' }
  ],
  page: {
    total: 0,
    current: 1,
    size: 10
  },
  data: []
})

const tableData1 = ref({
  columns: [
    { label: '一级组织', prop: 'oneLevelName' },
    { label: '二级组织', prop: 'twoLevelName' },
    { label: '三级组织', prop: 'threeLevelName' },
    { label: '四级组织', prop: 'fourLevelName' },
    { label: '五级组织', prop: 'fiveLevelName' },
    { label: '员工编码', prop: 'employeeCode' },
    { label: '员工姓名', prop: 'userName' },
    { label: '任职岗位', prop: 'postName' },
    { label: '直线上级编码', prop: 'parentLevelCode', width: 100 },
    { label: '直线上级姓名', prop: 'parentLevelName', width: 100 }
  ],
  page: {
    total: 0,
    current: 1,
    size: 10
  },
  data: []
})

watch(checkedPostList, val => {
  listPostCode.value = []
  enqUserCount.value = 0
  val.forEach(item => {
    listPostCode.value.push(item.postCode)
    enqUserCount.value += item.userNum
  })
})

const handleClick = () => {
  checkEnqScopeFun()
}

const exportDownloadFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      type: activeName.value == 'first' ? 'S' : activeName.value == 'second' ? 'R' : 'J'
    }

    const res = await exportEnqSubUserInfoData(params)
    const fileName =
      activeName.value == 'first' ? '未设置上级人员' : activeName.value == 'second' ? '未设置部门负责人' : '未设置职位'

    const blob = new Blob([res.data])
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `${fileName}.xlsx`
    link.click()
    URL.revokeObjectURL(link.href)
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const handleCurrentChange = currentPage => {
  if (activeName.value == 'first') {
    tableData1.value.page.current = currentPage
  } else if (activeName.value == 'second') {
    tableData.value.page.current = currentPage
  } else {
    tableData2.value.page.current = currentPage
  }
  checkEnqScopeFun()
}

const handleSizeChange = size => {
  if (activeName.value == 'first') {
    tableData1.value.page.size = size
  } else if (activeName.value == 'second') {
    tableData.value.page.size = size
  } else {
    tableData2.value.page.size = size
  }
  checkEnqScopeFun()
}

const pageChange = (pageSize, currentPage) => {
  if (activeName.value == 'first') {
    tableData1.value.page.size = pageSize
    tableData1.value.page.current = currentPage
  } else if (activeName.value == 'second') {
    tableData.value.page.size = pageSize
    tableData.value.page.current = currentPage
  } else {
    tableData2.value.page.size = pageSize
    tableData2.value.page.current = currentPage
  }
  checkEnqScopeFun()
}

const getFilterFun = async () => {
  try {
    const res = await getFilter()
    if (res.code == 200) {
      const data = res.data
      data.cmpyJobClassList.forEach(item => {
        delete item.children
      })
      cmpyJobClassList.value = data.cmpyJobClassList

      data.cmpyJobLevelList.forEach(item => {
        delete item.children
      })
      cmpyJobLevelList.value = data.cmpyJobLevelList
      cmpyOrgInfoList.value = data.cmpyOrgInfoList
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

const nodeClickCallback = checkedCode => {
  checkedOrgCode.value = checkedCode
  checkEnqScopeFun()
}

const jobClassCallback = checkedCode => {
  checkedJobClassCode.value = checkedCode
  checkEnqScopeFun()
}

const jobLevelCallback = checkedCode => {
  checkedJobLevelCode.value = checkedCode
  checkEnqScopeFun()
}

const saveEnqRangeFun = async type => {
  submitStatus.value = false
  try {
    const params = {
      enqId: enqId.value
    }
    const res = await checkEnqLoser(params)
    if (res.code == 200) {
      ElMessage.success(res.msg)
      submitStatus.value = true
      emit(type)
    } else {
      ElMessage.error(res.msg)
      submitStatus.value = true
    }
  } catch (error) {
    ElMessage.error(error.message)
    submitStatus.value = true
  }
}

const prevBtn = async () => {
  try {
    await ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '保存',
      cancelButtonText: '放弃修改'
    })
    await saveEnqRangeFun('prevStep')
  } catch (action) {
    if (action == 'cancel') {
      ElMessage.info('已放弃修改并返回上一步')
      emit('prevStep')
    } else {
      ElMessage.info('取消返回上一步')
    }
  }
}

const submit = () => {
  if (submitStatus.value) {
    saveEnqRangeFun('nextStep')
  }
}

const checkEnqScopeFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      current:
        activeName.value == 'first'
          ? tableData1.value.page.current
          : activeName.value == 'second'
            ? tableData.value.page.current
            : tableData2.value.page.current,
      size:
        activeName.value == 'first'
          ? tableData1.value.page.size
          : activeName.value == 'second'
            ? tableData.value.page.size
            : tableData2.value.page.size,
      orgCodes: checkedOrgCode.value.join(','),
      type: activeName.value == 'first' ? 'S' : activeName.value == 'second' ? 'R' : 'J'
    }

    const res = await checkEnqScope(params)
    if (res.code == 200) {
      if (activeName.value == 'first') {
        tableData1.value.data = res.data
        tableData1.value.page.total = res.total
      } else if (activeName.value == 'second') {
        tableData.value.data = res.data
        tableData.value.page.total = res.total
      } else {
        tableData2.value.data = res.data
        tableData2.value.page.total = res.total
      }
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    ElMessage.error(error.message)
  }
}

onMounted(() => {
  enqId.value = props.getEnqId()
  getFilterFun()
  checkEnqScopeFun()
})
</script>

<style scoped lang="scss">
.disable {
  :deep(.el-table) {
    pointer-events: none;
  }
}

.selection_range_left {
  float: left;

  .aside_filte_wrap {
    float: left;
    width: 160px;
  }

  .filter_tree_wrap {
    width: 220px;
    float: left;
    padding-right: 16px;

    .tree_title {
      font-size: 14px;
      padding-left: 16px;
    }

    .title {
      font-size: 16px;
      margin-bottom: 2px;
      background: #e5f0f9;
      padding: 5px 16px;
      color: #525e6c;
    }

    .tree_list {
      max-height: 400px;
      padding: 10px;
      overflow-y: auto;
      border: 1px solid #e5e5e5;
      border-top: none;
    }
  }
}

.filter_table_wrap {
  float: left;
  width: calc(100% - 230px);

  .table_list {
    height: 400px;
    padding: 10px;
    overflow-y: auto;
    border: 1px solid #e5e5e5;
    border-top: none;
    position: relative;

    .el-tabs__item {
      padding: 0 15px;
    }

    .btn_box {
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 99;
    }
  }
}

.selection_range_title {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  background: #e5f0f9;
  padding: 0px 16px;
  color: #525e6c;
  height: 40px;
}
</style>
