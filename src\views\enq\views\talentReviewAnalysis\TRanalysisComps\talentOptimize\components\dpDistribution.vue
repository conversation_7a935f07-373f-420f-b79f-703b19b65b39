<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">建议优化人才类型</div>
          <div class="content_item_content" id="risk_type"></div>
        </div>
      </div>
      <div class="content_item el-col-16">
        <div class="content_item_main">
          <div class="content_item_title">部门分布</div>
          <div class="content_item_content" id="org_distr"></div>
        </div>
      </div>
      <!-- <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">岗位分布</div>
                    <div class="content_item_content" id="post_distr"></div>
                </div>
            </div> -->
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            详情列表
            <el-button class="fr" type="primary"  @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { qualityAllOrgDist, queryPostDistributionList, exportData } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'

const route = useRoute()
const enqId = ref(route.query.enqId)
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref([])

const view = reactive({
  data: []
})

const orgView = reactive({
  data: []
})

const postView = reactive({
  data: []
})

const distributionView = reactive({})

const page = ref(1)
const size = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '所属组织',
      prop: 'org_name'
    },
    {
      label: '职位编码',
      prop: 'job_code'
    },
    {
      label: '职位名称',
      prop: 'job_name'
    },
    {
      label: '职位族群',
      prop: 'parent_job_class_name'
    },
    {
      label: '职位序列',
      prop: 'job_class_name'
    },
    {
      label: '职层',
      prop: 'job_level_name'
    },
    {
      label: '待优化人才',
      prop: 'cc'
    },
    {
      label: '关注人才',
      prop: 'bc'
    },
    {
      label: '稳定人才',
      prop: 'cb'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('risk_type', 'YBar', '230', '220', view)
  echartsRenderPage('org_distr', 'YStack', '470', '220', distributionView)
}

const qualityAllOrgDistFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      dictCode: 'TALENT_TYPE'
    }
    const res = await qualityAllOrgDist(params)
    if (res.code == 200) {
      const data = res.data
      data.view.map(item => {
        item.value = item.value + '%'
      })
      view.data = data.view
      Object.assign(distributionView, data.distributionView)
      initChart()
    }
  } catch (error) {
    console.error('获取部门分布数据失败:', error)
  }
}

const getCode = (orgCode, jobClassCode) => {
  jobClassCode.value = jobClassCode
  orgCode.value = orgCode
  page.value = 1
  qualityAllOrgDistFun()
  getTableData()
}

const handleCurrentChange = page => {
  page.value = page
  getTableData()
}

const handleSizeChange = size => {
  size.value = size
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryPostDistributionList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error('获取表格数据失败:', error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'o'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '部门岗位分布详情列表')
  } catch (error) {
    console.error('导出数据失败:', error)
  }
}

onMounted(() => {
  filterData.value = route.attrs.filterData
  getTableData()
  qualityAllOrgDistFun()
})
</script>

<style scoped lang="scss"></style>
