<script setup>
defineOptions({ name: 'model' })
const modelList = ref([
  {
    name: '战略绩效闭环运营',
    list: [
      {
        status: '2',
        name: '大客户管理',
        completed: 25,
        total: 25
      },
      {
        status: '2',
        name: '战略意图定义与共识构建',
        completed: 25,
        total: 25
      },
      {
        status: '2',
        name: '业务设计与商业模式创新',
        completed: 25,
        total: 25
      },
      {
        status: '2',
        name: '创新焦点与机会管理',
        completed: 25,
        total: 25
      },
      {
        status: '2',
        name: '战略解码与目标穿透',
        completed: 25,
        total: 25
      },
      {
        status: '2',
        name: '关键任务与资源统筹',
        completed: 25,
        total: 25
      },
      {
        status: '1',
        name: '组织协同与变革管理',
        completed: 15,
        total: 25
      },
      {
        status: '0',
        name: '执行体系与流程构建',
        completed: 0,
        total: 25
      },
      {
        status: '0',
        name: '战略校准与偏差修复',
        completed: 0,
        total: 25
      },
      {
        status: '0',
        name: '战略绩效评估与激励',
        completed: 0,
        total: 25
      },
      {
        status: '0',
        name: '能力评估小结',
        completed: 0,
        total: 25
      }
    ]
  }
])
const router = useRouter()
const startEval = () => {
  router.push({ path: '/AI/targetSpotDiagnosis/project/diagnose/answer' })
}
</script>
<template>
  <div class="model-wrap">
    <div class="model-list" v-for="item in modelList" :key="item.name">
      <div class="model-name">
        <div class="name">{{ item.name }}</div>
        <div class="line"></div>
        <div class="expand">
          <div class="text">收起</div>
          <SvgIcon name="eval-arrow-bg" />
        </div>
      </div>
      <div class="model-content">
        <div class="list" v-for="list in item.list" :key="list.name">
          <div class="list-status">
            <SvgIcon :name="'eval-status-' + list.status" width="52px" height="20px" />
          </div>
          <div class="list-name">{{ list.name }}</div>
          <div class="list-process">
            <div class="process">
              <b>{{ list.completed }}</b
              >/{{ list.total }}
            </div>
            <div class="process-bar">
              <div class="bar" :style="{ width: (list.completed / list.total) * 100 + '%' }"></div>
            </div>
          </div>
          <div class="list-btn" :class="{ disable: list.status == 2 }" @click="startEval">进入</div>
        </div>
      </div>
    </div>
    <div class="submit-wrap">
      <div class="tip">各板块完成评估后，可点击“提交”按钮，提交完成后，将无法修改 。</div>
      <div class="btn">提交</div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.model-wrap {
  .model-list {
    padding: 0 20px 20px;
    // box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
    border-radius: 8px 8px 8px 8px;
    .model-name {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .name {
        font-weight: 600;
        font-size: 16px;
        color: #53a9f9;
        line-height: 16px;
        padding: 20px 0;
      }
      .line {
        flex: 1;
        height: 2px;
        background: #eeeeee;
        border-radius: 0px 0px 0px 0px;
        margin: 0 8px;
      }
      .expand {
        display: flex;
        align-items: center;
        font-size: 14px;
        gap: 8px;
        color: #888888;
        cursor: pointer;
      }
    }
    .model-content {
      display: flex;
      align-items: stretch;
      flex-flow: row wrap;
      gap: 10px;
      .list {
        width: calc((100% - 30px) / 4);
        position: relative;
        padding: 18px 20px 10px;
        background: #ffffff;
        border-radius: 5px 5px 5px 5px;
        border: 1px solid #c6dbf3;
        .list-status {
          position: absolute;
          top: -1px;
          right: -1px;
        }
        .list-name {
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          line-height: 23px;
          margin-bottom: 10px;
        }
        .list-process {
          margin-bottom: 20px;
          .process {
            text-align: right;
            font-size: 14px;
            color: #898989;
            line-height: 20px;
            margin-bottom: 10px;
            b {
              color: #333;
            }
          }
          .process-bar {
            position: relative;
            height: 6px;
            background: #f3f3f3;
            border-radius: 3px 3px 3px 3px;
            .bar {
              position: absolute;
              top: 0;
              left: 0;
              height: 100%;
              background: linear-gradient(223deg, #40a0ff 0%, #acd5ff 100%);
              border-radius: 3px 3px 3px 3px;
            }
          }
        }
        .list-btn {
          font-size: 14px;
          color: #54d898;
          text-align: center;
          line-height: 32px;
          background: #f2fcf7;
          border-radius: 10px 10px 10px 10px;
          background: #f2f6fa;
          color: #53a9f9;
          cursor: pointer;
          &.disable {
            color: #666666;
            background: #f1f1f1;
            cursor: not-allowed;
            pointer-events: none;
          }
        }
      }
    }
  }
}
.submit-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 20px;
  .tip {
    font-weight: 400;
    font-size: 16px;
    color: #acacac;
    line-height: 15px;
  }
  .btn {
    width: 200px;
    line-height: 40px;
    background: #53a9f9;
    border-radius: 42px;
    font-size: 16px;
    text-align: center;
    color: #ffffff;
    cursor: pointer;
    margin-left: 23px;
  }
}
</style>
