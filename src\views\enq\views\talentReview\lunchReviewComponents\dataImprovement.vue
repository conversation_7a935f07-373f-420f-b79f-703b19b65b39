<template>
  <div class="clearfix">
    <div class="selection_range_left clearfix">
      <div class="page_second_title">选择盘点组织与岗位范围</div>
      <div class="aside_filte_wrap">
        <aside-filter-checkbox
          :item-data="filterItemData"
          :default-checked="checkedItemId"
          @get-checked-id="getCheckedJobGroupId"
        />
      </div>
      <div class="filter_tree_wrap">
        <div class="selection_range_title">按职群</div>
        <div class="tree_title">国机精工集团</div>
        <tree-comp-checkbox :tree-data="treeData" />
      </div>
    </div>
    <div class="selection_range_select_post data_inprovement">
      <div class="selection_range_title">1人</div>
      <div class="selected_post">
        <div class="item">
          <span class="post_name">人事主管--王伟</span>
          <span class="pointer close_btn">
            <el-icon><Close /></el-icon>
          </span>
        </div>
      </div>
    </div>
    <div class="review_content">
      <div class="item">
        <div class="title">数据相关人数预估</div>
        <div class="content">
          <span class="text">262</span>
          <span>人</span>
        </div>
      </div>
      <div class="item">
        <div class="title">需完善数据项目</div>
        <div class="content">
          <span class="text">28</span>
          <span>项</span>
        </div>
      </div>
      <div class="item">
        <div class="title">单人维护时间预估</div>
        <div class="content">
          <span class="text">20</span>
          <span>分钟</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Close } from '@element-plus/icons-vue'
import AsideFilterCheckbox from './asideFilterCheckbox.vue'
import TreeCompCheckbox from '@/components/talent/treeComps/treeCompCheckbox.vue'

const checkedItemId = ref(['1-2', '1-3'])
const checkedJobLevelId = ref(['1-1', '1-2'])

const filterItemData = ref({
  id: '1',
  name: '职群',
  children: [
    {
      id: '1-1',
      name: '战略运营类'
    },
    {
      id: '1-2',
      name: '市场销售类'
    },
    {
      id: '1-3',
      name: '供应链类'
    }
  ]
})

const filterItemData2 = ref({
  id: '1',
  name: '职层',
  children: [
    {
      id: '1-1',
      name: '高级总监'
    },
    {
      id: '1-2',
      name: '总监'
    },
    {
      id: '1-3',
      name: '经理'
    }
  ]
})

const treeData = ref([
  {
    id: 0,
    label: '全选',
    children: [
      {
        id: 1,
        label: '销售一部',
        children: [
          {
            id: 11,
            label: '销售总监'
          },
          {
            id: 12,
            label: '大区经理'
          }
        ]
      },
      {
        id: 2,
        label: '销售二部',
        children: [
          {
            id: 5,
            label: '华北市场',
            children: [
              {
                id: 52,
                label: '销售总监'
              },
              {
                id: 53,
                label: '大区经理'
              }
            ]
          },
          {
            id: 6,
            label: '华东市场'
          },
          {
            id: 7,
            label: '华南市场'
          }
        ]
      },
      {
        id: 3,
        label: '采购部',
        children: [
          {
            id: 31,
            label: '采购总监'
          },
          {
            id: 32,
            label: '采购经理'
          }
        ]
      }
    ]
  }
])

const getCheckedJobGroupId = id => {
  console.log(id)
}

const getCheckedJobLevelId = id => {
  console.log(id)
}
</script>

<style scoped lang="scss">
.selection_range_left {
  float: left;
  width: 400px;
  margin-right: 16px;

  .aside_filte_wrap {
    float: left;
    width: 200px;
  }

  .filter_tree_wrap {
    width: 200px;
    float: left;
    padding-left: 16px;

    .tree_title {
      font-size: 14px;
      padding-left: 16px;
    }

    .title {
      font-size: 16px;
      margin-bottom: 2px;
      background: #e5f0f9;
      padding: 5px 16px;
      color: #525e6c;
    }
  }
}

.selection_range_select_post {
  float: left;
  width: 400px;

  &.data_inprovement {
    padding-top: 37px;
  }

  .selected_post {
    padding: 5px;
    height: 400px;
    overflow-y: auto;

    .item {
      display: flex;
      justify-content: space-between;
      border: 1px solid #efefef;
      margin-bottom: 3px;
      line-height: 26px;
      color: #0099fd;
      padding: 0 5px;

      .post_name {
        width: 150px;
      }

      .post_num {
        width: 100px;
      }

      .close_btn {
        width: 40px;
        text-align: center;
      }
    }
  }
}

.selection_range_title {
  font-size: 16px;
  margin-bottom: 2px;
  background: #e5f0f9;
  padding: 5px 16px;
  color: #525e6c;
}

.review_content {
  overflow: hidden;
  padding: 16px;

  .item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;

    .title {
      color: #0099fd;
      font-weight: bold;
      margin-bottom: 9px;
    }

    .text {
      font-size: 24px;
      color: #0099fd;
    }
  }
}
</style>
