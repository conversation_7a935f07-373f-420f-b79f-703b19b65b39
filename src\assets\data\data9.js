// 指标透视罗盘
// 指标库维护
export const libraryTending = {
  // 指标信息、组织关联
  kpiList: [
    {
      id: '1',
      kpiCode: 'GYL-001',
      kpiName: '销售预测准确率（sellin口径）',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '数量',
      kpiExpression:
        '销售预测准确率 = 0.6*（N-1 月销售预测准确率）+0.4*（N-2 月销售预测准确率），N-1 月销售预测准确率 = 1-∑abs (各型号 N-1 月预测 - 实际）/∑各型号 N-1 月预测值，N-2 月销售预测准确率 = 1-∑abs (各型号 N-2 月预测值 - 实际）/∑各型号 N-2 月预测值，sellin 销量，取 SAP 开单口径。（适用于国内营销总部、分公司、海外公司）',
      kpiUnit: '%',
      dataSource: 'SAP 系统',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '正',
      parentKpiCode: '存货周转天数',
      effectKpi: '4',
      person: '王伟'
    },
    {
      id: '2',
      kpiCode: 'GYL-002',
      kpiName: '销售预测准确率（sellout口径）',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '质量',
      kpiExpression:
        '销售预测准确率 = 0.6*（N-1 月销售预测准确率）+0.4*（N-2 月销售预测准确率），N-1 月销售预测准确率 = 1-∑abs (各型号 N-1 月预测 - 实际）/∑各型号 N-1 月预测值，N-2 月销售预测准确率 = 1-∑abs (各型号 N-2 月预测值 - 实际）/∑各型号 N-2 月预测值，sellout 销量。（适用于国内营销总部、分公司、海外公司）',
      kpiUnit: '%',
      dataSource: '零售数据平台',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '正',
      parentKpiCode: '存货周转天数',
      effectKpi: '5',
      person: '陈建军​'
    },
    {
      id: '3',
      kpiCode: 'GYL-003',
      kpiName: '存货周转天数',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '效率',
      kpiExpression:
        '当月存货周转天数 = 30/（当月主营业务成本 /（期初存货占用 + 期末存货占用）/2））累计存货周转天数 = 30/（累计主营业务成本 /(期初存货占用 / 2 + 期间各月占用 + 期末存货占用 / 2））',
      kpiUnit: '天',
      dataSource: 'ERP 系统',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '负',
      parentKpiCode: '无',
      effectKpi: '3',
      person: '陈丽娟​'
    },
    {
      id: '4',
      kpiCode: 'GYL-004',
      kpiName: '订单履约周期内销',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '效率',
      kpiExpression: '订单履约周期 = 订单关闭时间 - 订单生成时间',
      kpiUnit: '天',
      dataSource: '订单管理系统',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '负',
      parentKpiCode: '无',
      effectKpi: '3',
      person: '陈丽君​'
    },
    {
      id: '5',
      kpiCode: 'GYL-005',
      kpiName: '订单履约周期出口',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '效率',
      kpiExpression: '订单履约周期 = 订单关闭时间 - 订单生成时间',
      kpiUnit: '天',
      dataSource: '跨境订单系统',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '负',
      parentKpiCode: '无',
      effectKpi: '5',
      person: '陈秀英​'
    },
    {
      id: '6',
      kpiCode: 'GYL-006',
      kpiName: '市场需求变动率（总量）',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '风险',
      kpiExpression: '市场需求变动率（总量）=（N 月实际需求量 - N 月原始需求量）/N 月原始需求量，到月度总量',
      kpiUnit: '%',
      dataSource: '需求管理系统',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '负',
      parentKpiCode: '市场需求满足率',
      effectKpi: '4',
      person: '陈志强'
    },
    {
      id: '7',
      kpiCode: 'GYL-007',
      kpiName: '市场需求变动率（结构）',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '风险',
      kpiExpression:
        '市场需求变动率（结构）=∑abs（N 月各型号实际需求量 - N 月各型号原始需求量）/∑N 月各型号原始需求量，到型号维度',
      kpiUnit: '%',
      dataSource: 'ERP-MRP 模块',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '负',
      parentKpiCode: '市场需求满足率',
      effectKpi: '4',
      person: '陈志强​'
    },
    {
      id: '8',
      kpiCode: 'GYL-008',
      kpiName: '产成品周转天数',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '效率',
      kpiExpression:
        '当月产成品周转天数 = 30/（当月主营业务成本 /（期初产成品占用 + 期末产成品占用）/2））累计产成品周转天数 = 30/（累计主营业务成本 /(期初产成品占用 / 2 + 期间各月占用 + 期末产成品占用 / 2））',
      kpiUnit: '天',
      dataSource: '库存管理系统',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '负',
      parentKpiCode: '存货周转天数',
      effectKpi: '4',
      person: '李凤霞​'
    },
    {
      id: '9',
      kpiCode: 'GYL-009',
      kpiName: '周AATP订单转化率',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '效率',
      kpiExpression:
        'W0 周度 AATP 转化率 = 1-∑abs（W-1 周实际收单数量（sku 预测内）-W-2 周生成 W0 周 AATP 承诺数量（sku））/∑W-2 周生成 W0 周 AATP 承诺数量（sku）',
      kpiUnit: '%',
      dataSource: 'AATP 管理系统',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '正',
      parentKpiCode: 'S&OP 计划准确率',
      effectKpi: '5',
      person: '李凤英​'
    },
    {
      id: '10',
      kpiCode: 'GYL-010',
      kpiName: '市场需求满足率',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '质量',
      kpiExpression:
        '市场需求满足率 =∑MIN（各型号周度的承诺满足数量 ATP，周度需求数量）/∑各型号周度的需求数量，月度值求月内各周的平均值，N 月需求满足率取 N-1 月第 2 周至 N 月第一周对应版的月度值求平均（重点考核供应能力）',
      kpiUnit: '%',
      dataSource: '需求满足监控系统',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '正',
      parentKpiCode: '无',
      effectKpi: '2',
      person: '李国强​'
    },
    {
      id: '11',
      kpiCode: 'GYL-011',
      kpiName: '产能利用率',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '效率',
      kpiExpression: '产能利用率 = 实际生产小时数 × 线体效率 /Σ（线体班次 × 班次工时），线体班次为规划班次',
      kpiUnit: '%',
      dataSource: 'MES 系统',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '正',
      parentKpiCode: '市场需求满足率',
      effectKpi: '4',
      person: '李建国​'
    },
    {
      id: '12',
      kpiCode: 'GYL-012',
      kpiName: '按承诺订单达成率',
      kpiClassCode: '组织',
      kpiType: '业务',
      kpiNature: '质量',
      kpiExpression: '按承诺订单达成率 =∑按承诺实际交付的订单行数 /∑首次承诺的订单行数',
      kpiUnit: '%',
      dataSource: '订单跟踪系统',
      kpiCycle: '月度,季度,年度',
      kpiPolarity: '正',
      parentKpiCode: '无',
      effectKpi: '2',
      person: '李建军​'
    }
  ],
  // 人员关联
  staffData: [
    {
      id: '1',
      code: 'GYL-001',
      name: '销售预测准确率（sellin口径）',
      type: '人员',
      class: '业务',
      nature: '数量',
      nlms: '销售预测准确率 = 0.6*（N-1 月销售预测准确率）+0.4*（N-2 月销售预测准确率），N-1 月销售预测准确率 = 1-∑abs (各型号 N-1 月预测 - 实际）/∑各型号 N-1 月预测值，N-2 月销售预测准确率 = 1-∑abs (各型号 N-2 月预测值 - 实际）/∑各型号 N-2 月预测值，sellin 销量，取 SAP 开单口径。（适用于国内营销总部、分公司、海外公司）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '存货周转天数',
      affect: '5',
      charge: '王伟'
    },
    {
      id: '2',
      code: 'GYL-002',
      name: '销售预测准确率（sellout口径）',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '销售预测准确率 = 0.6*（N-1 月销售预测准确率）+0.4*（N-2 月销售预测准确率），N-1 月销售预测准确率 = 1-∑abs (各型号 N-1 月预测 - 实际）/∑各型号 N-1 月预测值，N-2 月销售预测准确率 = 1-∑abs (各型号 N-2 月预测值 - 实际）/∑各型号 N-2 月预测值，sellout 销量。（适用于国内营销总部、分公司、海外公司）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '存货周转天数',
      affect: '5',
      charge: '王伟'
    },
    {
      id: '3',
      code: 'GYL-003',
      name: '存货周转天数',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '当月存货周转天数 = 30/（当月主营业务成本 /（期初存货占用 + 期末存货占用）/2））累计存货周转天数 = 30/（累计主营业务成本 /(期初存货占用 / 2 + 期间各月占用 + 期末存货占用 / 2））',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '3',
      charge: '王伟'
    },
    {
      id: '4',
      code: 'GYL-004',
      name: '订单履约周期内销',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '订单履约周期 = 订单关闭时间 - 订单生成时间',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '5',
      charge: '王伟'
    },
    {
      id: '5',
      code: 'GYL-005',
      name: '订单履约周期出口',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '订单履约周期 = 订单关闭时间 - 订单生成时间',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '3',
      charge: '王伟'
    },
    {
      id: '6',
      code: 'GYL-006',
      name: '市场需求变动率（总量）',
      type: '人员',
      class: '业务',
      nature: '风险',
      nlms: '市场需求变动率（总量）=（N 月实际需求量 - N 月原始需求量）/N 月原始需求量，到月度总量',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '市场需求满足率',
      affect: '3',
      charge: '王伟'
    },
    {
      id: '7',
      code: 'GYL-007',
      name: '市场需求变动率（结构）',
      type: '人员',
      class: '业务',
      nature: '风险',
      nlms: '市场需求变动率（结构）=∑abs（N 月各型号实际需求量 - N 月各型号原始需求量）/∑N 月各型号原始需求量，到型号维度',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '市场需求满足率',
      affect: '2',
      charge: '王伟'
    },
    {
      id: '8',
      code: 'GYL-008',
      name: '产成品周转天数',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '当月产成品周转天数 = 30/（当月主营业务成本 /（期初产成品占用 + 期末产成品占用）/2））累计产成品周转天数 = 30/（累计主营业务成本 /(期初产成品占用 / 2 + 期间各月占用 + 期末产成品占用 / 2））',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '存货周转天数',
      affect: '2',
      charge: '王伟'
    },
    {
      id: '9',
      code: 'GYL-009',
      name: '周AATP订单转化率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: 'W0 周度 AATP 转化率 = 1-∑abs（W-1 周实际收单数量（sku 预测内）-W-2 周生成 W0 周 AATP 承诺数量（sku））/∑W-2 周生成 W0 周 AATP 承诺数量（sku）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: 'S&OP 计划准确率',
      affect: '2',
      charge: '王伟'
    },
    {
      id: '10',
      code: 'GYL-010',
      name: '市场需求满足率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '市场需求满足率 =∑MIN（各型号周度的承诺满足数量 ATP，周度需求数量）/∑各型号周度的需求数量，月度值求月内各周的平均值，N 月需求满足率取 N-1 月第 2 周至 N 月第一周对应版的月度值求平均（重点考核供应能力）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: '王伟'
    },
    {
      id: '11',
      code: 'GYL-011',
      name: '产能利用率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '产能利用率 = 实际生产小时数 × 线体效率 /Σ（线体班次 × 班次工时），线体班次为规划班次',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '市场需求满足率',
      affect: '5',
      charge: ' 陈建军​'
    },
    {
      id: '12',
      code: 'GYL-012',
      name: '按承诺订单达成率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '按承诺订单达成率 =∑按承诺实际交付的订单行数 /∑首次承诺的订单行数',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 陈建军​'
    },
    {
      id: '13',
      code: 'GYL-013',
      name: '订单履约周期',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '订单履约周期 = 订单关闭时间 - 订单生成时间',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '2',
      charge: ' 陈丽娟​'
    },
    {
      id: '14',
      code: 'GYL-014',
      name: '供应承诺达成率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '供应承诺达成率 =Σ 本月（周）实际供应量 / 上版供应承诺量 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: 'S&OP 计划准确率',
      affect: '2',
      charge: ' 陈丽娟​'
    },
    {
      id: '15',
      code: 'GYL-015',
      name: 'T+3计划刚性执行率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: 'T+3 计划刚性执行率 =∑MIN（各型号的实际完成量，计划量）/∑T 天确定的第 T+3 日各型号的计划量，（周度、月度值取日累值）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '按承诺订单达成率',
      affect: '3',
      charge: ' 陈丽娟​'
    },
    {
      id: '16',
      code: 'GYL-016',
      name: '按时到货满足率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '/',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '市场需求满足率',
      affect: '5',
      charge: ' 陈丽君​'
    },
    {
      id: '17',
      code: 'GYL-017',
      name: '物料需求满足率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '物料需求满足率 =（物料承诺总量 + 物料库存）/ 物料毛需求总量 * 100%（支撑按单颗物料 / 品类维度分析，品类维度按单颗物料汇总并取平均值）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '市场需求满足率',
      affect: '5',
      charge: ' 陈丽君​'
    },
    {
      id: '18',
      code: 'GYL-018',
      name: '原材料周转天数',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '当月原材料周转天数 = 30/（当月耗用 /（期初原材料占用 + 期末原材料占用）/2））\n累计原材料周转天数 = 30/（累计耗用 /(期初原材料占用 / 2 + 期间各月占用 + 期末原材料占用 / 2））',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '存货周转天数',
      affect: '4',
      charge: ' 陈丽君​'
    },
    {
      id: '19',
      code: 'GYL-019',
      name: '原材料库存周转天数',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '当月原材料周转天数 = 30/（当月耗用 /（期初原材料占用 + 期末原材料占用）/2））\n累计原材料周转天数 = 30/（累计耗用 /(期初原材料占用 / 2 + 期间各月占用 + 期末原材料占用 / 2））',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '存货周转天数',
      affect: '3',
      charge: ' 陈秀英​'
    },
    {
      id: '20',
      code: 'GYL-020',
      name: '订单处理周期',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '订单接收周期 = 订单承诺时间 - 订单生成时间',
      unit: '小时',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '订单履约周期',
      affect: '3',
      charge: ' 陈秀英​'
    },
    {
      id: '21',
      code: 'GYL-021',
      name: '物流交付周期',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '物流交付周期 = 订单关闭时间 - 工单最后一台完工时间',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '订单履约周期',
      affect: '4',
      charge: ' 陈秀英​'
    },
    {
      id: '22',
      code: 'GYL-022',
      name: '品类采购策略制定完成及时率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '品类采购策略制定完成及时率 = 实际按时完成策略数 / 计划制定策略总数 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 陈秀英​'
    },
    {
      id: '23',
      code: 'GYL-023',
      name: '品类策略目标达成情况',
      type: '人员',
      class: '业务',
      nature: '效果',
      nlms: '品类策略目标达成情况 =∑（各子目标实际达成值 / 子目标计划值 × 权重）×100%（子目标包括成本降低率、准时交付率、供应商开发数量等）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 陈秀英​'
    },
    {
      id: '24',
      code: 'GYL-024',
      name: '供方引入淘汰完成率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '供方引入淘汰完成率 =（实际引入合格供应商数 + 实际淘汰不合格供应商数）/（计划引入供应商数 + 计划淘汰供应商数）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 陈秀英​'
    },
    {
      id: '25',
      code: 'GYL-025',
      name: '品类采购策略制定完成及时率（非生产）',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '品类采购策略制定完成及时率（非生产）= 实际按时完成非生产品类策略数 / 计划制定非生产品类策略总数 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '3',
      charge: ' 陈秀英​'
    },
    {
      id: '26',
      code: 'GYL-026',
      name: '寻源开发完成率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '寻源开发完成率 = 实际完成寻源并通过认证的供应商数 / 计划寻源开发供应商数 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供方引入淘汰完成率',
      affect: '4',
      charge: ' 陈秀英​'
    },
    {
      id: '27',
      code: 'GYL-027',
      name: '寻源开发完成率（非生产）',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '寻源开发完成率（非生产）= 实际完成寻源并通过认证的非生产供应商数 / 计划寻源开发非生产供应商数 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供方引入淘汰完成率（非生产）',
      affect: '2',
      charge: ' 陈秀英​'
    },
    {
      id: '28',
      code: 'GYL-028',
      name: '供方认定不良率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '供方认定不良率 = 认定不通过或认定后 1 年内出现重大质量 / 交付问题的供应商数 / 认定供应商总数 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '4',
      charge: ' 陈志强'
    },
    {
      id: '29',
      code: 'GYL-029',
      name: '供方认定及时率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '供方认定及时率 = 按时完成认定的供应商数 / 需认定供应商总数 ×100%（认定周期 = 从申请到最终批准的工作日）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '3',
      charge: ' 陈志强'
    },
    {
      id: '30',
      code: 'GYL-030',
      name: '采购早期介入完成及时率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '采购早期介入完成及时率 = 按时完成早期介入的研发项目数 / 总研发项目数 ×100%（早期介入节点：项目启动后 30 天内完成供应商资源评估与成本测算）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '2',
      charge: ' 陈志强'
    },
    {
      id: '31',
      code: 'GYL-031',
      name: '物料配额符合率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '物料配额符合率 =∑（各供应商实际采购量 / 该供应商配额量 × 权重）/ 物料总采购量 ×100%（权重根据品类战略重要性设定）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 陈志强​'
    },
    {
      id: '32',
      code: 'GYL-032',
      name: '目录采购降本目标达成率（非生产）',
      type: '人员',
      class: '业务',
      nature: '效果',
      nlms: '目录采购降本目标达成率 =（目录采购实际降本金额 / 目录采购目标降本金额）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 陈志强​'
    },
    {
      id: '33',
      code: 'GYL-033',
      name: '新品目标成本达成率',
      type: '人员',
      class: '业务',
      nature: '效果',
      nlms: '新品目标成本达成率 =（新品实际采购成本 / 新品目标成本）×100%（目标成本 = 研发阶段设定的采购成本上限）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '3',
      charge: ' 陈志强​'
    },
    {
      id: '34',
      code: 'GYL-034',
      name: '量产品采购降成本达成率',
      type: '人员',
      class: '业务',
      nature: '效果',
      nlms: '量产品采购降成本达成率 =（基准采购成本 - 实际采购成本）/ 基准采购成本 ×100%（基准成本 = 上年度同期采购成本或行业平均成本）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '2',
      charge: ' 陈志强​'
    },
    {
      id: '35',
      code: 'GYL-035',
      name: '询比价流程超期率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '询比价流程超期率 = 超期完成的询比价项目数 / 总询比价项目数 ×100%（超期定义：超过计划周期 5 个工作日）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '5',
      charge: ' 陈志强​'
    },
    {
      id: '36',
      code: 'GYL-036',
      name: '询比价按时完成率（非生）',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '询比价按时完成率（非生）= 按时完成的非生产性询比价项目数 / 非生产性询比价项目总数 ×100%（按时定义：按需求部门要求时间完成）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 陈志强​'
    },
    {
      id: '37',
      code: 'GYL-037',
      name: '招标计划及时率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '招标计划及时率 = 按时启动的招标项目数 / 计划招标项目总数 ×100%（按时定义：按招标文件发布时间计划执行）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李凤霞​'
    },
    {
      id: '38',
      code: 'GYL-038',
      name: '招标流程超期率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '招标流程超期率 = 超期完成的招标项目数 / 总招标项目数 ×100%（超期定义：超过计划周期 10 个工作日）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '3',
      charge: ' 李凤霞​'
    },
    {
      id: '39',
      code: 'GYL-039',
      name: '招标流程不良率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '招标流程不良率 =（流标项目数 + 废标项目数 + 供应商有效投诉项目数）/ 总招标项目数 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李凤霞​'
    },
    {
      id: '40',
      code: 'GYL-040',
      name: '招标配额符合率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '招标配额符合率 =∑（各中标供应商实际分配量 / 招标计划分配量 × 权重）/ 总招标量 ×100%（权重根据供应商战略重要性设定）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李凤英​'
    },
    {
      id: '41',
      code: 'GYL-041',
      name: '招标按时完成率（非生产）',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '招标按时完成率（非生产）= 按时完成的非生产单一来源采购项目数 / 计划项目数 ×100%（按时定义：按需求部门要求时间完成合同签署）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '3',
      charge: ' 李凤英​'
    },
    {
      id: '42',
      code: 'GYL-042',
      name: '废标率（非生产）',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '废标率（非生产）= 废标项目数 / 竞争性谈判项目总数 ×100%（废标定义：有效响应供应商 < 3 家或关键条款无共识）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李凤英​'
    },
    {
      id: '43',
      code: 'GYL-043',
      name: '竞争性谈判流程超期率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '竞争性谈判流程超期率 = 超期完成的谈判项目数 / 总谈判项目数 ×100%（超期定义：超过计划周期 15 个工作日）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '3',
      charge: ' 李国强​'
    },
    {
      id: '44',
      code: 'GYL-044',
      name: '竞争性谈判周期（非生产）',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '竞争性谈判周期（非生产）=∑（各项目谈判结束时间 - 启动时间）/ 谈判项目总数（工作日）',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李国强​'
    },
    {
      id: '45',
      code: 'GYL-045',
      name: '单一来源流程超期率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '单一来源流程超期率 = 超期完成的单一来源采购项目数 / 总项目数 ×100%（超期定义：超过计划周期 20 个工作日）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李国强​'
    },
    {
      id: '46',
      code: 'GYL-046',
      name: '核价模型覆盖率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '核价模型覆盖率 = 使用标准化核价模型的采购项目数 / 总采购项目数 ×100%（标准化核价模型：包含物料成本、加工费、运费等要素的结构化核价工具）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李建国​'
    },
    {
      id: '47',
      code: 'GYL-047',
      name: '核价模型完成及时率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '核价模型完成及时率 = 按时完成的核价模型开发 / 更新项目数 / 计划项目数 ×100%（按时定义：按研发新品上市计划完成模型建立）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '2',
      charge: ' 李建国​'
    },
    {
      id: '48',
      code: 'GYL-048',
      name: '特采次数',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '特采次数 = 统计周期内发起的特殊采购申请次数（特采定义：未纳入常规采购计划的紧急采购）',
      unit: '次',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李建国​'
    },
    {
      id: '49',
      code: 'GYL-049',
      name: '合同签署完成率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '合同签署完成率 = 按时签署的供应商合同数量 / 应签署的供应商合同总数 ×100%（按时定义：在采购订单生效前 3 个工作日内完成签署）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '2',
      charge: ' 李建军​'
    },
    {
      id: '50',
      code: 'GYL-050',
      name: '合同签署不规范率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '合同签署不规范率 = 存在不规范问题的合同数量 / 签署的合同总数 ×100%（不规范问题包括条款冲突、数据错误、权责缺失等）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '3',
      charge: ' 李建军​'
    },
    {
      id: '51',
      code: 'GYL-051',
      name: '大宗原材及关键部件锁储收益达成率',
      type: '人员',
      class: '业务',
      nature: '效果',
      nlms: '大宗原材及关键部件锁储收益达成率 =（锁储实际收益 / 锁储目标收益）×100%（实际收益 =（锁储期内市场均价 - 锁储价）× 锁储量；目标收益 = 预算锁储收益）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李建军​'
    },
    {
      id: '52',
      code: 'GYL-052',
      name: '战略储备未按期消化金额',
      type: '人员',
      class: '业务',
      nature: '风险',
      nlms: '战略储备未按期消化金额 =∑（未按期消化的储备物资数量 × 采购单价）（按期定义：在储备计划周期内消耗或周转）',
      unit: '元',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李建军​'
    },
    {
      id: '53',
      code: 'GYL-053',
      name: '估价偏差率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '估价偏差率 =∣（实际采购价格 - 估算价格）/ 估算价格∣×100%（估算价格 = 采购前通过市场调研、历史数据等形成的价格预期）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李建军​'
    },
    {
      id: '54',
      code: 'GYL-054',
      name: '价格超期率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '价格超期率 = 超期未更新价格的物料种类数 / 应更新价格的物料种类总数 ×100%（超期定义：超过价格更新周期 5 个工作日）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '2',
      charge: ' 李建军​'
    },
    {
      id: '55',
      code: 'GYL-055',
      name: '价格长期未变动',
      type: '人员',
      class: '业务',
      nature: '风险',
      nlms: '价格长期未变动 =（连续 6 个月未调价的物料种类数 / 总物料种类数）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '3',
      charge: ' 李丽华​'
    },
    {
      id: '56',
      code: 'GYL-056',
      name: '价格差异',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '价格差异 =（最高采购价格 - 最低采购价格）/ 行业平均采购价格 ×100%（选取同一时期至少 3 家合格供应商价格）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '2',
      charge: ' 李丽华​'
    },
    {
      id: '57',
      code: 'GYL-057',
      name: '部品认定不良率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '部品认定不良率 = 认定不通过或认定后 1 年内出现质量问题的部品数量 / 认定部品总数 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李丽华​'
    },
    {
      id: '58',
      code: 'GYL-058',
      name: '部品认定超期率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '部品认定超期率 = 超期完成认定的部品数量 / 认定部品总数 ×100%（超期定义：超过认定周期 10 个工作日，标准部品认定周期≤30 天，定制部品≤60 天）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '2',
      charge: ' 李丽娟​'
    },
    {
      id: '59',
      code: 'GYL-059',
      name: '因部品认定未识别风险导致的质量事件数',
      type: '人员',
      class: '业务',
      nature: '风险',
      nlms: '因部品认定未识别风险导致的质量事件数 = 统计周期内部品认定通过但后续引发质量问题的事件次数（包括生产停线、客户投诉等）',
      unit: '次',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '2',
      charge: ' 李丽娟​'
    },
    {
      id: '60',
      code: 'GYL-060',
      name: 'VMI/JIT供货模式执行达成率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: 'VMI/JIT 供货模式执行达成率 = 实际按 VMI/JIT 模式执行的订单数量 / 计划推行 VMI/JIT 模式的订单总数 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李丽娟​'
    },
    {
      id: '61',
      code: 'GYL-061',
      name: 'VMI/JIT供货模式占比',
      type: '人员',
      class: '业务',
      nature: '效果',
      nlms: 'VMI/JIT 供货模式占比 =（VMI 订单数量 + JIT 订单数量）/ 采购订单总数量 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '2',
      charge: ' 李丽萍​'
    },
    {
      id: '62',
      code: 'GYL-062',
      name: '采购预测承诺满足率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '采购预测承诺满足率 = 实际采购满足需求数量 / 预测需求数量 ×100%（满足需求数量 = 在承诺期内实际到货且符合质量要求的数量）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李丽萍​'
    },
    {
      id: '63',
      code: 'GYL-063',
      name: '采购预测承诺反馈及时率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '采购预测承诺反馈及时率 = 按时反馈的预测需求数量 / 总预测需求数量 ×100%（按时定义：在收到预测需求后 2 个工作日内反馈承诺数量）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李丽萍​'
    },
    {
      id: '64',
      code: 'GYL-064',
      name: '未自动转PO的PR关闭及时率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '未自动转 PO 的 PR 关闭及时率 = 按时关闭的未自动转 PO 的 PR 数量 / 未自动转 PO 的 PR 总数 ×100%（按时定义：在 PR 有效期内 3 个工作日内关闭）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李丽萍​'
    },
    {
      id: '65',
      code: 'GYL-065',
      name: '采购订单承诺反馈及时率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '采购订单承诺反馈及时率 = 按时反馈订单承诺的数量 / 总订单数量 ×100%（按时定义：在订单承诺后 24 小时内反馈给需求部门）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李丽萍​'
    },
    {
      id: '66',
      code: 'GYL-066',
      name: '采购配额执行符合率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '采购配额执行符合率 =∑（各供应商实际采购量 / 该供应商配额量 × 权重）/ 总采购量 ×100%（权重根据供应商战略重要性设定）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李丽萍​'
    },
    {
      id: '67',
      code: 'GYL-067',
      name: '供应商准时交付率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '供应商准时交付率 = 按时交付的订单行数量 / 总订单行数量 ×100%（按时定义：在订单约定交付日期 ±3 天内交付）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李淑华​'
    },
    {
      id: '68',
      code: 'GYL-068',
      name: '供应商按配额下单准确率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '供应商按配额下单准确率 = 外协供应商按配额要求下单的数量 / 计划配额数量 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李淑华​'
    },
    {
      id: '69',
      code: 'GYL-069',
      name: '供应商提报需求数量准确率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '供应商提报需求数量准确率 = 1-∣（提报需求数量 - 实际需求数量）/ 实际需求数量∣×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李淑华​'
    },
    {
      id: '70',
      code: 'GYL-070',
      name: '来料不合格造成的工时损失',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '来料不合格造成的工时损失 =∑（各批次不合格来料导致的停线时间 + 返工时间）（按生产线实际记录统计）',
      unit: '小时',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李素芳​'
    },
    {
      id: '71',
      code: 'GYL-071',
      name: '暂估指标（供应商发票入账及时率）',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '供应商发票入账及时率 = 按时入账的发票数量 / 应入账发票总数 ×100%（按时定义：在采购入库后 30 天内完成入账）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 李素芳​'
    },
    {
      id: '72',
      code: 'GYL-072',
      name: '供应商索赔单关闭及时率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '供应商索赔单关闭及时率 = 按时关闭的索赔单数量 / 总索赔单数量 ×100%（按时定义：在索赔协议约定时间内完成处理）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 李素芳​'
    },
    {
      id: '73',
      code: 'GYL-073',
      name: '战略MOU签署完成率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '战略 MOU 签署完成率 = 实际签署的战略 MOU 数量 / 计划签署的战略 MOU 数量 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 刘凤兰​'
    },
    {
      id: '74',
      code: 'GYL-074',
      name: '战略MOU达成率',
      type: '人员',
      class: '业务',
      nature: '效果',
      nlms: '战略 MOU 达成率 =∑（各 MOU 实际达成目标值 / 计划目标值 × 权重）×100%（目标包括成本降低、技术创新、交付提升等）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '4',
      charge: ' 刘凤兰​'
    },
    {
      id: '75',
      code: 'GYL-075',
      name: '供应商分类完成及时性',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '供应商分类完成及时性 = 按时完成分类评估的供应商数量 / 应评估的供应商总数 ×100%（按时定义：在年度 / 半年度评估周期结束后 15 个工作日内完成）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '3',
      charge: ' 刘凤兰​'
    },
    {
      id: '76',
      code: 'GYL-076',
      name: '供应商沟通计划按时完成率',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '供应商沟通计划按时完成率 = 按时完成的沟通计划项数 / 计划沟通总项数 ×100%（按时定义：按周 / 月计划时间完成沟通）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '3',
      charge: ' 刘凤霞​'
    },
    {
      id: '77',
      code: 'GYL-077',
      name: '供应商沟通内容闭环及时率',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '供应商沟通内容闭环及时率 = 按时闭环的问题数量 / 总沟通问题数量 ×100%（按时定义：在问题约定解决周期内完成闭环）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '2',
      charge: ' 刘凤霞​'
    },
    {
      id: '78',
      code: 'GYL-078',
      name: '供应商改进完成及时率',
      type: '人员',
      class: '业务',
      nature: '效果',
      nlms: '供应商改进完成及时率 = 按时完成改进计划的供应商数量 / 需改进的供应商总数 ×100%（按时定义：在改进计划约定时间内完成所有整改项）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '5',
      charge: ' 刘凤霞​'
    },
    {
      id: '79',
      code: 'GYL-079',
      name: '供应商改进完成及时率（非生产）',
      type: '人员',
      class: '业务',
      nature: '效果',
      nlms: '供应商改进完成及时率（非生产）= 按时完成改进计划的非生产供应商数量 / 需改进的非生产供应商总数 ×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '无',
      affect: '2',
      charge: ' 刘桂兰​'
    },
    {
      id: '80',
      code: 'GYL-080',
      name: '供应商变更流程完成及时率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '供应商变更流程完成及时率 =（规定时间内完成的供应商变更流程数量 / 应完成供应商变更流程总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商管理效率指标',
      affect: '5',
      charge: ' 刘桂兰​'
    },
    {
      id: '81',
      code: 'GYL-081',
      name: '培育方案完成率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '培育方案完成率 =（按时完成的供应商培育方案数量 / 应完成供应商培育方案总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商培育管理指标',
      affect: '2',
      charge: ' 刘桂兰​'
    },
    {
      id: '82',
      code: 'GYL-082',
      name: '培育方案时间延期数',
      type: '人员',
      class: '业务',
      nature: '数量',
      nlms: '培育方案时间延期数 =∑（每个未按时完成的供应商培育方案实际完成时间 - 计划完成时间）',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '供应商培育管理指标',
      affect: '4',
      charge: ' 刘桂珍​'
    },
    {
      id: '83',
      code: 'GYL-083',
      name: '供应商整改及时率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '供应商整改及时率 =（规定时间内完成整改的供应商数量 / 应整改供应商总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商审核管理指标',
      affect: '2',
      charge: ' 刘桂珍​'
    },
    {
      id: '84',
      code: 'GYL-084',
      name: '供应商审核完成率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '供应商审核完成率 =（按时完成的供应商审核数量 / 应进行供应商审核总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商审核管理指标',
      affect: '3',
      charge: ' 刘桂珍​'
    },
    {
      id: '85',
      code: 'GYL-085',
      name: '质量异常事件关闭率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '质量异常事件关闭率 =（已关闭的供应商质量异常事件数量 / 应关闭供应商质量异常事件总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商质量异常管理指标',
      affect: '3',
      charge: ' 刘桂珍​'
    },
    {
      id: '86',
      code: 'GYL-086',
      name: '质量异常事件的关闭及时率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '质量异常事件的关闭及时率 =（规定时间内关闭的供应商质量异常事件数量 / 应关闭供应商质量异常事件总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商质量异常管理指标',
      affect: '4',
      charge: ' 刘桂珍​'
    },
    {
      id: '87',
      code: 'GYL-087',
      name: '供应商冻结及退出完成及时率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '供应商冻结及退出完成及时率 =（规定时间内完成的供应商冻结及退出流程数量 / 应完成供应商冻结及退出流程总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商冻结及退出管理指标',
      affect: '4',
      charge: ' 刘桂珍​'
    },
    {
      id: '88',
      code: 'GYL-088',
      name: '供应商状态更新完成率（非生产）',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '供应商状态更新完成率（非生产）=（按时完成状态更新的非生产供应商数量 / 应进行状态更新的非生产供应商总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '非生产供应商管理指标',
      affect: '2',
      charge: ' 刘建军​'
    },
    {
      id: '89',
      code: 'GYL-089',
      name: '供应商绩效评价按时完成率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '供应商绩效评价按时完成率 =（按时完成的供应商绩效评价数量 / 应进行供应商绩效评价总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商绩效管理指标',
      affect: '3',
      charge: ' 刘建军​'
    },
    {
      id: '90',
      code: 'GYL-090',
      name: '供应商绩效按时评估完成率（非生产）',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '供应商绩效按时评估完成率（非生产）=（按时完成的非生产供应商绩效评估数量 / 应进行非生产供应商绩效评估总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '非生产供应商绩效管理指标',
      affect: '3',
      charge: ' 刘建军​'
    },
    {
      id: '91',
      code: 'GYL-091',
      name: '整改措施按期完成率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '整改措施按期完成率 =（规定时间内完成的采购运营指标整改措施数量 / 应完成采购运营指标整改措施总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '采购运营指标分析管理指标',
      affect: '4',
      charge: ' 刘俊芳​'
    },
    {
      id: '92',
      code: 'GYL-092',
      name: '月报按期发布率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '月报按期发布率 =（按时发布的采购运营指标分析月报数量 / 应发布采购运营指标分析月报总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '采购运营指标分析管理指标',
      affect: '4',
      charge: ' 刘俊芳​'
    },
    {
      id: '93',
      code: 'GYL-093',
      name: '采购内控检查完成率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '采购内控检查完成率 =（按时完成的采购内控检查项目数量 / 应进行采购内控检查项目总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '采购内控管理指标',
      affect: '3',
      charge: ' 刘俊芳​'
    },
    {
      id: '94',
      code: 'GYL-094',
      name: '采购流程内控遵从率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '采购流程内控遵从率 =（符合采购内控要求的业务流程数量 / 同期采购业务流程总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '采购内控管理指标',
      affect: '3',
      charge: ' 刘淑芳​'
    },
    {
      id: '95',
      code: 'GYL-095',
      name: '采购内控整改完成率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '采购内控整改完成率 =（已完成整改的采购内控问题数量 / 应整改采购内控问题总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '采购内控管理指标',
      affect: '4',
      charge: ' 刘淑芳​'
    },
    {
      id: '96',
      code: 'GYL-096',
      name: 'KCP控制要素更新及时率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: 'KCP 控制要素更新及时率 =（规定时间内完成更新的 KCP 数量 / 应更新 KCP 总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '采购内控管理指标',
      affect: '5',
      charge: ' 刘淑芳​'
    },
    {
      id: '97',
      code: 'GYL-097',
      name: '风险识别管控有效率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '风险识别管控有效率 =（有效识别并采取管控措施的供应商风险事件数量 / 同期识别的供应商风险事件总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商风险管理指标',
      affect: '4',
      charge: ' 刘淑华​'
    },
    {
      id: '98',
      code: 'GYL-098',
      name: '采购风险及时关闭率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '采购风险及时关闭率 =（规定时间内关闭的供应商风险事件数量 / 应关闭供应商风险事件总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商风险管理指标',
      affect: '2',
      charge: ' 刘淑华​'
    },
    {
      id: '99',
      code: 'GYL-099',
      name: '风险预案目标达成率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '风险预案目标达成率 =（实际达成风险预案设定目标的供应商风险事件数量 / 启动风险预案的供应商风险事件总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '供应商风险管理指标',
      affect: '2',
      charge: ' 刘淑华​'
    },
    {
      id: '100',
      code: 'GYL-100',
      name: '商情报告按时发布率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '商情报告按时发布率 =（按时发布的商情分析报告数量 / 应发布商情分析报告总数量）×100%',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '商情分析管理指标',
      affect: '4',
      charge: ' 刘淑珍​'
    },
    {
      id: '101',
      code: 'GYL-101',
      name: '商情报告评价目标得分完成率',
      type: '人员',
      class: '业务',
      nature: '比率',
      nlms: '商情报告评价目标得分完成率 =（实际得分≥目标得分的商情分析报告数量 / 同期发布商情分析报告总数量）×100%，（注：目标得分由采购决策委员会根据报告类型设定，如常规报告目标 80 分，专项报告目标 90 分）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '商情分析管理指标',
      affect: '4',
      charge: ' 刘淑珍​'
    }
  ],
  // 项目关联
  projectData: [
    {
      id: '1',
      code: 'GYL-001',
      name: '销售预测准确率（sellin口径）',
      type: '人员',
      class: '业务',
      nature: '数量',
      nlms: '销售预测准确率 = 0.6*（N-1 月销售预测准确率）+0.4*（N-2 月销售预测准确率），N-1 月销售预测准确率 = 1-∑abs (各型号 N-1 月预测 - 实际）/∑各型号 N-1 月预测值，N-2 月销售预测准确率 = 1-∑abs (各型号 N-2 月预测值 - 实际）/∑各型号 N-2 月预测值，sellin 销量，取 SAP 开单口径。（适用于国内营销总部、分公司、海外公司）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '存货周转天数',
      affect: '5',
      charge: '王伟'
    },
    {
      id: '2',
      code: 'GYL-002',
      name: '销售预测准确率（sellout口径）',
      type: '人员',
      class: '业务',
      nature: '质量',
      nlms: '销售预测准确率 = 0.6*（N-1 月销售预测准确率）+0.4*（N-2 月销售预测准确率），N-1 月销售预测准确率 = 1-∑abs (各型号 N-1 月预测 - 实际）/∑各型号 N-1 月预测值，N-2 月销售预测准确率 = 1-∑abs (各型号 N-2 月预测值 - 实际）/∑各型号 N-2 月预测值，sellout 销量。（适用于国内营销总部、分公司、海外公司）',
      unit: '%',
      cycle: '月度,季度,年度',
      polarity: '正',
      parentIndicator: '存货周转天数',
      affect: '5',
      charge: '王伟'
    },
    {
      id: '3',
      code: 'GYL-003',
      name: '存货周转天数',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '当月存货周转天数 = 30/（当月主营业务成本 /（期初存货占用 + 期末存货占用）/2））累计存货周转天数 = 30/（累计主营业务成本 /(期初存货占用 / 2 + 期间各月占用 + 期末存货占用 / 2））',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '3',
      charge: '王伟'
    },
    {
      id: '4',
      code: 'GYL-004',
      name: '订单履约周期内销',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '订单履约周期 = 订单关闭时间 - 订单生成时间',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '5',
      charge: '王伟'
    },
    {
      id: '5',
      code: 'GYL-005',
      name: '订单履约周期出口',
      type: '人员',
      class: '业务',
      nature: '效率',
      nlms: '订单履约周期 = 订单关闭时间 - 订单生成时间',
      unit: '天',
      cycle: '月度,季度,年度',
      polarity: '负',
      parentIndicator: '无',
      affect: '3',
      charge: '王伟'
    }
  ]
}
// 目标值与实际值维护
export const targetTending = {
  // 组织指示
  // 目标值设定
  targetData: [
    {
      id: '1',
      orgName: '供应链计划管理部',
      class: '年度目标',
      period: '2025 年',
      kpiName: '销售预测准确率（sellin 口径）',
      kpiUnit: '%',
      kpiTarget: '≥85%',
      charge: '王伟'
    },
    {
      id: '2',
      orgName: '供应链计划管理部',
      class: '年度目标',
      period: '2025 年',
      kpiName: '销售预测准确率（sellout 口径）',
      kpiUnit: '%',
      kpiTarget: '≥80%',
      charge: '陈建军​'
    },
    {
      id: '3',
      orgName: '供应链计划管理部',
      class: '年度目标',
      period: '2025 年',
      kpiName: '存货周转天数',
      kpiUnit: '天',
      kpiTarget: '≤45 天',
      charge: '陈丽娟​'
    },
    {
      id: '4',
      orgName: '供应链计划管理部',
      class: '年度目标',
      period: '2025 年',
      kpiName: '订单履约周期内销',
      kpiUnit: '天',
      kpiTarget: '≤15 天',
      charge: '陈丽君​'
    },
    {
      id: '5',
      orgName: '供应链计划管理部',
      class: '年度目标',
      period: '2025 年',
      kpiName: '订单履约周期出口',
      kpiUnit: '天',
      kpiTarget: '≤30 天',
      charge: '陈秀英​'
    },
    {
      id: '6',
      orgName: '供应链计划管理部',
      class: '年度目标',
      period: '2025 年',
      kpiName: '市场需求变动率（总量）',
      kpiUnit: '%',
      kpiTarget: '≤15%',
      charge: '陈志强'
    },
    {
      id: '7',
      orgName: '供应链计划管理部',
      class: '年度目标',
      period: '2025 年',
      kpiName: '市场需求变动率（结构）',
      kpiUnit: '%',
      kpiTarget: '≤20%',
      charge: '陈志强​'
    },
    {
      id: '8',
      orgName: '供应链计划管理部',
      class: '年度目标',
      period: '2025 年',
      kpiName: '产成品周转天数',
      kpiUnit: '天',
      kpiTarget: '≤35 天',
      charge: '李凤霞​'
    },
    {
      id: '9',
      orgName: '供应链计划管理部',
      class: '年度目标',
      period: '2025 年',
      kpiName: '周 AATP 订单转化率',
      kpiUnit: '%',
      kpiTarget: '≥75%',
      charge: '李凤英​'
    },
    {
      id: '10',
      orgName: '供应链计划管理部',
      class: '年度目标',
      period: '2025 年',
      kpiName: '市场需求满足率',
      kpiUnit: '%',
      kpiTarget: '≥95%',
      charge: '李国强​'
    }
  ],
  // 智能检查-目标设定统计
  targetStatistics: [
    {
      id: '1',
      orgName: '供应链计划管理部',
      orgCharge: '王伟',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '15',
      scalarType: '3',
      economicClass: '3',
      qualificationType: '1',
      concurrency: '3',
      restrain: '3',
      costPool: '2'
    },
    {
      id: '2',
      orgName: '供应链计划管理部',
      orgCharge: '李丽华​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '13',
      scalarType: '2',
      economicClass: '2',
      qualificationType: '3',
      concurrency: '1',
      restrain: '2',
      costPool: '3'
    },
    {
      id: '3',
      orgName: '供应链计划管理部',
      orgCharge: '李丽娟​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '15',
      scalarType: '3',
      economicClass: '3',
      qualificationType: '3',
      concurrency: '2',
      restrain: '1',
      costPool: '3'
    },
    {
      id: '4',
      orgName: '供应链计划管理部',
      orgCharge: '李丽萍​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '13',
      scalarType: '2',
      economicClass: '1',
      qualificationType: '2',
      concurrency: '3',
      restrain: '3',
      costPool: '2'
    },
    {
      id: '5',
      orgName: '供应链计划管理部',
      orgCharge: '李淑华​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '12',
      scalarType: '2',
      economicClass: '2',
      qualificationType: '1',
      concurrency: '1',
      restrain: '3',
      costPool: '3'
    },
    {
      id: '6',
      orgName: '供应链计划管理部',
      orgCharge: '李素芳​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '12',
      scalarType: '2',
      economicClass: '2',
      qualificationType: '2',
      concurrency: '3',
      restrain: '2',
      costPool: '1'
    },
    {
      id: '7',
      orgName: '供应链计划管理部',
      orgCharge: '刘凤兰​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '13',
      scalarType: '3',
      economicClass: '2',
      qualificationType: '2',
      concurrency: '2',
      restrain: '1',
      costPool: '3'
    },
    {
      id: '8',
      orgName: '供应链计划管理部',
      orgCharge: '刘凤霞​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '13',
      scalarType: '1',
      economicClass: '2',
      qualificationType: '3',
      concurrency: '2',
      restrain: '2',
      costPool: '3'
    },
    {
      id: '9',
      orgName: '供应链计划管理部',
      orgCharge: '刘桂兰​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '12',
      scalarType: '3',
      economicClass: '1',
      qualificationType: '2',
      concurrency: '2',
      restrain: '3',
      costPool: '1'
    },
    {
      id: '10',
      orgName: '供应链计划管理部',
      orgCharge: '刘桂珍​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '13',
      scalarType: '1',
      economicClass: '2',
      qualificationType: '3',
      concurrency: '3',
      restrain: '1',
      costPool: '3'
    }
  ],
  // 智能检查-智能检查
  smartCheck: [
    {
      id: '1',
      kpiName: '销售预测准确率（sellin 口径）',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≥85% 合理（参考行业平均），单位明确，责任人（市场预测经理）匹配职能'
    },
    {
      id: '2',
      kpiName: '销售预测准确率（sellout 口径）',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≥80% 略低于 sellin 口径（符合终端预测难度），责任人（销售计划总监）明确'
    },
    {
      id: '3',
      kpiName: '存货周转天数',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤45 天具挑战性（需结合物料周期优化），责任人（供应链总监）权责清晰'
    },
    {
      id: '4',
      kpiName: '订单履约周期内销',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤15 天对标高效物流可行，责任人（国内物流经理）匹配执行主体'
    },
    {
      id: '5',
      kpiName: '订单履约周期出口',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤30 天需考虑海运波动，责任人（国际物流经理）需协同关务资源'
    },
    {
      id: '6',
      kpiName: '市场需求变动率（总量）',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤15% 反映供应链稳定性要求，责任人（市场分析经理）需联动销售端数据'
    },
    {
      id: '7',
      kpiName: '市场需求变动率（结构）',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤20% 高于总量（合理体现结构调整难度），责任人（产品规划经理）需优化品类策略'
    },
    {
      id: '8',
      kpiName: '产成品周转天数',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤35 天需生产计划与销售协同，责任人（生产计划经理）需强化需求拉动机制'
    },
    {
      id: '9',
      kpiName: '周 AATP 订单转化率',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≥75% 衡量计划落地能力，责任人（订单管理经理）需关注供应商产能匹配度'
    },
    {
      id: '10',
      kpiName: '市场需求满足率',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≥95% 对标客户服务标准，责任人（客户服务总监）需打通供应链全环节响应'
    }
  ],
  // 实际值维护
  actualValue: [
    {
      id: '1',
      org: '供应链计划管理部',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '销售预测准确率（sellin 口径）',
      kpiUnit: '%',
      targetValue: '≥85%',
      realityValue: '88%',
      charge: '王伟'
    },
    {
      id: '2',
      org: '供应链计划管理部',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '销售预测准确率（sellout 口径）',
      kpiUnit: '%',
      targetValue: '≥80%',
      realityValue: '78%',
      charge: '王伟'
    },
    {
      id: '3',
      org: '供应链计划管理部',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '存货周转天数',
      kpiUnit: '天',
      targetValue: '≤45 天',
      realityValue: '48 天',
      charge: '王伟'
    },
    {
      id: '4',
      org: '供应链计划管理部',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '订单履约周期内销',
      kpiUnit: '天',
      targetValue: '≤15 天',
      realityValue: '14 天',
      charge: '王伟'
    },
    {
      id: '5',
      org: '供应链计划管理部',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '订单履约周期出口',
      kpiUnit: '天',
      targetValue: '≤30 天',
      realityValue: '32 天',
      charge: '王伟'
    },
    {
      id: '6',
      org: '供应链计划管理部',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求变动率（总量）',
      kpiUnit: '%',
      targetValue: '≤15%',
      realityValue: '18%',
      charge: '王伟'
    },
    {
      id: '7',
      org: '供应链计划管理部',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求变动率（结构）',
      kpiUnit: '%',
      targetValue: '≤20%',
      realityValue: '22%',
      charge: '王伟'
    },
    {
      id: '8',
      org: '供应链计划管理部',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '产成品周转天数',
      kpiUnit: '天',
      targetValue: '≤35 天',
      realityValue: '38 天',
      charge: '王伟'
    },
    {
      id: '9',
      org: '供应链计划管理部',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '周 AATP 订单转化率',
      kpiUnit: '%',
      targetValue: '≥75%',
      realityValue: '72%',
      charge: '王伟'
    },
    {
      id: '10',
      org: '供应链计划管理部',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求满足率',
      kpiUnit: '%',
      targetValue: '≥95%',
      realityValue: '92%',
      charge: '王伟'
    }
  ],
  // 个人指标
  // 目标值设定
  personageTarget: [
    {
      id: '1',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '销售预测准确率（sellin 口径）',
      kpiUnit: '%',
      targetValue: '≥85%'
    },
    {
      id: '2',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '销售预测准确率（sellout 口径）',
      kpiUnit: '%',
      targetValue: '≥80%'
    },
    {
      id: '3',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '存货周转天数',
      kpiUnit: '天',
      targetValue: '≤45 天'
    },
    {
      id: '4',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '订单履约周期内销',
      kpiUnit: '天',
      targetValue: '≤15 天'
    },
    {
      id: '5',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '订单履约周期出口',
      kpiUnit: '天',
      targetValue: '≤30 天'
    },
    {
      id: '6',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求变动率（总量）',
      kpiUnit: '%',
      targetValue: '≤15%'
    },
    {
      id: '7',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求变动率（结构）',
      kpiUnit: '%',
      targetValue: '≤20%'
    },
    {
      id: '8',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '产成品周转天数',
      kpiUnit: '天',
      targetValue: '≤35 天'
    },
    {
      id: '9',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '周 AATP 订单转化率',
      kpiUnit: '%',
      targetValue: '≥75%'
    },
    {
      id: '10',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求满足率',
      kpiUnit: '%',
      targetValue: '≥95%'
    }
  ],
  // 智能检查-目标设定统计
  personageTargetStatistics: [
    {
      id: '1',
      orgName: '供应链计划管理部',
      orgCharge: '王伟',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '15',
      scalarType: '3',
      economicClass: '3',
      qualificationType: '1',
      concurrency: '3',
      restrain: '3',
      costPool: '2'
    },
    {
      id: '2',
      orgName: '供应链计划管理部',
      orgCharge: '李丽华​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '13',
      scalarType: '2',
      economicClass: '2',
      qualificationType: '3',
      concurrency: '1',
      restrain: '2',
      costPool: '3'
    },
    {
      id: '3',
      orgName: '供应链计划管理部',
      orgCharge: '李丽娟​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '15',
      scalarType: '3',
      economicClass: '3',
      qualificationType: '3',
      concurrency: '2',
      restrain: '1',
      costPool: '3'
    },
    {
      id: '4',
      orgName: '供应链计划管理部',
      orgCharge: '李丽萍​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '13',
      scalarType: '2',
      economicClass: '1',
      qualificationType: '2',
      concurrency: '3',
      restrain: '3',
      costPool: '2'
    },
    {
      id: '5',
      orgName: '供应链计划管理部',
      orgCharge: '李淑华​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '12',
      scalarType: '2',
      economicClass: '2',
      qualificationType: '1',
      concurrency: '1',
      restrain: '3',
      costPool: '3'
    },
    {
      id: '6',
      orgName: '供应链计划管理部',
      orgCharge: '李素芳​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '12',
      scalarType: '2',
      economicClass: '2',
      qualificationType: '2',
      concurrency: '3',
      restrain: '2',
      costPool: '1'
    },
    {
      id: '7',
      orgName: '供应链计划管理部',
      orgCharge: '刘凤兰​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '13',
      scalarType: '3',
      economicClass: '2',
      qualificationType: '2',
      concurrency: '2',
      restrain: '1',
      costPool: '3'
    },
    {
      id: '8',
      orgName: '供应链计划管理部',
      orgCharge: '刘凤霞​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '13',
      scalarType: '1',
      economicClass: '2',
      qualificationType: '3',
      concurrency: '2',
      restrain: '2',
      costPool: '3'
    },
    {
      id: '9',
      orgName: '供应链计划管理部',
      orgCharge: '刘桂兰​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '12',
      scalarType: '3',
      economicClass: '1',
      qualificationType: '2',
      concurrency: '2',
      restrain: '3',
      costPool: '1'
    },
    {
      id: '10',
      orgName: '供应链计划管理部',
      orgCharge: '刘桂珍​',
      targetClass: '年度目标',
      period: '2025年',
      aimValue: '13',
      scalarType: '1',
      economicClass: '2',
      qualificationType: '3',
      concurrency: '3',
      restrain: '1',
      costPool: '3'
    }
  ],
  // 智能检查-智能检查
  personageSmartCheck: [
    {
      id: '1',
      kpiName: '销售预测准确率（sellin 口径）',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≥85% 合理（参考行业平均），单位明确，责任人（市场预测经理）匹配职能'
    },
    {
      id: '2',
      kpiName: '销售预测准确率（sellout 口径）',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≥80% 略低于 sellin 口径（符合终端预测难度），责任人（销售计划总监）明确'
    },
    {
      id: '3',
      kpiName: '存货周转天数',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤45 天具挑战性（需结合物料周期优化），责任人（供应链总监）权责清晰'
    },
    {
      id: '4',
      kpiName: '订单履约周期内销',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤15 天对标高效物流可行，责任人（国内物流经理）匹配执行主体'
    },
    {
      id: '5',
      kpiName: '订单履约周期出口',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤30 天需考虑海运波动，责任人（国际物流经理）需协同关务资源'
    },
    {
      id: '6',
      kpiName: '市场需求变动率（总量）',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤15% 反映供应链稳定性要求，责任人（市场分析经理）需联动销售端数据'
    },
    {
      id: '7',
      kpiName: '市场需求变动率（结构）',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤20% 高于总量（合理体现结构调整难度），责任人（产品规划经理）需优化品类策略'
    },
    {
      id: '8',
      kpiName: '产成品周转天数',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≤35 天需生产计划与销售协同，责任人（生产计划经理）需强化需求拉动机制'
    },
    {
      id: '9',
      kpiName: '周 AATP 订单转化率',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≥75% 衡量计划落地能力，责任人（订单管理经理）需关注供应商产能匹配度'
    },
    {
      id: '10',
      kpiName: '市场需求满足率',
      targetClass: '年度目标',
      period: '2025 年',
      checkResult: '目标值≥95% 对标客户服务标准，责任人（客户服务总监）需打通供应链全环节响应'
    }
  ],
  // 实际值维护
  personageActualValue: [
    {
      id: '1',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '销售预测准确率（sellin 口径）',
      kpiUnit: '%',
      targetValue: '≥85%',
      realityValue: '88%'
    },
    {
      id: '2',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '销售预测准确率（sellout 口径）',
      kpiUnit: '%',
      targetValue: '≥80%',
      realityValue: '78%'
    },
    {
      id: '3',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '存货周转天数',
      kpiUnit: '天',
      targetValue: '≤45 天',
      realityValue: '48 天'
    },
    {
      id: '4',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '订单履约周期内销',
      kpiUnit: '天',
      targetValue: '≤15 天',
      realityValue: '14 天'
    },
    {
      id: '5',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '订单履约周期出口',
      kpiUnit: '天',
      targetValue: '≤30 天',
      realityValue: '32 天'
    },
    {
      id: '6',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求变动率（总量）',
      kpiUnit: '%',
      targetValue: '≤15%',
      realityValue: '18%'
    },
    {
      id: '7',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求变动率（结构）',
      kpiUnit: '%',
      targetValue: '≤20%',
      realityValue: '22%'
    },
    {
      id: '8',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '产成品周转天数',
      kpiUnit: '天',
      targetValue: '≤35 天',
      realityValue: '38 天'
    },
    {
      id: '9',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '周 AATP 订单转化率',
      kpiUnit: '%',
      targetValue: '≥75%',
      realityValue: '72%'
    },
    {
      id: '10',
      orgName: '供应链计划管理部',
      staff: '王伟',
      postName: '供应链总监',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求满足率',
      kpiUnit: '%',
      targetValue: '≥95%',
      realityValue: '92%'
    }
  ],
  // 项目指标
  // 目标值设定
  projectActualValue: [
    {
      id: '1',
      projectName: '供应链效率提升项目',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '销售预测准确率（sellin 口径）',
      kpiUnit: '%',
      targetValue: '≥85%',
      realityValue: '88%',
      charge: '李凤英​'
    },
    {
      id: '2',
      projectName: '供应链效率提升项目',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '销售预测准确率（sellout 口径）',
      kpiUnit: '%',
      targetValue: '≥80%',
      realityValue: '78%',
      charge: '李国强​'
    },
    {
      id: '3',
      projectName: '供应链效率提升项目',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '存货周转天数',
      kpiUnit: '天',
      targetValue: '≤45 天',
      realityValue: '48 天',
      charge: '李建国​'
    },
    {
      id: '4',
      projectName: '供应链效率提升项目',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '订单履约周期内销',
      kpiUnit: '天',
      targetValue: '≤15 天',
      realityValue: '14 天',
      charge: '李建军​'
    },
    {
      id: '5',
      projectName: '供应链效率提升项目',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '订单履约周期出口',
      kpiUnit: '天',
      targetValue: '≤30 天',
      realityValue: '32 天',
      charge: '李丽华​'
    },
    {
      id: '6',
      projectName: '供应链效率提升项目',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求变动率（总量）',
      kpiUnit: '%',
      targetValue: '≤15%',
      realityValue: '18%',
      charge: '李丽娟​'
    },
    {
      id: '7',
      projectName: '供应链效率提升项目',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求变动率（结构）',
      kpiUnit: '%',
      targetValue: '≤20%',
      realityValue: '22%',
      charge: '李丽萍​'
    },
    {
      id: '8',
      projectName: '供应链效率提升项目',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '产成品周转天数',
      kpiUnit: '天',
      targetValue: '≤35 天',
      realityValue: '38 天',
      charge: '李淑华​'
    },
    {
      id: '9',
      projectName: '供应链效率提升项目',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '周 AATP 订单转化率',
      kpiUnit: '%',
      targetValue: '≥75%',
      realityValue: '72%',
      charge: '李素芳​'
    },
    {
      id: '10',
      projectName: '供应链效率提升项目',
      targetClass: '年度目标',
      period: '2025 年',
      kpiName: '市场需求满足率',
      kpiUnit: '%',
      targetValue: '≥95%',
      realityValue: '92%',
      charge: '刘凤兰​'
    }
  ]
}
// 指标诊断与根因分析
export const diagnosticAnalysis = {
  // 组织指标
  organization: [
    {
      id: '1',
      kpiName: '销售预测准确率（sellin 口径）',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '李凤英',
      targetValue: '≥85%',
      realityValue: '88%',
      yieldRate: '100.00%',
      gap: '3%'
    },
    {
      id: '2',
      kpiName: '销售预测准确率（sellout 口径）',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '李国强',
      targetValue: '≥80%',
      realityValue: '78%',
      yieldRate: '97.50%',
      gap: '-2%'
    },
    {
      id: '3',
      kpiName: '库存周转天数',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '天',
      charge: '李建国',
      targetValue: '≤45 天',
      realityValue: '48 天',
      yieldRate: '93.75%',
      gap: '+3 天'
    },
    {
      id: '4',
      kpiName: '订单履约周期内销',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '天',
      charge: '李建军',
      targetValue: '≤15 天',
      realityValue: '14 天',
      yieldRate: '100.00%',
      gap: '-1 天'
    },
    {
      id: '5',
      kpiName: '订单履约周期出口',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '天',
      charge: '李丽华',
      targetValue: '≤30 天',
      realityValue: '32 天',
      yieldRate: '93.75%',
      gap: '+2 天'
    },
    {
      id: '6',
      kpiName: '供应商交付准时率',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '张志强',
      targetValue: '≥95%',
      realityValue: '92%',
      yieldRate: '96.84%',
      gap: '-3%'
    },
    {
      id: '7',
      kpiName: '市场需求变动率（结构）',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '李丽萍',
      targetValue: '≤20%',
      realityValue: '22%',
      yieldRate: '90.91%',
      gap: '2%'
    },
    {
      id: '8',
      kpiName: '产成品周转天数',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '天',
      charge: '李淑华',
      targetValue: '≤35 天',
      realityValue: '38 天',
      yieldRate: '92.11%',
      gap: '+3 天'
    },
    {
      id: '9',
      kpiName: '周 AATP 订单转化率',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '李素芳',
      targetValue: '≥75%',
      realityValue: '72%',
      yieldRate: '96.00%',
      gap: '-3%'
    },
    {
      id: '10',
      kpiName: '市场需求满足率',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '刘凤兰',
      targetValue: '≥95%',
      realityValue: '92%',
      yieldRate: '96.84%',
      gap: '-3%'
    }
  ],
  // 人员指标
  person1: [
    {
      id: '1',
      kpiName: '供应商交付准时率',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '王伟',
      targetValue: '≥95%',
      realityValue: '93%',
      yieldRate: '97.89%',
      gap: '-2%'
    },
    {
      id: '2',
      kpiName: '库存周转天数',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '天',
      charge: '王伟',
      targetValue: '≤45 天',
      realityValue: '48 天',
      yieldRate: '93.75%',
      gap: '+3 天'
    },
    {
      id: '3',
      kpiName: '供应链协同指数',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '分',
      charge: '王伟',
      targetValue: '≥85 分',
      realityValue: '82 分',
      yieldRate: '96.47%',
      gap: '-3 分'
    },
    {
      id: '4',
      kpiName: '替代供应商储备率',
      targetClass: '月度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '王伟',
      targetValue: '≥30%',
      realityValue: '28%',
      yieldRate: '93.33%',
      gap: '-2%'
    },
    {
      id: '5',
      kpiName: '物流成本占比',
      targetClass: '月度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '王伟',
      targetValue: '≤8%',
      realityValue: '8.50%',
      yieldRate: '94.12%',
      gap: '0.50%'
    }
  ],
  // 同岗位人员指标表现
  person2: [
    {
      id: '1',
      kpiName: '库存周转天数',
      staff: '陈宇',
      depName: '供应链部',
      postName: '副总经理兼出口计划管理',
      targetValue: '≤45 天',
      realityValue: '38 天',
      yieldRate: '118.42%',
      ranking: '1'
    },
    {
      id: '2',
      kpiName: '库存周转天数',
      staff: '刘畅',
      depName: '采购部',
      postName: '采购部总经理',
      targetValue: '≤45 天',
      realityValue: '42 天',
      yieldRate: '107.14%',
      ranking: '2'
    },
    {
      id: '3',
      kpiName: '库存周转天数',
      staff: '王磊',
      depName: '计划与物资部',
      postName: '计划与物资管理部部长',
      targetValue: '≤45 天',
      realityValue: '45 天',
      yieldRate: '100.00%',
      ranking: '3'
    },
    {
      id: '4',
      kpiName: '库存周转天数',
      staff: '王伟',
      depName: '采购部',
      postName: '高级采购资源工程师',
      targetValue: '≤45 天',
      realityValue: '48 天',
      yieldRate: '93.75%',
      ranking: '4'
    },
    {
      id: '5',
      kpiName: '库存周转天数',
      staff: '李娜',
      depName: '供应链管理部',
      postName: '供应链管理部副部长',
      targetValue: '≤45 天',
      realityValue: '50 天',
      yieldRate: '90.00%',
      ranking: '5'
    },
    {
      id: '6',
      kpiName: '库存周转天数',
      staff: '赵阳',
      depName: '采购部',
      postName: '采购部副部长',
      targetValue: '≤45 天',
      realityValue: '52 天',
      yieldRate: '86.54%',
      ranking: '6'
    },
    {
      id: '7',
      kpiName: '库存周转天数',
      staff: '周伟',
      depName: '计划室',
      postName: '计划室室主任',
      targetValue: '≤45 天',
      realityValue: '55 天',
      yieldRate: '81.82%',
      ranking: '7'
    }
  ],
  // 项目指标
  project: [
    {
      id: '1',
      kpiName: '销售预测准确率（sellin 口径）',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '李凤英',
      targetValue: '≥85%',
      realityValue: '88%',
      yieldRate: '100.00%',
      gap: '3%'
    },
    {
      id: '2',
      kpiName: '销售预测准确率（sellout 口径）',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '李国强',
      targetValue: '≥80%',
      realityValue: '78%',
      yieldRate: '97.50%',
      gap: '-2%'
    },
    {
      id: '3',
      kpiName: '库存周转天数',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '天',
      charge: '李建国',
      targetValue: '≤45 天',
      realityValue: '48 天',
      yieldRate: '93.75%',
      gap: '+3 天'
    },
    {
      id: '4',
      kpiName: '订单履约周期内销',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '天',
      charge: '李建军',
      targetValue: '≤15 天',
      realityValue: '14 天',
      yieldRate: '100.00%',
      gap: '-1 天'
    },
    {
      id: '5',
      kpiName: '订单履约周期出口',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '天',
      charge: '李丽华',
      targetValue: '≤30 天',
      realityValue: '32 天',
      yieldRate: '93.75%',
      gap: '+2 天'
    },
    {
      id: '6',
      kpiName: '市场需求变动率（总量）',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '李丽娟',
      targetValue: '≤15%',
      realityValue: '18%',
      yieldRate: '83.33%',
      gap: '3%'
    },
    {
      id: '7',
      kpiName: '市场需求变动率（结构）',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '李丽萍',
      targetValue: '≤20%',
      realityValue: '22%',
      yieldRate: '90.91%',
      gap: '2%'
    },
    {
      id: '8',
      kpiName: '产成品周转天数',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '天',
      charge: '李淑华',
      targetValue: '≤35 天',
      realityValue: '38 天',
      yieldRate: '92.11%',
      gap: '+3 天'
    },
    {
      id: '9',
      kpiName: '周 AATP 订单转化率',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '李素芳',
      targetValue: '≥75%',
      realityValue: '72%',
      yieldRate: '96.00%',
      gap: '-3%'
    },
    {
      id: '10',
      kpiName: '市场需求满足率',
      targetClass: '年度目标',
      period: '2025 年',
      kpiUnit: '%',
      charge: '刘凤兰',
      targetValue: '≥95%',
      realityValue: '92%',
      yieldRate: '96.84%',
      gap: '-3%'
    }
  ]
}
// 指标趋势预测与风险预警
export const trendRisk = {
  // 指标趋势预测
  kpiTrendPre: {
    kpiTrendPreList: [
      {
        id: '1',
        kpiName: '销售预测准确率（sellin 口径）',
        targetClass: '年度目标',
        targetCycle: '2025 年',
        kpiUnit: '%',
        personLiable: '李凤英',
        targetValue: '≥85%',
        actualPerformance: '88%',
        achieveRate: '100.00%',
        gap: '3%'
      },
      {
        id: '2',
        kpiName: '销售预测准确率（sellout 口径）',
        targetClass: '年度目标',
        targetCycle: '2025 年',
        kpiUnit: '%',
        personLiable: '李国强',
        targetValue: '≥80%',
        actualPerformance: '78%',
        achieveRate: '97.50%',
        gap: '-2%'
      },
      {
        id: '3',
        kpiName: '库存周转天数',
        targetClass: '年度目标',
        targetCycle: '2025 年',
        kpiUnit: '天',
        personLiable: '李建国',
        targetValue: '≤45 天',
        actualPerformance: '48 天',
        achieveRate: '93.75%',
        gap: '+3 天'
      },
      {
        id: '4',
        kpiName: '订单履约周期内销',
        targetClass: '年度目标',
        targetCycle: '2025 年',
        kpiUnit: '天',
        personLiable: '李建军',
        targetValue: '≤15 天',
        actualPerformance: '14 天',
        achieveRate: '100.00%',
        gap: '-1 天'
      },
      {
        id: '5',
        kpiName: '订单履约周期出口',
        targetClass: '年度目标',
        targetCycle: '2025 年',
        kpiUnit: '天',
        personLiable: '李丽华',
        targetValue: '≤30 天',
        actualPerformance: '32 天',
        achieveRate: '93.75%',
        gap: '+2 天'
      },
      {
        id: '6',
        kpiName: '供应商交付准时率',
        targetClass: '年度目标',
        targetCycle: '2025 年',
        kpiUnit: '%',
        personLiable: '张志强',
        targetValue: '≥95%',
        actualPerformance: '92%',
        achieveRate: '96.84%',
        gap: '-3%'
      },
      {
        id: '7',
        kpiName: '市场需求变动率（结构）',
        targetClass: '年度目标',
        targetCycle: '2025 年',
        kpiUnit: '%',
        personLiable: '李丽萍',
        targetValue: '≤20%',
        actualPerformance: '22%',
        achieveRate: '90.91%',
        gap: '2%'
      },
      {
        id: '8',
        kpiName: '产成品周转天数',
        targetClass: '年度目标',
        targetCycle: '2025 年',
        kpiUnit: '天',
        personLiable: '李淑华',
        targetValue: '≤35 天',
        actualPerformance: '38 天',
        achieveRate: '92.11%',
        gap: '+3 天'
      },
      {
        id: '9',
        kpiName: '周 AATP 订单转化率',
        targetClass: '年度目标',
        targetCycle: '2025 年',
        kpiUnit: '%',
        personLiable: '李素芳',
        targetValue: '≥75%',
        actualPerformance: '72%',
        achieveRate: '96.00%',
        gap: '-3%'
      },
      {
        id: '10',
        kpiName: '市场需求满足率',
        targetClass: '年度目标',
        targetCycle: '2025 年',
        kpiUnit: '%',
        personLiable: '刘凤兰',
        targetValue: '≥95%',
        actualPerformance: '92%',
        achieveRate: '96.84%',
        gap: '-3%'
      }
    ],

    //趋势预测（存货周转天数）
    trendPreResult: [
      {
        id: '1',
        kpiName: '库存周转天数',
        kpiPeriod: '年度',
        predictCycle: '当前',
        kpiCycle: '2025年',
        baseValue: '48 天',
        optimisticValue: '45 天',
        pessimisticValue: '50 天'
      },
      {
        id: '2',
        kpiName: '库存周转天数',
        kpiPeriod: '年度',
        predictCycle: 'T+1',
        kpiCycle: '2026 年',
        baseValue: '47 天',
        optimisticValue: '44 天',
        pessimisticValue: '52 天'
      },
      {
        id: '3',
        kpiName: '库存周转天数',
        kpiPeriod: '年度',
        predictCycle: 'T+2',
        kpiCycle: '2027 年',
        baseValue: '45 天',
        optimisticValue: '42 天',
        pessimisticValue: '55 天'
      },
      {
        id: '4',
        kpiName: '库存周转天数',
        kpiPeriod: '年度',
        predictCycle: 'T+3',
        kpiCycle: '2028 年',
        baseValue: '42 天',
        optimisticValue: '39 天',
        pessimisticValue: '58 天'
      }
    ]
  },
  // 风险预警
  warningList: [
    {
      id: '1',
      kpiName: '销售预测准确率（sellin 口径）',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '李凤英',
      targetValue: '≥85%',
      actualPerformance: '88%',
      threshold:
        '①连续 2 个月实际值＜85%；②预测偏差导致经销商库存积压超 10%；③营收同比下降 5% 且毛利下降＞2%（结合用户预设条件）。'
    },
    {
      id: '2',
      kpiName: '销售预测准确率（sellout 口径）',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '李国强',
      targetValue: '≥80%',
      actualPerformance: '78%',
      threshold: '①连续 3 周实际值＜80%；②终端市场缺货率＞5%；③线上 / 线下渠道预测偏差率＞15%。'
    },
    {
      id: '3',
      kpiName: '库存周转天数',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '天',
      personLiable: '李建国',
      targetValue: '≤45 天',
      actualPerformance: '48 天',
      threshold: '①连续 2 个月＞45 天；②原材料 / 产成品库存周转天数同比上升＞10%；③库存资金占用超预算 15%。'
    },
    {
      id: '4',
      kpiName: '订单履约周期内销',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '天',
      personLiable: '李建军',
      targetValue: '≤15 天',
      actualPerformance: '14 天',
      threshold:
        '①单月履约超时订单占比＞8%；②华东 / 华南核心区域履约周期波动＞20%；③因履约延迟导致客户扣款≥50 万元 / 月。'
    },
    {
      id: '5',
      kpiName: '订单履约周期出口',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '天',
      personLiable: '李丽华',
      targetValue: '≤30 天',
      actualPerformance: '32 天',
      threshold:
        '①连续 4 周＞30 天；②海运 / 空运延误导致订单超期率＞12%；③主要出口国（如美国 / 欧洲）清关滞留订单占比＞10%。'
    },
    {
      id: '6',
      kpiName: '供应商交付准时率',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '张志强',
      targetValue: '≥95%',
      actualPerformance: '92%',
      threshold:
        '①关键物料（如芯片 / 压缩机）供应商准时率＜90%；②连续 3 次交付延迟导致产线停线≥2 小时；③月度交付准时率环比下降＞5%。'
    },
    {
      id: '7',
      kpiName: '市场需求变动率（结构）',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '李丽萍',
      targetValue: '≤20%',
      actualPerformance: '22%',
      threshold:
        '①智能家电 / 传统家电品类需求占比波动＞25%；②季度内产品结构调整频次＞5 次；③因结构变动导致生产线切换成本增加＞20%。'
    },
    {
      id: '8',
      kpiName: '产成品周转天数',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '天',
      personLiable: '李淑华',
      targetValue: '≤35 天',
      actualPerformance: '38 天',
      threshold:
        '①旺季（如 Q3）产成品库存积压超安全库存 20%；②重点型号（如空调 / 冰箱）周转天数＞45 天；③产成品仓储费用同比增加＞15%。'
    },
    {
      id: '9',
      kpiName: '周 AATP 订单转化率',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '李素芳',
      targetValue: '≥75%',
      actualPerformance: '72%',
      threshold: '①连续 3 周＜75%；②紧急插单率＞15% 导致产能利用率下降；③因订单转化不足导致原材料采购计划偏差＞10%。'
    },
    {
      id: '10',
      kpiName: '市场需求满足率',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '刘凤兰',
      targetValue: '≥95%',
      actualPerformance: '92%',
      threshold:
        '①核心客户（年采购额≥5000 万）需求满足率＜90%；②月度客户投诉量超历史均值 2 倍；③因缺货导致的订单流失金额≥1000 万元 / 月。'
    }
  ],
  // 风险视图
  viewList: [
    {
      id: '1',
      orgName: '采购部',
      total: '18',
      attentionLevel: '12',
      warnLevel: '5',
      seriousLevel: '1',
      addAttentionLevel: '3',
      addWarnLevel: '1',
      addSeriousLevel: '0',
      handelProgress: '65%'
    },
    {
      id: '2',
      orgName: '重庆冰箱工厂',
      total: '15',
      attentionLevel: '10',
      warnLevel: '4',
      seriousLevel: '1',
      addAttentionLevel: '2',
      addWarnLevel: '1',
      addSeriousLevel: '0',
      handelProgress: '70%'
    },
    {
      id: '3',
      orgName: '电子商务部',
      total: '22',
      attentionLevel: '15',
      warnLevel: '6',
      seriousLevel: '1',
      addAttentionLevel: '4',
      addWarnLevel: '2',
      addSeriousLevel: '0',
      handelProgress: '55%'
    },
    {
      id: '4',
      orgName: '欧盟区产品部',
      total: '20',
      attentionLevel: '13',
      warnLevel: '6',
      seriousLevel: '1',
      addAttentionLevel: '3',
      addWarnLevel: '2',
      addSeriousLevel: '0',
      handelProgress: '60%'
    },
    {
      id: '5',
      orgName: '工艺部',
      total: '10',
      attentionLevel: '7',
      warnLevel: '3',
      seriousLevel: '0',
      addAttentionLevel: '1',
      addWarnLevel: '1',
      addSeriousLevel: '0',
      handelProgress: '80%'
    },
    {
      id: '6',
      orgName: '供应链计划管理部',
      total: '25',
      attentionLevel: '18',
      warnLevel: '6',
      seriousLevel: '1',
      addAttentionLevel: '5',
      addWarnLevel: '2',
      addSeriousLevel: '0',
      handelProgress: '50%'
    },
    {
      id: '7',
      orgName: 'GTM 部',
      total: '16',
      attentionLevel: '11',
      warnLevel: '4',
      seriousLevel: '1',
      addAttentionLevel: '3',
      addWarnLevel: '1',
      addSeriousLevel: '0',
      handelProgress: '65%'
    },
    {
      id: '8',
      orgName: '结构研发部',
      total: '12',
      attentionLevel: '8',
      warnLevel: '4',
      seriousLevel: '0',
      addAttentionLevel: '2',
      addWarnLevel: '1',
      addSeriousLevel: '0',
      handelProgress: '75%'
    },
    {
      id: '9',
      orgName: '经营与财务管理部',
      total: '19',
      attentionLevel: '13',
      warnLevel: '5',
      seriousLevel: '1',
      addAttentionLevel: '3',
      addWarnLevel: '2',
      addSeriousLevel: '0',
      handelProgress: '60%'
    },
    {
      id: '10',
      orgName: '冷柜研发部',
      total: '11',
      attentionLevel: '7',
      warnLevel: '4',
      seriousLevel: '0',
      addAttentionLevel: '2',
      addWarnLevel: '1',
      addSeriousLevel: '0',
      handelProgress: '70%'
    },
    {
      id: '11',
      orgName: '零售与用户运营部',
      total: '21',
      attentionLevel: '14',
      warnLevel: '6',
      seriousLevel: '1',
      addAttentionLevel: '4',
      addWarnLevel: '2',
      addSeriousLevel: '0',
      handelProgress: '55%'
    },
    {
      id: '12',
      orgName: '品牌与产品营销部',
      total: '17',
      attentionLevel: '12',
      warnLevel: '4',
      seriousLevel: '1',
      addAttentionLevel: '3',
      addWarnLevel: '1',
      addSeriousLevel: '0',
      handelProgress: '65%'
    }
  ]
}
// 指标改善任务一览
export const improveTaskList = [
  {
    id: '1',
    orgName: '采购部',
    improveKpi: '3',
    totalTask: '22 项',
    noStart: '5 项',
    inProgress: '12 项',
    completed: '4 项',
    beOverdue: '1 项',
    addImproveKpi: '3',
    addTask: '4 项',
    addNoStart: '1 项',
    addInProgress: '2 项',
    addCompleted: '1 项',
    addBeOverdue: '0 项',
    taskProgress: '18%'
  },
  {
    id: '2',
    orgName: '重庆冰箱工厂',
    improveKpi: '4',
    totalTask: '28 项',
    noStart: '7 项',
    inProgress: '15 项',
    completed: '5 项',
    beOverdue: '1 项',
    addImproveKpi: '4',
    addTask: '6 项',
    addNoStart: '2 项',
    addInProgress: '3 项',
    addCompleted: '1 项',
    addBeOverdue: '0 项',
    taskProgress: '18%'
  },
  {
    id: '3',
    orgName: '电子商务部',
    improveKpi: '5',
    totalTask: '24 项',
    noStart: '6 项',
    inProgress: '13 项',
    completed: '4 项',
    beOverdue: '1 项',
    addImproveKpi: '5',
    addTask: '5 项',
    addNoStart: '1 项',
    addInProgress: '3 项',
    addCompleted: '1 项',
    addBeOverdue: '0 项',
    taskProgress: '17%'
  },
  {
    id: '4',
    orgName: '欧盟区产品部',
    improveKpi: '6',
    totalTask: '26 项',
    noStart: '8 项',
    inProgress: '14 项',
    completed: '3 项',
    beOverdue: '1 项',
    addImproveKpi: '6',
    addTask: '5 项',
    addNoStart: '3 项',
    addInProgress: '1 项',
    addCompleted: '1 项',
    addBeOverdue: '0 项',
    taskProgress: '12%'
  },
  {
    id: '5',
    orgName: '工艺部',
    improveKpi: '7',
    totalTask: '20 项',
    noStart: '4 项',
    inProgress: '10 项',
    completed: '5 项',
    beOverdue: '1 项',
    addImproveKpi: '7',
    addTask: '4 项',
    addNoStart: '0 项',
    addInProgress: '2 项',
    addCompleted: '2 项',
    addBeOverdue: '0 项',
    taskProgress: '25%'
  },
  {
    id: '6',
    orgName: '供应链计划管理部',
    improveKpi: '8',
    totalTask: '30 项',
    noStart: '9 项',
    inProgress: '16 项',
    completed: '4 项',
    beOverdue: '1 项',
    addImproveKpi: '8',
    addTask: '6 项',
    addNoStart: '4 项',
    addInProgress: '1 项',
    addCompleted: '1 项',
    addBeOverdue: '0 项',
    taskProgress: '13%'
  },
  {
    id: '7',
    orgName: 'GTM 部',
    improveKpi: '4',
    totalTask: '23 项',
    noStart: '5 项',
    inProgress: '13 项',
    completed: '4 项',
    beOverdue: '1 项',
    addImproveKpi: '4',
    addTask: '5 项',
    addNoStart: '1 项',
    addInProgress: '3 项',
    addCompleted: '1 项',
    addBeOverdue: '0 项',
    taskProgress: '17%'
  },
  {
    id: '8',
    orgName: '结构研发部',
    improveKpi: '9',
    totalTask: '18 项',
    noStart: '3 项',
    inProgress: '9 项',
    completed: '5 项',
    beOverdue: '1 项',
    addImproveKpi: '9',
    addTask: '3 项',
    addNoStart: '0 项',
    addInProgress: '1 项',
    addCompleted: '2 项',
    addBeOverdue: '0 项',
    taskProgress: '28%'
  },
  {
    id: '9',
    orgName: '经营与财务管理部',
    improveKpi: '10',
    totalTask: '27 项',
    noStart: '7 项',
    inProgress: '15 项',
    completed: '4 项',
    beOverdue: '1 项',
    addImproveKpi: '10',
    addTask: '6 项',
    addNoStart: '3 项',
    addInProgress: '2 项',
    addCompleted: '1 项',
    addBeOverdue: '0 项',
    taskProgress: '15%'
  },
  {
    id: '10',
    orgName: '冷柜研发部',
    improveKpi: '11',
    totalTask: '19 项',
    noStart: '4 项',
    inProgress: '10 项',
    completed: '4 项',
    beOverdue: '1 项',
    addImproveKpi: '11',
    addTask: '4 项',
    addNoStart: '1 项',
    addInProgress: '2 项',
    addCompleted: '1 项',
    addBeOverdue: '0 项',
    taskProgress: '21%'
  },
  {
    id: '11',
    orgName: '零售与用户运营部',
    improveKpi: '12',
    totalTask: '25 项',
    noStart: '6 项',
    inProgress: '14 项',
    completed: '4 项',
    beOverdue: '1 项',
    addImproveKpi: '12',
    addTask: '5 项',
    addNoStart: '2 项',
    addInProgress: '2 项',
    addCompleted: '1 项',
    addBeOverdue: '0 项',
    taskProgress: '16%'
  },
  {
    id: '12',
    orgName: '品牌与产品营销部',
    improveKpi: '2',
    totalTask: '21 项',
    noStart: '5 项',
    inProgress: '11 项',
    completed: '4 项',
    beOverdue: '1 项',
    addImproveKpi: '2',
    addTask: '4 项',
    addNoStart: '1 项',
    addInProgress: '2 项',
    addCompleted: '1 项',
    addBeOverdue: '0 项',
    taskProgress: '19%'
  }
]
// 指标改善效果追踪
export const effectTracking = {
  //当期表现[供应链计划管理部]
  currentPerformance: [
    {
      id: '1',
      kpiName: '销售预测准确率（sellin 口径）',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '李凤英',
      targetValue: '≥85%',
      actualPerformance: '88%',
      achieveRate: '100.00%',
      gap: '3%'
    },
    {
      id: '2',
      kpiName: '销售预测准确率（sellout 口径）',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '李国强',
      targetValue: '≥80%',
      actualPerformance: '78%',
      achieveRate: '97.50%',
      gap: '-2%'
    },
    {
      id: '3',
      kpiName: '库存周转天数',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '天',
      personLiable: '李建国',
      targetValue: '≤45 天',
      actualPerformance: '48 天',
      achieveRate: '93.75%',
      gap: '+3 天'
    },
    {
      id: '4',
      kpiName: '订单履约周期内销',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '天',
      personLiable: '李建军',
      targetValue: '≤15 天',
      actualPerformance: '14 天',
      achieveRate: '100.00%',
      gap: '-1 天'
    },
    {
      id: '5',
      kpiName: '订单履约周期出口',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '天',
      personLiable: '李丽华',
      targetValue: '≤30 天',
      actualPerformance: '32 天',
      achieveRate: '93.75%',
      gap: '+2 天'
    },
    {
      id: '6',
      kpiName: '供应商交付准时率',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '张志强',
      targetValue: '≥95%',
      actualPerformance: '92%',
      achieveRate: '96.84%',
      gap: '-3%'
    },
    {
      id: '7',
      kpiName: '市场需求变动率（结构）',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '李丽萍',
      targetValue: '≤20%',
      actualPerformance: '22%',
      achieveRate: '90.91%',
      gap: '2%'
    },
    {
      id: '8',
      kpiName: '产成品周转天数',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '天',
      personLiable: '李淑华',
      targetValue: '≤35 天',
      actualPerformance: '38 天',
      achieveRate: '92.11%',
      gap: '+3 天'
    },
    {
      id: '9',
      kpiName: '周 AATP 订单转化率',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '李素芳',
      targetValue: '≥75%',
      actualPerformance: '72%',
      achieveRate: '96.00%',
      gap: '-3%'
    },
    {
      id: '10',
      kpiName: '市场需求满足率',
      targetClass: '年度目标',
      targetCycle: '2025 年',
      kpiUnit: '%',
      personLiable: '刘凤兰',
      targetValue: '≥95%',
      actualPerformance: '92%',
      achieveRate: '96.84%',
      gap: '-3%'
    }
  ],

  //上期表现[供应链计划管理部]
  prePerformance: [
    {
      id: '1',
      kpiName: '销售预测准确率（sellin 口径）',
      targetClass: '年度目标',
      targetCycle: '2024 年',
      kpiUnit: '%',
      personLiable: '李凤英',
      targetValue: '≥80%',
      actualPerformance: '83%',
      achieveRate: '103.75%',
      gap: '3%'
    },
    {
      id: '2',
      kpiName: '销售预测准确率（sellout 口径）',
      targetClass: '年度目标',
      targetCycle: '2024 年',
      kpiUnit: '%',
      personLiable: '李国强',
      targetValue: '≥75%',
      actualPerformance: '73%',
      achieveRate: '97.33%',
      gap: '-2%'
    },
    {
      id: '3',
      kpiName: '存货周转天数',
      targetClass: '年度目标',
      targetCycle: '2024 年',
      kpiUnit: '天',
      personLiable: '李建国',
      targetValue: '≤50 天',
      actualPerformance: '53 天',
      achieveRate: '94.34%',
      gap: '+3 天'
    },
    {
      id: '4',
      kpiName: '订单履约周期内销',
      targetClass: '年度目标',
      targetCycle: '2024 年',
      kpiUnit: '天',
      personLiable: '李建军',
      targetValue: '≤18 天',
      actualPerformance: '17 天',
      achieveRate: '100.00%',
      gap: '-1 天'
    },
    {
      id: '5',
      kpiName: '订单履约周期出口',
      targetClass: '年度目标',
      targetCycle: '2024 年',
      kpiUnit: '天',
      personLiable: '李丽华',
      targetValue: '≤35 天',
      actualPerformance: '37 天',
      achieveRate: '94.59%',
      gap: '+2 天'
    },
    {
      id: '6',
      kpiName: '供应商交付准时率',
      targetClass: '年度目标',
      targetCycle: '2024 年',
      kpiUnit: '%',
      personLiable: '张志强',
      targetValue: '≥92%',
      actualPerformance: '89%',
      achieveRate: '96.74%',
      gap: '-3%'
    },
    {
      id: '7',
      kpiName: '市场需求变动率（结构）',
      targetClass: '年度目标',
      targetCycle: '2024 年',
      kpiUnit: '%',
      personLiable: '李丽萍',
      targetValue: '≤25%',
      actualPerformance: '27%',
      achieveRate: '92.59%',
      gap: '2%'
    },
    {
      id: '8',
      kpiName: '产成品周转天数',
      targetClass: '年度目标',
      targetCycle: '2024 年',
      kpiUnit: '天',
      personLiable: '李淑华',
      targetValue: '≤40 天',
      actualPerformance: '43 天',
      achieveRate: '93.02%',
      gap: '+3 天'
    },
    {
      id: '9',
      kpiName: '周 AATP 订单转化率',
      targetClass: '年度目标',
      targetCycle: '2024 年',
      kpiUnit: '%',
      personLiable: '李素芳',
      targetValue: '≥70%',
      actualPerformance: '67%',
      achieveRate: '95.71%',
      gap: '-3%'
    },
    {
      id: '10',
      kpiName: '市场需求满足率',
      targetClass: '年度目标',
      targetCycle: '2024 年',
      kpiUnit: '%',
      personLiable: '刘凤兰',
      targetValue: '≥92%',
      actualPerformance: '89%',
      achieveRate: '96.74%',
      gap: '-3%'
    }
  ],

  //指标变化
  kpiChange: [
    {
      id: '1',
      kpiName: '销售预测准确率（sellin 口径）',
      targetClass: '年度目标',
      kpiUnit: '%',
      personLiable: '李凤英',
      current: '2025 年',
      pre: '2024 年',
      currentTarget: '≥85%',
      precurrentTarget: '≥80%',
      targetChange: '5%',
      currentActuall: '88%',
      preActuall: '83%',
      ActuallChange: '5%'
    },
    {
      id: '2',
      kpiName: '销售预测准确率（sellout 口径）',
      targetClass: '年度目标',
      kpiUnit: '%',
      personLiable: '李国强',
      current: '2025 年',
      pre: '2024 年',
      currentTarget: '≥80%',
      precurrentTarget: '≥75%',
      targetChange: '5%',
      currentActuall: '78%',
      preActuall: '73%',
      ActuallChange: '5%'
    },
    {
      id: '3',
      kpiName: '库存周转天数',
      targetClass: '年度目标',
      kpiUnit: '天',
      personLiable: '李建国',
      current: '2025 年',
      pre: '2024 年',
      currentTarget: '≤45 天',
      precurrentTarget: '≤50 天',
      targetChange: '-5 天',
      currentActuall: '48 天',
      preActuall: '53 天',
      ActuallChange: '-5 天'
    },
    {
      id: '4',
      kpiName: '订单履约周期内销',
      targetClass: '年度目标',
      kpiUnit: '天',
      personLiable: '李建军',
      current: '2025 年',
      pre: '2024 年',
      currentTarget: '≤15 天',
      precurrentTarget: '≤18 天',
      targetChange: '-3 天',
      currentActuall: '14 天',
      preActuall: '17 天',
      ActuallChange: '-3 天'
    },
    {
      id: '5',
      kpiName: '订单履约周期出口',
      targetClass: '年度目标',
      kpiUnit: '天',
      personLiable: '李丽华',
      current: '2025 年',
      pre: '2024 年',
      currentTarget: '≤30 天',
      precurrentTarget: '≤35 天',
      targetChange: '-5 天',
      currentActuall: '32 天',
      preActuall: '37 天',
      ActuallChange: '-5 天'
    },
    {
      id: '6',
      kpiName: '供应商交付准时率',
      targetClass: '年度目标',
      kpiUnit: '%',
      personLiable: '张志强',
      current: '2025 年',
      pre: '2024 年',
      currentTarget: '≥95%',
      precurrentTarget: '≥92%',
      targetChange: '3%',
      currentActuall: '92%',
      preActuall: '89%',
      ActuallChange: '3%'
    },
    {
      id: '7',
      kpiName: '市场需求变动率（结构）',
      targetClass: '年度目标',
      kpiUnit: '%',
      personLiable: '李丽萍',
      current: '2025 年',
      pre: '2024 年',
      currentTarget: '≤20%',
      precurrentTarget: '≤25%',
      targetChange: '-5%',
      currentActuall: '22%',
      preActuall: '27%',
      ActuallChange: '-5%'
    },
    {
      id: '8',
      kpiName: '产成品周转天数',
      targetClass: '年度目标',
      kpiUnit: '天',
      personLiable: '李淑华',
      current: '2025 年',
      pre: '2024 年',
      currentTarget: '≤35 天',
      precurrentTarget: '≤40 天',
      targetChange: '-5 天',
      currentActuall: '38 天',
      preActuall: '43 天',
      ActuallChange: '-5 天'
    },
    {
      id: '9',
      kpiName: '周 AATP 订单转化率',
      targetClass: '年度目标',
      kpiUnit: '%',
      personLiable: '李素芳',
      current: '2025 年',
      pre: '2024 年',
      currentTarget: '≥75%',
      precurrentTarget: '≥70%',
      targetChange: '5%',
      currentActuall: '72%',
      preActuall: '67%',
      ActuallChange: '5%'
    },
    {
      id: '10',
      kpiName: '市场需求满足率',
      targetClass: '年度目标',
      kpiUnit: '%',
      personLiable: '刘凤兰',
      current: '2025 年',
      pre: '2024 年',
      currentTarget: '≥95%',
      precurrentTarget: '≥92%',
      targetChange: '3%',
      currentActuall: '92%',
      preActuall: '89%',
      ActuallChange: '3%'
    }
  ]
}
