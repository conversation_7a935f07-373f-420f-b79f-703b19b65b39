<script setup>
import Sortable from 'sortablejs'
defineOptions({ name: 'sortComponent' })
const list = [
  {
    id: 'flfj',
    name: '大客户分类分级（1）'
  },
  {
    id: 'gxgl',
    name: '大客户关系管理（2）'
  },
  {
    id: 'jzwj',
    name: '大客户价值挖掘（3）'
  },
  {
    id: 'jxgl',
    name: '大客户绩效管理（4）'
  }
]
const sortList = ref([
  {
    id: 'zycd',
    title: '重要程度排序',
    tip: '请基于项目价值、影响、必要性、长期意义、对管理提升的贡献等方面，在您选择的项目中进行排序（长按上下拖动可更改排序）',
    list: JSON.parse(JSON.stringify(list))
  },
  {
    id: 'jjcd',
    title: '紧急程度排序',
    tip: '请重点从时间维度、时效性等方面，做出紧急度的排序，即：在有限时间内想做出改善的顺序 （长按上下拖动可更改排序）',
    list: JSON.parse(JSON.stringify(list))
  },
  {
    id: 'sswbd',
    title: '实施完备度排序',
    tip: '请从计划制定、资源准备、风险应对等方面，在您选择的项目中进行排序，即：开展事项基础比较成熟 （长按上下拖动可更改排序）',
    list: JSON.parse(JSON.stringify(list))
  },
  {
    id: 'ssgzl',
    title: '实施工作量排序',
    tip: '请从从人员投入、时间投入、成本投入、复杂程度等方面考量，在您选择的项目中进行排序 （长按上下拖动可更改排序）',
    list: JSON.parse(JSON.stringify(list))
  }
])

const initSortable = id => {
  const el = document.getElementById(id)
  // 创建拖拽实例
  Sortable.create(el, {
    animation: 150,
    handle: '.sort-list',
    dragClass: 'sort-active',
    chosenClass: 'sort-active',
    dataIdAttr: 'id',
    // 结束拖动事件
    onEnd: p => {
      let { newIndex, oldIndex } = p
      let index = p.target.id.split('-')[1]
      console.log(index)

      //拖动后的操作
      const curr = JSON.parse(JSON.stringify(sortList.value[index].list[oldIndex]))
      nextTick(() => {
        sortList.value[index].list.splice(oldIndex, 1)
        sortList.value[index].list.splice(newIndex, 0, curr)
      })
    }
  })
}
onMounted(() => {
  // 初始化拖拽实例
  for (let i = 0; i < sortList.value.length; i++) {
    initSortable(sortList.value[i].id + '-' + i)
  }
})

const dragEnter = item => {
  console.log(item, 'dragEnter')
}
</script>
<template>
  <div class="eval-answer-content">
    <div class="tip">提示：最后请对所有您参与的能力模块，进行相关排序</div>
    <div class="sort-box">
      <div class="sort-item" v-for="(item, index) in sortList" :key="item.id">
        <div class="sort-title">{{ item.title }}</div>
        <div class="sort-tip">{{ item.tip }}</div>
        <div class="sort-list" :id="item.id + '-' + index">
          <div class="list sort-list" v-for="(list, index) in item.list" :key="list.id">
            <span class="index">{{ index + 1 }}</span>
            <span class="name">{{ list.name }}</span>
            <SvgIcon class="icon" name="drag-icon" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.eval-answer-content {
  .module-name {
    font-weight: 600;
    font-size: 16px;
    color: #333333;
    line-height: 16px;
    margin-bottom: 10px;
  }
  .tip {
    line-height: 35px;
    background: #eff4f9;
    border-radius: 5px 5px 5px 5px;
    font-size: 14px;
    color: #40a0ff;
    padding-left: 14px;
    margin-bottom: 14px;
  }
  .sort-box {
    display: flex;
    align-items: stretch;
    justify-content: flex-start;
    gap: 20px;
    .sort-item {
      flex: 1;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      padding: 20px;
      .sort-title {
        font-weight: 600;
        font-size: 16px;
        color: #3d3d3d;
        line-height: 16px;
        margin-bottom: 10px;
      }
      .sort-tip {
        font-size: 14px;
        color: #666666;
        line-height: 21px;
        margin-bottom: 20px;
      }
      .sort-list {
        background: #ffffff;
        box-shadow: 0px 0px 6px 0px rgba(190, 201, 209, 0.3);
        border-radius: 4px 4px 4px 4px;
        font-size: 14px;
        color: #333333;
        .list {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          line-height: 40px;
          padding: 0 10px;
          border-bottom: 1px solid #eaeaea;
          font-size: 16px;
          cursor: move;
          &:last-of-type {
            border-bottom: none;
          }
          .index {
            font-weight: 600;
            font-size: 14px;
            color: #53b8ff;
            margin-right: 8px;
          }
          .name {
          }
          .icon {
            margin-left: auto;
          }
        }
      }
    }
  }
}
</style>
