<template>
    <div class="personnel_performance_matrix_wrap">
        <div class="page_third_title">分析图</div>
        <div class="main">
            <div class="matrix clearfix marginB_16">
                <div class="matrix_chart">
                    <talent-classify-matrix
                        :kpiRankOption="kpiRankOption"
                        :competenceRankOptions="competenceRankOptions"
                        :talentData="talentData"
                    ></talent-classify-matrix>
                    <!-- <div class="matrix_head">
                        <div class="title">绩效指标</div>
                        <div class="flex_row_start border">
                            <div class="item">后20%</div>
                            <div class="item">20%~80%</div>
                            <div class="item">前20%</div>
                        </div>
                       
                    </div>
                    <div class="clearfix">
                        <div class="matrix_aside">
                            <div class="matrix_aside_head flex_row_start">
                                <div class="title">核心能力</div>
                                <div class="flex_col_start border">
                                    <div class="item">前20%</div>
                                    <div class="item">20%~80%</div>
                                    <div class="item">后20%</div>
                                </div>
                            </div>
                        </div>
                        <div class="matrix_main">
                            <div
                                class="item"
                                :class="'level_' + item.name"
                                v-for="item in matrixData"
                            >
                                <div class="item_key flex_row_between">
                                    <span>{{ textObj[item.name] }}</span>
                                    <span>{{ item.name }}</span>
                                </div>
                                <div class="item_num" v-if="item.value > 0">


                                    {{ item.value }}位
                                </div>
                            </div>
                      
                        </div>
                    </div> -->
                </div>
                <!-- <div class="matrix_explain">
                    <div class="matrix_explain_title">矩阵说明</div>
                    <div class="matrix_explain_item">
                        <div class="line level_1"></div>
                        <div class="title">关键人才</div>
                        <div class="explain">
                            在绩效指标和能力两项上拥有很高的评价
                        </div>
                    </div>
                    <div class="matrix_explain_item">
                        <div class="line level_2"></div>
                        <div class="title">绩效标兵</div>
                        <div class="explain">
                            在绩效指标和能力两项上拥有很高的评价
                        </div>
                    </div>
                    <div class="matrix_explain_item">
                        <div class="line level_3"></div>
                        <div class="title">一般人才</div>
                        <div class="explain">
                            能力考核得分较高，绩效指标考核得分较低
                        </div>
                    </div>
                    <div class="matrix_explain_item">
                        <div class="line level_4"></div>
                        <div class="title">一般人才</div>
                        <div class="explain">
                            能力考核得分较低，绩效指标考核得分较高
                        </div>
                    </div>
                    <div class="matrix_explain_item">
                        <div class="line level_5"></div>
                        <div class="title">绩效不良者</div>
                        <div class="explain">
                            在绩效指标和能力两项上所获评价较低
                        </div>
                    </div>
                </div>
                <div class="matrix_explain plan">
                    <div class="matrix_explain_title">解决方案</div>
                    <div class="matrix_explain_item">
                        <div class="line"></div>
                        <ul class="text">
                            <li>应用关键人才管理方案</li>
                            <li>强化绩效与变动工资挂钩</li>
                        </ul>
                    </div>
                    <div class="matrix_explain_item">
                        <div class="line"></div>
                        <ul class="text">
                            <li>需要进一步绩效强化</li>
                            <li>提供更高水平的绩效辅导与个人发展</li>
                        </ul>
                    </div>
                    <div class="matrix_explain_item">
                        <div class="line"></div>
                        <ul class="text">
                            <li>改善下一年度绩效指标设计</li>
                            <li>考虑岗位轮换</li>
                        </ul>
                    </div>
                    <div class="matrix_explain_item">
                        <div class="line"></div>
                        <ul class="text">
                            <li>强化能力开发项目</li>
                            <li>强化个人发展计划</li>
                        </ul>
                    </div>
                    <div class="matrix_explain_item">
                        <div class="line"></div>
                        <ul class="text">
                            <li>绩效警告</li>
                            <li>解除合同</li>
                        </ul>
                    </div>
                </div> -->
            </div>
            <table-component
                :tableData="tableData"
                :needIndex="true"
                :loading="loading"
                @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange"
            ></table-component>
        </div>
    </div>
</template>
 
<script>
    import {
        lowerMeritsAbilityInfo,
        lowerMeritsAbilityInfoTable,
    } from "../../../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    import talentClassifyMatrix from "@/components/common/talentClassifyMatrix";
    const textObj = {
        AA: "明星人才",
        AB: "核心人才",
        AC: "关注人才",
        BA: "核心人才",
        BB: "骨干人才",
        BC: "关注人才",
        CA: "待提升人才",
        CB: "待提升人才",
        CC: "待优化人才",
    };
    export default {
        name: "personnelPerformanceMatrix",
        props: ["orgCode", "evalId"],
        components: {
            tableComponent,
            talentClassifyMatrix,
        },
        data() {
            return {
                currPage: 1,
                pageSize: 10,
                total: 0,
                loading: false,
                matrixData: [],
                kpiRankOption: [],
                competenceRankOptions: [],
                talentData: {},
                textObj: textObj,
                tableData: {
                    columns: [
                        {
                            label: "部门",
                            prop: "org_name",
                        },
                        {
                            label: "岗位",
                            prop: "post_name",
                        },
                        {
                            label: "姓名",
                            prop: "object_name",
                        },
                        {
                            label: "绩效",
                            prop: "kpi_percentile",
                        },
                        {
                            label: "能力",
                            prop: "score_percentile",
                        },

                        {
                            label: "人才区分",
                            prop: "talent_type",
                        },
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10,
                    },
                },
            };
        },
        created() {},
        watch: {
            orgCode: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                        this.getTableData();
                        this.getDocFun();
                    }
                },
            },
            evalId: {
                immediate: true, // 首次加载的时候执行函数
                deep: true,
                handler(val) {
                    if (val) {
                        this.getData();
                        this.getTableData();
                        this.getDocFun();
                    }
                },
            },
        },
        methods: {
            getData() {
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                };
                lowerMeritsAbilityInfo(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        // this.matrixData = res.data.kpi_score;
                        this.talentData = res.data;
                    }
                });
            },
            handleSizeChange(size) {
                this.pageSize = size;
                this.getTableData();
            },
            handleCurrentChange(current) {
                this.currPage = current;
                this.getTableData();
            },
            getDocFun() {
                let docList = ["KPI_RANK", "COMPETENCE_RANK"];
                this.$getDocList(docList).then((res) => {
                    console.log(res);
                    this.kpiRankOption = this.$util
                        .deepClone(res.KPI_RANK)
                        .reverse();
                    this.competenceRankOptions = res.COMPETENCE_RANK;
                });
            },
            getTableData() {
                this.loading = true;
                let params = {
                    current: this.currPage,
                    size: this.pageSize,
                    evalId: this.evalId,
                    orgCode: this.orgCode,
                    type: "",
                };
                lowerMeritsAbilityInfoTable(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$set(this.tableData, "data", res.data);
                        this.$set(this.tableData, "page", res.page);
                        this.loading = false;
                    } else {
                        this.loading = false;
                    }
                });
            },
            calcScoreKey(val) {
                switch (val) {
                    case val < 20:
                        return "A";
                        break;
                    case val >= 20 && val < 80:
                        return "B";
                        break;
                    default:
                        return "C";
                        break;
                }
            },
            calcKpiKey(val) {},
        },
    };
</script>
 
<style scoped lang="scss">
    .personnel_performance_matrix_wrap {
        .matrix_chart {
            float: left;
            // width: 500px;
            margin-right: 6px;
            .matrix_head {
                width: 490px;
                // padding-left: 45px;
                // margin-left: 45px;
                text-align: center;
                line-height: 30px;
                .title {
                    height: 30px;
                    background: #fbfbfb;
                    padding-left: 90px;
                }
                .flex_row_start {
                    height: 30px;
                    margin-left: 90px;
                    &.border {
                        border-bottom: 1px solid #f6f6f6;
                    }
                }
                .item {
                    flex: 1;
                }
            }
            .matrix_aside {
                float: left;
                width: 90px;
                height: 350px;
                text-align: center;
                .matrix_aside_head {
                    height: 100%;
                }
                .title {
                    height: calc(100% + 50px);
                    padding: 185px 5px 0;
                    width: 30px;
                    background: #fbfbfb;
                    margin-top: -50px;
                }
                .flex_col_start {
                    height: 100%;
                    width: 60px;
                    &.border {
                        border-right: 1px solid #f6f6f6;
                    }
                }
                .item {
                    flex: 1;
                    line-height: 70px;
                }
            }
            .matrix_main {
                overflow: hidden;
                .item {
                    width: 135px;
                    height: 116px;
                    float: left;
                    margin: 0 1px 1px 0;
                    padding: 8px;
                    color: #fff;
                    font-size: 14px;
                    .item_num {
                        margin-top: 50px;
                        color: #dd1a11;
                    }
                    &.level_AA {
                        background-color: #e28d80;
                    }
                    &.level_AB,
                    &.level_BA {
                        background-color: #719dd5;
                    }
                    &.level_AC,
                    &.level_BC,
                    &.level_CB,
                    &.level_CA {
                        background-color: #bed269;
                    }
                    &.level_BB {
                        background-color: #a3d0f3;
                    }
                    &.level_CC {
                        background-color: #dddee3;
                    }
                }
            }
        }
        .matrix_explain {
            float: left;
            width: 190px;
            &.plan {
                margin-left: 10px;
                .line {
                    background-color: #fff;
                }
                .matrix_explain_item {
                    min-height: 72px;
                }
            }
            .matrix_explain_title {
                padding: 0 5px;
                text-align: center;
                vertical-align: center;
                display: flex;
                align-items: center;
                background: #fbfbfb;
                font-size: 16px;
                font-weight: bold;
                line-height: 34px;
            }
            .matrix_explain_item {
                background-color: #fbfbfb;
                margin-bottom: 8px;
                &:last-of-type {
                    margin-bottom: 0;
                }
                .line {
                    width: 100%;
                    height: 6px;
                    margin-bottom: 5px;
                    background-color: #fff;
                    &.level_1 {
                        background-color: #e28d80;
                    }
                    &.level_2 {
                        background-color: #719dd5;
                    }
                    &.level_3 {
                        background-color: #bed269;
                    }
                    &.level_4 {
                        background-color: #a3d0f3;
                    }
                    &.level_5 {
                        background-color: #dddee3;
                    }
                }
                .title {
                    font-size: 14px;
                    font-weight: bold;
                    padding-left: 16px;
                }
                .explain {
                    padding-left: 16px;
                    font-size: 14px;
                    color: #555c71;
                    line-height: 22px;
                }
                .text {
                    padding-left: 16px;
                }
                li {
                    list-style: outside;
                }
            }
        }
    }
</style>