<template>
  <div class="post_relationship_map_wrap clearfix bg_write">
    <div class="page_main_title">岗位关系图谱</div>
    <div class="page_section post_relationship_map_aside fl">
      <div class="page_second_title">筛选</div>
      <div class="page_section_aside org_chart_aside">
        <div class="aside_tree_title">
          <div>组织分类</div>
        </div>
        <div class="aside_tree_list">
          <tree-comp-radio
            :treeData="treeData"
            :defaultCheckedKeys="defaultCheckedKeys"
            @clickCallback="clickCallback"
          ></tree-comp-radio>
        </div>
      </div>
    </div>
    <div class="page_section post_relationship_map_main">
      <div class="page_second_title">关系图谱</div>
      <div class="post_relationship_map_center clearfix" v-if="mapShowSign">
        <postMap :postData="currPost"></postMap>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { getOrgDeptTree, getPostRelationShip } from '../../request/api'
import postMap from './components/postRelationChart.vue'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio.vue'

const userStore = useUserStore()
const treeData = ref([])
const orgCode = ref('')
const currPost = ref({})
const mapShowSign = ref(false)
const defaultCheckedKeys = ref([])

const companyId = computed(() => userStore.userInfo.companyId)

function getOrgTreeFun() {
  getOrgDeptTree({ companyId: companyId.value }).then(res => {
    if (res.code == 200) {
      treeData.value = res.data.length > 0 ? res.data : []
    } else {
      treeData.value = []
      orgCode.value = ''
    }
  })
}

function clickCallback(val, isLastNode) {
  orgCode.value = ''
  currPost.value = {}
  mapShowSign.value = false
  if (val) {
    orgCode.value = val
    getPostRelationShipFun()
  }
}

function getPostRelationShipFun() {
  getPostRelationShip({ orgCode: orgCode.value }).then(res => {
    currPost.value = {}
    if (res.code == 200) {
      currPost.value = {
        postName: res.data.postName,
        postType: res.data.postTypeName,
        name: res.data.userNameList ? res.data.userNameList.join(',') : '',
        personnelNum: res.data.userNum,
        supPost:
          JSON.stringify(res.data.parent) == '{}'
            ? ''
            : {
                postName: res.data.parent.postName,
                postType: res.data.parent.postTypeName,
                name: res.data.parent.userNameList ? res.data.parent.userNameList.join(',') : '',
                personnelNum: res.data.parent.userNum
              },
        subPost:
          res.data.children.length == 0
            ? ''
            : res.data.children.map(item => ({
                postName: item.postName,
                postType: item.postTypeName,
                name: item.userNameList ? item.userNameList.join(',') : '',
                personnelNum: item.userNum
              })),
        common:
          res.data.peer.length == 0
            ? ''
            : res.data.peer.map(item => ({
                postName: item.postName,
                postType: item.postTypeName,
                name: item.userNameList ? item.userNameList.join(',') : '',
                personnelNum: item.userNum
              })),
        team:
          res.data.coop.length == 0
            ? ''
            : res.data.coop.map(item => ({
                postName: item.postName,
                postType: item.postTypeName,
                name: item.userNameList ? item.userNameList.join(',') : '',
                personnelNum: item.userNum
              })),
        outside:
          res.data.ExternalCoop.length == 0
            ? ''
            : res.data.ExternalCoop.map(item => ({
                postName: item.postName,
                postType: '对外'
              }))
      }
      mapShowSign.value = true
    } else {
      currPost.value = {}
      mapShowSign.value = false
    }
  })
}

watch(
  companyId,
  (val, oldVal) => {
    if (val) {
      getOrgTreeFun()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.post_relationship_map_wrap {
}

.post_relationship_map_aside {
  margin-right: 16px;
}
.page_main_aside_filter {
  width: 280px;
  height: auto;
  margin: 0 -16px;
}
.post_relationship_map_main {
  overflow: hidden;
}
</style>
