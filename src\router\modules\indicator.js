// 指标透视罗盘
import Layout from '@/layout/index.vue'
const indicator = [
  {
    path: '/indicator',
    component: Layout,
    redirect: '/indicator/home',
    meta: {
      title: '指标透视罗盘'
    },
    children: [
      {
        path: '/indicator/home',
        meta: {
          // title: '指标透视罗盘'
        },
        component: () => import('@/views/indicator/home.vue')
      },
      {
        path: '/indicator/libraryRefer',
        meta: {
          title: '参考指标库'
        },
        redirect: '/indicator/libraryRefer/typical',
        component: () => import('@/views/indicator/libraryRefer/index.vue'),
        children: [
          {
            path: 'typical',
            component: () => import('@/views/indicator/libraryRefer/hydx.vue')
          },
          {
            path: 'undertake',
            component: () => import('@/views/indicator/libraryRefer/dxgw.vue')
          },
          {
            path: 'ability',
            component: () => import('@/views/indicator/libraryRefer/zbnl.vue')
          }
        ]
      },
      {
        path: '/indicator/libraryTending',
        meta: {
          title: '指标库维护'
        },
        component: () => import('@/views/indicator/libraryTending/index.vue')
      },
      {
        path: '/indicator/diagnosticAnalysis',
        meta: {
          title: '指标诊断与根因分析'
        },
        component: () => import('@/views/indicator/diagnosticAnalysis/index.vue')
      },
      {
        path: '/indicator/trendRisk',
        meta: {
          title: '指标趋势预测与风险预警'
        },
        component: () => import('@/views/indicator/trendRisk/index.vue')
      },
      {
        path: '/indicator/indicatorImprove',
        meta: {
          title: '指标改善效果追踪'
        },
        redirect: '/indicator/indicatorImprove/zzIndex',
        component: () => import('@/views/indicator/indicatorImprove/index.vue'),
        children: [
          {
            path: '/indicator/indicatorImprove/zzIndex',
            component: () => import('@/views/indicator/indicatorImprove/organization/index.vue')
          },
          {
            path: '/indicator/indicatorImprove/personnel',
            component: () => import('@/views/indicator/indicatorImprove/personnel/index.vue')
          },
          {
            path: '/indicator/indicatorImprove/project',
            component: () => import('@/views/indicator/indicatorImprove/project/index.vue')
          }
        ]
      },
      {
        path: '/indicator/targetTending',
        meta: {
          title: '目标值与实际值维护'
        },
        component: () => import('@/views/indicator/targetTending/index.vue')
      },
      {
        path: '/indicator/benchmarking',
        meta: {
          title: '指标智能对标'
        },
        redirect: '/indicator/benchmarking/know',
        component: () => import('@/views/indicator/benchmarking/index.vue'),
        children: [
          {
            path: 'know',
            component: () => import('@/views/indicator/benchmarking/know/index.vue')
          },
          {
            path: 'project',
            component: () => import('@/views/indicator/benchmarking/project/index.vue')
          },
          {
            path: 'add',
            component: () => import('@/views/indicator/benchmarking/add/index.vue')
          }
        ]
      },
      {
        path: '/indicator/improveTasks',
        meta: {
          title: '指标改善任务一览'
        },
        component: () => import('@/views/indicator/improveTasks/index.vue')
      }
    ]
  }
]

export default indicator
