<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">活动类型</div>
          <div class="content_item_content" id="activityType"></div>
        </div>
      </div>
      <div class="content_item el-col-12">
        <div class="content_item_main">
          <div class="content_item_title">各部门人员效率</div>
          <div class="content_item_content" id="orgEfficiency"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title">各岗位人员效率</div>
          <div class="content_item_content" id="postEfficiency"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { efficiencyActivity } from '../../../../../request/api.js'
import asideFilterBar from '../../asideFilterBar.vue'

const route = useRoute()
const enqId = ref(route.query.enqId)
const jobClassCode = ref('')
const orgCode = ref('')

const activityType = reactive({
  data: []
})

const orgEfficiency = reactive({
  data: []
})

const postEfficiency = reactive({
  legend: [
    {
      legendName: '部门岗位',
      legendKey: 'value'
    },
    {
      legendName: '高绩效岗位',
      legendKey: 'value1'
    }
  ],
  data: []
})

const filterData = ref({})

const initChart = () => {
  echartsRenderPage('activityType', 'Rose', '300', '220', activityType)
  echartsRenderPage('orgEfficiency', 'YBar', '330', '220', orgEfficiency)
  echartsRenderPage('postEfficiency', 'XBar', '700', '220', postEfficiency)
}

const efficiencyActivityFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    const res = await efficiencyActivity(params)
    if (res.code == 200) {
      const data = res.data
      activityType.data = window.$util.addPercentSign(data.activityType, 'value')
      orgEfficiency.data = data.orgEfficiency
      postEfficiency.data = data.postEfficiency
      initChart()
    }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  efficiencyActivityFun()
}

onMounted(() => {
  filterData.value = route.attrs.filterData
  efficiencyActivityFun()
})
</script>

<style scoped lang="scss"></style>
