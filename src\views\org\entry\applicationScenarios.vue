<script setup>
const navList = ref([
  {
    title: "岗位专业能力评估",
    tips: "场景1",
    target:
      "基础性匹配，确保“人岗匹配”最优化，直接减少能力错位导致的效能损耗。这是效能提升的起点，影响所有岗位的生产力。管理意义强调减少效能损耗，且评估重点（胜任力模型清晰度）是人才管理的核心。",
    bg: "新业务落地或岗位职能调整，需重新定义岗位能力要求但缺乏科学评估工具。",
  },

  {
    title: "高潜力人才培养",
    tips: "场景2",
    target:
      "长期驱动力，系统识别和发展未来领导者，解决领导梯队断层问题，驱动组织可持续成长。管理意义直接关联组织效能提升（确保领导力连续性），且评估重点（潜力测评和盘点）是人才发展的关键杠杆。 ",
    bg: "领导梯队断层，关键岗位后继无人，影响组织可持续发展。",
  },
  {
    title: "关键岗位人才储备评估",
    tips: "场景3",
    target:
      "风险防控，建立接班人计划，保障核心岗位能力传承，降低人才断层风险。管理意义强调组织连续性，这对业务稳定性至关重要，且评估重点（储备人才成熟度）能预防突发流失带来的效能骤降。",
    bg: "核心岗位依赖个别骨干，缺乏系统化储备机制，面临人才流失风险。",
  },
  {
    title: "激励机制优化",
    tips: "场景4",
    target:
      "  动力引擎，设计效能导向的激励（如人效奖金），激活员工潜能，提升人才价值。管理意义直接提升绩效和效率，且评估重点（激励与绩效关联）是维持员工动力的核心，比其他激励相关场景（如15）更全面。 ",
    bg: "激励体系单一，无法激发人才潜力。",
  },
  {
    title: "领导力效能评估",
    tips: "场景5",
    target:
      "  通过360度反馈等评估领导力，提升团队执行力，确保战略落地。管理意义直接增强组织效能（领导力是效能的关键驱动力），且评估重点（管理人才质量）影响整个组织的决策和绩效。 ",
    bg: "管理者能力不足，团队绩效低下，影响组织目标达成。",
  },
  {
    title: "人才保留策略",
    tips: "场景6",
    target:
      "针对核心人才流失问题，实施保留措施（如基于测评的差异化策略），保持组织知识资产和业务稳定，减少招聘成本并维持高效能，（覆盖原因分析和策略制定）。 ",
    bg: "核心人才流失严重，知识断层，业务稳定性受威胁。",
  },
  // {
  //   title: "",
  //   tips: "场景7",
  //   target: "",
  //   bg: "",
  // },
  // {
  //   title: "",
  //   tips: "场景8",
  //   target: "",
  //   bg: "",
  // },
  // {
  //   title: "",
  //   tips: "场景9",
  //   target: "",
  //   bg: "",
  // },
]);
const openAi = inject("openAi");
</script>
<template>
  <div class="applicationScenarios_wrap">
    <div class="page-title-line">典型应用场景</div>
    <div class="applicationScenarios_main justify-start">
      <div class="item_wrap" v-for="item in navList">
        <div class="title_wrap justify-start">
          <div class="tips">{{ item.tips }}</div>
          <div class="title">{{ item.title }}</div>
        </div>
        <div class="main_wrap">
          <span class="title">应用目的：</span>
          <span class="info">{{ item.target }}</span>
        </div>
        <div class="main_wrap">
          <span class="title">应用背景：</span>
          <span class="info">{{ item.bg }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.applicationScenarios_wrap {
  .justify-start {
    display: flex;
    justify-content: flex-start;
  }
  .justify-between {
    display: flex;
    justify-content: space-between;
  }
  .applicationScenarios_main {
    flex-wrap: wrap;
    margin-left: -10px;
  }
  .item_wrap {
    margin: 0 10px 20px;
    padding: 20px;
    width: 30%;
    background: url("@/assets/imgs/coreBusinessNav/img_01.png") no-repeat center;
    background-size: 100% 100%;
    font-size: 14px;
    cursor: pointer;
    .title_wrap {
      .title {
        font-size: 16px;
        font-weight: 600;
      }
      .tips {
        margin-right: 6px;
        padding: 0px 12px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        color: #fff;
        border-radius: 3px 3px 3px 3px;
        background: #53a9f9;
        cursor: pointer;
      }
    }
    .main_wrap {
      margin-top: 15px;
      .title {
        font-weight: 600;
      }
      .info {
        color: #666666;
      }
    }
  }
  .item_wrap:hover {
    border: 1px solid #40a0ff;
    box-shadow: 0px 4px 10px 0px #cad5e1;
    border-radius: 8px 8px 8px 8px;
  }
}
</style>
