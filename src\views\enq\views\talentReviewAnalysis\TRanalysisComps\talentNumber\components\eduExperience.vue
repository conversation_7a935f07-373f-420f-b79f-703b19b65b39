<template>
  <div class="talent_main">
    <div class="aside_filter_wrap">
      <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
    </div>
    <div class="talent_number_content page_section flex_row_wrap_start">
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">学历分布</div>
          <div class="content_item_content" id="edu"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">学位分布</div>
          <div class="content_item_content" id="degree"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">专业分布</div>
          <div class="content_item_content" id="major"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">学校类型分布</div>
          <div class="content_item_content" id="school_type"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">学校层次分布</div>
          <div class="content_item_content" id="school_level"></div>
        </div>
      </div>
      <div class="content_item el-col-8">
        <div class="content_item_main">
          <div class="content_item_title">学校分布</div>
          <div class="content_item_content" id="school"></div>
        </div>
      </div>
      <div class="content_item el-col-24">
        <div class="content_item_main">
          <div class="content_item_title clearfix">
            教育经历分布明细
            <el-button class="fr" type="primary" @click="exportDataFn">导出</el-button>
          </div>
          <div class="content_item_content">
            <tableComponet
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
              :tableData="tableData"
              :needIndex="true"
            ></tableComponet>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import tableComponet from '@/components/talent/tableComps/tableComponent.vue'
import { echartsRenderPage } from '../../../../../../../../public/js/echartsimg/echartsToImg.js'
import { exportData } from '../../../../../request/api'
import asideFilterBar from '../../asideFilterBar'

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')

const eduData = reactive({
  data: []
})

const degreeData = reactive({
  data: []
})

const majorData = reactive({
  data: []
})

const schoolTypeData = reactive({
  data: []
})

const schoolLevelData = reactive({
  data: []
})

const schoolData = reactive({
  data: []
})

const filterData = ref([])
const page = ref(1)
const size = ref(10)

const tableData = reactive({
  columns: [
    {
      label: '一级组织',
      prop: 'onLevelName'
    },
    {
      label: '二级组织',
      prop: 'twoLevelName'
    },
    {
      label: '三级组织',
      prop: 'threeLevelName'
    },
    {
      label: '四级组织',
      prop: 'fourLevelName'
    },
    {
      label: '姓名',
      prop: 'userName'
    },
    {
      label: '岗位',
      prop: 'postName'
    },
    {
      label: '学校名称',
      prop: 'schoolName'
    },
    {
      label: '开始日期',
      prop: 'beginDate'
    },
    {
      label: '结束日期',
      prop: 'endDate'
    },
    {
      label: '学历',
      prop: 'eduName'
    },
    {
      label: '学位',
      prop: 'degreeName'
    },
    {
      label: '专业',
      prop: 'majorName'
    },
    {
      label: '学校类型',
      prop: 'schoolTypeName'
    },
    {
      label: '学校层次',
      prop: 'schoolLevelName'
    }
  ],
  data: [],
  page: {
    current: 1,
    total: 0,
    size: 10
  }
})

const initChart = () => {
  echartsRenderPage('edu', 'YBar', '230', '200', eduData)
  echartsRenderPage('degree', 'YBar', '230', '200', degreeData)
  echartsRenderPage('major', 'YBar', '230', '200', majorData)
  echartsRenderPage('school_type', 'Ring', '230', '200', schoolTypeData)
  echartsRenderPage('school_level', 'Ring', '230', '200', schoolLevelData)
  echartsRenderPage('school', 'YBar', '230', '200', schoolData)
}

const eduExperienceFun = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value
    }
    // const res = await eduExperience(params)
    // if (res.code == 200) {
    //   const data = res.data
    //   eduData.data = window.$util.addPercentSign(data.edu, 'value')
    //   degreeData.data = window.$util.addPercentSign(data.degree, 'value')
    //   majorData.data = window.$util.addPercentSign(data.major, 'value')
    //   schoolTypeData.data = window.$util.addPercentSign(data.schoolType, 'value')
    //   schoolLevelData.data = window.$util.addPercentSign(data.schoolLevel, 'value')
    //   schoolData.data = window.$util.addPercentSign(data.school, 'value')
    //   initChart()
    // }
  } catch (error) {
    console.error(error)
  }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
  jobClassCode.value = jobClassCodeVal
  orgCode.value = orgCodeVal
  page.value = 1
  eduExperienceFun()
  getTableData()
}

const handleCurrentChange = pageVal => {
  page.value = pageVal
  getTableData()
}

const handleSizeChange = sizeVal => {
  size.value = sizeVal
  getTableData()
}

const getTableData = async () => {
  try {
    const params = {
      enqId: enqId.value,
      jobClassCode: jobClassCode.value,
      orgCode: orgCode.value,
      current: page.value,
      size: size.value
    }
    const res = await queryEduExpList(params)
    if (res.code == 200) {
      tableData.data = res.data
      tableData.page = res.page
    }
  } catch (error) {
    console.error(error)
  }
}

const exportDataFn = async () => {
  try {
    const params = {
      enqId: enqId.value,
      orgCode: orgCode.value,
      type: 'e'
    }
    const res = await exportData(params)
    window.$exportDownloadFile(res.data, '教育经历分布明细')
  } catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  enqId.value = route.query.enqId
  filterData.value = route.attrs.filterData
  eduExperienceFun()
  getTableData()
})
</script>

<style scoped lang="scss"></style>
