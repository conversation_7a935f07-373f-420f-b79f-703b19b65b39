<script setup>
import SectionTab from "../../../components/sectionTab.vue";
import SectionTabV from "../../../components/sectionTabVertical.vue";
import Table from "../../../components/table.vue";
import Tree from "@/components/tree/index.vue";
// import ReportInfo from "./reportInfo.vue";
const emits = defineEmits(["sign"]);
const sectionTabVCheckSign = ref(1);
const reportList = ref([
  {
    date: "20240201",
    name: "战略管理人才盘点报告",
    show: false,
  },
  {
    date: "20250201",
    name: "战略管理人才专业能力盘点报告",
    show: false,
  },
]);
const input = ref("");
const orgList = ref([
  {
    name: "海信冰箱公司",
    info: "2024-09-25",
  },
  {
    name: "海信冰箱公司",
    info: "2024-09-25",
  },
  {
    name: "海信冰箱公司",
    info: "2024-09-25",
  },
  {
    name: "海信冰箱公司",
    info: "2024-09-25",
  },
]);
const personList = ref([
  {
    name: "周腾飞",
    info: "采购部/副部长（主持工作）",
  },
  {
    name: "王培金",
    info: "采购部/副部长（主持工作）",
  },
  {
    name: "王可心",
    info: "采购部/采购资源管理",
  },
  {
    name: "孙华",
    info: "采购部/采购成本管理",
  },
]);
const pageInfo = ref({
  current: 1,
  size: 10,
  total: 100,
});
const reportType = ref();
const showReportInfoSign = ref(false);
const handleSizeChange = (s) => {};
const handleCurrentChange = (c) => {};
const showInfo = (i) => {
  reportList.value[i].show = !reportList.value[i].show;
};
const reportInfo = (c) => {
  emits("sign", c);
  // reportType.value = c;
  // showReportInfoSign.value = !showReportInfoSign.value;
};
const closeReportInfo = (c) => {
  if (c) {
    showReportInfoSign.value = false;
  }
};
</script>
<template>
  <div class="index_wrap inventoryReport_wrap">
    <div class="report_list" v-if="!showReportInfoSign">
      <div class="item_report_wrap" v-for="(item, index) in reportList">
        <div class="item_report_t justify-between">
          <div class="date_name">
            <span>{{ item.date }}</span>
            <span>{{ item.name }}</span>
          </div>
          <div class="btn" v-if="!item.show" @click="showInfo(index)">展开</div>
          <div class="btn" v-else @click="showInfo(index)">收起</div>
        </div>
        <div class="item_report_b justify-between" v-if="item.show">
          <div class="l_wrap">
            <div class="title">组织</div>
            <div class="tree_wrap">
              <Tree></Tree>
            </div>
          </div>
          <div class="r_wrap">
            <div class="section_w">
              <div class="title_wrap justify-between">
                <div>部门报告（4份）</div>
                <el-input v-model="input" placeholder="" />
              </div>
              <div class="repo_list_w org_report justify-between">
                <div
                  class="repo_item_w justify-between"
                  v-for="(item, index) in orgList"
                  @click="reportInfo('o')"
                >
                  <div class="">{{ index + 1 }}.{{ item.name }}</div>
                  <div class="">{{ item.info }}</div>
                </div>
                <!-- personList -->
              </div>
              <el-pagination
                :current-page="pageInfo.current"
                :page-size="pageInfo.size"
                :page-sizes="[10, 20, 50, 100]"
                :total="pageInfo.total"
                :small="small"
                background
                layout=" sizes, prev, pager, next,total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
            <div class="section_w">
              <div class="title_wrap justify-between">
                <div>个人报告（4份）</div>
                <el-input v-model="input" placeholder="" />
              </div>
              <div class="repo_list_w org_report justify-between">
                <div
                  class="repo_item_w justify-start"
                  v-for="(item, index) in personList"
                  @click="reportInfo('p')"
                >
                  <div class="name_w">{{ index + 1 }}.{{ item.name }}</div>
                  <div class="">{{ item.info }}</div>
                </div>
              </div>
              <el-pagination
                :current-page="pageInfo.current"
                :page-size="pageInfo.size"
                :page-sizes="[10, 20, 50, 100]"
                :total="pageInfo.total"
                :small="small"
                background
                layout=" sizes, prev, pager, next,total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- reportList -->
      <!-- <div class="main_r_wrap"></div> -->
    </div>
    <div v-else>
      <!-- <ReportInfo
        @closeReportInfo="closeReportInfo"
        :reportType="reportType"
      ></ReportInfo> -->
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.inventoryReport_wrap {
  .report_list {
    .item_report_wrap {
      padding: 20px;
      margin-bottom: 20px;
      background: #fff;
      box-shadow: 0px 0px 10px 0px rgba(170, 191, 212, 0.5);
      border-radius: 8px 8px 8px 8px;
      .item_report_t {
        padding: 33px 22px;
        background: linear-gradient(
          226deg,
          #ffffff 0%,
          #f3f9fd 54%,
          #ffffff 100%
        );
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #c6dbf3;
        .date_name {
          span {
            margin-right: 20px;
            display: inline-block;
          }
        }
      }
      .item_report_b {
        margin: 20px 0 0 0;
        .l_wrap {
          padding: 8px;
          margin-right: 10px;
          width: 230px;
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #c6dbf3;
          .title {
            margin: 0 auto;
            width: 210px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            font-size: 12px;
            background: #e3effa;
            color: #40a0ff;
            border-radius: 8px 8px 8px 8px;
          }
          .second_t {
            font-size: 12px;
            height: 40px;
            line-height: 40px;
            color: #40a0ff;
          }
        }
        .r_wrap {
          flex: 1;
          .section_w {
            margin-bottom: 20px;
            border-bottom: 1px solid #d8d8d8;
            .title_wrap {
              padding: 0 20px;
              height: 40px;
              background: #eff3f6;
              align-items: center;
              :deep .el-input {
                width: 228px;
                height: 27px;
                background: #ffffff;
                border-radius: 5px 5px 5px 5px;
              }
            }
            .repo_list_w {
              flex-wrap: wrap;
              .repo_item_w {
                align-items: center;
                margin-top: 10px;
                padding: 0 14px;
                width: 49%;
                height: 40px;
                background: #ffffff;
                border-radius: 3px 3px 3px 3px;
                border: 1px solid #d8d8d8;
                cursor: pointer;
                .name_w {
                  width: 140px;
                }
              }
              .repo_item_w:hover {
                color: #40a0ff;
                background: rgba(64, 160, 255, 0.1);
                border: 1px solid #40a0ff;
              }
              .act {
                color: #40a0ff;
                background: rgba(64, 160, 255, 0.1);
                border: 1px solid #40a0ff;
              }
            }
          }
          .section_w:last-child {
            border-bottom: 1px solid transparent;
          }
        }
      }
    }
  }
}
</style>
