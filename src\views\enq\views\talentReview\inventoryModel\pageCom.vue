<template>
  <div class="talent_review_report_center clearfix">
    <tabsDiffentPane
      :isDefaultTheme="true"
      :tabsData="tabsData"
      :tabsDefault="tabsPaneName"
      @tabsChange="tabsChange"
    ></tabsDiffentPane>
    <component ref="child" :is="tabsPaneComp[tabsPaneName]"></component>
  </div>
</template>
 
<script>
import tabsDiffentPane from "@/components/talent/tabsComps/tabsDiffentPane";
const compsObj = {
  evaluationItems: (resolve) => require(["./evaluationItems"], resolve),
  personnelMatching: (resolve) => require(["./personnelMatching"], resolve),
};
export default {
  name: "pageCom",
  props: ["tabsPanesign"],
  data() {
    return {
      tabsPaneName: "evaluationItems",
      tabsPaneComp: compsObj,
      tabsData: [
        {
          label: "评价项目",
          name: "evaluationItems",
        },
        {
          label: "人员匹配",
          name: "personnelMatching",
        },
      ],

      listData: [],
    };
  },
  components: {
    tabsDiffentPane,
  },
  computed: {
    curTabsPanesign: function () {
      return this.tabsPanesign;
    },
  },
  create() {},
  mounted() {
    console.log(this.tabsPanesign);
  },
  methods: {
    tabsChange(data) {
      this.tabsPaneName = data.name;
    },
  },
  watch: {
    curTabsPanesign: {
      deep: true,
      handler: function (val) {
        if (val) {
          this.tabsPaneName = "evaluationItems";
        }
      },
    },
  },
};
</script>
 
<style scoped lang="scss">
</style>