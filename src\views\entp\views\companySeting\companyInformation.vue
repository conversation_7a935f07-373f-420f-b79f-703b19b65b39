<template>
  <div class="company_information_wrap bg_write">
    <div class="page_main_title">企业信息设置</div>
    <div class="page_section">
      <div class="company_information_center from_wrap clearfix">
        <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="100px" label-position="left">
          <el-form-item label="企业名称" prop="companyName">
            <el-input v-model="ruleForm.companyName" />
          </el-form-item>
          <el-form-item label="企业简称">
            <el-input v-model="ruleForm.companyShortName" />
          </el-form-item>
          <el-form-item label="公司logo" prop="logoUrl">
            <el-upload
              class="avatar-uploader"
              :action="actionUrl"
              :show-file-list="false"
              :before-upload="beforeAvatarUpload"
              :auto-upload="false"
              :on-change="changeFile"
              ref="logoImg"
            >
              <img v-if="logoBase64" :src="logoBase64" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon">
                <Plus />
              </el-icon>
            </el-upload>
          </el-form-item>
          <el-form-item label="企业所在地" prop="regionCode">
            <el-cascader
              :options="cityInfo"
              v-model="ruleForm.regionCode"
              :change-on-select="true"
              :clearable="true"
              :filterable="true"
              :props="{
                expandTrigger: 'hover'
              }"
              @change="handleChange"
            />
          </el-form-item>
          <el-form-item label="企业网址">
            <el-input v-model="ruleForm.websiteUrl" />
          </el-form-item>
          <el-form-item label="企业简介">
            <el-input
              class="item_textarea"
              :autosize="{ minRows: 4, maxRows: 10 }"
              type="textarea"
              v-model="ruleForm.companyDesc"
            />
          </el-form-item>
          <el-form-item class="align_center">
            <el-button class="page_confirm_btn" type="primary" @click="onSubmit">确认</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { regionData } from 'element-china-area-data'
import { getCompany, createCompany, updateCompany, fileUpload, fileDownload } from '../../request/api'
import { getBase64 } from '@/utils/utils'
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const companyId = computed(() => userStore.userInfo.companyId)

// Refs
const ruleFormRef = ref(null)
const logoImg = ref(null)

// 响应式状态
const cityInfo = ref(regionData)
const ruleForm = reactive({
  companyName: '',
  companyShortName: '',
  logoUrl: '',
  regionCode: '',
  websiteUrl: '',
  companyDesc: ''
})

const logoBase64 = ref('')
const logoFile = ref({})
const uploadFlag = ref(false)
const actionUrl = '/edp/api/entp/attach/upload'

// 表单验证规则
const rules = {
  companyName: [{ required: true, message: '请输入企业名称' }]
}

// 方法定义
const onSubmit = async () => {
  if (!ruleFormRef.value) return

  const valid = await ruleFormRef.value.validate()
  if (valid) {
    if (uploadFlag.value) {
      try {
        const res = await uploadImgFun()
        ruleForm.logoUrl = res.id
        await toSubmitFun()
      } catch (error) {
        console.error('上传图片失败:', error)
        ElMessage.error('上传图片失败')
      }
    } else {
      await toSubmitFun()
    }
  }
}

const toSubmitFun = async () => {
  if (companyId.value) {
    await updateCompanyFun()
  } else {
    await createCompanyFun()
  }
}

const uploadImgFun = async () => {
  const formData = new FormData()
  formData.append('containerId', companyId.value)
  formData.append('containerName', 'company_info')
  formData.append('file', logoFile.value.raw)

  try {
    const res = await fileUpload(formData)
    if (res.code == 200) {
      return res.data
    } else {
      ElMessage.warning(res.msg)
      return null
    }
  } catch (error) {
    console.error('上传文件失败:', error)
    ElMessage.error('上传文件失败')
    return null
  }
}

const changeFile = async file => {
  uploadFlag.value = true
  logoFile.value = file
  try {
    const base64 = await getBase64(file.raw)
    logoBase64.value = base64
  } catch (error) {
    console.error('转换图片失败:', error)
    ElMessage.error('转换图片失败')
  }
}

const beforeAvatarUpload = file => {
  const isImg = file.type == 'image/jpeg' || file.type == 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImg) {
    ElMessage.error('上传图片只能是JPG或PNG格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
  }
  return isImg && isLt2M
}

const handleChange = code => {
  if (code && code.length > 0) {
    ruleForm.regionCode = code[code.length - 1]
  }
}

const getCompanyFun = async () => {
  try {
    const res = await getCompany({
      companyId: companyId.value
    })

    Object.assign(ruleForm, res)

    if (res.logoUrl) {
      await getImgFileStreamFun(res.logoUrl)
    } else {
      logoBase64.value = '/images/logo.png'
    }
  } catch (error) {
    console.error('获取公司信息失败:', error)
    ElMessage.error('获取公司信息失败')
  }
}

const getImgFileStreamFun = async id => {
  try {
    const res = await fileDownload({ id })
    if (res) {
      logoBase64.value = res
    }
  } catch (error) {
    console.error('获取图片失败:', error)
    ElMessage.error('获取图片失败')
  }
}

const createCompanyFun = async () => {
  try {
    const res = await createCompany(ruleForm)
    if (res.code == 200) {
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('创建公司失败:', error)
    ElMessage.error('创建公司失败')
  }
}

const updateCompanyFun = async () => {
  try {
    const res = await updateCompany(ruleForm)
    if (res.code == 200) {
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('更新公司信息失败:', error)
    ElMessage.error('更新公司信息失败')
  }
}

// 监听
watch(
  companyId,
  val => {
    if (val) {
      getCompanyFun()
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.company_information_wrap {
}

.company_information_center {
  width: 600px;
  /* margin: 0 auto; */
}

.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
}

.avatar-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

:deep(.el-cascader),
:deep(.el-select) {
  width: 100%;
}

:deep(.el-textarea__inner) {
  resize: none;
  font-family:
    PingFang SC,
    Avenir,
    Helvetica,
    Arial,
    sans-serif;
  font-size: 14px;
}
</style>
