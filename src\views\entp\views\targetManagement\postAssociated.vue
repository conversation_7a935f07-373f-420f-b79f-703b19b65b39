<template>
  <div class="post_associated_wrap bg_write">
    <div class="page_main_title">岗位关联</div>
    <div class="page_section">
      <div class="post_associated_center clearfix">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div class="overflow_elps tree_title">组织</div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio
              :treeData="treeData"
              :defaultCheckedKeys="defaultCheckedKeys"
              @clickCallback="clickCallback"
            ></tree-comp-radio>
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <el-table
            class="table_wrap"
            max-height="540"
            :data="tableData.data"
            @cell-click="cellClick"
            :header-cell-class-name="tableRowClassName"
          >
            <el-table-column
              v-for="(item, index1) in tableData.tableTitle"
              v-if="!item.canCheck"
              :label="item.label"
              :prop="item.prop"
              :key="item.code"
              align="center"
            >
            </el-table-column>
            <el-table-column
              v-for="(item, index1) in tableData.tableTitle"
              width="40"
              v-if="item.canCheck"
              class-name="check_column"
              :show-overflow-tooltip="item.label.length > 5"
              :label="item.label"
              :prop="item.prop"
              :key="item.code"
              align="center"
              :render-header="renderHeader"
            >
              <template v-slot="scope">
                <span
                  :class="{
                    'el-icon-check': scope.row[item.prop],
                    'el-icon-disabled_check': !scope.row[item.disabledSign],
                    check_box_wrap: true
                  }"
                ></span>
              </template>
            </el-table-column>
          </el-table>
          <div class="align_center">
            <el-button
              type="primary"
              class="page_confirm_btn"
              v-if="tableData.tableTitle.length > 0"
              @click="undateTargetBtn()"
              >确认</el-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getOrgDeptTree, relatePostKpi, updatePostKpi } from '../../request/api'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import tableComponent from '@/components/talent/tableComps/tableComponent'

export default {
  name: 'relationshipGroups',
  components: {
    treeCompRadio,
    tableComponent
  },
  data() {
    return {
      treeData: [],
      defaultCheckedKeys: [],
      checkedId: '',
      tableData: {
        tableTitle: [],
        data: []
      },
      kpiCodeList: [],
      checkTargetSignList: [],
      checkTargetDisabledSignList: [],
      checkArr: [],
      param: {}
    }
  },
  mounted() {
    // this.getPostKpiListFun(this.searchTargetTypeSign)
  },
  methods: {
    // 获取组织树
    getOrgDeptTreeFun() {
      getOrgDeptTree({
        companyId: this.companyId
      }).then(res => {
        console.log(res)
        if (res.code == 200) {
          this.treeData = res.data.length > 0 ? res.data : []
        } else {
          this.treeData = []
          this.defaultCheckedKeys = []
        }
      })
    },
    // 勾选左侧组织树
    clickCallback(val, isLastNode) {
      // console.log(val,isLastNode)
      // checkedId 为code
      if (val) {
        this.checkedId = val
        this.relatePostKpiFun()
      } else {
        this.tableData = {
          tableTitle: [],
          data: []
        }
      }
    },
    // 获取岗位和对应指标数组
    relatePostKpiFun() {
      relatePostKpi({
        orgCode: this.checkedId
      }).then(res => {
        // console.log(res)
        if (res.code == 200) {
          if (res.data.kpiList.length == 0 && res.data.postList.length == 0) {
            this.tableData = {
              tableTitle: [],
              data: []
            }
            return
          }
          // 初始表头固定部分 及表格数据
          this.tableData.tableTitle = [
            {
              label: '序号',
              prop: 'index',
              canCheck: false
            },
            {
              label: '组织',
              prop: 'orgName',
              canCheck: false
            },
            {
              label: '岗位',
              prop: 'postName',
              canCheck: false
            }
          ]
          this.tableData.data = []
          this.kpiCodeList = []
          this.checkTargetSignList = []
          this.checkTargetDisabledSignList = []

          // 表头数据
          for (let i = 0; i < res.data.kpiList.length; i++) {
            this.tableData.tableTitle.push({
              label: res.data.kpiList[i].val,
              prop: 'targetName-' + (i + 1),
              canCheck: true,
              disabledSign: 'targetKpiCode-' + (i + 1),
              code: res.data.kpiList[i].code
            })
            this.checkTargetSignList.push('targetName-' + (i + 1))
            this.checkTargetDisabledSignList.push('targetKpiCode-' + (i + 1))
            this.kpiCodeList.push(res.data.kpiList[i].code)
          }
          // 表格数据
          for (let j = 0; j < res.data.postList.length; j++) {
            this.tableData.data.push({
              index: j + 1,
              orgName: res.data.postList[j].orgName,
              postName: res.data.postList[j].postName,
              orgCode: res.data.postList[j].orgCode,
              postCode: res.data.postList[j].postCode,
              selectedKpiCodes: res.data.postList[j].selectedKpiCodes,
              kpiCodeList: this.kpiCodeList,
              // kpiCode:this.kpiCodeList
              optionalKpiCodes: res.data.postList[j].optionalKpiCodes
            })
            // 将所有表格初始为未被选中 初始为可勾选
            for (let i = 0; i < this.kpiCodeList.length; i++) {
              this.tableData.data[j][this.checkTargetSignList[i]] = false
              this.tableData.data[j][this.checkTargetDisabledSignList[i]] = false
            }

            // 初始化不可点击表格
            if (this.tableData.data[j].optionalKpiCodes.length > 0) {
              for (let k = 0; k < this.tableData.data[j].optionalKpiCodes.length; k++) {
                for (let h = 0; h < this.kpiCodeList.length; h++) {
                  if (this.tableData.data[j].optionalKpiCodes[k] == this.kpiCodeList[h]) {
                    this.tableData.data[j][this.checkTargetDisabledSignList[h]] = true
                  }
                }
              }
            }
            // 变更所有表格选中状态
            for (let k = 0; k < res.data.postList[j].selectedKpiCodes.length; k++) {
              let checkColumnn
              if (this.kpiCodeList.indexOf(res.data.postList[j].selectedKpiCodes[k]) != -1) {
                checkColumnn = this.kpiCodeList.indexOf(res.data.postList[j].selectedKpiCodes[k])
                this.tableData.data[j]['targetName-' + (checkColumnn + 1)] = true
              }
            }
          }
          // console.log(this.tableData)
          // console.log(this.checkTargetDisabledSignList)
        }
      })
    },
    cellClick(row, column, cell, event) {
      // console.table(event,cell)
      // console.log(row)
      // console.log(column)
      if (!row['targetKpiCode-' + column.property.split('-')[1]]) {
        return
      }
      if (row[column.property] != true && row[column.property] != false) {
        return
      }
      let checkKpiCode
      for (let i = 0; i < this.tableData.data.length; i++) {
        if (this.tableData.data[i].index == row.index) {
          checkKpiCode = this.tableData.data[i].kpiCodeList[column.property.split('-')[1] - 1]
          // console.log(checkKpiCode)
          if (this.tableData.data[i][column.property] == true) {
            this.tableData.data[i][column.property] = false
            this.tableData.data[i].selectedKpiCodes.splice(
              this.tableData.data[i].selectedKpiCodes.indexOf(checkKpiCode),
              1
            )
            this.$set(this.tableData.data, row.index - 1, row)
          } else {
            this.tableData.data[i][column.property] = true
            this.tableData.data[i].selectedKpiCodes.push(checkKpiCode)
            this.$set(this.tableData.data, row.index - 1, row)
          }
          // console.log(this.tableData.data)
          return
        }
      }
    },
    undateTargetBtn() {
      this.checkArr = []
      // console.log(this.tableData.data)
      for (let i = 0; i < this.tableData.data.length; i++) {
        this.checkArr.push({
          orgCode: this.tableData.data[i].orgCode,
          postCode: this.tableData.data[i].postCode,
          kpiCodes: this.tableData.data[i].selectedKpiCodes
        })
      }
      this.param = {
        orgCode: this.checkedId,
        postKpiList: this.checkArr
      }
      this.updatePostKpiFun()
    },
    // 表头样式
    tableRowClassName({ row, colum, rowIndex, columnIndex }) {
      // console.log(row[columnIndex].label)
      if (row[columnIndex].label.length > 5) {
        return 'check_column_act'
      }
    },
    renderHeader(h, { column, $index }) {
      // return (
      //     <div>
      //         <span class='table_title_indexs' title={column.label}>{column.label}</span>
      //     </div>
      // )
    },
    updatePostKpiFun() {
      updatePostKpi({
        json: JSON.stringify(this.param)
      }).then(res => {
        // console.log(res)
        if (res.code == 200) {
          this.$msg.success(res.msg)
        } else {
          this.$msg.error(res.msg)
        }
      })
    }
  },
  watch: {
    companyId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getOrgDeptTreeFun()
          // this.getKpiClassTreeFun()
        }
      }
    }
  },
  computed: {
    companyId() {
      return this.$store.state.userInfo.companyId
    }
  }
}
</script>

<style scoped lang="scss">
.post_associated_wrap {
  .page_section_main {
    .table_wrap {
      .el-table__row {
        height: 41px;
      }
    }
    .table_wrap tbody .cell {
      cursor: pointer;
    }
    .table_wrap thead {
      .check_column {
        .cell {
          height: 93px;
          span {
            line-height: 18px;
            display: block;
            height: 93px;
            word-wrap: break-word;
            letter-spacing: 20px;
          }
        }
      }
    }
    .table_wrap .check_box_wrap {
      color: #0099fd;
      font-weight: 700;
    }
    .align_center {
      margin: 15px 0 0;
    }
    .table_wrap tbody {
      .check_column {
        .cell {
          width: 30px;
          height: 30px;
          padding: 0;
          margin: 5px 0 4px;
        }
        .check_box_wrap {
          display: inline-block;
          width: 28px;
          height: 28px;
          border: 1px solid #dcdfe6;
          color: #000;
          text-align: center;
          line-height: 28px;
          // // margin-right: 3px;
          cursor: pointer;
        }
        .check_box_wrap:hover {
          border: 1px solid #0099ff;
        }
        .el-icon-check {
          border: 1px solid #0099ff;
          color: #fff;
          background: #0099ff;
        }
        .el-icon-disabled_check {
          cursor: no-drop;
          background: #edf2fc;
        }
        .el-icon-disabled_check:hover {
          border: 1px solid #dcdfe6;
        }
      }
    }
    .check_column_act {
      .cell::after {
        content: '¨¨';
        // content: '···';
        font-weight: bold;
        position: absolute;
        bottom: -0px;
        height: 9px;
        right: 0;
        left: -8px;
      }
    }
  }
}
</style>
