export default {
  /**
   * 权限取并集
   */
  createAuthority: function (arr, ) {
    if (arr && arr.length != 0) {
      let result = [];
      for (let i = 0; i < arr.length; i++) {
        result = result.concat(arr[i].actionList);
      }
      // console.log(result);
      return result;
    }
  },
  createRandomId: function () {
    return '_' + Math.random().toString(36).substr(2)
  },
  objHasEmpty(obj, verifiKey, ignoreKey) {
    // verifiKey : Array 校验必填字段、非空；不传默认所有必填（若后端返回的其他非展示用的字段为空时，会有影响，建议传入必填字段）
    // ignoreKey: Array 忽略校验的字段
    // 校验对象是否有空值，
    // 有 返回 ture 反之为 false(undefinde)
    ignoreKey = ignoreKey ? ignoreKey : [];
    if (!verifiKey || verifiKey.length == 0) {
      for (const key in obj) {
        if (ignoreKey.indexOf(key) == -1) {
          if (obj.hasOwnProperty(key)) {
            const element = obj[key];
            if ((Array.isArray(element) && element.length == 0) || (Object.prototype.toString.call(element) ==
                '[object Object]' && this.isEmptyObj(element)) || (!element && element !== 0)) {
              // 空数组   空对象  空值
              return true;
            }
            // if (Array.isArray(element) && element.length == 0) {
            //     // 空数组
            //     return true;
            // }
            // if (Object.prototype.toString.call(element) == '[object Object]' && this.isEmptyObj(element)) {
            //     //  空对象
            //     return true;
            // }
            // if (!element) {
            //     return true;
            // }
          }
        }
      }
    } else {
      for (const key in obj) {
        if (verifiKey.indexOf(key) !== -1) {

          if (obj.hasOwnProperty(key)) {
            const element = obj[key];
            if ((Array.isArray(element) && element.length == 0) || (Object.prototype.toString.call(element) ==
                '[object Object]' && this.isEmptyObj(element)) || (!element && element !== 0)) {
              // 空数组   空对象  空值
              return true;
            }
            // if (Array.isArray(element) && element.length == 0) {
            //     // 空数组
            //     return true;
            // }
            // if (Object.prototype.toString.call(element) == '[object Object]' && this.isEmptyObj(element)) {
            //     //  空对象
            //     return true;
            // }
            // if (!element) {
            //     return true;
            // }
          }
        }
      }
    }

  },

}
