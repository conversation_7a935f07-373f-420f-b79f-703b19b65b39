<template>
  <div class="post_import_wrap bg_write">
    <div class="page_main_title">
      <div class="goback_geader" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>返回
      </div>
      组织职责
    </div>
    <div class="page_second_title">组织职责导入--错误一览</div>
    <div class="page_section staff_import_center clearfix">
      <el-table :data="tableData" stripe ref="tableRef" v-if="flag">
        <el-table-column type="index" width="50" />
        <el-table-column
          show-overflow-tooltip
          v-for="col in columns"
          :prop="col.prop"
          :key="col.prop"
          :label="col.label"
          :width="col.width"
        >
          <template #default="scope">
            <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row[col.prop].msg"
              placement="top"
              :disabled="scope.row[col.prop].accept"
            >
              <el-input
                :class="{ error: !scope.row[col.prop].accept }"
                v-model="scope.row[col.prop].val"
                :title="scope.row[col.prop].val"
                :type="col.prop == 'userPasswd' ? 'password' : 'text'"
                :disabled="scope.row[col.prop].accept"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!-- <coustomPagination @pageChange="pageChange" :total="tableDataCopy.length" /> -->
      <div class="btn_wrap align_center">
        <el-button type="primary" @click="saveBtn">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { importOrgDuty } from '../../request/api'
// import coustomPagination from '@/components/table/paginationComps/coustomPagination'

// Router
const router = useRouter()
const route = useRoute()

// Refs
const tableRef = ref(null)
const flag = ref(true)
const tableData = ref([])
const tableDataCopy = ref([])

// 列定义
const columns = [
  {
    label: '职责名称',
    prop: 'respName'
  },
  {
    label: '业务领域',
    prop: 'bizDomainCode'
  },
  {
    label: '职责描述',
    prop: 'respDesc'
  }
]

// 方法
const goBack = () => {
  router.go(-1)
}

const pageChange = (pageSize, currentPage) => {
  getPageData(pageSize, currentPage)
}

const getPageData = (pageSize, currentPage) => {
  const offset = (currentPage - 1) * pageSize
  tableData.value =
    offset + pageSize >= tableDataCopy.value.length
      ? tableDataCopy.value.slice(offset, tableDataCopy.value.length)
      : tableDataCopy.value.slice(offset, offset + pageSize)
}

const saveBtn = async () => {
  const data = tableDataCopy.value.map(item => {
    const obj = {
      respName: '',
      bizDomainCode: '',
      respDesc: ''
    }

    Object.keys(obj).forEach(key => {
      obj[key] = item[key].val
    })

    return obj
  })

  try {
    const res = await importOrgDuty(data)
    if (res.code == 200) {
      tableData.value = []
      tableDataCopy.value = []

      if (res.data.errorCount == 0) {
        router.go(-1)
        return
      }

      tableData.value = res.data.obj
      tableDataCopy.value = res.data.obj
      flag.value = false

      getPageData(10, 1)

      await nextTick()
      flag.value = true
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error('保存组织职责失败:', error)
    ElMessage.error('保存组织职责失败')
  }
}

// 初始化
onMounted(() => {
  const errorData = route.query.data
  tableData.value = errorData
  tableDataCopy.value = errorData
  pageChange(10, 1)
})
</script>

<style scoped>
.post_import_wrap .page_second_title {
  margin: 0 0 0 15px;
}

:deep(.el-input.error .el-input__inner) {
  border-color: var(--el-color-danger);
}

.goback_geader {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  color: var(--el-color-primary);
}

.goback_geader .el-icon {
  margin-right: 4px;
}
</style>
