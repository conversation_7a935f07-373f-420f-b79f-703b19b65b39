<template>
  <div class="main_detail">
    <tableList :tableData="tableData" @detail="toDetail" :hasBack="true" @back="back">
      <template v-slot:nameColumn>
        <el-table-column label="能力组件群" prop="name" align="center" width="200">
          <template #default="scope">
            <div class="align_left">{{ scope.row['name'] }}</div>
            <div class="score_bar">
              <div
                class="content_bar"
                :style="{
                  width: `${scope.row['value']}%`
                  // background: `linear-gradient(-90deg,${setColor(scope.row['value'])})`
                }"
              ></div>
              <div class="text" :style="{ left: `calc(${scope.row['value']}% + 5px)` }">
                {{ scope.row['value'] }}
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="组件群" prop="name" width="200">
          <template #default="scope">
            <div class="align_left">{{ scope.row['name'] }}</div>
            <div class="score_bar">
              <div
                class="content_bar"
                :style="{
                  width: `${scope.row['value']}%`,
                  background: `linear-gradient(-90deg,${setColor(scope.row['value'])})`
                }"
              ></div>
              <div class="text" :style="{ left: `calc(${scope.row['value']}% + 5px)` }">
                {{ scope.row['value'] }}
              </div>
            </div>
          </template>
        </el-table-column> -->
      </template>
    </tableList>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import tableList from './tableList.vue'

const route = useRoute()
const emit = defineEmits(['setComponentGroupName'])

const modelId = ref(null)
const diagnosticType = ref(null)
const moduleCode = ref(null)
const tableData = ref({
  columns: [
    {
      name: '流程赋能',
      code: 'A'
    },
    {
      name: '组织赋能',
      code: 'B'
    },
    {
      name: '人岗赋能',
      code: 'C'
    },
    {
      name: '数字化赋能',
      code: 'D'
    },
    {
      name: 'AI赋能',
      code: 'E'
    }
  ],
  list: [
    {
      name: '管理需求计划策略',
      A: 55.4,
      B: 54.8,
      C: 57.4,
      D: 33.5,
      E: 33.3,
      value: 46.2,
      changeType: 1,
      changeScore: 1.2,
      AList: [
        {
          name: '流程端到端',
          value: 53.6
        },
        {
          name: '输入输出文档',
          value: 55.4
        },
        {
          name: '业务 KPI',
          value: 58.6
        },
        {
          name: '业务规则',
          value: 54.0
        }
      ],
      BList: [
        {
          name: '岗位角色职责',
          value: 54.2
        },
        {
          name: '岗位协同 RACI',
          value: 57.8
        },
        {
          name: '组织岗位 KPI',
          value: 56.0
        },
        {
          name: '组织设置',
          value: 51.0
        }
      ],
      CList: [
        {
          name: '能力培训',
          value: 56.8
        },
        {
          name: '人员动力',
          value: 55.6
        },
        {
          name: '人员能力评估',
          value: 60.0
        },
        {
          name: '人员能力要求',
          value: 57.2
        }
      ],
      DList: [
        {
          name: '数据治理',
          value: 44.4
        },
        {
          name: '系统赋能',
          value: 41.6
        },
        {
          name: '系统改善规划',
          value: 48.0
        },
        {
          name: '自动化与智能化设备',
          value: 0.0
        }
      ],
      EList: [
        {
          name: '数据驱动决策',
          value: 36.0
        },
        {
          name: '算法赋能',
          value: 33.0
        },
        {
          name: '业务模型优化',
          value: 32.8
        },
        {
          name: '智能风险预警',
          value: 31.6
        },
        {
          name: '自动化流程优化',
          value: 33.2
        }
      ]
    },
    {
      name: '管理物料需求计划策略',
      A: 57.3,
      B: 53.2,
      C: 54.4,
      D: 32.9,
      E: 32.7,
      value: 45.5,
      changeType: null,
      changeScore: 0
    }
  ]
})

const router = useRouter()
const toDetail = (item, index) => {
  console.log('item :>> ', item)
  emit('setComponentGroupName', item.name)
  router.push(
    `/AI/displayer/overview/component?modelId=${modelId.value}&diagnosticType=${diagnosticType.value}&moduleCode=${item.code}`
  )
}

const back = () => {
  emit('setComponentGroupName', '')
  router.back()
}

const setColor = score => {
  const scoreRanges = [
    '#B2FDFE, rgba(178,253,254,0.11)',
    '#83FDFE, rgba(131,253,254,0.11)',
    '#34EAFF, rgba(52,234,255,0.11)',
    '#3ED4FF, rgba(131,253,254,0.11)',
    '#22B5FA, rgba(34,181,250,0.11)',
    '#2589EC, rgba(37,137,236,0.11)',
    '#0D69C1, rgba(13,105,193,0.11)',
    '#08549B, rgba(8,84,155,0.11)',
    '#03396E, rgba(3,57,110,0.11)',
    '#032B4D, rgba(3,43,77,0.11)'
  ]
  let index = parseInt(score / 10) == 10 ? 9 : parseInt(score / 10)
  return scoreRanges[index]
}

// 初始化
console.log('route.query :>> ', route.query)
let { modelId: mId, diagnosticType: dType, moduleCode: mCode } = route.query
modelId.value = mId
diagnosticType.value = dType
moduleCode.value = mCode

// 监听路由变化
watch(
  () => route.query,
  value => {
    console.log('value :>> ', value)
    let { modelId: mId, diagnosticType: dType, moduleCode: mCode } = value
    modelId.value = mId
    diagnosticType.value = dType
    moduleCode.value = mCode
  }
)
</script>

<style lang="scss" scoped>
.score_bar {
  position: relative;
  width: calc(100% - 30px);
  height: 20px;
  background-color: #f5f7f7;
  margin-top: 10px;

  .content_bar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #85e5ff;
  }

  .text {
    position: absolute;
    left: 100%;
    top: -3px;
    color: #000;
    white-space: nowrap;
    font-size: 12px;
  }

  .name {
    width: 100%;
    text-align: left;
  }
}
.align_left {
  text-align: left;
}
</style>
