import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'

/******
 * id: String 需要导出为pdf的dom对象
 * title: String pdf文件名
 * callback: 回调函数
 * singlepage: Boolean, 是否是单页pdf模式
 * 
 * eg：
 * this.getPdf(id,title,callback,singlepage)
 */

// 单页
export default {
    install(Vue, options) {
        Vue.prototype.getPdf = function (id, title, singlepage,callback) {
            if (singlepage) {
                // 单页pdf
                // 获取dom高度、宽度
                var shareContent = document.querySelector('#' + id)
                var width = shareContent.offsetWidth / 4
                var height = shareContent.offsetHeight / 4

                console.log(shareContent.offsetHeight);

                html2Canvas(document.querySelector(`#${id}`), {
                    // allowTaint: true
                    useCORS: true,//看情况选用上面还是下面的，
                    scale: 1
                }).then(function (canvas) {
                    var context = canvas.getContext('2d')
                    context.mozImageSmoothingEnabled = false
                    context.webkitImageSmoothingEnabled = false
                    context.msImageSmoothingEnabled = false
                    context.imageSmoothingEnabled = false
                    var pageData = canvas.toDataURL('image/jpeg',)
                    console.log(pageData);
                    var img = new Image()
                    img.src = pageData

                    img.onload = function () {
                        // 获取dom高度、宽度
                        img.width = img.width / 2
                        img.height = img.height / 2
                        img.style.transform = 'scale(0.5)'
                        if (width > height) {
                            // 此可以根据打印的大小进行自动调节
                            // eslint-disable-next-line
                            var PDF = new JsPDF('l', 'mm', [
                                width * 0.505,
                                height * 0.545
                            ])
                        } else {
                            // eslint-disable-next-line
                            var PDF = new JsPDF('p', 'mm', [
                                width * 0.505,
                                height * 0.545
                            ])
                        }
                        PDF.addImage(
                            pageData,
                            'jpeg',
                            0,
                            0,
                            width * 0.505,
                            height * 0.545
                        )
                        callback();
                        PDF.save(title + '.pdf')
                    }
                })
            } else {
                // 多页
                html2Canvas(document.querySelector(`${id}`), {
                    // allowTaint: true
                    useCORS: true//看情况选用上面还是下面的，
                }).then(function (canvas) {
                    let contentWidth = canvas.width
                    let contentHeight = canvas.height
                    console.log(contentWidth);
                    //一页pdf显示html页面生成的canvas高度;
                    let pageHeight = contentWidth / 592.28 * 841.89
                    //未生成pdf的html页面高度
                    let leftHeight = contentHeight
                    //页面偏移 
                    let position = 0
                    //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
                    let imgWidth = 595.28
                    let imgHeight = 592.28 / contentWidth * contentHeight
                    let pageData = canvas.toDataURL('image/jpeg', 1.0)
                    // console.log(pageData);
                    let PDF = new JsPDF('', 'pt', 'a4')
                    //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
                    //当内容未超过pdf一页显示的范围，无需分页
                    if (leftHeight < pageHeight) {
                        PDF.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
                    } else {
                        while (leftHeight > 0) {
                            PDF.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
                            leftHeight -= pageHeight
                            position -= 841.89
                            //避免添加空白页
                            if (leftHeight > 0) {
                                PDF.addPage()
                            }
                        }
                    }
                    callback();
                    PDF.save(title + '.pdf')
                })
            }
        }
    }
}














// 分页


// import html2Canvas from 'html2canvas'
// import JsPDF from 'jspdf'

// export default {
//     install(Vue, options) {
//         Vue.prototype.getPdf = function (id, title) {
//             
//         }
//     }
// }