<template>
  <div class="input-main">
    <el-input v-model="input" type="textarea" placeholder="给兮小易发消息" @keydown.enter.prevent="sendBtn()" />
    <div class="action-bar">
      <div class="think">
        <img class="ico" src="@/assets/imgs/dialogue/think.webp" alt="" />
        <div class="text">深度思考</div>
        <el-switch size="small" v-model="think" />
      </div>
      <div class="right">
        <!-- <div class="accessory">
          <img src="@/assets/imgs/dialogue/accessory.webp" alt="" />
        </div> -->
        <div class="voice" @click="voiceChange()">
          <img v-if="!isSpeaking" src="@/assets/imgs/dialogue/voice.webp" alt="" />
          <img v-else src="@/assets/imgs/dialogue/voice.gif" alt="" />
        </div>
        <div class="send" @click="pause()" v-if="loading" style="background: #fff">
          <img src="@/assets/imgs/dialogue/pause.webp" alt="" />
        </div>
        <div class="send" v-else :style="{ background: input ? '#53a9f9' : '#CAD6E1' }" @click="sendBtn()">
          <img src="@/assets/imgs/dialogue/send.webp" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const emit = defineEmits(['send', 'pause'])
//#region 语音
const isSpeaking = ref(false)
let recognition = null
let finalResult = '' // 保存最终结果的变量
let interimResult = '' // 保存中间结果的变量
const voiceChange = () => {
  if (!recognition) {
    // eslint-disable-next-line no-undef
    recognition = new webkitSpeechRecognition()
    recognition.continuous = true
    recognition.interimResults = true
    recognition.lang = 'zh-CN'
    recognition.onresult = event => {
      interimResult = '' // 清空中间结果
      for (let i = event.resultIndex; i < event.results.length; i++) {
        if (event.results[i].isFinal) {
          finalResult += event.results[i][0].transcript
        } else {
          interimResult += event.results[i][0].transcript
        }
      }
      input.value = finalResult + interimResult
    }
  }
  if (isSpeaking.value) {
    // finalResult = input.value
    recognition.stop()
  } else {
    // 保持输入内容
    finalResult = input.value
    recognition.start()
  }
  isSpeaking.value = !isSpeaking.value
}
//#endregion
//#region 问答
const loading = ref(false)
const think = ref(true)
const input = ref('')
const sendBtn = () => {
  if (!loading.value) emit('send')
}

const pause = input => {
  loading.value = false
  emit('pause', input)
}
//#endregion
defineExpose({
  input,
  loading,
  pause
})
</script>
<style lang="scss" scoped>
.input-main {
  background: #ffffff;
  border-radius: 10px;
  border: 1px solid #dddddd;
  padding: 12px;
  margin-top: 40px;
  :deep(.el-textarea__inner) {
    outline: none;
    border: none;
    resize: none;
    box-shadow: none;
    // background: #f8f8f8;
  }
  .action-bar {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    .right {
      display: flex;
      align-items: center;
      div {
        cursor: pointer;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .accessory,
      .voice {
        width: 24px;
        height: 24px;
        background: #ebf3ff;
        border-radius: 2px 2px 2px 2px;
        margin-right: 12px;
        &:hover {
          background: #53a9f9;
        }
      }
      .send {
        width: 32px;
        height: 32px;
        background: #53a9f9;
        border-radius: 50%;
        margin-left: 4px;
        &:nth-child(1) {
          cursor: not-allowed;
        }
      }
    }
    .think {
      display: flex;
      align-items: center;
      height: 36px;
      border-radius: 10px;
      background: #ffffff;
      border: 1px solid #dddddd;
      padding: 0 10px;
      .ico {
        width: 18px;
        height: 18px;
        margin-right: 4px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .text {
        font-size: 14px;
        color: #333333;
        margin-right: 7px;
      }
    }
  }
}
</style>
