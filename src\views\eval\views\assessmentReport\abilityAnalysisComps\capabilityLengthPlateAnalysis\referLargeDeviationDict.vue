<template>
    <div class="">
        <table-component
            :tableData="tableData"
            :needIndex="true"
        ></table-component>
    </div>
</template>
 
<script>
    import tableComponent from "@/components/talent/tableComps/tableComponent";
    export default {
        name: "referLargeDeviationDict",
        data() {
            return {
                tableData: {
                    columns: [
                        {
                            label: "能力词典",
                            prop: "abilityDict",
                        },
                        {
                            label: "所属模块",
                            prop: "belongModule",
                        },
                        {
                            label: "参评人数",
                            prop: "contestantNumber",
                            formatterFun: function (data) {
                                return data["notStandard"] + data["standard"];
                            },
                        },
                        {
                            label: "综合得分",
                            prop: "synthesisScore",
                        },
                        {
                            label: "偏差值",
                            prop: "deviationValue",
                        },
                        {
                            label: "自评",
                            prop: "selfAssessment",
                        },
                        {
                            label: "上级",
                            prop: "superior",
                        },
                        {
                            label: "下级",
                            prop: "subordinate",
                        },
                        {
                            label: "同级",
                            prop: "equative",
                        },
                        {
                            label: "协同岗位",
                            prop: "collaborativeJobs",
                        },
                    ],
                    data: [
                        {
                            id: "1",
                            abilityDict: "高效沟通",
                            belongModule: "人际互动",
                            contestantNumber: 9,
                            synthesisScore: 72,
                            deviationValue: 7,
                            selfAssessment: 60,
                            superior: 66,
                            subordinate: 20,
                            equative: 10,
                            collaborativeJobs: 80,
                        },
                    ],
                },
            };
        },
        components: {
            tableComponent,
        },

        methods: {},
    };
</script>
 
<style scoped lang="scss">
</style>