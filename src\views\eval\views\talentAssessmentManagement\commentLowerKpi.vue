<template>
    <div class="task_confirmation_main marginT_30">
        <div class="page_second_title">人员评价</div>
        <div class="personnel_info marginT_20">
            <div>
                <span>人员</span>
                <span>{{ confirmNum }} /</span>
                <span>{{ personnelData.length }}</span>
            </div>
            <div class="flex_row_start">
                <span class="name">{{ objectName }}</span>
                <span class="department">{{ orgName }}</span>
                <div class="post_list_wrap flex_row_start">
                    <div class="post_list active">{{ postName }}</div>
                </div>
            </div>
        </div>
        <div class="department_main">
            <div class="personnel_item_wrap">
                <div
                    class="personnel_item"
                    v-for="(item, index) in personnelData"
                    :class="{
                        completed: item.reviewStatus == 'Y',
                        curr: currIndex == index,
                    }"
                    :key="index"
                    @click="selectPersonnel(index)"
                >
                    <span>{{ item.objectName }}</span>
                    <i
                        class="icon el-icon-check"
                        v-if="item.reviewStatus == 'Y'"
                    ></i>
                    <i class="icon disc" v-else></i>
                </div>
            </div>
            <div class="personnel_form_wrap clearfix">
                <el-form
                    :model="ruleForm"
                    :rules="rules"
                    ref="ruleForm"
                    :size="mini"
                    label-width="90px"
                    class="demo-ruleForm form_main"
                >
                    <el-form-item label="核心能力" prop="competenceRank">
                        <el-radio-group
                            v-model="ruleForm.competenceRank"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.competenceRank"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="绩效表现" prop="kpiRank">
                        <el-radio-group v-model="ruleForm.kpiRank" size="mini">
                            <el-radio-button
                                v-for="item in optionsCfg.kpiRank"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="发展潜力" prop="developmentPotential">
                        <el-radio-group
                            v-model="ruleForm.developmentPotential"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.developmentPotential"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="晋升可能" prop="promotionPossibility">
                        <el-radio-group
                            v-model="ruleForm.promotionPossibility"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.promotionPossibility"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="离职风险" prop="retentionRisk">
                        <el-radio-group
                            v-model="ruleForm.retentionRisk"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.retentionRisk"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="离职影响" prop="dimissionImpact">
                        <el-radio-group
                            v-model="ruleForm.dimissionImpact"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.dimissionImpact"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="内部可替代" prop="innerSubstitution">
                        <el-radio-group
                            v-model="ruleForm.innerSubstitution"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.innerSubstitution"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="外部可替代"
                        prop="externalSubstitution"
                    >
                        <el-radio-group
                            v-model="ruleForm.externalSubstitution"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.externalSubstitution"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="离职可能性"
                        prop="dimissionPossibility"
                    >
                        <el-radio-group
                            v-model="ruleForm.dimissionPossibility"
                            size="mini"
                        >
                            <el-radio-button
                                v-for="item in optionsCfg.dimissionPossibility"
                                :key="item.dictCode"
                                :label="item.dictCode"
                                >{{ item.codeName }}</el-radio-button
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="能否继任" prop="successionPossibility">
                        <el-select
                            v-model="ruleForm.successionPossibility"
                            size="mini"
                            placeholder
                        >
                            <el-option
                                v-for="item in optionsCfg.successionPossibility"
                                :key="item.dictCode"
                                :value="item.dictCode"
                                :label="item.codeName"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <div class="paddT_12">
                            <el-button
                                class="page_add_btn"
                                type="primary"
                                @click="submitForm('ruleForm')"
                                >确定</el-button
                            >
                            <!-- <el-button
                                class="page_clear_btn"
                                @click="resetForm('ruleForm')"
                                size="mini"
                                >重置</el-button
                            > -->
                        </div>
                    </el-form-item>
                </el-form>
                <div class="matrix_chart_wrap">
                    <div class="page_third_title">人才九宫格</div>
                    <div class="">
                        <talentClassifyMatrix
                            :kpiRankOption="kpiRankOption"
                            :competenceRankOptions="competenceRankOptions"
                            :talentData="kpiCapablity"
                        ></talentClassifyMatrix>
                    </div>
                    <div class="matrix_chart small">
                        <div class="matrix_head">
                            <div class="title">商业风险</div>
                            <div class="flex_row_start border">
                                <div
                                    class="item"
                                    v-for="item in optionsCfg.dimissionImpact"
                                    :key="item.dictCode"
                                >
                                    {{ item.codeName }}
                                </div>
                            </div>
                        </div>
                        <div class="clearfix">
                            <div class="matrix_aside">
                                <div class="matrix_aside_head flex_row_start">
                                    <div class="title">离职风险</div>
                                    <div class="flex_col_start border">
                                        <div
                                            class="item"
                                            v-for="item in optionsCfg.retentionRisk"
                                            :key="item.dictCode"
                                        >
                                            {{ item.codeName }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="matrix_main">
                                <div
                                    class="matrix_row"
                                    :class="'matrix_row_' + (index + 1)"
                                    v-for="(item, index) in matrixData2"
                                    :key="index"
                                >
                                    <div
                                        class="item"
                                        v-for="list in item"
                                        :class="'item_' + list.key"
                                        :key="list.key"
                                    >
                                        {{ list.size ? list.size + " 人" : "" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="matrix_chart small">
                        <div class="matrix_head">
                            <div class="title">核心能力</div>
                            <div class="flex_row_start border">
                                <div class="item">高</div>
                                <div class="item">中</div>
                                <div class="item">低</div>
                            </div>
                        </div>
                        <div class="clearfix">
                            <div class="matrix_aside">
                                <div class="matrix_aside_head flex_row_start">
                                    <div class="title">发展潜力</div>
                                    <div class="flex_col_start border">
                                        <div
                                            class="item"
                                            v-for="item in optionsCfg.developmentPotential"
                                            :key="item.dictCode"
                                        >
                                            {{ item.codeName }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="matrix_main">
                                <div
                                    class="matrix_row"
                                    :class="'matrix_row_' + (index + 1)"
                                    v-for="(item, index) in matrixData3"
                                    :key="index"
                                >
                                    <div
                                        class="item"
                                        :class="'item_' + list.key"
                                        v-for="list in item"
                                        :key="list.key"
                                    >
                                        {{ list.size ? list.size + " 人" : "" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="align_center marginT_30">
            
                <el-button
                    class="page_confirm_btn"
                    type="primary"
                    @click="nextBtn()"
                    >下一步</el-button
                >
            </div>
    </div>
</template>

<script>
    import {
        getReviewSub,
        getInterval,
        submitPerformance,
    } from "../../request/api";

    import talentClassifyMatrix from "@/components/talent/common/talentClassifyMatrix";
    export default {
        name: "commentLowerKpi",
        props: ["nextBtnText"],
        components: { talentClassifyMatrix },
        created() {
            let docList = [
                "KPI_RANK",
                "COMPETENCE_RANK",
                "DEVELOPMENT_POTENTIAL",
                "TALENT_CLASS",
                "PROMOTION_POSSIBILITY",
                "RETENTION_RISK",
                "DIMISSION_IMPACT",
                "INNER_SUBSTITUTION",
                "EXTERNAL_SUBSTITUTION",
                "EXPECTATION_CYCLE",
                "DIMISSION_POSSIBILITY",
                "SUCCESSION_POSSIBILITY",
            ];
            this.$getDocList(docList).then((res) => {
                let optionsCfg = {
                    kpiRank: res.KPI_RANK,
                    competenceRank: res.COMPETENCE_RANK,
                    developmentPotential: res.DEVELOPMENT_POTENTIAL,
                    promotionPossibility: res.PROMOTION_POSSIBILITY,
                    retentionRisk: res.RETENTION_RISK,
                    dimissionImpact: res.DIMISSION_IMPACT,
                    innerSubstitution: res.INNER_SUBSTITUTION,
                    externalSubstitution: res.EXTERNAL_SUBSTITUTION,
                    // expectationCycle: res.EXPECTATION_CYCLE,
                    dimissionPossibility: res.DIMISSION_POSSIBILITY,
                    successionPossibility: res.SUCCESSION_POSSIBILITY,
                };
                this.kpiRankOption = this.$util.deepClone(res.KPI_RANK).reverse();
                this.competenceRankOptions = res.COMPETENCE_RANK;
                this.optionsCfg = optionsCfg;
            });
            this.getReviewSubFun();
            this.getMatrixData();
        },

        methods: {
            getReviewSubFun(refreshFlag) {
                let flag = refreshFlag ? true : false;
                let params = {
                    evalId: this.evalId,
                };
                getReviewSub(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.confirmNum = 0;
                        this.personnelData = res.data;
                        this.personnelData.forEach((item) => {
                            if (item.reviewStatus == "Y") {
                                this.confirmNum++;
                            }
                        });
                        if (!flag) {
                            let data = this.personnelData[this.currIndex];
                            this.postCode = data.postCode;
                            this.postName = data.postName;
                            this.objectId = data.objectId;
                            this.objectName = data["objectName"];
                            this.orgName = data["orgName"];
                            this.setFromData(data);
                        }
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
            setFromData(data) {
                for (let key in this.ruleForm) {
                    this.ruleForm[key] = data[key];
                }
            },
            saveUserEvalFun(prevStep) {
                this.ruleForm.evalId = this.evalId;
                this.ruleForm.objectId = this.objectId;
                this.ruleForm.postCode = this.postCode;
                let params = [];
                params[0] = this.$util.deepClone(this.ruleForm);
                submitPerformance(params).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        this.$msg.success(res.msg);
                        // if (prevStep) {
                        //     this.$emit("prevStep");
                        // }
                        this.getReviewSubFun(true);
                        this.getMatrixData();
                        this.savaFlag = true;
                    } else {
                        this.$msg.error(res.msg);
                    }
                });
            },
            submitForm(formName, prevStep) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        if (this.savaFlag) {
                            this.savaFlag = false;
                            this.saveUserEvalFun(prevStep);
                        }
                    } else {
                        console.log("error submit!!");
                        return false;
                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },
            getMatrixData() {
                getInterval({ evalId: this.evalId }).then((res) => {
                    console.log(res);
                    if (res.code == "200") {
                        let data = res.data;
                        // 绩效指标&&核心能力
                        this.kpiCapablity = data.kpiCapablity;
                        // 核心能力&&发展潜力
                        this.matrixData3 = data.developmentCapability;
                        // 商业风险&&离职风险
                        this.matrixData2 = data.retention;
                    }
                });
            },
            selectPersonnel(val) {
                console.log(val);
                this.currIndex = val;
            },
            // prevBtn() {
           
            // },
            nextBtn() {
                for (let index = 0; index < this.personnelData.length; index++) {
                    let status = this.personnelData[index].reviewStatus;
                    let userName = this.personnelData[index].objectName;
                    console.log(status);
                    if (status != "Y") {
                        this.$msg.error(
                            `请完善人员：${userName}的评价后再次提交！`
                        );
                        return;
                    }
                }
                this.$emit("nextStep");
            },
        },
        watch: {
            currIndex: function (val) {
                let data = this.personnelData[val];
                this.postCode = data.postCode;
                this.postName = data.postName;
                this.objectId = data.objectId;
                this.objectName = data["objectName"];
                this.orgName = data["orgName"];
                this.setFromData(data);
            },
        },
        data() {
            return {
                evalId: this.$route.query.evalId ? this.$route.query.evalId : "",
                savaFlag: true,
                optionsCfg: {},
                currIndex: 0,
                objectName: "",
                orgName: "",
                postName: [],
                postCode: "",
                objectId: "",
                confirmNum: 0,
                personnelData: [],
                kpiRankOption: [],
                kpiCapablity: {}, //人才分布九宫格组件所需数据
                competenceRankOptions: [],
                ruleForm: {
                    kpiRank: "",
                    competenceRank: "",
                    developmentPotential: "",
                    promotionPossibility: "",
                    retentionRisk: "",
                    dimissionImpact: "",
                    innerSubstitution: "",
                    externalSubstitution: "",
                    dimissionPossibility: "",
                    successionPossibility: "",
                },
                rules: {
                    kpiRank: [
                        {
                            required: true,
                            message: "请选择绩效表现",
                            trigger: "change",
                        },
                    ],
                    competenceRank: [
                        {
                            required: true,
                            message: "请选择核心能力",
                            trigger: "change",
                        },
                    ],
                    developmentPotential: [
                        {
                            required: true,
                            message: "请选择发展潜力",
                            trigger: "change",
                        },
                    ],
                    promotionPossibility: [
                        {
                            required: true,
                            message: "请选择晋升可能",
                            trigger: "change",
                        },
                    ],
                    retentionRisk: [
                        {
                            required: true,
                            message: "请选择离职风险",
                            trigger: "change",
                        },
                    ],
                    dimissionImpact: [
                        {
                            required: true,
                            message: "请选择离职影响",
                            trigger: "change",
                        },
                    ],
                    innerSubstitution: [
                        {
                            required: true,
                            message: "请选择内部可替代",
                            trigger: "change",
                        },
                    ],
                    externalSubstitution: [
                        {
                            required: true,
                            message: "请选择外部可替代",
                            trigger: "change",
                        },
                    ],
                    dimissionPossibility: [
                        {
                            required: true,
                            message: "请选择离职可能性",
                            trigger: "change",
                        },
                    ],
                    successionPossibility: [
                        {
                            required: true,
                            message: "请选择能否继任",
                            trigger: "change",
                        },
                    ],
                },
                matrixData2: [],
                matrixData3: [],
            };
        },
    };
</script>

<style scoped lang="scss">
    .personnel_info {
        // text-align: right;
        // background: #f4f4f4;
        // line-height: 45px;
        // margin-bottom: 16px;
        // padding: 0 16px;
        font-weight: bold;
        background: #f4f4f4;
        line-height: 25px;
        margin-bottom: 16px;
        padding: 12px 16px;
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        .department {
            // color: #449CFF;
            // border: 1px solid #449CFF;
            // padding: 2px 5px;
            // border-radius: 3px;
            // margin: 0 16px;
            color: #449cff;
            border: 1px solid #449cff;
            padding: 1px 6px;
            line-height: 21px;
            border-radius: 3px;
            margin: 0 16px;
            background: #fff;
        }
        .inline_b {
            width: auto;
        }
        .post_list_wrap {
            .post_list {
                cursor: pointer;
                margin-right: 16px;
                &.active {
                    color: #ffc000;
                    // color: #0099FF;
                }
            }
        }
    }

    .personnel_item_wrap {
        width: 120px;
        float: left;
        margin-right: 8px;

        .personnel_item {
            line-height: 30px;
            padding: 0 8px;
            color: #525e6c;
            font-size: 14px;
            background: #f8f8f8;
            margin-bottom: 5px;
            font-weight: bold;
            cursor: pointer;

            &.completed {
                color: #0099fd;
                background: #eef5fb;

                .icon {
                    display: block;
                }
            }

            &.curr {
                background: #0099fd;
                color: #fff;

                .icon {
                    display: block;
                    color: #fff;

                    &.disc {
                        background: #fff;
                    }
                }
            }

            .icon {
                display: none;
                float: right;
                font-weight: bold;
                line-height: 30px;
                text-align: center;
                color: #0099fd;

                &.disc {
                    width: 8px;
                    height: 8px;
                    margin: 10px 4px 0 auto;
                    border-radius: 50%;
                    background: #ffc000;
                }
            }
        }
    }

    .personnel_form_wrap {
        overflow: hidden;

        .form_main {
            float: left;
            width: 340px;
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-start;
        }
    }

    .matrix_chart_wrap {
        overflow: hidden;
        // margin-left: 20px;
        float: right;
        width: calc(100% - 350px);
    }

    .matrix_chart {
        // width: 480px;
        /*float: left;*/
        margin-right: 6px;
        margin-bottom: 16px;

        &.small {
            width: 240px;
            float: left;
            margin-top: 20px;

            .matrix_head {
                line-height: 20px;

                .title {
                    height: 20px;
                    padding-left: 40px;
                }

                .flex_row_start {
                    height: 20px;
                    margin-left: 40px;
                }
            }

            .matrix_aside {
                width: 40px;
                height: 190px;

                .title {
                    width: 20px;
                    height: calc(100% + 40px);
                    margin-top: -40px;
                }

                .flex_col_start {
                    width: 20px;
                }

                .item {
                    line-height: 60px;
                }
            }

            .matrix_main {
                .matrix_row {
                    &_1 {
                        .item_1 {
                            background-color: #e28d80;
                        }

                        .item_2 {
                            background-color: #719dd5;
                        }

                        .item_3 {
                            background-color: #a3d0f3;
                        }
                    }

                    &_2 {
                        .item_1 {
                            background-color: #719dd5;
                        }

                        .item_2,
                        .item_3 {
                            background-color: #a3d0f3;
                        }
                    }

                    &_3 {
                        .item_1,
                        .item_2 {
                            background-color: #a3d0f3;
                        }

                        .item_3 {
                            background-color: #dddee3;
                        }
                    }
                }

                .item {
                    width: 66px;
                    height: 63px;
                }
            }
        }

        .matrix_head {
            width: 100%;
            // padding-left: 45px;
            // margin-left: 45px;
            text-align: left;
            line-height: 30px;

            .title {
                height: 30px;
                background: #fbfbfb;
                padding-left: 100px;
            }

            .flex_row_start {
                height: 30px;
                margin-left: 100px;

                &.border {
                    border-bottom: 1px solid #f6f6f6;
                }
            }

            .item {
                flex: 1;
                text-align: center;
            }
        }

        .matrix_aside {
            float: left;
            width: 100px;
            height: 230px;
            text-align: center;

            .matrix_aside_head {
                height: 100%;
            }

            .title {
                height: calc(100% + 50px);
                padding: 30px 10px 0 5px;
                width: 30px;
                background: #fbfbfb;
                margin-top: -50px;
            }

            .flex_col_start {
                height: 100%;
                width: 70px;

                &.border {
                    border-right: 1px solid #f6f6f6;
                }
            }

            .item {
                flex: 1;
                line-height: 45px;
            }
        }

        .matrix_main {
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .matrix_row {
                width: 100%;
                display: flex;
                flex: row nowrap;
            }

            .item {
                flex: 1;
                // width: 125px;
                height: 76px;
                margin: 0 1px 1px 0;
                padding: 2px;
                color: #fff;
                font-size: 16px;
                overflow: hidden;
                // 矩阵下方 三个颜色样式
                &_HA,
                &_HH {
                    background-color: #e28d80;
                }
                &_HB,
                &_HM {
                    background-color: #719dd5;
                }
                &_HC,
                &_HL {
                    background-color: #bed269;
                }
                &_MA,
                &_MH {
                    background-color: #719dd5;
                }
                &_MB,
                &_MM {
                    background-color: #bed269;
                }
                &_MC,
                &_ML {
                    background-color: #bed269;
                }
                &_LA,
                &_LH {
                    background-color: #bed269;
                }
                &_LB,
                &_LM {
                    background-color: #bed269;
                }
                &_LC,
                &_LL {
                    background-color: #a3d0f3;
                }
            }
        }
    }

    .el-form-item {
        width: 100%;
        margin-bottom: 15px;
    }

    .el-form-item__content {
        line-height: 30px;
    }

    .el-form-item__label {
        line-height: 30px;
    }

     .el-input__inner {
        width: 100%;
    }

     .el-form-item__error {
        padding-top: 2px;
    }

     .el-textarea {
        padding-top: 10px;
        width: 100%;
    }

    .el-select {
        width: 100%;
    }

    .el-radio-group {
        display: flex;

        label {
            flex: 1;

            span {
                width: 100%;
            }
        }
    }

    .popover_dom {
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .el-popover__reference {
        width: 100%;
        height: 100%;
    }
    .el-form-item__label{font-size: 12px;}
</style>
