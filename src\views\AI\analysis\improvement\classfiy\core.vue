<script setup>
import SimplenessTable from '@/components/table/simplenessTable.vue'

defineOptions({ name: 'core' })

const columns = ref([
  {
    type: 'index',
    label: '序号'
  },
  {
    label: '阶段名称',
    prop: 'name',
    width: 80
  },
  {
    label: '核心任务',
    prop: 'task'
  },
  {
    label: '输出文档',
    prop: 'doc',
    width: 100
  },
  {
    label: '耗时标准',
    prop: 'time',
    width: 90
  },
  {
    label: '核心管控点',
    prop: 'core'
  }
])
const tableData = ref([
  {
    strategy: '需求计划全流程端到端贯通',
    name: '数据采集',
    task: '整合市场调研数据、历史销售数据、客户订单数据及产能库存数据，清洗无效数据（准确率≥95%）',
    doc: '《需求数据整合报告》',
    time: '≤3 个工作日',
    core: '数据完整率≥98%，无效数据剔除率≥90%'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '需求建模',
    task: '运用 ABC 分类法对产品需求进行分类，结合季节波动、促销活动等因素构建需求预测模型',
    doc: '《需求分类及预测模型报告》',
    time: '≤5 个工作日',
    core: '模型预测误差率≤8%，分类准确率≥95%'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '产能校验',
    task: '组织生产、采购部门进行产能评估，校验需求计划与产能匹配度，输出产能平衡方案',
    doc: '《产能校验报告及平衡方案》',
    time: '≤4 个工作日',
    core: '产能匹配度≥90%，关键物料缺口识别率 100%'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '计划评审',
    task: '召开跨部门评审会，审核需求计划合理性，重点关注高优先级订单保障方案',
    doc: '《需求计划评审报告》',
    time: '≤2 个工作日',
    core: '评审通过率≥90%，高优先级订单保障措施完整率 100%'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '执行监控',
    task: '实时跟踪需求计划执行情况，每日更新计划进度，识别异常并触发调整机制',
    doc: '《需求计划执行日报》',
    time: '每日',
    core: '异常响应时效≤2 小时，计划执行偏差率≤5%'
  },
  {
    strategy: '需求计划全流程端到端贯通',
    name: '复盘优化',
    task: '每月召开需求计划复盘会，分析执行效果，沉淀优化措施并更新预测模型参数',
    doc: '《需求计划复盘报告及优化方案》',
    time: '≤3 个工作日',
    core: '经验沉淀率≥80%，模型参数更新及时率 100%'
  }
])
</script>
<template>
  <div class="core">
    <div class="core-title">
      <div class="index">1</div>
      <div class="text">阶段定义</div>
    </div>
    <SimplenessTable :columns="columns" :data="tableData"></SimplenessTable>
    <div class="core-title mt-6">
      <div class="index">2</div>
      <div class="text">闭环监控设计</div>
    </div>
    <div class="border">
      <b> 阶段准入 / 准出标准：</b>
      <p>
        准入控制：进入产能校验阶段需满足《需求数据整合报告》经数据中心与计划部双签、历史数据覆盖率≥90%、产能预评估偏差率≤15%
      </p>
      <p>准出校验：计划评审阶段结束需《需求计划评审报告》经生产、采购、销售部门会签，高优先级订单保障措施完整率 100%</p>
      <b>可视化监控看板：</b>
      <p>
        实时显示需求计划各阶段进度（数据采集 / 建模 / 校验 /
        评审）、滞留订单数量、预测偏差率趋势。支持穿透查询：点击阶段节点可查看详细数据明细（如未通过评审的具体原因、产能缺口明细）
      </p>
      <p>支持穿透式查询：点击单个商机可查看全流程操作日志（时间 / 责任人 / 操作内容），便于责任追溯。</p>
    </div>
    <div class="core-title mt-6">
      <div class="index">3</div>
      <div class="text">异常管理机制</div>
    </div>
    <div class="border">
      <b>三级预警体系：</b>
      <p>黄色预警：预测偏差率达 10%-15%，自动提醒计划员复核数据；</p>
      <p>橙色预警：偏差率 15%-20%，触发跨部门数据对齐会，24 小时内提交修正方案；</p>
      <p>红色预警：偏差率≥20% 或连续 3 次评审未通过，冻结计划流程，启动高层数据治理专项小组介入</p>
      <b>熔断后处理流程：</b>
      <p>48 小时内召开数据治理复盘会，追溯数据源头问题（如市场数据失真 / 产能数据滞后）；</p>
      <p>1 周内完成数据采集流程补丁设计，同类问题重复发生率需下降 80% 以上</p>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.core {
  .core-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
    color: #3d3d3d;
    margin-bottom: 13px;
    .index {
      width: 16px;
      height: 16px;
      background: #40a0ff;
      border-radius: 50%;
      text-align: center;
      line-height: 16px;
      color: #fff;
      font-weight: normal;
      margin-right: 7px;
    }
  }
  .border {
    background: linear-gradient(228deg, #e6f5ff 3%, #ffffff 21%, #ffffff 82%, #e6f5ff 100%), rgba(255, 255, 255, 0.5);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    padding: 16px 20px;
  }
}
</style>
