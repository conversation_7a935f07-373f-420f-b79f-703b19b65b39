<script setup>
import Table from '@/components/table/simplenessTable.vue'
//
const columns7 = ref([
  {
    label: '二级能力',
    prop: 'a',
    width: 260
  },
  {
    label: '高风险',
    prop: 'b'
  },
  {
    label: '中风险',
    prop: 'c'
  },
  {
    label: '低风险',
    prop: 'd'
  }
])
const data7 = ref([
  {
    a: '市场分析与战略规划',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  },
  {
    a: '战略解码与目标分解',
    b: '15人',
    c: '15人',
    d: '15人',
    e: '15人'
  }
])

const columns8 = ref([
  {
    label: '风险类型',
    prop: 'f'
  },
  {
    label: '三级能力',
    prop: 'a',
    width: 260
  },
  {
    label: '人数',
    prop: 'b'
  },
  {
    label: '能力得分',
    prop: 'c'
  },
  {
    label: '潜在风险描述',
    prop: 'd',
    width: 360
  }
])
const data8 = ref([
  {
    a: '经验依赖型',
    b: '12',
    c: '36',
    d: '以过往成功或失败案例为决策核心依据，通过类比历史情境解决当下问题',
    e: '快速复用历史验证的成熟方案，在核心业务领域凭借经验积累精准判断关键成功因子，提升战略落地效率。',
    f: '高风险',
    g: '0',
    h: '2024-09-26 09:59:59',
    k: '100'
  }
])

const columns12 = ref([
  {
    label: '风险类型',
    prop: 'd',
    width: 150
  },
  {
    label: '三级能力',
    prop: 'a',
    width: 150
  },
  {
    label: '人员',
    prop: 'b'
  },
  {
    label: '改善建议',
    prop: 'c'
  }
])
const data12 = ref([
  {
    a: '市场数据收集',
    b: '王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）、王伟（销售总监）',
    c: '1.划清经验边界：填写《经验适用边界清单》，标注差异点与潜在风险，避免盲目复制。 2.反经验训练：每月推演 “非惯性做法”，输出《反经验行动清单》，强制尝试新策略。 3.新场景探索：分配 10% 时间接触跨界案例，撰写《旧经验失效分析》，打破路径依赖。',
    d: '高风险'
  }
])

onMounted(() => {})
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">风险偏好分析</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        通过人才测评中参评人员的选项数据，构建风险偏好分析模型，精准识别其在不同能力场景下决策可能面临的风险。基于分析结果，针对性制定能力提升方案，有效降低错误决策或非最优决策对业务运作的影响，保障业务高效推进。
      </div>
    </div>
    <div class="info_section_wrap three_seven_wrap justify-between">
      <div class="l_wrap">
        <div class="page-title-line">整体风险人数分布</div>
        <div class="chart_box"></div>
      </div>
      <div class="r_wrap">
        <div class="page-title-line">不同能力下的风险分布</div>
        <Table :roundBorder="false" :columns="columns7" :data="data7" headerColor showIndex> </Table>
      </div>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">各项风险场景分析（市场分析与战略规划）</div>
      <Table :roundBorder="false" :columns="columns8" :data="data8" headerColor> </Table>
    </div>
    <!-- <div class="info_section_wrap">
      <div class="page-title-line">决策模式改善建议</div>
      <Table :roundBorder="false" :columns="columns9" :data="data9" headerColor>
      </Table>
    </div> -->

    <div class="info_section_wrap">
      <div class="page-title-line">各项风险管控改善建议</div>
      <Table :roundBorder="false" :columns="columns12" :data="data12" headerColor> </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../../../../style/common.scss';
@import './common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
