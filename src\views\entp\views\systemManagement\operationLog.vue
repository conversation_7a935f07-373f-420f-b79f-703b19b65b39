<template>
    <div class="operation_log_wrap bg_write">
        <div class="page_main_title">系统日志</div>
        <div class="page_section">
            <div class="page_section_main page_shadow">
                <div class="filter_bar_wrap">
                    <div class="flex_row_start">
                        <div class="filter_item title">筛选</div>
                        <div class="filter_item">
                            <el-date-picker
                                v-model="beginDate"
                                type="date"
                                value-format="YYYY-MM-DD"
                                placeholder="开始日"
                            >
                            </el-date-picker>
                        </div>
                        <div class="filter_item">
                            <el-date-picker
                                v-model="endDate"
                                type="date"
                                value-format="YYYY-MM-DD"
                                placeholder="截止日"
                            >
                            </el-date-picker>
                        </div>
                        <div class="filter_item">
                            <el-input
                                placeholder="搜索人员"
                                suffix-icon="el-icon-search"
                                v-model="operatorName"
                                
                            ></el-input>
                        </div>
                        <div class="filter_item">
                            <el-button
                                class="page_add_btn"
                                type="primary"
                                
                                @click="keyWordSearch"
                                >查询</el-button
                            >
                        </div>
                    </div>
                </div>
                <div class="operation_log_center clearfix">
                    <div>
                        <table-component
                            :tableData="tableData"
                            :needIndex="needIndex"
                            @handleSizeChange="handleSizeChange"
                            @handleCurrentChange="handleCurrentChange"
                        >
                        </table-component>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
 
<script>
    import { getLogList } from "../../request/api";
    import tableComponent from "@/components/talent/tableComps/tableComponent";

    export default {
        name: "operationLog",
        components: {
            tableComponent,
        },
        data() {
            return {
                needIndex: true,
                tableData: {
                    columns: [
                        {
                            label: "用户名",
                            prop: "operatorId",
                        },
                        {
                            label: "姓名",
                            prop: "operatorName",
                        },
                        {
                            label: "操作时间",
                            prop: "operationTime",
                        },
                        {
                            label: "对象",
                            prop: "menuName",
                        },
                        {
                            label: "行为",
                            prop: "actionName",
                        },
                        {
                            label: "IP",
                            prop: "ip",
                        },
                        {
                            label: "参数",
                            prop: "requestParam",
                        },
                        {
                            label: "浏览器版本",
                            prop: "userAgent",
                        },
                    ],
                    data: [],
                    page: {
                        total: 0,
                        current: 1,
                        size: 10,
                    },
                },
                beginDate: "",
                endDate: "",
                operatorName: "",
            };
        },
        created() {
            this.getLogListFun();
        },
        mounted() {},
        methods: {
            getLogListFun() {
                getLogList({
                    beginDate: this.beginDate,
                    operatorName: this.operatorName,
                    endDate: this.endDate,
                    current: this.tableData.page.current,
                    size: this.tableData.page.size,
                }).then((res) => {
                    //    console.log(res)
                    if (res.code == 200 && res.data) {
                        this.tableData.data = res.data.map((item) => {
                            return {
                                operatorId: item.operatorId,
                                operatorName: item.operatorName,
                                operationTime: item.operationTime.replace(
                                    /T/g,
                                    " "
                                ),
                                menuName: item.menuName,
                                actionName: item.actionName,
                                ip: item.ip,
                                requestParam: item.requestParam,
                                userAgent: item.userAgent,
                            };
                        });
                        this.tableData.page.total = res.total;
                    } else {
                        this.tableData.data = [];
                        this.tableData.page = {
                            total: 0,
                            current: 1,
                            size: 10,
                        };
                    }
                });
            },
            // 翻页
            handleSizeChange(handleSizeChange) {
                this.tableData.page.current = 1;
                this.tableData.page.size = handleSizeChange;
                this.getLogListFun();
            },
            handleCurrentChange(handleCurrentChange) {
                this.tableData.page.current = handleCurrentChange;
                this.getLogListFun();
            },
            keyWordSearch() {
                this.tableData.page.current = 1;
                this.getLogListFun();
            },
        },
    };
</script>
 
<style scoped lang="scss">
    .operation_log_wrap {
        .el-table__row {
            height: 47px;
        }
    }
</style>