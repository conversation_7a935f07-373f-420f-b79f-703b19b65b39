<script setup>
import SectionTab from "../../components/sectionTab.vue";
import SectionTabV from "../../components/sectionTabVertical.vue";
import Table from "../../components/table.vue";
const router = useRouter();
const route = useRoute();
// import inventoryProject from "./inventoryProject.vue";
// import inventoryReport from "./inventoryReport.vue";
// const tabContentList = ref([inventoryProject, inventoryReport]);
const sectionTabVList = ref([
  {
    name: "盘点项目",
    code: 1,
    path: "/org/internalIdentification/talentInventoryS/inventoryProject",
  },
  {
    name: "盘点报告",
    code: 2,
    path: "/org/internalIdentification/talentInventoryS/inventoryReport",
  },
]);
const sectionTabVCheckSign = ref(2);
const checkSecTabV = (c) => {
  sectionTabVCheckSign.value = c;
  router.push(sectionTabVList.value[c - 1].path);
};

const initActMenu = (t) => {
  t.forEach((e) => {
    if (route.path.indexOf(e.path) > -1) {
      sectionTabVCheckSign.value = e.code;
    }
  });
};
onMounted(() => {
  initActMenu(sectionTabVList.value);
});
</script>
<template>
  <div class="index_wrap">
    <div class="i_main_wrap justify-between">
      <div class="main_l_wrap">
        <SectionTabV
          :sectionTabVList="sectionTabVList"
          :sectionTabVCheckSign="sectionTabVCheckSign"
          :title="'人才盘点服务 ：'"
          @checkSecTabV="checkSecTabV"
        ></SectionTabV>
      </div>
      <div class="content-mian">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.index_wrap {
  .i_main_wrap {
    .main_l_wrap {
      width: 180px;
      margin-right: 20px;
    }
    .content-mian {
      flex: 1;
    }
  }
}
</style>
