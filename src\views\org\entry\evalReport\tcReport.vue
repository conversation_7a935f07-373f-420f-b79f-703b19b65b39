<script setup>
import SectionTab from '../../components/sectionTab.vue'
const router = useRouter()
const route = useRoute()

const sectionTabCheckSign = ref(1)
const sectionTabList = ref([
  {
    name: '组织报告',
    code: 1
  },
  {
    name: '个人报告',
    code: 2
  }
])

const checkSecTab = c => {
  sectionTabCheckSign.value = c
}
</script>
<template>
  <div class="tcReport_wrap"></div>
</template>
<style lang="scss" scoped>
@import '../../style/common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.tcReport_wrap {
}
</style>
