<script setup>
defineOptions({ name: 'Breadcrumb' })
const breadcrumbList = ref([])
const route = useRoute()
watchEffect(() => {
  if (route.path.startsWith('/redirect/')) return
  console.log('route', route)

  let matched = route.matched.filter(item => item.meta?.title || item.meta?.name)

  if (!matched.some(item => item.path == '/' || item.path == '/dialogue')) {
    // matched = [{ path: '/', meta: { title: '首页' } }].concat(matched)
  }
  console.log('matched', matched)
  breadcrumbList.value = matched
})

const router = useRouter()
const navigationTo = item => {
  console.log('navigationTo')
  console.log(item)

  if (item.path == route.path) return
  router.push({ path: item.path })
}
</script>
<template>
  <div class="breadcrumb">
    <el-breadcrumb separator="/">
      <!-- <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item> -->
      <el-breadcrumb-item @click="navigationTo(item)" v-for="item in breadcrumbList" :key="item.name">
        {{ item.meta.title || item.meta.name }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>
<style lang="scss" scoped>
.breadcrumb {
  font-weight: 600;
  font-size: 18px;
  color: #3d3d3d;
  line-height: 32px;
}
:deep(.el-breadcrumb) {
  font-size: 18px;
  color: #3d3d3d;
  line-height: 32px;
  .el-breadcrumb__item:last-of-type {
    .el-breadcrumb__inner {
      font-weight: 600;
    }
  }
  .el-breadcrumb__inner {
    font-weight: 400;
    cursor: pointer;
    color: #3d3d3d;
  }
}
</style>
