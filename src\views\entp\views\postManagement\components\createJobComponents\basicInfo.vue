<template>
  <div class>
    <div class="job_management_basic_info_wrap from_wrap">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <!--<el-form-item label="职位编码" prop="jobCode">
          <el-input
            v-model="ruleForm.jobCode"
            class="bg_none"
            disabled
          ></el-input>
        </el-form-item> -->
        <el-form-item label="职位名称" prop="jobName">
          <el-input v-model="ruleForm.jobName"></el-input>
        </el-form-item>
        <!-- <el-form-item label="职位类别" prop="jobType">
                    <el-input v-model="ruleForm.jobType"></el-input>
                </el-form-item> -->
        <el-form-item label="职位类型" prop="jobType">
          <el-select
            v-model="ruleForm.jobType"
            clearable
            placeholder="请选择职位类型"
          >
            <el-option
              v-for="item in jobTypeOption"
              :key="item.dictCode"
              :label="item.codeName"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="族群序列" prop="jobClassCode">
          <el-cascader
            :options="jobClassTreeData"
            v-model="ruleForm.jobClassCode"
            placeholder="请选择族群"
            :change-on-select="true"
            disabled
            :props="{
              label: 'value',
              value: 'code',
              expandTrigger: 'hover',
            }"
            @change="(val) => handleItemChange(val, 'jobClassTree')"
            clearable
          >
          </el-cascader>
        </el-form-item>
        <el-form-item label="职层" prop="jobLevelCode">
          <el-cascader
            :options="jobLevelTreeData"
            v-model="ruleForm.jobLevelCode"
            placeholder="请选择职层"
            :change-on-select="true"
            :props="{
              label: 'value',
              value: 'code',
              expandTrigger: 'hover',
            }"
            @change="(val) => handleItemChange(val, 'jobLevelTree')"
            clearable
          >
          </el-cascader>
        </el-form-item>
        <!-- <el-form-item label="职层" prop="jobLevelCode">
                    <el-cascader
                        :options="jobLevelTreeData"
                        v-model="ruleForm.jobLevelCode"
                        placeholder="请选择职层"
                        :change-on-select="true"
                        :props="{
                            label: 'value',
                            value: 'code',
                            expandTrigger: 'hover',
                        }"
                        @change="(val) => handleItemChange(val, 'jobLevelTree')"
                        clearable
                    >
                    </el-cascader>
                </el-form-item> -->
        <el-form-item label="职等" prop="jobGradeCode">
          <el-select
            v-model="ruleForm.jobGradeCode"
            clearable
            placeholder="请选择职等"
          >
            <el-option
              v-for="item in jobGradeOption"
              :key="item.dictCode"
              :label="item.codeName"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="职位外部编码" prop="jobCodeExtn">
          <el-input
            v-model="ruleForm.jobCodeExtn"
            :disabled="this.jobCode ? true : false"
          ></el-input>
        </el-form-item>

        <el-form-item label="关联标准职位(业务测评相关)" prop="jobNormRelated">
          <div @click="openRelationPopup">
            <el-input
              placeholder="请选择"
              suffix-icon="el-icon-arrow-down"
              v-model="ruleForm.jobNormRelated"
            >
            </el-input>
          </div>
        </el-form-item>
        <div class="related_job_list_wrap" v-if="isHasCheckDataSign">
          <div class="related_job_list_title flex_row_betweens">
            <p class="title">已关联职位</p>
            <p class="related_job_manage_btn" @click="relatedJobManage">
              管理关联职位
            </p>
          </div>
          <!-- <ul class="related_job_list flex_row_wrap_start" v-if="curRJobList.length > 0"> -->
          <ul class="related_job_list flex_row_wrap_start">
            <li
              :class="{ act: isManageRelatedJobSign }"
              v-for="(item, index) in curRJobList"
              :key="index"
            >
              {{ item.name }}
              <span
                v-if="isManageRelatedJobSign"
                class="el-icon-close"
                @click="deleteRelatedJob(index)"
              ></span>
            </li>
          </ul>
          <!-- <ul class="related_job_list" v-if="curRJobList.length == 0">
                        <li>
                            请选择关联职位
                        </li>
                    </ul> -->
          <div class="related_job_btn_wrap" v-if="isManageRelatedJobSign">
            <div class="related_job_btn flex_row_betweens">
              <div class="cancel_btn btn" @click="relatedJobCancel">取消</div>
              <div class="affirm_btn btn" @click="relatedJobAffirm">确定</div>
            </div>
          </div>
        </div>
        <el-form-item label="职位描述" class="textarea_wrap">
          <el-input type="textarea" v-model="ruleForm.jobDesc"></el-input>
        </el-form-item>
        <el-form-item class="align_center">
          <el-button
            class="page_confirm_btn"
            type="primary"
            @click="submitForm('ruleForm')"
            >保 存</el-button
          >
          <!-- <el-button @click="resetForm('ruleForm')">重置</el-button> -->
        </el-form-item>
      </el-form>
    </div>

    <el-dialog
      class="matched_dialog_wrap"
      title="匹配系统职位"
      :visible.sync="centerDialogVisible"
      width="60%"
      center
    >
      <div class="matched_job_wrap">
        <div class="matched_job_title">
          当前职位信息是企业自己的职位信息，需要将企业自己的职位与标准职位建立关系，请选择关联的标准职位
        </div>
        <div class="matched_job_main flex_row_betweens">
          <ul class="matched_job_left_wrap" id="nav_box">
            <li
              v-for="(item, index) in departmentList"
              :key="item.orgCode"
              :class="[
                { department_act: checkDepartmentCode == item.orgCode },
                setClass(item.orgCode, index),
              ]"
              @click="checkDepartment(item.orgCode, index)"
            >
              {{ item.orgName }}
            </li>
          </ul>
          <!-- :class="setClass(item.orgCode,index)" -->
          <div class="matched_job_right_wrap" id="data_box">
            <div
              class="matched_job_right_item_wrap"
              v-for="(item, index) in departmentTree"
              :key="item.code"
            >
              <div
                class="title"
                :class="[
                  { department_act: checkDepartmentCode == item.code },
                  setClass(item.code, index),
                ]"
              >
                <span class="circle"></span>{{ item.name }}
              </div>
              <ul class="job_info_item_wrap">
                <li v-for="item1 in item.children" :key="item1.code">
                  <span
                    class="job_name overflow_elps"
                    :class="{ job_name_act: item1.isCheck }"
                    @click="
                      checkRelationJob(item.code, item1.code, item1.isCheck)
                    "
                    >{{ item1.name }}</span
                  ><span class="desc overflow_elps">{{ item1.desc }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCancel">取 消</el-button>
        <el-button type="primary" @click="dialogAffirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
 
<script>
import {
  insertJobInfo,
  getJobClassTree,
  jobLevelTree,
  getJobGradeList,
  jobInfoByCode,
  updateJobInfo,
  jobInfoJobtree,
  jobInfoOrgList,
  // jobInfoRelationJobSave,
  stJobList,
} from "../../../../request/api";
export default {
  name: "basicInfo",
  props: {
    jobCode: String,
    jobClassCode: String,
  },
  data() {
    return {
      // jobCode:this.$route.query.jobCode?this.$route.query.jobCode:'',
      jobTypeOption: [],
      ruleForm: {
        jobCode: "",
        jobName: "",
        jobType: "",
        jobClassCode: this.jobClassCode,
        jobLevelCode: "",
        jobGradeCode: "",
        jobCodeExtn: "",
        jobNormRelated: "",
        jobDesc: "",
      },
      rules: {
        jobName: [
          {
            required: true,
            message: "请输入职位名称",
            trigger: "blur",
          },
          // {
          //     min: 3,
          //     max: 5,
          //     message: "长度在 3 到 5 个字符",
          //     trigger: "blur"
          // }
        ],
        jobClassCode: [
          {
            required: true,
            message: "请选择族群序列",
            trigger: "change",
          },
        ],
        jobLevelCode: [
          {
            required: true,
            message: "请选择职层",
            trigger: "change",
          },
        ],
        jobGradeCode: [
          {
            required: true,
            message: "请选择职等",
            trigger: "change",
          },
        ],
        jobCodeExtn: [
           {
             required: true,
             message: "职位外部编码",
             trigger: "blur",
           },
         ],
        jobNormRelated: [
          {
            required: false,
            message: "请选择标准职位",
            trigger: "change",
          },
        ],
      },
      jobLevelTreeData: [],
      jobClassTreeData: [],
      jobGradeOption: {},

      isHasCheckDataSign: false,
      departmentList: [],
      checkDepartmentCode: "",
      departmentTree: [],
      relatedJobList: [],
      curRJobList: [],
      stJobCodeStrs: "",
      isManageRelatedJobSign: false,
      centerDialogVisible: false,
    };
  },
  created() {
    this.$getDocList(["JOB_TYPE"]).then((res) => {
      console.log(res);
      this.jobTypeOption = res.JOB_TYPE;
    });
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getJobClassTreeFun();
      this.getJobLevelTree();
      this.getJobGradeListFun();
      this.searchJobFun();
      this.jobInfoJobtreeFun();
      this.jobInfoOrgListFun();
    },
    // 获取职族树
    getJobClassTreeFun() {
      getJobClassTree({}).then((res) => {
        // console.log(res)
        if (res.length > 0) {
          res.forEach((item) => {
            item.disabled = true;
          });
          this.jobClassTreeData = this.$util.formatterData(res);
          console.log(res);
        } else {
          this.jobClassTreeData = [];
        }
      });
    },
    // 获取职层树
    getJobLevelTree() {
      jobLevelTree({}).then((res) => {
        // console.log(res)
        if (res.length > 0) {
          // res.forEach((item) => {
          // item.disabled = true;
          // });
          this.jobLevelTreeData = this.$util.formatterData(res);
        } else {
          this.jobLevelTreeData = [];
        }
      });
    },
    // 获取职等
    getJobGradeListFun() {
      getJobGradeList({}).then((res) => {
        // console.log(res)
        if (res.code == 200) {
          if (res.data.length > 0) {
            this.jobGradeOption = res.data.map((item) => {
              return {
                dictCode: item.jobGradeCode,
                codeName: item.jobGradeName,
              };
            });
          }
        }
      });
    },
    // 选择 组织 族群 职层
    handleItemChange(val, treeType) {
      console.log(val, treeType);
      if (val) {
        if (treeType == "jobLevelTree") {
          this.ruleForm.checkJobLevelCode =
            val.length > 0 ? val[val.length - 1] : "";
          // console.log(val[val.length-1]);
          console.log(this.ruleForm.checkJobLevelCode);
        } else if (treeType == "jobClassTree") {
          this.ruleForm.checkJobClassCode =
            val.length > 0 ? val[val.length - 1] : "";
        } else if (treeType == "parentPostTree") {
          this.ruleForm.checkParentPostCode =
            val.length > 0 ? val[val.length - 1] : "";
        }
      }
    },
    handelChange(val) {
      console.log(val);
      this.checkPostLeaderFun();
    },
    // 管理关联职位--------
    // 关联标准职位
    relatedJobManage() {
      this.isManageRelatedJobSign = true;
    },
    // 删除关联岗位
    deleteRelatedJob(index) {
      console.log(this.curRJobList);
      // this.relatedJobList.splice(index,1)
      this.curRJobList.splice(index, 1);
      console.log(89898989);
    },
    // 取消
    relatedJobCancel() {
      console.log(this.curRJobList);
      this.isManageRelatedJobSign = false;
      this.curRJobList = JSON.parse(JSON.stringify(this.relatedJobList));
    },
    // 确认
    relatedJobAffirm() {
      this.isManageRelatedJobSign = false;
      this.ruleForm.jobNormRelated = this.curRJobList.length > 0 ? " " : "";
      this.relatedJobList = JSON.parse(JSON.stringify(this.curRJobList));
      this.isHasCheckDataSign = this.curRJobList.length > 0 ? true : false;
      // 清空弹窗职位选中
      this.clearDialogCheckData(this.departmentTree);
      // 弹窗职位重新选中
      this.checkedDialogDataFormat();
    },
    // 弹窗部分-------
    setClass(val, index) {
      return "demp" + "_" + (index + 1);
    },
    // 匹配系统职位右侧
    jobInfoJobtreeFun() {
      jobInfoJobtree({}).then((res) => {
        this.departmentTree = [];
        if (res.code == 200) {
          this.departmentTree = res.data;
        }
      });
    },
    //部门列表左侧
    jobInfoOrgListFun() {
      jobInfoOrgList({}).then((res) => {
        // console.log(res)
        this.departmentList = [];
        if (res.code == 200) {
          this.departmentList = res.data;
          if (res.data.length > 0) {
            this.checkDepartmentCode = this.departmentList[0].orgCode;
          }
        }
      });
    },
    // 勾选部门
    checkDepartment(checkCode, val) {
      this.checkDepartmentCode = checkCode;
      let targetScrollElement = document.getElementById("data_box");
      let heightCount =
        targetScrollElement.getElementsByClassName(
          "matched_job_right_item_wrap"
        )[val].offsetTop -
        targetScrollElement.getElementsByClassName(
          "matched_job_right_item_wrap"
        )[0].offsetTop;
      targetScrollElement.scrollTo(0, heightCount);
    },
    checkRelationJob(departmentCode, relationjJobCode, isCheck) {
      for (let i = 0; i < this.departmentTree.length; i++) {
        if (this.departmentTree[i].code == departmentCode) {
          for (let j = 0; j < this.departmentTree[i].children.length; j++) {
            if (this.departmentTree[i].children[j].code == relationjJobCode) {
              if (this.departmentTree[i].children[j].isCheck) {
                // 取消勾选
                this.departmentTree[i].children[j].isCheck = false;
                for (let k = 0; k < this.relatedJobList.length; k++) {
                  if (relationjJobCode == this.relatedJobList[k].code) {
                    this.relatedJobList.splice(k, 1);
                  }
                }
                return;
              } else {
                // 勾选
                this.departmentTree[i].children[j].isCheck = true;
                this.relatedJobList.push(this.departmentTree[i].children[j]);
                this.relatedJobList[this.relatedJobList.length - 1][
                  "parentCode"
                ] = this.departmentTree[i].code;
                return;
              }
            }
          }
        }
      }
    },
    // 打开弹窗
    openRelationPopup() {
      this.centerDialogVisible = true;
      this.checkedDialogDataFormat();
    },
    //弹窗取消 匹配系统职位
    dialogCancel() {
      this.centerDialogVisible = false;
      this.relatedJobList = JSON.parse(JSON.stringify(this.curRJobList));
    },
    // 弹窗选中 数据整合
    checkedDialogDataFormat() {
      // console.log(this.curRJobList)
      this.relatedJobList = [];
      // this.jobInfoJobtreeFun = []
      for (let k = 0; k < this.curRJobList.length; k++) {
        for (let i = 0; i < this.departmentTree.length; i++) {
          if (this.departmentTree[i].code == this.curRJobList[k].parentCode) {
            for (let j = 0; j < this.departmentTree[i].children.length; j++) {
              if (
                this.departmentTree[i].children[j].code ==
                this.curRJobList[k].code
              ) {
                this.departmentTree[i].children[j].isCheck = true;
                this.relatedJobList.push(this.departmentTree[i].children[j]);
                if (this.relatedJobList.parentCode) {
                } else {
                  this.relatedJobList[this.relatedJobList.length - 1][
                    "parentCode"
                  ] = this.departmentTree[i].code;
                }
              }
            }
          }
        }
      }
    },
    // 清空所有弹窗右侧勾选
    clearDialogCheckData(treeData) {
      for (let i = 0; i < treeData.length; i++) {
        if (treeData[i].children.length > 0) {
          for (let j = 0; j < treeData[i].children.length; j++) {
            treeData[i].children[j].isCheck = false;
            if (treeData[i].children[j].children) {
              this.recursion(treeData[i].children[j]);
            }
          }
        }
      }
    },
    //保存弹窗 匹配系统职位
    dialogAffirm() {
      this.centerDialogVisible = false;
      this.curRJobList = JSON.parse(JSON.stringify(this.relatedJobList));
      this.isHasCheckDataSign = this.curRJobList.length > 0 ? true : false;
      this.ruleForm.jobNormRelated = this.curRJobList.length > 0 ? " " : "";
    },
    // 提交基本信息
    submitForm(ruleForm) {
      this.stJobCodeStrs = [];
      for (let i = 0; i < this.curRJobList.length; i++) {
        this.stJobCodeStrs.push(this.curRJobList[i].code);
      }
      this.stJobCodeStrs = this.stJobCodeStrs.join();
      // console.log(this.stJobCodeStrs)
      this.$refs[ruleForm].validate((valid) => {
        if (valid) {
          if (this.jobCode) {
            this.updateJobFun();
          } else {
            this.createJobFun();
          }
        } else {
          return false;
        }
      });
    },
    createJobFun() {
      // if (!this.ruleForm.jobName) {
      //     this.$msg.warning("请填写职位名称");
      //     return;
      // } else if (!this.ruleForm.checkJobClassCode) {
      //     this.$msg.warning("请选择职位族群");
      //     return;
      // }
      // this.$emit('submitSuccessTab','basicInfo')
      insertJobInfo({
        // jobCode: this.ruleForm.jobCode,
        jobName: this.ruleForm.jobName,
        jobClassCode: this.jobClassCode,
        jobType: this.ruleForm.jobType,
        jobLevelCode: this.ruleForm.checkJobLevelCode,
        jobGradeCode: this.ruleForm.jobGradeCode,
        jobCodeExtn: this.ruleForm.jobCodeExtn,
        jobDesc: this.ruleForm.jobDesc,
        stJobCodeStrs: this.stJobCodeStrs,
      }).then((res) => {
        if (res.code == 200) {
          this.$emit("curJobCode", res.data);
          this.$emit("curJobClassCode", this.ruleForm.checkJobClassCode);
          this.$msg.success(res.msg);
          this.$emit("submitSuccessTab", "basicInfo");
        } else {
          this.$msg.error(res.msg);
        }
      });
    },
    // 修改基本信息
    updateJobFun() {
      let params = {
        jobCode: this.ruleForm.jobCode,
        jobName: this.ruleForm.jobName,
        jobType: this.ruleForm.jobType,
        jobClassCode: this.ruleForm.checkJobClassCode,
        jobLevelCode: this.ruleForm.checkJobLevelCode,
        jobGradeCode: this.ruleForm.jobGradeCode,
        jobCodeExtn: this.ruleForm.jobCodeExtn,
        jobDesc: this.ruleForm.jobDesc,
        stJobCodeStrs: this.stJobCodeStrs,
      };
      updateJobInfo(params).then((res) => {
        // console.log(res);
        if (res.code == 200) {
          this.$msg.success(res.msg);
          // this.$emit("submitSuccessTab", "basicInfo");
        } else {
          this.$msg.error(res.msg);
        }
      });
    },

    // 数据回显
    searchJobFun() {
      if (this.jobCode) {
        jobInfoByCode({
          jobCode: this.jobCode,
        }).then((res) => {
          console.log(res);
          if (res.code == 200) {
            this.ruleForm.jobName = res.data.jobName;
            this.ruleForm.jobCode = res.data.jobCode;
            this.ruleForm.jobType = res.data.jobType;
            this.ruleForm.jobClassCode = res.data.jobClassCode;
            this.ruleForm.jobLevelCode = res.data.jobLevelCode;
            this.ruleForm.jobGradeCode = res.data.jobGradeCode;
            this.ruleForm.jobDesc = res.data.jobDesc;
            this.ruleForm.jobCodeExtn = res.data.jobCodeExtn;
            this.$emit("curJobClassCode", this.ruleForm.checkJobClassCode);
            this.stJobListFun(res.data.jobCode);
          }
        });
      }
    },
    stJobListFun(val) {
      stJobList({
        jobCode: val,
      }).then((res) => {
        // console.log(res)
        if (res.code == 200) {
          this.relatedJobList = res.data.map((item) => {
            return {
              name: item.jobName,
              code: item.jobCode,
              parentCode: item.orgCode,
            };
          });
          this.curRJobList = JSON.parse(JSON.stringify(this.relatedJobList));
          this.isHasCheckDataSign = this.curRJobList.length > 0 ? true : false;
          this.ruleForm.jobNormRelated = this.curRJobList.length > 0 ? " " : "";
        }
      });
    },
  },
  watch: {
    centerDialogVisible(val) {
      // 弹窗关闭
      if (!val) {
        this.clearDialogCheckData(this.departmentTree);
        this.checkedDialogDataFormat();
        this.checkDepartment(this.departmentList[0].orgCode, 0);
      }
    },
  },
  // computed: {
  //     companyId() {
  //         return this.$store.state.userInfo.companyId;
  //     },
  // },
};
</script>
 
<style scoped lang="scss">
.job_management_basic_info_wrap {
  .related_job_list_wrap {
    margin: 0 0 20px 240px;
    width: 650px;
    // background: pink;
    border: 1px solid #e4e4e4;
    border-radius: 4px;
    .related_job_list_title {
      padding: 0 20px;
      height: 40px;
      line-height: 40px;
      background: #f3f6f9;
      border-bottom: 1px solid #e4e4e4;
      .title {
        font-weight: 700;
      }
      .related_job_manage_btn:hover {
        color: #2294f9;
        cursor: pointer;
      }
    }
    .related_job_list {
      padding: 0 0 12px 0;
      li {
        margin: 12px 10px 0;
        padding: 0 10px;
        height: 26px;
        line-height: 26px;
        border-radius: 4px;
        span {
          color: #2294f9;
          cursor: pointer;
        }
      }
      .act {
        border: 1px solid #2294f9;
      }
    }
    .related_job_btn_wrap {
      border-top: 1px solid #e4e4e4;
      .related_job_btn {
        margin: 0 auto;
        width: 130px;
        padding: 10px 0;
        div {
          width: 60px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          border: 1px solid #2294f9;
        }
        div:hover {
          cursor: pointer;
          background: #2294f9;
          color: #fff;
        }
      }
    }
  }
  .el-form {
    .el-form-item__label {
      width: 240px !important;
    }
    .el-form-item__content {
      .el-form-item__error {
        left: 140px;
      }
    }
  }
  .textarea_wrap {
    .el-form-item__content {
      margin-left: 240px !important;
    }
  }

  .el-input__inner {
    width: 280px;
  }

  .el-textarea {
    .el-textarea__inner {
      width: 650px;
      resize: none !important;
      font-family: PingFang SC, Avenir, Helvetica, Arial, sans-serif !important;
    }
  }
  .leader_post_wrap {
    position: relative;
    .el-select {
      margin: 0 10px 0 0;
    }
  }
  .leader_tips_wrap {
    position: absolute;
    width: 520px;
    color: #f56c6c;
    font-size: 12px;
  }
}
.matched_dialog_wrap {
  .el-dialog__header {
    padding: 0 20px;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #dddddd;
    .el-dialog__title {
      font-weight: 700;
    }
  }
  .el-dialog__body {
    padding: 0;
    .matched_job_title {
      padding: 0 20px;
      height: 50px;
      line-height: 50px;
      border-bottom: 1px solid #dddddd;
      color: #666;
    }
    .matched_job_main {
      height: 260px;
      border-bottom: 1px solid #dddddd;
      .matched_job_left_wrap {
        overflow: auto;
        padding: 10px 0;
        width: 160px;
        background: #f2f2f2;
        li {
          padding: 0 0 0 20px;
          height: 36px;
          line-height: 36px;
          color: #333;
          cursor: pointer;
        }
        li:hover {
          font-weight: 600;
          background: #fff;
          color: #2294f9;
        }
        .department_act {
          font-weight: 600;
          color: #2294f9;
        }
      }
      .matched_job_right_wrap {
        overflow: auto;
        flex: 1;
        margin: 0 20px 0 30px;
        // background: pink;
        .matched_job_right_item_wrap {
          .title {
            height: 36px;
            line-height: 36px;
            display: flex;
            align-items: center;
            .circle {
              display: inline-block;
              margin: 0 5px 0 0;
              width: 6px;
              height: 6px;
              background: #8296ae;
            }
          }
          .department_act {
            font-weight: 600;
            color: #2294f9;
          }
          .job_info_item_wrap {
            line-height: 26px;
            margin: 10px 0 0 0;
            span {
              display: inline-block;
            }
            .job_name {
              padding: 0 10px;
              width: 125px;
              color: #333;
              height: 26px;
              line-height: 26px;
              cursor: pointer;
            }
            .job_name:hover {
              color: #fff;
              background: #2294f9;
              border-radius: 4px;
            }
            .job_name_act {
              width: 125px;
              height: 26px;
              line-height: 26px;
              color: #fff;
              background: #2294f9;
              border-radius: 4px;
            }
            .desc {
              margin: 0 0 0 10px;
              font-size: 12px;
              width: 390px;
            }
          }
        }
      }
    }
  }
}
</style>