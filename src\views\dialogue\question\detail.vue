<template>
  <div class="dialogue-detail">
    <ChatQuestion :id="sessionId" style="height: calc(100vh - 130px)"></ChatQuestion>
  </div>
</template>
<script setup>
defineOptions({ name: 'QuestionDetail' })
import ChatQuestion from '@/components/AI/chatQuestion.vue'
const route = useRoute()
const sessionId = ref('')
onMounted(() => {
  sessionId.value = route.params.id
})
watch(
  () => route.fullPath,
  () => {
    sessionId.value = route.params.id
  }
)
</script>
