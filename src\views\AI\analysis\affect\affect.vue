<script setup>
import EChartsBar from '@/components/EChartsBar.vue'
import SimplenessTable from '@/components/table/simplenessTable.vue'
defineOptions({ name: 'affect' })
const chartData = ref([
  {
    name: '核心指标影响',
    value: '33'
  },
  {
    name: '岗位履职影响',
    value: '22'
  },
  {
    name: '决策效率影响',
    value: '33'
  },
  {
    name: '流程执行影响',
    value: '22'
  },
  {
    name: '目标驱动影响',
    value: '11'
  },
  {
    name: '人员效能影响',
    value: '11'
  },
  {
    name: '数据质量影响',
    value: '22'
  },
  {
    name: '系统支撑影响',
    value: '22'
  },
  {
    name: '协同效率影响',
    value: '22'
  },
  {
    name: '制度约束影响',
    value: '22'
  }
])
const chartOpt = ref({
  xAxisData: chartData.value.map(i => i.name),
  xAxis: {
    show: true,
    axisLabel: {
      interval: 0
    }
  },
  series: [
    {
      data: chartData.value.map(i => i.value),
      type: 'bar',
      showBackground: true,
      itemStyle: {
        color: '#40a0ff'
      },
      label: {
        show: true
      }
    }
  ]
})

const affectTable = ref(null)
const typeCol = ref([
  {
    label: '序号',
    type: 'index'
  },
  {
    label: '影响类型',
    prop: 'impactType',
    width: '100px'
  },
  {
    label: '影响类型说明',
    prop: 'impactDescription'
  },
  {
    label: '占比',
    prop: 'proportion',
    width: '60px'
  },
  {
    label: '影响系数',
    prop: 'impactFactor',
    width: '80px'
  },
  {
    label: '影响路径',
    prop: 'impactPath'
  }
])
const typeData = ref([
  {
    id: '1',
    impactType: '核心指标影响',
    impactDescription: '导致与核心能力相关的核心指标表现不佳或恶化',
    proportion: '15%',
    impactFactor: '极高',
    impactPath: '需求计划偏差→库存周转率下降 / 订单交付准时率降低→供应链成本上升→核心 KPI 恶化'
  },
  {
    id: '2',
    impactType: '岗位履职影响',
    impactDescription: '无专职岗位负责核心环节，导致专业深度不足',
    proportion: '10%',
    impactFactor: '非常低',
    impactPath: '缺岗位→核心环节由兼职人员承担→精力分散→需求计划颗粒度粗糙→但跨部门临时协作可部分弥补专业性缺口'
  },
  {
    id: '3',
    impactType: '决策效率影响',
    impactDescription: '关键信息缺失或流程卡顿导致决策滞后或失误',
    proportion: '15%',
    impactFactor: '高',
    impactPath: '缺数据→市场趋势、客户历史需求等信息不全→管理层凭经验制定计划→需求响应滞后→错失销售旺季或库存积压'
  },
  {
    id: '4',
    impactType: '流程执行影响',
    impactDescription: '无明确执行规范或质量要求，导致流程各环节操作不统一',
    proportion: '10%',
    impactFactor: '极高',
    impactPath: '缺标准→需求收集 / 分析 / 评审环节无统一模板→执行差异大→计划版本混乱→流程返工率升高→计划时效性受损'
  },
  {
    id: '5',
    impactType: '目标驱动影响',
    impactDescription: '无绩效考核驱动目标达成，导致执行动力不足',
    proportion: '5%',
    impactFactor: '非常低',
    impactPath:
      '缺考评→缺乏计划准确率 / 响应时效等考核指标→员工积极性不足→计划更新滞后→长期目标达成率波动→非核心影响因素'
  },
  {
    id: '6',
    impactType: '人员效能影响',
    impactDescription: '人员技能不匹配业务要求，导致执行效率低下',
    proportion: '5%',
    impactFactor: '非常低',
    impactPath:
      '缺能力→员工缺乏预测模型应用 / 数据解读等技能→需求分析耗时延长→计划输出质量不稳定→但可通过短期培训快速改善'
  },
  {
    id: '7',
    impactType: '数据质量影响',
    impactDescription: '关键数据缺失或质量不可控，导致分析失真',
    proportion: '10%',
    impactFactor: '高',
    impactPath: '缺数据→客户订单波动 / 历史需求数据缺失或错误→需求预测模型失效→计划偏离实际需求→库存冗余或短缺风险增加'
  },
  {
    id: '8',
    impactType: '系统支撑影响',
    impactDescription: '系统功能不支撑业务流程，导致操作断层',
    proportion: '10%',
    impactFactor: '中',
    impactPath:
      '缺系统→需求计划编制 / 审批流程无系统适配→人工跨表操作 / 线下传递→数据一致性差→操作失误风险→流程自动化水平受限'
  },
  {
    id: '9',
    impactType: '协同效率影响',
    impactDescription: '跨部门协作接口不清晰，导致沟通成本增加',
    proportion: '10%',
    impactFactor: '中',
    impactPath:
      '缺标准→销售 / 市场 / 供应链部门需求对接规则模糊→信息传递偏差→反复确认需求→计划制定周期延长→跨部门协作效率下降'
  },
  {
    id: '10',
    impactType: '制度约束影响',
    impactDescription: '无管理制度约束关键行为，导致执行随意性',
    proportion: '10%',
    impactFactor: '低',
    impactPath:
      '缺制度→需求变更审批 / 版本管理等关键环节无约束→执行标准不统一→责任界定模糊→长期积累导致计划体系权威性下降'
  }
])
onMounted(() => {
  affectTable.value.simplenessTableRef.setCurrentRow(typeData.value[0])
})
const weaknessCol = ref([
  {
    label: '序号',
    type: 'index'
  },
  {
    label: '影响要素名称',
    prop: 'impactFactorName',
    width: '100px'
  },
  {
    label: '对能力的影响路径',
    prop: 'capabilityImpactPath',
    width: '120px'
  },
  {
    label: '影响详情',
    prop: 'impactDetails'
  },
  {
    label: '影响程度',
    prop: 'impactLevel',
    width: '50px'
  },
  {
    label: '改善优先级',
    prop: 'improvementPriority',
    width: '60px'
  },
  {
    label: '改善建议',
    prop: 'improvementSuggestion'
  }
])

const weaknessData = ref([
  {
    id: '1',
    impactFactorName: '库存周转率',
    capabilityImpactPath: '需求计划偏差→安全库存设定失准→库存积压 / 短缺→周转率下降',
    impactDetails:
      '需求计划颗粒度粗糙或历史数据缺失，导致无法精准匹配市场需求，高周转产品缺货率上升，低周转产品占比超 30%，库存资金利用率下降，周转率较行业标杆低 15%-20%',
    impactLevel: '高',
    improvementPriority: '高',
    improvementSuggestion:
      '①建立《需求计划分层建模规范》，按产品生命周期（导入期 / 成长期 / 成熟期）设定差异化预测模型，成熟期产品预测周期细化至周度；②在 ERP 系统中集成需求计划模块，自动抓取销售 POS 数据、市场趋势报告，通过机器学习算法生成动态需求预测，减少人工经验依赖'
  },
  {
    id: '2',
    impactFactorName: '订单交付准时率',
    capabilityImpactPath: '需求计划与产能错配→生产排程混乱→紧急插单率上升→交付延迟率增加',
    impactDetails:
      '需求计划未同步生产产能上限及供应商交付周期，导致计划量超出实际可执行能力，月度紧急插单次数超 10 次，交付准时率跌破 85%，客户投诉率同比上升 25%',
    impactLevel: '高',
    improvementPriority: '高',
    improvementSuggestion:
      '①构建《需求 - 产能联动分析模型》，在计划制定阶段自动校验产能负荷（如设备利用率超 90% 时触发预警）；②建立跨部门需求评审机制，要求销售提报需求时同步提供客户优先级、交付期弹性等信息，由供应链计划部联合生产部、采购部进行可行性评估，评审通过后方可纳入主计划'
  },
  {
    id: '3',
    impactFactorName: '供应链总成本',
    capabilityImpactPath: '需求波动放大→牛鞭效应加剧→库存持有成本 + 运输成本 + 采购成本上升',
    impactDetails:
      '需求计划缺乏市场波动过滤机制，终端需求变动被逐级放大，导致工厂按失真计划备料，原材料库存周转率下降 8%，旺季紧急采购比例超 20%，运输成本因临时调拨增加 12%，年度供应链总成本超预算 10% 以上',
    impactLevel: '高',
    improvementPriority: '高',
    improvementSuggestion:
      '①引入需求信号过滤技术（如 Holt-Winters 预测模型），区分真实需求与噪声波动，设置需求变动阈值（如周度需求波动超 ±20% 时触发人工校验）；②与核心客户签订滚动预测协议，约定未来 3 个月需求变动幅度（如 ±15%），超出部分由客户承担额外物流成本，从源头抑制牛鞭效应'
  },
  {
    id: '4',
    impactFactorName: '需求计划准确率',
    capabilityImpactPath: '数据维度单一→预测逻辑简单→计划与实际需求偏差率超标',
    impactDetails:
      '仅依赖历史销售数据制定计划，未纳入市场促销活动、宏观经济指标（如 PMI 指数）、竞品动态等变量，季度需求计划准确率低于 75%，导致采购计划偏差率超 20%，生产计划调整频率每周超 3 次，计划体系权威性丧失',
    impactLevel: '中高',
    improvementPriority: '中高',
    improvementSuggestion:
      '①搭建《需求计划多维数据中台》，整合内部销售数据、库存数据、外部市场舆情数据（如社交媒体热度）、经济指标数据，通过 BI 工具生成可视化需求分析看板；②建立计划准确率考核机制，将月度需求计划准确率纳入供应链计划团队 KPI（目标值≥90%），每低于 1% 扣减团队绩效奖金 2%'
  },
  {
    id: '5',
    impactFactorName: '客户需求响应速度',
    capabilityImpactPath: '需求计划更新滞后→市场机会捕捉延迟→新品上市周期延长→客户流失风险增加',
    impactDetails:
      '需求计划按月度更新，无法应对周度级市场变化（如突发网红带货需求），导致新品从需求识别到上市需 60 天，较竞争对手慢 30 天，错失黄金销售窗口，季度新客户获取量较计划少 20%，老客户因响应不及时流失率上升 5%',
    impactLevel: '中高',
    improvementPriority: '中高',
    improvementSuggestion:
      '①建立《需求动态跟踪机制》，设置专职市场情报岗，每日收集行业趋势、客户动态等信息，每周二、五更新需求预测（滚动预测周期缩短至 2 周）；②在 APS（高级计划系统）中开发 "应急需求快速插入" 功能，对于突发需求可一键触发计划重排，确保 48 小时内输出调整后的生产 / 采购计划，同时保留原计划版本供追溯'
  }
])
const taskCol = ref([
  {
    label: '序号',
    type: 'index'
  },
  {
    label: '组织名称',
    prop: 'organization',
    width: '100px'
  },
  {
    label: '举措',
    prop: 'measure',
    width: '100px'
  },
  {
    label: '关键行动',
    prop: 'keyAction'
  },
  {
    label: '建议责任人',
    prop: 'proposedOwner',
    width: '100px'
  },
  {
    label: '输出成果',
    prop: 'deliverable',
    width: '100px'
  },
  {
    label: '优先级',
    prop: 'priority',
    width: '60px'
  }
])
const taskData = ref([
  {
    id: '1',
    organization: '供应链计划管理部',
    measure: '建立需求计划核心指标体系',
    keyAction:
      '①明确库存周转率、订单交付准时率、供应链总成本等核心指标计算逻辑；②制定指标异常波动预警阈值（如库存周转率周降超 5% 触发预警）；③建立指标与需求计划对象的关联映射模型（如高价值客户需求对应交付准时率指标）。',
    proposedOwner: '张涛（计划管理经理）',
    deliverable: '《核心指标管理手册》',
    priority: '高'
  },
  {
    id: '2',
    organization: '供应链计划管理部',
    measure: '构建需求计划偏差分析机制',
    keyAction:
      '①开发《需求计划偏差分析模板》，包含数据偏差率、影响范围、责任环节诊断；②建立月度偏差复盘会议机制，重大偏差（超 20%）触发专项整改；③将偏差分析结果纳入需求计划模型优化输入参数。',
    proposedOwner: '李娜（营销流程专员）',
    deliverable: '《偏差分析报告模板》',
    priority: '中'
  },
  {
    id: '3',
    organization: '供应链计划管理部',
    measure: '系统固化核心指标监控模块',
    keyAction:
      '①在供应链系统中嵌入核心指标实时监控看板，动态显示库存周转率、交付准时率等数据；②设置指标达标率排行榜，按业务单元展示核心指标完成情况；③自动生成指标波动分析报告，推送至管理层及相关责任人。',
    proposedOwner: '王强（系统实施工程师）',
    deliverable: '《系统监控模块操作指南》',
    priority: '低'
  },
  {
    id: '4',
    organization: '供应链计划管理部',
    measure: '建立指标驱动的需求计划优化机制',
    keyAction:
      '①根据核心指标表现动态调整需求计划策略（如库存周转率低于目标时收紧安全库存）；②将指标完成情况与需求计划资源分配挂钩（如高交付准时率业务单元优先获取产能）；③定期更新指标权重（季度根据公司战略调整指标优先级）。',
    proposedOwner: '陈宇（人力资源经理）',
    deliverable: '《指标优化策略文档》',
    priority: '高'
  },
  {
    id: '5',
    organization: '供应链计划管理部',
    measure: '开展核心指标对标管理项目',
    keyAction:
      '①选取行业标杆企业核心指标数据（如库存周转率、订单满足率）进行对标分析；②制定指标提升路线图（明确 3 年内核心指标追赶目标及关键举措）；③每半年召开对标成果评审会，根据差距调整需求计划管理策略。',
    proposedOwner: '周明（营销主管）',
    deliverable: '《对标分析报告及路线图》',
    priority: '中'
  }
])
</script>
<template>
  <div class="affect-page">
    <div class="page-title-line">能力短板影响类型占比分布</div>
    <div class="h-[300px]">
      <EChartsBar :options="chartOpt"></EChartsBar>
    </div>
    <div class="page-title-line mt-7">能力短板影响分析</div>
    <SimplenessTable ref="affectTable" highlight-current-row :columns="typeCol" :data="typeData">
      <template #oper="">
        <el-table-column label="" width="100">
          <template #default="">
            <el-button size="small" type="primary" plain>详情</el-button>
          </template>
        </el-table-column>
      </template>
    </SimplenessTable>
    <div class="page-title-line mt-7">能力短板影响分析（核心指标影响）</div>
    <SimplenessTable :columns="weaknessCol" :data="weaknessData"> </SimplenessTable>
    <div class="page-title-line mt-7">建议改善关键任务（核心指标影响）</div>
    <SimplenessTable :columns="taskCol" :data="taskData"> </SimplenessTable>
  </div>
</template>
<style lang="scss" scoped></style>
