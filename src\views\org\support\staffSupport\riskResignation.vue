<script setup>
import Table from "../../components/table.vue";
const numberArea = ref([
  {
    num: "0~59",
  },
  {
    num: "60~69",
  },
  {
    num: "70~79",
  },
  {
    num: "80~89",
  },
  {
    num: "90~100",
  },
]);

const columns = ref([
  {
    label: "姓名",
    prop: "a",
  },
  {
    label: "岗位",
    prop: "b",
  },
  {
    label: "离职可能性",
    prop: "c",
    width: 80,
  },
  {
    label: "离职影响",
    prop: "d",
    width: 80,
  },
  {
    label: "人员可替代性",
    prop: "e",
    width: 90,
  },
  {
    label: "个人职业发展",
    prop: "f",
    slot: "fSlot",
    width: 90,
  },
  {
    label: "岗位匹配度",
    prop: "g",
    slot: "gSlot",
    width: 90,
  },
  {
    label: "薪酬匹配度",
    prop: "h",
    slot: "hSlot",
    width: 90,
  },
  {
    label: "组织架构调整",
    prop: "i",
    slot: "iSlot",
    width: 90,
  },
  {
    label: "末位淘汰",
    prop: "j",
    slot: "jSlot",
    width: 90,
  },
  {
    label: "工作环境",
    prop: "k",
    slot: "kSlot",
    width: 90,
  },
  {
    label: "人际关系",
    prop: "l",
    slot: "lSlot",
    width: 90,
  },
  {
    label: "家庭原因",
    prop: "m",
    slot: "mSlot",
    width: 90,
  },
  {
    label: "操作",
    prop: "n",
    slot: "nSlot",
    align: "center",
    width: 180,
  },
]);
const data = ref([
  {
    a: "王伟",
    b: "供应链总监",
    c: "高",
    d: "中",
    e: "低",
    f: 83,
    g: 93,
    h: 43,
    i: 54,
    j: 63,
    k: 74,
    l: 83,
    m: 93,
    n: 93,
    o: 93,
    p: 93,
    q: 93,
    r: 33,
    s: 94,
    t: 94,
  },
]);

const columns2 = ref([
  {
    label: "责任人",
    prop: "a",
    width: 150,
  },
  {
    label: "得分",
    prop: "b",
    width: 150,
  },
  {
    label: "关联策略",
    prop: "c",
  },
  {
    label: "流程端到端要点核心内容",
    prop: "d",
  },
]);
const data2 = ref([
  {
    a: "职业发展",
    b: "52",
    c: "战略能力施展受限，缺乏CPO级晋升通道；新技术实践机会不足（如未接触AI供应链金融",
    d: "1. 签订《战略官发展契约》： - 达成行业TOP3库存周转率→进入高管继任计划 - 预测准确率92%+→牵头供应链科技子公司 2. 授权组建家电供应链数字化委员会（直接向CEO汇报）",
  },
]);

const columns3 = ref([
  {
    label: "行业特性",
    prop: "a",
  },
  {
    label: "刘威风险点",
    prop: "b",
  },
  {
    label: "杠杆效应",
    prop: "c",
  },
]);
const data3 = ref([
  {
    a: "预测准确率要求>85%",
    b: "预测能力排名15/20",
    c: "能力质疑引发地位危机",
    d: "",
    e: "",
    f: "",
  },
]);

const columns4 = ref([
  {
    label: "风险维度",
    prop: "a",
  },
  {
    label: "岗位要求",
    prop: "b",
  },
  {
    label: "现状",
    prop: "c",
  },
  {
    label: "燃点预测",
    prop: "d",
  },
]);
const data4 = ref([
  {
    a: "薪酬敏感度",
    b: "掌握核心供应商议价权",
    c: "得分39（强负激励）",
    d: "竞品挖角成本=年薪×1.8倍 ",
    e: "",
    f: "",
  },
]);
const columns5 = ref([
  {
    label: "风险维度",
    prop: "a",
  },
  {
    label: "行动措施",
    prop: "b",
  },
  {
    label: "操作要点",
    prop: "c",
  },
]);
const data5 = ref([
  {
    a: "薪酬匹配",
    b: "签署《战略价值分成协议》",
    c: "按供应链降本额8%提取奖金",
    d: "",
    e: "",
    f: "",
  },
]);

const incentiveFactor = ref([
  {
    title: "离职可能性",
    level: 3,
    info: "不会离职，工作充满热情",
  },
  {
    title: "离职对业务的影响",
    level: 1,
    info: "有较大的影响，其负责的业务可能会进展缓慢，需要一定的缓冲期来过渡，或是对部门及公司业绩有小范围的冲击，但不至于对核心业务产生致命影响",
  },
  {
    title: "人员是否可替代",
    level: 3,
    info: "不可替代，内部需要很长的时间培养，外部需要很长周期才能招募到同水平的人员",
  },
]);

const circleColor = (v) => {
  if (v < 59) {
    return "bg1_b";
  } else if (v > 59 && v < 69) {
    return "bg2_b";
  } else if (v > 69 && v < 79) {
    return "bg3_b";
  } else if (v > 79 && v < 89) {
    return "bg4_b";
  } else if (v > 89 && v <= 100) {
    return "bg5_b";
  }
};

const aiData = ref({
  t: "",
  info: [
    {
      title: "离职风险结论与保留策略： ",
    },
    {
      title: "刘威的离职风险本质是家电行业转型期的系统性错配：",
      info: [
        {
          title: "",
          info: "短期：薪酬缺口(39分)是引爆炸药，需90日内拆除",
        },
        {
          title: "",
          info: "中期：岗位错配(69分)持续消耗高敬业度资本",
        },
        {
          title: "",
          info: "长期：职业发展(52分)缺失将窒息事业心引擎",
        },
      ],
    },
    {
      title: "保留策略：",
      info: [
        {
          title: "",
          info: "通过 “薪酬止血 → 职责再造 → 战略捆绑” 三级火箭，将其转型为 “技术供应链投资人”（主管芯片/新能源投资），既规避能力短板，又激活冒险型特质。同时每季度审计风险指标，确保薪酬总包持续位于行业前10%，发展进度每年推进≥2个职级里程碑。",
        },
      ],
    },
  ],
});
</script>
<template>
  <div class="content-wrap">
    <div class="page-title-line">人员离职风险</div>
    <div class="number_area_list justify-start">
      分值：
      <div class="item_wrap" v-for="(item, index) in numberArea">
        <span
          class="icon"
          :class="{
            act: index == 0,
            act1: index == 1,
            act2: index == 2,
            act3: index == 3,
            act4: index == 4,
          }"
        ></span
        >{{ item.num }}
      </div>
    </div>
    <Table
      :roundBorder="false"
      :columns="columns"
      :data="data"
      headerColor
      showIndex
    >
      <template v-slot:fSlot="scope">
        <span class="circle" :class="circleColor(scope.row.f)">{{
          scope.row.f
        }}</span>
      </template>
      <template v-slot:gSlot="scope">
        <span class="circle" :class="circleColor(scope.row.g)">{{
          scope.row.g
        }}</span>
      </template>
      <template v-slot:hSlot="scope">
        <span class="circle" :class="circleColor(scope.row.h)">{{
          scope.row.h
        }}</span>
      </template>
      <template v-slot:iSlot="scope">
        <span class="circle" :class="circleColor(scope.row.i)">{{
          scope.row.i
        }}</span>
      </template>
      <template v-slot:jSlot="scope">
        <span class="circle" :class="circleColor(scope.row.j)">{{
          scope.row.j
        }}</span>
      </template>
      <template v-slot:kSlot="scope">
        <span class="circle" :class="circleColor(scope.row.k)">{{
          scope.row.k
        }}</span>
      </template>
      <template v-slot:lSlot="scope">
        <span class="circle" :class="circleColor(scope.row.l)">{{
          scope.row.l
        }}</span>
      </template>

      <template v-slot:mSlot="scope">
        <span class="circle" :class="circleColor(scope.row.m)">{{
          scope.row.m
        }}</span>
      </template>

      <template v-slot:nSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>详情</el-button>
        <el-button class="ai_btn" type="primary" plain round>对比</el-button>
      </template>
    </Table>
    <div class="tips">已选人员：<span>王伟</span></div>

    <div class="page-title-line">人员离职风险详情</div>
    <div class="chart_list_wrap justify-between">
      <div class="item_wrap item_chart_wrap"></div>
      <div class="item_wrap" v-for="item in incentiveFactor">
        <div class="chart_t">{{ item.title }}</div>
        <div class="type_box justify-between">
          <div
            class="circle"
            :class="{
              act_red: item.level == 1,
              act_blue: item.level == 2,
              act_green: item.level == 3,
            }"
          >
            {{
              item.level == 1
                ? "高"
                : item.level == 2
                ? "中"
                : item.level == 3
                ? "低"
                : ""
            }}
          </div>
        </div>
        <div class="info">{{ item.info }}</div>
      </div>
    </div>
    <div class="tips">已选人员：<span>王伟</span></div>

    <div class="page-title-line">潜在离职因素</div>
    <div class="chart_box marginB20"></div>
    <div class="tips">已选人员：<span>王伟</span></div>

    <!-- <div class="tip_blue_bg marginB20">
      工作驱动（ 哪些因素能够有效的驱动个人更加的投入工作 ）
    </div> -->
    <div class="section_title blue_section_wrap justify-between">
      <div class="page-title-line">离职风险应对策略</div>
      <div class="line"></div>
      <div class="ai">AI解读</div>
    </div>
    <div class="table2_wrap">
      <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor>
      </Table>
    </div>
    <div class="page-title-line marginB20">
      人员离职风险 AI解读（刘威-供应链总监）
    </div>
    <div>TA面临的核心挑战</div>
    <div class="table3_wrap">
      <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor>
      </Table>
    </div>
    <div>离职风险暴雷点预测</div>
    <div class="table4_wrap">
      <Table :roundBorder="false" :columns="columns4" :data="data4" headerColor>
      </Table>
    </div>
    <div>可考虑的干预策略</div>
    <div class="table4_wrap">
      <Table :roundBorder="false" :columns="columns5" :data="data5" headerColor>
      </Table>
    </div>
    <div class="">
      <div class="t marginB20">{{ aiData.t }}</div>
      <div class="dot_content_wrap" v-for="item in aiData.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          <span class="icon"></span>
          <span class="title">{{ item1.title }}</span>
          <span class="info">{{ item1.info }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}

.content-wrap {
  :deep .el-table {
    .ai_btn {
      padding: 0 15px;
      height: 24px;
      font-size: 14px;
    }
    .circle {
      display: inline-block;
      width: 65px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      color: #fff;
      border-radius: 10px;
    }
    .bg1_b {
      background: #deeff9;
      color: #3d3d3d;
    }
    .bg2_b {
      background: #95d9f0;
    }
    .bg3_b {
      background: #65bbea;
    }
    .bg4_b {
      background: #2c89cd;
    }
    .bg5_b {
      background: #00659b;
    }
  }

  .tips {
    margin: 16px 0 40px;
    color: #94a1af;
    span {
      color: #3d3d3d;
    }
  }
  .tip_blue_bg {
    padding: 0 12px;
    height: 36px;
    line-height: 36px;
    background: #eff4f9;
    border-radius: 5px 5px 5px 5px;
    font-size: 14px;
    color: #40a0ff;
  }
  .chart_box {
    height: 300px;
  }
  .number_area_list {
    font-size: 12px;
    .item_wrap {
      margin: 0 25px 24px 0;
      .icon {
        display: inline-block;
        margin: 0 5px 0 15px;
        width: 14px;
        height: 8px;
        &.act {
          background: #deeff9;
        }
        &.act1 {
          background: #95d9f0;
        }
        &.act2 {
          background: #65bbea;
        }
        &.act3 {
          background: #2c89cd;
        }
        &.act4 {
          background: #00659b;
        }
      }
    }
  }
  .chart_list_wrap {
    margin: 0 -6px;

    .item_wrap {
      margin: 0 5px;
      padding: 7px 9px;
      flex: 1;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c6dbf3;
      .chart_t {
        height: 29px;
        line-height: 29px;
        text-align: center;
        background: #eaf4ff;
        border-radius: 15px;
        color: #40a0ff;
      }
      .type_box {
        padding: 5px 0 10px 0;
        width: 100%;
        height: 150px;
        flex-wrap: wrap;
        align-items: center;
        .circle {
          margin: 0 auto;
          width: 94px;
          height: 94px;
          border-radius: 50%;
          border: 13px solid #fff;
          padding: 10px 0 0 0;
          font-size: 30px;
          color: #fff;
          text-align: center;
          &.act_red {
            border-color: #ffd0cb;
            background: #ff6250;
          }
          &.act_blue {
          }
          &.act_green {
            border-color: #e1f7e0;
            background: #9ae496;
          }
        }

        span {
          display: block;
        }
      }
    }
    .item_chart_wrap {
      border-color: transparent;
    }
  }
  .section_title {
    margin: 36px 0 15px;
    .t {
      // color: #40a0ff;
    }
    .line {
      flex: 1;
      margin: 10px 7px 0;
      height: 1px;
      background: #d8d8d8;
    }
    .ai {
      margin-top: -5px;
      width: 73px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #40a0ff;
      border: 1px solid #40a0ff;
      border-radius: 18px;
    }
    &.blue_section_wrap {
    }
  }
  .table2_wrap {
    margin: 20px 0;
  }
  .table3_wrap {
    margin: 10px 0 37px;
  }
  .table4_wrap {
    margin: 13px 0 27px 0;
  }

  .section_box_wrap {
    background: #fff;
    .item_wrap {
      .item_title {
        margin: 10px 0 10px 0;
      }
      .info_item {
      }
    }
  }
  :deep .demo-form-inline {
    .el-form-item {
      width: 25%;
      margin-right: 0;
      .el-form-item__label {
        width: 130px;
        text-align: right;
      }
    }
  }
  .dot_content_wrap {
    // margin: 0 0 40px 0;
    .t {
      line-height: 34px;
    }
    .item_wrap {
      margin-bottom: 0;
      .icon {
        margin: -2px 0px 0 15px;
      }
      .info {
        line-height: 34px;
      }
    }
  }
}
</style>
