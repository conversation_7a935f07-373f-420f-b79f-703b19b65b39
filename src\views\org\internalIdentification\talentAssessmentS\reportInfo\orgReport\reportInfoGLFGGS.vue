<script setup>
import Table from '@/components/table/simplenessTable.vue'

const columns = ref([
  {
    label: '决策模式',
    prop: 'a',
    width: 150
  },
  {
    label: '训练模块',
    prop: 'b'
  },
  {
    label: '实战场景设计',
    prop: 'c'
  }
])
const data = ref([
  {
    a: '执行层决策能力',
    b: '《标准化流程决策认证》+《基础数据分析工具实操》（如 Excel 数据验证、SOP 模板应用）',
    c: '模拟订单处理异常场景（如库存不足），要求 10 分钟内按标准化流程完成订单拆分 / 调货方案制定'
  }
])

const columns2 = ref([
  {
    label: '决策风格',
    prop: 'a',
    width: 150
  },
  {
    label: '训练模块',
    prop: 'b'
  },
  {
    label: '实战场景设计',
    prop: 'c'
  }
])
const data2 = ref([
  {
    a: '保守型',
    b: '《风险分级决策法》+《试点项目管理实战》',
    c: '参与新品区域试销（预算 50 万），学习设置最小可行性测试（MVP），逐步扩大投入而非直接全局推广'
  }
])

const columns3 = ref([
  {
    label: '协同层级',
    prop: 'a',
    width: 150
  },
  {
    label: '训练模块',
    prop: 'b'
  },
  {
    label: '实战场景设计',
    prop: 'c'
  }
])
const data3 = ref([
  {
    a: '同小组合作',
    b: '《即时任务分工与沟通技巧》认证培训 + 班组应急协作沙盘演练',
    c: '模拟生产线设备故障场景，训练 10 人小组 30 分钟内完成任务分配与故障修复方案落地'
  }
])

onMounted(() => {})
</script>
<template>
  <div class="com_right_wrap right11_wrap">
    <div class="info_section_wrap">
      <div class="section_title blue_section_wrap justify-between">
        <div class="t">管理风格改善计划</div>
        <div class="line"></div>
        <div class="ai">AI解读</div>
      </div>
      <div class="tips">
        <el-icon><i-ep-WarningFilled /></el-icon>
        基于能力测评数据，精准识别团队成员能力与目标岗位胜任力要求的匹配程度，通过岗位调整实现
        “人岗适配”，最大化释放人才效能。
      </div>
    </div>

    <div class="info_section_wrap">
      <div class="page-title-line">决策模式优化工程</div>
      <Table :roundBorder="false" :columns="columns" :data="data" headerColor showIndex> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">决策风格优化工程</div>
      <Table :roundBorder="false" :columns="columns2" :data="data2" headerColor showIndex> </Table>
    </div>
    <div class="info_section_wrap">
      <div class="page-title-line">协同能力优化工程</div>
      <Table :roundBorder="false" :columns="columns3" :data="data3" headerColor showIndex> </Table>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../../../../style/common.scss';
@import './common.scss';
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
</style>
