<script setup>
defineOptions({ name: 'steps' })
const props = defineProps({
  stepsData: {
    type: Object
  },
  stepsNum: {
    type: Number,
    default: 1
  },
  verticalSign: {
    type: Boolean,
    default: false
  }
})
const stepsStatus = n => {
  if (n == props.stepsNum || n < props.stepsNum) {
    return true
  }
}
</script>
<template>
  <div class="steps_content justify-between">
    <div
      class="step_item"
      v-for="(item, index) in stepsData"
      :key="item.code"
      :class="{
        last_item: item.code == 3,
        act: stepsStatus(item.code)
      }"
    >
      <div class="decorate_wrap justify-between" v-if="!verticalSign">
        <div class="step_info justify-start">
          <div class="step_num">
            {{ index + 1 }}
          </div>
          <div class="info">{{ item.name }}</div>
        </div>
        <div v-if="index + 1 !== stepsData.length" class="line"></div>
      </div>

      <div class="decorate_wrap justify-start vertical_decorate_wrap" v-else>
        <div class="step_info" :class="{ '': !verticalSign }">
          <div class="step_num">
            {{ index + 1 }}
          </div>
          <div class="info">{{ item.name }}</div>
        </div>
        <div v-if="index + 1 !== stepsData.length" class="line"></div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.steps_content {
  color: #acacac;
  width: 100%;
  .step_item {
    width: 100%;
    .decorate_wrap {
      align-items: center;
    }
    .step_info {
      margin: 0 10px;
      .step_num {
        margin-right: 10px;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 50%;
        background: linear-gradient(180deg, #ebf0f4 0%, #ffffff 100%);
        box-shadow:
          0px 15px 24px 0px rgba(165, 186, 217, 0.3),
          inset 0px 1px 0px 0px #ffffff;
      }
      .info {
        line-height: 30px;
      }
    }
    .line {
      flex: 1;
      min-width: 100px;
      height: 6px;
      background: #d8d8d8;
      border-radius: 37px 37px 37px 37px;
    }
    .vertical_decorate_wrap {
      .step_info {
        width: 150px;
        text-align: center;
        .step_num {
          margin: 0 auto 10px;
        }
      }
      .line {
        margin: -35px -60px 0;
      }
    }
  }
  .step_item:last-child {
    flex: 1;
    .info {
      white-space: nowrap;
    }
  }
  .act {
    .step_info {
      .step_num {
        background: linear-gradient(180deg, #40a0ff 0%, #9fcfff 100%);
        box-shadow:
          0px 15px 24px 0px rgba(165, 186, 217, 0.3),
          inset 0px 1px 0px 0px #9fcfff;
        color: #fff;
      }
    }
    .info {
      color: #40a0ff;
    }
    .line {
      background: linear-gradient(63deg, #40a0ff 0%, rgba(64, 160, 255, 0.1) 97%);
    }
  }
}
</style>
