import path from 'path'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import Icons from 'unplugin-icons/vite'
import createAutoImport from './auto-import'
import createComponents from './components'
import createSvgIcons from './svg-icon'
import createRollupV from './rollup-visualizer'

export const createPlugins = (viteEnv, isBuild = false) => {
  return [
    vue(),
    vueJsx(),
    createAutoImport(path, viteEnv),
    createComponents(path, viteEnv),
    createSvgIcons(path, isBuild),
    createRollupV(path, viteEnv),
    Icons({
      autoInstall: true
    })
  ]
}
