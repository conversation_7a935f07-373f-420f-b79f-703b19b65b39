<template>
  <div class="edu_info_wrap">
    <div class="clearfix">
      <div class="page_second_title marginT_8">
        <span>业绩评价</span>
      </div>
      <div style="color: #0099ff; font-weight: 600">
        上级需要根据系统给出的各项维度分数，对下级人员进行等级评价，评价标准分为5档：S杰出（通常占10%），A一贯超出预期（30%），B符合预期（30%），C需要提高（20%），D不合格（10%）
      </div>
      <div class="btn_wrap align_right">
        <el-button class="page_add_btn" type="primary" @click="exportDownloadFun">下载详细数据</el-button>
      </div>
      <div class="edu_info_center marginT_16">
        <el-table :data="eduInfoData" style="width: 100%">
          <el-table-column type="index" label="序号" width="100" align="center" fixed="left"></el-table-column>
          <el-table-column prop="userName" label="姓名" width="230" align="center" fixed="left"></el-table-column>
          <el-table-column
            prop="performOverallScore"
            label="综合得分"
            width="230"
            align="center"
            fixed="left"
          ></el-table-column>
          <el-table-column
            prop="performLevelSys"
            label="系统评级"
            width="230"
            align="center"
            fixed="left"
          ></el-table-column>
          <el-table-column prop="actualPerformScore" label="实际得分" width="195" align="center" fixed="left">
            <template #default="scope">
              <el-input class="item" v-model="scope.row.actualPerformScore" />
            </template>
          </el-table-column>
          <el-table-column prop="actualPerformGrade" label="实际等级" width="195" align="center" fixed="left">
            <template #default="scope">
              <el-select class="item" v-model="scope.row.actualPerformGrade" placeholder="请选择">
                <el-option
                  v-for="(item, index) in qualificationOptions"
                  :label="item.codeName"
                  :value="item.dictCode"
                  :key="index"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
        <div class="edu_info_mmain">
          <div class="align_center marginT_30">
            <el-button
              class="page_confirm_btn"
              type="primary"
              @click="prevStep"
              v-show="currentIndex != currentFirstCode"
              >上一步</el-button
            >
            <el-button class="page_confirm_btn" type="primary" @click="() => submit('nextStep')">{{
              nextBtnText
            }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { saveQualityEval, performanceEvalList, exportQualityEvalList } from '../../../request/api.js'
import { objHasEmpty } from '@/utils/utils.js'
import { useUserStore } from '@/stores/modules/user.js'

const props = defineProps({
  nextBtnText: String,
  enqId: [String, Number],
  currentIndex: [String, Number],
  currentFirstCode: [String, Number]
})
const emit = defineEmits(['nextStep', 'prevStep'])

const submitFlag = ref(true)
const qualificationOptions = ref([])
const eduInfoData = ref([])

const userStore = useUserStore()

const submit = stepType => {
  if (!submitFlag.value) return
  if (checkData(eduInfoData.value)) {
    ElMessage.warning('请完善数据后提交！')
    return
  }
  submitFlag.value = false
  const arr = eduInfoData.value.map(item => ({
    grade: item.actualPerformGrade,
    comments: item.achievementComments,
    score: item.actualPerformScore,
    userId: item.userId
  }))
  const params = {
    enqId: props.enqId,
    list: arr,
    type: 'achievement'
  }
  saveQualityEval(params).then(res => {
    if (res.code == '200') {
      ElMessage.success('保存成功!')
      submitFlag.value = true
      emit(stepType)
    } else {
      submitFlag.value = true
      ElMessage.error('保存失败!')
    }
  })
}

const getEducationData = () => {
  performanceEvalList({ enqId: props.enqId }).then(res => {
    if (res.code == '200') {
      eduInfoData.value = res.data
    } else {
      ElMessage.error('获取数据失败!')
    }
  })
}

const exportDownloadFun = () => {
  const params = {
    enqId: props.enqId,
    type: 'performance'
  }
  exportQualityEvalList(params).then(res => {
    const blob = new Blob([res])
    const elink = document.createElement('a')
    elink.download = '部门业业绩价列表.xlsx'
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  })
}

const checkData = data => {
  for (let index = 0; index < data.length; index++) {
    const obj = data[index]
    if (objHasEmpty(obj, ['actualPerformGrade', 'achievementComments'])) {
      return true
    }
  }
  return false
}

const prevStep = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submit('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') emit('prevStep')
    })
}

onMounted(async () => {
  const res = await userStore.getDocList(['ACTUAL_GRADE'])
  qualificationOptions.value = res.ACTUAL_GRADE
  getEducationData()
})
</script>

<style scoped lang="scss">
.edu_info_wrap {
  margin-bottom: 16px;
}
.edu_info_header {
  .item {
    width: 9%;
    // padding-left: 15px;
  }

  .item_icon_wrap {
    text-align: center;
    width: 6%;
  }
}
</style>
