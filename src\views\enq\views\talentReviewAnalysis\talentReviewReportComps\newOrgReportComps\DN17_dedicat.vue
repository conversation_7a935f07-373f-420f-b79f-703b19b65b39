<template>
    <div class="dedicated_main" :class="{'height_auto':isPdf}">
        <page1 :enqId="enqId" :orgCode="orgCode" :pageData="pageData" ></page1>
        <page2 :enqId="enqId" :orgCode="orgCode" :pageData="pageData"></page2>
        <page3 :enqId="enqId" :orgCode="orgCode"></page3>
        <page4 :enqId="enqId" :orgCode="orgCode"></page4>
        <page5 :enqId="enqId" :orgCode="orgCode"></page5>
        <page6 :enqId="enqId" :orgCode="orgCode" :pageData="pageData"></page6>
    </div>
</template>
 
<script>
    import { jfAnalysisPage } from "../../../../request/api";
    import { getOrgEngagement } from "../../../../request/api";  //敬业度新接口

    import page1 from "./orgRDedicatComps/page1-2";
    import page2 from "./orgRDedicatComps/page3-4";
    import page3 from "./orgRDedicatComps/page5-6";
    import page4 from "./orgRDedicatComps/page7-8";
    import page5 from "./orgRDedicatComps/page9-11";
    import page6 from "./orgRDedicatComps/page12-13";
    export default {
        name: "orgRDedicatedAnalysis",
        props: ["enqId", "orgCode","isPdf"],
        components: {
            page1,
            page2,
            page3,
            page4,
            page5,
            page6,
        },
        data() {
            return {
                pageData:{}
            };
        },
        beforeCreate(){
        },
        created() {
            this.getOrgEngagementFun()
        },
        mounted() {
        },
        methods: {
            getOrgEngagementFun(){
                getOrgEngagement({
                   enqId:this.enqId,
                   orgCode:this.orgCode 
                }).then(res=>{
                    console.log(res)
                    if(res.code == 200){
                        this.pageData = res.data
                        // console.log(this.pageData)
                    }
                })
            }
        },
    };
</script>
 
<style scoped lang="scss">
    .dedicated_main {
        height: 420px;
        padding-right: 16px;
        overflow-y: auto;
        &.height_auto{
            height:auto
        }
    }
    .report_section {
        margin-bottom: 32px;
        .report_section_content{
            margin-bottom:16px;
        }
    }
    .chart_box {
        float: left;
        width: 230px;
        height: 140px;
        /*background: darkkhaki;*/
        margin-right: 32px;
    }
    .explain_text {
        overflow: hidden;
        color: #212121;
        line-height: 24px;
        .explain_title{
            font-size:16px;
            /*color: #92D050;*/
            font-weight: bold;
            margin-bottom:16px;
        }
        .line{
        line-height:28px;
        }
        .dot{
            list-style: inside disc;
        }
    }
</style>