<template>
  <div class="main_relationship_main">
    <div class="page_second_title">主要关系</div>
    <div v-if="mapShowSign">
      <postMap :postData="currPost" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import postMap from '../postMap.vue'
import { getPostRelationShip } from '../../../../request/api'

const props = defineProps({
  postCode: String
})

const currPost = ref({})
const mapShowSign = ref(false)

function getPostRelationShipFun() {
  if (!props.postCode) return

  getPostRelationShip({
    postCode: props.postCode
  }).then(res => {
    currPost.value = {}
    mapShowSign.value = false

    if (res.code == 200) {
      currPost.value = {
        postName: res.data.postName,
        postType: res.data.postTypeName,
        name: res.data.userNameList ? res.data.userNameList.join(',') : '',
        personnelNum: res.data.userNum,
        supPost:
          JSON.stringify(res.data.parent) == '{}'
            ? ''
            : {
                postName: res.data.parent.postName,
                postType: res.data.parent.postTypeName,
                name: res.data.parent.userNameList ? res.data.parent.userNameList.join(',') : '',
                personnelNum: res.data.parent.userNum
              },
        subPost:
          res.data.children.length == 0
            ? ''
            : res.data.children.map(item => ({
                postName: item.postName,
                postType: item.postTypeName,
                name: item.userNameList ? item.userNameList.join(',') : '',
                personnelNum: item.userNum
              })),
        common:
          res.data.peer.length == 0
            ? ''
            : res.data.peer.map(item => ({
                postName: item.postName,
                postType: item.postTypeName,
                name: item.userNameList ? item.userNameList.join(',') : '',
                personnelNum: item.userNum
              })),
        team:
          res.data.coop.length == 0
            ? ''
            : res.data.coop.map(item => ({
                postName: item.postName,
                postType: item.postTypeName,
                name: item.userNameList ? item.userNameList.join(',') : '',
                personnelNum: item.userNum
              })),
        outside:
          res.data.ExternalCoop.length == 0
            ? ''
            : res.data.ExternalCoop.map(item => ({
                postName: item.postName,
                postType: '对外'
              }))
      }
      mapShowSign.value = true
    }
  })
}

onMounted(getPostRelationShipFun)

watch(
  () => props.postCode,
  newVal => {
    if (newVal) {
      getPostRelationShipFun()
    }
  }
)
</script>

<style scoped lang="scss">
.page_second_title {
  margin: 10px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
}

.chart_wrap {
  width: 100%;
  height: 580px;
  margin-top: 20px;
  overflow: auto;
}

.filter_item {
  line-height: 30px;
  margin-right: 10px;
}

.no_data {
  line-height: 80px;
  color: #909399;
  text-align: center;
}
</style>
