<script setup>
import Table from "../../components/table.vue";
const numberArea = ref([
  {
    num: "0~59",
  },
  {
    num: "60~69",
  },
  {
    num: "70~79",
  },
  {
    num: "80~89",
  },
  {
    num: "90~100",
  },
]);

const columns = ref([
  {
    label: "姓名",
    prop: "a",
  },
  {
    label: "岗位",
    prop: "b",
  },
  {
    label: "人才类别",
    prop: "c",
  },
  {
    label: "建议发展类型",
    prop: "d",
  },
  {
    label: "发展职位",
    prop: "e",
    width: 155,
  },
  {
    label: "预计周期",
    prop: "f",
  },
  {
    label: "人才发展就绪度",
    prop: "g",
    slot: "gSlot",
    width: 105,
    align: "center",
  },
  {
    label: "操作",
    prop: "h",
    slot: "hSlot",
    align: "center",
    width: 180,
  },
]);
const data = ref([
  {
    a: "王伟",
    b: "供应链总监",
    c: "绩效之星",
    d: "巩固现有级别",
    e: "资深供应链副总 / 供应链专家顾问 ",
    f: "1 年到 3 年 ",
    g: 93,
  },
]);

const columns2 = ref([
  {
    label: "责任人",
    prop: "a",
    width: 150,
  },
  {
    label: "得分",
    prop: "b",
    width: 150,
  },
  {
    label: "关联策略",
    prop: "c",
  },
  {
    label: "流程端到端要点核心内容",
    prop: "d",
  },
]);
const data2 = ref([
  {
    a: "职业发展",
    b: "52",
    c: "战略能力施展受限，缺乏CPO级晋升通道；新技术实践机会不足（如未接触AI供应链金融",
    d: "1. 签订《战略官发展契约》： - 达成行业TOP3库存周转率→进入高管继任计划 - 预测准确率92%+→牵头供应链科技子公司 2. 授权组建家电供应链数字化委员会（直接向CEO汇报）",
  },
]);

const columns3 = ref([
  {
    label: "行业特性",
    prop: "a",
  },
  {
    label: "刘威风险点",
    prop: "b",
  },
  {
    label: "杠杆效应",
    prop: "c",
  },
]);
const data3 = ref([
  {
    a: "预测准确率要求>85%",
    b: "预测能力排名15/20",
    c: "能力质疑引发地位危机",
    d: "",
    e: "",
    f: "",
  },
]);

const columns4 = ref([
  {
    label: "风险维度",
    prop: "a",
  },
  {
    label: "岗位要求",
    prop: "b",
  },
  {
    label: "现状",
    prop: "c",
  },
  {
    label: "燃点预测",
    prop: "d",
  },
]);
const data4 = ref([
  {
    a: "薪酬敏感度",
    b: "掌握核心供应商议价权",
    c: "得分39（强负激励）",
    d: "竞品挖角成本=年薪×1.8倍 ",
    e: "",
    f: "",
  },
]);
const columns5 = ref([
  {
    label: "风险维度",
    prop: "a",
  },
  {
    label: "行动措施",
    prop: "b",
  },
  {
    label: "操作要点",
    prop: "c",
  },
]);
const data5 = ref([
  {
    a: "薪酬匹配",
    b: "签署《战略价值分成协议》",
    c: "按供应链降本额8%提取奖金",
    d: "",
    e: "",
    f: "",
  },
]);

const incentiveFactor = ref([
  {
    title: "离职可能性",
    level: 3,
    info: "不会离职，工作充满热情",
  },
  {
    title: "离职对业务的影响",
    level: 1,
    info: "有较大的影响，其负责的业务可能会进展缓慢，需要一定的缓冲期来过渡，或是对部门及公司业绩有小范围的冲击，但不至于对核心业务产生致命影响",
  },
  {
    title: "人员是否可替代",
    level: 3,
    info: "不可替代，内部需要很长的时间培养，外部需要很长周期才能招募到同水平的人员",
  },
]);

const circleColor = (v) => {
  if (v < 59) {
    return "bg1_b";
  } else if (v > 59 && v < 69) {
    return "bg2_b";
  } else if (v > 69 && v < 79) {
    return "bg3_b";
  } else if (v > 79 && v < 89) {
    return "bg4_b";
  } else if (v > 89 && v <= 100) {
    return "bg5_b";
  }
};

const aiData = ref({
  t: "",
  info: [
    {
      title: "① 战略执行力与事业驱动",
      info: [
        {
          title: "",
          info: "高事业心（90分）与敬业度（94分）形成“双引擎”，能高效推进供应链重大项目（如智能调度系统落地），在库存周转率提升等战略目标上表现出强结果导向。",
        },
      ],
    },
    {
      title: "② 制度设计与流程优化能力 ",
      info: [
        {
          title: "",
          info: "优势工作类别为“制度构建类”，成功建立供应商评估体系、动态库存校准规则等框架性制度，降低采购成本15%+，库存周转率提升20%。",
        },
      ],
    },
    {
      title: "③ 数据驱动决策偏好",
      info: [
        {
          title: "",
          info: "以数据驱动型决策模式（历史评估）主导开发库存周转驾驶舱、AI预测模型（准确率≥85%），实现供应链全链路可视化。",
        },
      ],
    },
    {
      title: "④ 高风险承压韧性",
      info: [
        {
          title: "",
          info: "在离职风险高企（薪酬39分、职业发展52分）下仍保持94分“全力工作”表现，旺季高压期可连续驻仓调度，保障供应链稳定性。",
        },
      ],
    },
    {
      title: "⑤ 团队领导与凝聚力",
      info: [
        {
          title: "",
          info: "管理团队满意度91分，所带团队离职率低于行业均值（8% vs 15%），擅长中层带教与绩效设计",
        },
      ],
    },
  ],
});

const ai2Data = ref({
  t: "",
  info: [
    {
      title: "① 供应链风险管理薄弱 ",
      info: [
        {
          title: "",
          info: "对突发性供应链中断（如极端天气、疫情）预判不足，曾因未建立应急预案导致关键物料短缺4；动态安全库存算法未覆盖黑天鹅事件场景，需强化灾害强度依赖性建模能力。",
        },
      ],
    },
    {
      title: "② 团队凝聚力建设不足",
      info: [
        {
          title: "",
          info: "跨部门协作中强势推进技术方案（如动态库存算法），遭销售/生产部门抵制3；团队内部存在沟通隔阂，未有效化解质检人员因新标准产生的抵触情绪。",
        },
      ],
    },
    {
      title: " ③ 薪酬竞争力严重失衡",
      info: [
        {
          title: "",
          info: "总包低于市场水平35%，期权占比不足20%，物质激励满意度仅39分3；战功奖励机制缺失，VMI节约收益未与个人收入挂钩，加剧离职风险。",
        },
      ],
    },
    {
      title: "④ 创新项目资源协调低效",
      info: [
        {
          title: "",
          info: "30%创新提案（如区块链溯源）因预算审批受阻3；缺乏高层支持，未能争取董事会级资源倾斜，影响技术落地速度。",
        },
      ],
    },
    {
      title: "⑤ 职业发展通道受阻 ",
      info: [
        {
          title: "",
          info: "任职4年未进入高管梯队，CPO岗位长期空缺但无继任计划，晋升满意度仅52分3；事务性工作占比70%（如签批补货单），战略峰会出席率低于40%，岗位价值被稀释。",
        },
      ],
    },
  ],
});

const ai3Data = ref({
  t: "",
  info: [
    {
      title: "① 授权战略性创新项目",
      info: [
        {
          title: "",
          info: "任命为 “供应链控制塔”总架构师，主导AI预测模型与动态调度系统集成，直接向CEO汇报； 剥离常规运营工作（移交副总监），保障50%精力聚焦技术攻坚4。",
        },
      ],
    },
    {
      title: "② 货币化薪酬改革",
      info: [
        {
          title: "",
          info: "实施 “VMI节约收益证券化”提成制：节约金额500万内提3%，超500万部分提5%3； 补差至行业薪酬90分位，期权授予量绑定库存周转率提升幅度（每+0.5次期权+5%）36。",
        },
      ],
    },
    {
      title: "③ 构建尊严激励体系",
      info: [
        {
          title: "",
          info: "设立 “供应链军功章”：库存下降1亿=金星勋章（董事长亲授），预测准确率破90%=黑钻勋章（官网头条公示）3； 技术成果冠名权（如“刘威-动态调度算法”）3。",
        },
      ],
    },

    {
      title: "④ 配备运营副总监+变革顾问",
      info: [
        {
          title: "",
          info: "副总监分担订单审批、报表汇总等事务； 外部顾问协助化解跨部门冲突（如销售部抵制算法问题）3。",
        },
      ],
    },
    {
      title: "⑤ 规划科技子公司CEO路径",
      info: [
        {
          title: "",
          info: "签订 《战略官发展契约》：若达成库存周转率行业TOP3，晋升集团CPO；预测准确率92%+则出任供应链科技子公司CEO3； 每年提供硅谷考察机会（预算50万）及EMBA全额资助",
        },
      ],
    },
  ],
});

const sudokuData = ref([
  {
    name: "待发展人才",
    bgC: "act_b_1",
    sign: false,
  },
  {
    name: "明日之星",
    bgC: "act_b_2",
    sign: false,
  },
  {
    name: "明星人才",
    bgC: "act_b_3",
    sign: true,
  },
  {
    name: "差距员工",
    bgC: "act_b_4",
    sign: false,
  },
  {
    name: "中坚力量",
    bgC: "act_b_5",
    sign: false,
  },
  {
    name: "绩效之星",
    bgC: "act_b_6",
    sign: false,
  },
  {
    name: "待优化人才",
    bgC: "act_b_7",
    sign: false,
  },
  {
    name: "稳定人才",
    bgC: "act_b_8",
    sign: false,
  },
  {
    name: "专业人才",
    bgC: "act_b_9",
    sign: false,
  },
]);
</script>
<template>
  <div class="content-wrap">
    <div class="page-title-line">人才发展</div>
    <div class="number_area_list justify-start">
      分值：
      <div class="item_wrap" v-for="(item, index) in numberArea">
        <span
          class="icon"
          :class="{
            act: index == 0,
            act1: index == 1,
            act2: index == 2,
            act3: index == 3,
            act4: index == 4,
          }"
        ></span
        >{{ item.num }}
      </div>
    </div>
    <Table
      :roundBorder="false"
      :columns="columns"
      :data="data"
      headerColor
      showIndex
    >
      <template v-slot:gSlot="scope">
        <span class="circle" :class="circleColor(scope.row.g)">{{
          scope.row.g + "%"
        }}</span>
      </template>

      <template v-slot:hSlot="scope">
        <el-button class="ai_btn" type="primary" plain round>详情</el-button>
        <el-button class="ai_btn" type="primary" plain round>对比</el-button>
      </template>
    </Table>
    <div class="tips">已选人员：<span>王伟</span></div>

    <div class="page-title-line">
      人才所在能力业绩矩阵（ AI基于各类要素分析 ）
    </div>
    <div class="sudoku_wrap justify-between">
      <div class="left_w justify-between">
        <div class="y_axis">
          <div class="y_i">能力</div>
          <div class="y_sign">
            <span class="t">高</span>
            <span class="c">中</span>
            <span class="b">低</span>
          </div>
        </div>
        <div class="axis_main">
          <div class="sudoku_mian justify-between">
            <div class="item_w" :class="item.bgC" v-for="item in sudokuData">
              {{ item.name }}
              <span v-show="item.sign" class="sign">
                <el-icon><i-ep-CircleCheck /></el-icon
              ></span>
            </div>
          </div>
          <div class="x_axis justify-between">
            <div class="y_sign">
              <span class="b">低</span>
              <span class="c">中</span>
              <span class="t">高</span>
            </div>
            <div class="y_i">业绩</div>
          </div>
        </div>
      </div>
      <div class="right_w">
        <div class="b_title marginB20">明星人才</div>
        <div class="info marginB20">
          能力强、业绩好的业务骨干，未来可以承受更多压力，在岗位上的可塑性非常强。
        </div>
        <div class="b_title b_title_2 marginB20">管理策略</div>
        <div class="info">
          业绩与潜能双优的明星员工，有能力承担更高层级的任务，也是外部挖猎的主要对象，需要重点培养发展，注重给予平台和机会：短时间内安排合适的新职务或赋予更重要的责任，使其迅速获得晋升；个性化保留策略，激励倾斜。
        </div>
      </div>
    </div>

    <div class="tips">已选人员：<span>王伟</span></div>

    <div class="page-title-line">个人优势（AI基于各类要素分析）</div>
    <div class="">
      <div class="t marginB20">{{ aiData.t }}</div>
      <div class="dot_content_wrap" v-for="item in aiData.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          {{ item1.info }}
        </div>
      </div>
    </div>
    <div class="tips">已选人员：<span>王伟</span></div>

    <div class="page-title-line">主要短板（AI基于各类要素分析）</div>
    <div class="">
      <div class="t marginB20">{{ ai2Data.t }}</div>
      <div class="dot_content_wrap" v-for="item in ai2Data.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          {{ item1.info }}
        </div>
      </div>
    </div>
    <div class="tips">已选人员：<span>王伟</span></div>

    <div class="page-title-line marginT20">任用建议（AI基于各类要素分析）</div>
    <div class="">
      <div class="t marginB20">{{ ai3Data.t }}</div>
      <div class="dot_content_wrap" v-for="item in ai3Data.info">
        <div class="t">{{ item.title }}</div>
        <div class="item_wrap" v-for="item1 in item.info">
          {{ item1.info }}
        </div>
      </div>
    </div>
    <div class="tips">已选人员：<span>王伟</span></div>
  </div>
</template>
<style lang="scss" scoped>
@import "../../style/common.scss";
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}

.content-wrap {
  :deep .el-table {
    .ai_btn {
      padding: 0 15px;
      height: 24px;
      font-size: 14px;
    }
    .circle {
      display: inline-block;
      width: 65px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      color: #fff;
      border-radius: 10px;
    }
    .bg1_b {
      background: #deeff9;
      color: #3d3d3d;
    }
    .bg2_b {
      background: #95d9f0;
    }
    .bg3_b {
      background: #65bbea;
    }
    .bg4_b {
      background: #2c89cd;
    }
    .bg5_b {
      background: #00659b;
    }
  }

  .tips {
    margin: 16px 0 40px;
    color: #94a1af;
    span {
      color: #3d3d3d;
    }
  }

  .chart_box {
    height: 300px;
  }
  .number_area_list {
    font-size: 12px;
    .item_wrap {
      margin: 0 25px 24px 0;
      .icon {
        display: inline-block;
        margin: 0 5px 0 15px;
        width: 14px;
        height: 8px;
        &.act {
          background: #deeff9;
        }
        &.act1 {
          background: #95d9f0;
        }
        &.act2 {
          background: #65bbea;
        }
        &.act3 {
          background: #2c89cd;
        }
        &.act4 {
          background: #00659b;
        }
      }
    }
  }
  .sudoku_wrap {
    .left_w {
      flex: 1;
      .y_i {
        width: 51px;
        height: 23px;
        line-height: 23px;
        text-align: center;
        color: #40a0ff;
        background: #eaf4ff;
        border-radius: 135px 135px 135px 135px;
      }
      .y_axis {
        .y_sign {
          margin: 10px 0 0 12px;
          width: 26px;
          height: 228px;
          background: linear-gradient(180deg, #2c89cd 0%, #95d9f0 100%);
          border-radius: 103px 103px 103px 103px;
          color: #fff;
          position: relative;
          span {
            position: absolute;
            left: 5px;
          }
          .t {
            top: 20px;
          }
          .b {
            bottom: 20px;
          }
          .c {
            top: 45%;
          }
        }
      }
      .axis_main {
        .sudoku_mian {
          width: 481px;
          height: 255px;
          border: 1px solid #d8d8d8;
          border-top: 1px solid transparent;
          border-right: 1px solid transparent;
          margin: 0 0 10px 5px;
          padding: 12px;
          flex-wrap: wrap;
          .item_w {
            width: 32%;
            height: 67px;
            line-height: 67px;
            text-align: center;
            border-radius: 5px 5px 5px 5px;
            color: #fff;
            position: relative;
            .sign {
              position: absolute;
              display: block;
              top: -16px;
              right: 8px;
            }
            &.act_b_1 {
              background: #3cb2ea;
            }
            &.act_b_2 {
              background: #219cdf;
            }
            &.act_b_3 {
              background: #2c89cd;
            }
            &.act_b_4 {
              background: #73c8f0;
            }
            &.act_b_5 {
              background: #3cb2ea;
            }
            &.act_b_6 {
              background: #219cdf;
            }
            &.act_b_7 {
              background: #95d9f0;
            }
            &.act_b_8 {
              background: #73c8f0;
            }
            &.act_b_9 {
              background: #3cb2ea;
            }
          }
        }
        .x_axis {
          .y_sign {
            width: 436px;
            height: 26px;
            background: linear-gradient(90deg, #95d9f0 0%, #2c89cd 100%);
            border-radius: 24px;
            position: relative;
            color: #fff;
            span {
              position: absolute;
            }
            .t {
              right: 20px;
            }
            .b {
              left: 20px;
            }
            .c {
              left: 45%;
            }
          }
          .y_i {
          }
        }
      }
    }
    .right_w {
      padding: 27px;
      margin-left: 20px;
      width: 775px;
      height: 236px;
      background: #eaf4ff;
      border-radius: 10px;
      font-size: 14px;
      .b_title {
        color: #40a0ff;
        font-weight: 600;
      }
      .b_title_2 {
      }
      .info {
      }
    }
  }

  .dot_content_wrap {
    // margin: 0 0 40px 0;
    .t {
      line-height: 34px;
    }
    .item_wrap {
      margin-bottom: 0;
      .icon {
        margin: -2px 0px 0 15px;
      }
      .info {
        line-height: 34px;
      }
    }
  }
}
</style>
