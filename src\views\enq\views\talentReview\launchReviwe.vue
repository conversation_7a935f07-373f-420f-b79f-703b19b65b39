<template>
  <div class="launch_review_wrap bg_write">
    <div class="page_main_title">
      发起盘点
      <div class="goback_geader" @click="goback"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="page_section">
      <div class="launch_review_center clearfix">
        <step-bar
          :stepData="stepData"
          :needClick="!isEdit"
          @stepClick="stepClick"
          :currentIndex="currentModuleCode"
        ></step-bar>
        <component
          :isEdit="isEdit"
          :getEnqId="getEnqId"
          :is="moduleObj[currentModuleCode]"
          @nextStep="nextStep"
          @prevStep="prevStep"
        ></component>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useEnqStore } from '@/views/enq/store'
import { getEnqInfo } from '../../request/api'
import stepBar from '@/components/talent/stepsComps/stepBar'
import createReview from './lunchReviewComponents/createReview'
import selectionRange from './lunchReviewComponents/selectionRange'
import rangeCheck from './lunchReviewComponents/rangeCheck'
import dataImprovement from './lunchReviewComponents/dataImprovement'
import viewReporter from './lunchReviewComponents/viewReporter'
import startReview from './lunchReviewComponents/startReview'
import settingRelationship from './lunchReviewComponents/settingRelationship'
import confirmRelationship from './lunchReviewComponents/confirmRelationship'
import personnelMatching from './lunchReviewComponents/personnelMatching.vue'

const route = useRoute()
const enqStore = useEnqStore()

const moduleObj = {
  L01: createReview,
  L02: selectionRange,
  L03: dataImprovement,
  L04: viewReporter,
  L05: startReview,
  L06: rangeCheck,
  L07: settingRelationship,
  L08: confirmRelationship,
  L09: personnelMatching
}

const isEdit = ref(true)
const moduleArr = ref(['L01', 'L02', 'L06', 'L09', 'L07', 'L08', 'L05'])
const currentModuleCode = ref('L01')
const currentIndex = ref(0)
const stepData = ref([
  { code: 'L01', name: '创建盘点项目', enqProgress: 'Y' },
  { code: 'L02', name: '选择盘点范围', enqProgress: 'N' },
  { code: 'L06', name: '盘点范围校验', enqProgress: 'N' },
  { code: 'L09', name: '评级模型确认', enqProgress: 'N' },
  { code: 'L07', name: '评价关系设置', enqProgress: 'N' },
  { code: 'L08', name: '评价关系确认', enqProgress: 'N' },
  { code: 'L05', name: '启动盘点', enqProgress: 'N' }
])

const router = useRouter()
const goback = () => {
  router.back()
}

const getEnqId = () => {
  const routeKey = route.name
  let enqId = null
  if (routeKey == 'perfectReview') {
    enqId = route.query.enqId
  } else {
    enqId = enqStore.createEnqId || ''
  }
  return enqId
}

const getEnqInfoFun = async () => {
  const enqId = getEnqId()
  if (enqId) {
    const res = await getEnqInfo({ id: enqId })
    if (res.code == 200) {
      isEdit.value = res.data.enqStatus == 1
      stepData.value.forEach(item => {
        item.enqProgress = res.data.enqStatus == 1 ? 'N' : 'Y'
      })
    }
  }
}

const stepClick = (code, index) => {
  currentIndex.value = index
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

const nextStep = () => {
  stepData.value[currentIndex.value].enqProgress = 'Y'
  if (currentIndex.value == stepData.value.length - 1) {
    return false
  }
  currentIndex.value++
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

const prevStep = () => {
  if (currentIndex.value == 0) {
    return false
  }
  currentIndex.value--
  currentModuleCode.value = moduleArr.value[currentIndex.value]
}

onMounted(() => {
  getEnqInfoFun()
})
</script>

<style scoped lang="scss">
.step_btn_wrap {
  padding-top: 16px;
}
</style>
