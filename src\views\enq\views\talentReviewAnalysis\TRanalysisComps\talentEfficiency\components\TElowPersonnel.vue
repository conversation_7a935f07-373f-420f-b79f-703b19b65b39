<template>
    <div class="talent_main">
        <div class="aside_filter_wrap">
            <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
        </div>
        <div class="talent_number_content page_section flex_row_wrap_start">
            <div class="content_item_title">人才效率较低人员</div>
            <tableComponent :needIndex="true" :tableData="tableData"></tableComponent>
        </div>
    </div>
</template>
 
<script>
import asideFilterBar from "../../asideFilterBar";
import tableComponent from "@/components/talent/tableComps/tableComponent";
export default {
    name: "TElowPersonnel",
    props: [],
    components: {
        asideFilter,
        tableComponent
    },
    data() {
        return {
            id: "",
            tableData:{
                columns:[
                    {
                        label:'工作类别',
                        prop:'workType'
                    },
                    {
                        label:'主要活动',
                        prop:'acivity'
                    },
                    {
                        label:'高绩效部门',
                        prop:'highPartment'
                    },
                    {
                        label:'部门1',
                        prop:'department1'
                    },
                    {
                        label:'部门2',
                        prop:'department2'
                    },
                    {
                        label:'部门3',
                        prop:'department3'
                    },
                    {
                        label:'部门4',
                        prop:'department4'
                    },
                ],
                data:[
                    {
                        id:'123123',
                        workType:'销售',
                        acivity:'业务企划',
                        highPartment:"23%",
                        department1:'21%',
                        department2:'21%',
                        department3:'21%',
                        department4:'21%',
                    },
                    {
                        id:'12dsa3123',
                        workType:'销售',
                        acivity:'业务企划',
                        highPartment:"23%",
                        department1:'21%',
                        department2:'21%',
                        department3:'21%',
                        department4:'21%',
                        department4:'21%',
                    },
                ]
            },
            navData: [
                {
                    id: "1",
                    name: "按组织架构",
                    children: [
                        {
                            id: "1-1",
                            name: "组织架构1"
                        },
                        {
                            id: "1-2",
                            name: "组织架构2"
                        },
                        {
                            id: "1-3",
                            name: "组织架构3"
                        },
                        {
                            id: "1-4",
                            name: "组织架构4"
                        }
                    ]
                },
                {
                    id: "2",
                    name: "按职务类型",
                    children: [
                        {
                            id: "2-1",
                            name: "全部"
                        },
                        {
                            id: "2-2",
                            name: "战略运营类"
                        },
                        {
                            id: "2-3",
                            name: "市场营销类"
                        },
                        {
                            id: "2-4",
                            name: "供应链类"
                        },
                        {
                            id: "2-5",
                            name: "业务流程设置"
                        }
                    ]
                },
                {
                    id: "3",
                    name: "研发技术类",
                    children: [
                        {
                            id: "3-1",
                            name: "研发技术1"
                        },
                        {
                            id: "3-2",
                            name: "研发技术2"
                        }
                    ]
                }
            ]
        };
    },
    created() {},
    mounted() {},
    methods: {
        getCode(orgCode,jobClassCode) {
                this.jobClassCode = jobClassCode;
                this.orgCode = orgCode;
                this.characteristicsFun();
            },
    }
};
</script>
 
<style scoped lang="scss">
</style>