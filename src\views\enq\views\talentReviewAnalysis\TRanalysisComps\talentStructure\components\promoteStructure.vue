<template>
    <div class="talent_main">
        <div class="aside_filter_wrap">
            <asideFilterBar :filterData="filterData" @getCode="getCode"></asideFilterBar>
        </div>
        <div class="talent_number_content page_section flex_row_wrap_start">
            <div class="content_item el-col-12">
                <div class="content_item_main">
                    <div class="content_item_title">晋升周期分布</div>
                    <div class="content_item_content" id="job_class"></div>
                </div>
            </div>
            <div class="content_item el-col-12">
                <div class="content_item_main">
                    <div class="content_item_title">各职层最近一次晋升周期</div>
                    <div class="content_item_content" id="job_level"></div>
                </div>
            </div>
            <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">个人期望的岗位预计晋升周期</div>
                    <div class="content_item_content" id="chart_expectations"></div>
                </div>
            </div>
            <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">上级评价给出的岗位预计晋升周期</div>
                    <div class="content_item_content" id="chart_evaluation"></div>
                </div>
            </div>
            <!-- <div class="content_item el-col-24">
                <div class="content_item_main">
                    <div class="content_item_title">各岗位平均晋升周期</div>
                    <div class="content_item_content" id="job_grade"></div>
                </div>
            </div> -->
        </div>
    </div>
</template>
 
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { echartsRenderPage } from "../../../../../../../../public/js/echartsimg/echartsToImg.js"
import { PromotionCycle } from "../../../../../request/api.js"
import asideFilterBar from "../../asideFilterBar.vue"

const route = useRoute()
const enqId = ref('')
const jobClassCode = ref('')
const orgCode = ref('')
const filterData = ref([])

const jobClass = reactive({
    data: []
})

const jobLevel = reactive({
    data: []
})

const jobGrade = reactive({
    data: []
})

const expectations = reactive({
    area: [],
    data: [],
    legend: []
})

const evaluation = reactive({
    area: [],
    data: [],
    legend: []
})

const initChart = () => {
    echartsRenderPage("job_class", "YBar", "350", "250", jobClass)
    echartsRenderPage("job_level", "YBar", "350", "250", jobLevel)
    echartsRenderPage("chart_expectations", "YStack", "700", "250", expectations)
    echartsRenderPage("chart_evaluation", "YStack", "700", "250", evaluation)
}

const promotionStructureFun = async () => {
    try {
        const params = {
            enqId: enqId.value,
            jobClassCode: jobClassCode.value,
            orgCode: orgCode.value
        }
        const res = await PromotionCycle(params)
        if (res.code == "200") {
            const data = res.data
            jobClass.data = data.jobClass
            jobLevel.data = data.jobLevel
            // this.$set(this.jobGrade, "data", data.average);
            // this.$set(this.expectations, "data", data.expectations);
            Object.assign(expectations, data.expectations)
            Object.assign(evaluation, data.evaluation)
            // this.$set(this.evaluation, "data", data.evaluation);
            initChart()
        }
    } catch (error) {
        console.error(error)
    }
}

const getCode = (orgCodeVal, jobClassCodeVal) => {
    jobClassCode.value = jobClassCodeVal
    orgCode.value = orgCodeVal
    promotionStructureFun()
}

onMounted(() => {
    enqId.value = route.query.enqId
    filterData.value = route.attrs.filterData
    promotionStructureFun()
})
</script>
 
<style scoped lang="scss">
</style>