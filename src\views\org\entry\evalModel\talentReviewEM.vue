<script setup>
import SectionTab from "../../components/sectionTab.vue";
const router = useRouter();
const route = useRoute();

const sectionTabCheckSign = ref(1);
const sectionTabList = ref([
  {
    name: "人才数量盘点",
    code: 1,
  },
  {
    name: "人才结构盘点",
    code: 2,
  },
  {
    name: "人才质量盘点",
    code: 3,
  },
  {
    name: "人才效能盘点",
    code: 4,
  },
  {
    name: "人才发展盘点",
    code: 5,
  },
  {
    name: "人才风险盘点",
    code: 6,
  },
]);

const sectionTab1Data = ref([
  {
    title: "定义",
    info: "主要关注企业中人才的数量情况，包括岗位需求数量、实际人员数量、空缺岗位数量等，以及与人才数量相关的招募、配置等方面的内容。",
  },
  {
    title: "缺乏了解的典型问题",
    info: "岗位需求不明确，招募计划不合理，导致人才数量不足或过剩，影响企业的正常运营。人才数量不足时，部门工作无法顺利开展，企业的发展战略难以实施；人才数量过剩时，造成人力成本浪费，员工之间竞争激烈，影响团队氛围。",
  },
  {
    title: "对管理者的价值",
    info: "清晰掌握企业人才数量情况，合理制定招募计划和人员配置方案，确保人才数量与企业的发展需求相匹配，提高人力资源的利用效率，降低人力成本。",
  },
  {
    title: "包含的要素",
    info: "岗位招募评估、人才协同网络（涉及人员在组织中的分布和连接情况，间接反映人才数量相关的协同效率）等",
  },
]);
const sectionTab2Data = ref([
  {
    title: "定义",
    info: "对企业人才的结构进行分析，包括岗位结构、层级结构、专业结构、年龄结构、性别结构等，了解人才在不同维度上的分布和比例情况，为优化人才结构提供依据",
  },
  {
    title: "缺乏了解的典型问题",
    info: "人才结构不合理，可能出现关键岗位人才短缺、管理层级过多或过少、专业人才比例失衡等问题，影响企业的组织效能和发展潜力。例如，关键岗位人才短缺导致企业核心业务发展受阻，专业人才比例失衡影响企业的创新能力和竞争力。",
  },
  {
    title: "对管理者的价值",
    info: "明确人才结构状况，发现结构中的问题和不足，有针对性地进行调整和优化，使人才结构更加合理，适应企业的发展战略和业务需求，提高企业的组织效能和竞争力。",
  },
  {
    title: "包含的要素",
    info: "明确人才结构状况，发现结构中的问题和不足，有针对性地进行调整和优化，使人才结构更加合理，适应企业的发展战略和业务需求，提高企业的组织效能和竞争力。",
  },
]);
const sectionTab3Data = ref([
  {
    title: "定义",
    info: "评估人才的能力、素质、绩效、潜力等方面的质量水平，全面了解员工的综合能力和发展潜力，为人才选拔、培养、评价等提供依据。",
  },
  {
    title: "缺乏了解的典型问题",
    info: "无法准确评估人才质量，导致人岗不匹配、培训效果不佳、绩效评价不公平等问题。优秀人才得不到合理使用和培养，普通人才的能力得不到提升，影响企业的整体绩效和人才发展",
  },
  {
    title: "对管理者的价值",
    info: "全面掌握人才质量情况，准确判断员工的优势和不足，为人才选拔、任用、培训、晋升等提供科学依据，合理配置人才资源，提升人才的整体素质和能力，为企业的发展提供有力的人才支持。",
  },
  {
    title: "包含的要素",
    info: "核心素质评价、员工潜力评价、核心业绩评价、KPI 达成评估、360° 人才分类、工作驱动因素（员工的工作动力来源影响其工作投入和质量）、员工发展评估（员工的发展路线与自身质量提升相关）",
  },
]);
const sectionTab4Data = ref([
  {
    title: "定义",
    info: "关注人才在工作中的效率和效果，包括岗位工作效率、岗位可优化性、人才协同网络的效率等，评估人才为企业创造价值的能力和效率",
  },
  {
    title: "缺乏了解的典型问题",
    info: "岗位工作效率低下，岗位流程繁琐，人才协同不畅，导致企业整体运营效率不高，资源浪费严重，无法实现经济效益的最大化。",
  },
  {
    title: "对管理者的价值",
    info: "了解人才效能情况，发现工作中的低效环节和问题，采取优化措施，提高岗位工作效率和人才协同效率，降低运营成本，提升企业的经济效益和竞争力。",
  },
  {
    title: "包含的要素",
    info: "岗位工作效率、岗位可优化性、人才协同网络（直接涉及人才在工作中的协同效率）",
  },
]);
const sectionTab5Data = ref([
  {
    title: "定义",
    info: "围绕员工的职业发展和成长，包括员工职业规划、培训计划、晋升与发展等内容，了解员工的发展需求和企业为员工提供的发展机会，促进员工与企业的共同发展。",
  },
  {
    title: "缺乏了解的典型问题",
    info: "员工职业规划不明确，培训计划不合理，晋升机制不健全，导致员工在企业中缺乏发展动力和机会，人才流失率升高，企业也难以培养出符合自身需求的人才。",
  },
  {
    title: "对管理者的价值",
    info: "明确员工的发展需求，制定个性化的培训计划和晋升机制，为员工提供广阔的发展空间，激发员工的潜力和创造力，实现员工与企业的共同发展，增强企业的凝聚力和吸引力。",
  },
  {
    title: "包含的要素",
    info: "员工职业规划、培训计划、晋升与发展、员工发展评估（直接与员工的发展相关）",
  },
]);
const sectionTab6Data = ref([
  {
    title: "定义",
    info: "评估企业面临的人才相关风险，包括离职风险、岗位空缺风险、人才结构不合理风险等，提前采取措施降低风险，保障企业的稳定发展。",
  },
  {
    title: "缺乏了解的典型问题",
    info: "无法及时发现人才风险，当风险发生时措手不及，导致关键人才流失、岗位空缺影响工作开展、人才结构问题制约企业发展等，给企业带来损失",
  },
  {
    title: "对管理者的价值",
    info: "识别和评估人才风险，制定风险应对策略，如建立人才储备库、改善员工待遇、优化人才结构等，降低人才风险对企业的影响，保障企业的稳定运营和发展。",
  },
  {
    title: "包含的要素",
    info: "离职风险评估、目标结果评估（目标未达成可能引发的人才相关风险）、敬业度与满意度（低敬业度和满意度可能导致离职风险）",
  },
]);

const checkSecTab = (c) => {
  sectionTabCheckSign.value = c;
};
</script>
<template>
  <div class="talentReviewEM_wrap">
    <div class="">
      <div class="section_tab_title">盘点维度：</div>
      <SectionTab
        :sectionTabList="sectionTabList"
        :sectionTabCheckSign="sectionTabCheckSign"
        @checkSecTab="checkSecTab"
      ></SectionTab>
    </div>

    <div class="tab_main_wrap" v-if="sectionTabCheckSign == 1">
      <div class="section_box_wrap">
        <div class="" v-for="it in sectionTab1Data">
          <div class="page-title-line">{{ it.title }}</div>
          <div class="info">
            {{ it.info }}
          </div>
        </div>
      </div>
      <div class="btn_wrap justify-end">
        <div class="btn">取消关注</div>
        <div class="btn">加入评估模块</div>
      </div>
    </div>
    <div class="tab_main_wrap" v-if="sectionTabCheckSign == 2">
      <div class="section_box_wrap">
        <div class="" v-for="it in sectionTab2Data">
          <div class="page-title-line">{{ it.title }}</div>
          <div class="info">
            {{ it.info }}
          </div>
        </div>
      </div>
      <div class="btn_wrap justify-end">
        <div class="btn">取消关注</div>
        <div class="btn">加入评估模块</div>
      </div>
    </div>
    <div class="tab_main_wrap" v-if="sectionTabCheckSign == 3">
      <div class="section_box_wrap">
        <div class="" v-for="it in sectionTab3Data">
          <div class="page-title-line">{{ it.title }}</div>
          <div class="info">
            {{ it.info }}
          </div>
        </div>
      </div>
      <div class="btn_wrap justify-end">
        <div class="btn">取消关注</div>
        <div class="btn">加入评估模块</div>
      </div>
    </div>
    <div class="tab_main_wrap" v-if="sectionTabCheckSign == 4">
      <div class="section_box_wrap">
        <div class="" v-for="it in sectionTab4Data">
          <div class="page-title-line">{{ it.title }}</div>
          <div class="info">
            {{ it.info }}
          </div>
        </div>
      </div>
      <div class="btn_wrap justify-end">
        <div class="btn">取消关注</div>
        <div class="btn">加入评估模块</div>
      </div>
    </div>
    <div class="tab_main_wrap" v-if="sectionTabCheckSign == 5">
      <div class="section_box_wrap">
        <div class="" v-for="it in sectionTab5Data">
          <div class="page-title-line">{{ it.title }}</div>
          <div class="info">
            {{ it.info }}
          </div>
        </div>
      </div>
      <div class="btn_wrap justify-end">
        <div class="btn">取消关注</div>
        <div class="btn">加入评估模块</div>
      </div>
    </div>
    <div class="tab_main_wrap" v-if="sectionTabCheckSign == 6">
      <div class="section_box_wrap">
        <div class="" v-for="it in sectionTab6Data">
          <div class="page-title-line">{{ it.title }}</div>
          <div class="info">
            {{ it.info }}
          </div>
        </div>
      </div>
      <div class="btn_wrap justify-end">
        <div class="btn">取消关注</div>
        <div class="btn">加入评估模块</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.justify-end {
  display: flex;
  justify-content: flex-end;
}
.talentReviewEM_wrap {
  .section_tab_title {
    margin-bottom: 10px;
  }
  .section_box_wrap {
    margin-bottom: 30px;
    padding: 0px 20px 16px;
    line-height: 26px;
    background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #c6dbf3;
    .page-title-line {
      margin: 30px auto 12px;
    }
  }
  .tab_main_wrap {
    .btn {
      margin-left: 20px;
      width: 120px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      color: #fff;
      background: #40a0ff;
      border-radius: 6px 6px 6px 6px;
      cursor: pointer;
    }
  }
}
</style>
