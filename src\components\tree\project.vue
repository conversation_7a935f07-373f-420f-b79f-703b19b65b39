<template>
  <el-tree
    :data="data"
    :props="defaultProps"
    node-key="id"
    default-expand-all
    :current-node-key="active"
    highlight-current
  />
</template>
<script setup>
const defaultProps = {
  children: 'children',
  label: 'label'
}

const active = ref(5)
const data = ref([
  {
    id: 1,
    label: 'H公司',
    children: [
      {
        id: 5,
        label: '供应链效率提升项目'
      },
      {
        id: 6,
        label: '工厂产能优化与成本控制项目'
      },
      {
        id: 7,
        label: '经销商渠道管理升级项目'
      },
      {
        id: 8,
        label: '新品研发流程优化项目'
      },
      {
        id: 9,
        label: '库存周转与滞销品清理项目'
      },
      {
        id: 10,
        label: '供应链计划管理部'
      },
      {
        id: 11,
        label: '售后服务标准化建设项目'
      },
      {
        id: 12,
        label: '跨境物流成本管控项目'
      },
      {
        id: 13,
        label: '生产计划与采购协同项目'
      },
      {
        id: 14,
        label: '终端门店陈列优化项目'
      },
      {
        id: 15,
        label: '组织架构与绩效考核优化项目'
      }
    ]
  }
])
</script>
<style lang="scss" scoped></style>
