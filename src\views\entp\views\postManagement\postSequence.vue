<template>
  <div class="post_sequence_wrap bg_write">
    <div class="page_main_title">岗位族群</div>
    <div class="page_section">
      <div class="post_sequence_center clearfix">
        <div class="page_section_aside">
          <div class="aside_tree_title flex_row_between">
            <div>族群分类</div>
            <div class="btn_wrap">
              <el-button link size="mini" class="oper_btn" @click="addTreeNode">新增</el-button>
              <el-button link size="mini" class="oper_btn" @click="editTreeNode('')">修改</el-button>
              <el-button link size="mini" class="oper_btn" @click="deleteTreeNode('')">删除</el-button>
            </div>
          </div>
          <div class="aside_tree_list">
            <tree-comp-radio
              :treeData="treeData"
              :needCheckedFirstNode="false"
              :canCancel="true"
              :defaultCheckedKeys="defaultCheckedKeys"
              @clickCallback="clickCallback"
            ></tree-comp-radio>
          </div>
        </div>
        <div class="page_section_main page_shadow">
          <table-component
            :tableData="tableData"
            :needIndex="needIndex"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          >
            <template #oper>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button @click.prevent="tableEdit(scope)" link icon="el-icon-edit" class="icon_edit"></el-button>
                  <el-button
                    class="color_danger icon_del"
                    @click.prevent="tableDeleteRow(scope)"
                    link
                    icon="el-icon-delete"
                  ></el-button>
                </template>
              </el-table-column>
            </template>
          </table-component>
        </div>
      </div>
    </div>
    <div class="dialog">
      <el-dialog :title="dialogTitle" v-model="dialogVisible" width="40%">
        <div class="dialog_form">
          <el-form ref="formRef" :model="form" label-width="100px">
            <el-form-item label="职族名称">
              <el-input v-model="form.jobClassName"></el-input>
            </el-form-item>
            <el-form-item label="职族描述">
              <el-input v-model="form.jobClassDesc"></el-input>
            </el-form-item>
            <el-form-item label="选择上级族群">
              <el-cascader
                :options="treeData"
                placeholder="空为最上级"
                :props="{ checkStrictly: true, value: 'code', label: 'value', expandTrigger: 'hover' }"
                clearable
                v-model="form.parentJobClassCode"
                :disabled="dialogType == 'update' ? true : false"
              >
              </el-cascader>
            </el-form-item>
            <el-form-item label="是否启用" v-if="dialogType == 'update'">
              <el-select v-model="form.status">
                <el-option label="是" value="Y"></el-option>
                <el-option label="否" value="N"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="排序号">
              <el-input
                type="number"
                v-model="form.sortNbr"
                oninput="if(value.length>4) value=value.slice(0,4)"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button class="page_clear_btn" @click="cancelSequence">取 消</el-button>
            <el-button class="page_add_btn" @click="confirmAddSequence">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import treeCompRadio from '@/components/talent/treeComps/treeCompRadio'
import tableComponent from '@/components/talent/tableComps/tableComponent'
import {
  getJobClassTree,
  addJobClass,
  delJobClass,
  getJobClass,
  updateJobClass,
  getJobClassPage
} from '../../request/api'

const needIndex = ref(true)
const dialogTitle = ref('新增岗位族群')
const lastNode = ref(false)
const dialogType = ref('add')
const checkedJobClassCode = ref('')
const defaultCheckedKeys = ref([])
const dialogVisible = ref(false)
const formRef = ref(null)
const form = reactive({
  jobClassCode: '',
  jobClassName: '',
  jobClassDesc: '',
  status: 'Y',
  parentJobClassCode: [],
  sortNbr: ''
})
const treeData = ref([])
const tableData = reactive({
  columns: [
    {
      label: '职族名称',
      prop: 'jobClassName'
    },
    {
      label: '职族说明',
      prop: 'jobClassDesc'
    },
    {
      label: '上级',
      prop: 'parentJobClassName'
    },
    {
      label: '状态',
      prop: 'rstatus',
      width: '80',
      formatterFun: (row, column, val) => {
        return val == 'Y' ? '启用' : '未启用'
      }
    },
    {
      label: '创建人',
      prop: 'createUserName'
    },
    {
      label: '创建时间',
      prop: 'rcreateTime'
    }
  ],
  data: [],
  page: {
    current: 1,
    size: 10,
    total: 0
  }
})
const page = reactive({
  current: 1,
  size: 10,
  total: 100
})
const dataLength = ref(0)

watch(checkedJobClassCode, () => {
  page.current = 1
  getJobClassPageFun()
})

onMounted(() => {
  getJobClassTreeFun()
})

function tableEdit(scope) {
  let jobClassCode = scope.row.jobClassCode
  editTreeNode(jobClassCode)
}
function tableDeleteRow(scope) {
  let jobClassCode = scope.row.jobClassCode
  deleteTreeNode(jobClassCode)
}
function clickCallback(code, last) {
  checkedJobClassCode.value = code
  lastNode.value = last
}
function addTreeNode() {
  dialogVisible.value = true
  dialogTitle.value = '新增岗位族群'
  dialogType.value = 'add'
  clearForm()
}
function editTreeNode(jobClassCode) {
  let code = jobClassCode || checkedJobClassCode.value
  if (!code) {
    ElMessage.warning('请选择一个族群节点')
    return
  }
  getJobClassFun(code).then(res => {
    form.jobClassCode = res.jobClassCode
    form.jobClassName = res.jobClassName
    form.jobClassDesc = res.jobClassDesc
    form.status = res.rstatus
    form.parentJobClassCode = res.parentCodes ? res.parentCodes.reverse() : ''
    form.sortNbr = res.sortNbr
    dialogVisible.value = true
    dialogTitle.value = '修改岗位族群'
    dialogType.value = 'update'
  })
}
function deleteTreeNode(jobClassCode) {
  let code = jobClassCode || checkedJobClassCode.value
  if (!code) {
    ElMessage.warning('请选择一个族群节点')
    return
  }
  ElMessageBox.confirm('确认删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      delJobClassFun(code)
    })
    .catch(() => {})
}
function confirmAddSequence() {
  if (dialogType.value == 'add') {
    addJobClassFun()
  } else {
    updateJobClassFun()
  }
}
function cancelSequence() {
  dialogVisible.value = false
  clearForm()
}
function handleSizeChange(size) {
  page.current = 1
  page.size = size
  getJobClassPageFun()
}
function handleCurrentChange(p) {
  page.current = p
  getJobClassPageFun()
}
function clearForm() {
  form.jobClassCode = ''
  form.jobClassName = ''
  form.jobClassDesc = ''
  form.status = 'Y'
  form.parentJobClassCode = []
  form.sortNbr = ''
}
function getJobClassTreeFun() {
  getJobClassTree({}).then(res => {
    if (res.length > 0) {
      treeData.value = res
    } else {
      treeData.value = []
    }
    getJobClassPageFun()
  })
}
function addJobClassFun() {
  if (!form.jobClassName) {
    ElMessage.warning('请输入职族名称')
    return
  }
  if (!form.jobClassDesc) {
    ElMessage.warning('请输入职族描述')
    return
  }
  if (!form.sortNbr) {
    ElMessage.warning('请输入排序号')
    return
  }
  let params = JSON.parse(JSON.stringify(form))
  params.jobClassCode = ''
  params.parentJobClassCode = params.parentJobClassCode.pop()
  addJobClass(params).then(res => {
    if (res.code == 200) {
      dialogVisible.value = false
      ElMessage.success('新增成功！')
      getJobClassTreeFun()
      clearForm()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
function delJobClassFun(code) {
  let params = {
    jobClassCode: code
  }
  delJobClass(params).then(res => {
    if (res.code == 200) {
      ElMessage.success('删除成功！')
      if (checkedJobClassCode.value == code) {
        checkedJobClassCode.value = ''
      } else {
        defaultCheckedKeys.value = [checkedJobClassCode.value]
      }
      if (dataLength.value == 1) {
        page.current > 1 && page.current--
      }
      getJobClassTreeFun()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
async function getJobClassFun(code) {
  let params = {
    jobClassCode: code
  }
  return await getJobClass(params).then(res => {
    if (res.code == 200) {
      return res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
function updateJobClassFun() {
  let params = JSON.parse(JSON.stringify(form))
  params.parentJobClassCode = params.parentJobClassCode ? params.parentJobClassCode.pop() : ''
  updateJobClass(params).then(res => {
    if (res.code == 200) {
      dialogVisible.value = false
      ElMessage.success('修改成功！')
      getJobClassTreeFun()
      defaultCheckedKeys.value = [checkedJobClassCode.value]
      clearForm()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
function getJobClassPageFun() {
  let params = {
    parentJobClassCode: checkedJobClassCode.value,
    current: page.current,
    size: page.size
  }
  getJobClassPage(params).then(res => {
    if (res.code == 200) {
      tableData.data = res.data
      page.total = res.total
      tableData.page.total = res.total
      dataLength.value = res.data.length
    }
  })
}
</script>

<style scoped lang="scss">
.post_sequence_wrap {
  .dialog {
    .el-cascader,
    .el-select {
      width: 100%;
    }
  }
}
</style>
