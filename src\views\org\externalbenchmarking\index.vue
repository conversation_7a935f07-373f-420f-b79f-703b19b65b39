<script setup>
import SectionTab from '../components/sectionTab.vue'
import understandBM from './understandBM.vue'
import projectBM from './projectBM.vue'
const tabContentList = ref([understandBM, projectBM])
const sectionTabList = ref([
  {
    name: '了解对标',
    code: 1
  },
  {
    name: '对标项目',
    code: 2
  }
])
const sectionTabCheckSign = ref(1)
const checkSecTab = c => {
  sectionTabCheckSign.value = c
}
</script>
<template>
  <div class="index_wrap">
    <div class="v_tab_wrap">
      <SectionTab
        :sectionTabList="sectionTabList"
        :sectionTabCheckSign="sectionTabCheckSign"
        @checkSecTab="checkSecTab"
      ></SectionTab>
    </div>
    <div class="content-mian">
      <component :is="tabContentList[sectionTabCheckSign - 1]" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.index_wrap {
}
</style>
