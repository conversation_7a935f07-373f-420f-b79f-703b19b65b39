<script setup>
defineOptions({ name: 'benchmarkingKnow' })

const developList = [
  {
    name: '明确竞争坐标',
    desc: '通过与行业标杆企业、跨领域优秀企业进行核心指标对标，清晰界定自身在关键业绩指标上的行业站位，客观评估优势指标与短板领域，为战略决策提供精准定位依据；'
  },
  {
    name: '动态校准战略',
    desc: '实时监测关键指标与行业趋势、战略目标的匹配度，当发现核心指标偏离行业标杆或战略预期时，及时调整业务策略、资源配置方向，确保企业战略始终保持市场适应性与竞争引领性；'
  },
  {
    name: '锚定改进方向',
    desc: '基于对标分析结果，企业可结合自身发展愿景与核心能力，聚焦差距显著的关键指标制定改进路径，明确短期攻坚重点与长期提升目标，避免盲目投入，使资源聚焦于最具价值的指标优化领域；'
  },
  {
    name: '优化资源配置',
    desc: '通过解构核心指标（如投入产出比、资产周转率、客户获取成本等），精准识别各业务单元、产品线的资源使用效率，将有限资源从低效指标领域向高价值创造环节倾斜，提升整体资源利用效能；'
  },
  {
    name: '管控经营成本',
    desc: '对标成本类核心指标（如毛利率、单位成本、费用率等），可直观判断企业在成本控制上的竞争力水平，识别成本异常波动或低效支出领域，通过流程优化、技术升级等手段实现成本精细化管控；'
  },
  {
    name: '平衡效益产出',
    desc: '核心指标对标促使企业建立 "指标 - 效益" 联动思维，在关注成本控制的同时，更注重投入产出的效益转化（如研发投入回报率、营销 ROI 等），推动企业从单纯的指标达标向价值创造型管理升级；'
  },
  {
    name: '科学设定目标',
    desc: '以行业标杆值、历史最优值为参考，为各业务单元及岗位制定具有挑战性与可行性的核心指标目标，使绩效考核体系更贴近市场实际与企业发展需求，激发组织与员工的价值创造动力；'
  },
  {
    name: '完善管理机制',
    desc: '通过对标结果分析，发现管理体系中制约核心指标提升的制度性短板，针对性优化绩效考核、激励分配、资源审批等管理机制，构建与核心指标提升相匹配的高效管理体系。'
  }
]

const tabList = [
  { name: '人力资源效能' },
  { name: '财务健康度' },
  { name: '运营效率' },
  { name: '研创新发投入与产出' },
  { name: '行业增长与市场竞争' },
  { name: '供应链管理对标' }
]
const activeTab = ref('人力资源效能')
const changeTab = name => {
  activeTab.value = name
}

const contentList = [
  '人员总量及增长对标',
  '人力成本对标',
  '人员结构占比及趋势变化对标',
  '人员质量对标',
  '人力资源效率对标',
  '人员结构配置对标',
  '薪酬投入效率对标',
  '……'
]
</script>

<template>
  <div class="page-container">
    <div class="page-title-line">什么是指标对标</div>
    <div class="page-text location border p-[16px] text-[16px]">
      核心指标对标是指企业将自身运营管理中涉及的关键业绩指标，与同行业领先企业、跨行业标杆企业或具有相似业务逻辑的卓越企业进行多维度对比，以精准定位自身在核心指标表现上的行业坐标、竞争优势及短板差距，进而通过针对性策略调整与管理优化，实现关键指标的持续改进与企业核心竞争力提升的管理实践。
    </div>
    <div class="page-title-line">为什么要开展指标对标</div>
    <div class="page-list location border p-[20px] text-[16px]">
      <div class="item" v-for="item in developList" :key="item.name">
        <SvgIcon name="rhombus" class="rhombus"></SvgIcon>
        <span class="name">{{ item.name }}</span>
        <span class="desc">{{ item.desc }}</span>
      </div>
    </div>
    <div class="page-title-line">可以开展哪些方面的指标对标</div>
    <div class="page-info text-[16px]">
      <div class="tab-list">
        <div
          class="item"
          @click="changeTab(item.name)"
          :class="{ active: activeTab == item.name }"
          v-for="item in tabList"
          :key="item.name"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="info-card">
        <div class="left location border p-[20px]">
          <div class="info-title">对标主题简介</div>
          <div class="info-text">
            人力资源效能对标是指企业将自身的人力资源效率相关指标与同行业其他企业、行业标杆企业或不同行业但具有类似业务模式的优秀企业进行对比，以明确自身在人力资源利用效率方面的水平、优势与不足，进而采取针对性措施加以改进和优化，提高人力资源价值创造能力的管理活动。
          </div>
        </div>
        <div class="right location border p-[20px]">
          <div class="info-title">主要对标内容</div>
          <div class="info-list">
            <div class="info-item" v-for="item in contentList" :key="item">{{ item }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.location {
  background: linear-gradient(226deg, #ffffff 0%, #f3f9fd 54%, #ffffff 100%);
}
.border {
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #c6dbf3;
}
.page-container {
  .page-text {
    margin-bottom: 40px;
  }
  .page-list {
    padding: 20px 16px;
    margin-bottom: 40px;
    .item {
      @include flex-center(row, flex-start, center);
      margin-bottom: 16px;
      &:last-child {
        margin-bottom: 0;
      }
      .rhombus {
        font-size: 11px;
        margin-right: 12px;
      }
      .name {
        margin-right: 10px;
        color: #40a0ff;
        font-weight: 500;
      }
    }
  }
  .page-info {
    // padding: 20px 16px;
    .tab-list {
      @include flex-center(row, flex-start, center);
      gap: 10px;
      margin-bottom: 20px;
      .item {
        width: calc((100% - 50px) / 6);
        height: 35px;
        line-height: 35px;
        text-align: center;
        background: #f0f9ff;
        border-radius: 5px;
        border: 1px solid #a5c1dc;
        font-size: 14px;
        color: #6c757e;
        cursor: pointer;
        &.active {
          color: #fff;
          background: #40a0ff;
          border: 1px solid #40a0ff;
        }
      }
    }
    .info-card {
      @include flex-center(row, flex-start, stretch);
      gap: 20px;
      .info-title {
        margin-bottom: 12px;
        color: #333333;
        font-weight: 600;
      }
      .left {
        flex: 1;
        flex-shrink: 0;
        .info-text {
          line-height: 24px;
          color: #3d3d3d;
        }
      }
      .right {
        flex: 1;
        flex-shrink: 0;
        .info-list {
          @include flex-center(row, flex-start, center);
          flex-wrap: wrap;
          .info-item {
            width: 40%;
            &::before {
              content: '●';
              display: inline-block;
              position: relative;
              font-size: 10px;
              margin-right: 6px;
              top: -2px;
            }
          }
        }
      }
    }
  }
}
</style>
