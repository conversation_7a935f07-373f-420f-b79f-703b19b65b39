<script setup>
defineOptions({ name: 'sectionTab' })
const props = defineProps(['sectionTabCheckSign', 'sectionTabList', 'itemWidth', 'actColor', 'actBgcolor'])
const emits = defineEmits(['checkSecTab'])
const checkSecTab = c => {
  emits('checkSecTab', c)
}
let itemWidth = computed(() => {
  let num = props?.itemWidth ?? 'auto'
  return num
})
let actColor = computed(() => {
  let c = props?.actColor ?? ''
  return c
})
let actBgcolor = computed(() => {
  let c = props?.actBgcolor ?? ''
  return c
})
</script>
<template>
  <div
    class="section_tab_wrap"
    :class="{
      item_width: props.itemWidth
    }"
  >
    <div
      class="s_tab_item"
      :class="{
        act: props.sectionTabCheckSign == item.code,
        actColor: props.actColor && props.sectionTabCheckSign == item.code,
        actBgcolor: props.actBgcolor && props.sectionTabCheckSign == item.code
      }"
      v-for="item in props.sectionTabList"
      :key="item.code"
      @click="checkSecTab(item.code)"
    >
      {{ item.name }}
    </div>
  </div>
</template>
<style lang="scss" scoped>
$itemWidth: v-bind(itemWidth);
$actColor: v-bind(actColor);
$actBgcolor: v-bind(actBgcolor);
.section_tab_wrap {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
  .s_tab_item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 12px 12px 0;
    padding: 6px;
    width: 310px;
    height: 40px;
    text-align: center;
    color: #333;
    font-size: 14px;
    background: url('../../../assets/imgs/indicator/img_01.png') no-repeat center center;
    background-size: 100% 100%;
    border-radius: 5px 5px 5px 5px;
    cursor: pointer;
    &.act {
      color: #40a0ff;
      // background: url("../../../assets/imgs/indicator/img_02.png") no-repeat
      //   center center;
      // background-size: 100% 100%;
      background: #e9f2fd;
      box-shadow: 0px 0px 10px 0px rgba(90, 153, 238, 0.5);
      border: 1px solid #5a99ee;
    }
    &.actColor {
      color: $actColor;
      background: #eafcff;
      box-shadow: 0px 0px 10px 0px rgba(96, 199, 212, 0.5);
      border: 1px solid #51d0e1;
    }
    &.actBgcolor {
      background: $actBgcolor;
      color: #fff;
    }
  }
}
.item_width {
  flex-wrap: wrap;
  .s_tab_item {
    width: $itemWidth;
  }
}
</style>
