<template>
  <div class="edu_info_wrap performance_info_main">
    <div class="clearfix">
      <div class="page_second_title marginT_30">
        部门指标达成
        <div class="change_post fr">
          <div class="kpi_list_wrap flex_row_end">
            <div class="post_title">指标类型</div>
            <div
              class="kpi_list"
              :class="{ active: kpiType == item.kpi_cycle }"
              @click="kpiTypeChange(item.kpi_cycle)"
              v-for="item in kpiTypeOptions"
              :key="item.kpi_cycle"
            >
              {{ item.kpi_codeName }}
            </div>
          </div>
          <!-- <el-select size="mini" v-model="kpiType" @change="kpiTypeChange">
						<el-option v-for="item in kpiTypeOptions" :key="item.kpi_cycle" :label="item.kpi_codeName" :value="item.kpi_cycle">
						</el-option>
					</el-select> -->
        </div>
      </div>
      <div class="kpi_header flex_row_between marginT_20">
        <div class="kpi_header_item name">指标名称</div>
        <div class="kpi_header_item unit">单位</div>
        <!-- <div class="kpi_header_item postName">岗位名称</div> -->
        <div class="kpi_header_item" v-for="(item, index) in columns" :key="index">
          <div class="kpi_header_item_title">{{ item.assessmentDate }}</div>
          <div class="flex_row_around">
            <span>目标</span>
            <span>实际</span>
          </div>
        </div>
      </div>
      <div class="kpi_content_wrap">
        <div class="kpi_content_item flex_row_between" v-for="(item, index) in enqOrgKpiData" :key="index">
          <div class="kpi_content name">{{ item.kpiName }}</div>
          <div class="kpi_content unit">{{ item.kpiUnit }}</div>
          <!-- <div class="kpi_content postName">{{item.orgCode}}</div> -->
          <div class="kpi_content" v-for="(list, index) in item.enqOrgKpiDate" :key="index">
            <div class="flex_row_between">
              <el-input class="kpi_ipt" type="number" v-model="list.kpiGoal" size="small"></el-input>
              <el-input class="kpi_ipt" type="number" v-model="list.kpiScore" size="small"></el-input>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="marginT_30 align_center">
      <el-button class="page_confirm_btn" type="primary" @click="prevBtn">上一步</el-button>
      <el-button class="page_confirm_btn" type="primary" @click="nextBtn">{{ nextBtnText }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { updateEnqOrgKpi, getEnqUserPost, getenqOrgKpiCycle, queryAllEnqOrgKpi } from '../../../request/api'

const props = defineProps({
  nextBtnText: String,
  enqId: String,
  orgCode: String
})

const emit = defineEmits(['prevStep', 'nextStep'])

const kpiType = ref(null)
const kpiTypeOptions = ref([])
const columns = ref([])
const enqOrgKpiData = ref([])
const backupData = reactive({})

watch(
  enqOrgKpiData,
  val => {
    if (kpiType.value) {
      backupData[kpiType.value].enqOrgKpiData = val
    }
  },
  { deep: true }
)

watch(
  columns,
  val => {
    if (kpiType.value) {
      backupData[kpiType.value].columns = val
    }
  },
  { deep: true }
)

onMounted(() => {
  getKpiCycleFun()
})

const queryAllEnqOrgKpiFun = async kpiTypeVal => {
  try {
    backupData[kpiTypeVal] = {}

    const res = await queryAllEnqOrgKpi({
      enqId: props.enqId,
      kpiCycle: kpiTypeVal,
      orgCode: props.orgCode
    })

    if (res.code == 200) {
      backupData[kpiTypeVal].enqOrgKpiData = res.data.enqOrgKpi
      backupData[kpiTypeVal].columns = res.data.enqOrgKpiColumn

      enqOrgKpiData.value = backupData[kpiType.value].enqOrgKpiData
      columns.value = backupData[kpiType.value].columns
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const getKpiCycleFun = async () => {
  try {
    const res = await getenqOrgKpiCycle({
      enqId: props.enqId,
      orgCode: props.orgCode
    })

    if (res.code == 200) {
      kpiTypeOptions.value = res.data
      kpiType.value = kpiTypeOptions.value[0].kpi_cycle

      for (const item of kpiTypeOptions.value) {
        await queryAllEnqOrgKpiFun(item.kpi_cycle)
      }

      ElMessage.success(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  }
}

const kpiTypeChange = val => {
  kpiType.value = val
  if (backupData[val]) {
    console.log('不调用接口')
    enqOrgKpiData.value = backupData[val].enqOrgKpiData
    columns.value = backupData[val].columns
  } else {
    queryAllEnqOrgKpiFun(val)
  }
}

const submit = async stepType => {
  const resultArr = []

  for (const key in backupData) {
    if (Object.prototype.hasOwnProperty.call(backupData, key)) {
      const list = backupData[key].enqOrgKpiData
      list.forEach(item => {
        const kpidata = item.enqOrgKpiDate
        resultArr.push(...kpidata)
      })
    }
  }

  if (checkData(resultArr)) {
    ElMessage.warning('请完善数据后提交')
    return
  }

  try {
    const res = await updateEnqOrgKpi(resultArr)

    if (res.code == 200) {
      ElMessage.success(res.msg)
      emit(stepType)
    } else {
      ElMessage.error(res.msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('保存失败')
  }
}

const prevBtn = () => {
  ElMessageBox.confirm('即将离开当前页面，是否保存当前页数据?', '提示', {
    distinguishCancelAndClose: true,
    confirmButtonText: '保存',
    cancelButtonText: '放弃修改'
  })
    .then(() => {
      submit('prevStep')
    })
    .catch(action => {
      ElMessage.info(action == 'cancel' ? '已放弃修改并返回上一步' : '取消返回上一步')
      if (action == 'cancel') {
        emit('prevStep')
      }
    })
}

const nextBtn = () => {
  submit('nextStep')
}

const checkData = data => {
  for (const obj of data) {
    if (window.$util.objHasEmpty(obj, ['kpiGoal', 'kpiScore'])) {
      console.log('有空值')
      return true
    }
    console.log('没有空值')
  }
  return false
}
</script>

<style scoped lang="scss">
.edu_info_wrap {
  margin-bottom: 16px;
}

.change_post {
  margin-left: 16px;

  .post_title {
    // display: inline-block;
    font-size: 14px;
    margin-right: 16px;
  }
  .kpi_list_wrap {
    align-items: center;
  }
  .kpi_list {
    color: #449cff;
    border: 1px solid #449cff;
    padding: 0px 5px;
    line-height: 20px;
    height: 25px;
    border-radius: 3px;
    margin: 0 8px;
    background: #fff;
    cursor: pointer;
    &.active {
      background: #449cff;
      color: #fff;
    }
  }
}

.edu_info_center {
  width: 60%;
}

.kpi_header {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  background: #f4f4f4;
  color: #525e6c;
  font-size: 14px;
  padding: 0 16px;
  height: 56px;
  line-height: 20px;
  text-align: center;

  .kpi_header_item {
    width: 15%;
    &.name {
      text-align: left;
    }

    &.unit {
      width: 50px;
    }

    &.postName {
      width: 100px;
      text-align: left;
    }
  }

  .kpi_header_item_title {
    font-size: 14px;
    margin-bottom: 4px;
  }
}

.kpi_content_wrap {
  font-size: 12px;
  .kpi_content_item {
    padding: 6px 0;
    &:nth-child(even) {
      background: #f4f4f4;
    }
    .kpi_content {
      width: 15%;

      .kpi_ipt {
        margin-right: 8px;
      }
      &.name {
        padding-left: 20px;
      }

      &.unit {
        width: 50px;
        text-align: center;
        // flex: 0;
      }

      &.postName {
        width: 100px;
      }
    }
  }
}

.column_header_title {
  margin-bottom: 8px;
}

.column_ipt:first-of-type {
  margin-right: 5px;
}

// 去除input number类型 加减箭头
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  -moz-appearance: textfield;
}
input {
  -moz-appearance: textfield;
}
</style>
